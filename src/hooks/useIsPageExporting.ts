import { useDispatch, useSelector } from 'react-redux';
import { getIsPageExportingPDF } from '../redux/selectors/exportPDFSlice.selectors';
import exportPDFSlice from '../redux/slices/exportPDF.slice';

export const useIsPageExporting = () => {
  const dispatch = useDispatch();
  const isPageExportingPDF = useSelector(getIsPageExportingPDF);

  const setIsExportingPDF = (isExporting: boolean) => {
    dispatch(exportPDFSlice.actions.setIsPDFExporting(isExporting));
  };

  return { isPageExportingPDF, setIsExportingPDF };
};
