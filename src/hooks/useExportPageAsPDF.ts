import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useToastAlert } from '../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { PAGE_HEADER_V2_ID } from '../components/PageHeaderV2/PageHeaderV2';
import { useIsPageExporting } from './useIsPageExporting';

interface Payload {
  pdfTitle: string;
  elementsIdsToIgnore?: string[]; // whatever elements you dont want to render in pdf
  elementsIdsToUpdateOverflow?: string[]; // if content is being cut off in the pdf and contentFullHeightPx below isn't showing the right length for the page, you likely have overflow = hidden parents, add their ids here to temporarily change this
  mainContentElementId: string;
  errorAlertCopyId: string;
  pageSubheaderId?: string; // usually has the creator name / date / anything else you want before the border
  elementIdToAddBorderAfter?: string;
  headerMarginLeft?: string; // making space for vidmob logo
}

// BASICS:
// This code creates a new pdf, then finds the element to render based on mainContentElementId
// We ignore whatever elements are needed within that content during render, take a screenshot, and add it to the pdf

// ABOUT THE HEADER:
// This is meant to always show the header at the top w the Vidmob logo to the left, and a border underneath
// If you need different styling, let's do so carefully so the design above is always the default

// IMPORTANT NOTES:
// If channel logo is not loading, replace getMUIIconForChannel with MuiIconForChannel component, it's the same thing
// If you have to change more styling based on whether you're exporting or not, use the hook useIsPageExporting to get isPageExportingPDF and then change css based on its value

export const useExportPageToPDF = ({
  pdfTitle,
  mainContentElementId,
  errorAlertCopyId,
  elementIdToAddBorderAfter,
  pageSubheaderId,
  headerMarginLeft = '158px',
  elementsIdsToIgnore = [],
  elementsIdsToUpdateOverflow = [],
}: Payload) => {
  const showToastAlert = useToastAlert();
  const { setIsExportingPDF, isPageExportingPDF } = useIsPageExporting();

  let pageContent: HTMLElement | null = null;
  let vidmobIcon: HTMLElement | null = null;
  let elementToAddBorderTo: HTMLElement | null = null;
  let pageHeaderV2: HTMLElement | null = null;
  let pageSubheader: HTMLElement | null = null;
  let pageHeaderBorder: HTMLElement | null = null;
  const elementsToUpdateOverflow: HTMLElement[] = [];

  let originalHeaderLeftMargin: string;
  let originalSubheaderLeftMargin: string;

  const handleError = (error: Error) => {
    setIsExportingPDF(false);
    showToastAlert(errorAlertCopyId, 'error');
    console.log('Error downloading pdf:', error);
  };

  const handleOverflowStyles = () => {
    try {
      elementsIdsToUpdateOverflow.map((elementId) => {
        const element = document.getElementById(elementId) as HTMLElement;
        element.style.overflow = 'visible';
        elementsToUpdateOverflow.push(element);
      });
    } catch (error) {
      handleError(error as Error);
    }
  };

  const createVidmobLogoSvg = () => {
    try {
      const box = document.createElement('div');
      box.style.width = '100px';
      box.style.height = '1200px';
      box.style.position = 'absolute';
      box.style.left = '48px';
      box.style.top = '24px';

      const vidmobSvg = document.createElementNS(
        'http://www.w3.org/2000/svg',
        'svg',
      );
      vidmobSvg.setAttribute('width', '84');
      vidmobSvg.setAttribute('height', '20');
      vidmobSvg.setAttribute('viewBox', '0 0 84 20');
      vidmobSvg.setAttribute('fill', '#010101');
      vidmobSvg.setAttribute('fillRule', 'evenodd');
      vidmobSvg.setAttribute('clipRule', 'evenodd');

      vidmobSvg.innerHTML =
        '<path d="M32.82 0H29.9608V4.85317H29.9558L29.9608 4.87785V7.07411C29.9608 7.07411 28.6912 5.503 25.9911 5.503C22.1822 5.503 19.1639 8.49058 19.1639 12.7351C19.1639 16.9795 22.1822 19.9704 25.9911 19.9671C28.5436 19.9655 29.9575 18.7727 29.9575 18.7727L30.9735 19.7779L32.3542 19.7763C32.6111 19.7763 32.82 19.5805 32.82 19.3107V0ZM26.1519 17.2937C23.9275 17.2937 22.0231 15.4068 22.0231 12.7334C22.0231 10.06 23.9292 8.17472 26.1519 8.17472C28.3746 8.17472 30.279 10.0617 30.279 12.7334C30.279 15.4051 28.3729 17.2937 26.1519 17.2937ZM14.7269 2.38714C14.7269 1.5185 15.4595 0.792992 16.338 0.792992C17.2165 0.792992 17.9491 1.5185 17.9491 2.38714C17.9491 3.25577 17.2165 3.98457 16.338 3.98457C15.4595 3.98457 14.7269 3.25906 14.7269 2.38714ZM14.0025 5.58197L8.3836 19.4933C8.3173 19.6562 8.15818 19.7631 7.98083 19.7631H6.02333C5.84597 19.7631 5.68686 19.6562 5.62056 19.4933L0 5.58197H3.02327L7.00125 15.8246L10.9809 5.58197H14.0025ZM14.9673 7.02143C14.9673 6.23834 15.5922 5.60332 16.3613 5.60332V5.60167C17.132 5.60167 17.7552 6.2367 17.7552 7.01978V19.7844H14.9673V7.02143ZM53.8769 19.7631V11.266H53.8786C53.8786 7.64666 51.6526 5.44381 48.9525 5.44381C46.3302 5.44381 44.9097 7.32102 44.6911 7.60987C44.6712 7.63613 44.6613 7.64927 44.6613 7.64666C44.6613 7.64666 43.3883 5.44381 40.6866 5.44381C38.4623 5.44381 37.3501 6.87673 37.3501 6.87673L36.0324 5.60175H34.9285C34.6865 5.60175 34.4909 5.79588 34.4909 6.03442V19.7631H37.3501V11.266C37.3501 9.37735 38.6214 8.11882 40.0518 8.11882C41.4822 8.11882 42.7535 9.37735 42.7535 11.266V19.7631H45.6143V11.266C45.6143 9.37735 46.884 8.11882 48.3144 8.11882C49.7448 8.11882 51.0161 9.37735 51.0161 11.266V19.7631H53.8769ZM62.1095 5.47667C58.2973 5.47667 55.2773 8.46754 55.2773 12.7153C55.2773 16.9631 58.2973 19.9539 62.1095 19.9539C65.9218 19.9539 68.945 16.9631 68.945 12.7153C68.945 8.46754 65.9251 5.47667 62.1095 5.47667ZM62.1095 17.2789C60.0443 17.2789 58.1365 15.3903 58.1365 12.7153C58.1365 10.0403 60.0443 8.15167 62.1095 8.15167C64.1748 8.15167 66.0825 10.0403 66.0825 12.7153C66.0825 15.3903 64.1781 17.2789 62.1095 17.2789ZM70.8096 19.8092C70.5527 19.8092 70.3438 19.6134 70.3438 19.3436V0.0329204H73.203V4.88609H73.208L73.203 4.91077V7.10703C73.203 7.10703 74.4727 5.53592 77.1727 5.53592C80.9816 5.53592 83.9999 8.5235 83.9999 12.768C83.9999 17.0124 80.9816 20.0033 77.1727 20C74.6202 19.9984 73.2063 18.8056 73.2063 18.8056L72.1903 19.8108L70.8096 19.8092ZM72.8848 12.7663C72.8848 15.4397 74.7909 17.3267 77.0119 17.3267C79.2346 17.3267 81.1408 15.4397 81.1408 12.7663C81.1408 10.0946 79.233 8.20763 77.0119 8.20763C74.7909 8.20763 72.8848 10.0946 72.8848 12.7663Z"/>';
      vidmobSvg.style.zIndex = '100';

      box.appendChild(vidmobSvg);

      vidmobIcon = box;
    } catch (error) {
      handleError(error as Error);
    }
  };

  const onFinishCleanupStyles = () => {
    try {
      if (pageHeaderBorder && elementToAddBorderTo) {
        elementToAddBorderTo.removeChild(pageHeaderBorder);
      }

      if (pageContent && vidmobIcon) {
        pageContent.removeChild(vidmobIcon);
      }

      if (pageHeaderV2) {
        pageHeaderV2.style.marginLeft = originalHeaderLeftMargin;
      }

      if (pageSubheader) {
        pageSubheader.style.marginLeft = originalSubheaderLeftMargin;
      }

      if (elementsToUpdateOverflow.length > 0) {
        elementsToUpdateOverflow.map(
          (element) => (element.style.overflow = 'hidden'),
        );
      }
    } catch (error) {
      handleError(error as Error);
    }
  };

  const onClickExportAsPDF = async () => {
    try {
      if (isPageExportingPDF) return; // block user double clicks while this is running

      setIsExportingPDF(true);
      await new Promise((resolve) => setTimeout(resolve, 100));

      pageContent = document.getElementById(
        mainContentElementId,
      ) as HTMLElement;

      pageHeaderV2 = document.getElementById(PAGE_HEADER_V2_ID) as HTMLElement;

      originalHeaderLeftMargin = pageHeaderV2.style.marginLeft;
      pageHeaderV2.style.marginLeft = headerMarginLeft;

      handleOverflowStyles();

      if (pageSubheaderId) {
        pageSubheader = document.getElementById(pageSubheaderId) as HTMLElement;

        originalSubheaderLeftMargin = pageSubheader.style.marginLeft;
        pageSubheader.style.marginLeft = headerMarginLeft;
      }

      if (elementIdToAddBorderAfter) {
        pageHeaderBorder = document.createElement('div');
        pageHeaderBorder.style.position = 'absolute';
        pageHeaderBorder.style.width = '100%';
        pageHeaderBorder.style.borderTop = '1px solid #BDBDBD';

        elementToAddBorderTo = document.getElementById(
          elementIdToAddBorderAfter,
        ) as HTMLElement;

        elementToAddBorderTo.appendChild(pageHeaderBorder);
      }

      createVidmobLogoSvg();
      vidmobIcon && pageContent.appendChild(vidmobIcon);

      await new Promise((resolve) =>
        requestAnimationFrame(() => resolve(null)),
      );
      await new Promise((resolve) => setTimeout(resolve, 700)); // Generous timeout for rendering and layout calculation

      const canvas = await html2canvas(pageContent, {
        height: pageContent.scrollHeight,
        allowTaint: true,
        useCORS: true,
        scale: 2,
        logging: false,
        ignoreElements: (element) => elementsIdsToIgnore.includes(element.id),
      });

      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const orientation = canvasWidth > canvasHeight ? 'l' : 'p';

      const doc = new jsPDF({
        orientation,
        unit: 'px',
        format: [canvasWidth, canvasHeight],
      });

      doc.addImage(canvas, 'PNG', 0, 0, canvasWidth, canvasHeight);
      doc.save(pdfTitle);

      onFinishCleanupStyles();

      setIsExportingPDF(false);
    } catch (error) {
      handleError(error as Error);
    }
  };

  return { onClickExportAsPDF };
};
