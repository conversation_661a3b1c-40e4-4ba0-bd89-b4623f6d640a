import React, { useContext } from 'react';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
import { useIntl } from 'react-intl';
import { InsightCreatePanelContext } from '../../creativeAnalytics/insights/components/__providers/InsightCreatePanelContextProvider';

import CommonButton from '../CommonButton';
import { getActiveInsight } from '../../redux/selectors/creativeAnalytics/insights.selectors';
import { GLOBALS } from '../../constants';
import './InsightCreatePanelFooter.scss';
import Loading from '../../vcl/ui/Loading';

const { PENDING } = GLOBALS.REDUX_LOADING_STATUS;

const InsightCreatePanelFooter = () => {
  const intl = useIntl();
  const {
    handleSubmit,
    isSubmitDisabled,
    currentPageIndex = 0,
    isCreativeExamplesPanel,
    shouldShowCustomGroupCreativeExamples,
    setCurrentPageIndex = () => {},
  } = useContext(InsightCreatePanelContext);

  const { insightSavingStatus, isPublished } = useSelector(getActiveInsight);

  const publishButtonClass = classNames({
    'publish-button': true,
    'additional-fields': true,
  });

  const backButtonClass = classNames({
    'back-button': true,
    'additional-fields': true,
  });

  const showNextButton =
    (isCreativeExamplesPanel || shouldShowCustomGroupCreativeExamples) &&
    currentPageIndex < 1;

  const isInsightBeingPublished =
    insightSavingStatus === PENDING && isPublished === true;

  const confirmButton = showNextButton ? (
    <CommonButton
      disabled={isSubmitDisabled}
      buttonColoring="primary"
      buttonSize="medium"
      className={publishButtonClass}
      onClick={() => setCurrentPageIndex(currentPageIndex + 1)}
    >
      {intl.messages['ui.user.insightCreatePanel.footer.next.label']}
    </CommonButton>
  ) : (
    <CommonButton
      disabled={isSubmitDisabled}
      buttonColoring="primary"
      buttonSize="medium"
      className={publishButtonClass}
      onClick={handleSubmit}
    >
      {isInsightBeingPublished ? (
        <Loading dots />
      ) : (
        intl.messages['ui.notes.subpages.newNote.editor.publishLabel']
      )}
    </CommonButton>
  );

  return (
    <div className="panel-footer insight-footer">
      {currentPageIndex === 1 && (
        <CommonButton
          buttonColoring="secondary"
          buttonSize="medium"
          className={backButtonClass}
          onClick={() => setCurrentPageIndex(currentPageIndex - 1)}
        >
          {intl.messages['button.global.back.label']}
        </CommonButton>
      )}
      {confirmButton}
    </div>
  );
};

export default InsightCreatePanelFooter;
