import React, { useContext, useState } from 'react';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { InsightCreatePanelContext } from '../../creativeAnalytics/insights/components/__providers/InsightCreatePanelContextProvider';
import CollapseButton from '../../creativeAnalytics/components/CollapseButton/CollapseButton';
import MoreOptionsDropDown from '../MoreOptionsDropDown';
import { showLocalizedConfirmationModal } from '../../utils/confirmationModalControls';
import classnames from 'classnames';

import { getActiveInsight } from '../../redux/selectors/creativeAnalytics/insights.selectors';
import Loading from '../../vcl/ui/Loading';
import { GLOBALS } from '../../constants';
import './InsightCreatePanelHeader.scss';

const { PENDING } = GLOBALS.REDUX_LOADING_STATUS;

const InsightCreatePanelHeader = () => {
  const intl = useIntl();
  const { onClosePanel, handleDelete, handleSaveDraft, isSaveDraftEnabled } =
    useContext(InsightCreatePanelContext);

  const [moreOptionsIsOpen, setMoreOptionsIsOpen] = useState(false);
  const { insightSavingStatus, isDraft } = useSelector(getActiveInsight);
  const isInsightBeingSavedAsDraft =
    insightSavingStatus === PENDING && isDraft === true;

  const showDeleteConfirmationModal = () => {
    showLocalizedConfirmationModal(
      'ui.user.insightCreatePanel.deleteModal.header',
      'ui.user.insightCreatePanel.deleteModal.description',
      'button.global.delete.label',
      'ui.user.insightCreatePanel.deleteModal.cancel.label',
    )
      .then(handleDelete)
      .catch(() => {});
  };

  const moreOptionsItems = [
    {
      name: intl.messages['ui.user.insightCreatePanel.header.saveDraft.label'],
      id: 'save-draft',
      onClick() {
        if (isSaveDraftEnabled) {
          handleSaveDraft();
        }
      },
    },
    {
      name: intl.messages[
        'ui.user.insightCreatePanel.header.deleteDraft.label'
      ],
      id: 'delete-draft',
      onClick: showDeleteConfirmationModal,
    },
  ];

  const panelHeaderClasses = classnames({
    'panel-header': true,
    'insight-header': true,
    'save-disabled': !isSaveDraftEnabled,
  });

  return (
    <div className={panelHeaderClasses}>
      <h2 className="section-header-left-aligned-black">
        {intl.messages['ui.user.insightCreatePanel.header']}
      </h2>
      {isInsightBeingSavedAsDraft ? (
        <Loading dots />
      ) : (
        <MoreOptionsDropDown
          isOpen={moreOptionsIsOpen}
          dropDownItems={moreOptionsItems}
          setIsOpen={setMoreOptionsIsOpen}
        />
      )}
      <CollapseButton
        controlSide="RIGHT"
        isDisabled={false}
        onClick={onClosePanel}
      />
    </div>
  );
};

export default InsightCreatePanelHeader;
