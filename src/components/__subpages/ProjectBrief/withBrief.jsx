import React, { Component } from 'react';
import { IntlShape } from '../../../utils/intlShape';
import { injectIntl } from 'react-intl';
import { connect } from 'react-redux';
import { func, string, number, shape, bool } from 'prop-types';
import Loading from '../../../vcl/ui/Loading';
import {
  PROJECT,
  MEDIA,
  APP_CONFIG,
  PROJECT_CONSTANTS,
  PROJECT_CREATE,
} from '../../../constants';
import {
  showLocalizedErrorBar,
  showLocalizedConfirmationBar,
} from '../../../utils/showConfirmationBar';
import { previewMedia } from '../../../redux/actions/mediaPreview.actions';

import { showConfirmationModal } from '../../../utils/confirmationModalControls';
import isArrayShallowEqual from '../../../utils/isArrayShallowEqual';
import promiseSequentially from '../../../utils/promiseSequentially';
import vmErrorLog from '../../../utils/vmErrorLog';
import store from '../../../redux/store';

import ProjectService from '../../../apiServices/ProjectService';
import ProjectSowService from '../../../apiServices/ProjectSowService';
import BriefDocumentService from '../../../apiServices/BriefDocumentService';

import SowDocumentsStructure from '../../../featureServices/SowDocuments/SowDocuments';
import BriefDocumentsStructure from '../../../featureServices/BriefDocuments/BriefDocuments';
import { currentProjectShape } from '../../../featureServices/CurrentProject/CurrentProject';
import { getCurrentProject } from '../../../featureServices/CurrentProject/getCurrentProject';
import { getProjectMilestoneStatusAsShape } from '../../../featureServices/ProjectMilestoneStatus/ProjectMilestoneStatus';
import {
  getAsAppropriateBriefQuestionShape,
  OTHER_ANSWER_VALUE,
} from '../../../featureServices/BriefQuestion/BriefQuestion';
import ProjectPlatformAdAccountService from '../../../apiServices/ProjectPlatformAdAccountService';
import { completeTourRequirement } from '../../../redux/actions/intercomTour.actions';
import { PROJECT_IN_EDIT_REQUIREMENT } from '../../../constants/intercomTour.constants';
import { CHECK_CURRENT_PROJECT_KICK_OFF_REQUIREMENTS } from '../../../redux/action.types';

// A large number to bypass pagination.
// Definitely not a best practice, but how we are doing this for now.
const PER_PAGE_ALL = 200;

const { PROJECT_STATUS } = PROJECT;

const withBrief = (BriefDataComponent) => {
  class WithBrief extends Component {
    state = {
      sowDocuments: null,
      briefDocuments: null,
      briefQuestions: [],
      updatedAnswersByQuestionId: {},
      cachedOtherText: {},
      isLoadingProject: true,
      isLoadingBriefQuestions: true,
      isLoadingBriefDocuments: true,
      isLoadingSowDocuments: true,
      isLoadingProjectStatus: true,
      milestone: null,
      mediaIsRequired: null,
      project: null,
      projectPlatformAdAccounts: null,
      hasKickOffBannerDisplayed: false,
    };

    cachedQuestionCallbacks = {};

    componentDidMount() {
      const { project, isInProjectCreate, onUserUpdate } = this.props;
      this._isMounted = true;
      this.loadInitialData();

      // onUserUpdate was added to enable next button when component loads
      // allows user to continue without adding any information
      onUserUpdate(true, {});

      if (!isInProjectCreate) {
        this.getProjectPlatformAdAccounts(project.id);
      }
    }

    componentDidUpdate() {
      const { project, isInProjectCreate } = this.props;
      // Here we are checking if the project is in edit status and if it is
      // we call the action in redux saying that the requirement has been met to trigger the Intercom tour

      if (!isInProjectCreate && project && project.isInEdit) {
        this.props.completeTourRequirement(PROJECT_IN_EDIT_REQUIREMENT);
      }
    }

    componentWillUnmount() {
      this._isMounted = false;
    }

    getProjectPlatformAdAccounts = (projectId) => {
      ProjectPlatformAdAccountService.getProjectPlatformAdAccounts(projectId)
        .then((apiResponse) => {
          this.setState({ projectPlatformAdAccounts: apiResponse.data });
        })
        .catch((err) => {
          vmErrorLog(
            err,
            'withProjectBrief',
            'Error retrieving project ad accounts platform' + projectId,
          );
          showLocalizedErrorBar(
            'error.api.brief.projectPlatformAdAccount.failed',
          );
        });
    };

    pollAndUpdateUploadCompletedMedia = (fileId, mediaType) => {
      if (fileId && mediaType) {
        let getFilePromise = Promise.resolve(null);
        const isProjectBrief =
          mediaType === MEDIA.MEDIA_TYPE.PROJECT_BRIEF_MEDIA;
        const isProjectSow =
          mediaType === MEDIA.MEDIA_TYPE.PROJECT_BRIEF_SOW_MEDIA;

        if (isProjectBrief) {
          getFilePromise = ProjectService.getProjectBrief(fileId);
        }

        if (isProjectSow) {
          getFilePromise = ProjectService.getSowDocument(fileId);
        }

        getFilePromise.then((res) => {
          if (res && this._isMounted) {
            const { data } = res;
            if (
              [
                MEDIA.MEDIA_FILE_PROCESSING_STATE.COMPLETE_STATE,
                MEDIA.MEDIA_FILE_PROCESSING_STATE.FAILED_STATE,
              ].includes(data.media.processingState)
            ) {
              if (isProjectBrief) {
                this.setState((prevState) => ({
                  briefDocuments: new BriefDocumentsStructure(
                    prevState.briefDocuments.rawBriefData.map((file) => {
                      // rawBriefData for platform upload do not contain ids so we need to match the media id instead
                      if (file.media.id === data.media.id) {
                        file.media = {
                          ...file.media,
                          ...data.media,
                        };
                      }

                      return file;
                    }),
                    this.detatchBriefDocument,
                    this.previewBriefMedia,
                  ),
                }));
              }

              if (isProjectSow) {
                this.setState((prevState) => ({
                  sowDocuments: new SowDocumentsStructure(
                    prevState.sowDocuments.rawSowData.map((file) => {
                      if (file.id === data.id) {
                        file.media = {
                          ...file.media,
                          ...data.media,
                        };
                      }

                      return file;
                    }),
                    this.detatchBriefSow,
                    this.previewBriefMedia,
                  ),
                }));
              }
            } else {
              setTimeout(
                () => this.pollAndUpdateUploadCompletedMedia(fileId, mediaType),
                APP_CONFIG.BRIEF_POLLING_TIME_INTERVAL,
              );
            }
          }
        });
      }
    };

    pollAndUpdateUploadCompleteQuestionMedia = (questionId, mediaId) => {
      ProjectService.getProjectQuestionAttachments(
        this.state.project.id,
        questionId,
      ).then((res) => {
        if (this._isMounted) {
          const { data } = res;

          const attachment = data.find((attach) => attach.media.id === mediaId);

          if (attachment) {
            if (
              attachment.media.processingState ===
              MEDIA.MEDIA_FILE_PROCESSING_STATE.COMPLETE_STATE
            ) {
              this.setState((prevState) => ({
                briefQuestions: prevState.briefQuestions.map((question) => {
                  if (question.id === questionId) {
                    question.files = question.files.map((file) => {
                      if (file.mediaId === mediaId) {
                        file.mediaStatus =
                          MEDIA.MEDIA_FILE_PROCESSING_STATE.COMPLETE_STATE;
                        file.media.mediaStatus =
                          MEDIA.MEDIA_FILE_PROCESSING_STATE.COMPLETE_STATE;
                      }

                      return file;
                    });
                  }

                  return question;
                }),
              }));
            } else {
              setTimeout(
                () =>
                  this.pollAndUpdateUploadCompleteQuestionMedia(
                    questionId,
                    mediaId,
                  ),
                APP_CONFIG.BRIEF_POLLING_TIME_INTERVAL,
              );
            }
          }
        }
      });
    };

    loadInitialData() {
      const { projectId } = this.props;
      this.loadProjectIfNeeded().then(() => {
        // create project brief doesn't need to know about the status
        if (!projectId) {
          this.getProjectStatusData();
        }

        this.getInitialBriefQuestions();
        this.getSow();
        this.getBriefDocuments();
      });
    }

    loadProjectIfNeeded = () => {
      const { projectId, project } = this.props;

      return new Promise((resolve) => {
        if (project) {
          this.setState({ project, isLoadingProject: false }, resolve);
        } else {
          getCurrentProject(projectId).then((project) => {
            this.setState({ project, isLoadingProject: false }, resolve);
          });
        }
      });
    };

    getInitialBriefQuestions = () =>
      this.getBriefQuestions()
        .then(this.processBriefQuestions)
        .then(this.updateStateAfterLoadingBriefQuestions)
        .catch(this.showBriefQuestionsError);

    getBriefQuestions = () => {
      const { project } = this.state;
      return ProjectService.getProjectQuestions(project.id, 'attachments', {
        perPage: PER_PAGE_ALL,
      });
    };

    getBriefQuestionWithCallbacks = (briefQuestion) =>
      getAsAppropriateBriefQuestionShape(
        briefQuestion,
        {
          onDelete: this.detachFileFromBriefQuestion,
          onPreview: this.previewBriefMedia,
        },
        (mediaId) => this.attachFileToBriefQuestion(briefQuestion.id, mediaId),
      );

    getProjectStatusData = () =>
      Promise.all([this.getMilestoneInfo(), this.getMediaIsRequired()]).then(
        ([milestone, mediaIsRequired]) => {
          this.setState({
            milestone,
            mediaIsRequired,
            isLoadingProjectStatus: false,
          });
        },
      );

    getLongAnswerUpdateBody = (answerUpdateToAdd) => ({
      answers: [{ answer: answerUpdateToAdd.text }],
    });

    // get a callback to use when updating a long answer question
    getLongAnswerUpdateCallback = (questionObject) => {
      let { id, text } = questionObject;
      const key = `${id}-${text}`;

      if (!text) {
        text = '';
      }

      // the long answer callback is cached to avoid rerenders, since rerenders
      // cause a noticable lag when typing. We should consider caching or other solutions
      // for other parts of this as well, the render function is pretty busy here.
      if (this.cachedQuestionCallbacks[key]) {
        return this.cachedQuestionCallbacks[key];
      }

      const callback = (newText) => {
        const { updatedAnswersByQuestionId } = this.state;

        if (newText === text && updatedAnswersByQuestionId[id]) {
          this.resetUpdatedAnswer(id);
          return;
        }

        if (newText === text) {
          return;
        }

        this.setState((prevState) => ({
          ...prevState,
          updatedAnswersByQuestionId: {
            ...prevState.updatedAnswersByQuestionId,
            [id]: { text: newText },
          },
        }));
      };

      this.cachedQuestionCallbacks[key] = callback;

      return callback;
    };

    getCheckboxSelectCallback = (questionOptionObject) => {
      const { optionSetId, questionId, compoundKey } = questionOptionObject;
      const initialValues = questionOptionObject.currentValues;
      const initialOtherText = questionOptionObject.otherText;

      return (selectedValue, removeFromSelection, belowSelectionLimit) => {
        const isOtherSelected = selectedValue === OTHER_ANSWER_VALUE;

        if (!belowSelectionLimit && !removeFromSelection) {
          // do nothing if a new check is requested when the selection limit is full
          return;
        }

        this.setState((prevState) => {
          const existingAnswerForOptionSet =
            prevState.updatedAnswersByQuestionId[compoundKey] || {};
          const lastValuesForOptionSet =
            existingAnswerForOptionSet.value || initialValues;
          const lastOtherText =
            existingAnswerForOptionSet.otherText || initialOtherText;

          // revisit this for single select.
          const willDeselectOtherText = isOtherSelected && removeFromSelection;

          // if we are adding other text
          const otherTextForSection =
            !removeFromSelection && isOtherSelected
              ? // use cached other text if available and we are selecting other text
                prevState.cachedOtherText[compoundKey] || null
              : // if we are deselecting other text, remove it, or else defer to prevState
                willDeselectOtherText
                ? null
                : lastOtherText;

          const updatedValuesForOptionSet = removeFromSelection
            ? lastValuesForOptionSet.filter((value) => value !== selectedValue)
            : [...lastValuesForOptionSet, selectedValue];

          if (
            otherTextForSection === initialOtherText &&
            isArrayShallowEqual(
              updatedValuesForOptionSet.sort(),
              initialValues.sort(),
            )
          ) {
            return this.getStateWithoutAnswer(prevState, compoundKey);
          }

          return {
            ...prevState,
            updatedAnswersByQuestionId: {
              ...prevState.updatedAnswersByQuestionId,
              [compoundKey]: {
                ...existingAnswerForOptionSet,
                value: updatedValuesForOptionSet,
                otherText: otherTextForSection,
                questionId,
                optionSetId,
              },
            },
          };
        });
      };
    };

    getMultipleChoiceAnswerUpdateBody = (answerUpdatesToAdd) => ({
      answers: answerUpdatesToAdd.map((answerUpdate) => {
        const answer = {
          questionOptionSetId: answerUpdate.optionSetId,
        };

        return {
          ...answer,
          questionOptionIds: answerUpdate.value,
          questionOptionOther: answerUpdate.otherText,
        };
      }),
    });

    getMultipleChoiceSelectCallback = (questionOptionObject) => {
      const { optionSetId, otherText, questionId, compoundKey } =
        questionOptionObject;
      const initialValue = questionOptionObject.currentValues;

      return (newValue) => {
        const { updatedAnswersByQuestionId } = this.state;
        const currentUpdates = updatedAnswersByQuestionId[compoundKey];

        if (newValue === initialValue[0]) {
          if (!currentUpdates) {
            return;
          }

          if (
            newValue !== OTHER_ANSWER_VALUE ||
            otherText === currentUpdates.otherText
          ) {
            this.resetUpdatedAnswer(compoundKey);
            return;
          }
        }

        this.setState((prevState) => {
          const existingAnswerForId =
            prevState.updatedAnswersByQuestionId[compoundKey] || {};
          // if there's any otherText to pull from our saved
          const otherTextToAdd =
            newValue === OTHER_ANSWER_VALUE &&
            prevState.cachedOtherText[compoundKey]
              ? prevState.cachedOtherText[compoundKey]
              : null;
          return {
            ...prevState,
            updatedAnswersByQuestionId: {
              ...prevState.updatedAnswersByQuestionId,
              [compoundKey]: {
                ...existingAnswerForId,
                value: [newValue],
                otherText: otherTextToAdd,
                questionId,
                optionSetId,
              },
            },
          };
        });
      };
    };

    getMultipleChoiceOtherCallback = (questionOptionObject) => {
      const { compoundKey, otherText, questionId, optionSetId } =
        questionOptionObject;

      return (newOtherText) => {
        if (otherText === newOtherText) {
          this.resetUpdatedAnswerAndCachedText(compoundKey);
        }

        this.setState((prevState) => {
          const existingAnswerForId = prevState.updatedAnswersByQuestionId[
            compoundKey
          ] || {
            questionId,
            optionSetId,
          };
          return {
            ...prevState,
            updatedAnswersByQuestionId: {
              ...prevState.updatedAnswersByQuestionId,
              [compoundKey]: {
                ...existingAnswerForId,
                otherText: newOtherText,
              },
            },
            cachedOtherText: {
              ...prevState.cachedOtherText,
              [compoundKey]: newOtherText,
            },
          };
        });
      };
    };

    getMilestoneInfo = () => {
      const { project } = this.state;
      const { intl } = this.props;
      return ProjectService.getMilestone(project.id).then((milestone) =>
        getProjectMilestoneStatusAsShape(milestone.data, null, intl),
      );
    };

    getMediaIsRequired = () => {
      const { project } = this.state;
      return new Promise((resolve) => {
        // if you have no media but are in a different state, we actually don't need to redirect you
        // NOTE: media is only required for CREATIVE products
        if (
          project.status === PROJECT_STATUS.IN_BUILD &&
          (project.productType === PROJECT.PRODUCT_TYPE.CREATIVE ||
            (project.product &&
              project.product.type === PROJECT.PRODUCT_TYPE.CREATIVE))
        ) {
          resolve(
            ProjectService.getProject(project.id, 'totalMediaCount').then(
              ({ data }) => data.totalMediaCount === 0,
            ),
          );
        }

        resolve(false);
      });
    };

    getBriefDocuments = () => {
      const { project } = this.state;
      const { COMPLETE_STATE, FAILED_STATE, ARCHIVING_STATE } =
        MEDIA.MEDIA_FILE_PROCESSING_STATE;
      // This exists in CSM but without extra fields.
      return ProjectService.getBriefs(project.id, 'uploader', {
        perPage: PER_PAGE_ALL,
      })
        .then(({ data }) => {
          const statesTusIsManaging = [
            COMPLETE_STATE,
            FAILED_STATE,
            ARCHIVING_STATE,
          ];
          const completeOrFailedUploads = data.filter((brief) =>
            statesTusIsManaging.includes(brief.media?.processingState),
          );
          this.setState({
            briefDocuments: new BriefDocumentsStructure(
              completeOrFailedUploads,
              this.detatchBriefDocument,
              this.previewBriefMedia,
            ),
            isLoadingBriefDocuments: false,
          });
          const archivingUploads = completeOrFailedUploads.filter(
            (brief) => brief.media?.processingState === ARCHIVING_STATE,
          );
          if (archivingUploads.length > 0) {
            archivingUploads.forEach((brief) =>
              this.pollAndUpdateUploadCompletedMedia(
                brief.id,
                brief.media.mediaType,
              ),
            );
          }
        })
        .catch(this.showBriefDocumentError);
    };

    getSow = () => {
      const { project } = this.state;
      if (!project.permissions.canReadProjectSow()) {
        this.setState({ isLoadingSowDocuments: false });
        return;
      }

      return ProjectService.getProjectSows(project.id, {
        perPage: PER_PAGE_ALL,
      })
        .then(({ data }) => {
          this.setState({
            sowDocuments: new SowDocumentsStructure(
              data,
              this.detatchBriefSow,
              this.previewBriefMedia,
            ),
            isLoadingSowDocuments: false,
          });
        })
        .catch(this.showSowError);
    };

    getStateWithoutAnswer = (prevState, id) => {
      const copyOfUpdatedAnswers = { ...prevState.updatedAnswersByQuestionId };
      delete copyOfUpdatedAnswers[id];
      return {
        ...prevState,
        updatedAnswersByQuestionId: copyOfUpdatedAnswers,
      };
    };

    processBriefQuestions = (apiResponse) =>
      apiResponse.data.map((briefQuestion) =>
        this.getBriefQuestionWithCallbacks(briefQuestion),
      );

    // update state all at once with the new state information.
    updateStateAfterLoadingBriefQuestions = (newBriefQuestions) => {
      if (!this._isMounted) {
        return;
      }

      this.setState(() => ({
        briefQuestions: newBriefQuestions,
        isLoadingBriefQuestions: false,
      }));
    };

    updateBriefQuestion = (projectId, questionId, updateBody) =>
      ProjectService.updateProjectQuestion(projectId, questionId, updateBody)
        .then((questionResult) =>
          // right now the question result does not include attachments, so we need to fetch them in
          // a separate API call. The API gives us no way to get a single question with its answers
          // and it is not returned in the save response.
          Promise.all([
            questionResult,
            ProjectService.getProjectQuestionAttachments(projectId, questionId),
          ]),
        )
        .then(([questionResult, attachmentsResult]) =>
          this.getBriefQuestionWithCallbacks({
            ...questionResult.data,
            attachments: attachmentsResult.data,
          }),
        );

    updateBriefQuestions = (briefAnswerUpdatesById) => {
      const { project } = this.state;
      const projectId = project.id;
      const answerUpdates = this.state.briefQuestions
        .map((briefQuestion) => {
          if (briefQuestion.isLongAnswer) {
            const answerUpdateToAdd = briefAnswerUpdatesById[briefQuestion.id];
            if (!answerUpdateToAdd) {
              return null;
            }

            const updateBody = this.getLongAnswerUpdateBody(answerUpdateToAdd);
            // DRAGON: We can and should execute these immediately once the API can handle it.
            // here we are making a function that will return a promise to permit a delay.
            return () =>
              this.updateBriefQuestion(projectId, briefQuestion.id, updateBody);
          }

          const updates = briefQuestion.optionSets.reduce(
            (updates, optionSet) => {
              const result = briefAnswerUpdatesById[optionSet.compoundKey];
              return result ? [...updates, result] : updates;
            },
            [],
          );

          const updateBody = updates.length
            ? this.getMultipleChoiceAnswerUpdateBody(updates)
            : null;
          return updateBody
            ? // DRAGON: We can and should execute these immediately once the API can handle it.
              // here we are making a function that will return a promise to permit a delay.
              () =>
                this.updateBriefQuestion(
                  projectId,
                  briefQuestion.id,
                  updateBody,
                )
            : null;
        })
        .filter((executeUpdateFunction) => executeUpdateFunction !== null);

      return promiseSequentially(answerUpdates)
        .then((updates) => {
          const updatesById = {};
          updates.forEach((question) => {
            updatesById[question.id] = question;
          });
          return this.replaceQuestions(updatesById);
        })
        .then(() => {
          showLocalizedConfirmationBar('confirmationBar.brief.success');
        });
    };

    attachBriefSow = (mediaId) => {
      const projectId = this.state.project.id;
      return ProjectSowService.createSowDocument(projectId, mediaId).then(
        ({ data }) => {
          this.setState((prevState) => {
            const combinedBriefData = [
              ...prevState.sowDocuments.rawSowData,
              data,
            ];
            return {
              ...prevState,
              sowDocuments: new SowDocumentsStructure(
                combinedBriefData,
                this.detatchBriefSow,
                this.previewBriefMedia,
              ),
            };
          });
          this.pollAndUpdateUploadCompletedMedia(data.id, data.media.mediaType);
        },
      );
    };

    attachBriefDocument = (mediaId) => {
      const projectId = this.state.project?.id || this.props.project?.id;
      return BriefDocumentService.getAllProjectBriefs(projectId)
        .then(({ data }) => {
          const newBriefData = data.filter(
            (brief) => brief.media?.id === mediaId,
          )[0];
          // the new brief doc doesn't have the owner extra field, so we need to add it here.
          const newBriefDoc = {
            ...newBriefData,
            media: {
              ...newBriefData.media,
              owner: { displayName: this.props.currentUserName },
            },
          };
          // here we add the new brief to the full API response used to build the
          // brief document list.
          this.setState((prevState) => {
            const combinedBriefData = [
              ...prevState.briefDocuments.rawBriefData,
              newBriefDoc,
            ];
            return {
              ...prevState,
              briefDocuments: new BriefDocumentsStructure(
                combinedBriefData,
                this.detatchBriefDocument,
                this.previewBriefMedia,
              ),
            };
          });
          this.pollAndUpdateUploadCompletedMedia(
            newBriefData.id,
            newBriefData.media.mediaType,
          );
          this.checkProjectKickOffRequirements();
        })
        .catch((e) => {
          vmErrorLog(
            e,
            'withBrief, attachBriefDocument',
            'Error getting all project briefs',
          );
        });
    };

    attachBriefDocumentFromPlatform = (mediaId) => {
      const projectId = this.state.project.id;
      return BriefDocumentService.importProjectBrief(projectId, mediaId).then(
        ({ data }) => {
          ProjectService.getBriefs(projectId, { perPage: PER_PAGE_ALL }).then(
            (res) => {
              const uploadedBriefDoc = res.data.find(
                (file) => file.media.id === data.media.id,
              );

              if (uploadedBriefDoc) {
                // the new brief doc doesn't have the owner extra field, so we need to add it here.
                const newBriefDoc = {
                  id: uploadedBriefDoc.id,
                  ...data,
                  media: {
                    ...data.media,
                    owner: { displayName: this.props.currentUserName },
                  },
                };

                // here we add the new brief to the full API response used to build the
                // brief document list.
                this.setState((prevState) => {
                  const combinedBriefData = [
                    ...prevState.briefDocuments.rawBriefData,
                    newBriefDoc,
                  ];
                  return {
                    ...prevState,
                    briefDocuments: new BriefDocumentsStructure(
                      combinedBriefData,
                      this.detatchBriefDocument,
                      this.previewBriefMedia,
                    ),
                  };
                });

                this.pollAndUpdateUploadCompletedMedia(
                  uploadedBriefDoc.id,
                  data.media.mediaType,
                );
                this.checkProjectKickOffRequirements();
              }
            },
          );
        },
      );
    };

    attachFileToBriefQuestion = (briefQuestionId, mediaId) => {
      const projectId = this.state.project.id;
      return this.updateBriefQuestion(projectId, briefQuestionId, {
        attachments: [{ mediaId }],
      }).then((newBriefQuestion) => {
        return this.replaceQuestions({
          [briefQuestionId]: newBriefQuestion,
        }).then(() => {
          this.pollAndUpdateUploadCompleteQuestionMedia(
            briefQuestionId,
            mediaId,
          );
          this.checkProjectKickOffRequirements();
          return Promise.resolve();
        });
      });
    };

    detatchBriefSow = (sowId) => {
      const { intl } = this.props;

      showConfirmationModal({
        cancelButtonLabel: intl.messages['button.global.cancel.label'],
        submitButtonLabel: intl.messages['button.global.delete.label'],
        headerText: intl.messages['modal.deleteFile.header'],
      })
        .then(() => {
          ProjectSowService.deleteSowDocument(sowId).then(() => {
            this.setState((prevState) => {
              const sowDocsWithoutFiles = [
                ...prevState.sowDocuments.rawSowData,
              ].filter((doc) => doc.id !== sowId);
              return {
                ...prevState,
                sowDocuments: new SowDocumentsStructure(
                  sowDocsWithoutFiles,
                  this.detatchBriefSow,
                  this.previewBriefMedia,
                ),
              };
            });
          });
        })
        .catch(() => {});
    };

    detatchBriefDocument = (briefId) => {
      const { intl } = this.props;

      showConfirmationModal({
        cancelButtonLabel: intl.messages['button.global.cancel.label'],
        submitButtonLabel: intl.messages['button.global.delete.label'],
        headerText: intl.messages['modal.deleteFile.header'],
      })
        .then(() => {
          BriefDocumentService.deleteProjectBrief(briefId).then(() => {
            this.setState((prevState) => {
              const briefDocumentWithoutFiles = [
                ...prevState.briefDocuments.rawBriefData,
              ].filter((doc) => doc.id !== briefId);
              this.checkProjectKickOffRequirements();
              return {
                ...prevState,
                briefDocuments: new BriefDocumentsStructure(
                  briefDocumentWithoutFiles,
                  this.detatchBriefDocument,
                  this.previewBriefMedia,
                ),
              };
            });
          });
        })
        .catch(() => {});
    };

    detachFileFromBriefQuestion = (attachmentId, mediaId, questionId) => {
      const { intl } = this.props;
      showConfirmationModal({
        cancelButtonLabel: intl.messages['button.global.cancel.label'],
        submitButtonLabel: intl.messages['button.global.delete.label'],
        headerText: intl.messages['modal.deleteFile.header'],
      })
        .then(() =>
          ProjectService.deleteProjectQuestionAttachment(attachmentId).then(
            () => {
              this.checkProjectKickOffRequirements();
              return this.removeFileFromQuestionInState(questionId, mediaId);
            },
          ),
        )
        .catch(() => {});
    };

    removeFileFromQuestionInState = (questionId, mediaId) =>
      new Promise((resolve) => {
        this.setState((prevState) => {
          const newQuestions = prevState.briefQuestions.map((question) => {
            if (question.id === questionId) {
              question.files = question.files.filter(
                (file) => file.mediaId !== mediaId,
              );
            }

            return question;
          });
          return { ...prevState, briefQuestions: newQuestions };
        }, resolve);
      });

    // Replace brief questions with updated versions
    // Check if the view is still showed to update it
    replaceQuestions = (newQuestionsById) => {
      return new Promise((resolve) => {
        if (this._isMounted) {
          this.setState((prevState) => {
            const newQuestions = prevState.briefQuestions.map(
              (existingQuestion) =>
                newQuestionsById[existingQuestion.id]
                  ? newQuestionsById[existingQuestion.id]
                  : existingQuestion,
            );

            return { ...prevState, briefQuestions: newQuestions };
          }, resolve);
        }
      });
    };

    // Reset an updated answer with a given ID, so it is unchanged
    resetUpdatedAnswer = (id) => {
      this.setState((prevState) => this.getStateWithoutAnswer(prevState, id));
    };

    previewBriefMedia = (media) => {
      if (media) {
        this.props.previewMedia(media, { mediaPreviewOnAPath: false });
      }
    };

    showBriefQuestionsError = (err) => {
      vmErrorLog(err, 'Brief Data', 'Error occured when loading Brief Data');
      showLocalizedErrorBar('error.api.brief.failed');
    };

    lookupQuestionById = (id) => {
      const { briefQuestions } = this.state;
      const unCompoundedId = Number(String(id).split('-')[0]); // ID can be like 34-7. We just need number before '-'

      return briefQuestions.find((question) => question.id === unCompoundedId);
    };

    submitChanges = () => {
      const { updatedAnswersByQuestionId, project, briefQuestions } =
        this.state;

      const newUpdatedAnswersByQuestionId = { ...updatedAnswersByQuestionId };

      const { ENRICHED_FEED } = PROJECT_CREATE.PRODUCT_IDENTIFIER;

      if (project.product?.identifier === ENRICHED_FEED) {
        const { BRIEF_QUESTION_TYPES } = PROJECT_CONSTANTS;
        const toggleQuestion = briefQuestions.find(
          (question) =>
            question.questionType === BRIEF_QUESTION_TYPES.TOGGLE_ANSWER,
        );

        if (toggleQuestion) {
          const chosenPlatformId =
            updatedAnswersByQuestionId[toggleQuestion.optionSets[0].compoundKey]
              ?.value[0] || toggleQuestion.optionSets[0].currentValues[0];

          briefQuestions.forEach((briefQuestion) => {
            if (
              briefQuestion.parentQuestionOption &&
              briefQuestion.parentQuestionOption.id !== chosenPlatformId &&
              briefQuestion.isRequired
            ) {
              if (
                briefQuestion.questionType === BRIEF_QUESTION_TYPES.TEXT_ANSWER
              ) {
                newUpdatedAnswersByQuestionId[briefQuestion.id] = {
                  text: 'null',
                };
              } else if (
                briefQuestion.questionType ===
                BRIEF_QUESTION_TYPES.MULTIPLE_CHOICE_ANSWER
              ) {
                newUpdatedAnswersByQuestionId[
                  briefQuestion.optionSets[0].compoundKey
                ] = {
                  value: [
                    briefQuestion.optionSets[0].options.find(
                      (option) => option.name === 'Not applicable',
                    ).value,
                  ],
                  optionSetId: briefQuestion.optionSets[0].optionSetId,
                  questionId: briefQuestion.optionSets[0].questionId,
                };
              }
            }
          });
        }
      }

      return this.updateBriefQuestions(newUpdatedAnswersByQuestionId).then(
        () => {
          this.setState({ updatedAnswersByQuestionId: {} });
          this.checkProjectKickOffRequirements();
        },
      );
    };

    discardChanges = () => {
      this.setState({ updatedAnswersByQuestionId: {} });
    };

    checkProjectKickOffRequirements = () => {
      const { isInProjectCreate } = this.props;
      if (!isInProjectCreate) {
        store.dispatch({ type: CHECK_CURRENT_PROJECT_KICK_OFF_REQUIREMENTS });
      }
    };

    render() {
      // brief in active project doesn't get passed a projectId, just the actual project
      // this is used to determine where the brief is being displayed
      const { isEnterprisePartner, isInProjectCreate, onSavingBrief } =
        this.props;

      const projectId = isInProjectCreate
        ? this.props.projectId
        : this.props.project.id;

      const {
        briefDocuments,
        briefQuestions,
        isLoadingBriefDocuments,
        isLoadingBriefQuestions,
        isLoadingProject,
        isLoadingProjectStatus,
        isLoadingSowDocuments,
        mediaIsRequired,
        milestone,
        project,
        projectPlatformAdAccounts,
        sowDocuments,
        updatedAnswersByQuestionId,
      } = this.state;

      if (isLoadingProject) {
        return <Loading noLabel />;
      }

      const {
        launchDate,
        deliveryDate,
        expectedDate,
        isComplete,
        permissions,
        status,
        isDataProduct,
        reportAnalysisPeriodBegin,
        reportAnalysisPeriodEnd,
      } = project;

      // data reports do not have a delivery date (before they're purchased?) so to allow the card rail
      // to render we pass down the expected date
      const effectiveDeliveryDate = deliveryDate || expectedDate;

      return (
        <BriefDataComponent
          analysisPeriodBegin={reportAnalysisPeriodBegin}
          analysisPeriodEnd={reportAnalysisPeriodEnd}
          attachBriefDocument={this.attachBriefDocument}
          attachBriefDocumentFromPlatform={this.attachBriefDocumentFromPlatform}
          attachBriefSow={this.attachBriefSow}
          attachFileToBriefQuestion={this.attachFileToBriefQuestion}
          briefDocuments={briefDocuments}
          briefQuestions={briefQuestions}
          canReadSow={permissions.canReadProjectSow()}
          canUpdateBrief={permissions.canUpdateProjectDetails()}
          canUpdateSow={permissions.canUpdateProjectSow()}
          effectiveDeliveryDate={effectiveDeliveryDate}
          getCheckboxSelectCallback={this.getCheckboxSelectCallback}
          getLongAnswerUpdateCallback={this.getLongAnswerUpdateCallback}
          getMultipleChoiceOtherCallback={this.getMultipleChoiceOtherCallback}
          getMultipleChoiceSelectCallback={this.getMultipleChoiceSelectCallback}
          isEnterprisePartner={isEnterprisePartner}
          isInProjectCreate={isInProjectCreate}
          isLoadingBrief={
            isLoadingBriefDocuments ||
            isLoadingSowDocuments ||
            isLoadingBriefQuestions
          }
          isLoadingProjectStatus={isLoadingProjectStatus}
          isDataProduct={isDataProduct}
          isReadOnly={isComplete}
          launchDate={launchDate}
          mediaIsRequired={mediaIsRequired}
          milestone={milestone}
          projectPlatformAdAccounts={projectPlatformAdAccounts}
          project={project}
          projectId={projectId}
          projectStatus={status}
          sowDocuments={sowDocuments}
          updatedAnswersByQuestionId={updatedAnswersByQuestionId}
          onDiscard={this.discardChanges}
          onSavingBrief={onSavingBrief}
          onSubmit={this.submitChanges}
        />
      );
    }
  }

  WithBrief.propTypes = {
    completeTourRequirement: func.isRequired,
    currentUserName: string.isRequired,
    intl: IntlShape.isRequired,
    isEnterprisePartner: bool.isRequired,
    previewMedia: func.isRequired,
    isInProjectCreate: bool,
    project: shape(currentProjectShape),
    projectId: number,
    onSavingBrief: func,
    onUserUpdate: func,
  };

  WithBrief.defaultProps = {
    onSavingBrief() {},
    isInProjectCreate: false,
    onUserUpdate() {},
    projectId: null,
    project: null,
  };

  const mapStateToProps = ({ user, partner }) => ({
    currentUserName: user.currentUser.displayName,
    isEnterprisePartner: partner.currentPartner.isEnterprise,
  });

  return connect(mapStateToProps, { previewMedia, completeTourRequirement })(
    injectIntl(WithBrief),
  );
};

export default withBrief;
