import withBrief from './withBrief';
import { useSelector } from 'react-redux';
import React, { useState } from 'react';
import {
  MEDIA,
  PLATFORM,
  PROJECT_CONSTANTS,
  PROJECT_CREATE,
} from '../../../constants';
import { getCurrentUser } from '../../../redux/selectors/user.selectors';
import vmErrorLog from '../../../utils/vmErrorLog';
import ModalPlatformAssets from '../Assets/ModalPlatformAssets';
import { useIntl } from 'react-intl';
import Loading from '../../../vcl/ui/Loading';
import CardRail from './CardRail';
import SaveChangesBar from '../../SaveChangesBar';
import BriefQuestions from '../../../creativeProduction/components/Brief/BriefQuestions';
import BriefDocuments from '../../../creativeProduction/components/Brief/BriefDocuments';
import BriefSowDocuments from '../../../creativeProduction/components/Brief/BriefSowDocuments';
import { LoadingButton } from '../../CommonButton';
import {
  any,
  arrayOf,
  bool,
  func,
  instanceOf,
  number,
  objectOf,
  oneOfType,
  shape,
} from 'prop-types';
import {
  longAnswerBriefQuestionShape,
  multipleChoiceBriefQuestionShape,
} from '../../../featureServices/BriefQuestion/BriefQuestion';
import { currentProjectShape } from '../../../featureServices/CurrentProject/CurrentProject';
import { isoDateString } from '../../../customProps';
import BriefDocumentsStructure from '../../../featureServices/BriefDocuments/BriefDocuments';
import { projectMilestoneStatusShape } from '../../../featureServices/ProjectMilestoneStatus/ProjectMilestoneStatus';
import { projectPlatformAdAccountShape } from '../../../featureServices/projectPlatformAdAccounts/projectPlatformAdAccounts';
import SowDocumentsStructure from '../../../featureServices/SowDocuments/SowDocuments';
import GoogleDriveModal from '../Assets/GoogleDriveModal';
import BriefQuestionV2 from '../../../creativeProduction/components/Brief/BriefQuestionsV2/BriefQuestionV2.tsx';
import { getFeatureFlag } from '../../../utils/featureFlagUtils';
import LabelQuestion from '../../__pages/QuestionAnswer/LabelQuestion';
import HelperTextQuestion from '../../__pages/QuestionAnswer/HelperTextQuestion';
import dayjs from 'dayjs';
import BffBriefV2Service from '../../../apiServices/BffBriefV2Service';
import { getCurrentPartnerId } from '../../../redux/selectors/partner.selectors';
import ProjectBriefInsightsSection from '../../../creativeAnalytics/insights/components/ProjectBriefInsightsSection/ProjectBriefInsightsSection';

import './ProjectBrief.scss';

const ProjectBrief = ({
  attachBriefDocument,
  attachBriefSow,
  briefDocuments,
  canReadSow,
  canUpdateBrief,
  canUpdateSow,
  onSubmit,
  onSavingBrief,
  briefQuestions,
  updatedAnswersByQuestionId,
  isEnterprisePartner,
  isInProjectCreate,
  isLoadingBrief,
  isReadOnly,
  project,
  projectId,
  mediaIsRequired,
  sowDocuments,
  attachFileToBriefQuestion,
  attachBriefDocumentFromPlatform,
  analysisPeriodBegin,
  analysisPeriodEnd,
  effectiveDeliveryDate,
  isDataProduct,
  isLoadingProjectStatus,
  launchDate,
  milestone,
  projectPlatformAdAccounts,
  onDiscard,
  getCheckboxSelectCallback,
  getLongAnswerUpdateCallback,
  getMultipleChoiceOtherCallback,
  getMultipleChoiceSelectCallback,
}) => {
  const intl = useIntl();

  const isBriefOutputAutomationEnabled = getFeatureFlag(
    'isBriefOutputAutomationEnabled',
  );
  const [selectedDate, setSelectedDate] = useState(
    effectiveDeliveryDate ? dayjs(effectiveDeliveryDate) : null,
  );

  const [isDeliveryDateUpdated, setIsDeliveryDateUpdated] = useState(false);

  const currentWorkspaceId = useSelector(getCurrentPartnerId);

  const isSelfManagedSocialOrDigitalAd =
    project.product?.identifier === 'SELF_MANAGED_SOCIAL_OR_DIGITAL_AD' ||
    project.product?.identifier === 'SOCIAL_OR_DIGITAL_AD';

  const initialCurrentState = {
    isPlatformAssetsModalVisible: false,
    currentPlatform: null,
    currentMediaType: null,
    currentQuestionId: null,
  };

  const currentUser = useSelector(getCurrentUser);
  const [currentBriefState, setCurrentBriefState] =
    useState(initialCurrentState);
  const [isSavingBrief, setIsSavingBrief] = useState(false);

  const getProjectBriefMediaUploadOptions = (questionId = null) => {
    const mediaUploadOptions = {};

    if (isPlatformConnected(PLATFORM.DROPBOX)) {
      mediaUploadOptions.onClickDropbox = () => {
        showPlatformAssetsModal(
          PLATFORM.DROPBOX,
          MEDIA.MEDIA_TYPE.PROJECT_BRIEF_MEDIA,
          questionId,
        );
      };
    }

    if (isPlatformConnected(PLATFORM.GOOGLEDRIVE)) {
      mediaUploadOptions.onClickGoogleDrive = () => {
        showPlatformAssetsModal(
          PLATFORM.GOOGLEDRIVE,
          MEDIA.MEDIA_TYPE.PROJECT_BRIEF_MEDIA,
          questionId,
        );
      };
    }

    if (isPlatformConnected(PLATFORM.BOX)) {
      mediaUploadOptions.onClickBox = () => {
        showPlatformAssetsModal(
          PLATFORM.BOX,
          MEDIA.MEDIA_TYPE.PROJECT_BRIEF_MEDIA,
          questionId,
        );
      };
    }

    return mediaUploadOptions;
  };

  const showPlatformAssetsModal = (platform, mediaType, questionId = null) => {
    setCurrentBriefState({
      currentPlatform: platform,
      currentMediaType: mediaType,
      currentQuestionId: questionId,
      isPlatformAssetsModalVisible: true,
    });
  };

  const isPlatformConnected = (platform) => {
    const { platformAccounts } = currentUser;
    const platformStatus = platformAccounts.find(
      (item) => item.platform.IDENTIFIER === platform.IDENTIFIER,
    );

    return platformStatus && platformStatus.isEnabled;
  };

  const closePlatformAssetsModal = () => {
    setCurrentBriefState({
      currentPlatform: null,
      currentMediaType: null,
      currentQuestionId: null,
      isPlatformAssetsModalVisible: false,
    });
  };

  const attachPlatformMedia = (media) => {
    const { currentMediaType, currentQuestionId } = currentBriefState;

    if (currentMediaType === MEDIA.MEDIA_TYPE.PROJECT_BRIEF_MEDIA) {
      if (currentQuestionId) {
        attachFileToBriefQuestion(currentQuestionId, media.id);
      } else {
        attachBriefDocumentFromPlatform(media.id);
      }
    } else if (currentMediaType === MEDIA.MEDIA_TYPE.PROJECT_BRIEF_SOW_MEDIA) {
      attachBriefSow(media.id);
    } else {
      vmErrorLog(
        null,
        'attach platform assets',
        `unsupported media type: ${currentMediaType})`,
      );
    }
  };

  const onProjectCreateBriefSubmit = () => {
    const hasBriefBeenUpdated =
      Object.keys(updatedAnswersByQuestionId).length > 0;

    if (briefQuestions.length > 0 && hasBriefBeenUpdated) {
      setIsSavingBrief(true);
      onSubmit().then(() => {
        setIsSavingBrief(false);
        onSavingBrief();
      });
    } else {
      onSavingBrief();
    }
  };

  const renderPlatformAssetModal = () => {
    const { currentPlatform, currentMediaType, isPlatformAssetsModalVisible } =
      currentBriefState;

    if (!isPlatformAssetsModalVisible) {
      return null;
    }

    if (currentPlatform === PLATFORM.GOOGLEDRIVE) {
      return (
        <GoogleDriveModal
          onUpload={attachPlatformMedia}
          isBrief={true}
          onCancel={() => {
            closePlatformAssetsModal();
          }}
        />
      );
    } else {
      return (
        <ModalPlatformAssets
          currentProject={project}
          currentPlatform={currentPlatform}
          mediaType={currentMediaType}
          onCancel={() => closePlatformAssetsModal()}
          onUpload={attachPlatformMedia}
        />
      );
    }
  };

  const renderCardRail = () => (
    <div id="card-rail">
      {isLoadingProjectStatus ? (
        <Loading dots />
      ) : (
        <CardRail
          isDataProduct={isDataProduct}
          mediaIsRequired={mediaIsRequired}
          deliveryDate={effectiveDeliveryDate}
          launchDate={launchDate}
          milestone={milestone}
          project={project}
          analysisPeriodBegin={analysisPeriodBegin}
          analysisPeriodEnd={analysisPeriodEnd}
          adAccountsInfo={projectPlatformAdAccounts}
        />
      )}
    </div>
  );

  const renderSaveChangesBar = () => {
    if (isDeliveryDateUpdated) {
      return null;
    }

    return (
      <div id="changes-bar-wrapper">
        <SaveChangesBar
          hasChanges={
            Object.keys(updatedAnswersByQuestionId).length > 0 ||
            (selectedDate &&
              effectiveDeliveryDate &&
              !dayjs(effectiveDeliveryDate).isSame(selectedDate, 'day'))
          }
          onSave={onSave}
          onDiscard={() => {
            setSelectedDate(
              effectiveDeliveryDate ? dayjs(effectiveDeliveryDate) : null,
            );
            onDiscard();
          }}
        />
      </div>
    );
  };
  const onSave = async () => {
    if (
      selectedDate &&
      effectiveDeliveryDate &&
      !dayjs(effectiveDeliveryDate).isSame(selectedDate, 'day')
    ) {
      try {
        await BffBriefV2Service.setDeliveryDate(
          projectId,
          selectedDate.format('YYYY-MM-DD'),
          currentWorkspaceId,
        );
        setIsDeliveryDateUpdated(true);
      } catch (e) {
        console.error('Error updating delivery date:', e);
      }
    } else {
      onSubmit();
    }
  };

  const renderBriefSection = () => {
    const { ENRICHED_FEED } = PROJECT_CREATE.PRODUCT_IDENTIFIER;

    if (project.product?.identifier === ENRICHED_FEED) {
      let enrichedFeedQuestions = [];
      const { BRIEF_QUESTION_TYPES } = PROJECT_CONSTANTS;

      const toggleQuestion = briefQuestions.find(
        (question) =>
          question.questionType === BRIEF_QUESTION_TYPES.TOGGLE_ANSWER,
      );

      if (toggleQuestion) {
        const toggleQuestion = briefQuestions.find(
          (question) =>
            question.questionType === BRIEF_QUESTION_TYPES.TOGGLE_ANSWER,
        );
        const findChosenPlatformId = () => {
          const hasChosenPlatform =
            updatedAnswersByQuestionId[
              toggleQuestion.optionSets[0].compoundKey
            ];
          if (hasChosenPlatform) {
            return updatedAnswersByQuestionId[
              toggleQuestion.optionSets[0].compoundKey
            ].value[0];
          }

          const hasSavedPlatform =
            toggleQuestion.optionSets[0].currentValues?.length;
          if (hasSavedPlatform) {
            return toggleQuestion.optionSets[0].currentValues[0];
          }

          return null;
        };

        const chosenPlatformId = findChosenPlatformId();

        if (chosenPlatformId) {
          enrichedFeedQuestions = briefQuestions
            .filter(
              (briefQuestion) =>
                !briefQuestion.parentQuestionOption ||
                briefQuestion.parentQuestionOption.id === chosenPlatformId,
            )
            .map((filteredBriefQuestion) => {
              const copiedFilteredBriefQuestion = { ...filteredBriefQuestion };
              if (copiedFilteredBriefQuestion.text === 'null') {
                copiedFilteredBriefQuestion.text = '';
              }

              return copiedFilteredBriefQuestion;
            });
        } else {
          enrichedFeedQuestions = briefQuestions.filter(
            (question) =>
              question.questionType === BRIEF_QUESTION_TYPES.TOGGLE_ANSWER,
          );
        }
      } else {
        enrichedFeedQuestions = briefQuestions.filter(
          (question) =>
            question.questionType !== BRIEF_QUESTION_TYPES.TOGGLE_ANSWER &&
            question.parentQuestionOption?.text !== 'A different channel',
        );
      }

      return isBriefOutputAutomationEnabled ? (
        <BriefQuestionV2
          currentProjectId={project.id}
          setIsDeliveryDateUpdated={setIsDeliveryDateUpdated}
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
          briefQuestions={enrichedFeedQuestions}
          updatedAnswersByQuestionId={updatedAnswersByQuestionId}
          getLongAnswerUpdateCallback={getLongAnswerUpdateCallback}
          getCheckboxSelectCallback={getCheckboxSelectCallback}
          getMultipleChoiceOtherCallback={getMultipleChoiceOtherCallback}
          getMultipleChoiceSelectCallback={getMultipleChoiceSelectCallback}
        />
      ) : (
        <BriefQuestions
          project={project}
          briefQuestions={enrichedFeedQuestions}
          updatedAnswersByQuestionId={updatedAnswersByQuestionId}
          getCheckboxSelectCallback={getCheckboxSelectCallback}
          getLongAnswerUpdateCallback={getLongAnswerUpdateCallback}
          getMultipleChoiceOtherCallback={getMultipleChoiceOtherCallback}
          getMultipleChoiceSelectCallback={getMultipleChoiceSelectCallback}
        />
      );
    }

    return isBriefOutputAutomationEnabled ? (
      <BriefQuestionV2
        currentProjectId={project.id}
        setIsDeliveryDateUpdated={setIsDeliveryDateUpdated}
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
        briefQuestions={briefQuestions}
        updatedAnswersByQuestionId={updatedAnswersByQuestionId}
        getLongAnswerUpdateCallback={getLongAnswerUpdateCallback}
        getCheckboxSelectCallback={getCheckboxSelectCallback}
        getMultipleChoiceOtherCallback={getMultipleChoiceOtherCallback}
        getMultipleChoiceSelectCallback={getMultipleChoiceSelectCallback}
      />
    ) : (
      <BriefQuestions
        project={project}
        briefQuestions={briefQuestions}
        updatedAnswersByQuestionId={updatedAnswersByQuestionId}
        getCheckboxSelectCallback={getCheckboxSelectCallback}
        getLongAnswerUpdateCallback={getLongAnswerUpdateCallback}
        getMultipleChoiceOtherCallback={getMultipleChoiceOtherCallback}
        getMultipleChoiceSelectCallback={getMultipleChoiceSelectCallback}
      />
    );
  };

  let topLevelDivClass = 'active-project-brief';

  if (isInProjectCreate) {
    if (isEnterprisePartner) {
      topLevelDivClass = 'brief-section enterprise';
    } else {
      topLevelDivClass = 'brief-section';
    }
  }

  return (
    <div>
      <div id="brief" className={topLevelDivClass}>
        {isInProjectCreate ? (
          isEnterprisePartner ? (
            <>
              <h2 className="display-left-aligned-black enterprise-title">
                {intl.messages['ui.projectCreate.brief.enterprise.title']}
              </h2>
              <p className="body-left-aligned-black enterprise-description">
                {intl.messages['ui.projectCreate.brief.enterprise.description']}
              </p>
            </>
          ) : (
            <h2 className="display-left-aligned-black">
              {intl.messages['ui.projectCreate.brief.title']}
            </h2>
          )
        ) : (
          renderCardRail()
        )}

        <div id="brief-question-list">
          {isLoadingBrief ? (
            <Loading dots />
          ) : (
            <>
              {project.allowsBriefDocs && (
                <>
                  <BriefDocuments
                    mediaUploadOptions={getProjectBriefMediaUploadOptions()}
                    isReadOnly={isReadOnly || !canUpdateBrief}
                    projectId={projectId}
                    files={briefDocuments.files}
                    lastUpdatedDisplayName={
                      briefDocuments.lastUpdatedDisplayName
                    }
                    lastUpdated={briefDocuments.lastUpdated}
                    onUploadComplete={attachBriefDocument}
                    isSelfManagedSocialOrDigitalAd={
                      isSelfManagedSocialOrDigitalAd
                    }
                  />
                  <div
                    className="questions-header"
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                        justifyContent: 'space-between',
                      }}
                    >
                      {isSelfManagedSocialOrDigitalAd &&
                        isBriefOutputAutomationEnabled && (
                          <LabelQuestion
                            label={
                              intl.messages['ui.brief.questionsHeader.title']
                            }
                          />
                        )}
                      {isSelfManagedSocialOrDigitalAd &&
                        !isBriefOutputAutomationEnabled && (
                          <h3
                            className="body-left-aligned-black-emphasized"
                            style={{ marginBottom: '8px' }}
                          >
                            {intl.messages['ui.brief.questionsHeader.title']}
                          </h3>
                        )}
                      {!isSelfManagedSocialOrDigitalAd && (
                        <h4 className={'sub-heading-1-left-aligned-black'}>
                          {intl.messages['ui.brief.questionsHeader.title']}
                        </h4>
                      )}
                      {!isSelfManagedSocialOrDigitalAd && (
                        <p
                          className="small-body-left-aligned-black"
                          style={{ margin: 0 }}
                        >
                          {intl.messages['ui.brief.questionsHeader.required']}
                        </p>
                      )}
                    </div>
                    {isSelfManagedSocialOrDigitalAd &&
                      !isBriefOutputAutomationEnabled && (
                        <p className="body-left-aligned-black">
                          {
                            intl.messages[
                              'ui.brief.questionsHeader.required.v2'
                            ]
                          }
                        </p>
                      )}

                    {isSelfManagedSocialOrDigitalAd &&
                      isBriefOutputAutomationEnabled && (
                        <div style={{ marginBottom: '32px' }}>
                          <HelperTextQuestion
                            helperText={
                              intl.messages[
                                'ui.brief.questionsHeader.required.v2'
                              ]
                            }
                          />
                        </div>
                      )}
                  </div>
                </>
              )}
              {(!isEnterprisePartner || !isInProjectCreate) &&
                renderBriefSection()}
              {!isSelfManagedSocialOrDigitalAd &&
                canReadSow &&
                project.allowsSowDocs &&
                (!isEnterprisePartner || !isInProjectCreate) && (
                  <BriefSowDocuments
                    isReadOnly={isReadOnly || !canUpdateSow}
                    projectId={projectId}
                    files={sowDocuments.files}
                    onUploadComplete={attachBriefSow}
                  />
                )}
            </>
          )}
        </div>
        {!isInProjectCreate && renderSaveChangesBar()}
        {renderPlatformAssetModal()}
        {isInProjectCreate && (
          <LoadingButton
            stretch
            className="next-button"
            buttonColoring="primary"
            buttonSize="large"
            isLoading={isSavingBrief}
            onClick={onProjectCreateBriefSubmit}
          >
            {isEnterprisePartner
              ? intl.messages['button.global.createProject.label']
              : intl.messages['button.global.next.label']}
          </LoadingButton>
        )}
      </div>
      <ProjectBriefInsightsSection
        projectId={projectId}
        isInProjectCreate={isInProjectCreate}
      />
    </div>
  );
};

ProjectBrief.propTypes = {
  attachBriefDocument: func.isRequired,
  attachBriefDocumentFromPlatform: func.isRequired,
  attachBriefSow: func.isRequired,
  attachFileToBriefQuestion: func.isRequired,
  briefQuestions: arrayOf(
    oneOfType([
      shape(multipleChoiceBriefQuestionShape),
      shape(longAnswerBriefQuestionShape),
    ]),
  ).isRequired,
  canReadSow: bool.isRequired,
  canUpdateBrief: bool.isRequired,
  canUpdateSow: bool.isRequired,
  getCheckboxSelectCallback: func.isRequired,
  getLongAnswerUpdateCallback: func.isRequired,
  getMultipleChoiceOtherCallback: func.isRequired,
  getMultipleChoiceSelectCallback: func.isRequired,
  isDataProduct: bool.isRequired,
  isEnterprisePartner: bool.isRequired,
  isInProjectCreate: bool.isRequired,
  isLoadingBrief: bool.isRequired,
  isLoadingProjectStatus: bool.isRequired,
  isReadOnly: bool.isRequired,
  project: shape(currentProjectShape).isRequired,
  // updatedAnswersByQuestionId has keys that can be various IDs and a number or string
  projectId: number.isRequired,

  updatedAnswersByQuestionId: objectOf(any).isRequired,
  onDiscard: func.isRequired,
  onSubmit: func.isRequired,
  analysisPeriodBegin: isoDateString,
  analysisPeriodEnd: isoDateString,
  briefDocuments: instanceOf(BriefDocumentsStructure),
  effectiveDeliveryDate: isoDateString,
  launchDate: isoDateString,

  mediaIsRequired: bool,
  milestone: shape(projectMilestoneStatusShape),
  projectPlatformAdAccounts: arrayOf(shape(projectPlatformAdAccountShape)),
  sowDocuments: instanceOf(SowDocumentsStructure),
  onSavingBrief: func,
};

// These should only be null while loading.
ProjectBrief.defaultProps = {
  projectPlatformAdAccounts: null,
  analysisPeriodBegin: null,
  analysisPeriodEnd: null,
  effectiveDeliveryDate: null,
  onSavingBrief() {},
  launchDate: null,
  briefDocuments: null,
  mediaIsRequired: null,
  milestone: null,
  sowDocuments: null,
};

export default withBrief(ProjectBrief);
