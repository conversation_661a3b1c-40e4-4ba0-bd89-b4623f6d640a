import React, { ReactNode } from 'react';
import { NavLink } from 'react-router-dom';
import { Box, Breadcrumbs, Grid, Link, SxProps } from '@mui/material';
import PageHeaderV2Tabs from './subComponents/PageHeaderV2Tabs';
import EditablePageTitle from '../../muiCustomComponents/EditablePageTitle';
import { MessageFormatElement } from 'react-intl';
import { VidMobTypography } from '../../vidMobComponentWrappers';

export const PAGE_HEADER_V2_ID = 'page-header-v2';
export const PAGE_HEADER_V2_BREADCRUMBS_ID = 'page-header-v2-breadcrumbs';

export interface BreadcrumbType {
  label: string | MessageFormatElement[];
  url: string;
  onClick?: () => void;
}

export interface Tab {
  label: string | MessageFormatElement[];
  identifier: string | number;
  selected: boolean;
}

interface PageHeaderV2Props {
  title: string | MessageFormatElement[];
  subtitle?: string | MessageFormatElement[];
  customTitlePlaceholder?: string | MessageFormatElement[];
  customSubtitleComponent?: React.ReactNode;
  customSubtitlePlaceholder?: string | MessageFormatElement[];
  customTitleMaxWidth?: string | number;
  breadcrumbActionOverride?: (breadcrumb: BreadcrumbType) => void;
  breadcrumbs?: BreadcrumbType[];
  isEditable?: boolean;
  onTitleChange?: (newTitle: string) => void;
  onSubtitleChange?: (newSubtitle: string) => void;
  additionalBoxStyles?: SxProps;
  tabs?: Tab[];
  onTabChange?: (selectedTabIdentifier: string | number) => void;
  children?: ReactNode | any;
  isLoading?: boolean;
  notificationBanner?: ReactNode;
}
const PageHeaderV2: React.FC<PageHeaderV2Props> = ({
  title,
  subtitle,
  customSubtitleComponent,
  customTitlePlaceholder,
  customSubtitlePlaceholder,
  customTitleMaxWidth,
  breadcrumbs = [],
  breadcrumbActionOverride,
  isEditable,
  onTitleChange,
  onSubtitleChange,
  additionalBoxStyles,
  tabs,
  onTabChange,
  children,
  isLoading,
  notificationBanner,
}) => {
  const boxStyling: SxProps = {
    paddingTop: '16px',
    paddingLeft: '24px',
    paddingRight: '24px',
    paddingBottom: tabs ? '0px' : '16px',
    minHeight: 'fit-content',
    ...additionalBoxStyles,
  };

  const titleItemsStyling: SxProps = {
    flexGrow: 1,
    marginRight: '24px',
    padding: '0 !important',
    margin: '0 !important',
  };

  const actionItemsStyling: SxProps = {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    gap: '8px',
  };

  return (
    <Box sx={boxStyling} className="mui-page-header" id={PAGE_HEADER_V2_ID}>
      <Grid container alignItems="stretch">
        <Grid item sx={titleItemsStyling}>
          {breadcrumbs.length > 0 && (
            <Breadcrumbs separator={'/'} id={PAGE_HEADER_V2_BREADCRUMBS_ID}>
              {breadcrumbs.map((breadcrumb, index) =>
                breadcrumbActionOverride ? (
                  <VidMobTypography
                    key={index}
                    color="primary"
                    variant="body2"
                    onClick={() => breadcrumbActionOverride(breadcrumb)}
                    sx={{
                      cursor: 'pointer',
                      '&:hover': { textDecoration: 'underline' },
                    }}
                  >
                    {breadcrumb.label as ReactNode}
                  </VidMobTypography>
                ) : (
                  <Link
                    id={PAGE_HEADER_V2_BREADCRUMBS_ID}
                    key={index}
                    component={NavLink}
                    to={breadcrumb.url}
                    underline="hover"
                    color="primary"
                    variant="body2"
                    onClick={breadcrumb.onClick || undefined}
                  >
                    {breadcrumb.label as ReactNode}
                  </Link>
                ),
              )}
            </Breadcrumbs>
          )}
          <EditablePageTitle
            isEditable={isEditable}
            title={title || ''}
            customSubtitleComponent={customSubtitleComponent}
            customTitlePlaceholder={customTitlePlaceholder}
            customSubtitlePlaceholder={customSubtitlePlaceholder}
            customTitleMaxWidth={customTitleMaxWidth}
            onTitleChange={onTitleChange}
            subtitle={subtitle || ''}
            onSubtitleChange={onSubtitleChange}
            isLoading={isLoading}
          />
        </Grid>
        <Grid item sx={actionItemsStyling}>
          {children}
        </Grid>
      </Grid>
      {notificationBanner && notificationBanner}
      {tabs && tabs.length > 0 && (
        <PageHeaderV2Tabs tabs={tabs} onTabChange={onTabChange} />
      )}
    </Box>
  );
};

export default PageHeaderV2;
