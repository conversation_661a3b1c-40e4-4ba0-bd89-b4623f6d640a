import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import ReactQuill from 'react-quill';
import TurndownService from 'turndown';
import { Toolbar } from './Toolbar';
import { VidMobBox, VidMobTooltip } from '../../vidMobComponentWrappers';

import 'react-quill/dist/quill.snow.css';
import { INSIGHT_MODAL_INTL_KEYS } from '../../creativeAnalytics/insights/components/InsightsV2/components/InsightModal/constants';
import { convertMarkdownToHtmlWithColors } from '../../creativeAnalytics/insights/components/InsightsV2/components/InsightModal/helpers';
import { SxProps } from '@mui/material';

const DisabledOverlay: React.FC = () => (
  <VidMobBox
    sx={{
      position: 'absolute',
      width: '100%',
      height: '100%',
      top: 0,
      left: 0,
      background: 'rgba(255, 255, 255, 0.45)',
      zIndex: 1,
      pointerEvents: 'auto',
      cursor: 'pointer',
    }}
  />
);

const turndownService = new TurndownService();

// Handle underline with color - priority rule
turndownService.addRule('underlineWithColor', {
  filter: function (node) {
    return (
      node.nodeName === 'U' &&
      !!(node as HTMLElement).getAttribute('style')?.includes('color')
    );
  },
  replacement: function (content, node) {
    const style = (node as HTMLElement).getAttribute('style') || '';
    const colorMatch = style.match(
      /color:\s*(rgb\([^)]+\)|#[\w\d]{3,6}|rgba?\([^)]+\))/i,
    );
    const color = colorMatch ? colorMatch[1] : null;

    if (!color) return `~~${content}~~`;
    const sanitizedColor = color.replace(/\s+/g, '');

    return `~~![${content}](${sanitizedColor})~~`;
  },
});

// Handle strong with color
turndownService.addRule('strongWithColor', {
  filter: function (node) {
    return (
      (node.nodeName === 'STRONG' || node.nodeName === 'B') &&
      !!(node as HTMLElement).getAttribute('style')?.includes('color')
    );
  },
  replacement: function (content, node) {
    const style = (node as HTMLElement).getAttribute('style') || '';
    const colorMatch = style.match(
      /color:\s*(rgb\([^)]+\)|#[\w\d]{3,6}|rgba?\([^)]+\))/i,
    );
    const color = colorMatch ? colorMatch[1] : null;

    if (!color) return `**${content}**`;
    const sanitizedColor = color.replace(/\s+/g, '');

    return `**![${content}](${sanitizedColor})**`;
  },
});

// Handle em with color
turndownService.addRule('emWithColor', {
  filter: function (node) {
    return (
      (node.nodeName === 'EM' || node.nodeName === 'I') &&
      !!(node as HTMLElement).getAttribute('style')?.includes('color')
    );
  },
  replacement: function (content, node) {
    const style = (node as HTMLElement).getAttribute('style') || '';
    const colorMatch = style.match(
      /color:\s*(rgb\([^)]+\)|#[\w\d]{3,6}|rgba?\([^)]+\))/i,
    );
    const color = colorMatch ? colorMatch[1] : null;

    if (!color) return `*${content}*`;
    const sanitizedColor = color.replace(/\s+/g, '');

    return `*![${content}](${sanitizedColor})*`;
  },
});

// Regular underline (without color)
turndownService.addRule('underlineToMarkdown', {
  filter: function (node) {
    return (
      node.nodeName === 'U' &&
      !(node as HTMLElement).getAttribute('style')?.includes('color')
    );
  },
  replacement: function (content) {
    return `~~${content}~~`;
  },
});

// Regular color spans
turndownService.addRule('colorToMarkdown', {
  filter: function (node) {
    return (
      node.nodeName === 'SPAN' &&
      !!(node as HTMLElement).getAttribute('style')?.includes('color')
    );
  },
  replacement: function (content, node) {
    const style = (node as HTMLElement).getAttribute('style') || '';
    const colorMatch = style.match(
      /color:\s*(rgb\([^)]+\)|#[\w\d]{3,6}|rgba?\([^)]+\))/i,
    );
    const color = colorMatch ? colorMatch[1] : null;

    if (!color) return content;
    const sanitizedColor = color.replace(/\s+/g, '');

    return `![${content}](${sanitizedColor})`;
  },
});

const editorStyles = {
  borderRadius: '6px',
  '.ql-container': {
    fontSize: '14px',
    fontFamily: 'MaisonNeue, Roboto, sans-serif',
    lineHeight: '20px',
    fontWeight: 400,
  },
  '.ql-container.ql-snow, .ql-toolbar.ql-snow': {
    border: 'none',
  },
  '.ql-color': {
    borderLeft: '1px solid #E0E0E0',
    borderRight: '1px solid #E0E0E0',
  },
  '.ql-editor': {
    minHeight: 155,
    maxHeight: 200,
    overflowY: 'auto',
    fontFamily: 'MaisonNeue, Roboto, sans-serif',
  },
  '.ql-editor strong': {
    fontWeight: 600,
    fontSize: '14px',
    lineHeight: '20px',
  },
  '.ql-snow .ql-color-picker, .ql-snow .ql-icon-picker': {
    width: 'auto',
    margin: '0 10px',
    padding: '0 10px',
  },
  '.ql-snow .ql-picker.ql-expanded .ql-picker-options': {
    padding: '8px',
    width: '239px',
    height: '70px',
    flexWrap: 'wrap',
    display: 'flex',
    borderRadius: '6px',
    gap: '4px',
    boxShadow: '0px 4px 8px 0px rgba(15, 15, 15, 0.16)',
    border: '1px solid #9E9E9E',
  },
  '.ql-snow .ql-color-picker.ql-color .ql-picker-item': {
    width: '20px',
    height: '20px',
    borderRadius: '2px',
  },
  '.ql-tooltip': {
    zIndex: 9999,
    maxWidth: 'calc(100% - 24px)',
    left: '12px !important',
  },
  '.ql-toolbar.ql-snow .ql-picker-label': {
    border: 'none',
  },
  '.ql-snow.ql-toolbar .ql-picker-label:hover': {
    color: '#000',
  },
  '.ql-toolbar.ql-snow .ql-picker-label:hover': {
    borderRadius: '6px',
    background: '#EEE',
    border: 'none',
  },
  '.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label': {
    borderRadius: '6px',
    background: '#DEE6FF',
    border: 'none',
  },
  '.ql-snow .ql-picker .ql-picker-label .ql-stroke': {
    stroke: '#000',
  },
  '.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke': {
    stroke: '#1842EF',
  },
  '.ql-snow .ql-color-picker .ql-picker-label svg': {
    width: '20px',
  },
  '.ql-snow .ql-color-picker .ql-picker-label svg .ql-stroke': {
    stroke: '#212121',
  },
  '.ql-snow .ql-transparent': {
    opacity: 1,
  },
  '.ql-snow.ql-toolbar button:hover .ql-stroke': {
    stroke: '#212121',
  },
  '.ql-snow.ql-toolbar button:hover': {
    background: '#EEE',
    borderRadius: '6px',
  },
  '.ql-snow.ql-toolbar button.ql-active .ql-stroke': {
    stroke: '#1842EF',
  },
};

interface Props {
  value: string;
  onChange: (markdown: string, html: string) => void;
  toolbarId: string;
  disabled?: boolean;
  error?: boolean;
  onBlur?: () => void;
  customToolbarOptions?: any[];
  customWrapperSx?: SxProps;
  autoFocus?: boolean;
  customTurndownService?: TurndownService;
}

export const RichTextEditor: React.FC<Props> = ({
  value,
  onChange,
  toolbarId,
  disabled,
  error = false,
  onBlur,
  customToolbarOptions,
  customWrapperSx = {},
  autoFocus = false,
  customTurndownService,
}) => {
  const intl = useIntl();
  const [isFocused, setFocused] = useState<boolean>(false);
  const [htmlValue, setHtmlValue] = useState<string>();
  const didInit = useRef(false);
  const quillRef = useRef<ReactQuill | null>(null);

  useEffect(() => {
    if (!didInit.current && value) {
      (async () => {
        const parsed = await convertMarkdownToHtmlWithColors(value);
        setHtmlValue(parsed);
        didInit.current = true;
      })();
    }
  }, [value]);

  const onFocus = () => setFocused(true);
  const handleBlur = () => {
    setFocused(false);
    onBlur?.();
  };

  const borderColor = error ? 'error.main' : 'secondary.main';
  const outline = isFocused ? '2px solid' : 'none';
  const outlineColor = isFocused ? 'primary.main' : 'transparent';

  const handleEditorChange = (html: string) => {
    setHtmlValue(html);
    const service = customTurndownService ?? turndownService;
    const markdown = service.turndown(html);
    onChange(markdown, html);
  };

  useEffect(() => {
    if (!autoFocus || disabled) return;
    if (!quillRef.current) return;
    if (!htmlValue && value) return;

    const t = setTimeout(() => {
      const quill = quillRef.current!.getEditor();
      quill.focus();
      quill.setSelection(quill.getLength(), 0);
      setFocused(true);
    }, 0);
    return () => clearTimeout(t);
  }, [autoFocus, disabled, htmlValue, value]);

  const editor = useMemo(
    () => (
      <VidMobBox
        sx={{
          ...editorStyles,
          border: '1px solid',
          borderColor: borderColor,
          outline: outline,
          outlineColor: outlineColor,
          outlineOffset: '0px',
          '&:hover': {
            borderColor: error ? 'error.main' : disabled ? '' : 'primary.main',
          },
          position: 'relative',
          ...customWrapperSx,
        }}
      >
        {disabled && <DisabledOverlay />}
        <Toolbar toolbarId={toolbarId} />
        <ReactQuill
          ref={quillRef}
          theme="snow"
          value={htmlValue}
          onChange={handleEditorChange}
          onFocus={disabled ? undefined : onFocus}
          onBlur={disabled ? undefined : handleBlur}
          readOnly={disabled}
          modules={{
            toolbar: {
              container: customToolbarOptions ?? `#toolbar-${toolbarId}`,
            },
          }}
          formats={[
            'bold',
            'italic',
            'underline',
            'list',
            'bullet',
            'ordered',
            'color',
            'link',
            'header',
          ]}
        />
      </VidMobBox>
    ),
    [
      htmlValue,
      disabled,
      error,
      borderColor,
      onFocus,
      handleBlur,
      handleEditorChange,
      toolbarId,
      autoFocus,
    ],
  );

  return disabled ? (
    <VidMobTooltip
      placement="top"
      PopperProps={{
        sx: {
          '& .MuiTooltip-tooltip': {
            maxWidth: '225px',
          },
        },
      }}
      title={intl.formatMessage({
        id: INSIGHT_MODAL_INTL_KEYS.DISABLED_EDITOR_TOOLTIP,
      })}
    >
      {editor}
    </VidMobTooltip>
  ) : (
    editor
  );
};
