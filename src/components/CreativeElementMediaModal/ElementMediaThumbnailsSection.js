import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';
import { generatePath } from 'react-router';
import classNames from 'classnames';
import { useIntl } from 'react-intl';
import { getElementMediaModalData } from '../../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';
import OverviewThumbnail from './OverviewThumbnail';
import { uniqueId } from '../../utils/uniqueIdGenerator';
import { composeLinkHelperIndividualCreativeView } from '../../creativeAnalytics/helpers/composeLinkHelper.ts';
import { MultiAssetAdSelectionModal } from '../../creativeAnalytics/__pages/IndividualCreativeView/MultiAssetAdSelectionModal/MultiAssetAdSelectionModal';
import { getIndividualCreativeViewLinkProps } from '../../creativeAnalytics/__pages/IndividualCreativeViewV2/individualCreativeViewV2Utils/getIndividualCreativeViewLinkProps';
import analyticsConfigurationSlice from '../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';
import { GLOBALS } from '../../constants';
import './CreativeElementMediaOverview.scss';

const { SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const ElementMediaThumbnailsSection = ({ filters, source, reportName }) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const { pathname, search } = useLocation();
  const [isICVAdSelectionModalOpen, setIsICVAdSelectionModalOpen] =
    useState(false);

  const {
    element,
    topConfidentMedia = [],
    elementMediaIds = [],
    elementMediaLoadingStatus,
  } = useSelector(getElementMediaModalData);

  const platform = filters?.channel?.value;
  const elementDataLoaded = elementMediaLoadingStatus === SUCCESS;

  const nonEmptyTopConfidentMedia =
    topConfidentMedia.length &&
    topConfidentMedia.filter((media) => media.media);

  const elementMediaThumbnailsClassNames = classNames({
    'element-media-overview-thumbnails': true,
    'thumbnails-preloading-animation': !elementDataLoaded,
    'is-hidden': nonEmptyTopConfidentMedia.length === 0,
  });

  const renderOverviewThumbnail = (asset) => {
    const isCreativeOfMultiAssetAds = asset?.media?.isCreativeOfMultiAssetAds;

    const ICVLink = composeLinkHelperIndividualCreativeView({
      platformMediaId: asset?.media?.platformMediaId,
      source,
      platform,
      adAccountId: asset?.adAccountId,
      isCreativeOfMultiAssetAds,
    });

    const state = {
      cameFromVidmob: true,
      prevUrl: `${pathname}${search}`,
      reportName,
    };

    const onCloseMultiAssetAdSelectionModal = () => {
      dispatch(analyticsConfigurationSlice.actions.resetElementMediaModal());
      setIsICVAdSelectionModalOpen(false);
    };

    if (ICVLink) {
      return (
        <>
          <Link
            key={asset?.media.id}
            {...getIndividualCreativeViewLinkProps({
              link: generatePath(ICVLink),
              isCreativeOfMultiAssetAds,
              setIsICVAdSelectionModalOpen,
              state,
            })}
          >
            <OverviewThumbnail media={asset.media} />
          </Link>
          {isICVAdSelectionModalOpen && (
            <MultiAssetAdSelectionModal
              isOpen={isICVAdSelectionModalOpen}
              onClose={onCloseMultiAssetAdSelectionModal}
              platformMediaId={asset?.media?.platformMediaId}
              mediaName={asset?.media?.displayName}
              platform={platform}
              adAccountId={asset?.adAccountId}
              source={source}
              state={state}
            />
          )}
        </>
      );
    }

    return <OverviewThumbnail key={asset?.media.id} media={asset.media} />;
  };

  const renderAdOrCreativeCount = () => {
    if (!elementDataLoaded) {
      return <div className="media-overview-text-loading" />;
    }

    const creativeCount = elementMediaIds.length;
    const adsOrPostsOrCreatives =
      creativeCount > 1
        ? intl.messages['ui.creatives.lowercase']
        : intl.messages['ui.creative.lowercase'];

    return (
      <div className="media-overview-text body-left-aligned-black">
        {intl.formatMessage(
          { id: 'element.media.modal.count' },
          {
            elementName: element?.title,
            count: creativeCount,
            adsOrPostsOrCreatives,
          },
        )}
      </div>
    );
  };

  return (
    <div className="creative-element-media-overview">
      <div className="media-overview-header large-body-left-aligned-black-bold">
        {intl.messages['element.media.modal.where.detected']}
      </div>
      {renderAdOrCreativeCount()}
      <div className={elementMediaThumbnailsClassNames}>
        {!elementDataLoaded &&
          Array(4)
            .fill(null)
            .map(() => <span key={uniqueId()} />)}
        {nonEmptyTopConfidentMedia.length > 0 &&
          elementDataLoaded &&
          nonEmptyTopConfidentMedia.map((asset) =>
            renderOverviewThumbnail(asset),
          )}
      </div>
    </div>
  );
};

export default ElementMediaThumbnailsSection;
