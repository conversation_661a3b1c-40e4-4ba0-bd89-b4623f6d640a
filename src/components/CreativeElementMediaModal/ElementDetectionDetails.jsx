import React from 'react';
import { useIntl } from 'react-intl';
import {
  ELEMENT_MEDIA_MODAL_COPY,
  TAG_TYPES,
} from '../../constants/ci.constants';
import { VidMobBox, VidMobTypography } from '../../vidMobComponentWrappers';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';

const TAG_TYPES_REQUIRING_TITLE_LOOKUP = [
  TAG_TYPES.APERTURE_MESSAGING,
  TAG_TYPES.APERTURE_PRODUCTION_LEVEL,
  TAG_TYPES.APERTURE_SOUND_TYPE,
  TAG_TYPES.APERTURE_STORY_TYPE,
  TAG_TYPES.APERTURE_TONE_EXPRESSION,
  TAG_TYPES.APERTURE_BRANDING_LOGO,
  TAG_TYPES.APERTURE_BRANDING_LOGO_TYPE,
  TAG_TYPES.APERTURE_PRODUCTION_TYPE,
];

const ElementDetectionDetails = ({ tagType, title }) => {
  const intl = useIntl();

  const modalCopyObject = ELEMENT_MEDIA_MODAL_COPY.find((copyObj) =>
    copyObj.tagTypes.includes(tagType),
  );

  const resolvedCopy =
    TAG_TYPES_REQUIRING_TITLE_LOOKUP.includes(tagType) &&
    title &&
    modalCopyObject?.elementTitles?.[title]
      ? modalCopyObject.elementTitles[title]
      : modalCopyObject;

  if (!resolvedCopy) return null;

  const titleStyle = {
    fontSize: '17px',
    fontWeight: 700,
    margin: '20px 0',
  };

  const textStyle = {
    fontSize: '15px',
    fontWeight: 500,
  };

  return (
    <>
      <VidMobTypography sx={titleStyle}>
        {intl.messages['element.media.modal.how.detected']}
      </VidMobTypography>

      <VidMobTypography sx={textStyle}>
        {intl.messages['element.media.modal.type']}
        {intl.messages[resolvedCopy.elementType]}
      </VidMobTypography>

      <VidMobBox mt={'20px'}>
        <VidMobTypography sx={textStyle}>
          {intl.messages[resolvedCopy.description]}
        </VidMobTypography>
      </VidMobBox>

      <VidMobTypography sx={titleStyle}>
        {intl.messages['element.media.modal.how.use']}
      </VidMobTypography>

      <VidMobBox sx={textStyle}>
        <VidMobBox mb={'20px'}>
          {intl.messages['element.media.modal.example.use.cases']}
        </VidMobBox>
        <VidMobBox component="ul" sx={{ pl: 2, m: 0, listStyle: 'none' }}>
          {resolvedCopy?.examples?.map((example) => (
            <VidMobBox
              component="li"
              key={example}
              sx={{
                display: 'flex',
                alignItems: 'flex-start',
                mb: '4px',
              }}
            >
              <FiberManualRecordIcon
                sx={{ fontSize: 8, mt: '8px', mr: '6px' }}
              />
              <VidMobTypography component="span" sx={textStyle}>
                {intl.messages[example]}
              </VidMobTypography>
            </VidMobBox>
          ))}
        </VidMobBox>
      </VidMobBox>
    </>
  );
};

export default ElementDetectionDetails;
