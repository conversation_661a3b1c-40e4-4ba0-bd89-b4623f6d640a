import React from 'react';
import { useSelector } from 'react-redux';
import { getElementMediaModalData } from '../../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';
import ElementDetectionDetails from './ElementDetectionDetails';
import ElementMediaThumbnailsSection from './ElementMediaThumbnailsSection';
import { Box } from '@mui/material';

import './CreativeElementMediaOverview.scss';

const CreativeElementMediaOverview = ({ filters, source, reportName }) => {
  const elementMediaModalData = useSelector(getElementMediaModalData);
  const { element } = elementMediaModalData;

  return (
    <Box>
      <ElementMediaThumbnailsSection
        source={source}
        reportName={reportName}
        filters={filters}
      />
      <ElementDetectionDetails
        tagType={element.tagType}
        title={element.title}
      />
    </Box>
  );
};

export default CreativeElementMediaOverview;
