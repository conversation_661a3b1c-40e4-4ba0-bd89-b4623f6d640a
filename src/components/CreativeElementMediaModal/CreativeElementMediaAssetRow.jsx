import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Link, useLocation } from 'react-router-dom';
import { generatePath } from 'react-router';
import { useDispatch } from 'react-redux';
import { useIntl } from 'react-intl';
import useHover from '../../hooks/useHover';
import MediaPreview from '../MediaPreviewCorner/MediaPreviewCorner';
import Tags from '../../creativeAnalytics/components/media/tags';
import { ScrollingText } from '../../vcl/ui/ScrollingText';
import { FILE_TYPES } from '../../constants/analytics.api.constants';
import {
  ORGANIC_PLATFORMS,
  TAG_CATEGORIES_MAP,
  TAGS_COLORS_MAP,
} from '../../constants/ci.constants';
import formatDuration from '../../utils/formatDuration';
import PLATFORM from '../../constants/platform.constants';
import timeIcon from '../../assets/icons/ic-clock-gray.svg';
import downloadIcon from '../../assets/icons/ic-download-gray.svg';
import Icon from '../Icon';
import ClickableRegion from '../ClickableRegion';
import './CreativeElementMediaAssetRow.scss';
import { mediaShape } from '../../featureServices/Media/Media';
import ShimmerAndFadeLoadingState from '../ShimmerAndFadeLoadingState';
import { EMPTY_ASPECT_RATIO } from '../../utils/getAspectRatio';
import { MultiAssetAdSelectionModal } from '../../creativeAnalytics/__pages/IndividualCreativeView/MultiAssetAdSelectionModal/MultiAssetAdSelectionModal';
import { getIndividualCreativeViewLinkProps } from '../../creativeAnalytics/__pages/IndividualCreativeViewV2/individualCreativeViewV2Utils/getIndividualCreativeViewLinkProps';
import analyticsConfigurationSlice from '../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';

const { platformGrayIconUrls } = PLATFORM;
const { image, video } = FILE_TYPES;

const CreativeElementMediaAssetRow = ({
  asset,
  link,
  platform,
  reportName,
  source,
}) => {
  const { media, tags } = asset;
  const isCreativeOfMultiAssetAds = media?.isCreativeOfMultiAssetAds;
  const intl = useIntl();
  const dispatch = useDispatch();
  const { pathname, search } = useLocation();
  const [hoverRef, isHovered] = useHover();
  const hasThumbnail = media?.thumbnails?.length > 0;

  const [isICVAdSelectionModalOpen, setIsICVAdSelectionModalOpen] =
    useState(false);

  if (typeof media === 'undefined') {
    return null;
  }

  const isSupportedPlatformForIndividualCreativeView =
    !ORGANIC_PLATFORMS.includes(platform?.toUpperCase());

  const name = media.displayName ? media.displayName : media.name;
  const { duration, fileType, format } = media;

  const renderTagsSection = () => {
    const tagKey = Object.keys(tags)[0];

    const tagClipsAsMultipleTags = [];
    if (tags?.[tagKey]?.[0]?.clips) {
      tags[tagKey][0].clips.forEach((clip) => {
        tagClipsAsMultipleTags.push({
          ...tags[tagKey][0],
          clip: {
            startTime: clip.startTime,
            duration: clip.duration,
          },
        });
      });

      const formattedTag = {
        [tagKey]: tagClipsAsMultipleTags,
      };

      return (
        <div className="asset-element-data">
          <Tags
            noName
            colorPalette={TAGS_COLORS_MAP}
            media={media}
            tags={formattedTag}
            clipCategories={TAG_CATEGORIES_MAP}
          />
        </div>
      );
    }

    return null;
  };

  const renderAssetThumbnail = () => (
    <div className="asset-thumbnail-container">
      <div ref={hoverRef} className="asset-thumbnail">
        {hasThumbnail ? (
          <img alt="media thumbnail" src={media.thumbnails[0].url} />
        ) : (
          <ShimmerAndFadeLoadingState height="60px" width="90px" />
        )}
      </div>
      <div className="asset-details">
        <ScrollingText
          baseClass="small-body-left-aligned-black asset-name"
          text={name}
        />
        <div className="asset-metadata">
          <div className="metadata-item">
            <img
              alt={platform}
              className="icon"
              src={platformGrayIconUrls[platform.toUpperCase()]}
            />
            <div className="small-body-left-aligned-gray">
              {format || EMPTY_ASPECT_RATIO}
            </div>
          </div>
          <div className="metadata-item">
            {fileType === video && (
              <>
                <img alt="time icon" className="icon" src={timeIcon} />
                <div className="small-body-left-aligned-gray">
                  {formatDuration(duration, true)}{' '}
                </div>
              </>
            )}
            {fileType === image && (
              <>
                <div className="image-icon" />
                <div className="small-body-left-aligned-gray">
                  {intl.messages['element.media.modal.static']}{' '}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const state = {
    cameFromVidmob: true,
    prevUrl: `${pathname}${search}`,
    reportName: reportName,
  };

  const onCloseMultiAssetAdSelectionModal = () => {
    dispatch(analyticsConfigurationSlice.actions.resetElementMediaModal());
    setIsICVAdSelectionModalOpen(false);
  };

  const renderAssetThumbnailWithIndividualCreativeViewLink = () => {
    if (link && isSupportedPlatformForIndividualCreativeView) {
      return (
        <Link
          key={asset?.media?.id}
          {...getIndividualCreativeViewLinkProps({
            link: generatePath(link),
            isCreativeOfMultiAssetAds,
            setIsICVAdSelectionModalOpen,
            state,
          })}
        >
          {renderAssetThumbnail()}
        </Link>
      );
    }

    return renderAssetThumbnail();
  };

  return (
    <div className="media-asset-row">
      <div className="asset-main">
        <div className="asset-info">
          {renderAssetThumbnailWithIndividualCreativeViewLink()}
        </div>
        <ClickableRegion
          className="download-button standard-icon-size"
          regionLabel={intl.messages['button.global.download.label']}
          href={asset.media.downloadUrl}
        >
          <Icon iconPath={downloadIcon} />
        </ClickableRegion>
      </div>
      {Object.keys(tags || {}).length > 0 && renderTagsSection()}
      {isHovered && hasThumbnail && <MediaPreview media={media} />}
      {isICVAdSelectionModalOpen && (
        <MultiAssetAdSelectionModal
          isOpen={isICVAdSelectionModalOpen}
          onClose={onCloseMultiAssetAdSelectionModal}
          platformMediaId={asset?.media?.platformMediaId}
          mediaName={asset?.media?.displayName}
          platform={platform}
          adAccountId={asset?.adAccountId}
          source={source}
          state={state}
        />
      )}
    </div>
  );
};

CreativeElementMediaAssetRow.propTypes = {
  asset: PropTypes.shape({
    media: PropTypes.shape(mediaShape),
    tags: PropTypes.objectOf(
      PropTypes.arrayOf(
        PropTypes.shape({
          clips: PropTypes.arrayOf(
            PropTypes.shape({
              duration: PropTypes.number,
              startTime: PropTypes.number,
            }),
          ),
        }),
      ),
    ),
  }).isRequired,
  link: PropTypes.string.isRequired,
};

export default CreativeElementMediaAssetRow;
