import React from 'react';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
import { useIntl } from 'react-intl';
import { getElementMediaModalData } from '../../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';
import { uniqueId } from '../../utils/uniqueIdGenerator';
import { TAG_TIME_RANGE_TYPES } from '../../constants/creativeAnalytics.constants';
import CreativeElementMediaAssetRow from './CreativeElementMediaAssetRow';
import { GLOBALS, MEDIA } from '../../constants';
import { composeLinkHelperIndividualCreativeView } from '../../creativeAnalytics/helpers/composeLinkHelper';
import './CreativeElementMediaViewAssets.scss';
import { AdvancedSectionChildrenType } from '../../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersTypes';

const { PERCENTAGE, DURATION } = TAG_TIME_RANGE_TYPES;
const { SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;
const { VIDEO_TYPE } = MEDIA.FILE_TYPES;

const getStringForModal = (intl, element, selectedKpi, statSettings) => {
  const { minimumTagConfidenceLevel, isDefaultKPITimeRangeActive } =
    statSettings;

  if (!selectedKpi) {
    return {
      subheaderDetectedString: '',
      subheaderConfidenceString: '',
      subheaderTimingString: '',
    };
  }

  const { tagTimeRange, tagTimeRangeType } = selectedKpi;
  const { title } = element;

  const subheaderDetectedString = intl.formatMessage(
    { id: 'element.media.modal.view.assets.subheader.detected' },
    { elementTitle: title },
  );
  const subheaderConfidenceString = intl.formatMessage(
    { id: 'element.media.modal.view.assets.subheader.confidence' },
    { confidencePercent: minimumTagConfidenceLevel },
  );

  const assetOrCreativeString = intl.messages['ui.creative.lowercase'];

  let subheaderTimingString;

  if (tagTimeRangeType === PERCENTAGE) {
    if (tagTimeRange === 1) {
      subheaderTimingString = intl.formatMessage(
        { id: 'element.media.modal.view.assets.subheader.not.time.based' },
        { assetOrCreativeString },
      );
    } else {
      subheaderTimingString = intl.formatMessage(
        { id: 'element.media.modal.view.assets.subheader.percent' },
        { percent: tagTimeRange * 100, assetOrCreativeString },
      );
    }
  }

  if (tagTimeRangeType === DURATION) {
    subheaderTimingString = intl.formatMessage(
      { id: 'element.media.modal.view.assets.subheader.duration' },
      { seconds: tagTimeRange.value || tagTimeRange, assetOrCreativeString },
    );
  }

  if (!isDefaultKPITimeRangeActive || !tagTimeRangeType) {
    subheaderTimingString = intl.formatMessage(
      { id: 'element.media.modal.view.assets.subheader.not.time.based' },
      { assetOrCreativeString },
    );
  }

  return {
    subheaderDetectedString,
    subheaderConfidenceString,
    subheaderTimingString,
  };
};

const getAssetOrCreativeString = (mediaCount, intl) => {
  return mediaCount > 1
    ? intl.messages['ui.creatives']
    : intl.messages['ui.creative'];
};

const CreativeElementMediaViewAssets = ({
  filters,
  advancedFilters,
  statSettings,
  source,
  reportName,
}) => {
  const intl = useIntl();
  const elementMediaModalData = useSelector(getElementMediaModalData);
  const { element, elementMedia } = elementMediaModalData;
  const elementDataLoaded =
    elementMediaModalData.elementMediaLoadingStatus === SUCCESS;
  const mediaTypeFilter =
    advancedFilters?.find(
      (filter) =>
        filter.type === AdvancedSectionChildrenType.CREATIVE_MEDIA_TYPE,
    )?.value || [];
  const isStaticLoadingState =
    mediaTypeFilter.length === 1 && mediaTypeFilter.includes(VIDEO_TYPE);
  const mediaCount = elementMediaModalData.elementMedia.length;
  const kpi = filters?.kpi?.value;
  const platform = filters?.channel?.value;

  const {
    subheaderDetectedString,
    subheaderConfidenceString,
    subheaderTimingString,
  } = getStringForModal(intl, element, kpi, statSettings);

  const assetOrCreativeString = getAssetOrCreativeString(mediaCount, intl);

  const preloadingAnimationWrapperClassnames = classNames({
    'preloading-animation-wrapper': true,
    'is-static-loading-state': !isStaticLoadingState,
  });

  return (
    <div className="creative-element-media-view-ads">
      <div className="subheader-string large-body-left-aligned-black">
        {subheaderDetectedString}
        <span className="large-body-left-aligned-black-bold">
          {subheaderConfidenceString}
        </span>
        {subheaderTimingString}
      </div>
      {elementDataLoaded && (
        <div className="large-body-left-aligned-black-bold">
          {mediaCount} {assetOrCreativeString}
        </div>
      )}
      {!elementDataLoaded && (
        <div className={preloadingAnimationWrapperClassnames}>
          {Array(4).fill(
            <div key={uniqueId()} className="preloading-animation">
              <div className="row meta-row">
                {Array(3).fill(<span key={uniqueId()} />)}
              </div>
              <div className="row">
                <span className="thumbnail-preview" />
                <div className="row title-row">
                  <div className="row">
                    <div className="col">
                      <span className="element-title" />
                      <div className="row title-meta">
                        {Array(2).fill(<span key={uniqueId()} />)}
                      </div>
                    </div>
                  </div>
                </div>
                {Array(2).fill(<span key={uniqueId()} />)}
              </div>
              <span />
            </div>,
          )}
        </div>
      )}
      <div className="media-asset-rows">
        {elementDataLoaded &&
          elementMedia.map((asset) => {
            const isCreativeOfMultiAssetAds =
              asset?.media?.isCreativeOfMultiAssetAds;

            const link = composeLinkHelperIndividualCreativeView({
              platformMediaId: asset?.media?.platformMediaId,
              source,
              platform,
              adAccountId: asset?.adAccountId,
              isCreativeOfMultiAssetAds,
            });

            return (
              <CreativeElementMediaAssetRow
                key={asset?.media?.id}
                asset={asset}
                link={link}
                platform={platform}
                reportName={reportName}
                source={source}
              />
            );
          })}
      </div>
    </div>
  );
};

export default CreativeElementMediaViewAssets;
