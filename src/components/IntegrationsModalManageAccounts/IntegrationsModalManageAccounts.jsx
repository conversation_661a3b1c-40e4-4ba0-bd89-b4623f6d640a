import React, { useCallback, useMemo, useContext, useState } from 'react';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { IntegrationModalChangesContext } from '../__providers/IntegrationsModalChangesContext';
import './IntegrationsModalManageAccounts.scss';
import { getShallowCopyWithoutKey } from '../../utils/copyObjectWithKeyChanges';
import { arrayWithFilterAndMap } from '../../utils/copyArrayWithChanges';
import PropTypes from 'prop-types';
import IntegrationsAdAccountList from '../IntegrationsAdAccountList/IntegrationsAdAccountList';
import { FormattedMessage } from 'react-intl';
import withIntegrationData from '../__providers/withIntegrationData';
import IntegrationsModalSigninBlankState from '../IntegrationsModalSigninBlankState';
import IntegrationsModalNoAccountsBlankState from '../IntegrationsModalNoAccountsBlankState';
import IntegrationsModalFailedBlankState, {
  FAILURE_TYPES,
} from '../IntegrationsModalFailedBlankState';
import IntegrationsModalLoadingBlankState from '../IntegrationsModalLoadingBlankState';
import IntegrationsModalNoAccountsDV360BlankState from '../IntegrationsModalNoAccountsDV360BlankState';
import DV360PartnerIdWarningHandler from '../DV360PartnerIdWarningHandler';
import { RectangularSearchField } from '../SearchField';
import platformIdentifier from '../../customProps/platformIdentifier';
import ModalDV360PartnerIds from '../ModalDV360PartnerIds';
import { getOrganizationName } from '../../redux/selectors/partner.selectors';

import PseudoLink from '../PseudoLink';
import { useDispatch } from 'react-redux';
import adAcctSharingSlice from '../../redux/slices/adAcctSharing/adAcctSharing.slice';
import { PLATFORM } from '../../constants';

const { removeAdAccountFromList } = adAcctSharingSlice.actions;

const getSetOfEnabledAdAccounts = (adAccounts) => {
  const enabledAdAccountsArray = arrayWithFilterAndMap(
    adAccounts,
    ({ isEnabled }) => isEnabled,
    ({ id }) => id,
  );
  return new Set(enabledAdAccountsArray);
};

/**
 * Get a copy of the pendingAdAccountChanges object updated to reflect a user selection.
 * @param {Object} currentStateObject The object used to store pending updates to ad account enabled state
 * @param {Set} enabledAdAccountsSet A set of ad account IDs that were originally enabled
 * @param {Number|String} adAccountId The ID of the ad account to update
 * @param {String} platform The name of the platform to be updated
 * @param {Boolean} willBeEnabled Will the ad account be enabled now?
 * @returns {Object} a shallow copy of the state object with changes
 */
const getUpdatedPendingAccountChangesObject = (
  currentStateObject,
  enabledAdAccountsSet,
  adAccountId,
  platform,
  willBeEnabled,
) => {
  const wasAdAccountOriginallyEnabled = enabledAdAccountsSet.has(adAccountId);
  const isReturningToOriginalState =
    willBeEnabled === wasAdAccountOriginallyEnabled;
  const keyOfStateToUpdate = wasAdAccountOriginallyEnabled
    ? 'accountsToDisconnect'
    : 'accountsToConnect';
  const objectToUpdate = currentStateObject[keyOfStateToUpdate];
  const updatedStateSection = isReturningToOriginalState
    ? {
        ...objectToUpdate,
        [platform]: getShallowCopyWithoutKey(
          objectToUpdate[platform],
          adAccountId,
        ),
      }
    : {
        ...objectToUpdate,
        [platform]: { ...objectToUpdate[platform], [adAccountId]: true },
      };
  return {
    ...currentStateObject,
    [keyOfStateToUpdate]: updatedStateSection,
  };
};

const getPendingAdAccountChangesWithSingleValue = (
  platformIdentifier,
  adAccounts,
  universalIsEnabledValue,
  lastPendingChanges,
) => {
  const keyOfStateWithChanges = universalIsEnabledValue
    ? 'accountsToConnect'
    : 'accountsToDisconnect';
  const keyOfStateWithoutChanges = universalIsEnabledValue
    ? 'accountsToDisconnect'
    : 'accountsToConnect';
  const newChanges = adAccounts.reduce(
    (changes, { isEnabled, id, platform }) => {
      if (
        isEnabled !== universalIsEnabledValue &&
        platform === platformIdentifier
      ) {
        return { ...changes, [platform]: { ...changes[platform], [id]: true } };
      }

      return changes;
    },
    {},
  );

  return {
    ...lastPendingChanges,
    [keyOfStateWithoutChanges]: getShallowCopyWithoutKey(
      lastPendingChanges[keyOfStateWithoutChanges],
      platformIdentifier,
    ),
    [keyOfStateWithChanges]: {
      ...lastPendingChanges[keyOfStateWithChanges],
      ...newChanges,
    },
  };
};

export const IntegrationsModalManageAccounts = ({
  adAccounts,
  isAdAccountsFailed,
  isAdAccountsLoaded,
  isPlatformLoggedIn,
  identifier,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const [isDV360ModalVisible, setIsDV360ModalVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const organizationName = useSelector(getOrganizationName);

  const isDV360 = identifier === PLATFORM.DV360.IDENTIFIER;
  const isFacebook = identifier === PLATFORM.FACEBOOK.IDENTIFIER;
  const isAmazon = identifier === PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER;

  if (isAmazon) {
    identifier = PLATFORM.AMAZON_ADVERTISING.IDENTIFIER;
  }

  const enabledAdAccountsSet = useMemo(
    () => getSetOfEnabledAdAccounts(adAccounts),
    [adAccounts],
  );
  /**
   * Two objects, used as a set(not an actual set to ensure we do not mutate state)
   * key = ad account ID, value: true
   */
  const { pendingAdAccountChanges, setPendingAdAccountChanges } = useContext(
    IntegrationModalChangesContext,
  );
  const checkedAdAccounts = useMemo(() => {
    const { accountsToConnect, accountsToDisconnect } = pendingAdAccountChanges;
    let accountsToConnectByPlatform = [];
    Object.keys(accountsToConnect).forEach((platform) => {
      accountsToConnectByPlatform = [
        ...accountsToConnectByPlatform,
        ...Object.keys(accountsToConnect[platform]),
      ];
    });
    const checkedSet = new Set([
      ...enabledAdAccountsSet,
      ...accountsToConnectByPlatform,
    ]);
    Object.keys(accountsToDisconnect).forEach((platform) => {
      Object.keys(accountsToDisconnect[platform]).forEach((id) => {
        checkedSet.delete(id);
      });
    });

    return checkedSet;
  }, [pendingAdAccountChanges, enabledAdAccountsSet]);

  const onChange = useCallback(
    (adAccountId, platform, willBeEnabled) => {
      const newState = getUpdatedPendingAccountChangesObject(
        pendingAdAccountChanges,
        enabledAdAccountsSet,
        adAccountId,
        platform,
        willBeEnabled,
      );
      setPendingAdAccountChanges(newState);
      if (!willBeEnabled) {
        dispatch(removeAdAccountFromList({ adAccountId }));
      }
    },
    [pendingAdAccountChanges, enabledAdAccountsSet],
  );

  const onSelectNone = (platformIdentifier) => {
    setPendingAdAccountChanges(
      getPendingAdAccountChangesWithSingleValue(
        platformIdentifier,
        adAccounts,
        false,
        pendingAdAccountChanges,
      ),
    );
  };

  const onSelectAll = (platformIdentifier) => {
    setPendingAdAccountChanges(
      getPendingAdAccountChangesWithSingleValue(
        platformIdentifier,
        adAccounts,
        true,
        pendingAdAccountChanges,
      ),
    );
  };

  if (!isPlatformLoggedIn) {
    return <IntegrationsModalSigninBlankState />;
  }

  if (isAdAccountsFailed) {
    return (
      <IntegrationsModalFailedBlankState
        failureType={FAILURE_TYPES.FAILURE_MANAGE_ACCOUNTS}
      />
    );
  }

  if (!isAdAccountsLoaded) {
    return <IntegrationsModalLoadingBlankState />;
  }

  if (!adAccounts.length) {
    return isDV360 ? (
      <>
        <IntegrationsModalNoAccountsDV360BlankState
          onClickConnectPartnerIds={() => setIsDV360ModalVisible(true)}
        />
        {isDV360ModalVisible && (
          <ModalDV360PartnerIds
            onClose={() => {
              setIsDV360ModalVisible(false);
            }}
          />
        )}
      </>
    ) : (
      <IntegrationsModalNoAccountsBlankState />
    );
  }

  let IntroText;
  if (isDV360) {
    const DV360LinkText = (
      <FormattedMessage id="integrations.modal.accounts.intro.dv360.linkText">
        {(text) => (
          <PseudoLink
            text={text}
            onClick={() => {
              setIsDV360ModalVisible(true);
            }}
          />
        )}
      </FormattedMessage>
    );
    IntroText = (
      <FormattedMessage
        id="integrations.modal.accounts.intro.dv360"
        values={{ linkText: DV360LinkText }}
      />
    );
  } else {
    IntroText = <FormattedMessage id="integrations.modal.accounts.intro" />;
  }

  // Set up for platforms that authorize under a different platform, that way
  // all platform accounts will surface under the right modal
  let adAccountsPrimaryPlatform = adAccounts;
  let adAccountsFacebookPages = null;
  let adAccountsInstagramPages = null;
  let adAccountsDSP = null;
  let sectionTitle = null;

  // We are able to trust the capitalization here because they are transformed at the API level
  if (isFacebook) {
    sectionTitle = 'platform.metaAdAccounts.label';
    adAccountsPrimaryPlatform = adAccounts.filter(
      (account) => account.platform === identifier,
    );
    adAccountsFacebookPages = adAccounts.filter(
      (account) => account.platform === PLATFORM.FACEBOOK_PAGES.IDENTIFIER,
    );
    adAccountsInstagramPages = adAccounts.filter(
      (account) => account.platform === PLATFORM.INSTAGRAM_PAGES.IDENTIFIER,
    );
  }

  if (isAmazon) {
    sectionTitle = 'platform.sponsoredAds.label';
    adAccountsPrimaryPlatform = adAccounts.filter(
      (account) => account.platform === identifier,
    );
    adAccountsDSP = adAccounts.filter(
      (account) =>
        account.platform === PLATFORM.AMAZON_ADVERTISING_DSP.IDENTIFIER,
    );
  }

  return (
    <div className="integrations-modal-manage-accounts">
      <div className="integrations-modal-accounts-intro">
        <h2 className="large-body-left-aligned-black-bold">
          {intl.formatMessage(
            {
              id: 'integrations.modal.accounts.header',
            },
            { organization: organizationName },
          )}
        </h2>
        <p className="body-left-aligned-gray integrations-modal-manage-accounts-intro">
          {IntroText}
        </p>
        {isDV360 && (
          <DV360PartnerIdWarningHandler
            onManagePartnerIds={() => {
              setIsDV360ModalVisible(true);
            }}
          />
        )}
        <FormattedMessage id="modal.manageAdAccounts.search">
          {(message) => (
            <RectangularSearchField
              isVisible
              searchTerm={searchTerm}
              placeholder={message}
              onInput={setSearchTerm}
            />
          )}
        </FormattedMessage>
      </div>
      <IntegrationsAdAccountList
        adAccountFilter={searchTerm}
        adAccountList={adAccountsPrimaryPlatform}
        checkedAdAccounts={checkedAdAccounts}
        sectionHeaderText={sectionTitle}
        onChange={onChange}
        onSelectAll={() => {
          onSelectAll(identifier);
        }}
        onSelectNone={() => {
          onSelectNone(identifier);
        }}
      />
      {adAccountsFacebookPages && (
        <div className="facebook-pages-section">
          <IntegrationsAdAccountList
            adAccountFilter={searchTerm}
            adAccountList={adAccountsFacebookPages}
            checkedAdAccounts={checkedAdAccounts}
            sectionHeaderText={
              PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[
                PLATFORM.FACEBOOK_PAGES.IDENTIFIER
              ]
            }
            onChange={onChange}
            onSelectAll={() => {
              onSelectAll(PLATFORM.FACEBOOK_PAGES.IDENTIFIER);
            }}
            onSelectNone={() => {
              onSelectNone(PLATFORM.FACEBOOK_PAGES.IDENTIFIER);
            }}
          />
        </div>
      )}
      {adAccountsInstagramPages && (
        <IntegrationsAdAccountList
          adAccountFilter={searchTerm}
          adAccountList={adAccountsInstagramPages}
          checkedAdAccounts={checkedAdAccounts}
          sectionHeaderText={
            PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[
              PLATFORM.INSTAGRAM_PAGES.IDENTIFIER
            ]
          }
          onChange={onChange}
          onSelectAll={() => {
            onSelectAll(PLATFORM.INSTAGRAM_PAGES.IDENTIFIER);
          }}
          onSelectNone={() => {
            onSelectNone(PLATFORM.INSTAGRAM_PAGES.IDENTIFIER);
          }}
        />
      )}
      {adAccountsDSP && (
        <IntegrationsAdAccountList
          adAccountFilter={searchTerm}
          adAccountList={adAccountsDSP}
          checkedAdAccounts={checkedAdAccounts}
          sectionHeaderText="platform.dsp.label"
          onChange={onChange}
          onSelectAll={() => {
            onSelectAll(PLATFORM.AMAZON_ADVERTISING_DSP.IDENTIFIER);
          }}
          onSelectNone={() => {
            onSelectNone(PLATFORM.AMAZON_ADVERTISING_DSP.IDENTIFIER);
          }}
        />
      )}
      {isDV360ModalVisible && (
        <ModalDV360PartnerIds
          onClose={() => {
            setIsDV360ModalVisible(false);
          }}
        />
      )}
    </div>
  );
};

IntegrationsModalManageAccounts.propTypes = {
  identifier: platformIdentifier.isRequired,
  isPlatformLoggedIn: PropTypes.bool.isRequired,
  // adAccounts: requiredBy('isAdAccountsLoaded', PropTypes.arrayOf(PropTypes.shape(adAccountShape))),
  // isAdAccountsFailed: requiredBy('isAdAccountsPlatform', PropTypes.bool),
  // isAdAccountsLoaded: requiredBy('isAdAccountsPlatform', PropTypes.bool)
};

IntegrationsModalManageAccounts.defaultProps = {
  adAccounts: null,
  isAdAccountsFailed: null,
  isAdAccountsLoaded: false,
};

export default withIntegrationData(IntegrationsModalManageAccounts);
