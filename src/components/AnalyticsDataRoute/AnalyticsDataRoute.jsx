import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { loadAnalyticsUserDataIfNeeded } from '../../redux/actions/user.actions.js';
import { Route } from 'react-router-dom';
import Loading from '../../vcl/ui/Loading';
import { GLOBALS } from '../../constants';
import BlankStateError from '../BlankStateError';
import VmHelmet from '../VmHelmet';
import ErrorBoundaryLoopPrevention from '../ErrorBoundaryLoopPrevention';

const { SUCCESS, FAILED } = GLOBALS.REDUX_LOADING_STATUS;

/**
 * @visibleName Analytics Data Route
 */

const AnalyticsDataRoute = ({
  accountsLoaded,
  isLoadingOptional,
  isPlatformAccountsOutOfSync,
  loadAnalyticsUserDataIfNeeded,
  analyticsUserStatus,
  analyticsUserId,
  isAnalyticsUserLoaded,
  hadErrorLoadingAnalyticsUser,
  routes,
  titleMessageId,
  component: Component,
  errorComponent: ErrorComponent,
  loadingComponent: LoadingComponent,
  ...otherProps
}) => {
  // whenever the user status updates, we should check if we need to load it.
  useEffect(() => {
    if (!isAnalyticsUserLoaded) {
      loadAnalyticsUserDataIfNeeded();
    }
  }, [analyticsUserStatus]);

  return (
    <Route
      {...otherProps}
      render={(props) => {
        if (isLoadingOptional || isAnalyticsUserLoaded) {
          return (
            <>
              <VmHelmet titleMessageId={titleMessageId} />
              <ErrorBoundaryLoopPrevention boundaryIdentifier="CI">
                <Component {...props} routes={routes} />
              </ErrorBoundaryLoopPrevention>
            </>
          );
        }

        if (hadErrorLoadingAnalyticsUser) {
          return <ErrorComponent />;
        }

        return <LoadingComponent />;
      }}
    />
  );
};

AnalyticsDataRoute.propTypes = {
  analyticsUserStatus: PropTypes.oneOf(
    Object.values(GLOBALS.REDUX_LOADING_STATUS),
  ).isRequired,
  component: PropTypes.func.isRequired,
  hadErrorLoadingAnalyticsUser: PropTypes.bool.isRequired,
  isAnalyticsUserLoaded: PropTypes.bool.isRequired,
  isPlatformAccountsOutOfSync: PropTypes.bool.isRequired,
  loadAnalyticsUserDataIfNeeded: PropTypes.func.isRequired,
  accountsLoaded: PropTypes.bool,
  analyticsUserId: PropTypes.number,
  errorComponent: PropTypes.func,
  isLoadingOptional: PropTypes.bool,
  loadingComponent: PropTypes.func,
  /**
   * routes to be rendered by the component
   */
  routes: PropTypes.arrayOf(PropTypes.object),
  titleMessageId: PropTypes.string,
};

AnalyticsDataRoute.defaultProps = {
  accountsLoaded: false,
  isLoadingOptional: false,
  loadingComponent: Loading,
  errorComponent: BlankStateError,
  analyticsUserId: null,
  routes: [],
  titleMessageId: null,
};

const mapStateToProps = ({ user, platformAccounts }) => {
  const analyticsUserStatus = user.analyticsUser.status;
  return {
    isAnalyticsUserLoaded: analyticsUserStatus === SUCCESS,
    hadErrorLoadingAnalyticsUser: analyticsUserStatus === FAILED,
    analyticsUserStatus,
    isPlatformAccountsOutOfSync: platformAccounts.isOutOfSyncWithAdAccounts,
    accountsLoaded: platformAccounts.accountsLoaded,
  };
};

const mapDispatchToProps = {
  loadAnalyticsUserDataIfNeeded,
};

export default connect(mapStateToProps, mapDispatchToProps)(AnalyticsDataRoute);
