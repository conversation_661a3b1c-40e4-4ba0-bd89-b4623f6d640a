/* eslint react/prop-types: 0 */
import React, { useEffect, useState } from 'react';
import { PLATFORM, GL<PERSON>BALS } from '../../constants';
import { connect, useSelector } from 'react-redux';
import PropTypes, { object } from 'prop-types';
const { FAILED, SUCCESS, NOT_LOADED } = GLOBALS.REDUX_LOADING_STATUS;
import {
  getPlatformScopeStatusByPlatform,
  areDV360ScopesMissing,
} from '../../redux/selectors/platformScopes.selectors';
import { loadSingleUnloadedPlatformScope } from '../../redux/actions/platformScopes.actions';
import { IntlShape } from '../../utils/intlShape';
import { injectIntl } from 'react-intl';
import { getGrantedAccounts } from '../../redux/selectors/adAcctSharing/adAcctSharing.selectors';
import PlatformOauthService from '../../apiServices/PlatformOauthService';
import {
  getLogoByPlatform,
  getLogoByPlatformIdentifier,
} from '../../utils/feConstantsUtils';

const {
  PLUGIN_ACCOUNT_IDENTIFIERS,
  PLATFORM_IDENTIFIERS_TO_LOCALES,
  platformColorIconUrls,
  FACEBOOK,
  SUPPORTED_PLATFORMS_PAGES_AND_ORGANIC,
} = PLATFORM;

const META_IDENTIFIER = 'META';

const getIsFacebookMissingPagePermissions = (facebookAccount) => {
  // We do not consider the facebook auth to be missing permissions if it is not logged in, it's just missing login.
  // It really is always going to have a facebook status, but this null check is here out of an abundance of caution.
  const { facebookStatus, isEnabled } = facebookAccount;
  if (!isEnabled || !facebookStatus) {
    return false;
  }

  // DRAGON: There are two different objects that might appear in facebook status, so we have to check both.
  // This should be fixed because it's quite tricky to figure out - we may run into bugs related to this which would be hard to fix.
  // TODO: https://vidmob.atlassian.net/browse/AGL-4211

  // If the user was already logged into facebook on page load, they have "scopes" or else they have "grantedScopes" from recent oauth
  // empty array should not come up, but is for extra safety.
  const scopes = facebookStatus.scopes || facebookStatus.grantedScopes || [];

  if (
    FACEBOOK.LEGACY_MANAGE_PAGES_SCOPE_LIST.every((managePagesScope) =>
      scopes.includes(managePagesScope),
    )
  ) {
    return false;
  }

  return !FACEBOOK.MANAGE_PAGES_SCOPES.every((managePagesScope) =>
    scopes.includes(managePagesScope),
  );
};

const addOtherPlatformsAccountsToCurrentModal = (platformsToAggregate) => {
  const aggregated = {
    isAdAccountsUnloaded: null,
    isAdAccountsFailed: null,
    isAdAccountsLoaded: null,
    adAccounts: null,
  };

  // if anything is loading still, wait to determine if there is fail.
  aggregated.isAdAccountsUnloaded = platformsToAggregate.reduce(
    (previousValue, currentValue) =>
      previousValue || currentValue.status === NOT_LOADED,
    false,
  );

  aggregated.isAdAccountsFailed = platformsToAggregate.reduce(
    (previousValue, currentValue) =>
      previousValue || currentValue.status === FAILED,
    false,
  );

  // if all is happy, we are loaded
  aggregated.isAdAccountsLoaded = platformsToAggregate.reduce(
    (previousValue, currentValue) =>
      previousValue && currentValue.status === SUCCESS,
    true,
  );

  aggregated.adAccounts = platformsToAggregate.reduce(
    (previousValue, currentValue) => [...previousValue, ...currentValue.data],
    [],
  );

  return aggregated;
};

const withIntegrationData = (Component) => {
  const IntegrationsWrapper = ({
    intl,
    isScopesDataUnloaded,
    loadSingleUnloadedPlatformScope,
    identifier,
    isPlatformLoggedIn,
    ...passedProps
  }) => {
    const iconUrls =
      identifier === PLATFORM.FACEBOOK.IDENTIFIER
        ? [
            platformColorIconUrls[PLATFORM.FACEBOOK.IDENTIFIER],
            platformColorIconUrls[PLATFORM.INSTAGRAM_PAGES.IDENTIFIER],
          ]
        : [platformColorIconUrls[identifier]];
    const platformLogos =
      identifier === PLATFORM.FACEBOOK.IDENTIFIER
        ? [getLogoByPlatform(META_IDENTIFIER)]
        : [getLogoByPlatformIdentifier(identifier)];

    const platformTitle =
      identifier === PLATFORM.FACEBOOK.IDENTIFIER
        ? intl.messages['platform.facebookAndInstagram.label']
        : intl.messages[PLATFORM_IDENTIFIERS_TO_LOCALES[identifier]];
    const grantedAccounts = useSelector((state) =>
      getGrantedAccounts(state, identifier),
    );

    const [platformLoggedIn, setPlatformLoggedIn] = useState(false);
    const user = useSelector((state) => state.user);

    useEffect(() => {
      if (isScopesDataUnloaded) {
        loadSingleUnloadedPlatformScope(identifier);
      }

      const isPinterestPlatform = identifier === PLATFORM.PINTEREST.IDENTIFIER;
      const isRedditPlatform = identifier === PLATFORM.REDDIT.IDENTIFIER;
      if (isPinterestPlatform || isRedditPlatform) {
        const fetchUserToken = async () => {
          try {
            const response =
              await PlatformOauthService.getUserToken(identifier);
            const isTokenNotRevoked = response.result.revoke === 0;
            setPlatformLoggedIn(isTokenNotRevoked);
          } catch {
            setPlatformLoggedIn(false);
          }
        };

        fetchUserToken();
      } else {
        setPlatformLoggedIn(isPlatformLoggedIn);
      }
    }, [isScopesDataUnloaded, platformLoggedIn, user]);

    return (
      <Component
        // passed props are both from parent and from redux connection
        {...passedProps}
        isPlatformLoggedIn={platformLoggedIn}
        identifier={identifier}
        iconUrls={iconUrls}
        platformLogos={platformLogos}
        platformTitle={platformTitle}
        grantedAccounts={grantedAccounts}
      />
    );
  };

  // ILLUSTRATION: Complex prop type checking with airbnb - marking props as required based on other props
  IntegrationsWrapper.propTypes = {
    /**
     * array of ad accounts granted access
     */
    grantedAccounts: PropTypes.arrayOf(object).isRequired,
    /**
     * The platform's identifier - passed in directly
     */
    identifier: PropTypes.oneOf(PLUGIN_ACCOUNT_IDENTIFIERS).isRequired,
    intl: IntlShape.isRequired,
    /**
     * Is this a platform that may have ad accounts? Determined by redux
     */
    isAdAccountsPlatform: PropTypes.bool.isRequired,
    /**
     * Has data related to oauth login loaded successfully?
     */
    // isPlatformLoggedInLoaded: mutuallyExclusiveTrueProps('isPlatformLoggedInFailed').isRequired,
    /**
     * Has data related to oauth login failed to load
     */
    // isPlatformLoggedInLoadingFailed: mutuallyExclusiveTrueProps('isPlatformLoggedInLoaded').isRequired,
    /**
     * Is scopes data present but not loaded? This will also be false if scopes data does not apply.
     */
    isScopesDataUnloaded: PropTypes.bool.isRequired,
    /**
     * The connected redux action by the same name - loads a platform scope that is not loaded
     */
    loadSingleUnloadedPlatformScope: PropTypes.func.isRequired,
    /**
     * All ad accounts associated with this platform
     */
    // adAccounts: requiredBy('isAdAccountsLoaded', PropTypes.arrayOf(PropTypes.shape(adAccountShape))),
    /**
     * Are there ad accounts connected to this platform that have been enabled, regardless of processing state
     */
    // hasConnectedAdAccounts: requiredBy('isAdAccountsLoaded', PropTypes.bool),
    /**
     * Has loading ad accounts for this platform failed
     */
    // isAdAccountsFailed: requiredBy('isAdAccountsPlatform', PropTypes.bool),
    /**
     * Has loading ad accounts for this platform completed successfully
     */
    // isAdAccountsLoaded: requiredBy('isAdAccountsPlatform', PropTypes.bool),
    /**
     * Is the loading of ad accounts not even started?
     */
    // isAdAccountsUnloaded: requiredBy('isAdAccountsPlatform', PropTypes.bool),
    /**
     * Is the platform logged in via oauth
     */
    // isPlatformLoggedIn: requiredBy('isPlatformLoggedInLoaded', PropTypes.bool),
    /**
     * does the current platform support pages / organic data
     */
    // isPlatformSupportingPages: requiredBy('isPlatformSupportingPages', PropTypes.bool)
  };

  IntegrationsWrapper.defaultProps = {
    isPlatformLoggedIn: null,
    adAccounts: null,
    hasConnectedAdAccounts: null,
    isAdAccountsFailed: null,
    isAdAccountsUnloaded: null,
    isAdAccountsLoaded: null,
    isPlatformSupportingPages: null,
  };

  const mapStateToProps = (state, { identifier }) => {
    const { user, adAccounts: adAccountsByPlatform } = state;
    const thisPlatformAccount = user.currentUser.platformAccounts.find(
      ({ platform }) => platform.IDENTIFIER === identifier,
    );
    const { isEnabled: isPlatformLoggedIn } = thisPlatformAccount;
    let thisPlatformAdAccounts = adAccountsByPlatform[identifier];

    if (identifier === PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER) {
      thisPlatformAdAccounts =
        adAccountsByPlatform[PLATFORM.AMAZON_ADVERTISING.IDENTIFIER];
    }

    const isAdAccountsPlatform = Boolean(thisPlatformAdAccounts);

    const isScopesDataUnloaded =
      getPlatformScopeStatusByPlatform(state)[identifier] === NOT_LOADED;

    let isMissingPlatformScopes = null;
    if (identifier === PLATFORM.DV360.IDENTIFIER) {
      const isDV360LoggedIn = thisPlatformAccount.isEnabled;
      const areDV360ScopesLoaded =
        getPlatformScopeStatusByPlatform(state)[identifier] === SUCCESS;
      const DV360NeedsReAuth =
        isDV360LoggedIn && areDV360ScopesLoaded && areDV360ScopesMissing(state);
      isMissingPlatformScopes = DV360NeedsReAuth;
    }

    // ad account related variables will be null for non ad account platforms.
    let isAdAccountsFailed = null;
    let isAdAccountsLoaded = null;
    let adAccounts = null;
    let isAdAccountsUnloaded = null;
    let hasConnectedAdAccounts = null;
    let isEveryConnectedAccountProcessed = null;
    let numberOfAdAccounts = null;
    const isPlatformSupportingPages =
      SUPPORTED_PLATFORMS_PAGES_AND_ORGANIC.includes(identifier);
    const isPlatformAmazon =
      identifier === PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER;
    const isPlatformFacebook = identifier === PLATFORM.FACEBOOK.IDENTIFIER;
    if (isAdAccountsPlatform) {
      isAdAccountsFailed = thisPlatformAdAccounts.status === FAILED;
      isAdAccountsLoaded = thisPlatformAdAccounts.status === SUCCESS;
      isAdAccountsUnloaded = thisPlatformAdAccounts.status === NOT_LOADED;
      adAccounts = thisPlatformAdAccounts.data;

      // This is for platforms that authorize other platforms under the same modal
      if (isPlatformAmazon || isPlatformFacebook) {
        let aggregatedAdAccountsAndStatus;

        if (isPlatformAmazon) {
          aggregatedAdAccountsAndStatus =
            addOtherPlatformsAccountsToCurrentModal([
              thisPlatformAdAccounts,
              adAccountsByPlatform[PLATFORM.AMAZON_ADVERTISING_DSP.IDENTIFIER],
            ]);
        }

        if (identifier === PLATFORM.FACEBOOK.IDENTIFIER) {
          aggregatedAdAccountsAndStatus =
            addOtherPlatformsAccountsToCurrentModal([
              thisPlatformAdAccounts,
              adAccountsByPlatform[PLATFORM.FACEBOOK_PAGES.IDENTIFIER],
              adAccountsByPlatform[PLATFORM.INSTAGRAM_PAGES.IDENTIFIER],
            ]);
        }

        isAdAccountsUnloaded =
          aggregatedAdAccountsAndStatus.isAdAccountsUnloaded;
        isAdAccountsFailed =
          !isAdAccountsUnloaded &&
          aggregatedAdAccountsAndStatus.isAdAccountsFailed;
        isAdAccountsLoaded = aggregatedAdAccountsAndStatus.isAdAccountsLoaded;
        adAccounts = aggregatedAdAccountsAndStatus.adAccounts;
      }

      numberOfAdAccounts = adAccounts.length;
      const connectedAdAccounts = adAccounts.filter(
        ({ isEnabled }) => isEnabled,
      );
      hasConnectedAdAccounts = connectedAdAccounts.length > 0;
      isEveryConnectedAccountProcessed = connectedAdAccounts.every(
        ({ isProcessingCompleted }) => isProcessingCompleted,
      );
      // This will be null if the platform is not facebook.
    }

    const isFacebookAndMissingPagePermissions =
      identifier === PLATFORM.FACEBOOK.IDENTIFIER
        ? getIsFacebookMissingPagePermissions(thisPlatformAccount)
        : null;

    return {
      isAdAccountsPlatform,
      // TODO: Pull this out when platform login is asynchronous
      isPlatformLoggedInLoaded: true,
      isPlatformLoggedInLoadingFailed: false,
      isPlatformLoggedIn,
      adAccounts,
      hasConnectedAdAccounts,
      isAdAccountsUnloaded,
      isAdAccountsFailed,
      isScopesDataUnloaded,
      isMissingPlatformScopes,
      isAdAccountsLoaded,
      isEveryConnectedAccountProcessed,
      isFacebookMissingPagePermissions: isFacebookAndMissingPagePermissions,
      numberOfAdAccounts,
      isPlatformSupportingPages,
    };
  };

  return connect(mapStateToProps, { loadSingleUnloadedPlatformScope })(
    injectIntl(IntegrationsWrapper),
  );
};

export default withIntegrationData;
