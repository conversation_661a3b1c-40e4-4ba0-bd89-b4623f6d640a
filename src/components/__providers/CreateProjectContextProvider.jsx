import React, { Children, Component, createContext } from 'react';
import moment from 'moment';
import store from '../../redux/store';
import { IntercomAPI as intercomAPI } from 'react-intercom';
import { arrayOf, bool, node, number, object, shape, string } from 'prop-types';
import routeParams from '../../routing/siteMapRouteParams';
import ProjectService from './../../apiServices/ProjectService';
import ProjectProjectAddonService from './../../apiServices/ProjectProjectAddonService';

import OutputGroupStructure from '../../creativeProduction/featureServices/Output/OutputGroup';
import CategoryGroupStructure from './../../featureServices/Formats/CategoryGroups';
import ProjectPrepFormatShape from './../../featureServices/Formats/ProjectPrepFormat';
import outputObjectForProject from '../../creativeProduction/featureServices/Output/OutputObjectForProject';
import Format from './../../featureServices/Formats/Format';
import {
  daysBetween,
  daysUntil,
  formatDateForApi,
  getDateAfterGivenBusinessDays,
  today,
} from './../../utils/dateUtils';

import { PROJECT, MEDIA } from '../../constants';
import insightsSlice from '../../redux/slices/creativeAnalytics/insights.slice';

const {
  PROJECT_DEFAULTS,
  PROJECT_CREATE_STEP,
  OUTPUT_TYPES,
  PROJECT_STATUS,
  RUSH_DELIVERY_TURNAROUND_DAYS,
  RUSH_DELIVERY_ADDON_IDENTIFIER,
} = PROJECT;
const excludedProjectAddons = [
  PROJECT.PROJECT_ADD_ON_IDENTIFIERS.INTELLIGENT_TESTING_ADDON,
  PROJECT.PROJECT_ADD_ON_IDENTIFIERS.RUSH_DELIVERY,
];
const DATA_ROUTE_PARAM = routeParams.sections.projectCreate.productTypes.data;
const HERO_FORMAT_DEFAULT_ID = 1;

const createProjectDataDefaults = {
  analysisPeriodEnd: null,
  analysisPeriodStart: null,
  categoryList: [],
  currentPartnerId: null,
  currentUserId: null,
  currentVariationNote: null,
  daysToDeliver: 0,
  dueDate: null,
  estimatedCost: null,
  // earliestDueDate is added to prevent the costly calculations for X future business days every time the date picker renders vs on section load
  earliestDueDate: null,
  formatSelectModalOpen: false,
  hasDataProductAccess: false,
  heroUploadInProgress: false,
  inEditMode: false,
  isDataProduct: false,
  isEnterprisePartner: false,
  isHero: true,
  isProductTypeSelected: false,
  isReady: false,
  isRush: false,
  isRushAllowed: true,
  isSavingProject: false,
  launchDate: null,
  maxOutputGroups: null,
  // very hard serious time until the project is done.
  // If set then rush delivery is impossible.
  minimumTurnaroundDays: null,
  // time until it is considered rush delivery
  minimumTurnaroundTime: PROJECT_DEFAULTS.RUSH_DURATION,
  outputGroupArray: [],
  outputGroupLength: '1',
  outputGroupType: null,
  outputGroupsData: [],
  outputGroupsKey: 1,
  partnerCode: '',
  partners: [],
  product: {
    formats: [],
    identifier: null,
    name: null,
    productId: null,
  },
  productId: null,
  products: [],
  projectAddons: [],
  projectId: null,
  projectName: '',
  projectDescription: '',
  projectOwnerId: null,
  projectType: null,
  savingBriefChanges: false,
  turnaroundDate: null,
  variationList: [],
  variationTypes: [],
};

export const CreateProjectContext = createContext(createProjectDataDefaults);

class CreateProjectContextProvider extends Component {
  constructor(props) {
    super(props);
    // we count the number of times we reset the component, and compare when doing state updates.
    this.resetCounter = 0;
    // this will be passed in a prop about whether we have created a project
    const {
      currentPartnerId,
      currentUserId,
      products,
      variationTypes,
      projectId,
      canSkipRedirect,
      initialProductType,
      isEnterprisePartner,
    } = props;

    const updatedDefaults = { ...createProjectDataDefaults };

    // check if they are an enterprise partner so data products are not shown
    updatedDefaults.hasDataProductAccess =
      products.some(
        (product) =>
          product.formats.length > 0 &&
          product.formats[0].category.identifier ===
            PROJECT.PRODUCT_TYPE.DATA_REPORT,
      ) && !isEnterprisePartner;

    updatedDefaults.currentPartnerId = currentPartnerId;
    updatedDefaults.currentUserId = currentUserId;
    updatedDefaults.products = products;
    updatedDefaults.variationTypes = variationTypes;
    updatedDefaults.isEnterprisePartner = isEnterprisePartner;

    if (canSkipRedirect) {
      updatedDefaults.projectId = projectId;
      updatedDefaults.isDataProduct = initialProductType === DATA_ROUTE_PARAM;
    }

    this.state = updatedDefaults;
  }

  reset = () =>
    new Promise((resolve) => {
      // DRAGON: We could do this better - it is a copy/paste from constructor.
      const { props } = this;
      const {
        currentPartnerId,
        currentUserId,
        products,
        variationTypes,
        projectId,
        canSkipRedirect,
        initialProductType,
        isEnterprisePartner,
      } = props;

      const updatedDefaults = { ...createProjectDataDefaults };

      // check if they are an enterprise partner so data products are not shown
      updatedDefaults.hasDataProductAccess =
        products.some(
          (product) =>
            product.formats.length > 0 &&
            product.formats[0].category.identifier ===
              PROJECT.PRODUCT_TYPE.DATA_REPORT,
        ) && !isEnterprisePartner;

      updatedDefaults.currentPartnerId = currentPartnerId;
      updatedDefaults.currentUserId = currentUserId;
      updatedDefaults.products = products;
      updatedDefaults.variationTypes = variationTypes;
      updatedDefaults.isEnterprisePartner = isEnterprisePartner;

      if (canSkipRedirect) {
        updatedDefaults.projectId = projectId;
        updatedDefaults.isDataProduct = initialProductType === DATA_ROUTE_PARAM;
      }

      this.setState({ ...updatedDefaults }, () => {
        this.resetCounter++;
        resolve();
      });
    });

  //
  // General Project functions
  //

  getStepData = (stepLabel) => {
    const {
      daysToDeliver,
      isDataProduct,
      launchDate,
      outputGroupType,
      outputGroupsData,
      partnerCode,
      productId,
      projectAddons,
      projectName,
      projectDescription,
      projectOwnerId,
      turnaroundDate,
    } = this.state;

    const { isInTwitterProjectCreateFlow } = this.props;

    let stepData;

    switch (stepLabel) {
      case PROJECT_CREATE_STEP.PRODUCT_TYPE_SELECT:
        stepData = { isDataProduct, isInTwitterProjectCreateFlow };
        break;
      case PROJECT_CREATE_STEP.BASICS:
        stepData = {
          projectName,
          projectDescription,
          productId,
          partnerCode,
          projectOwnerId,
        };
        break;
      case PROJECT_CREATE_STEP.OUTPUTS:
        stepData = { outputGroupType, outputGroupsData };
        break;
      case PROJECT_CREATE_STEP.ADD_ONS:
        stepData = { projectAddons };
        break;
      case PROJECT_CREATE_STEP.KEY_DATES:
        stepData = { daysToDeliver, launchDate, turnaroundDate };
        break;
      case PROJECT_CREATE_STEP.AD_ACCOUNT:
        stepData = {};
        break;
      case PROJECT_CREATE_STEP.BRIEF:
        stepData = {};
        break;
      case PROJECT_CREATE_STEP.INVITE_TEAM:
        stepData = {};
        break;
      default:
        break;
    }

    return stepData;
  };

  isStepComplete = (stepLabel) => {
    const {
      heroUploadInProgress,
      isDataProduct,
      isProductTypeSelected,
      outputGroupArray,
      partnerCode,
      partners,
      productId,
      projectAddons,
      projectName,
      projectType,
    } = this.state;

    // calculate required outputs for the project type
    const requiredOutputListLength =
      projectType === PROJECT.PROJECT_TYPE.NEW_HEROES ? 1 : 2;
    const product = this.getProduct();

    const hasVariations =
      outputGroupArray[0] &&
      outputGroupArray[0].outputList.filter(
        (output) => output.format && output.format.id !== undefined,
      ).length > 0;

    let stepCompleted;

    switch (stepLabel) {
      case PROJECT_CREATE_STEP.PRODUCT_TYPE_SELECT:
        stepCompleted = isProductTypeSelected;
        break;
      case PROJECT_CREATE_STEP.BASICS:
        stepCompleted =
          projectName.length > 0 &&
          Boolean(productId) &&
          (partners.length === 0 || Boolean(partnerCode));
        break;
      case PROJECT_CREATE_STEP.OUTPUTS:
        stepCompleted =
          !heroUploadInProgress &&
          outputGroupArray.length > 0 &&
          outputGroupArray[0].outputList.length >= requiredOutputListLength &&
          hasVariations;
        break;
      case PROJECT_CREATE_STEP.ADD_ONS:
        stepCompleted =
          projectAddons.every(
            (projectAddon) =>
              excludedProjectAddons.includes(projectAddon.identifier) ||
              projectAddon.selected !== undefined,
          ) || product.optionalProjectAddons.length === 0;
        break;
      case PROJECT_CREATE_STEP.KEY_DATES:
        stepCompleted = isDataProduct ? true : this.isKeyDatesCompleted();
        break;
      case PROJECT_CREATE_STEP.AD_ACCOUNT:
        stepCompleted = true;
        break;
      case PROJECT_CREATE_STEP.BRIEF:
        stepCompleted = true;
        break;
      case PROJECT_CREATE_STEP.INVITE_TEAM:
        stepCompleted = true;
        break;
      default:
        break;
    }

    return stepCompleted;
  };

  getDefaultProducts = (isDataProduct, products, isEnterprisePartner) => {
    let filteredProducts = [...products];

    if (isDataProduct) {
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.formats.length > 0 &&
          product.formats[0].category.identifier ===
            PROJECT.PRODUCT_TYPE.DATA_REPORT,
      );
    }

    if (!isDataProduct) {
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.formats.length === 0 ||
          (product.formats.length > 0 &&
            product.formats[0].category.identifier !==
              PROJECT.PRODUCT_TYPE.DATA_REPORT),
      );
    }

    if (isEnterprisePartner) {
      filteredProducts = filteredProducts.filter(
        (product) => !product.requiresSupport,
      );
    }

    return filteredProducts;
  };

  updateProductTypeInContext = (isDataProduct) => {
    const { products, isEnterprisePartner } = this.state;
    const filteredProducts = this.getDefaultProducts(
      isDataProduct,
      products,
      isEnterprisePartner,
    );
    return new Promise((resolve) => {
      this.setState({ isDataProduct, products: filteredProducts }, resolve);
    });
  };

  getProduct = () => {
    const { products } = this.state;
    const stepData = this.getStepData(PROJECT_CREATE_STEP.BASICS);

    if (stepData && stepData.productId) {
      return products.find((product) => product.id === stepData.productId);
    }

    return null;
  };

  saveProject = () => {
    const { isDataProduct } = this.state;
    this.setState({ isSavingProject: true });
    return isDataProduct ? this.saveDataProject() : this.saveCreativeProject();
  };

  saveDataProject = () => {
    const product = this.getProduct();
    const format = product.formats[0];

    const formatId = format.id;

    const {
      analysisPeriodEnd,
      analysisPeriodStart,
      currentPartnerId,
      currentUserId,
      daysToDeliver,
      dueDate,
      productId,
      projectName,
      projectOwnerId,
    } = this.state;

    const ownerId = projectOwnerId ? projectOwnerId : currentUserId;

    // DRAGON
    // This isn't how we should eventually be structuring data reports to be created but
    // right now this allows us to send them out to bid properly
    const outputVideoData = [
      {
        formatId,
        name: format.name,
        videoNumber: 1,
      },
    ];

    const payload = {
      totalOutputVideos: 1,
      outputVideoData,
      daysToDeliver,
      deliveryDate: formatDateForApi(dueDate),
      reportAnalysisPeriodBegin: formatDateForApi(analysisPeriodStart),
      reportAnalysisPeriodEnd: formatDateForApi(analysisPeriodEnd),
      ownerId,
      productId,
      name: projectName,
      status: PROJECT_STATUS.IN_BUILD, // status is always IN_BUILD on new project creation
    };

    return ProjectService.createProject(payload, currentPartnerId).then(
      (res) => {
        this.setState({ isSavingProject: false });
        return res;
      },
    );
  };

  saveCreativeProject = () => {
    const {
      currentUserId,
      currentPartnerId,
      projectOwnerId,
      daysToDeliver,
      launchDate,
      outputGroupType,
      outputGroupsData,
      partnerCode,
      productId,
      projectName,
      projectDescription,
      isRush,
      projectAddons,
    } = this.state;

    const {
      projectRushAddon,
      isEnterprisePartner,
      isInTwitterProjectCreateFlow,
    } = this.props;

    const finalProjectAddons = projectAddons ? [...projectAddons] : [];

    if (isRush) {
      finalProjectAddons.push({ ...projectRushAddon, selected: true });
    }

    const ownerId = projectOwnerId ? projectOwnerId : currentUserId;

    let payload = {
      ownerId,
      partnerCode,
      productId,
      name: projectName,
      description: projectDescription,
      status: PROJECT_STATUS.IN_BUILD, // status is always IN_BUILD on new project creation
    };

    if (!isEnterprisePartner && !isInTwitterProjectCreateFlow) {
      payload = {
        ...payload,
        daysToDeliver,
        outputGroupType,
        launchDate: launchDate ? formatDateForApi(launchDate) : null,
        outputGroupsData: [], // handled below
        outputVideoData: [], // handled below
        totalOutputVideos: outputGroupsData.flat(1).length,
      };
    }

    /*
       munge the complicated fields into the correct format. This is currently a little repetitive when it comes to
       sourceMedia items, since they're not to be put in the same arrays as the other media items. For now, sourceMedia items are
       included in the output groups as though they were normal media items, then filtered out afterwards.

    */

    payload.outputGroupsData = outputGroupsData.map((outputGroup) => ({
      outputVideos: outputGroup.map((mediaItem) => {
        const ret = {
          videoNumber: mediaItem.videoNumber,
          name: mediaItem.name,
          outputFileType: MEDIA.FILE_TYPES.VIDEO_TYPE,
          // outputVideoTypeId: '', // This field isn't used for projects that have a product
          sourceMediaId: mediaItem.sourceMediaId,
          formatId: mediaItem.format,
          outputType: mediaItem.outputType,
        };
        if (mediaItem.outputType === OUTPUT_TYPES.VARIATION) {
          ret.variation = mediaItem.variation
            ? {
                variationTypeId: mediaItem.variation.variationTypeId,
                description: mediaItem.variation.description,
              }
            : {};
        }

        return ret;
      }),
    }));

    // find sourceMedia ID for each output group, if present:
    payload.outputGroupsData.forEach((outputGroup) => {
      const sourceMediaObject = outputGroup.outputVideos.find(
        (mediaItem) => mediaItem.sourceMediaId,
      );
      if (sourceMediaObject) {
        outputGroup.sourceMediaId = sourceMediaObject.sourceMediaId;
      }
    });

    // Remove the sourceMedia items from outputVideos:
    payload.outputGroupsData.forEach((outputGroup) => {
      outputGroup.outputVideos = outputGroup.outputVideos.filter(
        (mediaItem) => !mediaItem.sourceMediaId,
      );
    });

    payload.outputVideoData = payload.outputGroupsData
      .map((outputGroup) => outputGroup.outputVideos)
      .flat()
      .filter(
        (
          mediaItem, // also remove sourceMedia items from outputVideoData
        ) => !mediaItem.sourceMediaId,
      );

    // submit the project for creation, at last:
    return ProjectService.createProject(payload, currentPartnerId).then(
      async (res) => {
        this.setState({ isSavingProject: false });
        const projectId = res.data ? res.data.id : null;
        if (projectId) {
          finalProjectAddons.forEach((addOn) => {
            if (addOn.selected) {
              ProjectProjectAddonService.addProjectAddon(projectId, addOn.id);
            }
          });
        }

        return res;
      },
    );
  };

  //
  // Basics Section
  //

  onProjectNameInput = (event) => {
    const projectName = event.target.value;
    this.setState({ projectName }, this.isComplete);
  };

  onProjectDescriptionInput = (event) => {
    const projectDescription = event.target.value;
    this.setState({ projectDescription });
  };

  handleProjectOwnerSelect = (memberId) => {
    this.setState({ projectOwnerId: memberId });
  };

  handleProductClick = (product, isDisabled) => {
    if (isDisabled) {
      intercomAPI('showNewMessage', product.tooltip);
      this.setState({ productId: null }); // deselect any previously-selected product
    } else {
      intercomAPI('hide');
      this.setState(
        (prevState) => {
          // If the user decides to change the product type we want to reset the output groups
          // in case they have previously selected one that is not allowed in the newly selected product type.
          if (prevState && product.id !== prevState.productId) {
            return {
              productId: product.id,
              outputGroupArray: [],
              projectType: null,
              productName: product.name,
            };
          }
        },
        () => {
          this.applyOutputGroupCount(1); // cheap way to initialize that first, now-empty output group
        },
      );
    }
  };

  //
  // Outputs Section
  //

  findOutputInGroupById = (groupId, outputId) => {
    const { outputGroupArray } = this.state;
    const validOutputGroup = outputGroupArray.find(
      (group) => group.groupId === groupId,
    );

    return validOutputGroup.outputList
      ? validOutputGroup.outputList.find((output) => output.id === outputId)
      : null;
  };

  findSelectedOutput = (formatList) => {
    let selectedFormat = null;
    const selectedCategory = formatList.find((category) => category.selected);
    if (selectedCategory) {
      selectedFormat = selectedCategory.formatList.find(
        (format) => format.selected,
      );
    }

    return selectedFormat;
  };

  getFormatById = (formatId, product) => {
    product = product ? product : this.state.product;
    return product.formats.find((format) => format.id === formatId);
  };

  getVariationById = (variationTypeId) => {
    const { variationTypes } = this.state;
    return variationTypes.find((variation) => variation.id === variationTypeId);
  };

  getOutputGroupWithFormatUpdated = (format) => {
    const { currentOutputGroupId, outputGroupArray } = this.state;
    const localOutputGroupArray = [...outputGroupArray];
    const validOutputGroup = localOutputGroupArray.find(
      (group) => group.groupId === currentOutputGroupId,
    );
    if (validOutputGroup) {
      const formatIndex = validOutputGroup.outputList.findIndex(
        (output) => output.id === format.id,
      );
      if (formatIndex > -1) {
        validOutputGroup.outputList.splice(formatIndex, 1, format);
      } else {
        validOutputGroup.outputList.push(format);
      }
    }

    return localOutputGroupArray;
  };

  getOutputGroupWithFormatRemoved = (groupId, outputId) => {
    const { outputGroupArray } = this.state;
    const localOutputGroupArray = [...outputGroupArray];
    const validOutputGroup = localOutputGroupArray.find(
      (group) => group.groupId === groupId,
    );
    if (validOutputGroup) {
      const formatIndex = validOutputGroup.outputList.findIndex(
        (output) => output.id === outputId,
      );
      if (formatIndex > -1) {
        if (formatIndex === 0) {
          validOutputGroup.outputList = [];
        } else {
          validOutputGroup.outputList.splice(formatIndex, 1);
        }
      }
    }

    return localOutputGroupArray;
  };

  newOutputGroup = (groupId) => {
    const outputGroup = { groupId, outputList: [] };
    return new OutputGroupStructure(outputGroup);
  };

  removeOutputGroup = (groupId) => {
    // remove the output group with the given ID, and renumbers the remaining groups to keep the IDs sequential.
    this.setState(
      (prevState) => {
        const outputGroupArray = prevState.outputGroupArray
          .filter(
            (
              outputGroup, // filter to remove the to-be-deleted-group
            ) => outputGroup.groupId !== groupId,
          )
          .map((outputGroup, idx) => {
            // renumber all groups based on their (post-deletion) index
            outputGroup.groupId = idx + 1;
            return outputGroup;
          });
        const outputGroupLength = outputGroupArray.length;
        return { outputGroupArray, outputGroupLength };
      },
      () => {
        // if we just deleted the last output group, re-init with an empty one:
        if (this.state.outputGroupArray.length === 0) {
          this.applyOutputGroupCount(1);
        }
      },
    );
  };

  updateProjectType = (projectType) => {
    // As of AGL-1084, changing the project type resets all output groups.
    // TODO AGL-1085 changing project type when there are already assigned output formats should pop a confirmation that all
    // selections will be reset.
    this.setState(
      {
        projectType,
        outputGroupArray: [],
      },
      () => {
        this.applyOutputGroupCount(1); // cheap way to initialize that first, now-empty output group
      },
    );
  };

  applyOutputGroupCount = (outputGroupLength) => {
    const formatList = this.state.product.formats;
    if (this.state.outputGroupArray.length !== outputGroupLength) {
      this.setState(
        (prevState) => {
          let updatedGroupArray;
          if (prevState.outputGroupArray.length > outputGroupLength) {
            updatedGroupArray = [...prevState.outputGroupArray];
            const deleteCount = updatedGroupArray.length - outputGroupLength;
            updatedGroupArray.splice(outputGroupLength, deleteCount);
          } else {
            const newOutputGroupArray = [];
            let startingIdx = prevState.outputGroupArray.length;
            for (
              let i = prevState.outputGroupArray.length;
              i < outputGroupLength;
              i++
            ) {
              startingIdx += 1;
              const outputGroup = this.newOutputGroup(startingIdx);
              if (formatList.length === 1) {
                outputGroup.outputList.push(
                  new ProjectPrepFormatShape(
                    1,
                    PROJECT.OUTPUT_TYPES.HERO,
                    new Format(formatList[0]),
                  ),
                );
              }

              newOutputGroupArray.push(outputGroup);
            }

            updatedGroupArray =
              prevState.outputGroupArray.concat(newOutputGroupArray);
          }

          return {
            outputGroupArray: updatedGroupArray,
            outputGroupLength: updatedGroupArray.length,
          };
        },
        () => {
          // https://vidmob.atlassian.net/browse/AGL-1414
          // Clean up this condition so it's only called if hero output is preselected.

          // If the format list has 1 format, it will preselect on hero output if users want hero-variation projects.
          // So we want to make sure the data is ready for new project creation.
          this.setState((prevState) => ({
            ...prevState,
            outputGroupsData: this.prepareOutputsForSubmission(),
          }));
        },
      );
    }
  };

  updateOutputGroupCount = (event) => {
    let outputGroupLength = parseInt(event.target.value, 10);
    if (isNaN(outputGroupLength)) {
      outputGroupLength = '';
    }

    this.setState((prevState) => {
      if (prevState.outputGroupLength !== outputGroupLength) {
        return {
          outputGroupLength,
        };
      }
    });
    if (!isNaN(outputGroupLength) && outputGroupLength > 0) {
      this.applyOutputGroupCount(outputGroupLength);
    }
  };

  getNextOutputId = (outputList) => {
    const lastId = outputList.sort((a, b) => a.id - b.id)[
      outputList.length - 1
    ];
    return lastId ? lastId.id : 1;
  };

  onVariationCountUpdate = (groupId, variationCount) => {
    const { outputGroupArray } = this.state;
    const localOutputGroupArray = [...outputGroupArray];
    const validOutputGroup = localOutputGroupArray.find(
      (group) => group.groupId === groupId,
    );
    if (validOutputGroup) {
      let lastOutputId = this.getNextOutputId(validOutputGroup.outputList);
      for (let i = 0; i < variationCount; i++) {
        lastOutputId += 1;
        const format = new ProjectPrepFormatShape(
          lastOutputId,
          PROJECT.OUTPUT_TYPES.VARIATION,
          new Format({}),
        );
        validOutputGroup.outputList.push(format);
      }
    }

    this.setState({ outputGroupArray });
  };

  removeFormat = (groupId, outputId) => {
    const updatedOutputGroupArray = this.getOutputGroupWithFormatRemoved(
      groupId,
      outputId,
    );
    this.setState(
      { outputGroupArray: updatedOutputGroupArray },
      this.triggerUserUpdate,
    );
  };

  updateFormat = (updatedList) => {
    const selectedFormat = updatedList
      ? this.findSelectedOutput(updatedList.categoryList)
      : null;
    if (selectedFormat) {
      const { currentOutputId, isHero } = this.state;
      let outputId = currentOutputId;
      let outputType = PROJECT.OUTPUT_TYPES.VARIATION;
      const variationObj = updatedList.selectedVariation
        ? updatedList.selectedVariation
        : null;
      const variationNote = updatedList.variationNote
        ? updatedList.variationNote
        : null;
      if (isHero) {
        outputId = HERO_FORMAT_DEFAULT_ID;
        outputType = PROJECT.OUTPUT_TYPES.HERO;
      }

      const outputFormat = new ProjectPrepFormatShape(
        outputId,
        outputType,
        selectedFormat,
        variationObj,
        variationNote,
      );
      const updatedOutputGroupArray =
        this.getOutputGroupWithFormatUpdated(outputFormat);
      this.setState(
        { outputGroupArray: updatedOutputGroupArray },
        this.triggerUserUpdate,
      );
    }

    this.closeFormatSelectModal();
  };

  closeFormatSelectModal = () => {
    this.setState({
      formatSelectModalOpen: false,
      currentOutputGroupId: undefined,
      isHero: false,
    });
  };

  openHeroModalForOutputGroup = (outputGroupId) => {
    const heroOutput = this.findOutputInGroupById(
      outputGroupId,
      HERO_FORMAT_DEFAULT_ID,
    );
    this.setState(() => ({
      currentOutputGroupId: outputGroupId,
      categoryList: this.prepareCategoryListForDisplay(heroOutput),
      formatSelectModalOpen: true,
      currentVariationNote: '',
      isHero: true,
    }));
  };

  uploadHero = (sourceMedia, outputGroupId) => {
    /* If sourceMedia exists, start a new upload.
       If sourceMedia is falsy, the user selected "reset"; remove the hero.
    */
    this.setState((prevState) => {
      const newOutputGroupArray = [...prevState.outputGroupArray];
      const outputGroupIndex = newOutputGroupArray.findIndex(
        (outputGroup) => outputGroup.groupId === outputGroupId,
      );

      newOutputGroupArray[outputGroupIndex] =
        newOutputGroupArray[outputGroupIndex] || [];
      if (sourceMedia) {
        // Add new hero; preserve already-selected variations, if there are any:
        newOutputGroupArray[outputGroupIndex].outputList[0] =
          new ProjectPrepFormatShape(
            null,
            PROJECT.OUTPUT_TYPES.HERO,
            new Format({}),
            null,
            null,
            sourceMedia.id,
          );
        newOutputGroupArray[outputGroupIndex].uploadedHeroMedia = sourceMedia;
      } else {
        // falsy sourceMedia means user selected "reset": remove the hero
        newOutputGroupArray[outputGroupIndex].outputList[0] =
          new ProjectPrepFormatShape(
            null,
            PROJECT.OUTPUT_TYPES.HERO,
            new Format({}),
            null,
            null,
          );
        delete newOutputGroupArray[outputGroupIndex].uploadedHeroMedia;
      }

      return { outputGroupArray: newOutputGroupArray };
    }, this.triggerUserUpdate);
  };

  openVariationModalForOutputGroup = (outputGroupId, outputId) => {
    const variationOutput = this.findOutputInGroupById(outputGroupId, outputId);
    this.setState(() => ({
      currentOutputId: outputId,
      currentOutputGroupId: outputGroupId,
      categoryList: this.prepareCategoryListForDisplay(variationOutput),
      variationList: this.prepareVariationListForDisplay(variationOutput),
      formatSelectModalOpen: true,
      currentVariationNote: variationOutput.variationNote
        ? variationOutput.variationNote
        : '',
      isHero: false,
    }));
  };

  prepareVariationListForDisplay = (output) => {
    const { variationList } = this.state;
    let selectedId = -1;
    if (output && output.variation && output.variation.id) {
      selectedId = output.variation.id;
    }

    return variationList.map((variation) => {
      variation.selected = variation.id === selectedId;
      return variation;
    });
  };

  prepareCategoryListForDisplay = (output) => {
    const { product } = this.state;
    if (output && output.format && output.format.category) {
      return new CategoryGroupStructure(product.formats).map((category) => {
        category.selected = category.id === output.format.category.id;
        category.formatList = category.formatList.map((item) => {
          item.selected = item.id === output.format.id;
          return item;
        });
        return category;
      });
    }

    const categoryList = new CategoryGroupStructure(product.formats);
    categoryList[0].selected = true;
    return categoryList;
  };

  prepareOutputsForSubmission = () => {
    const { outputGroupArray } = this.state;
    let outputCount = 1;
    const outputGroupsData = [];
    outputGroupArray.forEach((group) => {
      const outputGroup = [];
      if (group.outputList.length > 0) {
        group.outputList.forEach((output) => {
          const newOutput = outputObjectForProject(output, outputCount);
          if (newOutput) {
            outputCount += 1;
            outputGroup.push(newOutput);
          }
        });
        outputGroupsData.push(outputGroup);
      }
    });
    return outputGroupsData;
  };

  triggerUserUpdate = () => {
    const { projectType } = this.state;

    // Convert project type into output group type (they are effectively the same thing but use different constant strings)
    const outputGroupType = PROJECT.OUTPUT_GROUP_TYPE[projectType];

    this.setState({
      outputGroupType,
      outputGroupsData: this.prepareOutputsForSubmission(),
    });
  };

  onOutputsSectionLoad = () => {
    const { outputGroupsData, variationTypes } = this.state;
    const product = this.getProduct();

    if (!product) {
      // If the browser navigation back button is used on the step after outputs
      // it will redirect to product type select but briefly try to load outputs again
      // this prevents an error trying to access product data AGL-1600
      return;
    }

    const maxOutputGroups = product.maxOutputGroups
      ? product.maxOutputGroups
      : null;
    const outputGroupArray = [];
    if (outputGroupsData.length > 0) {
      outputGroupsData.forEach((group, index) => {
        const outputGroup = this.newOutputGroup(index + 1);
        group.forEach((output) => {
          const format = this.getFormatById(output.format, product);
          // If the format we already had was not found in the products formats list it is likely that the user changed
          // the product so we can safely ignore the format.
          if (format) {
            let variationObj;
            let variationNote;
            if (output.variation) {
              variationObj = this.getVariationById(
                output.variation.variationTypeId,
              );
              variationNote = output.variation.description;
            }

            outputGroup.outputList.push(
              new ProjectPrepFormatShape(
                output.videoNumber,
                output.outputType,
                new Format(format),
                variationObj,
                variationNote,
              ),
            );
          }
        });
        if (outputGroup.outputList.length > 0) {
          outputGroupArray.push(outputGroup);
        }
      });
    }

    // If the outputGroupArray is empty and there is only one format available add it automatically.
    if (outputGroupArray.length === 0) {
      if (product.formats.length === 1) {
        const outputGroup = this.newOutputGroup(1);
        outputGroup.outputList.push(
          new ProjectPrepFormatShape(
            1,
            PROJECT.OUTPUT_TYPES.HERO,
            new Format(product.formats[0]),
          ),
        );
        outputGroupArray.push(outputGroup);
      } else {
        outputGroupArray.push(this.newOutputGroup(1));
      }
    }

    if (maxOutputGroups && maxOutputGroups === 1) {
      // TODO consolidate this check - https://vidmob.atlassian.net/browse/AGL-1413
      // DRAGON: maxOutputGroups === 1 is currently how we identify ACS Basic.
      // ACS Basic only allows one project type, so preselect it:
      this.updateProjectType(PROJECT.PROJECT_TYPE.NEW_HEROES);

      // If only one outputgroup is allowed, preset the outputGroupArray.
      // This is harmless but unnecessary now, since the default number of output groups is set to 1 anyway.
      // But design is lobbying for the default to be changed back to zero, which would cause this to be needed again,
      // so just to prevent this tripping anyone else up if we can't talk them out of that, I'm leaving this in just in case:
      if (outputGroupArray.length === 0) {
        outputGroupArray.push(this.newOutputGroup(1));
      }
    }

    this.setState(
      {
        product,
        outputGroupArray,
        maxOutputGroups,
        outputGroupLength: outputGroupArray.length,
        variationList: variationTypes,
      },
      this.triggerUserUpdate,
    );
  };

  onUploadChange = (upload) => {
    const { outputGroupArray } = this.state;

    const updatedOutputGroupArray = [...outputGroupArray];

    updatedOutputGroupArray.forEach((outputGroup) => {
      if (!outputGroup.outputList[0]) {
        return;
      }

      const { sourceMediaId } = outputGroup.outputList[0];

      if (sourceMediaId) {
        const mediaUpload = upload.files.find(
          (file) => file.media.id === sourceMediaId,
        );

        if (mediaUpload) {
          outputGroup.uploadedHeroMedia.uploadProgress = mediaUpload.percentage;
          outputGroup.uploadedHeroMedia.uploadStatus = mediaUpload.status;
        }
      }
    });

    this.setState({ outputGroupArray: updatedOutputGroupArray });
  };

  //
  // Add Ons Section
  //

  onAddonsSectionLoad = () => {
    const product = this.getProduct();
    let projectAddons = [];
    if (product && product.optionalProjectAddons.length > 0) {
      projectAddons = product.optionalProjectAddons;
    }

    this.setState({ projectAddons });
  };

  onAddonSelect = (identifier, selected) => {
    this.setState((prevState) => {
      const projectAddons = prevState.projectAddons.map((projectAddon) => {
        if (projectAddon.identifier === identifier) {
          projectAddon.selected = selected;
        }

        return projectAddon;
      });
      return {
        projectAddons,
      };
    });
  };

  //
  // Key Dates Section
  //

  /*

  Naming things is hard:
  minimumTurnaroundDays is a hard limit; users should not be allowed to select a date before that range.
  minimumTurnaroundTime is a soft limit; users can select before that range and the project will be converted to a rush project.

  Business logic built into this component:
    - "rush delivery" sets a turnaround date PROJECT_DEFAULTS.RUSH_DURATION days in the future
    - Launch date must be after the turnaround date, the product minimumTurnaroundDays, or at least RUSH_DURATION days after today
    - Prevent user setting a turnaround date that is after the launch date (if they fill the form in out of order)
  */

  onKeyDatesSectionLoad = () => {
    const { turnaroundDate, launchDate } = this.state;
    const product = this.getProduct();

    const { minimumTurnaroundDays } = product;
    const minimumTurnaroundTime =
      product.minTurnaroundTime || PROJECT_DEFAULTS.RUSH_DURATION;
    const isRushAllowed = this.getIsRushDeliveryIsAvailableForProduct(product);
    const isRush = turnaroundDate
      ? daysUntil(turnaroundDate) <= PROJECT_DEFAULTS.RUSH_DURATION
      : false;

    this.setState({
      turnaroundDate: turnaroundDate ? moment(turnaroundDate) : null,
      launchDate: launchDate ? moment(launchDate) : null,
      isRush,
      minimumTurnaroundDays,
      minimumTurnaroundTime,
      isRushAllowed,
    });
  };

  // rush delivery might be disabled either because it is disabled on the product via excludedAddons
  // or because it has minimum turnaround days set
  getIsRushDeliveryIsAvailableForProduct = (product) =>
    !product.minimumTurnaroundDays &&
    !product.excludedProjectAddons.some(
      ({ identifier }) => identifier === RUSH_DELIVERY_ADDON_IDENTIFIER,
    );

  handleUserInputKeyDates = () => {
    // DRAGON: This function was yelling because nothing was used, when eslint was re-enabled.
    // It is being referenced, so I am leaving it in place in case we need it and need it to be fixed.
    // const {turnaroundDate, launchDate} = this.state;
    // const userInput = {
    //   daysToDeliver: turnaroundDate ? daysUntil(turnaroundDate) : null, // int
    //   launchDate: launchDate ? formatDateForApi(launchDate) : null, // ISO Date string, without the milliseconds
    //   turnaroundDate: turnaroundDate ? formatDateForApi(turnaroundDate) : null
    // };
  };

  handleNewTurnaroundDate = (newDate) => {
    // if the user selected a date less than minimumTurnaroundTime days in the future, automatically set rush mode.
    // (This depends on the calendar preventing the user from selecting inappropriate dates, such as when rush mode is not allowed)
    this.setState((prevState) => {
      const daysUntilSelectedDate = daysUntil(newDate);
      const newDateIsRush =
        prevState.isRushAllowed &&
        daysUntilSelectedDate < prevState.minimumTurnaroundTime;

      // BUSINESS LOGIC If the user tries to set a date sooner than project's RUSH_DURATION (currently 4 days), we mark it as rush and set
      // a turnaround time of RUSH_DELIVERY_TURNAROUND_DAYS (currently 2)
      return {
        daysToDeliver: newDateIsRush
          ? RUSH_DELIVERY_TURNAROUND_DAYS
          : daysUntilSelectedDate,
        turnaroundDate: newDate,
        isRush: newDateIsRush,
      };
    }, this.handleUserInputKeyDates);
  };

  handleNewLaunchDate = (newDate) => {
    this.setState({ launchDate: newDate }, this.handleUserInputKeyDates);
  };

  handleIsRush = (selected) => {
    // BUSINESS LOGIC as in handleNewTurnaroundDate, override the turnaround date for rush projects
    this.setState(
      {
        isRush: Boolean(selected),
        turnaroundDate: selected
          ? today().add(RUSH_DELIVERY_TURNAROUND_DAYS, 'day')
          : null,
        daysToDeliver: selected ? RUSH_DELIVERY_TURNAROUND_DAYS : null,
      },
      this.handleUserInputKeyDates,
    );
  };

  // minimum number of days in the future that the user should be allowed to select in either date picker
  getStartDateConstraint = () => {
    const { isRushAllowed, minimumTurnaroundDays, minimumTurnaroundTime } =
      this.state;
    const possibleConstraints = [RUSH_DELIVERY_TURNAROUND_DAYS]; // absolute minimum (currently 2 days)

    // hard limit, if any:
    if (minimumTurnaroundDays) {
      possibleConstraints.push(minimumTurnaroundDays);
    }

    // soft limit:
    if (minimumTurnaroundTime && !isRushAllowed) {
      // if rush is disabled, treat the soft limit (if any) as a hard limit
      possibleConstraints.push(minimumTurnaroundTime);
    }

    return Math.max(...possibleConstraints);
  };

  getTurnaroundDateFirstAvailableDate = () =>
    today().add(this.getStartDateConstraint(), 'days');

  getLaunchDateFirstAvailableDate = () => {
    const possibleConstraints = [this.getStartDateConstraint()];
    if (this.state.turnaroundDate) {
      // don't allow launch before turnaround
      possibleConstraints.push(daysUntil(this.state.turnaroundDate));
    }

    return today().add(Math.max(...possibleConstraints), 'days');
  };

  turnaroundDateIsOutsideRange = (d) => {
    // return true to disallow a date

    // Prevent selecting a turnaround date after the launch date, if one exists
    if (this.state.launchDate && daysBetween(this.state.launchDate, d) > 0) {
      return true;
    }

    // prevent selecting a turnaround date before the minimum allowed
    return daysUntil(d) < this.getStartDateConstraint();
  };

  launchDateIsOutsideRange = (d) => {
    // return true to disallow a date.

    // Launch date must be after the turnaround date, if there is one
    if (
      this.state.turnaroundDate &&
      daysBetween(this.state.turnaroundDate, d) < 0
    ) {
      return true;
    }

    // prevent selecting a turnaround date before the minimum allowed
    return daysUntil(d) < this.getStartDateConstraint();
  };

  // this is for keyDates
  isKeyDatesCompleted = () => {
    const { turnaroundDate, launchDate } = this.state;
    const userInput = {
      daysToDeliver: turnaroundDate ? daysUntil(turnaroundDate) : null, // int
      launchDate: launchDate ? formatDateForApi(launchDate) : null, // ISO Date string, without the milliseconds
      turnaroundDate: turnaroundDate ? formatDateForApi(turnaroundDate) : null,
    };
    return userInput.daysToDeliver !== null && userInput.daysToLaunch !== null;
  };

  //
  // Data Key Dates Section
  //

  onDataKeyDatesSectionLoad = () => {
    const productInfo = this.getProduct();

    if (productInfo && productInfo.minimumTurnaroundDays) {
      // calculate the earliest due date on section load to avoid doing it on every date picker render
      const earliestDueDate = getDateAfterGivenBusinessDays(
        today(),
        productInfo.minimumTurnaroundDays,
      );

      this.setState({
        earliestDueDate,
        minimumTurnaroundDays: productInfo.minimumTurnaroundDays,
      });
    }
  };

  handleDueDate = (dueDate) => {
    this.setState({ dueDate, daysToDeliver: daysUntil(dueDate) });
  };

  handleAnalysisPeriod = (startDate, endDate) => {
    this.setState({
      analysisPeriodStart: startDate,
      analysisPeriodEnd: endDate,
    });
  };

  getDueDateFirstAvailableDate = () => this.state.earliestDueDate;

  dueDateIsOutsideRange = (d) => {
    // return true to disallow a date
    const { earliestDueDate } = this.state;

    return earliestDueDate && moment(d).isBefore(earliestDueDate);
  };

  render() {
    const { children, canSkipRedirect, isEnterprisePartner } = this.props;
    return (
      <CreateProjectContext.Provider
        value={{
          ...this.state,
          canSkipRedirect,
          currentStep: this.props.currentStep,
          closeFormatSelectModal: this.closeFormatSelectModal,
          dueDateIsOutsideRange: this.dueDateIsOutsideRange,
          findOutputInGroupById: this.findOutputInGroupById,
          findSelectedOutput: this.findSelectedOutput,
          getDueDateFirstAvailableDate: this.getDueDateFirstAvailableDate,
          getFormatById: this.getFormatById,
          getOutputGroupWithFormatRemoved: this.getOutputGroupWithFormatRemoved,
          getOutputGroupWithFormatUpdated: this.getOutputGroupWithFormatUpdated,
          getProduct: this.getProduct,
          getStepData: this.getStepData,
          getTurnaroundDateFirstAvailableDate:
            this.getTurnaroundDateFirstAvailableDate,
          getLaunchDateFirstAvailableDate: this.getLaunchDateFirstAvailableDate,
          getVariationById: this.getVariationById,
          handleAnalysisPeriod: this.handleAnalysisPeriod,
          handleDueDate: this.handleDueDate,
          handleIsRush: this.handleIsRush,
          handleNewLaunchDate: this.handleNewLaunchDate,
          handleNewTurnaroundDate: this.handleNewTurnaroundDate,
          handleProductClick: this.handleProductClick,
          handleProjectOwnerSelect: this.handleProjectOwnerSelect,
          handleUserInputKeyDates: this.handleUserInputKeyDates,
          isStepComplete: this.isStepComplete,
          isEnterprisePartner,
          launchDateIsOutsideRange: this.launchDateIsOutsideRange,
          onAddonSelect: this.onAddonSelect,
          onAddonsSectionLoad: this.onAddonsSectionLoad,
          onDataKeyDatesSectionLoad: this.onDataKeyDatesSectionLoad,
          onKeyDatesSectionLoad: this.onKeyDatesSectionLoad,
          onOutputsSectionLoad: this.onOutputsSectionLoad,
          onProjectNameInput: this.onProjectNameInput,
          onProjectDescriptionInput: this.onProjectDescriptionInput,
          onUploadChange: this.onUploadChange,
          onVariationCountUpdate: this.onVariationCountUpdate,
          openHeroModalForOutputGroup: this.openHeroModalForOutputGroup,
          openVariationModalForOutputGroup:
            this.openVariationModalForOutputGroup,
          removeFormat: this.removeFormat,
          removeOutputGroup: this.removeOutputGroup,
          resetProvider: this.reset,
          saveProject: this.saveProject,
          setCurrentUserAndPartnerInContext:
            this.setCurrentUserAndPartnerInContext,
          setProductsInContext: this.setProductsInContext,
          triggerUserUpdate: this.triggerUserUpdate,
          turnaroundDateIsOutsideRange: this.turnaroundDateIsOutsideRange,
          updateFormat: this.updateFormat,
          updateOutputGroupCount: this.updateOutputGroupCount,
          updateProductTypeInContext: this.updateProductTypeInContext,
          updateProjectType: this.updateProjectType,
          uploadHero: this.uploadHero,
        }}
      >
        {Children.only(children)}
      </CreateProjectContext.Provider>
    );
  }
}

CreateProjectContextProvider.propTypes = {
  canSkipRedirect: bool.isRequired,
  children: node.isRequired,
  currentPartnerId: number.isRequired,
  currentUserId: number.isRequired,
  isEnterprisePartner: bool.isRequired,
  isInTwitterProjectCreateFlow: bool.isRequired,
  products: arrayOf(object).isRequired,
  projectRushAddon: shape({ id: number }).isRequired,
  variationTypes: arrayOf(object).isRequired,
  currentStep: string,
  initialProductType: string,
  projectId: number,
};

CreateProjectContextProvider.defaultProps = {
  initialProductType: null,
  projectId: null,
  /**
   * The current step - this should only be null if we are actively redirecting from /create/project
   */
  currentStep: null,
};

export default CreateProjectContextProvider;
