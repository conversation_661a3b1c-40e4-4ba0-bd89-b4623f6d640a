import React, { Component } from 'react';
import { useSelector } from 'react-redux';
import { bool, func, shape, arrayOf, number } from 'prop-types';
import siteMap from '../../routing/siteMap';
import Modal, { <PERSON><PERSON><PERSON><PERSON>, ModalHeader, ModalFooter } from './../Modal';
import { RectangularSearchField } from './../SearchField';
import { connect } from 'react-redux';
import { IntlShape } from '../../utils/intlShape';
import { injectIntl } from 'react-intl';
import FlatList from './../FlatList';
import PartnerListItem from './PartnerListItem';
import partnerSlice from '../../redux/slices/partner.slice';
import searchBlankIcon from '../../assets/icons/blanks/bs-search.svg';
import BlankState from '../BlankState';
import { isNil } from '../../utils/typeCheckUtils';
import { partnerShape } from '../../featureServices/partner/partnerShapes';
import pathToRegexp from 'path-to-regexp';
import history from './../../routing/history';
import './ModalPartnerSwitch.scss';
import { GLOBALS } from '../../constants';
import { getPartner } from '../../featureServices/partner/getUserPartners';
import { updatePartnerInPartnersList } from '../../redux/actions/user.actions';
import escapeStringRegexp from 'escape-string-regexp';
import { matchPath, generatePath } from 'react-router-dom';
import { getScorecardsLandingViewLink } from '../../creativeScoring/redux/selectors/complianceShared.selectors';
import { groupedNavPaths } from '../__views/SideNav/navPathsConstants';

const currentProjectRegexp = pathToRegexp(siteMap.currentProject);
const brandGovernanceRegexp = pathToRegexp(
  siteMap.creativeIntelligenceCompliance,
);

const { PARTNER_SPECIFIC_FEATURES } = GLOBALS;
const { BRAND_GOVERNANCE } = PARTNER_SPECIFIC_FEATURES;

class ModalPartnerSwitch extends Component {
  static propTypes = {
    isOpen: bool.isRequired,
    partnerList: arrayOf(shape(partnerShape)).isRequired,
    togglePartnerSelectionModal: func.isRequired,
    updateCurrentPartner: func.isRequired,
    currentProjectPartnerId: number,
    initialPartner: shape(partnerShape),
    isPartnerSelectionModalHasCancel: bool,
    intl: IntlShape.isRequired,
  };

  static defaultProps = {
    currentProjectPartnerId: null,
    initialPartner: null,
    isPartnerSelectionModalHasCancel: true,
  };

  state = {
    searchInput: '',
    selectedPartner: null,
  };

  handleSearch = (searchInput) => {
    this.setState({ searchInput });
  };

  selectPartner = (selectedPartner) => {
    this.setState({ selectedPartner });
  };

  updatePartner = (selectedPartner) => {
    const { updateCurrentPartner } = this.props;
    const oldPartnerId = this.props.initialPartner.id;
    updateCurrentPartner({ newPartner: selectedPartner, oldPartnerId });
  };

  updatePartnersList = (updatedPartner) => {
    const { updatePartnerInPartnersList } = this.props;
    updatePartnerInPartnersList(updatedPartner);
  };

  /**
   * If the user is switching partners while looking at the project of a partner
   * that does not match the partner they are switching to, kick them to active projects.
   * @param  {Number} selectedPartnerId ID of the requested partner
   */
  redirectIfInOtherPartnersProject = (selectedPartnerId) => {
    const { currentProjectPartnerId } = this.props;
    // we check the location because current project could be set while outside of a project route -
    // it is not *unset* when leaving a project.
    if (
      // eslint-disable-next-line no-undef
      location.pathname.match(currentProjectRegexp) &&
      (!currentProjectPartnerId ||
        currentProjectPartnerId !== selectedPartnerId)
    ) {
      history.push(siteMap.activeProjects);
    }
  };

  redirectIfInOtherPartnersScoringPage = (selectedPartner) => {
    // eslint-disable-next-line no-undef
    const isInScoring = location.pathname.match(brandGovernanceRegexp);
    if (!isInScoring) {
      return;
    }

    // redirect to empty state page if we are already in Scoring and new partner does not have scoring enabled
    if (!selectedPartner.featureList[BRAND_GOVERNANCE]) {
      history.push(generatePath(siteMap.try, { featureName: 'scoring' }));
      // eslint-disable-next-line no-undef
      location.reload();
    }

    const currentProjectPartnerId = this.props.initialPartner.id;

    // if we switch partners
    if (
      !currentProjectPartnerId ||
      currentProjectPartnerId !== selectedPartner.id
    ) {
      // if we are already in Scorecards, or if new partner is trying to see the dashboard without admin permission, redirect to correct Scorecards page
      // eslint-disable-next-line no-undef
      const isInAnyScorecardsPage = matchPath(location.pathname, [
        siteMap.creativeIntelligenceReports,
        siteMap.creativeScoringScoreCardLanding,
        siteMap.creativeScoringScoreCardDetails,
      ]);

      if (isInAnyScorecardsPage) {
        const scoreCardsUrl = useSelector(getScorecardsLandingViewLink);
        history.push(scoreCardsUrl);
      }

      // if we are on a report, redirect to the Reports landing page
      const isInARollupReport = matchPath(
        // eslint-disable-next-line no-undef
        location.pathname,
        siteMap.creativeScoringRollUpReport,
      );
      if (isInARollupReport) {
        const reportsHomeUrl = generatePath(
          siteMap.creativeIntelligenceRollupReportsLanding,
        );
        history.push(reportsHomeUrl);
      }

      // refresh to load in all the new batches
      // eslint-disable-next-line no-undef
      location.reload();
    }
  };

  redirectIfInWorkspacePage = () => {
    // eslint-disable-next-line no-undef
    const workspaceDetailsMatch = matchPath(location.pathname, {
      path: siteMap.workspaceDetails,
    });

    // eslint-disable-next-line no-undef
    const workspaceAdAccountsMatch = matchPath(location.pathname, {
      path: siteMap.workspaceAdAccount,
    });

    if (workspaceDetailsMatch || workspaceAdAccountsMatch) {
      history.push(siteMap.workspaceList);
    }
  };

  refreshIfInIntegrations = () => {
    const integrationsMatch = matchPath(
      // eslint-disable-next-line no-undef
      location.pathname,
      groupedNavPaths.organization,
    );

    if (integrationsMatch) {
      window.location.reload();
    }
  };

  getStoredPartner = () => {
    const { partnerList } = this.props;
    const sessionPartnerId = Number(sessionStorage.getItem('partnerId'));
    const localStoragePartnerId = Number(localStorage.getItem('partnerId'));

    if (!isNil(sessionPartnerId)) {
      return partnerList.find(
        (partner) => Number(partner.id) === sessionPartnerId,
      );
    }

    if (!isNil(localStoragePartnerId)) {
      return partnerList.find(
        (partner) => Number(partner.id) === localStoragePartnerId,
      );
    }

    return null;
  };

  renderPartnerItem = (partner) => {
    const { selectedPartner } = this.state;
    const { initialPartner } = this.props;
    const currentPartner =
      selectedPartner || initialPartner || this.getStoredPartner();
    const isInitialPartner = initialPartner && initialPartner.id === partner.id;

    return (
      <PartnerListItem
        key={partner.id}
        partner={partner}
        selectedPartnerId={currentPartner ? currentPartner.id : null}
        onClick={() => this.selectPartner(isInitialPartner ? null : partner)}
      />
    );
  };

  matchSearchTerm = (partner) => {
    const { searchInput } = this.state;
    return (
      partner.name
        .toLowerCase()
        .search(escapeStringRegexp(searchInput.toLowerCase())) >= 0
    );
  };

  renderBlankSearchResult = () => {
    const { intl } = this.props;
    return (
      <BlankState
        iconAsset={searchBlankIcon}
        message={intl.messages['blank.search']}
        className="center-absolute"
      />
    );
  };

  changeUserPartner = async () => {
    const { selectedPartner } = this.state;
    if (selectedPartner === null) {
      this.cancelSelection();
      return;
    }

    let modifiedSelectedPartner = { ...selectedPartner };
    if (
      !modifiedSelectedPartner.featureList ||
      !modifiedSelectedPartner.permissions
    ) {
      modifiedSelectedPartner = await getPartner(modifiedSelectedPartner);
      this.updatePartnersList(modifiedSelectedPartner);
    }

    this.cancelSelection();
    this.redirectIfInWorkspacePage();
    this.redirectIfInOtherPartnersProject(modifiedSelectedPartner.id);
    this.refreshIfInIntegrations();
    this.redirectIfInOtherPartnersScoringPage(modifiedSelectedPartner);
    this.updatePartner(modifiedSelectedPartner);
  };

  cancelSelection = () => {
    const { togglePartnerSelectionModal } = this.props;
    togglePartnerSelectionModal(false);
  };

  componentDidMount() {
    const storagePartner = this.getStoredPartner();

    // Initialize state with partner in browser storage
    if (!isNil(storagePartner)) {
      this.setState({ selectedPartner: storagePartner });
    }
  }

  componentDidUpdate(prevProps) {
    const prevPartnerFeatureList = prevProps.initialPartner?.featureList;
    const currentPartnerFeatureList = this.props.initialPartner?.featureList;

    // If a partner switch occurs where previous partner had Brand Governance and new partner doesn't,
    // and we are on Brand Governance, the app should redirect to active projects page.
    // We need to do this here because we need the partner selection to complete and pull in new features list
    // before we can make a decision.
    if (
      // eslint-disable-next-line no-undef
      location.pathname.match(brandGovernanceRegexp) &&
      prevPartnerFeatureList &&
      currentPartnerFeatureList &&
      prevPartnerFeatureList[BRAND_GOVERNANCE] !==
        currentPartnerFeatureList[BRAND_GOVERNANCE] &&
      !currentPartnerFeatureList[BRAND_GOVERNANCE]
    ) {
      history.push(siteMap.activeProjects);
    }
  }

  render() {
    const { searchInput, selectedPartner } = this.state;
    const { isOpen, partnerList, isPartnerSelectionModalHasCancel } =
      this.props;
    const { intl } = this.props;
    const modalHeaderMessageKey = 'modal.partnerSelection.title';

    return (
      <Modal
        isOpen={isOpen}
        id="partner-switch-modal"
        onExit={() => this.setState({ searchInput: '', selectedPartner: null })}
      >
        <ModalHeader title={intl.messages[modalHeaderMessageKey]} />
        <ModalBody isFullWidth notScrollable maxHeight={290}>
          <RectangularSearchField
            placeholder={
              intl.messages['modal.partnerSelection.searchPlaceholder']
            }
            collapsible={false}
            searchTerm={searchInput}
            onInput={this.handleSearch}
          />
          <div className="partner-list-wrapper">
            {/*
              The .filter(Boolean) is a temporary workaround for failed permissions checks on partners.
              TODO after API-1257 is done, can remove the filter() (or not, it's harmless if left in)
            */}
            <FlatList
              list={partnerList.filter(Boolean)}
              filterBy={searchInput.length > 0 ? this.matchSearchTerm : null}
              renderItem={this.renderPartnerItem}
              renderWhenEmpty={this.renderBlankSearchResult}
            />
          </div>
        </ModalBody>
        <ModalFooter
          doneButtonLabel={intl.messages['global.select.label']}
          doneButtonDisabled={
            isNil(this.getStoredPartner()) && isNil(selectedPartner)
          }
          onCancel={
            isPartnerSelectionModalHasCancel ? this.cancelSelection : null
          }
          onDone={this.changeUserPartner}
        />
      </Modal>
    );
  }
}

ModalPartnerSwitch.propTypes = {
  isOpen: bool.isRequired,
  partnerList: arrayOf(shape(partnerShape)).isRequired,
  togglePartnerSelectionModal: func.isRequired,
  updateCurrentPartner: func.isRequired,
  updatePartnerInPartnersList: func.isRequired,
  currentProjectPartnerId: number,
  initialPartner: shape(partnerShape),

  isPartnerSelectionModalHasCancel: bool,
};

ModalPartnerSwitch.defaultProps = {
  currentProjectPartnerId: null,
  initialPartner: null,
  isPartnerSelectionModalHasCancel: true,
};

const mapStateToProps = ({ partner, project, user }) => ({
  initialPartner: partner.currentPartner,
  currentProjectPartnerId: project.currentProject
    ? project.currentProject.partnerId
    : null,
  isPartnerSelectionModalHasCancel: partner.partnerSelectionModalHasCancel,
  isOpen: partner.partnerSelectionModalVisible,
  partnerList: user?.currentUser?.partners || [],
});

export default connect(mapStateToProps, {
  updateCurrentPartner: partnerSlice.actions.updateCurrentPartner,
  togglePartnerSelectionModal: partnerSlice.actions.togglePartnerSelectionModal,
  updatePartnerInPartnersList,
})(injectIntl(ModalPartnerSwitch));
