import React, { useState } from 'react';
import useReportFilterOptions from '../hooks/useReportFilterOptions';
import useReportFilters from '../hooks/useReportFilters';
import {
  Operator,
  ReportAdvancedFilterDefinition,
  ReportAdvancedFilterKey,
  ReportFilterKey,
  ReportScopeFilterDefinition,
  Value,
  AdvancedFiltersChannelType,
  ReportScopeParametersFilterKey,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  ListItem,
} from '../types';
import FilterEntry from './FilterEntry';
import { ShareRounded } from '@mui/icons-material';
import { SHOULD_NOT_SHOW_SELECT_ALL } from '../constants';
import { getValueType } from '../utils/getters';
import useReportChildFilterOptions from '../hooks/useReportChildFilterOptions';
import { REPORTS_THAT_SUPPORT_DATA_LEVEL } from '../../../creativeScoring/components/ScoringFilters/constants';
import {
  DataLevelType,
  ScoringReportType,
} from '../../../creativeScoring/types/rollUpReports.types';

const customHoverComponentSx = {
  right: '278px',
  width: '220px',
  p: '12px',
};

export default function FilterValuesDataWrapper({
  filterKey,
  onChange,
  isAdvancedFilter = false,
  channel,
  onDeleteFilter,
  options: passedOptions,
}: {
  filterKey: ReportFilterKey;
  onChange: (args: {
    filterKey: ReportFilterKey;
    value: Value;
    operator: Operator;
  }) => void;
  isAdvancedFilter?: boolean;
  channel?: AdvancedFiltersChannelType;
  onDeleteFilter?: () => void;
  options?: ListItem[];
}) {
  const {
    getAdvancedFilterValue,
    getFilterValue,
    filterDefinitions,
    reportType,
  } = useReportFilters();

  const [hoveredItem, setHoveredItem] = useState<{
    description: string;
    name: string;
  } | null>(null);

  const filterDefinition:
    | ReportScopeFilterDefinition
    | ReportAdvancedFilterDefinition = isAdvancedFilter
    ? (filterDefinitions.advancedFilters?.[
        filterKey as ReportAdvancedFilterKey
      ] as ReportAdvancedFilterDefinition)
    : (filterDefinitions[
        filterKey as ReportScopeParametersFilterKey
      ] as ReportScopeFilterDefinition);
  const childFilterDefinition = filterDefinition?.childFilterDefinition;
  const isScoringReport = Object.values(ScoringReportType).includes(
    reportType as ScoringReportType,
  );

  const { isDisabled, ...options } = useReportFilterOptions(
    filterKey,
    filterDefinition,
    isAdvancedFilter,
    channel,
  );

  const { isChildFilterDisplayed, ...childFilterOptions } =
    useReportChildFilterOptions(
      childFilterDefinition?.key,
      childFilterDefinition,
    );

  const dataLevel =
    isScoringReport &&
    REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType as ScoringReportType)
      ? (getFilterValue(REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL)
          ?.value as DataLevelType) || null
      : null;

  const isHidden = filterDefinition?.hideOnDataLevel === dataLevel;

  const disabled =
    filterDefinition?.isDisabled || options?.isLoading || isDisabled;
  const actsAsScopeFilter = (filterDefinition as ReportAdvancedFilterDefinition)
    ?.actsAsScopeFilter;

  const filterValue = isAdvancedFilter
    ? getAdvancedFilterValue(
        channel!,
        filterKey as ReportAdvancedFilterKey,
        false,
        actsAsScopeFilter,
      )
    : getFilterValue(filterKey);

  if (!filterDefinition) {
    return null;
  }

  const filter = {
    key: filterDefinition.key,
    labelKey: filterDefinition.labelKey,
    valueType: filterDefinition.valueType,
    textFieldInputType: filterDefinition.textFieldInputType,
    numberInputRule: filterDefinition.numberInputRule,
    hasGroupSelect: filterDefinition.hasGroupSelect,
    isMandatory: filterDefinition.isMandatory,
    initialBlur: filterDefinition.initialBlur,
    suffix: filterDefinition.suffix,
    icon: 'icon' in filterDefinition && filterDefinition.icon,
    disabled,
    placeholderOverrideKey: filterDefinition.placeholderOverrideKey,
    operator:
      filterValue?.operator || filterDefinition.defaultOperator || Operator.IN,
    operators: filterDefinition.operators || [Operator.IN],
    value: filterValue?.value,
    valueOptions: passedOptions || options.data || [],
    canDelete: filterDefinition.canDelete,
    onDeleteFilter,
    disabledTooltipKey: disabled
      ? filterDefinition.disabledTooltipKey
      : undefined,
    autoSelectIfSingleValue: false,
    displaySearch: filterDefinition.displaySearch,
    onlySelectWithinDivider: undefined,
    infoTooltipKey: filterDefinition.infoTooltipKey,
    hoverComponent: filterDefinition.hoverComponent,
    sectionDescriptionKey: filterDefinition.sectionDescriptionKey,
  };

  const childFilterValue = childFilterDefinition
    ? getFilterValue(childFilterDefinition.key)
    : null;

  const childFilterProps = childFilterDefinition && {
    key: childFilterDefinition.key,
    filterKey: childFilterDefinition.key,
    labelKey: childFilterDefinition.labelKey,
    valueType: childFilterDefinition.valueType,
    isMandatory: childFilterDefinition.isMandatory,
    initialBlur: childFilterDefinition.initialBlur,
    displaySearch: childFilterDefinition.displaySearch,
    operator:
      childFilterValue?.operator ||
      childFilterDefinition.defaultOperator ||
      Operator.IN,
    operators: childFilterDefinition.operators || [Operator.IN],
    value: childFilterValue?.value,
    valueOptions: childFilterOptions.data || [],
    disabled: childFilterDefinition.isDisabled || options.isLoading,
    onChange,
    displayed: isChildFilterDisplayed,
    DropdownLabelElement: childFilterDefinition.DropdownLabelElement,
    DropdownOptionSlot: childFilterDefinition.DropdownOptionSlot,
    customMenuItemSx: childFilterDefinition.customMenuItemSx,
  };

  const handleMenuItemMouseEnter = (
    description: string | null | undefined,
    name: string | null | undefined,
  ) => {
    if (filter.hoverComponent && description && name) {
      const somethingChanged =
        hoveredItem?.description !== description || hoveredItem?.name !== name;
      if (somethingChanged) {
        setHoveredItem({ description, name });
      }
    }
  };

  const handleMenuItemMouseLeave = () => {};

  const HoverComponent = filter.hoverComponent;
  const descriptionPopover = HoverComponent ? (
    <HoverComponent
      name={hoveredItem?.name}
      description={hoveredItem?.description}
      customPaperSx={customHoverComponentSx}
    />
  ) : null;

  if (isHidden) {
    return null;
  }

  return (
    <FilterEntry
      key={filter.key}
      filterKey={filter.key}
      icon={
        filter.icon || (
          <ShareRounded fontSize="small" sx={{ color: '#757575' }} />
        )
      }
      onChange={onChange}
      textFieldInputType={filter.textFieldInputType}
      numberInputRule={filter.numberInputRule}
      canDelete={filter.canDelete}
      value={filter.value}
      displaySearch={filter.displaySearch}
      operator={filter.operator}
      operators={filter.operators}
      valueOptions={filter.valueOptions}
      valueType={getValueType(filter.valueType, filter.operator)}
      suffix={filter.suffix}
      disabled={filter.disabled}
      disabledTooltipKey={filter.disabledTooltipKey}
      placeholderOverrideKey={filter.placeholderOverrideKey}
      hideOperator={!filter.operators || filter.operators.length <= 1}
      autoSelectIfSingleValue={filter.autoSelectIfSingleValue}
      shouldShowSelectAll={!SHOULD_NOT_SHOW_SELECT_ALL.includes(filter.key)}
      shouldShowRemoveAll={SHOULD_NOT_SHOW_SELECT_ALL.includes(filter.key)}
      onlySelectWithinDivider={filter.onlySelectWithinDivider}
      hasGroupSelect={filter.hasGroupSelect}
      labelKey={filter.labelKey}
      onDelete={filter.onDeleteFilter}
      infoTooltipKey={filter.infoTooltipKey}
      isMandatory={filter.isMandatory}
      initialBlur={filter.initialBlur}
      childFilterProps={childFilterProps}
      hoverComponent={hoveredItem && descriptionPopover}
      handleMenuItemMouseEnter={handleMenuItemMouseEnter}
      handleMenuItemMouseLeave={handleMenuItemMouseLeave}
      sectionDescriptionKey={filter.sectionDescriptionKey}
    />
  );
}
