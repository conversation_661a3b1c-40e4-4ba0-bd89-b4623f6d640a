import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import AccordionFilterWrapper from './AccordionFilterWrapper';
import {
  AdvancedFiltersChannelType,
  BatchType,
  ChannelListItem,
  ListItem,
  Operator,
  REPORT_ADVANCED_FILTERS,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  REPORT_SCOPE_METRICS_FILTERS,
  REPORT_SCOPE_PARAMETERS_FILTERS,
  ReportAdvancedFilterKey,
  ReportFilterKey,
  Value,
} from '../types';
import getMUIIconForChannel from '../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import useReportFilters from '../hooks/useReportFilters';
import { VidMobStack } from '../../../vidMobComponentWrappers';
import GenericSidePanel from './GenericSidePanel';
import CampaignIdentifierSelector from './CampaignIdentifierSelector';
import { REPORTS_THAT_SUPPORT_DATA_LEVEL } from '../../../creativeScoring/components/ScoringFilters/constants';
import { SelectableDataGridRow } from './SelectableDataGrid/types';
import {
  getAdvancedFiltersRequestParamsForChannel,
  getAvailableAdvancedFilterTypesForChannel,
  getAvailableFiltersBasedOnAddedAdvancedFilters,
  getCanAddFilter,
  getSelectedAdvancedFilterKeys,
} from '../utils/getters';
import { ChannelType } from '../../../types/channels.types';
import {
  DataLevelType,
  ScoringReportType,
} from '../../../creativeScoring/types/rollUpReports.types';
import FilterWrapper from './FilterWrapper';
import { Kpi } from '../../../types/kpi.types';
import { AnalyticsReportType } from '../../../creativeAnalytics/types/reports.types';
import { IdAndName } from '../../../types/common.types';
import { getMediaTypeFilterValue } from '../../../creativeAnalytics/components/AnalyticsFilters/utilsV2/getters';

const iconSx = {
  height: '16px',
  width: '16px',
};

const sidePanelSx = {
  width: '40%',
  '@media (max-width: 1440px)': {
    minWidth: '616px',
  },
  '@media (max-width: 616px)': {
    width: '768px',
  },
  '@media (min-width: 1440px)': {
    width: '40%',
    minWidth: '616px',
  },
};

interface Props {
  channel: ChannelListItem;
  isAccordion?: boolean;
  staticFilterKeys?: ReportFilterKey[];
}

const FiltersForChannel = ({
  channel,
  isAccordion = false,
  staticFilterKeys,
}: Props) => {
  const intl = useIntl();
  const {
    stagingFilters,
    stagingAdvancedFilters,
    getAdvancedFiltersByChannel,
    getAdvancedFilterValue,
    setFilterValue,
    setAdvancedFilterValue,
    removeStagingFilterValue,
    removeStagingAdvancedFilterValue,
    filterDefinitions,
    getFilterValue,
    reportType,
  } = useReportFilters();

  const isAnalyticsReport = Object.values(AnalyticsReportType).includes(
    reportType as AnalyticsReportType,
  );
  const isScoringReport = Object.values(ScoringReportType).includes(
    reportType as ScoringReportType,
  );

  const advancedFilterDefinitions = filterDefinitions.advancedFilters;

  const batchType = getFilterValue(REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE)
    ?.value as ListItem;

  const kpiValue = isAnalyticsReport
    ? (getFilterValue(REPORT_SCOPE_METRICS_FILTERS.KPI)?.value as Kpi)
    : null;

  const dataLevel =
    isScoringReport &&
    REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType as ScoringReportType)
      ? (getFilterValue(REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL)
          ?.value as DataLevelType)
      : null;

  const allAvailableFiltersForChannel =
    getAvailableAdvancedFilterTypesForChannel({
      reportType,
      channel: channel.id,
      advancedFilterDefinitions,
      batchType: batchType?.id as BatchType,
      dataLevel,
    });

  const requestParams = getAdvancedFiltersRequestParamsForChannel(
    stagingFilters,
    channel.id as ChannelType,
  );

  const [isCampaignsPanelOpen, setIsCampaignsPanelOpen] =
    useState<boolean>(false);

  const initialSelectedCampaignsValue = (stagingAdvancedFilters[channel.id]?.[
    REPORT_ADVANCED_FILTERS.CAMPAIGN_IDENTIFIER
  ]?.value || []) as ListItem[];
  const [campaignsValue, setCampaignsValue] = useState<ListItem[]>(
    initialSelectedCampaignsValue,
  );

  useEffect(() => {
    setCampaignsValue(initialSelectedCampaignsValue);
  }, [JSON.stringify(initialSelectedCampaignsValue)]);

  const advancedFilters = getAdvancedFiltersByChannel(channel.id, reportType);

  const availableFilters = getAvailableFiltersBasedOnAddedAdvancedFilters({
    allAvailableFiltersForChannel,
    stagingFilters,
    advancedFilters,
    advancedFilterDefinitions,
  });

  const handleToggleCampaignsPanel = () => {
    setIsCampaignsPanelOpen((prev) => !prev);
  };

  const onAddFilter = (filterId: ReportAdvancedFilterKey) => {
    const filterDefinition = advancedFilterDefinitions?.[filterId];

    if (filterDefinition?.actsAsScopeFilter) {
      setFilterValue(filterId, {
        value: [],
        operator: filterDefinition?.defaultOperator || Operator.IN,
      });
    } else {
      setAdvancedFilterValue(
        channel.id as AdvancedFiltersChannelType,
        filterId as ReportAdvancedFilterKey,
        {
          value: [],
          operator: filterDefinition?.defaultOperator || Operator.IN,
        },
      );
    }
  };

  const onChange = ({
    filterKey,
    value,
    operator,
  }: {
    filterKey: ReportFilterKey;
    value: Value;
    operator: Operator;
  }) => {
    if (filterKey === REPORT_ADVANCED_FILTERS.CAMPAIGN_IDENTIFIER) {
      handleToggleCampaignsPanel();
      return;
    }

    const actsAsScopeFilter =
      advancedFilterDefinitions?.[filterKey as ReportAdvancedFilterKey]
        ?.actsAsScopeFilter;

    if (actsAsScopeFilter) {
      setFilterValue(filterKey, {
        value,
        operator,
      });
    } else {
      setAdvancedFilterValue(channel.id, filterKey as ReportAdvancedFilterKey, {
        value,
        operator,
      });
    }
  };

  const onCampaignsSave = () => {
    setAdvancedFilterValue(
      channel.id as AdvancedFiltersChannelType,
      REPORT_ADVANCED_FILTERS.CAMPAIGN_IDENTIFIER,
      {
        value: campaignsValue,
        operator: Operator.IN,
      },
    );
    handleToggleCampaignsPanel();
  };

  const onDeleteFilter = (filterId: ReportAdvancedFilterKey) => {
    const filterDefinition = advancedFilterDefinitions?.[filterId];

    if (filterDefinition?.actsAsScopeFilter) {
      removeStagingFilterValue(filterId);
    } else {
      removeStagingAdvancedFilterValue(
        channel.id as AdvancedFiltersChannelType,
        filterId,
      );
    }
  };

  const canAddFilter = getCanAddFilter(availableFilters);

  const filterKeys = getSelectedAdvancedFilterKeys(
    advancedFilters,
    staticFilterKeys,
  );

  const icon = getMUIIconForChannel(channel.id, iconSx, true);

  // Analytics only: set media type filter value based on KPI selection
  useEffect(() => {
    if (!isAnalyticsReport) {
      return;
    }

    const mediaTypeValue = getAdvancedFilterValue(
      channel.id as AdvancedFiltersChannelType,
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
    )?.value as IdAndName[];
    const mediaTypeFilterValue = getMediaTypeFilterValue(
      reportType as AnalyticsReportType,
      kpiValue,
      mediaTypeValue,
    );
    setAdvancedFilterValue(
      channel.id as AdvancedFiltersChannelType,
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
      {
        value: mediaTypeFilterValue,
        operator: Operator.IN,
      },
    );
  }, [isAnalyticsReport, kpiValue, reportType]);

  return (
    <VidMobStack>
      {isAccordion ? (
        <AccordionFilterWrapper
          key={channel.id}
          name={channel.name}
          channel={channel.id}
          icon={icon}
          filterKeys={filterKeys}
          addFilterMenuItems={availableFilters}
          onChange={onChange}
          hasDivider
          canAddFilter={canAddFilter}
          onAddFilter={onAddFilter}
          onDeleteFilter={onDeleteFilter}
        />
      ) : (
        <FilterWrapper
          key={channel.id}
          name={channel.name}
          channel={channel.id}
          icon={icon}
          filterKeys={filterKeys}
          addFilterMenuItems={availableFilters}
          onChange={onChange}
          canAddFilter={canAddFilter}
          onAddFilter={onAddFilter}
          onDeleteFilter={onDeleteFilter}
        />
      )}
      <GenericSidePanel
        open={isCampaignsPanelOpen}
        onClose={handleToggleCampaignsPanel}
        headerTitle={intl.formatMessage({
          id: 'ui.reportFilters.campaigns.panelTitle',
          defaultMessage: 'Select campaign(s)',
        })}
        isSubmitBtnDisabled={false}
        onSave={onCampaignsSave}
        sx={sidePanelSx}
        headerWithBottomBorder={false}
      >
        <CampaignIdentifierSelector
          requestParams={requestParams}
          selectedCampaigns={campaignsValue as SelectableDataGridRow[]}
          setSelectedCampaigns={
            setCampaignsValue as Dispatch<
              SetStateAction<SelectableDataGridRow[]>
            >
          }
        />
      </GenericSidePanel>
    </VidMobStack>
  );
};

export default FiltersForChannel;
