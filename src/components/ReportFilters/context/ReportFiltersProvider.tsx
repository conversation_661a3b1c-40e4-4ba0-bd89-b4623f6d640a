import React, { useState, useEffect, ReactNode } from 'react';
import { Backdrop } from '@mui/material';
import { ReportFiltersContext } from './ReportFiltersContext';
import {
  FilterValue,
  FiltersStorage,
  ReportFilterDefinitions,
  AdvancedFiltersStorage,
  ReportAdvancedFilterKey,
  REPORT_ADVANCED_FILTERS,
  ReportFilterKey,
  ReportFiltersLocalStorageFunctions,
  AdvancedFiltersChannelType,
  ReportPillarSpecificAdvancedFilterKey,
  ReportScopeFilterKey,
  ReportScopeFilterDefinition,
  ReportType,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
} from '../types';
import { VidMobBox, VidMobDrawer } from '../../../vidMobComponentWrappers';
import ReportFiltersDrawer from '../components/ReportFiltersDrawer';
import _ from 'lodash';
import { DRAWER_WIDTH } from '../constants';
import { getDefaultAdvancedFilters, getDefaultFilters } from '../utils/getters';
import { ChannelType } from '../../../types/channels.types';
import { Dispatch } from 'redux';
import FilterPanelContentWrapper from '../components/FilterPanelContentWrapper';
import { ScoringReportType } from '../../../creativeScoring/types/rollUpReports.types';

const wrapperSx = {
  width: '100%',
  height: '100%',
  display: 'flex',
  margin: 0,
  padding: 0,
};

const backdropSx = {
  color: 'rgba(0, 0, 0, 0.6)',
  zIndex: 6,
};

const drawerSx = {
  width: DRAWER_WIDTH,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: DRAWER_WIDTH,
    marginTop: '52px',
    height: 'calc(100% - 52px)',
    border: 'unset',
    borderLeft: '1px solid var(--divider, #BDBDBD)',
    zIndex: 7,
  },
};

type Props = {
  children: ReactNode;
  drawerContent: ReactNode;
  filterDefinitions: ReportFilterDefinitions;
  filterValidatorFunction: (filters: FiltersStorage) => boolean;
  reportType: ReportType;
  reportId?: string;
  supportedChannels: ChannelType[];
  localStorageFunctions: ReportFiltersLocalStorageFunctions;
  shouldAddIdToAppliedFilters?: boolean;
  updateFilterOptionsInRedux?: (
    data: unknown,
    reportType: string,
    filterKey: ReportFilterKey,
    dispatch: Dispatch,
  ) => void;
};

const ReportFiltersProvider = ({
  children,
  drawerContent,
  filterDefinitions,
  filterValidatorFunction = () => true,
  reportType,
  reportId,
  supportedChannels,
  localStorageFunctions,
  shouldAddIdToAppliedFilters = false,
  updateFilterOptionsInRedux,
}: Props) => {
  const {
    getFiltersFromLocalStorage,
    getAdvancedFiltersFromLocalStorage,
    setFiltersToLocalStorage,
    setAdvancedFiltersToLocalStorage,
    clearFiltersFromLocalStorage,
  } = localStorageFunctions;
  // Get scope filters from storage or defaults.
  const defaultValues = getFiltersFromLocalStorage(
    getDefaultFilters(filterDefinitions),
  );

  // Get advanced filters from storage or defaults.
  const defaultAdvancedFilters = getAdvancedFiltersFromLocalStorage(
    getDefaultAdvancedFilters(filterDefinitions),
  );

  // Memory state for scope & advanced filters.
  const [filters, setFilters] = useState<FiltersStorage>(defaultValues);
  const [advancedFilters, setAdvancedFilters] =
    useState<AdvancedFiltersStorage>(defaultAdvancedFilters);
  // Drawer state for scope & advanced filters.
  const [stagingFilters, setStagingFilters] =
    useState<FiltersStorage>(defaultValues);
  const [stagingAdvancedFilters, setAdvancedStagingFilters] =
    useState<AdvancedFiltersStorage>(defaultAdvancedFilters);
  // General flags.
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(true);
  const [isFilterDrawerLoading, setIsFilterDrawerLoading] =
    useState<boolean>(false);
  const [hasPendingFilters, setHasPendingFilters] = useState<boolean>(false);
  const [hasUnsavedAppliedFilters, setHasUnsavedAppliedFilters] =
    useState<boolean>(false);

  const [isOverlayVisible, setIsOverlayVisible] = useState(
    window.innerWidth < 1000,
  );

  useEffect(() => {
    if (reportId) {
      // don't load filters from local storage if reportId is provided
      clearFiltersFromLocalStorage();
      setIsFilterDrawerLoading(true);
    }
  }, [reportId]);

  useEffect(() => {
    const handleResize = () => {
      setIsOverlayVisible(window.innerWidth < 1000);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Apply all filters
  const applyFilters = () => {
    if (!hasPendingFilters) {
      return;
    }
    // Disable the apply button
    setHasPendingFilters(false);
    // Copy the staging filters to the local filters
    const filtersId = new Date().getTime();
    setFilters({
      ...stagingFilters,
      ...(shouldAddIdToAppliedFilters && { id: filtersId }),
    });
    setAdvancedFilters({
      ...stagingAdvancedFilters,
      ...(shouldAddIdToAppliedFilters && { id: filtersId }),
    });
    // Save staging filters to local storage
    setFiltersToLocalStorage(stagingFilters, stagingAdvancedFilters);
    // Enable the save button
    setHasUnsavedAppliedFilters(true);
    // Close the drawer
    setIsFilterDrawerOpen(false);
  };

  // RESET | REMOVE

  // util to remove advanced filter value for any channel
  const getUpdatedAdvancedFiltersAfterRemovingFilterForChannel = (
    prevFilters: AdvancedFiltersStorage,
    channel: AdvancedFiltersChannelType,
    filterKey: ReportAdvancedFilterKey,
  ) => {
    const newFilters = _.cloneDeep(prevFilters);
    if (newFilters[channel]) {
      delete newFilters[channel]?.[filterKey];
    }

    if (Object.keys(newFilters[channel] || {}).length === 0) {
      delete newFilters[channel];
    }
    return newFilters;
  };

  // util to remove advanced filter value for all channels
  const getUpdatedAdvancedFiltersAfterRemovingFilterForAllChannels = (
    prevFilters: AdvancedFiltersStorage,
    filterKey: ReportAdvancedFilterKey,
  ) =>
    supportedChannels.reduce((acc: AdvancedFiltersStorage, channel) => {
      const newAdvancedFiltersForChannel =
        getUpdatedAdvancedFiltersAfterRemovingFilterForChannel(
          prevFilters,
          channel,
          filterKey as ReportAdvancedFilterKey,
        );

      if (Object.keys(newAdvancedFiltersForChannel[channel] || {}).length > 0) {
        return {
          ...acc,
          [channel]: newAdvancedFiltersForChannel[channel],
        };
      }
      return acc;
    }, {});

  const resetFilter = (filterKey: ReportFilterKey) => {
    if (
      Object.values(REPORT_ADVANCED_FILTERS).includes(
        filterKey as ReportAdvancedFilterKey,
      )
    ) {
      const updatedAdvancedFilters =
        getUpdatedAdvancedFiltersAfterRemovingFilterForAllChannels(
          advancedFilters,
          filterKey as ReportAdvancedFilterKey,
        );

      setAdvancedStagingFilters(
        updatedAdvancedFilters as AdvancedFiltersStorage,
      );

      return;
    }

    setStagingFilters((prevFilters) => {
      const newFilters = _.cloneDeep(prevFilters);
      delete newFilters[filterKey];

      return newFilters;
    });
    setHasPendingFilters(true);
  };

  const removeStagingAdvancedFilterValue = (
    channel: AdvancedFiltersChannelType,
    filterKey: ReportAdvancedFilterKey,
  ) => {
    setAdvancedStagingFilters((prevFilters) =>
      getUpdatedAdvancedFiltersAfterRemovingFilterForChannel(
        prevFilters,
        channel,
        filterKey,
      ),
    );

    setHasPendingFilters(true);
  };

  // SETTERS
  const setFilterValue = (
    filterKey: ReportFilterKey,
    value: Omit<FilterValue, 'key'>,
    enableApplyButton = true,
  ) => {
    if (enableApplyButton) {
      setHasPendingFilters(true);
    }

    setStagingFilters((prevFilters) => {
      const newFilters = { ...prevFilters };
      newFilters[filterKey] = { key: filterKey, ...value };
      return newFilters;
    });
  };

  const setAdvancedFilterValue = (
    channel: AdvancedFiltersChannelType,
    filterKey: ReportAdvancedFilterKey | ReportPillarSpecificAdvancedFilterKey,
    value: Omit<FilterValue, 'key'>,
  ) => {
    setHasPendingFilters(true);
    setAdvancedStagingFilters((prevFilters) => {
      const newFilters = { ...prevFilters };
      newFilters[channel] = { ...newFilters[channel], [filterKey]: value };

      return newFilters;
    });
  };

  const saveFilterValueWithNoApply = (
    filterKey: ReportFilterKey,
    value: Omit<FilterValue, 'key'>,
    shouldEnableSaveReportButtonOnChange: boolean = true,
  ) => {
    const filtersId = new Date().getTime();
    resetDependentFilters(filterKey);
    const newFilters = {
      ...filters,
      ...(shouldAddIdToAppliedFilters && { id: filtersId }),
    };
    newFilters[filterKey] = { key: filterKey, ...value };

    setFiltersToLocalStorage(newFilters);
    setFilters(newFilters);
    setStagingFilters(newFilters);

    if (shouldEnableSaveReportButtonOnChange) {
      setHasUnsavedAppliedFilters(true);
    }
  };

  const resetDependentFilters = (filterKey: ReportFilterKey) => {
    // reset scope and criteria filters that depend on the filterKey
    // i.e. clear selected ad accounts and criteria
    const dependentFilterKeys = Object.keys(filterDefinitions).filter(
      (scopeOrCriteriaFilterKey) =>
        filterDefinitions[
          scopeOrCriteriaFilterKey as ReportScopeFilterKey
        ]?.dependentOn?.includes(filterKey),
    ) as ReportFilterKey[];

    const newFilters = _.cloneDeep(stagingFilters);
    dependentFilterKeys.forEach((filterKey) => {
      if (newFilters[filterKey]) {
        delete newFilters[filterKey];
      }
    });

    setFilters(newFilters);
    setStagingFilters(newFilters);
    setFiltersToLocalStorage(newFilters);

    // reset advanced filters that depend on the filterKey
    // e.g. clear Campaign Identifier, Ad Set Identifier, and Ad Identifier for each channel when we change the date range
    const advancedFilterDefinitions = filterDefinitions.advancedFilters;
    if (!advancedFilterDefinitions) {
      return;
    }

    const dependentAdvancedFilterKeys = Object.keys(
      advancedFilterDefinitions,
    ).filter((advancedFilterKey) =>
      advancedFilterDefinitions[
        advancedFilterKey as ReportAdvancedFilterKey
      ]?.dependentOn?.includes(filterKey),
    ) as ReportAdvancedFilterKey[];

    const advancedStagingFiltersCopy = _.cloneDeep(stagingAdvancedFilters);
    const newAdvancedFilters = supportedChannels.reduce((acc, channel) => {
      dependentAdvancedFilterKeys.forEach((advancedFilterKey) => {
        if (advancedStagingFiltersCopy[channel]?.[advancedFilterKey]) {
          delete advancedStagingFiltersCopy[channel]?.[advancedFilterKey];
        }
      });

      if (Object.keys(advancedStagingFiltersCopy[channel] || {}).length === 0) {
        delete advancedStagingFiltersCopy[channel];
      }

      return acc;
    }, advancedStagingFiltersCopy);

    setAdvancedFilters(newAdvancedFilters);
    setAdvancedStagingFilters(newAdvancedFilters);
    setAdvancedFiltersToLocalStorage(newAdvancedFilters);
  };

  const setSavedFilters = (
    savedScopeFilters: FilterValue[],
    savedAdvancedFilters?: AdvancedFiltersStorage,
  ) => {
    const savedFilters = savedScopeFilters.reduce((acc, filter) => {
      acc[filter.key] = filter;
      return acc;
    }, {} as FiltersStorage);

    const filtersId = new Date().getTime();
    setFilters({
      ...savedFilters,
      ...(shouldAddIdToAppliedFilters && { id: filtersId }),
    });
    setStagingFilters(savedFilters);

    if (savedAdvancedFilters) {
      setAdvancedFilters({
        ...savedAdvancedFilters,
        ...(shouldAddIdToAppliedFilters && { id: filtersId }),
      });
      setAdvancedStagingFilters(savedAdvancedFilters);
    }

    setHasPendingFilters(false);
    setFiltersToLocalStorage(savedFilters, savedAdvancedFilters);
    setIsFilterDrawerLoading(false);
  };

  const setDefaultValueIfScopeFilterEmpty = (
    key: ReportScopeFilterKey,
    filterDefinition: ReportScopeFilterDefinition,
  ) => {
    const value = getFilterValue(key);

    if (value) {
      return;
    }

    const getDefaultValueFn = filterDefinition.getDefaultValue;

    if (getDefaultValueFn) {
      const defaultValue = getDefaultValueFn();
      setFilterValue(
        key,
        {
          value: defaultValue.value,
          operator: defaultValue.operator,
        },
        false,
      );
    }
  };

  const setDefaultValuesForEmptyScopeFilters = () => {
    Object.entries(filterDefinitions).forEach(([key, definition]) => {
      setDefaultValueIfScopeFilterEmpty(
        key as ReportScopeFilterKey,
        definition as ReportScopeFilterDefinition,
      );
    });

    const advancedFilterDefinitions = filterDefinitions.advancedFilters;

    if (advancedFilterDefinitions) {
      Object.entries(advancedFilterDefinitions).forEach(([key, definition]) => {
        if (definition?.actsAsScopeFilter) {
          setDefaultValueIfScopeFilterEmpty(
            key as ReportScopeFilterKey,
            // we are only treating the advanced filters that behave like scope filters
            definition as unknown as ReportScopeFilterDefinition,
          );
        }
      });
    }
  };

  // GETTERS
  const getFilterValue = (
    filterKey: ReportFilterKey,
    isFromLocalStorage?: boolean,
  ): FilterValue | null =>
    ((isFromLocalStorage
      ? filters[filterKey]
      : stagingFilters[filterKey]) as FilterValue) || null;

  const getAdvancedFilterValue = (
    channel: AdvancedFiltersChannelType,
    filterKey: ReportAdvancedFilterKey | ReportPillarSpecificAdvancedFilterKey,
    isFromLocalStorage?: boolean,
    actsAsScopeFilter?: boolean,
  ): FilterValue | null => {
    if (actsAsScopeFilter) {
      return getFilterValue(filterKey, isFromLocalStorage);
    }

    if (isFromLocalStorage) {
      const channelAdvancedFilters = advancedFilters[channel];
      if (channelAdvancedFilters) {
        return (channelAdvancedFilters[filterKey] as FilterValue) || null;
      }
    }
    return (
      (stagingAdvancedFilters[channel]?.[filterKey] as FilterValue) || null
    );
  };

  const getAdvancedFiltersByChannel = (
    channel: AdvancedFiltersChannelType,
    reportType?: ReportType,
  ): AdvancedFiltersStorage[AdvancedFiltersChannelType] | null => {
    const advancedFiltersForChannel =
      (stagingAdvancedFilters[
        channel
      ] as AdvancedFiltersStorage[AdvancedFiltersChannelType]) || null;

    // Creative Adherence filter lives in scope filters (in local storage and staging filters)
    // but is displayed as a channel advanced filter for In-flight
    if (
      reportType === ScoringReportType.IN_FLIGHT &&
      stagingFilters[REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]
    ) {
      const filtersForChannel = advancedFiltersForChannel || {};
      return {
        [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]:
          stagingFilters[
            REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE
          ],
        ...filtersForChannel,
      };
    }

    return advancedFiltersForChannel;
  };

  const removeStagingFilterValue = (filterKey: ReportFilterKey) => {
    setStagingFilters((prevFilters) => {
      const newFilters = _.cloneDeep(prevFilters);
      delete newFilters[filterKey];
      return newFilters;
    });
    setHasPendingFilters(true);
  };

  const areFiltersValid = filterValidatorFunction(stagingFilters);

  const isApplyButtonEnabled =
    hasPendingFilters && areFiltersValid && !isFilterDrawerLoading;

  return (
    <ReportFiltersContext.Provider
      value={{
        reportType,
        reportId,
        supportedChannels,
        isFilterDrawerOpen,
        isFilterDrawerLoading,
        setIsFilterDrawerOpen,
        setIsFilterDrawerLoading,
        filterDefinitions,
        filters,
        stagingFilters,
        advancedFilters,
        stagingAdvancedFilters,
        getFilterValue,
        getAdvancedFilterValue,
        getAdvancedFiltersByChannel,
        setFilterValue,
        setAdvancedFilterValue,
        applyFilters,
        saveFilterValueWithNoApply,
        setDefaultValuesForEmptyScopeFilters,
        setSavedFilters,
        resetFilter,
        removeStagingFilterValue,
        removeStagingAdvancedFilterValue,
        hasPendingFilters,
        setHasUnsavedAppliedFilters,
        hasUnsavedAppliedFilters,
        updateFilterOptionsInRedux,
      }}
    >
      <VidMobBox sx={wrapperSx}>
        <FilterPanelContentWrapper open={isFilterDrawerOpen}>
          <Backdrop
            open={isFilterDrawerOpen && isOverlayVisible}
            onClick={() => setIsFilterDrawerOpen(false)}
            sx={backdropSx}
          />
          {children}
        </FilterPanelContentWrapper>
        <VidMobDrawer
          sx={drawerSx}
          variant="persistent"
          anchor="right"
          open={isFilterDrawerOpen}
        >
          <ReportFiltersDrawer
            isLoading={isFilterDrawerLoading}
            onApply={applyFilters}
            canApply={isApplyButtonEnabled}
            areFiltersValid={areFiltersValid}
            content={drawerContent}
            onClose={() => {
              setIsFilterDrawerOpen(false);
            }}
          />
        </VidMobDrawer>
      </VidMobBox>
    </ReportFiltersContext.Provider>
  );
};

export default ReportFiltersProvider;
