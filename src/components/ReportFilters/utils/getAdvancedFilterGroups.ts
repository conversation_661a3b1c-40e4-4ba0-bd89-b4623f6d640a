import {
  FilterGroupType,
  REPORT_ADVANCED_FILTERS,
  REPORT_ADVANCED_FILTERS_GROUPS,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  ReportType,
} from '../types';
import { createElement } from 'react';
import { CampaignIcon } from '../../../assets/vidmob-mui-icons/general/V2-CampaignIcon';
import { AdSetIcon } from '../../../assets/vidmob-mui-icons/general/V2-AdSetIcon';
import { AdIcon } from '../../../assets/vidmob-mui-icons/general/V2-AdIcon';
import { CreativeIcon } from '../../../assets/vidmob-mui-icons/general/V2-CreativeIcon';
import { ScoringReportType } from '../../../creativeScoring/types/rollUpReports.types';

export const getAdvancedFilterGroups = (
  reportType?: ReportType,
): FilterGroupType[] => [
  {
    id: REPORT_ADVANCED_FILTERS_GROUPS.CAMPAIGN,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.group.campaign',
    icon: createElement(CampaignIcon),
    children: [
      {
        id: REPORT_ADVANCED_FILTERS.CAMPAIGN_IDENTIFIER,
        labelKey:
          'ui.creative.intelligence.filtersDrawer.filter.campaignIdentifier',
      },
      {
        id: REPORT_ADVANCED_FILTERS.CAMPAIGN_OBJECTIVE,
        labelKey:
          'ui.creative.intelligence.filtersDrawer.filter.campaignObjective',
      },
    ],
  },
  {
    id: REPORT_ADVANCED_FILTERS_GROUPS.AD_SET,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.group.adSet',
    icon: createElement(AdSetIcon),
    children: [
      {
        id: REPORT_ADVANCED_FILTERS.AD_SET_IDENTIFIER,
        labelKey:
          'ui.creative.intelligence.filtersDrawer.filter.adSetIdentifier',
      },
    ],
  },
  {
    id: REPORT_ADVANCED_FILTERS_GROUPS.AD,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.group.ad',
    icon: createElement(AdIcon),
    children: [
      {
        id: REPORT_ADVANCED_FILTERS.AD_IMPRESSION,
        labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adImpression',
      },
      {
        id: REPORT_ADVANCED_FILTERS.AD_IDENTIFIER,
        labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adIdentifier',
      },
      {
        id: REPORT_ADVANCED_FILTERS.AD_TYPE,
        labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adType',
      },
      {
        id: REPORT_ADVANCED_FILTERS.AD_PLACEMENT,
        labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adPlacement',
      },
    ],
  },
  {
    id: REPORT_ADVANCED_FILTERS_GROUPS.CREATIVE,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.group.creative',
    icon: createElement(CreativeIcon),
    children: [
      ...(reportType === ScoringReportType.IN_FLIGHT
        ? [
            {
              id: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
              labelKey:
                'ui.creative.intelligence.filtersDrawer.filter.creativeAdherence',
              disabledTooltipKey:
                'ui.creative.intelligence.filtersDrawer.filter.creativeAdherence.disabled.tooltip',
            },
          ]
        : []),
      {
        id: REPORT_ADVANCED_FILTERS.CREATIVE_IMPRESSION,
        labelKey:
          'ui.creative.intelligence.filtersDrawer.filter.creativeImpression',
      },
      {
        id: REPORT_ADVANCED_FILTERS.CREATIVE_BY_VIDMOB,
        labelKey:
          'ui.creative.intelligence.filtersDrawer.filter.createdByVidmob',
      },
    ],
  },
];
