import React, { useEffect, useRef, useState } from 'react';
import CustomDialog from '../../muiCustomComponents/CustomDialog';
import Search from '../../muiCustomComponents/Search';
import {
  Box,
  Button,
  List,
  Skeleton,
  Tooltip,
  Typography,
} from '@mui/material';
import WorkspaceSwitcherModalOption from './WorkspaceSwitcherModalOption';
import { useSelector } from 'react-redux';
import { getCurrentPartnerId } from '../../redux/selectors/partner.selectors';
import { useIntl } from 'react-intl';
import { fetchWorkspaces } from '../../userManagement/redux/thunk/workspaces.thunk';
import { useAppDispatch } from '../../redux/store/appDispatch';
import {
  isLoadingSelector,
  showErrorBarSelector,
  workspacesPaginationNextOffsetSelector,
  workspacesPaginationTotalSizeSelector,
  workspacesSelector,
} from '../../userManagement/redux/selectors/workspaces.selectors';
import useDebounce from '../../hooks/useDebounce';
import { SEARCH_TERM_DEBOUNCE_DELAY_MS } from '../../constants/organization.constants';
import { generatePath, matchPath } from 'react-router-dom';
import siteMap from '../../routing/siteMap';
import history from '../../routing/history';
import { GLOBALS } from '../../constants';
import { WorkspaceSwitcherModalProps } from './types';
import {
  ROUTES_MAP,
  VOWELS,
} from '../../constants/workspaceSwitcherModal.constants';
import workspacesSlice, {
  Workspace,
} from '../../userManagement/redux/slices/workspaces.slice';
import EmptyState from '../EmptyState';
import { SearchFilledIcon } from '../../assets/vidmob-mui-icons/general';
import { useResetScoringSlices } from '../../creativeScoring/utils/useResetScoringSlices';
import { switchWorkspaceContext } from './switchWorkspaceContext';

const WorkspaceSwitcherModal = ({
  isOpen,
  onClose,
  organizationName,
}: WorkspaceSwitcherModalProps) => {
  const dispatch = useAppDispatch();
  const intl = useIntl();

  const currentWorkspaceId = useSelector(getCurrentPartnerId);
  const paginatedWorkspaces = useSelector(workspacesSelector);
  const [selectedWorkspaceId, setSelectedWorkspaceId] =
    useState(currentWorkspaceId);

  const [searchQuery, setSearchQuery] = useState<string | null>(null);
  const debouncedSearchTerm = useDebounce(
    searchQuery,
    SEARCH_TERM_DEBOUNCE_DELAY_MS,
  );

  const scrollableSectionRef = useRef(null);
  const nextOffset = useSelector(workspacesPaginationNextOffsetSelector);
  const workspacesPaginatedTotalSize = useSelector(
    workspacesPaginationTotalSizeSelector,
  );
  const [offset, setOffset] = useState<number>(0);
  const [fullWorkspaceList, setFullWorkspaceList] = useState<Workspace[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);

  const resetScoringSlices = useResetScoringSlices();

  const workspacesPerPage = 20;
  const skeletonCount = 3;

  const { PARTNER_SPECIFIC_FEATURES } = GLOBALS;
  const { BRAND_GOVERNANCE } = PARTNER_SPECIFIC_FEATURES;

  const isAPIResponseLoading = useSelector(isLoadingSelector);
  const isAPIResponseErrored = useSelector(showErrorBarSelector);
  const isEmptyState =
    !isAPIResponseLoading &&
    !isAPIResponseErrored &&
    workspacesPaginatedTotalSize === 0;
  const isInitialDataLoading =
    !isAPIResponseErrored && fullWorkspaceList.length === 0;
  const isSubmitButtonEnabled =
    selectedWorkspaceId !== currentWorkspaceId && fullWorkspaceList.length > 0;

  useEffect(() => {
    // prevents additional API call when search is updated and offset is reset to 0
    const isInitialLoadWithDefaultAPIParams =
      isInitialLoad && debouncedSearchTerm === null && offset === 0;
    const isNewSearch = offset === 0 && debouncedSearchTerm !== null;
    const hasReachedLastPaginatedPage = nextOffset === 0;

    if (
      (isNewSearch ||
        !hasReachedLastPaginatedPage ||
        isInitialLoadWithDefaultAPIParams) &&
      isOpen
    ) {
      dispatch(
        fetchWorkspaces({
          paginationParams: {
            perPage: workspacesPerPage,
            offset: offset,
          },
          searchParams: {
            search: debouncedSearchTerm,
          },
        }),
      );

      setIsInitialLoad(false);
    }
  }, [debouncedSearchTerm, isOpen, offset]);

  useEffect(() => {
    if (isOpen) {
      const fullWorkspaceIdsMap = new Map<number, Workspace>();
      fullWorkspaceList.forEach((workspace) =>
        fullWorkspaceIdsMap.set(workspace.id, workspace),
      );

      paginatedWorkspaces.forEach((workspace) => {
        if (!fullWorkspaceIdsMap.has(workspace.id)) {
          fullWorkspaceIdsMap.set(workspace.id, workspace);
        }
      });

      const combinedWorkspaces = Array.from(fullWorkspaceIdsMap.values());
      setFullWorkspaceList(combinedWorkspaces);
    }
  }, [paginatedWorkspaces]);

  useEffect(() => {
    return () => {
      dispatch(workspacesSlice.actions.resetWorkspaceData());
    };
  }, []);

  const onScroll = () => {
    if (scrollableSectionRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        scrollableSectionRef.current;
      const pixelsFromBottom = 200;
      const threshold = scrollHeight - pixelsFromBottom;
      const hasReachedThreshold = scrollTop + clientHeight >= threshold;

      if (hasReachedThreshold && nextOffset !== 0) {
        setOffset(nextOffset);
      }
    }
  };

  const resetFilters = () => {
    setOffset(0);
    setFullWorkspaceList([]);
    setSearchQuery(null);

    const workspaceId = getStoredWorkspaceId() ?? currentWorkspaceId;
    setSelectedWorkspaceId(workspaceId);
  };

  const onSearchChange = (value: string) => {
    if (fullWorkspaceList.length !== 0 || offset !== 0) {
      resetFilters();
    }

    setSearchQuery(value);
  };

  const headerStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    overflow: 'hidden',
  };

  const headerTextStyle = {
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    WebkitLineClamp: 2,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    lineHeight: '1.3',
  };

  const footerStyle = {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: '12px',
  };

  const getStoredWorkspaceId = () => {
    const sessionWorkspaceId = Number(sessionStorage.getItem('partnerId'));
    const localStorageWorkspaceId = Number(localStorage.getItem('partnerId'));

    if (sessionWorkspaceId) {
      return sessionWorkspaceId;
    }

    if (localStorageWorkspaceId) {
      return localStorageWorkspaceId;
    }

    return null;
  };

  const handleRedirectToOrgSwitcher = () => {
    history.push(siteMap.organizationSwitcher);
  };

  const handleCloseModal = () => {
    resetFilters();
    setIsInitialLoad(true);
    onClose();
  };

  const handleScoringRedirectAfterSwitch = (
    currentPath: string,
    workspace: any,
  ) => {
    if (!workspace.featureList[BRAND_GOVERNANCE]) {
      history.push(generatePath(siteMap.try, { featureName: 'scoring' }));
      location.reload();
    }

    const isInAnyScorecardsPage = matchPath(currentPath, [
      siteMap.creativeIntelligenceReports,
      siteMap.creativeScoringScoreCardLanding,
      siteMap.creativeScoringScoreCardDetails,
    ]);

    if (isInAnyScorecardsPage) {
      const scoreCardsUrl = generatePath(siteMap.creativeIntelligenceReports);
      history.push(scoreCardsUrl);
    }

    const isInARollupReport = matchPath(
      currentPath,
      siteMap.creativeScoringRollUpReport,
    );
    if (isInARollupReport) {
      history.push(siteMap.creativeIntelligenceRollupReportsLanding);
    }

    location.reload();
  };

  const redirectOrReloadPage = (currentPath: string) => {
    for (const entry of ROUTES_MAP) {
      const { path, redirectTo, reload } = entry;
      const match = matchPath(currentPath, path);

      if (match) {
        if (redirectTo) {
          history.push(redirectTo);
        }

        if (reload) {
          window.location.reload();
        }

        break;
      }
    }
  };

  const handleRedirectAfterSwitch = (updatedWorkspace: any) => {
    const currentPath = location.pathname;
    const isInScoring = matchPath(
      currentPath,
      siteMap.creativeIntelligenceCompliance,
    );

    if (isInScoring) {
      handleScoringRedirectAfterSwitch(currentPath, updatedWorkspace);
    } else {
      redirectOrReloadPage(currentPath);
    }
  };

  const handleSwitchWorkspace = async () => {
    const updatedWorkspace = await switchWorkspaceContext({
      currentWorkspaceId,
      targetWorkspaceId: selectedWorkspaceId,
      dispatch,
      resetScoringSlices,
    });

    if (updatedWorkspace) {
      handleRedirectAfterSwitch(updatedWorkspace);
    }

    handleCloseModal();
  };

  const workspaceOptions = () => {
    if (isEmptyState) {
      return (
        <Box sx={{ height: '290px' }}>
          <EmptyState
            icon={() => <SearchFilledIcon />}
            title={
              intl.messages['ui.reports.search.blankState.title'] as string
            }
            description={intl.formatMessage(
              { id: 'ui.reports.search.blankState.description' },
              { searchQuery },
            )}
          />
        </Box>
      );
    }

    if (isInitialDataLoading) {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            flexDirection: 'column',
            gap: '8px',
          }}
        >
          {Array.from({ length: skeletonCount }, (_, index) => (
            <Skeleton
              key={index}
              variant="rounded"
              sx={{ height: 80, width: '100%' }}
            />
          ))}
        </Box>
      );
    }

    return (
      <List
        sx={{ overflowY: 'auto', maxHeight: '360px' }}
        ref={scrollableSectionRef}
        onScroll={onScroll}
      >
        {fullWorkspaceList.map((workspace, index) => (
          <WorkspaceSwitcherModalOption
            keyValue={workspace.id}
            key={workspace.id}
            name={workspace.name}
            logoUrl={workspace.logoUrl}
            isSelected={selectedWorkspaceId === workspace.id}
            onClick={() => setSelectedWorkspaceId(workspace.id)}
            hasBottomDivider={index !== fullWorkspaceList.length - 1}
          />
        ))}
      </List>
    );
  };

  const workspaceModalBody = (
    <Box>
      <Box sx={{ marginBottom: '12px' }}>
        <Search
          placeholder={
            intl.messages['ui.workspaceSwitcher.search.label'] as string
          }
          onSearchChange={onSearchChange}
          onCollapse={() => setIsInitialLoad(true)}
        />
      </Box>
      {workspaceOptions()}
    </Box>
  );

  const workspaceFooterChildren = (
    <Box sx={footerStyle}>
      <Button
        onClick={handleRedirectToOrgSwitcher}
        sx={{ marginRight: '220px' }}
      >
        {
          intl.messages[
            'ui.workspaceSwitcher.button.switchOrganization.label'
          ] as string
        }
      </Button>
      <Button variant="contained" color="inherit" onClick={handleCloseModal}>
        {intl.messages['ui.workspaceSwitcher.button.cancel.label'] as string}
      </Button>
      <Button
        variant="contained"
        color="primary"
        onClick={handleSwitchWorkspace}
        disabled={!isSubmitButtonEnabled}
      >
        {intl.messages['ui.workspaceSwitcher.button.switch.label'] as string}
      </Button>
    </Box>
  );

  const getHeaderText = (organizationName: string) => {
    if (!organizationName) {
      return;
    }

    const startsWithVowel = VOWELS.includes(organizationName[0].toLowerCase());
    if (startsWithVowel) {
      return `${intl.formatMessage(
        { id: 'ui.workspaceSwitcher.header.vowelStart' },
        { organizationName: organizationName },
      )}`;
    }

    return intl.formatMessage(
      { id: 'ui.workspaceSwitcher.header' },
      { organizationName: organizationName },
    );
  };

  const workspaceHeaderChildren = (
    <Box sx={headerStyle}>
      <Tooltip title={getHeaderText(organizationName)} placement="top-end">
        <Typography variant="h6" sx={headerTextStyle}>
          {getHeaderText(organizationName)}
        </Typography>
      </Tooltip>
    </Box>
  );

  return (
    <CustomDialog
      isOpen={isOpen}
      customHeaderChildren={workspaceHeaderChildren}
      bodyChildren={workspaceModalBody}
      explicitDialogWidth={'600px'}
      customFooterChildren={workspaceFooterChildren}
    />
  );
};

export default WorkspaceSwitcherModal;
