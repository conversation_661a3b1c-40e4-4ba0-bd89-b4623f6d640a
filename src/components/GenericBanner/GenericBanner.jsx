import React from 'react';
import { Link } from 'react-router-dom';
import { string, func, element } from 'prop-types';
import './GenericBanner.scss';
import { GENERIC_BANNER_TYPES } from '../../constants/analytics.api.constants';

const { WARNING_WITH_CLOSE, SUCCESS_WITH_CLOSE } = GENERIC_BANNER_TYPES;

const GenericBanner = (props) => {
  const {
    type,
    icon,
    message,
    linkPath,
    linkMessage,
    closeBanner,
    iconComponent,
    messageStyle,
  } = props;

  return (
    <div className={`${type}-banner generic-banner`}>
      <span className="generic-banner-message">
        {icon && <img src={icon} alt="icon" className="generic-icon" />}
        {iconComponent && <div className="generic-icon">{iconComponent}</div>}
        <span className={`body-left-aligned-black ${messageStyle}`}>
          {message}
        </span>
      </span>
      {linkPath && linkMessage && (
        <Link to={linkPath} className="body-right-aligned-black">
          {linkMessage}
        </Link>
      )}
      {(type === WARNING_WITH_CLOSE || type === SUCCESS_WITH_CLOSE) && (
        <button type="button" className="close-button" onClick={closeBanner} />
      )}
    </div>
  );
};

GenericBanner.defaultProps = {
  linkPath: null,
  linkMessage: null,
  closeBanner() {},
  icon: null,
  iconComponent: null,
  messageStyle: '',
};

GenericBanner.propTypes = {
  message: string.isRequired,
  type: string.isRequired,
  closeBanner: func,
  icon: string,
  iconComponent: element,
  linkMessage: string,
  linkPath: string,
  messageStyle: string,
};

export default GenericBanner;
