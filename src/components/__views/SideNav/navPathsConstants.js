import { matchPath } from 'react-router-dom-v5-compat';

export const navPaths = {
  // Analytics
  analyticsComparisonReports: '/creativeIntelligence/platform/',
  analyticsInsightsLibrary: '/creativeIntelligence/insights-library',
  analyticsCreativeManager: '/creativeIntelligence/creativeManager',
  analyticsCreativeLeaderboard: '/creativeIntelligence/creativeLeaderboard',
  analyticsImpactReport: '/creativeIntelligence/impact',
  analyticsCriteriaPerformance: '/creativeIntelligence/criteria-performance',
  tryAnalytics: '/try/analytics/',
  analyticsCustomCompare: '/creativeIntelligence/customCompare/breakdown',
  analyticsSavedReports: '/creativeIntelligence/savedReports',
  insightCopilotRoute: '/creativeIntelligence/maddie/:copilotType',
  individualCreativeView: '/creativeIntelligence/individual',
  creativeLifecycle: '/creativeIntelligence/creative-lifecycle',
  // Home
  home: '/',
  // Organization
  organization: '/partner',
  organizationAccountGroups: '/user-profile/account-groups',
  organizationAssetLocker: '/partner/asset-locker',
  organizationCreativeGroups: '/user-profile/creative-groups',
  organizationIntegrations: '/integrations/app-store',
  organizationPeople: '/people',
  organizationPeopleFeatureFlag: '/partner/team',
  organizationNotifications: '/partner/notifications',
  organizationSecurity: '/single-sign-on',
  organizationTagGroups: '/user-profile/tag-groups',
  organizationWorkspace: '/workspaces',
  organizationBrandLabels: '/brands',
  organizationAdAccountHealthDashboard: '/integrations/manage',
  organizationSwitcher: '/organizations',
  organizationApiKeys: '/api-key-management',
  organizationDataExports: '/data-exports',
  organizationDataConnectors: '/connectors',
  // Profile
  profile: '/user-profile/personal',
  // Scoring
  scoringChannelFitness:
    '/creativeIntelligence/creative-scoring/platform-fitness-score',
  scoringCriteriaManagement:
    '/creativeIntelligence/creative-scoring/criteria-management',
  scoringCriteriaCreate:
    '/creativeIntelligence/creative-scoring/criteria-create',
  scoringReports: '/creativeIntelligence/creative-scoring/reports',
  scoringScorecardsLanding:
    '/creativeIntelligence/creative-scoring/scorecards-landing',
  scoringScorecardsLandingPreFlight:
    '/creativeIntelligence/creative-scoring/scorecards/pre-flight',
  scoringScorecardsLandingInFlight:
    '/creativeIntelligence/creative-scoring/scorecards/in-flight',
  scoringScorecardDetails:
    '/creativeIntelligence/creative-scoring/scorecard/:scorecardId',
  scoringCreateScorecard:
    '/creativeIntelligence/creative-scoring/submission-report',
  scoringReportsAdoption:
    '/creativeIntelligence/creative-scoring/reports/adoption',
  scoringReportsAdherence:
    '/creativeIntelligence/creative-scoring/reports/adherence',
  scoringImpressionAdherence:
    '/creativeIntelligence/creative-scoring/reports/impression-adherence',
  tryScoring: '/try/scoring/',
  scoringContentAudit:
    '/creativeIntelligence/creative-scoring/brand-score/:batchId',
  scoringIndividualCreativeViewBatch:
    '/creativeIntelligence/creative-scoring/:tab?/batch/:batchId/media/:mediaId',
  scoringIndividualCreativeViewAdAccount:
    '/creativeIntelligence/creative-scoring/:tab?/ad-account/:adAccountId/:batchId/platform/:platform/media/:mediaId',
  scoringIndividualCreativeViewInflight:
    '/creativeIntelligence/creative-scoring/:tab?/workspace/:workspaceId/media/:mediaId/:channel?',
  // Studio
  studioActiveProjects: '/projects/active',
  studioCompletedProjects: '/projects/completed',
  // Studio side nav
  studioActiveProject: '/project/active/',
  studioCompletedProject: '/project/completed/',
  // Studio side nav items
  studioProjectAssets: '/project/:status(active|completed)/:projectId/assets',
  studioProjectBrief: '/project/:status(active|completed)/:projectId/brief',
  studioProjectConcepts:
    '/project/:status(active|completed)/:projectId/concepts',
  studioProjectFinalFiles:
    '/project/:status(active|completed)/:projectId/files',
  studioProjectNotes: '/project/:status(active|completed)/:projectId/notes',
  studioProjectOutputs: '/project/:status(active|completed)/:projectId/outputs',
  studioProjectTeam: '/project/:status(active|completed)/:projectId/team',
  // Studio excluded paths for side nav
  studioProjectNoteEdit:
    '/project/:status(active|completed)/:projectId/note/edit/:noteId',
  studioProjectNoteCreate:
    '/project/:status(active|completed)/:projectId/note/create',
  studioProjectNoteView:
    '/project/:status(active|completed)/:projectId/note/:noteId',
};

export const pathMatchForIcon = {
  studio: [
    navPaths.studioActiveProjects,
    navPaths.studioCompletedProjects,
    navPaths.studioActiveProject,
    navPaths.studioCompletedProject,
  ],
};

export const groupedNavPaths = {
  analytics: [
    navPaths.analyticsComparisonReports,
    navPaths.analyticsInsightsLibrary,
    navPaths.analyticsCreativeManager,
    navPaths.analyticsCreativeLeaderboard,
    navPaths.analyticsCustomCompare,
    navPaths.analyticsSavedReports,
    navPaths.analyticsImpactReport,
    navPaths.analyticsCriteriaPerformance,
    navPaths.insightCopilotRoute,
    navPaths.individualCreativeView,
    navPaths.creativeLifecycle,
  ],
  home: navPaths.home,
  organization: [
    navPaths.organization,
    navPaths.organizationAccountGroups,
    navPaths.organizationApiKeys,
    navPaths.organizationAssetLocker,
    navPaths.organizationCreativeGroups,
    navPaths.organizationDataConnectors,
    navPaths.organizationDataExports,
    navPaths.organizationIntegrations,
    navPaths.organizationPeople,
    navPaths.organizationSecurity,
    navPaths.organizationTagGroups,
    navPaths.organizationWorkspace,
    navPaths.organizationBrandLabels,
    navPaths.organizationAdAccountHealthDashboard,
  ],
  profile: navPaths.profile,
  scoring: [
    navPaths.scoringCriteriaManagement,
    navPaths.scoringCriteriaCreate,
    navPaths.scoringReports,
    navPaths.scoringScorecardsLanding,
    navPaths.scoringScorecardsLandingPreFlight,
    navPaths.scoringScorecardsLandingInFlight,
    navPaths.scoringScorecardDetails,
    navPaths.scoringContentAudit,
    navPaths.scoringCreateScorecard,
    navPaths.scoringReportsAdoption,
    navPaths.scoringReportsAdherence,
    navPaths.scoringImpressionAdherence,
    navPaths.scoringIndividualCreativeViewBatch,
    navPaths.scoringIndividualCreativeViewAdAccount,
    navPaths.scoringIndividualCreativeViewInflight,
  ],
  studio: [navPaths.studioActiveProjects, navPaths.studioCompletedProjects],
  studioProjectSideNav: [
    navPaths.studioActiveProject,
    navPaths.studioCompletedProject,
  ],
  studioExcludedPaths: [
    navPaths.studioProjectNoteEdit,
    navPaths.studioProjectNoteCreate,
    navPaths.studioProjectNoteView,
  ],
};

export const getCurrentPageGroup = (pathname) => {
  for (const [group, paths] of Object.entries(groupedNavPaths)) {
    if (Array.isArray(paths)) {
      for (const path of paths) {
        const isExactMatch = path === navPaths.home;
        if (matchPath({ path, end: isExactMatch }, pathname)) {
          return group;
        }
      }
    } else {
      const isExactMatch = paths === navPaths.home;
      if (matchPath({ path: paths, end: isExactMatch }, pathname)) {
        return group;
      }
    }
  }
  return null;
};
