import React, { useEffect, useState } from 'react';
import Popover from '@mui/material/Popover';
import { MenuList, MenuItem, useTheme, Typography } from '@mui/material';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { vmSessionManager } from '../../../../vidmobConnectionManager';
import WebSocketService from '../../../../apiServices/WebSocketService';
import resetRedux from '../../../../redux/actions/reset.actions';
import { useIntl } from 'react-intl';
import { getCurrentUser } from '../../../../redux/selectors/user.selectors';
import vidMobConfig from '../../../../../configs/vidMobConfigConstants';
import Avatar from '@mui/material/Avatar';
import Tooltip from '@mui/material/Tooltip';
import './AvatarMenuPopover.scss';
import BffSsoService from '../../../../apiServices/BffSsoService';

const AvatarMenuPopover = ({ open, anchorEl, onClose }) => {
  const theme = useTheme();
  const history = useHistory();
  const dispatch = useDispatch();
  const intl = useIntl();
  const currentUser = useSelector(getCurrentUser);
  const userName =
    (currentUser?.firstName || '') + ' ' + (currentUser?.lastName || '');
  const userEmail = currentUser?.email || '';
  const [logoutUrlRedirect, setLogoutUrlRedirect] = useState('');

  const getLogoutUrl = (data) => {
    const {
      cognitoHostedLogin: { clientId, responseType, scope, redirectUri },
      ssoOptions,
    } = data;
    const vidmobConfig = vidMobConfig[process.env.VIDMOB_ENV || 'local'];
    const logoutUrl = vidmobConfig.baseUrl + 'login';

    if (ssoOptions.length === 0) {
      return logoutUrl;
    }

    const url = vidmobConfig.cognitoHostedUI;
    return `${url}/logout?client_id=${clientId}&scope=${scope}&response_type=${responseType}&logout_uri=${logoutUrl}&redirect_uri=${redirectUri}`;
  };

  useEffect(() => {
    BffSsoService.getLoginOptions(userEmail).then((data) => {
      const logoutUrl = getLogoutUrl(data);
      setLogoutUrlRedirect(logoutUrl);
    });
  }, []);

  const handleProfileSettings = () => {
    history.push('/user-profile/personal');
    onClose();
  };

  const logOutUser = () => {
    if (vmSessionManager.sessionExists()) {
      WebSocketService.disconnect();
      vmSessionManager
        .logoutPromise()
        .then((responseData) => {
          console.log(
            'Logout successfull.' + responseData.status
              ? responseData.status
              : '',
          );
        })
        .catch((error) => {
          console.log('Logout failed: ' + error);
        })
        .finally(() => {
          sessionStorage.clear();
          dispatch(resetRedux());
          onClose();
          window.location.href = logoutUrlRedirect;
        });
    }
  };

  const menuItemStyling = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '200px',
    height: '36px',
    fontSize: '14px',
    padding: '8px',
    borderRadius: '6px',
  };

  const avatarStyle = {
    backgroundColor: 'grey',
  };

  const userInfoTextStyle = {
    overflow: 'hidden',
    whiteSpace: 'pre-wrap',
    textOverflow: 'ellipsis',
    maxWidth: '184px',
  };

  const getActionMenuItems = () => (
    <MenuList sx={{ margin: '0 8px', maxWidth: 280 }}>
      <div className="user-info">
        <Avatar alt={userName} src={currentUser?.photo} sx={avatarStyle} />
        <div className="user-details-column">
          <div className="user-name">
            <Tooltip
              title={userName}
              sx={{ backgroundColor: theme.palette.grey }}
            >
              <Typography variant="body2" sx={userInfoTextStyle}>
                {userName}
              </Typography>
            </Tooltip>
          </div>
          <div className="user-email">
            <Tooltip
              title={currentUser?.email}
              sx={{ backgroundColor: theme.palette.grey }}
            >
              <Typography variant="caption" sx={userInfoTextStyle}>
                {currentUser?.email}
              </Typography>
            </Tooltip>
          </div>
        </div>
      </div>
      <MenuItem sx={menuItemStyling} onClick={handleProfileSettings}>
        <Typography variant="caption">
          {intl.messages['topNav.avatarMenu.profileSettings']}
        </Typography>
      </MenuItem>
      <MenuItem sx={menuItemStyling} onClick={logOutUser}>
        <Typography variant="caption" sx={userInfoTextStyle}>
          {intl.messages['topNav.avatarMenu.logout']}
        </Typography>
      </MenuItem>
    </MenuList>
  );

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      sx={{
        transform: 'translateY(6px)',
        '& .MuiPaper-root': {
          overflow: 'hidden',
        },
      }}
      PaperProps={{
        elevation: 2,
        sx: {
          border: `1px solid ${theme.palette.divider}`,
          boxShadow: '0px 4px 8px 0px rgba(15, 15, 15, 0.16)',
        },
      }}
    >
      {getActionMenuItems()}
    </Popover>
  );
};

export default AvatarMenuPopover;
