import siteMap, { routeParams } from '../../../../routing/siteMap';
import appAnalyticsIcon from '../../../../assets/icons/apps/app-analytics.png';
import appProductionIcon from '../../../../assets/icons/apps/app-production.png';
import appScoringIcon from '../../../../assets/icons/apps/app-scoring.png';
import IntegrationsIcon from '../../../../assets/icons/home-integration-link-icon.svg';
import ReportsIcon from '../../../../assets/icons/home-reports-link-icon.svg';
import ProjectsIcon from '../../../../assets/icons/home-projects-link-icon.svg';
import MembersIcon from '../../../../assets/icons/home-members-link-icon.svg';
import AssetsLockerIcon from '../../../../assets/icons/home-assets-locker-link-icon.svg';
import CampaignIcon from '../../../../assets/icons/home-campaigns-link-icon.svg';
import adAccountArticleImage from '../../../../assets/icons/home-articleCard-ad-account.jpg';
import accessAnalyticsArticleImage from '../../../../assets/icons/home-articleCard-access-analytics.jpg';
import inviteTeamArticleImage from '../../../../assets/icons/home-articleCard-invite-team.jpg';
import projectKickOffArticleImage from '../../../../assets/icons/home-articleCard-project-kickoff.jpg';
import creativeDataArticleImage from '../../../../assets/icons/home-articleCard-creative-data.jpg';
import troubleshootArticleImage from '../../../../assets/icons/home-articleCard-troubleshoot.jpg';
import { parseURL } from '../../../../utils/urlUtils';

export const ARTICLE_LIST = [
  {
    articleName: 'home.section.articles.adAccounts.title',
    articleImage: adAccountArticleImage,
    articleUrl:
      'https://help.vidmob.com/en/articles/3492239-how-do-i-connect-my-ad-accounts-to-the-vidmob-platform',
    articleDescription: 'home.section.articles.adAccounts.description',
    articleActionText: 'home.section.articles.helpCenter.actionText',
    articleType: 'Media',
    permissions: ['ALL_ACCESS'],
  },
  {
    articleName: 'home.section.articles.projectKickOff.title',
    articleImage: projectKickOffArticleImage,
    articleUrl:
      'https://help.vidmob.com/en/articles/5617290-how-to-kick-off-your-vidmob-project',
    articleDescription: 'home.section.articles.projectKickOff.description',
    articleActionText: 'home.section.articles.helpCenter.actionText',
    articleType: 'Media',
    permissions: [
      'CREATIVE_INTELLIGENCE_ONLY',
      'BASIC_ACCESS',
      'BRAND_GOVERNANCE_ONLY',
    ],
  },
  {
    articleName: 'home.section.articles.analytics.title',
    articleImage: accessAnalyticsArticleImage,
    articleUrl:
      'https://help.vidmob.com/en/articles/5575359-what-are-the-roles-and-permissions-needed-to-connect-to-creative-analytics',
    articleDescription: 'home.section.articles.analytics.description',
    articleActionText: 'home.section.articles.helpCenter.actionText',
    articleType: 'Media',
    permissions: ['ALL_ACCESS', 'CREATIVE_INTELLIGENCE_ONLY'],
  },
  {
    articleName: 'home.section.articles.teamInvite.title',
    articleImage: inviteTeamArticleImage,
    articleUrl:
      'https://help.vidmob.com/en/articles/8927827-how-do-i-invite-a-team-member-to-my-vidmob-workspace',
    articleDescription: 'home.section.articles.teamInvite.description',
    articleActionText: 'home.section.articles.helpCenter.actionText',
    articleType: 'Media',
    permissions: ['ALL_ACCESS', 'BASIC_ACCESS', 'BRAND_GOVERNANCE_ONLY'],
  },
  {
    articleName: 'home.section.articles.creativeData.title',
    articleImage: creativeDataArticleImage,
    articleUrl:
      'https://help.vidmob.com/en/articles/5575448-what-ad-types-does-vidmob-creative-analytics-support',
    articleDescription: 'home.section.articles.creativeData.description',
    articleActionText: 'home.section.articles.helpCenter.actionText',
    articleType: 'Media',
    permissions: ['CREATIVE_INTELLIGENCE_ONLY'],
  },
  {
    articleName: 'home.section.articles.studioTroubleshooting.title',
    articleImage: troubleshootArticleImage,
    articleUrl:
      'https://help.vidmob.com/en/articles/6887438-creative-studio-frequently-asked-questions',
    articleDescription:
      'home.section.articles.studioTroubleshooting.description',
    articleActionText: 'home.section.articles.helpCenter.actionText',
    articleType: 'Media',
    permissions: ['BASIC_ACCESS'],
  },
  {
    articleName: 'home.section.articles.scoringTroubleshooting.title',
    articleImage: troubleshootArticleImage,
    articleUrl:
      'https://help.vidmob.com/en/articles/6314118-creative-scoring-frequently-asked-questions',
    articleDescription:
      'home.section.articles.scoringTroubleshooting.description',
    articleActionText: 'home.section.articles.helpCenter.actionText',
    articleType: 'Media',
    permissions: ['BRAND_GOVERNANCE_ONLY'],
  },
  {
    articleName: 'home.section.articles.helpCenter.title',
    articleDescription: 'home.section.articles.helpCenter.description',
    articleActionText: 'home.section.articles.helpCenter.action.option',
    articleUrl: 'https://help.vidmob.com/en/',
    articleType: 'NoMedia',
    permissions: [
      'ALL_ACCESS',
      'BASIC_ACCESS',
      'BRAND_GOVERNANCE_ONLY',
      'CREATIVE_INTELLIGENCE_ONLY',
    ],
  },
];

export const LINK_LIST = [
  {
    title: 'home.section.quickLinks.integrations.title',
    subtitle: 'home.section.quickLinks.integrations.subtitle',
    icon: IntegrationsIcon,
    url: parseURL(siteMap.organizationIntegrations, {
      tab: routeParams.tabs.integrations.integrations,
    }),
    permissions: [
      'ALL_ACCESS',
      'BASIC_ACCESS',
      'BRAND_GOVERNANCE_ONLY',
      'CREATIVE_INTELLIGENCE_ONLY',
    ],
  },
  {
    title: 'home.section.quickLinks.preFlight.title',
    subtitle: 'home.section.quickLinks.reports.subtitle',
    icon: ReportsIcon,
    url: siteMap.creativeIntelligenceReports,
    permissions: ['ALL_ACCESS', 'BRAND_GOVERNANCE_ONLY'],
  },
  {
    title: 'home.section.quickLinks.createPreFlightCheck.title',
    subtitle: 'home.section.quickLinks.reports.subtitle',
    icon: ReportsIcon,
    url: siteMap.creativeIntelligenceCreativeScoringCreateScorecard,
    permissions: ['ALL_ACCESS', 'BRAND_GOVERNANCE_ONLY'],
  },
  {
    title: 'home.section.quickLinks.people.title',
    subtitle: 'home.section.quickLinks.people.subtitle',
    icon: MembersIcon,
    url: parseURL(siteMap.people),
    permissions: [
      'BASIC_ACCESS',
      'BRAND_GOVERNANCE_ONLY',
      'CREATIVE_INTELLIGENCE_ONLY',
    ],
  },
  {
    title: 'home.section.quickLinks.projects.title',
    subtitle: 'home.section.quickLinks.projects.subtitle',
    icon: ProjectsIcon,
    url: siteMap.activeProjects,
    permissions: [
      'ALL_ACCESS',
      'BASIC_ACCESS',
      'BRAND_GOVERNANCE_ONLY',
      'CREATIVE_INTELLIGENCE_ONLY',
    ],
  },
  {
    title: 'home.section.quickLinks.campaigns.title',
    subtitle: 'home.section.quickLinks.campaigns.subtitle',
    icon: CampaignIcon,
    url: siteMap.creativeIntelligenceCreativeManager,
    permissions: ['ALL_ACCESS', 'CREATIVE_INTELLIGENCE_ONLY'],
  },
  {
    title: 'home.section.quickLinks.assetLocker.title',
    subtitle: 'home.section.quickLinks.assetLocker.subtitle',
    icon: AssetsLockerIcon,
    url: parseURL(siteMap.partner, {
      tab: routeParams.tabs.partner['asset-locker'],
    }),
    permissions: ['BASIC_ACCESS'],
  },
];

export const APP_LIST = [
  {
    identifier: 'app-scoring',
    title: 'home.section.apps.scoring.title',
    path: parseURL(siteMap.creativeIntelligenceRollupReportsLanding),
    icon: appScoringIcon,
  },
  {
    identifier: 'app-analytics',
    title: 'home.section.apps.analytics.title',
    path: parseURL(siteMap.creativeIntelligenceSavedReports),
    icon: appAnalyticsIcon,
  },
  {
    identifier: 'app-production',
    title: 'home.section.apps.studio.title',
    path: parseURL(siteMap.activeProjects),
    icon: appProductionIcon,
  },
  {
    identifier: 'divider',
    title: 'divider',
    path: 'divider',
    icon: 'divider',
  },
  {
    identifier: 'app-admin',
    title: 'home.section.apps.organization.title',
    path: parseURL(siteMap.workspaceList),
    icon: 'partner',
  },
  {
    identifier: 'app-profile',
    title: 'home.section.apps.profile.title',
    path: parseURL(siteMap.userProfile, {
      tab: routeParams.tabs.profile.personal,
    }),
    icon: 'currentUser',
  },
];
