import { screen, render } from '@testing-library/react';
import React from 'react';
import {
  getCurrentPartner,
  getCurrentPartnerFeature,
} from '../../../redux/selectors/partner.selectors';
import Home from './Home';
import { GLOBALS } from '../../../constants';

jest.mock('../../../redux/selectors/user.selectors', () => ({
  getCurrentUser: jest.fn(),
}));
jest.mock('../../../redux/selectors/partner.selectors', () => ({
  getCurrentPartnerFeature: jest.fn(),
  getCurrentPartner: jest.fn(),
}));
jest.mock('react-redux', () => {
  return {
    useSelector: jest.fn((selector) => selector()),
    useDispatch: jest.fn(() => ({})),
  };
});

describe('home.jsx', () => {
  it('Should render basic quick link cards', () => {
    getCurrentPartnerFeature.mockReturnValue(false);
    getCurrentPartner.mockReturnValue({
      id: 1,
      industry: 'test',
      color: '#fc6574',
      name: 'just a test',
    });
    render(<Home />);
    expect(screen.queryByText('Integrations')).toBeTruthy();
    expect(screen.queryByText('People')).toBeTruthy();
    expect(screen.queryByText('Projects')).toBeTruthy();
    expect(screen.queryByText('Reports')).toBeFalsy();
    expect(screen.queryByText('Campaigns')).toBeFalsy();
    expect(screen.queryByText('Asset Locker')).toBeTruthy();
  });

  it('Should render all access quick link cards', () => {
    getCurrentPartnerFeature.mockReturnValue(true);
    getCurrentPartner.mockReturnValue({
      id: 1,
      industry: 'test',
      color: '#fc6574',
      name: 'just a test',
    });
    render(<Home />);
    expect(screen.queryByText('Integrations')).toBeTruthy();
    expect(screen.queryByText('Create Pre-flight Check')).toBeTruthy();
    expect(screen.queryByText('Projects')).toBeTruthy();
    expect(screen.queryByText('Campaigns')).toBeTruthy();
    expect(screen.queryByText('Members')).toBeFalsy();
    expect(screen.queryByText('Asset Locker')).toBeFalsy();
  });

  it('Should render analytics only quick link cards', () => {
    getCurrentPartnerFeature.mockImplementation((state, feature) => {
      if (feature === GLOBALS.PARTNER_SPECIFIC_FEATURES.CREATIVE_INTELLIGENCE) {
        return true;
      }

      return false;
    });
    getCurrentPartner.mockReturnValue({
      id: 1,
      industry: 'test',
      color: '#fc6574',
      name: 'just a test',
    });
    render(<Home />);
    expect(screen.queryByText('Integrations')).toBeTruthy();
    expect(screen.queryByText('People')).toBeTruthy();
    expect(screen.queryByText('Projects')).toBeTruthy();
    expect(screen.queryByText('Campaigns')).toBeTruthy();
    expect(screen.queryByText('Reports')).toBeFalsy();
    expect(screen.queryByText('Asset Locker')).toBeFalsy();
  });

  it('Should render a try chip button when the user does not have access', () => {
    getCurrentPartnerFeature.mockReturnValue(false);
    getCurrentPartner.mockReturnValue({
      id: 1,
      industry: 'test',
      color: '#fc6574',
      name: 'just a test',
    });
    render(<Home />);
    const tryChip = screen.getAllByText('TRY');
    expect(tryChip.length).toBeGreaterThan(0);
  });
});
