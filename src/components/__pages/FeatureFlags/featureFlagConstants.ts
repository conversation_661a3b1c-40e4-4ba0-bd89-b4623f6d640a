import { InitiativeType } from './types';

/*
 All Splits which are the "new" feature flags should go here.
 At the moment we can only have one workspace on Split.io so please give splits names which indicate what product they belong to
 so we can tell them apart on the Split dashboard.

 When adding or modifying a flag, please include the date, and the ticket number for the task to remove the flag.
 PLEASE use easy-to-identify names, preferably matching the epic! Include the name of the flag in related JIRA tickets.
 */
export const J_AND_J_DAM_SPLIT = 'acs-j-and-j-dam-integration'; // (TAF) This is not a regular Split so no removal timeline. Last modified Nov 18, 2020
export const INTERCOM_TOUR_SPLIT = 'acs-intercom-tour'; // (TARTANS) Added May 17, 2021 for intercom project chat tours A/B testing

/* See https://vidmob.atlassian.net/wiki/spaces/BK/pages/838664193/How+to+use+a+feature+flag */

// INSTRUCTIONS
// - All feature flags in use should be listed here.
// - When adding a new flag:
//    1. Add it under the Jira epic it corresponds to (add Jira epic details if necessary) and default it to false.
//    2. Add Jira ticket number and date under "info".
//    3. In Jira, add the name of the flag to the epic under the "Feature Flag" label.
// - When a feature is ready for release:
//    1. Change the flag to true.
//    2. Update "info" field with Jira ticket number and date.
//    3. Update the "live" field in the InitiativeType object to true if all flags in it are true.
// - When an epic gets deprioritized, change "prioritized" to false in the InitiativeType object.

// PLEASE use easy-to-identify names, preferably matching the epic!

export const FEATURE_FLAGS_PER_INITIATIVE: Record<string, InitiativeType> = {
  'VID-5514': {
    name: 'Data Exports - Creative Elements and Analytics',
    url: 'https://vidmob.atlassian.net/browse/VID-5514',
    prioritized: true,
    live: true,
    flags: {
      isExportElementsAndAnalyticsEnabled: {
        info: 'Data Exports - Creative Elements and Analytics: Added in VID-13183 on May 22, 2025',
        enabled: true,
      },
    },
  },
  'VID-13301': {
    name: 'Executive Dashboard',
    url: 'https://vidmob.atlassian.net/browse/VID-13301',
    prioritized: true,
    live: false,
    flags: {
      isDashboardEnabled: {
        info: 'Added in VID-13301 on May 15, 2025',
        enabled: false,
      },
    },
  },
  'VID-11457': {
    name: 'Update Create Insight Flow',
    url: 'https://vidmob.atlassian.net/browse/VID-11457',
    prioritized: true,
    live: true,
    flags: {
      isInsightCreateModalEnabled: {
        info: 'Analytics: Added in VID-11474 on March 13, 2025',
        enabled: true,
      },
    },
  },
  'VID-8742': {
    name: 'Creative Aperture',
    url: 'https://vidmob.atlassian.net/browse/VID-8742',
    prioritized: true,
    live: false,
    flags: {
      isMessagingApertureEnabled: {
        info: 'Creative Aperture: Added in VID-9515 on January 21, 2025',
        enabled: false,
      },
      isCreativeApertureConfidenceInputEnabled: {
        info: 'Creative Aperture: Added in VID-9931 on Fab 07, 2025',
        enabled: false,
      },
      isCreativeApertureAPIKeysEnabled: {
        info: 'Creative Aperture: Added in VID-10301 on Fab 14, 2025',
        enabled: true,
      },
    },
  },
  'VID-13455': {
    name: 'Should Include All Spend Kpis',
    url: 'https://vidmob.atlassian.net/browse/VID-13455',
    prioritized: false,
    live: true,
    flags: {
      isIncludeAllSpendKpisEnabled: {
        info: 'Should Include All Spend Kpis: Added in VID-13455',
        enabled: true,
      },
    },
  },
  'VID-9566': {
    name: 'React Router V6 Update',
    url: 'https://vidmob.atlassian.net/browse/VID-9566',
    prioritized: true,
    live: false,
    flags: {
      isNewRoutingEnabled: {
        info: 'Added in VID-9566 on January 9, 2025',
        enabled: false,
      },
    },
  },
  'VID-9061': {
    name: 'Filters UX Updates',
    url: 'https://vidmob.atlassian.net/browse/VID-9061',
    prioritized: true,
    live: false,
    flags: {
      isSharedFilterUXAcrossAnalyticsAndScoringEnabled: {
        info: 'Analytics: added in VID-9150 on December 19, 2024',
        enabled: false,
      },
    },
  },
  'VID-7106': {
    name: 'Real eyes View',
    url: 'https://vidmob.atlassian.net/browse/VID-7106',
    prioritized: false,
    live: true,
    flags: {
      isRealEyesViewEnabled: {
        info: 'Real eyes View: added in VID-7106 on October 10, 2024',
        enabled: true,
      },
    },
  },
  'VID-3733': {
    name: 'Criteria Groups',
    url: 'https://vidmob.atlassian.net/browse/VID-3733',
    prioritized: true,
    live: true,
    flags: {
      isCriteriaFiltersEnabled: {
        info: 'Scoring: added in VID-6776 on October 17, 2024',
        enabled: true,
      },
      isCriteriaGroupsEnabled: {
        info: 'Scoring: added in VID-6726 on September 23, 2024. Defaulted to true in VID-11984 on June 26, 2025.',
        enabled: true,
      },
    },
  },
  'VID-6485': {
    name: "L'Oreal POC: Creative Lifecycle",
    url: 'https://vidmob.atlassian.net/browse/VID-6485',
    prioritized: true,
    live: false,
    flags: {
      isCreativeLifecyclePOCEnabled: {
        info: 'Scoring: added in VID-6671 on Sept 17, 2024',
        enabled: false,
      },
    },
  },
  'VID-6669': {
    name: "L'Oreal POC: Creative Lifecycle Mock Data",
    url: 'https://vidmob.atlassian.net/browse/VID-6669',
    prioritized: true,
    live: false,
    flags: {
      isCreativeLifecyclePOCMockDataEnabled: {
        info: 'Scoring: added in VID-6787 on Sept 19, 2024',
        enabled: false,
        configuration: {
          isCoke: false,
        },
      },
    },
  },
  'VID-3875': {
    name: 'Insights Co-Pilot (V1/BETA)',
    url: 'https://vidmob.atlassian.net/browse/VID-3875',
    prioritized: true,
    live: false,
    flags: {
      isCopilotEnabled: {
        info: 'Analytics: added Copilot capability on February 6, 2024',
        enabled: false,
        configuration: {
          Model: [
            'OPEN_AI_4_1',
            'VERTEX_AI_GEMINI_2_5_PRO_PREVIEW',
            'VERTEX_AI_GEMINI_2_5_FLASH_PREVIEW',
            'VERTEX_AI_GEMINI_2_0_FLASH',
            'VERTEX_AI_GEMINI_2_0_FLASH_LITE',
            'VERTEX_AI_CLAUDE_3_7_SONNET',
          ],
        },
      },
    },
  },
  'VID-8747': {
    name: 'Mongo DB for Copilot',
    url: 'https://vidmob.atlassian.net/browse/VID-8747',
    prioritized: true,
    live: false,
    flags: {
      isMongoForCopilotEnabled: {
        info: 'Analytics: added Mongo flag on Dec 4, 2024',
        enabled: false,
      },
    },
  },
  'VID-5234': {
    name: 'Platform Data Exports - Scheduled Exports and Shared Locations',
    url: 'https://vidmob.atlassian.net/browse/VID-5234',
    prioritized: true,
    live: false,
    flags: {
      isScheduledDataExportsEnabled: {
        info: 'Platform: added in VID-6289 on September 4, 2024',
        enabled: false,
      },
    },
  },
  'PT-273': {
    name: 'Meta Expansion -- Custom Audience Data (CA)',
    url: 'https://vidmob.atlassian.net/browse/PT-273',
    prioritized: false,
    live: false,
    flags: {
      isMetaCustomAudienceEnabled: {
        info: 'Platform: added in AGL-14070 on July 5, 2023',
        enabled: false,
      },
    },
  },
  'VID-1281': {
    name: 'WPP & Coca-Cola Feedback & Enhancements',
    url: 'https://vidmob.atlassian.net/browse/VID-1281',
    prioritized: false,
    live: true,
    flags: {
      isFilterRedundantLogosEnabled: {
        info: 'Analytics: added in AN-1231 on February 14, 2024',
        enabled: true,
        configuration: {
          type: 'BRAND',
          percentage: 10,
        },
      },
    },
  },
  'VID-2638': {
    name: 'Diversity Report v2',
    url: 'https://vidmob.atlassian.net/browse/VID-2638',
    prioritized: false,
    live: false,
    flags: {
      isDiversityReportEnabled: {
        info: 'Scoring: added in VID-3955 on June 6, 2024',
        enabled: false,
      },
    },
  },
  'VID-3799': {
    name: 'Full Filters on the leaderboard',
    url: 'https://vidmob.atlassian.net/browse/VID-3799',
    prioritized: false,
    live: false,
    flags: {
      isLeaderboardsAdvancedFiltersEnabled: {
        info: 'Analytics: added in VID-3816 on June 10, 2024',
        enabled: false,
      },
    },
  },
  'VID-793': {
    name: 'Ability to download report PDF (Element Impact, Media Impact, Leaderboard, and Campaign, and Creative comparison)',
    url: 'https://vidmob.atlassian.net/browse/VID-793',
    prioritized: false,
    live: false,
    flags: {
      isAnalyticsReportsPDFExportEnabled: {
        info: 'Analytics: added in VID-4005 on June 10, 2024',
        enabled: false,
      },
    },
  },
  'VID-7763': {
    name: 'Export copilot insights to slides',
    url: 'https://vidmob.atlassian.net/browse/VID-7763',
    prioritized: true,
    live: false,
    flags: {
      isExportInsightsToSlidesEnabled: {
        info: 'Analytics: added in VID-7763 on October 28, 2024',
        enabled: false,
      },
    },
  },
  'VID-8380': {
    name: 'Criteria performance include standard criteria',
    url: 'https://vidmob.atlassian.net/browse/VID-8380',
    prioritized: true,
    live: false,
    flags: {
      isCriteriaPerformanceIncludeStandardCriteria: {
        info: 'Criteria performance include standard criteria: added in VID-8380 on November 15, 2024',
        enabled: false,
      },
    },
  },
  'VID-8948': {
    name: 'Brief Updates',
    url: 'https://vidmob.atlassian.net/browse/VID-8539',
    prioritized: true,
    live: false,
    flags: {
      isBriefOutputAutomationEnabled: {
        info: 'Studio: added in VID-8948 on December 12, 2024',
        enabled: false,
      },
    },
  },
  'VID-7866': {
    name: 'Criteria Groups - Workspace Control, Filtering, Display in Reports and Scorecards, and API Support',
    url: 'https://vidmob.atlassian.net/browse/VID-7866',
    prioritized: true,
    live: true,
    flags: {
      isCriteriaGroupsInReportsEnabled: {
        info: 'Scoring: Added in VID-10958 on Mar 4, 2025. Defaulted to true in VID-11984 on June 26, 2025.',
        enabled: true,
      },
      isCriteriaGroupsInPreFlightCreationEnabled: {
        info: 'Scoring: Added in VID-12869 on May 7, 2025. Defaulted to true in VID-11984 on June 26, 2025.',
        enabled: true,
      },
    },
  },
  'VID-10042': {
    name: 'Analytics sorting',
    url: 'https://vidmob.atlassian.net/browse/VID-10042',
    prioritized: true,
    live: false,
    flags: {
      isAnalyticsSortingEnabled: {
        info: 'Analytics: added in VID-10042 on Mar 11, 2025',
        enabled: false,
      },
    },
  },
};
