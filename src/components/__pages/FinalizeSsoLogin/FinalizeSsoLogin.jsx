import React, { useEffect } from 'react';
import queryString from 'query-string';
import { vmSessionManager } from '../../../vidmobConnectionManager';
import history from '../../../routing/history';
import WebSocketService from '../../../apiServices/WebSocketService';
import { trackSsoLogin } from '../../../featureServices/TrackingService/trackingService';
import { string, shape } from 'prop-types';
import { StorageTypes } from 'vidmob-client-session-manager/lib/session';
import VidMobInitialLoader from '../../../vcl/ui/Loading/InitialLoader';
import vmErrorLog from '../../../utils/vmErrorLog';
import siteMap from '../../../routing/siteMap';
import { fetchUserOrganizationDetails } from '../../../userManagement/redux/actions/organization.actions';
import { useDispatch, useSelector } from 'react-redux';
import {
  getUserOrganizationPaginationTotalSize,
  getUserOrganizationsAPIRequestStatus,
} from '../../../userManagement/redux/selectors/organization.selectors';
import { GLOBALS } from '../../../constants';
import organizationSlice from '../../../userManagement/redux/slices/organization.slice';
import {
  getReferrerCookie,
  clearReferrerCookie,
} from '../Login/referrerLoginUtils';

const FinalizeSsoLogin = (props) => {
  const dispatch = useDispatch();
  const orgTotalCount = useSelector((state) =>
    getUserOrganizationPaginationTotalSize(state),
  );
  const userOrganizationsAPIStatus = useSelector((state) =>
    getUserOrganizationsAPIRequestStatus(state),
  );
  const { REDUX_LOADING_STATUS } = GLOBALS;
  const { SUCCESS, FAILED } = REDUX_LOADING_STATUS;
  const storedReferrer = getReferrerCookie();

  // Redirects user to invitation or to org details
  const handlePostAuthentication = () => {
    if (window.localStorage.branch_session_first) {
      // There is an invitation waiting for the user, so redirect to the deep link page through a branch.io link
      redirectToProcessInvite().catch((err) => {
        vmErrorLog(
          err,
          'FinalizeSsoLogin redirectToProcessInvite',
          'Processing invite failed',
        );
      });
    } else {
      dispatch(fetchUserOrganizationDetails());
    }
  };

  const handleSsoLoginTracking = (accessToken) => {
    const sanitizedCurrentUrl = getSanitizedCurrentUrl();
    const emailAddress = getUserEmailFromAccessToken(accessToken);

    trackSsoLogin(sanitizedCurrentUrl, emailAddress);
  };

  useEffect(() => {
    // Extract the URL query parameters
    const queryParams = queryString.parse(props.location.search);

    const { error } = queryParams;

    if (!props.location.search) {
      return;
    }

    // Establish a web sockets connection
    initializeWebSocketService();

    if (error) {
      // Redirect to login page
      history.push('/login', { errorMessage: error.message ?? 'error' });
    } else {
      const { code } = queryParams; // Authentication code to be exchanged by sesison information.

      vmSessionManager
        .ssoSession({
          code,
          store: StorageTypes.CookieStorage,
        })
        .then((response) => {
          /**
           * accessToken - Access token from successful Cognito SSO login
           */
          const { accessToken } = response.data.result;

          handleSsoLoginTracking(accessToken);

          handlePostAuthentication();
        })
        .catch((error) => {
          history.push('/login', { errorMessage: error.message ?? 'error' });
        });
    }
  }, [props.location.search]);

  useEffect(() => {
    if (userOrganizationsAPIStatus === FAILED) {
      history.push(siteMap.home);
    }

    if (userOrganizationsAPIStatus === SUCCESS) {
      if (orgTotalCount > 1) {
        dispatch(organizationSlice.actions.reset());
        dispatch(
          organizationSlice.actions.setDoesOrgSwitcherHaveCancelButton(false),
        );
        history.push(siteMap.organizationSwitcher, {
          referrer: storedReferrer,
        });
        clearReferrerCookie();
      }

      if (orgTotalCount === 1) {
        const dest =
          storedReferrer && storedReferrer !== siteMap.home
            ? storedReferrer
            : siteMap.home;
        history.push(dest);
        clearReferrerCookie();
      }
    }
  }, [userOrganizationsAPIStatus, orgTotalCount]);

  return <VidMobInitialLoader />;
};

FinalizeSsoLogin.propTypes = {
  location: shape({
    search: string,
  }).isRequired,
};

export default FinalizeSsoLogin;

const base64UrlDecode = (str) => {
  // eslint-disable-next-line no-undef
  return decodeURIComponent(atob(str.replace(/-/g, '+').replace(/_/g, '/')));
};

/**
 * Extract the user's email address from the access token JWT.
 * Return null if the access token is falsy or invalid.
 *
 * @param accessToken
 */
export const getUserEmailFromAccessToken = (accessToken) => {
  if (!accessToken) {
    console.error('No access token passed to get user email address');
    return null; // no access token passed
  }

  try {
    const jwtSections = accessToken.split('.');

    if (jwtSections.length !== 3) {
      console.error(
        'Bad SSO access token JWT, should have 3 sections but found %s',
        jwtSections.length,
      );
      return null; // not a valid JWT
    }

    // Decode the JWT payload, convert it to a JavaScript object, and return the 'email' property value
    const payload = JSON.parse(base64UrlDecode(jwtSections[1]));
    return payload.email;
  } catch (error) {
    console.error('Error parsing SSO access token for user email', error);
    return null; // email not extracted
  }
};

/**
 * Sanitize the SSO finalize URL for log tracking in the format: <BASE-URL>?code=abcde...vwxyz or <BASE-URL>?sessionId=abcde...vwxyz&accessToken=abcde...vwxyz
 *
 * @returns {string}
 */
export const getSanitizedCurrentUrl = () => {
  let sanitizedCurrentUrl = '';

  try {
    const currentUrl = window?.location?.href || '';

    if (currentUrl && currentUrl.length > 0) {
      const parts = currentUrl.split('?', 2); // split the URL at the first '?' character
      sanitizedCurrentUrl += parts[0];

      if (parts.length > 1) {
        const parameters = JSON.parse(
          '{"' +
            decodeURI(parts[1])
              .replace(/"/g, '\\"')
              .replace(/&/g, '","')
              .replace(/=/g, '":"') +
            '"}',
        );
        const { sessionId } = parameters;
        const { accessToken } = parameters;
        const { code } = parameters;

        if (sessionId && sessionId.length > 10) {
          sanitizedCurrentUrl +=
            '?sessionId=' +
            sessionId.substr(0, 5) +
            '...' +
            sessionId.substr(sessionId.length - 5);
        }

        if (accessToken && accessToken.length > 10) {
          sanitizedCurrentUrl +=
            '&accessToken=' +
            accessToken.substr(0, 5) +
            '...' +
            accessToken.substr(accessToken.length - 5);
        }

        if (code && code.length > 10) {
          sanitizedCurrentUrl +=
            '?code=' + code.substr(0, 5) + '...' + code.substr(code.length - 5);
        }
      }
    }
  } catch (err) {
    console.error('Error sanitizing SSO login URL', err);
  }

  return sanitizedCurrentUrl;
};

const initializeWebSocketService = () => {
  WebSocketService.connect(true);
  if (WebSocketService.isLoginAvailable()) {
    WebSocketService.login();
  }
};

async function redirectToProcessInvite() {
  // Note: There is a timing problem.  The deep link page is not ready to be re-loaded immediately after the
  //       user logs in.  So, we wait one second before redirecting there to avoid an error.
  await new Promise((resolve) => {
    // eslint-disable-next-line no-undef
    setTimeout(() => {
      window.location.href = getBranchLink();
      resolve();
    }, 1000);
  });
}

/**
 * Reconstitute the branch.io link from the local storage information
 * @returns {string}
 */
export const getBranchLink = () => {
  const branchInfo = window.localStorage.branch_session_first;
  const baseUrl = window.location.origin;
  if (!branchInfo) {
    throw new Error('Branch info not present in storage');
  }

  const linkData = JSON.parse(branchInfo).data;
  if (!linkData) {
    throw new Error('Key data not present in branch info');
  }

  const link = JSON.parse(linkData)['~referring_link'];
  if (!link) {
    throw new Error('Link not present in branch info');
  }

  let result;

  if (link.includes('?')) {
    result = `${link}&$fallback_url=${baseUrl}/deeplink`;
  } else {
    result = `${link}?$fallback_url=${baseUrl}/deeplink`;
  }

  localStorage.removeItem('branch_session_first');
  return result;
};
