/* eslint-disable react-hooks/exhaustive-deps */
import loginLoader from './LoginLoader';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { func, string, bool, shape } from 'prop-types';
import { useIntl } from 'react-intl';
import siteMap from '../../../routing/siteMap';
import { isValidEmail } from '../../../utils/formValidations.js';
import TwoFactorAuthCodeModal from './TwoFactorAuthCode.modal';
import './Login.scss';
import { showLocalizedErrorBar } from '../../../utils/showConfirmationBar';
import { withRouter } from 'react-router-dom';
import LoginComponent from './LoginComponent';
import PrivacyPolicyFooter from '../../PrivacyPolicyFooter';
import { VidMobBox } from '../../../vidMobComponentWrappers';
import vidMobConfig from '../../../../configs/vidMobConfigConstants';
import BffSsoService from '../../../apiServices/BffSsoService';
import useIframeLogin from './useIframeLogin';
import {
  getNameOfProjectForPendingInvite,
  getDeepLinkService,
} from '../../../redux/reducers/deepLink.selectors';
import { Buffer } from 'buffer';
import { setReferrerCookie } from './referrerLoginUtils';

const loginViews = {
  EMAIL: 'EMAIL',
  PASSWORD: 'PASSWORD',
};

const LoginPage = ({
  hasInvite,
  location,
  resetLoginError,
  onLogin,
  history,
  inviteeEmailAddress,
  loginFailed,
}) => {
  const intl = useIntl();
  const [email, setEmail] = useState(inviteeEmailAddress || '');
  const [emailError, setEmailError] = useState(null);
  const [generalError, setGeneralError] = useState(null);
  const [password, setPassword] = useState('');
  const [ssoCognitoHostedUrl, setSsoCognitoHostedUrl] = useState('');
  const [isLegacyAndSsoLogin, setIsLegacyAndSsoLogin] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [isTwoFactorModalVisible, setIsTwoFactorModalVisible] = useState(false);
  const [loginView, setLoginView] = useState(loginViews.EMAIL);
  const { isInIframe, handleIframeLogin } = useIframeLogin();

  const projectName = useSelector(getNameOfProjectForPendingInvite);
  const deepLinkService = useSelector(getDeepLinkService);

  const rememberReferrer = () => {
    const referrer = history?.location?.state?.referrer;
    if (referrer && referrer !== siteMap.home) setReferrerCookie(referrer);
  };

  useEffect(() => {
    const errorMessage = location.state?.errorMessage;
    // Show error bar if finalizeSsoLogin endpoint fails
    if (errorMessage) {
      showLocalizedErrorBar('error.api.sso.login.general');
      // Clear the URL query parameters
      history.replace('/login');
    }
  }, []);

  const acceptInvite = async () => {
    deepLinkService.acceptInvite();
  };

  useEffect(() => {
    if (loginFailed && isLoggingIn) {
      setIsLoggingIn(false);
      setGeneralError(intl.messages['error.api.login.general']);
      setIsTwoFactorModalVisible(false);
      resetLoginError();
    }
  }, [loginFailed, isLoggingIn, intl, resetLoginError]);

  const loginUser = (userEmail, userPassword, twoFAPassword) => {
    setIsLoggingIn(true);

    if (isValidEmail(email)) {
      onLogin(userEmail, userPassword, twoFAPassword).then((result) => {
        let isTwoFactorModalVisible = false;
        if (
          result &&
          (result.error || result.isTwoFactorModalVisible) &&
          !loginFailed
        ) {
          if (result.isTwoFactorModalVisible) {
            isTwoFactorModalVisible = true;
          }

          // If the user did enter a twoFactorAuthPassword and they get back here it was not the correct code so
          // clear it out and let them try again.
          setGeneralError(result.error);
          setIsTwoFactorModalVisible(isTwoFactorModalVisible);
          setIsLoggingIn(false);
        }
      });
    } else {
      setEmailError(intl.messages['error.form.email.invalid']);
    }
  };

  const applyTwoFactorCode = (twoFACode) => {
    loginUser(email, password, twoFACode);
  };

  const getCognitoHostedLoginUrl = async (cognitoHostedLoginParams) => {
    const {
      url,
      clientId,
      responseType,
      scope,
      redirectUri,
      identityProvider,
      finalizeSsoLoginPath,
    } = cognitoHostedLoginParams;

    const vidmobConfig = vidMobConfig[process.env.VIDMOB_ENV || 'local'];
    const acsUrl = vidmobConfig.baseUrl + finalizeSsoLoginPath;

    const state = await getLoginStateData(acsUrl);

    return `${url}?client_id=${clientId}&scope=${scope}&response_type=${responseType}&redirect_uri=${redirectUri}&identity_provider=${identityProvider}&state=${state}`;
  };

  const getLoginStateData = async (acsUrl) => {
    const state = { url: acsUrl };

    if (!deepLinkService) {
      // there is no deeplink setup, so it's not comming from deep link page, we can safely return the state.
      return Buffer.from(JSON.stringify(state)).toString('base64');
    }

    const deeplinkInfo = deepLinkService.getInviteData();

    if (!deeplinkInfo) {
      return Buffer.from(JSON.stringify(state)).toString('base64');
    }

    // if is invalid, it will throw an error which will be catch it on the login error
    await deepLinkService.validateInvite();

    state['organizationId'] = deeplinkInfo.organizationId;
    state['inviteCode'] = deeplinkInfo.inviteCode;
    state['validationCode'] = deeplinkInfo.validationCode;

    localStorage.removeItem('branch_session_first'); // remove the invite from local storage

    return Buffer.from(JSON.stringify(state)).toString('base64');
  };

  const handleSubmitEmail = async (userEmail) => {
    setGeneralError(null);

    if (!isValidEmail(userEmail)) {
      return setEmailError(intl.messages['error.form.email.invalid']);
    }

    setIsLoggingIn(true);
    setEmailError('');
    setEmail(userEmail);

    try {
      const data = await BffSsoService.getLoginOptions(userEmail);

      const cognitoHostedLoginUrl = await getCognitoHostedLoginUrl({
        ...data.cognitoHostedLogin,
        finalizeSsoLoginPath: isInIframe
          ? 'finalizeSsoLoginFromIframe'
          : 'finalizeSsoLogin',
      });

      // If user can log in with username/password AND sso
      if (data?.canUseLegacyLogin && data?.ssoOptions?.length > 0) {
        setIsLegacyAndSsoLogin(true);
        setLoginView(loginViews.PASSWORD);
        setSsoCognitoHostedUrl(cognitoHostedLoginUrl);
      }

      if (data.canUseLegacyLogin === false) {
        rememberReferrer();
        if (isInIframe) {
          handleIframeLogin(cognitoHostedLoginUrl);
        } else {
          window.location.assign(cognitoHostedLoginUrl);
        }
      } else {
        setLoginView(loginViews.PASSWORD);
        setIsLoggingIn(false);
      }
    } catch (error) {
      console.error('Error:', error);
      setEmailError(intl.messages['error.api.sso.login.general']);
      setIsLoggingIn(false);
    }
  };

  const handleSubmitPassword = async (passwordValue, event) => {
    event?.preventDefault?.();

    setPassword(passwordValue);
    setGeneralError(null);
    loginUser(email, passwordValue);

    if (projectName) {
      await acceptInvite();
    }
  };

  const clearError = () => {
    setGeneralError(null);
    setEmailError(null);
  };

  const renderEmailView = () => {
    return (
      <LoginComponent
        key="email"
        hasInvite={hasInvite}
        buttonLabelText={intl.messages['ui.ssoLogin.login.button']}
        inputLabel={intl.messages['form.login.email.label']}
        inputType="email"
        isLoading={isLoggingIn}
        value={email}
        error={emailError}
        clearError={clearError}
        nextButtonHandler={handleSubmitEmail}
      />
    );
  };

  const renderPasswordView = () => {
    return (
      <LoginComponent
        key="password"
        hasInvite={hasInvite}
        buttonLabelText={intl.messages['ui.ssoLogin.login.button']}
        inputLabel={intl.messages['form.login.password.label']}
        ssoText={
          isLegacyAndSsoLogin ? intl.messages['ui.ssoLogin.header'] : null
        }
        ssoPath={ssoCognitoHostedUrl}
        forgotText={intl.messages['ui.login.v2.forgotPassword']}
        forgotPath={siteMap.resetPassword}
        generalError={generalError}
        inputType="password"
        isLoading={isLoggingIn}
        value={password}
        error={generalError}
        clearError={clearError}
        nextButtonHandler={handleSubmitPassword}
        isInIframe={isInIframe}
        handleIframeLogin={handleIframeLogin}
      />
    );
  };

  const renderLoginView = () => {
    if (loginView === loginViews.EMAIL) {
      return renderEmailView();
    }

    if (loginView === loginViews.PASSWORD) {
      return renderPasswordView();
    }

    return null;
  };

  return (
    <>
      {isTwoFactorModalVisible && (
        <TwoFactorAuthCodeModal
          intl={intl}
          loading={isLoggingIn}
          onCancel={() => setIsTwoFactorModalVisible(false)}
          onSubmit={applyTwoFactorCode}
        />
      )}
      {renderLoginView()}
      <VidMobBox sx={{ position: 'absolute', right: 0, bottom: 0 }}>
        <PrivacyPolicyFooter showDivider={false} />
      </VidMobBox>
    </>
  );
};

LoginPage.propTypes = {
  hasInvite: bool.isRequired,
  loading: bool.isRequired,
  location: shape({ pathname: string.isRequired }).isRequired,
  resetLoginError: func.isRequired,
  onLogin: func.isRequired,
  history: shape({}),
  inviteeEmailAddress: string,
  loginFailed: bool,
};

LoginPage.defaultProps = {
  history: {},
  inviteeEmailAddress: '',
  loginFailed: false,
};

export const LoginPageWithIntl = withRouter(LoginPage); // exporting unwrapped instance for unit testing

export default loginLoader(LoginPageWithIntl);
