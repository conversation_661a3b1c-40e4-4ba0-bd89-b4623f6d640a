import React from 'react';
import { SvgIcon } from '@mui/material';

export const <PERSON>utChartIcon = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 24 24">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.2426 3.02017C10.9548 2.88243 11.5639 3.46945 11.5639 4.19479C11.5639 4.86537 11.0376 5.40754 10.3874 5.57158C7.61662 6.27063 5.56918 8.73755 5.56918 11.6735C5.56918 15.153 8.44484 17.9737 11.9921 17.9737C13.5884 17.9737 15.0486 17.4026 16.172 16.4572C16.7474 15.9731 17.6217 15.8985 18.1585 16.425C18.6215 16.8792 18.6654 17.6201 18.1914 18.0628C16.5782 19.5692 14.3953 20.4938 11.9921 20.4938C7.02592 20.4938 3 16.5449 3 11.6736C3 7.38949 6.11383 3.81876 10.2426 3.02017ZM17.1196 6.31094C17.646 5.79462 17.6322 4.93325 17.0175 4.52613C16.0442 3.88154 14.9353 3.41892 13.7415 3.18803C13.0294 3.05029 12.4203 3.63733 12.4203 4.36266C12.4203 5.03324 12.9464 5.57538 13.5966 5.73941C14.208 5.89363 14.7841 6.13391 15.3108 6.44629C15.8873 6.7882 16.6412 6.78027 17.1196 6.31094ZM17.7818 6.84889C17.3123 7.32578 17.2983 8.07647 17.6281 8.65932C18.122 9.5325 18.4109 10.5339 18.4299 11.602C18.4428 12.3225 18.3317 13.0169 18.1161 13.6657C17.9276 14.2329 18.0206 14.8788 18.4548 15.2901C19.0628 15.866 20.052 15.7455 20.3666 14.9699C20.7941 13.9159 21.0201 12.7631 20.9986 11.558C20.9684 9.86267 20.4526 8.28742 19.5834 6.95765C19.1755 6.3337 18.3044 6.31804 17.7818 6.84889Z"
      />
    </SvgIcon>
  );
};
