import React from 'react';
import { DV360Logo } from './DV360Logo';
import { <PERSON>Logo } from './FacebookLogo';
import { GoogleAdsLogo } from './GoogleAdsLogo';
import { InstagramLogo } from './InstagramLogo';
import { LinkedInLogo } from './LinkedInLogo';
import { PinterestLogo } from './PinterestLogo';
import { RedditLogo } from './RedditLogo';
import { SnapchatLogo } from './SnapchatLogo';
import { TikTokLogo } from './TikTokLogo';
import { TwitterLogo } from './TwitterLogo';
import { VidMobLogo } from './VidMobLogo';
import { AmazonLogo } from './AmazonLogo';
import { MetaLogo } from './MetaLogo';
import {
  PLATFORM_ADWORDS,
  PLATFORM_DV360,
  PLATFORM_FACEBOOK,
  PLATFORM_FACEBOOK_PAGES,
  PLATFORM_INSTAGRAM_PAGES,
  PLATFORM_LINKEDIN,
  PLATFORM_PINTEREST,
  PLATFORM_SNAPCHAT,
  PLATFORM_TIKTOK,
  PLATFORM_TWITTER,
  PLATFORM_AMAZON_ADVERTISING,
  PLATFORM_AMAZON_ADVERTISING_DSP,
  PLATFORM_REDDIT,
  PLATFORM_ANALYTICS_NAME_SNAPCHAT,
  PLATFORM_META,
} from '../../../constants/platform.constants';
import PLATFORM from '../../../constants/platform.constants';
import { getLogoByPlatformIdentifier } from '../../../utils/feConstantsUtils';
import { VidMobSvgIcon } from '../../../vidMobComponentWrappers';

const PLATFORM_AMAZON = PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER; // added to support 'AMAZON' from Scoring

const staticIconComponents = {
  [PLATFORM_ADWORDS]: <GoogleAdsLogo />,
  [PLATFORM_AMAZON]: <AmazonLogo />,
  [PLATFORM_AMAZON_ADVERTISING]: <AmazonLogo />,
  [PLATFORM_AMAZON_ADVERTISING_DSP]: <AmazonLogo />,
  [PLATFORM_DV360]: <DV360Logo />,
  [PLATFORM_FACEBOOK]: <FacebookLogo />,
  [PLATFORM_FACEBOOK_PAGES]: <FacebookLogo />,
  [PLATFORM_INSTAGRAM_PAGES]: <InstagramLogo />,
  [PLATFORM_LINKEDIN]: <LinkedInLogo />,
  [PLATFORM_PINTEREST]: <PinterestLogo />,
  [PLATFORM_REDDIT]: <RedditLogo />,
  [PLATFORM_SNAPCHAT]: <SnapchatLogo />,
  [PLATFORM_ANALYTICS_NAME_SNAPCHAT]: <SnapchatLogo />,
  [PLATFORM_TIKTOK]: <TikTokLogo />,
  [PLATFORM_TWITTER]: <TwitterLogo />,
  [PLATFORM_META]: <MetaLogo />,
};

// IMPORTANT: The logic in this function is used both in getMUIIconForChannel and in MuiIconForChannel as it should remain largely the same
// MuiIconForChannel is a component so you can add hooks such
export const channelIconLogic = (
  platform,
  sxStyling,
  shouldShowMetaForFacebook,
  isPageExportingPDF = false,
) => {
  let adjustedPlatform = platform;

  if (shouldShowMetaForFacebook && platform === PLATFORM_FACEBOOK) {
    adjustedPlatform = PLATFORM_META;
  }

  const logoUrl = getLogoByPlatformIdentifier(adjustedPlatform);

  if (logoUrl && !isPageExportingPDF) {
    return (
      <VidMobSvgIcon sx={sxStyling}>
        <image href={logoUrl} width="100%" height="100%" />
      </VidMobSvgIcon>
    );
  } else if (staticIconComponents[adjustedPlatform]) {
    return React.cloneElement(staticIconComponents[adjustedPlatform], {
      sx: sxStyling,
    });
  }

  // Fallback to VidMob logo as the default
  return <VidMobLogo sx={sxStyling} />;
};

// This is doing the exact same as MuiIconForChannel, except as a function rather than component
const getMUIIconForChannel = (
  platform = '',
  sxStyling = {},
  shouldShowMetaForFacebook = false,
) => {
  return channelIconLogic(platform, sxStyling, shouldShowMetaForFacebook);
};

export default getMUIIconForChannel;
