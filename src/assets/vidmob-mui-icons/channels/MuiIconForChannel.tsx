import { SxProps } from '@mui/material';
import { useIsPageExporting } from '../../../hooks/useIsPageExporting';
import { channelIconLogic } from './getMUIIconForChannel';

type Props = {
  platform?: string;
  sxStyling?: SxProps;
  shouldShowMetaForFacebook?: boolean;
};

// This is doing the exact same as getMUIIconForChannel, just in component form so we can use hooks
const MuiIconForChannel = ({
  platform = '',
  sxStyling = {},
  shouldShowMetaForFacebook = false,
}: Props) => {
  const { isPageExportingPDF } = useIsPageExporting();

  return channelIconLogic(
    platform,
    sxStyling,
    shouldShowMetaForFacebook,
    isPageExportingPDF,
  );
};

export default MuiIconForChannel;
