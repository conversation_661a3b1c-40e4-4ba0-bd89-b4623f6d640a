import React from 'react';
import { SvgIcon } from '@mui/material';

export const Meta<PERSON><PERSON> = (props) => (
  <SvgIcon {...props} viewBox="0 0 24 16">
    {/* eslint-disable-next-line max-len */}
    <path
      d="M2.59229 10.5127C2.59229 11.4291 2.79343 12.1327 3.05633 12.5583C3.40102 13.1158 3.91514 13.352 4.43927 13.352C5.1153 13.352 5.73374 13.1843 6.92556 11.5359C7.88035 10.2147 9.0054 8.36025 9.76238 7.19764L11.0443 5.22797C11.9349 3.86005 12.9656 2.3394 14.1474 1.30866C15.1122 0.467379 16.153 0 17.2004 0C18.9589 0 20.634 1.01905 21.9159 2.9303C23.3189 5.02349 23.9999 7.66001 23.9999 10.3808C23.9999 11.9983 23.6811 13.1868 23.1386 14.1257C22.6145 15.0338 21.5929 15.941 19.8745 15.941V13.352C21.3459 13.352 21.7131 12 21.7131 10.4526C21.7131 8.24757 21.199 5.80051 20.0664 4.05201C19.2627 2.81179 18.2211 2.05396 17.0752 2.05396C15.8358 2.05396 14.8385 2.98872 13.7176 4.65543C13.1217 5.54095 12.5099 6.62009 11.823 7.83778L11.0669 9.17733C9.54789 11.8706 9.16314 12.484 8.40365 13.4964C7.07245 15.2691 5.93572 15.941 4.43927 15.941C2.66406 15.941 1.54152 15.1723 0.84629 14.0139C0.278758 13.0699 0 11.8314 0 10.4201L2.59229 10.5127Z"
      fill="#0081FB"
    />
    <path
      d="M2.04395 3.11308C3.23242 1.28112 4.94754 0 6.9147 0C8.05394 0 9.1865 0.337181 10.3691 1.30282C11.6628 2.3586 13.0415 4.09708 14.7617 6.96228L15.3784 7.99052C16.8674 10.471 17.7145 11.7471 18.2103 12.3488C18.8479 13.1217 19.2944 13.352 19.8745 13.352C21.3459 13.352 21.7131 12 21.7131 10.4526L23.9999 10.3808C23.9999 11.9983 23.6811 13.1868 23.1386 14.1257C22.6145 15.0338 21.5929 15.941 19.8745 15.941C18.8062 15.941 17.8597 15.709 16.8131 14.7216C16.0086 13.9638 15.068 12.6176 14.3444 11.4074L12.1919 7.81191C11.1119 6.00749 10.1213 4.66211 9.54789 4.05285C8.93111 3.39768 8.13824 2.60647 6.87297 2.60647C5.84891 2.60647 4.97925 3.32507 4.25148 4.42424L2.04395 3.11308Z"
      fill="url(#paint0_linear_4948_7093)"
    />
    <path
      d="M6.87298 2.60647C5.84892 2.60647 4.97926 3.32507 4.25148 4.42424C3.22241 5.97745 2.59229 8.29097 2.59229 10.5127C2.59229 11.4291 2.79343 12.1327 3.05633 12.5583L0.84629 14.0139C0.278758 13.0699 0 11.8314 0 10.4201C0 7.85364 0.704407 5.17873 2.04395 3.11308C3.23243 1.28112 4.94754 0 6.91471 0L6.87298 2.60647Z"
      fill="url(#paint1_linear_4948_7093)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_4948_7093"
        x1="5.09109"
        y1="9.76489"
        x2="21.6163"
        y2="10.5995"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#0064E1" />
        <stop offset="0.4" stopColor="#0064E1" />
        <stop offset="0.83" stopColor="#0073EE" />
        <stop offset="1" stopColor="#0082FB" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_4948_7093"
        x1="3.75573"
        y1="11.601"
        x2="3.75573"
        y2="5.5084"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#0082FB" />
        <stop offset="1" stopColor="#0064E0" />
      </linearGradient>
    </defs>
  </SvgIcon>
);
