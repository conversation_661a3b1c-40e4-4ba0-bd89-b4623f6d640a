import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { GLOBALS } from '../constants';
import { useNavigate } from 'react-router-dom-v5-compat';
import siteMapRoutes from './siteMapRoutes';
import { getFeatureFlag } from '../utils/featureFlagUtils';
import VmHelmet from '../components/VmHelmet';
import ErrorBoundaryLoopPrevention from '../components/ErrorBoundaryLoopPrevention';
import Loading from '../vcl/ui/Loading';
import BlankStateError from '../components/BlankStateError';
import { loadAnalyticsUserDataIfNeeded } from '../redux/actions/user.actions';

const { SUCCESS, FAILED } = GLOBALS.REDUX_LOADING_STATUS;

/**
 * @visibleName Analytics Data Element
 */

const AnalyticsDataElement = ({
  accountsLoaded,
  isLoadingOptional,
  isPlatformAccountsOutOfSync,
  loadAnalyticsUserDataIfNeeded,
  analyticsUserStatus,
  analyticsUserId,
  isAnalyticsUserLoaded,
  hadErrorLoadingAnalyticsUser,
  routes,
  titleMessageId,
  component: Component,
  errorComponent: ErrorComponent,
  loadingComponent: LoadingComponent,
  ...otherProps
}) => {
  useEffect(() => {
    if (!isAnalyticsUserLoaded) {
      loadAnalyticsUserDataIfNeeded();
    }
  }, [
    analyticsUserStatus,
    isAnalyticsUserLoaded,
    loadAnalyticsUserDataIfNeeded,
  ]);

  if (!Component) {
    if (hadErrorLoadingAnalyticsUser) {
      return ErrorComponent ? <ErrorComponent /> : null;
    }
    return LoadingComponent ? <LoadingComponent /> : null;
  }

  if (!isLoadingOptional && !isAnalyticsUserLoaded) {
    return LoadingComponent ? <LoadingComponent /> : null;
  }

  return (
    <>
      <VmHelmet titleMessageId={titleMessageId} />
      <ErrorBoundaryLoopPrevention boundaryIdentifier="CI">
        <Component {...otherProps} routes={routes} />
      </ErrorBoundaryLoopPrevention>
    </>
  );
};

AnalyticsDataElement.propTypes = {
  analyticsUserStatus: PropTypes.oneOf(
    Object.values(GLOBALS.REDUX_LOADING_STATUS),
  ).isRequired,
  component: PropTypes.func.isRequired,
  hadErrorLoadingAnalyticsUser: PropTypes.bool.isRequired,
  isAnalyticsUserLoaded: PropTypes.bool.isRequired,
  isPlatformAccountsOutOfSync: PropTypes.bool.isRequired,
  loadAnalyticsUserDataIfNeeded: PropTypes.func.isRequired,
  accountsLoaded: PropTypes.bool,
  analyticsUserId: PropTypes.number,
  errorComponent: PropTypes.func,
  isLoadingOptional: PropTypes.bool,
  loadingComponent: PropTypes.func,
  routes: PropTypes.arrayOf(PropTypes.object),
  titleMessageId: PropTypes.string,
};

AnalyticsDataElement.defaultProps = {
  accountsLoaded: false,
  isLoadingOptional: false,
  loadingComponent: Loading,
  errorComponent: BlankStateError,
  analyticsUserId: null,
  routes: [],
  titleMessageId: null,
};

const mapStateToProps = ({ user, platformAccounts }) => {
  const analyticsUserStatus = user.analyticsUser.status;
  return {
    isAnalyticsUserLoaded: analyticsUserStatus === SUCCESS,
    hadErrorLoadingAnalyticsUser: analyticsUserStatus === FAILED,
    analyticsUserStatus,
    isPlatformAccountsOutOfSync: platformAccounts.isOutOfSyncWithAdAccounts,
    accountsLoaded: platformAccounts.accountsLoaded,
  };
};

const mapDispatchToProps = {
  loadAnalyticsUserDataIfNeeded,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(AnalyticsDataElement);
