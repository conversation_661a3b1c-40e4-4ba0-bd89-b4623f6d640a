/**
 * This file contains all urls for the site. These are exclusive to be used for
 * Routes and to be parsed to create links
 * This is what routes.js uses to config routes
 * Check doc for how to create paths https://github.com/pillarjs/path-to-regexp/tree/v1.7.0
 */
const routes = {
  carouselExample: '/carouselExample',
  home: '/',
  main: '/',
  deeplink: '/deeplink',
  branchLandingFailed: '/uh-oh',
  componentDemo: '/__componentDemo',
  featureFlags: '/__featureFlags',
  login: '/login',
  externalOauth: '/oauth2/authorize/:step?',
  resetPassword: '/reset-password',
  updatePassword: '/update-password',
  notFound: '/404',
  signUp: '/signup/:step?',
  connect: '/connect/:platform?',
  snapCode: '/snapCode/:snapCode/:type?',
  authresult: '/authresult/:platform?',
  finalizeSsoLogin: '/finalizeSsoLogin',
  finalizeSsoLoginFromIframe: '/finalizeSsoLoginFromIframe',
  feedbackLanding:
    '/project/:status(active|completed)/:projectId/feedback/output/:outputId',
  feedback: '/feedback/project/:projectId/iteration/:iterationId',
  feedbackWithProject:
    '/project/:status(active|completed)/:projectId/feedback/iteration/:iterationId',
  feedbackPublic:
    '/contributor-feedback/project/:projectId/iteration/:iterationId/contributor/:contributorType/:contributorCode',
  activeProjects: '/projects/active',
  completedProjects: '/projects/completed',
  creativeScoringSingleAssetView: '/creativeScoring/singleAssetView/:token?',
  creativeIntelligenceRollupReportsLanding:
    '/creativeIntelligence/creative-scoring/reports',
  diversityReportPDF:
    '/creativeIntelligence/creative-scoring/reports/preview/diversity',
  creativeScoringDiversityReport:
    '/creativeIntelligence/creative-scoring/reports/diversity/:reportId?',
  creativeScoringRollUpReport:
    '/creativeIntelligence/creative-scoring/reports/:reportType/:reportId?',
  creativeAnalyticsIndividualCreativeView:
    '/creativeIntelligence/individual/platformMedia/:platformMediaId/:isCreativeOfMultiAssetAds?',
  creativeIntelligence: '/creativeIntelligence',
  creativeIntelligenceCompliance:
    '/creativeIntelligence/creative-scoring/:tab?/:batchId?/:adAccountId?',
  creativeIntelligenceReports:
    '/creativeIntelligence/creative-scoring/scorecards-landing',
  creativeIntelligenceCreativeScoringCreateScorecard:
    '/creativeIntelligence/creative-scoring/submission-report',
  creativeScoringScoreCardLanding:
    '/creativeIntelligence/creative-scoring/scorecards/:scorecardType',
  creativeScoringScoreCardDetails:
    '/creativeIntelligence/creative-scoring/scorecard/:scorecardId',
  creativeIntelligenceScoreOverrides:
    '/creativeIntelligence/creative-scoring/score-overrides',
  creativeIntelligenceSingleAssetBatch:
    '/creativeIntelligence/creative-scoring/:tab?/batch/:batchId/media/:mediaId/:groupBy?',
  creativeIntelligenceSingleAssetInflight:
    '/creativeIntelligence/creative-scoring/:tab?/workspace/:workspaceId/media/:mediaId/:channel/:groupBy/:reportId?',
  creativeIntelligenceSingleAssetAdAccount:
    '/creativeIntelligence/creative-scoring/:tab?/ad-account/:adAccountId/:batchId/platform/:platform/media/:mediaId',
  creativeIntelligenceCreativeManager: '/creativeIntelligence/creativeManager',
  creativeIntelligenceCreativeManagerPDF:
    '/creativeIntelligence/preview/creativeManager',
  creativeIntelligenceCreativeLeaderboard:
    '/creativeIntelligence/creativeLeaderboard/:reportId?',
  creativeIntelligenceCreativeLeaderboardPDF:
    '/creativeIntelligence/preview/creativeLeaderboard',
  creativeIntelligenceSavedReports: '/creativeIntelligence/savedReports',
  creativeIntelligenceImpactReport:
    '/creativeIntelligence/impact/:reportType?/:reportId?',
  creativeIntelligenceImpactReportPDF:
    '/creativeIntelligence/preview/impact/:reportType?',
  creativeIntelligenceCriteriaPerformance:
    '/creativeIntelligence/criteria-performance/:reportId?',
  creativeIntelligenceCompare:
    '/creativeIntelligence/platform/:platform/:type/:accountId/breakdown/:tab',
  creativeIntelligenceCustomCompareDeprecated:
    '/creativeIntelligence/customCompare/platform/:platform/:accountType/:accountId/breakdown/:reportId?',
  creativeIntelligenceCustomCompareNew:
    '/creativeIntelligence/customCompare/breakdown/:reportId?',
  creativeIntelligenceCustomComparePDF:
    '/creativeIntelligence/preview/customCompare',
  creativeIntelligenceInsightsLibrary:
    '/creativeIntelligence/insights-library/:viewParam?',
  currentProjectBase: '/project/:status(active|completed)/:projectId/',
  currentProject: '/project/:status(active|completed)/:projectId/:tab?',
  creatorSelection:
    '/project/:status(active|completed)/:projectId/team/selection',
  currentActiveProject: '/project/active/:projectId/:tab?',
  currentCompletedProject: '/project/completed/:projectId/:tab?',
  executiveDashboard: '/executive-dashboard/:dashboardId?',
  executiveDashboardLanding: '/executive-dashboard/landing',
  widgetEdit: '/executive-dashboard/widget/:widgetId',
  userProfile: '/user-profile/:tab?',
  partner: '/partner/:tab?',
  createPartner: '/create/partner',
  createProject: '/create/project/:productType(creative|data)?/:step?',
  editProject: '/edit/project/:projectId/:productType(creative|data)/:step',
  editorProfile: '/creator-profile/:editorId',
  editorProfileBase:
    '/creator-profile/:editorId/(portfolioMedia)?/:portfolioMediaId?',
  editorPortfolioMedia:
    '/creator-profile/:editorId/portfolioMedia/:portfolioMediaId',
  editorProfileLegacy: '/editor/profile/:editorId?',
  projectNoteView: '/project/:status(active|completed)/:projectId/note/:noteId',
  projectNoteNew: '/project/:status(active|completed)/:projectId/note/create',
  projectNoteEdit:
    '/project/:status(active|completed)/:projectId/note/edit/:noteId',
  stockContent: '/project/active/:projectId/assets/stock',
  payment: '/project/active/:projectId/payment',
  activeOutputs: '/project/active/:projectId/outputs',
  activeConcepts: '/project/active/:projectId/concepts',
  activeReport: '/project/active/:projectId/report',
  completedOutputs: '/project/completed/:projectId/outputs',
  completedReport: '/project/completed/:projectId/report',
  facebookMarketplace: '/landing/facebook-marketplace',
  facebookMarketplaceConnect: '/landing/facebook-marketplace/connect',
  facebookMarketplaceSelect: '/landing/facebook-marketplace/select',
  facebookMarketplaceConfirm: '/landing/facebook-marketplace/confirm',
  facebookMarketplaceThanks: '/landing/facebook-marketplace/thanks',
  lockedOutPage: '/locked-out',
  twitterCreateLandingPage: '/landing/twitter-creative',
  twitterCreateLandingPageSignUp: '/landing/twitter-creative/sign-up',
  mobileFitnessRequest: '/landing/mobile-fitness-request',
  mobileFitnessRequestConnect: '/landing/mobile-fitness-request/connect',
  mobileFitnessRequestSelect: '/landing/mobile-fitness-request/select',
  mobileFitnessRequestConfirm: '/landing/mobile-fitness-request/confirm',
  mobileFitnessRequestProcessing: '/landing/mobile-fitness-request/processing',
  mobileFitnessRequestResults: '/landing/mobile-fitness-request/results',
  muiSandbox: '/mui-sandbox',
  projectCreateV2: '/project/create/:projectId?/:step?',
  projectEdit: '/project/edit/:projectId?/:step?',
  studio: '/studio',
  try: '/try/:featureName',
  workspaceList: '/workspaces',
  workspaceDetails: '/workspaces/:workspaceId',
  workspaceAdAccount: '/workspaces/:workspaceId/ad-accounts/',
  people: '/people',
  brandLabels: '/brands',
  partnerPeople: '/partner/team',
  adAccountHealthDashboard: '/integrations/manage',
  organizationIntegrations: '/integrations/app-store',
  organizationSwitcher: '/organizations',
  organizationApiKeys: '/api-key-management',
  organizationDataExports: '/data-exports',
  organizationDataConnectors: '/connectors',
  organizationSecurity: '/single-sign-on',
  insightCopilotRoute: '/creativeIntelligence/maddie/:copilotType?/:chatId?',
  creativeLifecycle: '/creativeIntelligence/creative-lifecycle',
};

export default routes;
