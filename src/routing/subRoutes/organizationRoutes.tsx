import siteMap, { routeParams } from '../siteMap';
import loadable from 'react-loadable';
import DynamicRouteLoading from '../../components/DynamicRouteLoading';
import { parseURL } from '../../creativeAnalytics/helpers/composeLinkHelper';
import withOutletContext from '../withOutletContext';

const AdAccountHealthDashboard = loadable({
  loader: () =>
    import('./../../userManagement/__pages/AdAccountHealthDashboard'),
  loading: DynamicRouteLoading,
});

const ApiKeyManagement = loadable({
  loader: () => import('./../../platform/__pages/ApiKeyManagement'),
  loading: DynamicRouteLoading,
});

const BrandLabels = loadable({
  loader: () => import('./../../userManagement/__pages/BrandLabels'),
  loading: DynamicRouteLoading,
});

const DataConnectors = loadable({
  loader: () => import('./../../platform/__pages/Connectors'),
  loading: DynamicRouteLoading,
});

const DataExports = loadable({
  loader: () => import('./../../platform/__pages/DataExports'),
  loading: DynamicRouteLoading,
});

const Integrations = loadable({
  loader: () => import('./../../components/__subpages/Integrations'),
  loading: DynamicRouteLoading,
});

const OrganizationOutlet = loadable({
  loader: () => import('./OrganizationOutlet'),
  loading: DynamicRouteLoading,
});

const OrganizationSecurity = loadable({
  loader: () => import('./../../platform/__pages/SsoSettings'),
  loading: DynamicRouteLoading,
});

const PartnerAssetLocker = loadable({
  loader: () => import('./../../components/__subpages/PartnerAssetLocker'),
  loading: DynamicRouteLoading,
});

const Peoples = loadable({
  loader: () => import('./../../userManagement/__pages/Peoples'),
  loading: DynamicRouteLoading,
});

const ProfileCustomTagGroups = loadable({
  loader: () => import('./../../components/__subpages/ProfileCustomTagGroups'),
  loading: DynamicRouteLoading,
});

const WorkspaceAdAccounts = loadable({
  loader: () => import('./../../userManagement/__pages/AdAccounts'),
  loading: DynamicRouteLoading,
});

const WorkspaceDetails = loadable({
  loader: () => import('./../../userManagement/__pages/Workspace'),
  loading: DynamicRouteLoading,
});

const Workspaces = loadable({
  loader: () => import('./../../userManagement/__pages/Workspaces'),
  loading: DynamicRouteLoading,
});

export const organizationRoutes = [
  {
    path: '/',
    component: OrganizationOutlet,
    routes: [
      {
        path: siteMap.organizationApiKeys,
        component: ApiKeyManagement,
        titleMessageId: 'ui.site.pageTitle.apiKeyManagement',
      },
      {
        path: siteMap.organizationDataExports,
        component: DataExports,
        titleMessageId: 'ui.site.pageTitle.dataExports',
      },
      {
        path: siteMap.organizationDataConnectors,
        component: DataConnectors,
        titleMessageId: 'ui.site.pageTitle.dataConnectors',
      },
      {
        path: siteMap.organizationSecurity,
        component: OrganizationSecurity,
        titleMessageId: 'ui.site.pageTitle.singleSignOn',
      },
      {
        path: parseURL(siteMap.userProfile, {
          tab: routeParams.tabs.profile.tagGroups,
        }),
        exact: true,
        component: ProfileCustomTagGroups,
        isAnalyticsRoute: true,
        titleMessageId: 'ui.site.pageTitle.userProfile.creativeGroups',
      },
      {
        path: siteMap.workspaceList,
        exact: true,
        component: Workspaces,
        titleMessageId: 'ui.site.pageTitle.workspace',
      },
      {
        path: siteMap.workspaceDetails,
        exact: true,
        component: WorkspaceDetails,
        titleMessageId: 'ui.site.pageTitle.workspace.details',
      },
      {
        path: siteMap.workspaceAdAccount,
        exact: true,
        component: WorkspaceAdAccounts,
        titleMessageId: 'ui.site.pageTitle.workspace.adAccounts',
      },
      {
        path: siteMap.people,
        exact: true,
        component: Peoples,
        titleMessageId: 'ui.site.pageTitle.peoples',
      },
      {
        path: siteMap.organizationIntegrations,
        exact: true,
        component: Integrations,
        titleMessageId: 'ui.site.pageTitle.userProfile.integrations',
      },
      {
        path: siteMap.adAccountHealthDashboard,
        exact: true,
        component: AdAccountHealthDashboard,
        titleMessageId: 'ui.site.pageTitle.manage',
      },
      {
        path: siteMap.brandLabels,
        exact: true,
        component: BrandLabels,
        titleMessageId: 'ui.site.pageTitle.brandLabels',
      },
      {
        path: parseURL(siteMap.partner, {
          tab: routeParams.tabs.partner['asset-locker'],
        }),
        exact: true,
        component: withOutletContext(PartnerAssetLocker),
        titleMessageId: 'ui.site.pageTitle.partner.assetLocker',
      },
    ],
  },
];
