// loadable docs: https://github.com/jamiebuilds/react-loadable
import loadable from 'react-loadable';
import DynamicRouteLoading from './../components/DynamicRouteLoading';
import siteMap, { routeParams } from './siteMap';
import { parseURL } from '../utils/urlUtils';
import { PAGE_META_TAGS } from './../constants/';
import React from 'react';
import { objectOf, any } from 'prop-types';
import { getFeatureFlag } from '../utils/featureFlagUtils';
import { Navigate } from 'react-router-dom-v5-compat';

// All component in route need to be dynamically imported
// please check doc to understand how dynamic import plays out with webpack
// https://webpack.js.org/guides/code-splitting/#dynamic-imports

const MainPage = loadable({
  loader: () => import('./../components/__pages/Main'),
  loading: DynamicRouteLoading,
});

const Home = loadable({
  loader: () => import('./../components/__pages/Home'),
  loading: DynamicRouteLoading,
});

const DashboardPage = loadable({
  loader: () => import('../dashboard/Dashboard'),
  loading: DynamicRouteLoading,
});

const DashboardLandingPage = loadable({
  loader: () =>
    import('../dashboard/components/landingPage/DashboardLandingPage'),
  loading: DynamicRouteLoading,
});

const WidgetEditPage = loadable({
  loader: () => import('../dashboard/components/widgetEdit/WidgetEditPage'),
  loading: DynamicRouteLoading,
});

const DeepLinkHandler = loadable({
  loader: () => import('../components/__pages/DeepLinkLandingPage'),
  loading: DynamicRouteLoading,
});

const FeatureFlagsPage = loadable({
  loader: () => import('./../components/__pages/FeatureFlags'),
  loading: DynamicRouteLoading,
});

const LoginPage = loadable({
  loader: () => import('./../components/__pages/Login'),
  loading: DynamicRouteLoading,
});

const AuthorizeApplication = loadable({
  loader: () => import('../components/__pages/AuthorizeApplication'),
  loading: DynamicRouteLoading,
});

const AuthorizeApplicationLogin = loadable({
  loader: () =>
    import(
      '../components/__pages/AuthorizeApplication/sections/OauthLoginPage'
    ),
  loading: DynamicRouteLoading,
});

const AuthorizeApplicationConnect = loadable({
  loader: () =>
    import(
      '../components/__pages/AuthorizeApplication/sections/OauthConnectPage'
    ),
  loading: DynamicRouteLoading,
});

const AuthorizeApplicationError = loadable({
  loader: () =>
    import(
      '../components/__pages/AuthorizeApplication/sections/OauthErrorPage'
    ),
  loading: DynamicRouteLoading,
});

const ResetPassword = loadable({
  loader: () => import('./../components/__pages/ResetPassword'),
  loading: DynamicRouteLoading,
});

const CompletePasswordReset = loadable({
  loader: () => import('./../components/__pages/CompletePasswordReset'),
  loading: DynamicRouteLoading,
});

const SignUpPage = loadable({
  loader: () => import('./../components/__pages/SignUp'),
  loading: DynamicRouteLoading,
});

const ExpiredInvitePage = loadable({
  loader: () => import('./../components/__pages/SignUp/ExpiredInvitePage'),
  loading: DynamicRouteLoading,
});

const LockedOutPage = loadable({
  loader: () => import('./../components/__pages/SignUp/LockedOutPage'),
  loading: DynamicRouteLoading,
});

const PlatformConnectPage = loadable({
  loader: () => import('./../components/__pages/PlatformConnect'),
  loading: DynamicRouteLoading,
});

const FinalizeSsoLoginPage = loadable({
  loader: () => import('./../components/__pages/FinalizeSsoLogin'),
  loading: DynamicRouteLoading,
});

const FinalizeSsoLoginFromIframe = loadable({
  loader: () =>
    import(
      './../components/__pages/FinalizeSsoLogin/FinalizeSsoLoginFromIframe'
    ),
  loading: DynamicRouteLoading,
});

const FeedbackPage = loadable({
  loader: () => import('./../components/__pages/Feedback'),
  loading: DynamicRouteLoading,
});

const FeedbackRedirect = loadable({
  loader: () => import('./../components/__pages/Feedback/FeedbackRedirect'),
  loading: DynamicRouteLoading,
});

const FeedbackLandingPage = loadable({
  loader: () => import('../components/__pages/Feedback/FeedbackLanding'),
  loading: DynamicRouteLoading,
});

const PaymentPage = loadable({
  loader: () => import('./../components/__pages/Payment'),
  loading: DynamicRouteLoading,
});

const SingleAssetViewPage = loadable({
  loader: () => import('../creativeScoring/components/__pages/SingleAssetView'),
  loading: DynamicRouteLoading,
});

const EditorProfile = loadable({
  loader: () => import('./../components/__pages/EditorProfile'),
  loading: DynamicRouteLoading,
});

const NotFound404Page = loadable({
  loader: () => import('./../components/__pages/NotFound404'),
  loading: DynamicRouteLoading,
});

const CreatePage = loadable({
  loader: () => import('./../components/__pages/CreateProject'),
  loading: DynamicRouteLoading,
});

const CarouselExample = loadable({
  loader: () => import('./../components/__pages/CarouselExample'),
  loading: DynamicRouteLoading,
});

const MainView = loadable({
  loader: () => import('./../components/__views/Main'),
  loading: DynamicRouteLoading,
});

const CompletedProjects = loadable({
  loader: () => import('./../components/__subpages/CompletedProjects'),
  loading: DynamicRouteLoading,
});

const CreativeIntelligence = loadable({
  loader: () => import('../components/__views/CreativeAnalytics'),
  loading: DynamicRouteLoading,
});

const CICompliance = loadable({
  loader: () =>
    import('../creativeScoring/components/__subpages/CreativeScoring'),
  loading: DynamicRouteLoading,
});

const CIComplianceRollupReportsLanding = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/__subpages/CreativeScoringRollUpReportsLanding'
    ),
  loading: DynamicRouteLoading,
});

const CreativeScoringCriteriaManagementV2 = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/__subpages/CreativeScoringCriteriaManagement'
    ),
  loading: DynamicRouteLoading,
});

const CreativeScoringCriteriaCreate = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/__subpages/CreativeScoringCriteriaCreate'
    ),
  loading: DynamicRouteLoading,
});

const CreativeScoringCriteriaCreateV2 = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/__subpages/CreativeScoringCriteriaCreate/V2'
    ),
  loading: DynamicRouteLoading,
});

const CIComplianceSubmissionReport = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/__subpages/CreativeScoringBrandAuditReport'
    ),
  loading: DynamicRouteLoading,
});

const ScoringIndividualAssetViewV2 = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/individualAssetViewV2/ScoringIndividualAssetViewV2'
    ),
  loading: DynamicRouteLoading,
});

const ScoringIndividualAssetInflight = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/individualAssetViewV2/ScoringIndividualAssetViewInFlight'
    ),
  loading: DynamicRouteLoading,
});

const CreativeScoringRollUpReportPage = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/reports/rollUpReport/RollUpReportPage/RollUpReportPageWrapper'
    ),
  loading: DynamicRouteLoading,
});

const CreativeScoringDiversityReportPDF = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/reports/diversityReport/DiversityReportPDF/DiversityReportPDF'
    ),
  loading: DynamicRouteLoading,
});

const CICreativeManager = loadable({
  loader: () => import('../creativeAnalytics/__pages/CreativeManager'),
  loading: DynamicRouteLoading,
});

const CICreativeManagerPDF = loadable({
  loader: () =>
    import('../creativeAnalytics/__pages/CreativeManager/CreativeManagerPDF'),
  loading: DynamicRouteLoading,
});

const CICustomCompare = loadable({
  loader: () => import('../creativeAnalytics/__pages/CustomCompareReport'),
  loading: DynamicRouteLoading,
});

const CICustomComparePDF = loadable({
  loader: () =>
    import('../creativeAnalytics/__pages/CustomCompareReport/CustomComparePDF'),
  loading: DynamicRouteLoading,
});

const CIInsightsLibrary = loadable({
  loader: () =>
    import(
      '../creativeAnalytics/insights/components/InsightsV2/InsightsLibrary'
    ),
  loading: DynamicRouteLoading,
});

const IndividualCreativeView = loadable({
  loader() {
    import('../creativeAnalytics/reports/redux/individualCreativeView');
    return import(
      '../creativeAnalytics/__pages/IndividualCreativeView/IndividualCreativeViewWrapper'
    );
  },
  loading: DynamicRouteLoading,
});

const CurrentProject = loadable({
  loader: () => import('./../components/__subpages/CurrentProject'),
  loading: DynamicRouteLoading,
});

const CreatorSelection = loadable({
  loader: () => import('../creatorServices/components/CreatorSelection'),
  loading: DynamicRouteLoading,
});

const ActiveProjects = loadable({
  loader: () => import('./../components/__subpages/ActiveProjects'),
  loading: DynamicRouteLoading,
});

const Integrations = loadable({
  loader: () => import('./../components/__subpages/Integrations'),
  loading: DynamicRouteLoading,
});

const UserProfile = loadable({
  loader: () => import('./../components/__subpages/UserProfile'),
  loading: DynamicRouteLoading,
});

const PersonalInfo = loadable({
  loader: () => import('./../components/__subpages/PersonalInfo'),
  loading: DynamicRouteLoading,
});

const ExternalAccounts = loadable({
  loader: () => import('./../components/__subpages/ExternalAccounts'),
  loading: DynamicRouteLoading,
});

const ProfileAdAccounts = loadable({
  loader: () => import('./../components/__subpages/ProfileAdAccounts'),
  loading: DynamicRouteLoading,
});

const ProfileAccountGroups = loadable({
  loader: () => import('./../components/__subpages/ProfileAccountGroups'),
  loading: DynamicRouteLoading,
});

const ProfileCreativeGroups = loadable({
  loader: () => import('./../components/__subpages/ProfileCreativeGroups'),
  loading: DynamicRouteLoading,
});

const ProfileCustomTagGroups = loadable({
  loader: () => import('./../components/__subpages/ProfileCustomTagGroups'),
  loading: DynamicRouteLoading,
});

const Partner = loadable({
  loader: () => import('./../components/__subpages/Partner'),
  loading: DynamicRouteLoading,
});

const PartnerCreate = loadable({
  loader: () => import('../components/__subpages/PartnerCreateLandingPage'),
  loading: DynamicRouteLoading,
});

const PartnerDetails = loadable({
  loader: () => import('./../components/__subpages/PartnerDetails'),
  loading: DynamicRouteLoading,
});

const PartnerTeam = loadable({
  loader: () => import('./../components/__subpages/PartnerTeam'),
  loading: DynamicRouteLoading,
});

const PartnerAssetLocker = loadable({
  loader: () => import('./../components/__subpages/PartnerAssetLocker'),
  loading: DynamicRouteLoading,
});

const PartnerNotificationSettings = loadable({
  loader: () =>
    import('./../components/__subpages/PartnerNotificationSettings'),
  loading: DynamicRouteLoading,
});

const PartnerSsoSettings = loadable({
  loader: () => import('./../components/__subpages/PartnerSsoSettings'),
  loading: DynamicRouteLoading,
});

const NewNote = loadable({
  loader: () => import('./../components/__subpages/Notes/EditNote/NewNote'),
  loading: DynamicRouteLoading,
});

const ViewNote = loadable({
  loader: () => import('./../components/__subpages/Notes/ViewNote'),
  loading: DynamicRouteLoading,
});

const EditNote = loadable({
  loader: () => import('./../components/__subpages/Notes/EditNote'),
  loading: DynamicRouteLoading,
});

const Brief = loadable({
  loader: () => import('./../components/__subpages/ProjectBrief'),
  loading: DynamicRouteLoading,
});

const CurrentProjectDefault = loadable({
  loader: () => import('./../components/__subpages/CurrentProjectDefault'),
  loading: DynamicRouteLoading,
});

const Report = loadable({
  loader: () => import('../components/__subpages/Report'),
  loading: DynamicRouteLoading,
});

const Assets = loadable({
  loader: () => import('./../components/__subpages/Assets'),
  loading: DynamicRouteLoading,
});

const Notes = loadable({
  loader: () => import('./../components/__subpages/Notes'),
  loading: DynamicRouteLoading,
});

const Concepts = loadable({
  loader: () => import('./../components/__subpages/Concepts'),
  loading: DynamicRouteLoading,
});

const Outputs = loadable({
  loader: () =>
    import('../creativeProduction/components/OutputsV2/OutputTabWrapper'),
  loading: DynamicRouteLoading,
});

const Files = loadable({
  loader: () => import('./../components/__subpages/FinalFiles'),
  loading: DynamicRouteLoading,
});

const Team = loadable({
  loader: () => import('./../components/__subpages/Team'),
  loading: DynamicRouteLoading,
});

const EditorProfileSubPage = loadable({
  loader: () => import('./../components/__subpages/EditorProfileSubPage'),
  loading: DynamicRouteLoading,
});

const PortfolioMedia = loadable({
  loader: () => import('./../components/__subpages/PortfolioMedia'),
  loading: DynamicRouteLoading,
});

const StockContent = loadable({
  loader: () => import('./../components/__subpages/StockContent'),
  loading: DynamicRouteLoading,
});

const ProductTypeSelect = loadable({
  loader: () =>
    import('./../components/__pages/CreateProject/Sections/ProductTypeSelect'),
  loading: DynamicRouteLoading,
});

const BasicSection = loadable({
  loader: () =>
    import('./../components/__pages/CreateProject/Sections/BasicSection'),
  loading: DynamicRouteLoading,
});

const BriefSection = loadable({
  loader: () => import('./../components/__subpages/ProjectBrief'),
  loading: DynamicRouteLoading,
});

const OutputsSection = loadable({
  loader: () =>
    import('./../components/__pages/CreateProject/Sections/OutputsSection'),
  loading: DynamicRouteLoading,
});

const DataKeyDatesSection = loadable({
  loader: () =>
    import('../components/__pages/CreateProject/Sections/DataKeyDatesSection'),
  loading: DynamicRouteLoading,
});

const KeyDatesSection = loadable({
  loader: () =>
    import('../components/__pages/CreateProject/Sections/KeyDatesSection'),
  loading: DynamicRouteLoading,
});

const AddOnsSection = loadable({
  loader: () =>
    import('./../components/__pages/CreateProject/Sections/AddOnsSection'),
  loading: DynamicRouteLoading,
});

const AdAccountSection = loadable({
  loader: () =>
    import('./../components/__pages/CreateProject/Sections/AdAccountSection'),
  loading: DynamicRouteLoading,
});

const InviteTeam = loadable({
  loader: () =>
    import('./../components/__pages/CreateProject/Sections/InviteTeam'),
  loading: DynamicRouteLoading,
});

const FacebookMarketplace = loadable({
  loader: () => import('./../components/__pages/FacebookMarketplace'),
  loading: DynamicRouteLoading,
});

const FacebookMarketplaceConnect = loadable({
  loader: () => import('./../components/__pages/FacebookMarketplace/Connect'),
  loading: DynamicRouteLoading,
});

const FacebookMarketplaceSelect = loadable({
  loader: () => import('./../components/__pages/FacebookMarketplace/Select'),
  loading: DynamicRouteLoading,
});

const FacebookMarketplaceThanks = loadable({
  loader: () => import('./../components/__pages/FacebookMarketplace/Thanks'),
  loading: DynamicRouteLoading,
});

const TwitterCreateIntegrationLandingPage = loadable({
  loader: () =>
    import('../components/__pages/TwitterCreateIntegrationLandingPage'),
  loading: DynamicRouteLoading,
});

const TwitterCreateIntegrationSignUpPage = loadable({
  loader: () => import('../components/__pages/TwitterCreateIntegrationSignUp'),
  loading: DynamicRouteLoading,
});

const MobileFitnessRequest = loadable({
  loader: () => import('./../components/__pages/MobileFitnessRequest'),
  loading: DynamicRouteLoading,
});

const MobileFitnessRequestConnect = loadable({
  loader: () => import('./../components/__pages/MobileFitnessRequest/Connect'),
  loading: DynamicRouteLoading,
});

const MobileFitnessRequestSelect = loadable({
  loader: () =>
    import(
      '../components/__pages/MobileFitnessRequest/MobileFitnessRequestSelect'
    ),
  loading: DynamicRouteLoading,
});

const MobileFitnessRequestProcessing = loadable({
  loader: () =>
    import('./../components/__pages/MobileFitnessRequest/Processing'),
  loading: DynamicRouteLoading,
});

const MobileFitnessRequestResults = loadable({
  loader: () =>
    import('./../components/__pages/MobileFitnessRequest/MobileFitnessResults'),
  loading: DynamicRouteLoading,
});

const TryLandingPage = loadable({
  loader: () => import('./../components/__pages/TryLandingPage/TryLandingPage'),
  loading: DynamicRouteLoading,
});

const OrganizationSwitcherPage = loadable({
  loader: () => import('./../components/__pages/OrganizationSwitcher'),
  loading: DynamicRouteLoading,
});

const ApiKeyManagement = loadable({
  loader: () => import('./../platform/__pages/ApiKeyManagement'),
  loading: DynamicRouteLoading,
});

const DataExports = loadable({
  loader: () => import('./../platform/__pages/DataExports'),
  loading: DynamicRouteLoading,
});

const DataConnectors = loadable({
  loader: () => import('../platform/__pages/Connectors'),
  loading: DynamicRouteLoading,
});

const OrganizationSecurity = loadable({
  loader: () => import('./../platform/__pages/SsoSettings'),
  loading: DynamicRouteLoading,
});

const BriefComponent = ({ currentProject }) => (
  <Brief project={currentProject} />
);
BriefComponent.propTypes = {
  currentProject: objectOf(any).isRequired,
};

const ProjectCreateV2 = loadable({
  loader: () =>
    import('../creativeProduction/components/ProjectCreateV2/ProjectCreateV2'),
  loading: DynamicRouteLoading,
});

const ProjectCreateAdminDetails = loadable({
  loader: () =>
    import(
      '../creativeProduction/components/ProjectCreateV2/Sections/AdminProjectDetails'
    ),
  loading: DynamicRouteLoading,
});

const ProjectCreateDetails = loadable({
  loader: () =>
    import(
      '../creativeProduction/components/ProjectCreateV2/Sections/ProjectDetails'
    ),
  loading: DynamicRouteLoading,
});

const ProjectCreateTeam = loadable({
  loader: () =>
    import(
      '../creativeProduction/components/ProjectCreateV2/Sections/ProjectTeam'
    ),
  loading: DynamicRouteLoading,
});

const ProjectCreateServices = loadable({
  loader: () =>
    import(
      '../creativeProduction/components/ProjectCreateV2/Sections/CreativeServices'
    ),
  loading: DynamicRouteLoading,
});

const ProjectCreateOutputs = loadable({
  loader: () =>
    import(
      '../creativeProduction/components/ProjectCreateV2/Sections/CreativeOutputs'
    ),
  loading: DynamicRouteLoading,
});

const ProjectCreateDates = loadable({
  loader: () =>
    import(
      '../creativeProduction/components/ProjectCreateV2/Sections/DeliveryDate'
    ),
  loading: DynamicRouteLoading,
});

const ScoreOverridesDashboard = loadable({
  loader: () =>
    import('../creativeScoring/components/__subpages/ScoreOverridesDashboard'),
  loading: DynamicRouteLoading,
});

const Workspaces = loadable({
  loader: () => import('../userManagement/__pages/Workspaces'),
  loading: DynamicRouteLoading,
});

const WorkspaceDetails = loadable({
  loader: () => import('../userManagement/__pages/Workspace'),
  loading: DynamicRouteLoading,
});

const WorkspaceAdAccounts = loadable({
  loader: () => import('../userManagement/__pages/AdAccounts'),
  loading: DynamicRouteLoading,
});

const Peoples = loadable({
  loader: () => import('../userManagement/__pages/Peoples'),
  loading: DynamicRouteLoading,
});

const AdAccountHealthDashboard = loadable({
  loader: () => import('../userManagement/__pages/AdAccountHealthDashboard'),
  loading: DynamicRouteLoading,
});

const BrandLabels = loadable({
  loader: () => import('../userManagement/__pages/BrandLabels'),
  loading: DynamicRouteLoading,
});

const ScorecardLanding = loadable({
  loader: () =>
    import(
      '../creativeScoring/components/__subpages/CreativeScoringScorecardsLanding'
    ),
  loading: DynamicRouteLoading,
});

const PreFlightCheck = loadable({
  loader: () =>
    import('../creativeScoring/components/__subpages/PreFlightCheck'),
  loading: DynamicRouteLoading,
});

const CICreativeLeaderboard = loadable({
  loader: () => import('../creativeAnalytics/__pages/CreativeLeaderboard'),
  loading: DynamicRouteLoading,
});

const CICreativeLeaderboardPDF = loadable({
  loader: () =>
    import(
      '../creativeAnalytics/__pages/CreativeLeaderboard/CreativeLeaderboardPDF'
    ),
  loading: DynamicRouteLoading,
});

const CISavedReports = loadable({
  loader: () => import('../creativeAnalytics/__pages/SavedReports'),
  loading: DynamicRouteLoading,
});

const CIImpactReport = loadable({
  loader: () => import('../creativeAnalytics/__pages/ImpactReport'),
  loading: DynamicRouteLoading,
});

const CIImpactReportPDF = loadable({
  loader: () =>
    import('../creativeAnalytics/__pages/ImpactReport/ImpactReportPDF'),
  loading: DynamicRouteLoading,
});

const CICriteriaPerformanceReport = loadable({
  loader: () =>
    import('../creativeAnalytics/__pages/CriteriaPerformanceReport'),
  loading: DynamicRouteLoading,
});

const InsightCopilot = loadable({
  loader: () => import('../insights-copilot'),
  loading: DynamicRouteLoading,
});

const CreativeLifecycleReportPOC = loadable({
  loader: () =>
    import(
      '../creativeAnalytics/__pages/CreativeLifecycleReportPOC/CreativeLifecycleReport'
    ),
  loading: DynamicRouteLoading,
});

/**
 * Site routes,
 * contains the entire site routes mapped to their respective components. Contains list of route objects with information
 * used to be passed to <Route/>, <PrivateRoute/>, <CurrentProjectRoute/> or <Navigate/> depending on the properties set
 * ------------------ // ------------------ // ------------------ // ------------------ // ------------------
 * # HOW TO ADD A NEW ROUTE
 * If the route you want to render is not at the App.jsx/root level, find the parent route under to which to add.
 * If the parent has "routes" specified, add it to the list following the next section rules, otherwise
 * specify the parent "routes" property.
 * Use "routesRenderer.jsx" to render the parent routes. The routes will be injected in the parent component
 * that will be available through props.
 * ------------------ // ------------------ // ------------------ // ------------------ // ------------------
 * # RULES WHEN LISTING ROUTES
 * !!! - list the routes from exact=true to exact=false, meaning, exact=true routes come first
 * !!! - ONLY 1 isRedirect=true route per list
 * !!! - list isRedirect route ALWAYS last
 * !!! - path, exact, component and render follow same rules as react-router-dom <Route/> https://reacttraining.com/react-router/web/api/Route
 * ------------------ // ------------------ // ------------------ // ------------------ // ------------------
 * # ALL POSSIBLE ROUTE PROPERTIES:
 * - path (required): the url of where to render the component
 * - exact (required): flag whether the url needs to be an exact match or not
 * - component (required if render not present): the component to be rendered
 * - render (required if component not present): a function that renders/returns a component
 * - isPrivate: flag whether the path requires authentication (user to be logged in). Uses the <PrivateRoute/> if true
 * - withCurrentProject: flag whether the path requires project to be loaded first. Uses the <CurrentProjectRoute/> if true
 * - isAnalyticsRoute: A route that requires the analytics user to be loaded. Also begins loading KPIs. Uses <AnalyticsDataRoute/> if true
 * - isOptionalAnalyticsRoute: A route that requires that we start loading the analytics user, and KPIs. Does not guarantee that loading is complete.
 * - isPlatformAccountsRoute: A route that requires the user's platformAccounts to be loaded (possible dozens of API calls). Uses <PlatformAccountsRoute/> if true
 * - isOptionalPlatformAccountsRoute: A route that requires we start loading the user's platformAccounts, but does not guarantee that loading is complete.
 * - isMobileFriendly: If set to true the component <ModalScreenSizeWarning> will not be shown.
 * - redirectToPath (required if *isRedirect or *isPrivate is present): a path to redirect to if no match.
 * - routes: an array of children routes
 * - isRedirect: flag whether the route is a redirect type. Uses <Navigate/> if true
 * !!! - You don't need to include a property if it is false or nil
 * ------------------ // ------------------ // ------------------ // ------------------ // ------------------
 * REQUIRED PROPERTIES: path, exact, component or render
 */

const routes = [
  {
    path: siteMap.carouselExample,
    exact: true,
    component: CarouselExample,
    titleMessageId: 'ui.site.pageTitle.example.carousel',
  },
  {
    path: siteMap.branchLandingFailed,
    exact: true,
    component: ExpiredInvitePage,
  },
  {
    path: siteMap.deeplink,
    exact: true,
    component: DeepLinkHandler,
  },
  {
    path: siteMap.featureFlags,
    exact: true,
    component: FeatureFlagsPage,
    titleMessageId: 'ui.site.pageTitle.featureFlags',
  },
  {
    path: siteMap.notFound,
    exact: true,
    component: NotFound404Page,
    titleMessageId: 'ui.site.pageTitle.404',
  },
  {
    path: siteMap.login,
    exact: true,
    component: LoginPage,
    titleMessageId: 'ui.site.pageTitle.login',
  },
  {
    path: siteMap.organizationSwitcher,
    exact: true,
    component: OrganizationSwitcherPage,
    titleMessageId: 'ui.organizationSwitcher.description',
    isPrivate: true,
    redirectToPath: siteMap.login,
  },
  {
    path: siteMap.externalOauth,
    component: AuthorizeApplication,
    isMobileFriendly: true,
    routes: [
      {
        path: parseURL(siteMap.externalOauth, {
          step: routeParams.sections.externalOauth.login,
        }),
        exact: true,
        component: AuthorizeApplicationLogin,
        titleMessageId: 'ui.site.pageTitle.oauth.login',
      },
      {
        path: parseURL(siteMap.externalOauth, {
          step: routeParams.sections.externalOauth.connect,
        }),
        exact: true,
        component: AuthorizeApplicationConnect,
        titleMessageId: 'ui.site.pageTitle.oauth.connect',
      },
      {
        path: parseURL(siteMap.externalOauth, {
          step: routeParams.sections.externalOauth.error,
        }),
        exact: true,
        component: AuthorizeApplicationError,
        titleMessageId: 'ui.site.pageTitle.oauth.error',
      },
    ],
  },
  {
    path: siteMap.resetPassword,
    exact: true,
    component: ResetPassword,
    titleMessageId: 'ui.site.pageTitle.resetPassword',
  },
  {
    path: siteMap.updatePassword,
    exact: true,
    component: CompletePasswordReset,
  },
  {
    path: siteMap.signUp,
    exact: true,
    component: SignUpPage,
    routes: [],
  },
  {
    path: siteMap.lockedOutPage,
    exact: true,
    component: LockedOutPage,
  },
  {
    path: siteMap.twitterCreateLandingPage,
    component: TwitterCreateIntegrationLandingPage,
    exact: true,
    isMobileFriendly: true,
    titleMessageId: 'ui.site.pageTitle.twitter.landing',
  },
  {
    path: siteMap.twitterCreateLandingPageSignUp,
    exact: true,
    titleMessageId: 'ui.site.pageTitle.signUp.twitter',
    component: TwitterCreateIntegrationSignUpPage,
  },
  {
    path: siteMap.facebookMarketplace,
    component: FacebookMarketplace,
    isMobileFriendly: true,
    titleMessageId: 'ui.site.pageTitle.facebookAppMarketPlace.landing',
    metaDescriptionId: 'ui.site.metaDescription.facebookAppMarketPlace.landing',
    openGraphTags: PAGE_META_TAGS.OPEN_GRAPH_TAGS.FACEBOOK_APP_MARKET_PLACE,
    routes: [
      {
        path: siteMap.facebookMarketplace,
        exact: true,
        component: FacebookMarketplaceConnect,
      },
      {
        path: siteMap.facebookMarketplaceSelect,
        exact: true,
        component: FacebookMarketplaceSelect,
      },
      {
        path: siteMap.facebookMarketplaceThanks,
        exact: true,
        component: FacebookMarketplaceThanks,
      },
    ],
  },
  {
    path: siteMap.mobileFitnessRequest,
    component: MobileFitnessRequest,
    isMobileFriendly: true,
    titleMessageId: 'ui.site.pageTitle.mobileFitnessRequest.landing',
    metaDescriptionId: 'ui.site.metaDescription.mobileFitnessRequest.landing',
    openGraphTags: PAGE_META_TAGS.OPEN_GRAPH_TAGS.HOOKS_MOBILE_FITNESS_REQUEST,
    metaTags:
      PAGE_META_TAGS.PAGE_SPECIFIC_META_TAGS.HOOKS_MOBILE_FITNESS_REQUEST,
    routes: [
      {
        path: siteMap.mobileFitnessRequest,
        exact: true,
        component: MobileFitnessRequestConnect,
      },
      {
        path: siteMap.mobileFitnessRequestSelect,
        exact: true,
        component: MobileFitnessRequestSelect,
      },
      {
        path: siteMap.mobileFitnessRequestProcessing,
        exact: true,
        component: MobileFitnessRequestProcessing,
      },
      {
        path: siteMap.mobileFitnessRequestResults,
        exact: true,
        component: MobileFitnessRequestResults,
      },
    ],
  },
  ...[siteMap.connect, siteMap.snapCode, siteMap.authresult].map((route) => ({
    path: route,
    exact: true,
    isMobileFriendly: true,
    component: PlatformConnectPage,
    titleMessageId: 'ui.site.pageTitle.platformConnect',
  })),
  {
    path: siteMap.authresult,
    exact: true,
    isMobileFriendly: true,
    component: PlatformConnectPage,
    titleMessageId: 'ui.site.pageTitle.platformConnect',
  },
  {
    path: siteMap.finalizeSsoLogin,
    exact: true,
    isMobileFriendly: true,
    component: FinalizeSsoLoginPage,
    titleMessageId: 'ui.site.pageTitle.finalizeSsoLogin',
  },
  {
    path: siteMap.finalizeSsoLoginFromIframe,
    exact: true,
    isMobileFriendly: true,
    component: FinalizeSsoLoginFromIframe,
    titleMessageId: 'ui.site.pageTitle.finalizeSsoLogin',
  },
  {
    path: siteMap.feedbackPublic,
    exact: true,
    component: FeedbackPage,
    titleMessageId: 'ui.site.pageTitle.feedback',
  },
  {
    path: siteMap.editorProfileBase,
    exact: true,
    component: EditorProfile,
    routes: [
      {
        path: siteMap.editorPortfolioMedia,
        exact: true,
        component: PortfolioMedia,
        titleMessageId: 'ui.site.pageTitle.editor.portfolio',
      },
      {
        path: siteMap.editorProfile,
        exact: true,
        component: EditorProfileSubPage,
        titleMessageId: 'ui.site.pageTitle.editor.profile',
      },
      {
        isRedirect: true,
        redirectToPath: siteMap.notFound,
      },
    ],
  },
  {
    path: siteMap.editorProfileLegacy,
    exact: true,
    component: EditorProfile,
    routes: [
      {
        path: siteMap.editorProfileLegacy,
        exact: true,
        isRedirect: true,
        redirectToPath: parseURL(siteMap.editorProfile, {
          editorId: routeParams.editorId,
        }),
      },
    ],
  },
  {
    path: siteMap.main,
    exact: false,
    component: MainPage,
    isPrivate: true,
    redirectToPath: siteMap.login,
    routes: [
      {
        path: siteMap.feedbackLanding,
        withCurrentProject: true,
        exact: true,
        component: FeedbackLandingPage,
        titleMessageId: 'ui.site.pageTitle.feedback',
      },
      {
        path: siteMap.feedback,
        exact: true,
        component: FeedbackRedirect,
        titleMessageId: 'ui.site.pageTitle.feedback',
      },
      {
        path: siteMap.feedbackWithProject,
        withCurrentProject: true,
        exact: true,
        component: FeedbackPage,
        titleMessageId: 'ui.site.pageTitle.feedback',
      },
      {
        path: siteMap.creativeScoringSingleAssetView,
        exact: true,
        component: SingleAssetViewPage,
        titleMessageId: 'ui.site.pageTitle.SingleAssetView',
      },
      {
        path: siteMap.diversityReportPDF,
        exact: true,
        component: CreativeScoringDiversityReportPDF,
        titleMessageId: 'ui.site.pageTitle.diversityReportPDF',
      },
      {
        path: siteMap.creativeIntelligenceCreativeManagerPDF,
        exact: true,
        component: CICreativeManagerPDF,
        titleMessageId: 'ui.site.pageTitle.ci.analytics.preview',
      },
      {
        path: siteMap.creativeIntelligenceCustomComparePDF,
        exact: true,
        component: CICustomComparePDF,
        titleMessageId: 'ui.site.pageTitle.ci.analytics.preview',
      },
      {
        path: siteMap.creativeIntelligenceImpactReportPDF,
        exact: true,
        component: CIImpactReportPDF,
        titleMessageId: 'ui.site.pageTitle.ci.analytics.preview',
      },
      {
        path: siteMap.creativeIntelligenceCreativeLeaderboardPDF,
        exact: true,
        component: CICreativeLeaderboardPDF,
        titleMessageId: 'ui.site.pageTitle.ci.analytics.preview',
      },
      ...[siteMap.createProject, siteMap.editProject].map((route) => ({
        path: route,
        exact: false,
        component: CreatePage,
        routes: [
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.productTypeSelect,
              productType: null,
            }),
            exact: true,
            component: ProductTypeSelect,
            titleMessageId: 'ui.site.pageTitle.create.productType',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.basics,
            }),
            exact: true,
            component: BasicSection,
            titleMessageId: 'ui.site.pageTitle.create.basicInfo',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.brief,
            }),
            exact: true,
            component: BriefSection,
            titleMessageId: 'ui.site.pageTitle.create.brief',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.outputs,
            }),
            exact: true,
            component: OutputsSection,
            titleMessageId: 'ui.site.pageTitle.create.outputs',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.keyDates,
              productType: routeParams.sections.projectCreate.productTypes.data,
            }),
            exact: true,
            component: DataKeyDatesSection,
            titleMessageId: 'ui.site.pageTitle.create.keyDatesData',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.keyDates,
            }),
            exact: true,
            component: KeyDatesSection,
            titleMessageId: 'ui.site.pageTitle.create.keyDatesCreative',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.addOns,
            }),
            exact: true,
            component: AddOnsSection,
            titleMessageId: 'ui.site.pageTitle.create.addOns',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.adAccount,
              productType: routeParams.sections.projectCreate.productTypes.data,
            }),
            exact: true,
            component: AdAccountSection,
            titleMessageId: 'ui.site.pageTitle.create.adAccounts',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreate.inviteTeam,
            }),
            exact: true,
            component: InviteTeam,
            titleMessageId: 'ui.site.pageTitle.create.inviteTeam',
          },
        ],
      })),
      ...[siteMap.projectCreateV2, siteMap.projectEdit].map((route) => ({
        path: route,
        exact: false,
        component: ProjectCreateV2,
        routes: [
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreateV2.adminProjectDetails,
            }),
            exact: true,
            component: ProjectCreateAdminDetails,
            titleMessageId: 'ui.site.pageTitle.projectCreate.admin.details',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreateV2.projectDetails,
            }),
            exact: true,
            component: ProjectCreateDetails,
            titleMessageId: 'ui.site.pageTitle.projectCreate.details',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreateV2.projectTeam,
            }),
            exact: true,
            component: ProjectCreateTeam,
            titleMessageId: 'ui.site.pageTitle.projectCreate.team',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreateV2.creativeServices,
            }),
            exact: true,
            component: ProjectCreateServices,
            titleMessageId: 'ui.site.pageTitle.projectCreate.services',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreateV2.creativeOutputs,
            }),
            exact: true,
            component: ProjectCreateOutputs,
            titleMessageId: 'ui.site.pageTitle.projectCreate.outputs',
          },
          {
            path: parseURL(route, {
              step: routeParams.sections.projectCreateV2.keyDates,
            }),
            exact: true,
            component: ProjectCreateDates,
            titleMessageId: 'ui.site.pageTitle.projectCreate.dates',
          },
        ],
      })),
      {
        path: siteMap.payment,
        exact: true,
        component: PaymentPage,
        withCurrentProject: true,
        titleMessageId: 'ui.site.pageTitle.payment',
      },
      {
        path: siteMap.createPartner,
        exact: true,
        component: PartnerCreate,
        titleMessageId: 'ui.site.pageTitle.partner.create',
      },
      {
        path: siteMap.main,
        exact: false,
        component: MainView,
        isAnalyticsRoute: true,
        routes: [
          {
            path: siteMap.home,
            exact: true,
            component: getFeatureFlag('isDashboardEnabled')
              ? DashboardLandingPage
              : Home,
          },
          {
            path: siteMap.executiveDashboard,
            exact: true,
            component: DashboardPage,
            titleMessageId: 'ui.site.pageTitle.dashboard',
          },
          {
            path: siteMap.widgetEdit,
            exact: true,
            component: WidgetEditPage,
            titleMessageId: 'ui.site.pageTitle.widgetEdit',
          },
          {
            path: siteMap.try,
            exact: true,
            component: TryLandingPage,
            titleMessageId: 'ui.site.pageTitle.try',
          },
          {
            path: siteMap.organizationApiKeys,
            exact: true,
            component: ApiKeyManagement,
            titleMessageId: 'ui.site.pageTitle.apiKeyManagement',
          },
          {
            path: siteMap.organizationDataExports,
            exact: true,
            component: DataExports,
            titleMessageId: 'ui.site.pageTitle.dataExports',
          },
          {
            path: siteMap.organizationDataConnectors,
            exact: true,
            component: DataConnectors,
            titleMessageId: 'ui.site.pageTitle.dataConnectors',
          },
          {
            path: siteMap.organizationSecurity,
            exact: true,
            component: OrganizationSecurity,
            titleMessageId: 'ui.site.pageTitle.singleSignOn',
          },
          {
            path: siteMap.workspaceList,
            exact: true,
            component: Workspaces,
            titleMessageId: 'ui.site.pageTitle.workspace',
          },
          {
            path: siteMap.workspaceDetails,
            exact: true,
            component: WorkspaceDetails,
            titleMessageId: 'ui.site.pageTitle.workspace.details',
          },
          {
            path: siteMap.workspaceAdAccount,
            exact: true,
            component: WorkspaceAdAccounts,
            titleMessageId: 'ui.site.pageTitle.workspace.adAccounts',
          },
          {
            path: siteMap.people,
            exact: true,
            component: Peoples,
            titleMessageId: 'ui.site.pageTitle.peoples',
          },
          {
            path: siteMap.organizationIntegrations,
            exact: true,
            component: Integrations,
            titleMessageId: 'ui.site.pageTitle.userProfile.integrations',
          },
          {
            path: siteMap.adAccountHealthDashboard,
            exact: true,
            component: AdAccountHealthDashboard,
            titleMessageId: 'ui.site.pageTitle.manage',
          },
          {
            path: siteMap.brandLabels,
            exact: true,
            component: BrandLabels,
            titleMessageId: 'ui.site.pageTitle.brandLabels',
          },
          {
            path: siteMap.activeProjects,
            exact: true,
            component: ActiveProjects,
            titleMessageId: 'ui.site.pageTitle.project.listActive',
          },
          {
            path: siteMap.completedProjects,
            exact: true,
            component: CompletedProjects,
            titleMessageId: 'ui.site.pageTitle.project.listCompleted',
          },
          {
            path: siteMap.creativeAnalyticsIndividualCreativeView,
            exact: true,
            component: IndividualCreativeView,
            titleMessageId:
              'ui.site.pageTitle.creativeAnalytics.mediaTrackView',
          },
          {
            path: siteMap.creativeIntelligence,
            isAnalyticsRoute: true,
            component: CreativeIntelligence,
            routes: [
              {
                path: siteMap.creativeScoringScoreCardDetails,
                exact: true,
                component: PreFlightCheck,
                titleMessageId:
                  'ui.site.pageTitle.ci.compliance.scorecardDetails',
              },
              {
                path: siteMap.creativeIntelligenceScoreOverrides,
                exact: true,
                component: ScoreOverridesDashboard,
                titleMessageId: 'ui.creativeScoring.scoreOverrides.pageTitle',
              },
              {
                path: siteMap.creativeIntelligenceCompliance,
                exact: true,
                component: CICompliance,
                routes: [
                  {
                    path: parseURL(siteMap.creativeIntelligenceCompliance, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .brandScore,
                    }),
                    exact: true,
                    render: ({ match }) => (
                      <Navigate
                        to={`/creativeIntelligence/creative-scoring/scorecard/${match.params.batchId}`}
                      />
                    ),
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.brandContentAudit',
                  },
                  {
                    path: parseURL(siteMap.creativeIntelligenceCompliance, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .criteriaManagement,
                    }),
                    exact: true,
                    component: CreativeScoringCriteriaManagementV2,
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.criteriaManagement',
                  },
                  // {
                  //   path: parseURL(siteMap.creativeScoringRollUpReport),
                  //   exact: true,
                  //   component: CreativeScoringDiversityReportPage,
                  //   titleMessageId: 'ui.creativeScoring.rollUpReport.pageTitle',
                  // },
                  {
                    path: parseURL(siteMap.creativeScoringRollUpReport),
                    exact: true,
                    component: CreativeScoringRollUpReportPage,
                    titleMessageId: 'ui.creativeScoring.rollUpReport.pageTitle',
                  },
                  {
                    path: parseURL(siteMap.creativeIntelligenceCompliance, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .rollupReports,
                    }),
                    exact: true,
                    component: CIComplianceRollupReportsLanding,
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.rollupReports',
                  },
                  {
                    path: parseURL(siteMap.creativeScoringScoreCardLanding, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .scorecards.preFlight,
                    }),
                    exact: true,
                    component: ScorecardLanding,
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.scorecardsLanding.preFlight',
                  },
                  {
                    path: parseURL(siteMap.creativeScoringScoreCardLanding, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .scorecards.inFlight,
                    }),
                    exact: true,
                    component: ScorecardLanding,
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.scorecardsLanding.inFlight',
                  },
                  {
                    path: parseURL(siteMap.creativeIntelligenceCompliance, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .submissionReport,
                    }),
                    exact: true,
                    component: CIComplianceSubmissionReport,
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.submissionReport',
                  },
                  {
                    path: parseURL(siteMap.creativeIntelligenceCompliance, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .scorecardsLanding,
                    }),
                    exact: true,
                    component: ScorecardLanding,
                    titleMessageId: 'ui.site.pageTitle.ci.compliance.reports',
                  },
                  {
                    path: parseURL(siteMap.creativeIntelligenceCompliance, {
                      tab: routeParams.tabs.creativeIntelligenceCompliance
                        .criteriaCreate,
                    }),
                    exact: true,
                    component: CreativeScoringCriteriaCreate,
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.criteriaCreate',
                  },
                  {
                    path: '/creativeIntelligence/creative-scoring/V2/criteria-create',
                    exact: true,
                    component: CreativeScoringCriteriaCreateV2,
                    titleMessageId:
                      'ui.site.pageTitle.ci.compliance.criteriaCreate',
                  },
                  {
                    isRedirect: true,
                    redirectToPath: parseURL(
                      siteMap.creativeIntelligenceCompliance,
                      {
                        tab: routeParams.tabs.creativeIntelligenceCompliance
                          .executiveDashboard,
                      },
                    ),
                  },
                ],
              },
              {
                path: siteMap.creativeIntelligenceCreativeManager,
                exact: true,
                component: CICreativeManager,
                titleMessageId: 'ui.site.pageTitle.ci.creativeManager',
              },
              {
                path: siteMap.creativeIntelligenceInsightsLibrary,
                exact: true,
                component: CIInsightsLibrary,
                titleMessageId: 'ui.site.pageTitle.ci.insightsLibrary',
              },
              {
                path: siteMap.creativeIntelligenceCreativeLeaderboard,
                exact: true,
                component: CICreativeLeaderboard,
                titleMessageId: 'ui.site.pageTitle.ci.creativeLeaderboard',
              },
              {
                path: siteMap.insightCopilotRoute,
                component: InsightCopilot,
                titleMessageId: 'ui.site.pageTitle.insight.copilot',
                exact: true,
              },
              {
                path: siteMap.creativeLifecycle,
                exact: true,
                component: CreativeLifecycleReportPOC,
                titleMessageId: 'ui.site.pageTitle.creativeLifecycle',
              },
              {
                path: siteMap.creativeIntelligenceSavedReports,
                exact: true,
                component: CISavedReports,
                titleMessageId: 'ui.site.pageTitle.ci.savedReports',
              },
              {
                path: siteMap.creativeIntelligenceImpactReport,
                exact: true,
                component: CIImpactReport,
                titleMessageId: 'ui.site.pageTitle.ci.savedReports',
              },
              {
                path: siteMap.creativeIntelligenceCriteriaPerformance,
                exact: true,
                component: CICriteriaPerformanceReport,
                titleMessageId: 'ui.site.pageTitle.ci.criteriaPerformance',
              },
              {
                path: siteMap.creativeAnalyticsIndividualCreativeView,
                exact: true,
                component: IndividualCreativeView,
                titleMessageId:
                  'ui.site.pageTitle.creativeAnalytics.mediaTrackView',
              },
              {
                path: siteMap.creativeIntelligenceCustomCompareNew,
                exact: true,
                component: CICustomCompare,
                titleMessageId: 'ui.site.pageTitle.ci.compare.custom',
              },
              {
                path: siteMap.creativeIntelligenceCustomCompareDeprecated,
                exact: true,
                component: CICustomCompare,
                titleMessageId: 'ui.site.pageTitle.ci.compare.custom',
              },
              {
                path: parseURL(
                  siteMap.creativeIntelligenceSingleAssetAdAccount,
                  {
                    tab: routeParams.tabs.creativeIntelligenceCompliance
                      .singleAssetView,
                  },
                ),
                exact: true,
                component: ScoringIndividualAssetViewV2,
                titleMessageId:
                  'ui.site.pageTitle.ci.compliance.singleAssetView',
              },
              {
                path: parseURL(siteMap.creativeIntelligenceSingleAssetBatch, {
                  tab: routeParams.tabs.creativeIntelligenceCompliance
                    .singleAssetView,
                }),
                exact: true,
                component: ScoringIndividualAssetViewV2,
                titleMessageId:
                  'ui.site.pageTitle.ci.compliance.singleAssetView',
              },
              {
                path: parseURL(
                  siteMap.creativeIntelligenceSingleAssetInflight,
                  {
                    tab: routeParams.tabs.creativeIntelligenceCompliance
                      .singleAssetView,
                  },
                ),
                exact: true,
                component: ScoringIndividualAssetInflight,
                titleMessageId:
                  'ui.site.pageTitle.ci.compliance.singleAssetViewInflight',
              },
            ],
          },
          {
            path: siteMap.userProfile,
            exact: true,
            isOptionalAnalyticsRoute: true,
            component: UserProfile,
            routes: [
              {
                path: parseURL(siteMap.userProfile, {
                  tab: routeParams.tabs.profile.personal,
                }),
                exact: true,
                component: PersonalInfo,
                titleMessageId: 'ui.site.pageTitle.userProfile.personalInfo',
              },
              {
                path: parseURL(siteMap.userProfile, {
                  tab: routeParams.tabs.profile.plugIns,
                }),
                exact: true,
                component: ExternalAccounts,
              },
              {
                path: parseURL(siteMap.userProfile, {
                  tab: routeParams.tabs.profile.adAccounts,
                }),
                exact: true,
                component: ProfileAdAccounts,
              },
              {
                path: parseURL(siteMap.userProfile, {
                  tab: routeParams.tabs.profile.accountGroups,
                }),
                exact: true,
                component: ProfileAccountGroups,
                isAnalyticsRoute: true,
                titleMessageId: 'ui.site.pageTitle.userProfile.accountGroups',
              },
              {
                path: parseURL(siteMap.userProfile, {
                  tab: routeParams.tabs.profile.creativeGroups,
                }),
                exact: true,
                component: ProfileCreativeGroups,
                isAnalyticsRoute: true,
                titleMessageId: 'ui.site.pageTitle.userProfile.creativeGroups',
              },
              {
                path: parseURL(siteMap.userProfile, {
                  tab: routeParams.tabs.profile.tagGroups,
                }),
                exact: true,
                component: ProfileCustomTagGroups,
                isAnalyticsRoute: true,
                titleMessageId: 'ui.site.pageTitle.userProfile.creativeGroups',
              },
              {
                isRedirect: true,
                redirectToPath: siteMap.notFound,
              },
            ],
          },
          {
            path: siteMap.partner,
            exact: true,
            component: Partner,
            routes: [
              {
                path: parseURL(siteMap.partner, {
                  tab: routeParams.tabs.partner.details,
                }),
                exact: true,
                component: PartnerDetails,
                titleMessageId: 'ui.site.pageTitle.partner.details',
              },
              {
                path: parseURL(siteMap.partner, {
                  tab: routeParams.tabs.partner.team,
                }),
                exact: true,
                component: PartnerTeam,
                titleMessageId: 'ui.site.pageTitle.partner.team',
              },
              {
                path: parseURL(siteMap.partner, {
                  tab: routeParams.tabs.partner['asset-locker'],
                }),
                exact: true,
                component: PartnerAssetLocker,
                titleMessageId: 'ui.site.pageTitle.partner.assetLocker',
              },
              {
                path: parseURL(siteMap.partner, {
                  tab: routeParams.tabs.partner.notifications,
                }),
                exact: true,
                component: PartnerNotificationSettings,
                titleMessageId: 'ui.site.pageTitle.partner.notifications',
              },
              {
                path: parseURL(siteMap.partner, {
                  tab: routeParams.tabs.partner['single-sign-on'],
                }),
                exact: true,
                component: PartnerSsoSettings,
                titleMessageId: 'ui.site.pageTitle.partner.singleSignOn',
              },
              {
                isRedirect: true,
                redirectToPath: parseURL(siteMap.partner, {
                  tab: routeParams.tabs.partner.team,
                }),
              },
            ],
          },
          {
            path: siteMap.projectNoteNew,
            exact: true,
            component: NewNote,
            withCurrentProject: true,
            titleMessageId: 'ui.site.pageTitle.project.note.new',
          },
          {
            path: siteMap.projectNoteEdit,
            exact: true,
            component: EditNote,
            withCurrentProject: true,
            titleMessageId: 'ui.site.pageTitle.project.note.edit',
          },
          {
            path: siteMap.projectNoteView,
            exact: true,
            component: ViewNote,
            withCurrentProject: true,
            titleMessageId: 'ui.site.pageTitle.project.note.view',
          },
          {
            path: siteMap.stockContent,
            exact: true,
            component: StockContent,
            withCurrentProject: true,
            titleMessageId: 'ui.site.pageTitle.project.stockContent',
          },
          {
            path: siteMap.creatorSelection,
            exact: true,
            component: CreatorSelection,
            withCurrentProject: true,
          },
          {
            path: siteMap.currentProject,
            exact: false,
            component: CurrentProject,
            withCurrentProject: true,
            routes: [
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.brief,
                }),
                exact: true,
                render: BriefComponent,
                titleMessageId: 'ui.site.pageTitle.project.brief',
              },
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.report,
                }),
                exact: true,
                component: Report,
                titleMessageId: 'ui.site.pageTitle.project.report',
              },
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.assets,
                }),
                exact: true,
                component: Assets,
                titleMessageId: 'ui.site.pageTitle.project.assets',
              },
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.notes,
                }),
                exact: true,
                component: Notes,
                titleMessageId: 'ui.site.pageTitle.project.notes',
              },
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.concepts,
                }),
                exact: true,
                component: Concepts,
                titleMessageId: 'ui.site.pageTitle.project.concepts',
              },
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.outputs,
                }),
                exact: true,
                component: Outputs,
                titleMessageId: 'ui.site.pageTitle.project.outputs',
              },
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.files,
                }),
                exact: true,
                component: Files,
                titleMessageId: 'ui.site.pageTitle.project.files',
              },
              {
                path: parseURL(siteMap.currentProject, {
                  tab: routeParams.tabs.project.team,
                }),
                exact: true,
                component: Team,
                withCurrentProject: true,
                titleMessageId: 'ui.site.pageTitle.project.team',
              },
              {
                path: parseURL(siteMap.currentProject),
                exact: true,
                component: CurrentProjectDefault,
                withCurrentProject: true,
                titleMessageId: 'ui.site.pageTitle.project.default',
              },
              {
                isRedirect: true,
                redirectToPath: parseURL(siteMap.currentProject),
              },
            ],
          },
          {
            isRedirect: true,
            redirectToPath: siteMap.home,
          },
        ],
      },
    ],
  },
];

export default routes;
