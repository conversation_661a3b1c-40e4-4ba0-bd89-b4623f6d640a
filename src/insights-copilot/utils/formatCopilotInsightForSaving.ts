import {
  INSIGHT_SOURCES,
  INSIGHT_TYPES,
  INSIGHTS_STATUSES,
} from '../../constants/insights.constants';
import { AnalyticsFiltersType } from '../../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersTypes';
import { ToastAlertSnackbarType } from '../../redux/slices/toastAlert.slice';
import { BasicWorkspace } from '../../types/workspace.types';
import { getCreateUpdateInsightRequest } from '../../utils/buildAndSaveCIUrlFilters';
import { trackCustomEventGainsight } from '../../utils/gainsight';
import { createCopilotInsights } from '../apis/postCreateInsight';
import {
  ElementMetaDataType,
  NormativeInsightForSaving,
  NormativeMessageMetaDataType,
  BrandCopilotMetaData,
  NormativeCopilotMetaData,
  NormativeMetaDataForSavingLambda,
} from '../types/chatInsightType';
import { ChatType } from '../types/chatIType';
import {
  ElementType,
  MessageInsightType,
  MessageType,
  MessageUserType,
  ReportDataType,
} from '../types/messageType';
import {
  formatBrandFiltersForInsightRequest,
  getFormattedFiltersForBrandCopilot,
} from './formatFiltersForBrandCopilot';
import { isNormativeMetaData } from './isNormativeMetaData';
import { transformMarkdown } from './transformMarkdown';

const trackEvent = (currentChat: ChatType | null, formattedInsights: any) => {
  try {
    const insightIds = formattedInsights?.map((insight: any) => insight?.id);
    const userMessages = currentChat?.messages?.filter(
      (message) => message.type === MessageUserType.USER,
    );
    trackCustomEventGainsight('Maddie to Insights', {
      userInputCount: userMessages?.length || 1,
      insightIds: insightIds,
    });
  } catch (error) {
    console.error('Error tracking event:', error);
  }
};

export interface CopilotInsightResponse {
  id: string;
  insightLibraryId: string;
}

type HandleAddAllInsightsToLibraryParams = {
  insights: MessageInsightType[];
  organizationId: string;
  showToastAlert: (
    messageKey: string,
    type: ToastAlertSnackbarType,
    values?: Record<string, string | number | boolean>,
    duration?: number,
    autoClose?: boolean,
  ) => void;
  currentChat: ChatType | null;
  setCurrentChat: React.Dispatch<React.SetStateAction<ChatType | null>>;
  messageMetaData?: NormativeMessageMetaDataType;
  message: MessageType;
  activeWorkspaceId: number;
};

export const handleAddAllInsightsToLibrary = async ({
  insights,
  organizationId,
  showToastAlert,
  currentChat,
  setCurrentChat,
  messageMetaData,
  message,
  activeWorkspaceId,
}: HandleAddAllInsightsToLibraryParams) => {
  let formattedInsights = null;
  let workspaceIds: number[] = [];

  if (isNormativeMetaData(messageMetaData)) {
    formattedInsights = formatNormativeInsightForSaving(
      insights,
      messageMetaData,
    );
  } else if (message.filters) {
    const formattedFilters = await getFormattedFiltersForBrandCopilot(message);
    const formattedInsightRequest = formatBrandFiltersForInsightRequest(
      message,
      insights,
      formattedFilters as AnalyticsFiltersType,
    );
    formattedInsights = await Promise.all(
      formattedInsightRequest.map(async (insight) => ({
        id: insight.id,
        chatId: insight.chatId,
        ...(await getCreateUpdateInsightRequest({
          activeInsight: insight,
          analyticsFilters: formattedFilters,
          organizationId,
          activeWorkspaceId,
        })),
      })),
    );
    workspaceIds = message.filters?.workspaces?.map(
      (w: BasicWorkspace) => w.id,
    );
  }

  trackEvent(currentChat, formattedInsights);

  if (!formattedInsights) return null;

  const BATCH_SIZE = 3;
  const batches = [];
  for (let i = 0; i < formattedInsights.length; i += BATCH_SIZE) {
    batches.push(formattedInsights.slice(i, i + BATCH_SIZE));
  }

  const batchResponses = await Promise.all(
    batches.map((batch) =>
      createCopilotInsights(
        batch,
        organizationId,
        showToastAlert,
        workspaceIds,
        message.chatId || '',
        message.id || '',
      ),
    ),
  );

  const response = batchResponses
    .flat()
    .filter(
      (res): res is CopilotInsightResponse =>
        !!res && typeof res.insightLibraryId === 'string',
    );

  if (!response) return null;

  const insightMap = response.reduce<Record<string, string>>((acc, insight) => {
    if (insight && insight.id && insight.insightLibraryId) {
      const { id, insightLibraryId } = insight;
      acc[id] = insightLibraryId;
    }
    return acc;
  }, {});

  const updatedMessages =
    currentChat?.messages?.map((message) => {
      const updatedInsights = message.insights?.map((insight) => {
        const insightLibraryId = insight?.id
          ? insightMap[insight.id]
          : undefined;
        return insightLibraryId ? { ...insight, insightLibraryId } : insight;
      });

      if (updatedInsights?.some((insight) => insight.insightLibraryId)) {
        return { ...message, insights: updatedInsights };
      }
      return message;
    }) || [];

  setCurrentChat({ ...currentChat, messages: updatedMessages });
  return response;
};

export const formatNormativeInsightForSaving = (
  insights: MessageInsightType[],
  messageMetaData?: NormativeMessageMetaDataType,
): NormativeInsightForSaving[] | null => {
  if (!messageMetaData) return null;
  if (!insights) return null;
  return insights.map((insight) =>
    getAllFieldsForSavingNormativeInsight(insight, messageMetaData),
  );
};

const getAllFieldsForSavingNormativeInsight = (
  insight: MessageInsightType,
  messageMetaData: NormativeMessageMetaDataType,
): NormativeInsightForSaving => ({
  id: insight.id,
  insightLibraryId: insight.insightLibraryId,
  chatId: insight.chatId,
  platform: messageMetaData.platform?.toUpperCase(),
  status: INSIGHTS_STATUSES.PUBLISHED,
  title: insight.title,
  finding: transformMarkdown(insight.finding),
  recommendation: transformMarkdown(insight.recommendation),
  objectives: [],
  type: INSIGHT_TYPES.INDUSTRY,
  kpiIds: [parseInt(messageMetaData.kpi_id)],
  startDate: Date.parse(messageMetaData.start_date),
  endDate: Date.parse(messageMetaData.end_date),
  industryId: messageMetaData.industry_id,
  publishDate: Date.now(),
  source: INSIGHT_SOURCES.MANUAL,
  normativeMetadata: getAndFormatNormativeMetaData(
    insight.elements,
    insight.reportData,
    messageMetaData,
  ) as NormativeCopilotMetaData,
});

const getAndFormatNormativeMetaData = (
  elements: ElementType[],
  reportData: ReportDataType,
  messageMetaData: NormativeMessageMetaDataType,
): NormativeCopilotMetaData | NormativeMetaDataForSavingLambda | undefined => {
  const allElementMetaData = messageMetaData.advertisingData.find(
    (adData) => adData.element === elements[0].element,
  );
  if (!allElementMetaData) {
    return;
  }

  return {
    kpi: {
      id: messageMetaData.kpi_id,
      name: messageMetaData.kpi_name,
    },
    kpiFormat: reportData.kpi_format,
    inverseHealth: reportData.inverse_health,
    industryName: messageMetaData.industry_name,
    overallKpiAverage: messageMetaData.level_kpi_value,
    overallImpressions: messageMetaData.level_impressions,
    overallCreatives: messageMetaData.level_ad_video_count,
    elements: getAndFormatNormativeElements(
      elements,
      messageMetaData.advertisingData,
    ),
  } as NormativeCopilotMetaData;
};

const getAndFormatNormativeElements = (
  elements: ElementType[],
  elementMetaData: ElementMetaDataType[],
) => {
  if (!elements.length || !elementMetaData.length) return [];

  return elements.map((element: ElementType) => {
    const currentFullElement = elementMetaData.find(
      (adData) => adData.element === element.element,
    );

    if (!currentFullElement) return {};

    const baseElement = {
      elementValue: currentFullElement.element,
      elementTagType: currentFullElement.tag_type,
      elementAdVideoCount: currentFullElement.element_ad_video_count,
      elementImpressions: currentFullElement.element_impressions,
      elementKpiValue: currentFullElement.element_kpi_value,
    };

    return {
      ...baseElement,
      elementKpiLift: currentFullElement.element_percent_lift,
    };
  });
};

export const formatBrandCopilotMetaData = (
  insight: MessageInsightType,
  filters: AnalyticsFiltersType,
): BrandCopilotMetaData => {
  const { reportData, elements, platformMediaIds } = insight;
  const { globalFilters } = filters;
  const kpisInfo = globalFilters?.kpi?.value;

  return {
    kpi: {
      id: kpisInfo?.id || '',
      name: kpisInfo?.name || '',
    },
    kpiFormat: reportData?.kpi_format,
    inverseHealth: reportData?.inverse_health,
    overallKpiAverage: reportData?.overall_avg,
    overallImpressions: reportData?.impressions,
    overallCreatives: reportData?.number_of_creatives,
    platformMediaIds: platformMediaIds || [],
    elements: elements.map((element: ElementType) => ({
      elementValue: element.element,
      elementKpiValue: element.element_avg_kpi,
      elementKpiLift: element.kpi_lift,
      tagType: element.tag_type,
      elementImpressions: element.element_impressions,
      elementCreatives: element.element_creatives,
      isSignificant: element.element_is_significant,
    })),
  };
};
