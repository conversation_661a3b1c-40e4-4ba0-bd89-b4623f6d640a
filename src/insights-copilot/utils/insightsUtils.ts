import { Dispatch, SetStateAction } from 'react';
import { ToastAlertSnackbarType } from '../../redux/slices/toastAlert.slice';
import { MessageInsightType, MessageType } from '../types/messageType';
import { handleAddAllInsightsToLibrary } from './formatCopilotInsightForSaving';

export const addInsights = async ({
  insights,
  organizationId,
  currentChat,
  setCurrentChat,
  messageMetaData,
  message,
  showToastAlert,
  activeWorkspaceId,
}: {
  insights: MessageInsightType[];
  organizationId: string;
  currentChat: any;
  setCurrentChat: Dispatch<SetStateAction<any>>;
  messageMetaData: any;
  message: MessageType;
  showToastAlert: (messageKey: string, type: ToastAlertSnackbarType) => void;
  activeWorkspaceId: number;
}) => {
  const insightInfo = await handleAddAllInsightsToLibrary({
    insights,
    organizationId,
    showToastAlert,
    currentChat,
    setCurrentChat,
    messageMetaData,
    message,
    activeWorkspaceId,
  });
  if (!insightInfo) {
    return [];
  }

  return insightInfo.map(({ insightLibraryId }) => insightLibraryId);
};
