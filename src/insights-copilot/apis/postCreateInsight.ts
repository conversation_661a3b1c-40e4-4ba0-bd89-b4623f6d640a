import BffService from '../../apiServices/BffService';
import { ToastAlertSnackbarType } from '../../redux/slices/toastAlert.slice';
import { getFeatureFlag } from '../../utils/featureFlagUtils';
import vmErrorLog from '../../utils/vmErrorLog';
import {
  INSIGHT_ADD_TO_LIBRARY_ERROR,
  INSIGHT_ADD_TO_LIBRARY_SUCCESS,
  INSIGHTS_ADD_TO_LIBRARY_SUCCESS,
} from '../constants';
import {
  BrandInsightForSaving,
  NormativeInsightForSaving,
} from '../types/chatInsightType';

export async function createCopilotInsights(
  insights: NormativeInsightForSaving[] | BrandInsightForSaving[] | null,
  organizationId: string,
  showToastAlert: (
    messageKey: string,
    type: ToastAlertSnackbarType,
    values?: Record<string, string | number | boolean>,
    duration?: number,
    autoClose?: boolean,
  ) => void,
  workspaceIds: number[],
  chatId: string,
  messageId: string,
): Promise<Array<{ id: string; insightLibraryId: string }> | null> {
  if (!insights || !insights.length) return null;

  const isMongoEnabled = getFeatureFlag('isMongoForCopilotEnabled');

  try {
    const apiVersion = isMongoEnabled ? 'v2' : 'v1';
    const results = await BffService.handleBffApiPost(
      `${apiVersion}/organization/${organizationId}/copilot-insight`,
      {
        insightRequest: insights,
        organizationId,
        workspaceIds,
        chatId,
        messageId,
      },
    );

    results.length === 1
      ? showToastAlert(INSIGHT_ADD_TO_LIBRARY_SUCCESS, 'success')
      : showToastAlert(INSIGHTS_ADD_TO_LIBRARY_SUCCESS, 'success');
    return results;
  } catch (error) {
    showToastAlert(INSIGHT_ADD_TO_LIBRARY_ERROR, 'error');
    vmErrorLog(error as Error, 'createCopilotInsights');
    return null;
  }
}
