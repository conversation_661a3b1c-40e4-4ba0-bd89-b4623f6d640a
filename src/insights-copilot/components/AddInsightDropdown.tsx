import React, { useState, MouseEvent } from 'react';
import { <PERSON><PERSON>, <PERSON>u, <PERSON>uItem, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { addInsights } from '../utils/insightsUtils';
import { useSelector } from 'react-redux';
import { MessageType } from '../types/messageType';
import { ChatType } from '../types/chatIType';
import {
  ADD_ALL_INSIGHTS,
  ADD_INSIGHTS_TO_LIBRARY,
  ADD_INSIGHTS_TO_PROJECT,
  ADD_ONE_INSIGHT,
  LIBRARY,
  PROJECT,
} from '../constants';
import { AddIcon, CheckmarkIcon } from '../../assets/vidmob-mui-icons/general';
import { useToastAlert } from '../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { getCurrentPartnerId } from '../../redux/selectors/partner.selectors';
import InsightAddToProjectModal from '../../creativeAnalytics/insights/components/InsightsV2/components/insightAddToProjectModal/InsightAddToProjectModal';

const INSIGHT_OPTION_ALL = 'all';
const INSIGHT_OPTION_ONE = 'one';

type InsightOption =
  | typeof INSIGHT_OPTION_ALL
  | typeof INSIGHT_OPTION_ONE
  | null;

interface AddInsightsDropdownProps {
  message: MessageType;
  organizationId: string;
  currentChat: ChatType | null;
  setCurrentChat: React.Dispatch<React.SetStateAction<ChatType | null>>;
  currentPage: number;
  actionType: typeof LIBRARY | typeof PROJECT;
}

const AddInsightsDropdown = ({
  message,
  organizationId,
  currentChat,
  setCurrentChat,
  currentPage,
  actionType,
}: AddInsightsDropdownProps) => {
  const intl = useIntl();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isInsightCreating, setIsInsightCreating] = useState<boolean>(false);
  const insights = message.insights || [];
  const showToastAlert = useToastAlert();
  const currentWorkspaceId = useSelector(getCurrentPartnerId);
  const allInsightsInLibrary =
    insights.every((insight) => insight.insightLibraryId) &&
    actionType === LIBRARY;

  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const [selectedInsightOption, setSelectedInsightOption] =
    useState<InsightOption>(null);

  const handleClick = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setIsInsightCreating(false);
    setAnchorEl(null);
  };

  const handleAddAll = async () => {
    if (actionType === PROJECT) {
      setSelectedInsightOption(INSIGHT_OPTION_ALL);
      setIsProjectModalOpen(true);
    } else {
      const insightsToAdd = insights.filter(
        (insight) => !insight.insightLibraryId,
      );

      if (insightsToAdd.length === 0) {
        return;
      }

      await addInsights({
        insights: insightsToAdd,
        organizationId,
        currentChat,
        setCurrentChat,
        messageMetaData: message.messageMetaData,
        message,
        showToastAlert,
        activeWorkspaceId: currentWorkspaceId,
      });
    }
    handleClose();
  };

  const handleAddOnlyThisInsight = async () => {
    setIsInsightCreating(true);
    if (actionType === PROJECT) {
      setSelectedInsightOption(INSIGHT_OPTION_ONE);
      setIsProjectModalOpen(true);
    } else {
      const insight = insights[currentPage - 1];
      await addInsights({
        insights: [insight],
        organizationId,
        currentChat,
        setCurrentChat,
        messageMetaData: message.messageMetaData,
        message,
        showToastAlert,
        activeWorkspaceId: currentWorkspaceId,
      });
    }
    handleClose();
  };

  const handleLinkInsights = async (): Promise<string[]> => {
    const allSelectedInsights =
      selectedInsightOption === INSIGHT_OPTION_ONE
        ? [insights[currentPage - 1]]
        : insights;

    const insightsToCreate = allSelectedInsights.filter(
      (i) => !i.insightLibraryId,
    );
    const alreadyCreatedIds = allSelectedInsights
      .filter((i) => i.insightLibraryId)
      .map((i) => i.insightLibraryId);

    let newlyCreatedInsights: string[] = [];

    if (insightsToCreate.length > 0) {
      const created = await addInsights({
        insights: insightsToCreate,
        organizationId,
        currentChat,
        setCurrentChat,
        messageMetaData: message.messageMetaData,
        message,
        showToastAlert,
        activeWorkspaceId: currentWorkspaceId,
      });
      newlyCreatedInsights = created;
    }

    const allInsightIds = [
      ...alreadyCreatedIds,
      ...newlyCreatedInsights,
    ].filter((id): id is string => typeof id === 'string');

    setIsProjectModalOpen(false);
    return allInsightIds;
  };

  return (
    <>
      <Button
        sx={{ height: '36px', backgroundColor: '#fff' }}
        onClick={handleClick}
        variant="outlined"
        startIcon={
          allInsightsInLibrary ? (
            <CheckmarkIcon />
          ) : (
            <AddIcon sx={{ color: 'icon.secondary' }} />
          )
        }
        disabled={allInsightsInLibrary}
      >
        {actionType === LIBRARY
          ? (intl.messages[ADD_INSIGHTS_TO_LIBRARY] as string)
          : (intl.messages[ADD_INSIGHTS_TO_PROJECT] as string)}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <MenuItem
          onClick={handleAddAll}
          sx={{ width: '220px', borderRadius: '6px' }}
        >
          <Typography variant="body2">
            {intl.messages[ADD_ALL_INSIGHTS] as string}
          </Typography>
        </MenuItem>

        <MenuItem
          onClick={handleAddOnlyThisInsight}
          disabled={Boolean(
            isInsightCreating ||
              insights.length === 0 ||
              (insights[currentPage - 1]?.insightLibraryId &&
                actionType === LIBRARY),
          )}
          sx={{ width: '220px', borderRadius: '6px' }}
        >
          <Typography variant="body2">
            {intl.messages[ADD_ONE_INSIGHT] as string}
          </Typography>
        </MenuItem>
      </Menu>
      {actionType === PROJECT && (
        <InsightAddToProjectModal
          isOpen={isProjectModalOpen}
          onClose={() => setIsProjectModalOpen(false)}
          handleCreateInsight={handleLinkInsights}
        />
      )}
    </>
  );
};

export default AddInsightsDropdown;
