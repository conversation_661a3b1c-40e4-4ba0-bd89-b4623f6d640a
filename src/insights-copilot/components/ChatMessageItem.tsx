import { Stack, Box, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import VidMobIcon from '../assets/vidmob-icon';
import { copilotColors, userImageStyle } from '../styles';
import ChatInsight from './ChatInsight';
import ChatMessageFeedback from './ChatMessageFeedback';
import MessageContent from './MessageContent';
import { ChatType } from '../types/chatIType';
import { CopilotType } from '../types/copilotType';
import { MessageType, MessageUserType } from '../types/messageType';
import { isNormativeMetaData } from '../utils/isNormativeMetaData';
import { chatMessagesStyle } from '../styles/chatMessagesStyle';
import { AnalyticsFiltersModal } from '../../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersDrawer/AnalyticsFilterModal';
import { getFormattedFiltersForBrandCopilot } from '../utils/formatFiltersForBrandCopilot';
import { AnalyticsFiltersType } from '../../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersTypes';

interface ChatMessageItemProps {
  message: MessageType;
  index: number;
  userDisplayName: string;
  userImage?: string;
  userId: string;
  chatId?: string;
  currentChat: ChatType | null;
  setCurrentChat: React.Dispatch<React.SetStateAction<ChatType | null>>;
  copilotType: CopilotType;
  currentPages: { [key: string]: number };
  observeMessage: (message: MessageType, el: HTMLDivElement | null) => void;
  setPageForMessage: (messageId: string, page: number) => void;
  postChatPrompt: (value: string) => void;
  latestUserPrompt: string | null;
}

export const ChatMessageItem = (props: ChatMessageItemProps) => {
  const {
    message,
    index,
    userDisplayName,
    userImage,
    userId,
    chatId,
    currentChat,
    setCurrentChat,
    copilotType,
    currentPages,
    observeMessage,
    setPageForMessage,
    postChatPrompt,
    latestUserPrompt,
  } = props;
  const { messageContainerStyle, iconStyle, chatMessageStyle } =
    chatMessagesStyle;

  const isVidmob = message.type === MessageUserType.VIDMOB;
  const hasRequiredMetaData =
    message.messageMetaData && isNormativeMetaData(message.messageMetaData);
  const hasRequiredFilters = message.filters;
  const canAddInsights = Boolean(
    isVidmob && (hasRequiredMetaData || hasRequiredFilters) && message.insights,
  );
  const { lightGrey } = copilotColors;
  const background = isVidmob ? lightGrey : 'transparent';
  const withThumbs = isVidmob && message.id && chatId;
  const isInsight = isVidmob && message.insights;
  const [formattedFilters, setFormattedFilters] =
    useState<AnalyticsFiltersType | null>(null);
  const [isFiltersModalOpen, setIsFiltersModalOpen] = useState(false);

  const currentPage = (message.id && currentPages[message?.id]) || 1;
  useEffect(() => {
    if (message.filters) {
      const fetchFormattedFilters = async () => {
        const filters =
          (await getFormattedFiltersForBrandCopilot(message)) || null;
        setFormattedFilters(filters);
      };

      fetchFormattedFilters();
    } else {
      setFormattedFilters(null);
    }
  }, [message.filters]);

  return (
    <Stack
      key={message.id || index}
      sx={{
        ...messageContainerStyle,
        background,
        width: '100%',
        padding: '16px',
        alignItems: 'flex-start',
      }}
      ref={(el) => observeMessage(message || '', el)}
    >
      <Box sx={chatMessageStyle}>
        <Box sx={iconStyle}>
          {isVidmob ? (
            <VidMobIcon />
          ) : (
            <Box component={'img'} src={userImage} sx={userImageStyle} />
          )}
        </Box>
        <Box sx={{ marginLeft: '8px' }}>
          <Typography
            variant={'subtitle2'}
            sx={{
              maxWidth: '500px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {isVidmob ? message.type : userDisplayName || message.type}
          </Typography>
          {isInsight ? (
            <ChatInsight
              message={message}
              currentChat={currentChat}
              setCurrentChat={setCurrentChat}
              canAddInsights={canAddInsights}
              currentPage={currentPage}
              setCurrentPage={(page) =>
                setPageForMessage(message.id || '', page)
              }
              handleOpenInsightModal={
                copilotType === CopilotType.BRAND_COPILOT
                  ? () => setIsFiltersModalOpen(true)
                  : null
              }
            />
          ) : (
            <Typography variant="body2">
              <MessageContent message={message.text} />
            </Typography>
          )}
          {withThumbs && (
            <ChatMessageFeedback
              message={message}
              userId={userId}
              chatId={chatId}
              currentChat={currentChat}
              setCurrentChat={setCurrentChat}
              canAddInsights={canAddInsights}
              copilotType={copilotType}
              currentPage={currentPage}
              postChatPrompt={postChatPrompt}
              latestUserPrompt={latestUserPrompt}
            />
          )}
        </Box>
      </Box>
      {formattedFilters && (
        <AnalyticsFiltersModal
          filters={formattedFilters}
          isOpen={isFiltersModalOpen}
          setIsOpen={setIsFiltersModalOpen}
        />
      )}
    </Stack>
  );
};
