import React, { useState, useEffect, useCallback } from 'react';
import { Box, Stack, CardMedia, IconButton } from '@mui/material';
import {
  MessageMetaDataType,
  CreativeExampleType,
} from '../types/chatInsightType';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import MediaPreview from '../../creativeAnalytics/components/MediaPreview/MediaPreview';
import { composeLinkHelperIndividualCreativeView } from '../../creativeAnalytics/helpers/composeLinkHelper';
import { generatePath } from 'react-router';
import { BRAND_COPILOT } from '../../constants/creativeAnalytics.constants';
import { formatPlatformAccountId } from '../utils/formatPlatformAccountId';

interface CreativeExamplesProps {
  creativeExamples: CreativeExampleType[];
  insight?: MessageMetaDataType;
}

const thumbnailWidth = 80;

export const thumbnailSx = {
  height: '64px',
  width: `${thumbnailWidth}px`,
  borderRadius: '4px',
};

const buttonSx = {
  borderRadius: '6px',
  background: 'var(--background-primary, #E0E0E0)',
};

const CreativeExamplesDetails = ({
  creativeExamples,
  insight,
}: CreativeExamplesProps) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [activeMedia, setActiveMedia] = useState<CreativeExampleType | null>(
    null,
  );
  const [itemsPerPage, setItemsPerPage] = useState(0);

  useEffect(() => {
    setCurrentPage(0);
  }, [creativeExamples]);

  const updateItemsPerPage = useCallback(() => {
    const containerWidth =
      document.getElementById('creative-examples-container')?.clientWidth || 0;
    const itemWidth = thumbnailWidth;
    const spaceBetweenItems = 10;
    const newItemsPerPage = Math.floor(
      containerWidth / (itemWidth + spaceBetweenItems),
    );

    setItemsPerPage(Math.min(newItemsPerPage, 8));
  }, []);

  useEffect(() => {
    updateItemsPerPage();
    window.addEventListener('resize', updateItemsPerPage);
    return () => {
      window.removeEventListener('resize', updateItemsPerPage);
    };
  }, [updateItemsPerPage]);

  useEffect(() => {
    const newTotalPages = Math.ceil(creativeExamples.length / itemsPerPage);
    if (currentPage >= newTotalPages) {
      setCurrentPage(newTotalPages > 0 ? newTotalPages - 1 : 0);
    } else if (currentPage < 0 && newTotalPages > 0) {
      setCurrentPage(0);
    }
  }, [itemsPerPage, creativeExamples.length, currentPage]);

  if (!creativeExamples || !creativeExamples.length) return null;

  const totalPages = itemsPerPage
    ? Math.ceil(creativeExamples.length / itemsPerPage)
    : 1;
  const startIndex = currentPage * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, creativeExamples.length);
  const currentItems = creativeExamples.slice(startIndex, endIndex);

  const handleNext = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage((prev) => prev + 1);
      setActiveMedia(null);
    }
  };

  const handlePrev = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
      setActiveMedia(null);
    }
  };

  const onMediaClick = (
    creativeExample: CreativeExampleType,
    insight?: MessageMetaDataType,
  ) => {
    if (!insight) return;
    const link = composeLinkHelperIndividualCreativeView({
      platform: insight.platform,
      platformMediaId: creativeExample.platformMediaId,
      source: BRAND_COPILOT,
      adAccountId:
        formatPlatformAccountId(creativeExample.platformAccountId) || '',
      isCreativeOfMultiAssetAds: false, // TODO
    });
    if (link) {
      //@ts-expect-error this is working properly
      window.open(generatePath(link), '_blank').focus();
    }
  };

  return (
    <>
      {totalPages > 1 && currentPage > 0 && (
        <IconButton onClick={handlePrev} sx={buttonSx}>
          <ArrowBackIosNewIcon />
        </IconButton>
      )}
      <Stack
        id="creative-examples-container"
        direction="row"
        gap={5}
        sx={{
          overflow: 'hidden',
          display: 'flex',
          flexWrap: 'nowrap',
          flexGrow: 1,
        }}
      >
        {currentItems.map((creativeExample) => (
          <Box
            key={creativeExample.platformMediaId}
            onMouseEnter={() => setActiveMedia(creativeExample)}
            onMouseLeave={() => setActiveMedia(null)}
            onClick={() => onMediaClick(creativeExample, insight)}
            sx={{ flexShrink: 0 }}
          >
            <CardMedia
              component="img"
              image={creativeExample.media.thumbnails[0]?.url}
              sx={thumbnailSx}
              loading="lazy"
            />
            {activeMedia === creativeExample && (
              <MediaPreview
                mediaPreview={creativeExample.media}
                rightPosition={20}
              />
            )}
          </Box>
        ))}
      </Stack>
      {totalPages > 1 && currentPage < totalPages - 1 && (
        <IconButton onClick={handleNext} sx={buttonSx}>
          <ArrowForwardIosIcon />
        </IconButton>
      )}
    </>
  );
};

export default CreativeExamplesDetails;
