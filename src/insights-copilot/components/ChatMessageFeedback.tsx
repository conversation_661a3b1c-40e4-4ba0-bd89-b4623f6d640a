import { <PERSON><PERSON>, <PERSON>ack } from '@mui/material';
import React, { useEffect, useState } from 'react';
import {
  MessageType,
  FeedbackType,
  FeedbackReasonType,
} from '../types/messageType';
import { chatMessagesStyle } from '../styles/chatMessagesStyle';
import { ChatFeedbackDialog } from './ChatFeedbackDialog';
import { ChatType } from '../types/chatIType';
import { useSelector } from 'react-redux';
import { getOrganizationId } from '../../redux/selectors/partner.selectors';
import { AddAllInsightsButtons } from './AddAllInsightsButtons';
import { FeedbackButtons } from './FeedbackButtons';
import { CopilotType } from '../types/copilotType';
import {
  ExportIcon,
  ProcessingIcon,
} from '../../assets/vidmob-mui-icons/general';
import { downloadSlides } from '../utils/downloadPresentation';
import { getFeatureFlag } from '../../utils/featureFlagUtils';
import LoadingButton from '@mui/lab/LoadingButton';
import { useIntl } from 'react-intl';
import { EXPORT_INSIGHTS_TO_SLIDES, REGENERATE_MESSAGE } from '../constants';
import { useChatAPI } from './useChatApi';
import { useToastAlert } from '../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';

interface Props {
  message: MessageType;
  userId: string;
  chatId: string;
  currentChat: ChatType | null;
  setCurrentChat: React.Dispatch<React.SetStateAction<ChatType | null>>;
  copilotType: CopilotType;
  currentPage: number;
  canAddInsights?: boolean;
  postChatPrompt: (value: string) => void;
  latestUserPrompt: string | null;
}

const ChatMessageFeedback = (props: Props) => {
  const {
    message,
    chatId,
    currentChat,
    setCurrentChat,
    canAddInsights,
    copilotType,
    currentPage,
    postChatPrompt,
    latestUserPrompt,
  } = props;
  const intl = useIntl();
  const showToastAlert = useToastAlert();
  const organizationId = useSelector(getOrganizationId);
  const { handleChatMessageFeedback } = useChatAPI(copilotType, organizationId);
  const { mainContainerStyle } = chatMessagesStyle;
  const [feedback, setFeedback] = useState(message.feedback);
  const [isFeedbackDialogueOpen, setIsFeedbackDialogueOpen] = useState(false);
  const isExportInsightsToSlidesEnabled = getFeatureFlag(
    'isExportInsightsToSlidesEnabled',
  );
  const [isDownloading, setIsDownloading] = useState(false);

  useEffect(() => {
    setFeedback(message.feedback);
  }, [message.feedback, chatId]);

  const onSubmitMessageFeedback = (
    value: FeedbackType,
    feedbackReason?: FeedbackReasonType,
    additionalComments?: string,
  ) => {
    handleChatMessageFeedback({
      chatId,
      messageId: message?.id || '',
      value,
      feedbackReason,
      additionalComments,
    });
    const updatedMessages =
      currentChat?.messages?.map((msg) =>
        msg.id === message.id ? { ...msg, feedback: value } : msg,
      ) || [];

    setCurrentChat({ ...currentChat, messages: updatedMessages });

    setIsFeedbackDialogueOpen(false);
  };

  const updateFeedback = (value: FeedbackType) => {
    setFeedback(value);
    setIsFeedbackDialogueOpen(true);
  };

  const onCancelModal = () => {
    setIsFeedbackDialogueOpen(false);
    setFeedback(message.feedback);
  };
  return (
    <Stack
      sx={{ ...mainContainerStyle, marginTop: '12px' }}
      direction={'row'}
      justifyContent={'flex-start'}
      alignItems={'center'}
      gap={6}
    >
      {latestUserPrompt && (
        <Button
          sx={{ height: '36px', backgroundColor: '#fff' }}
          variant="outlined"
          startIcon={<ProcessingIcon sx={{ color: 'icon.secondary' }} />}
          onClick={() => postChatPrompt(latestUserPrompt)}
        >
          {intl.messages[REGENERATE_MESSAGE] as string}
        </Button>
      )}
      {canAddInsights && (
        <AddAllInsightsButtons
          message={message}
          organizationId={organizationId}
          currentChat={currentChat}
          setCurrentChat={setCurrentChat}
          currentPage={currentPage}
        />
      )}
      {isExportInsightsToSlidesEnabled && message.insights && (
        <LoadingButton
          sx={{ height: '36px', backgroundColor: '#fff' }}
          variant="outlined"
          loadingPosition="start"
          loading={isDownloading}
          onClick={() =>
            downloadSlides({
              insights: message.insights,
              onLoadingStart: () => setIsDownloading(true),
              onLoadingEnd: () => setIsDownloading(false),
              platform: message.messageMetaData?.platform,
              kpiName: message.messageMetaData?.kpi_name,
              showToastAlert,
              intl,
            })
          }
          startIcon={isDownloading ? null : <ExportIcon />}
        >
          {intl.messages[EXPORT_INSIGHTS_TO_SLIDES] as string}
        </LoadingButton>
      )}
      <FeedbackButtons
        feedback={feedback}
        setFeedback={setFeedback}
        updateFeedback={updateFeedback}
      />

      {feedback && isFeedbackDialogueOpen && (
        <ChatFeedbackDialog
          isOpen={isFeedbackDialogueOpen}
          feedback={feedback}
          onSubmit={onSubmitMessageFeedback}
          onCancel={onCancelModal}
        />
      )}
    </Stack>
  );
};

export default ChatMessageFeedback;
