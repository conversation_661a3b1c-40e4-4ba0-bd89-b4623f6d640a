import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

const MessageContent = ({
  message,
  noMargin = false,
  singleLine = false,
}: {
  message: string;
  noMargin?: boolean;
  singleLine?: boolean;
}) => {
  // Pre-process message to convert custom syntax
  const preprocessMessage = (text: string) => {
    // Handle nested patterns by processing from most specific to least specific

    // 1. Handle ~~![text](color)~~ (underline + color)
    let processed = text.replace(
      /~~!\[([^\]]*)\]\((#[\w\d]{3,6}|rgba?\([^)]+\)|rgb\([^)]+\))\)~~/g,
      '<u style="color: $2">$1</u>',
    );

    // 2. Handle remaining ![text](color) patterns (just color)
    processed = processed.replace(
      /!\[([^\]]*)\]\((#[\w\d]{3,6}|rgba?\([^)]+\)|rgb\([^)]+\))\)/g,
      '<span style="color: $2">$1</span>',
    );

    // 3. Handle remaining ~~text~~ patterns (just underline)
    processed = processed.replace(/~~([^~]+)~~/g, '<u>$1</u>');

    return processed;
  };

  const normalizedMessage = singleLine
    ? preprocessMessage(message)
        .replace(/[\r\n]+/g, ' ')
        .replace(/\s\s+/g, ' ')
        .trim()
    : preprocessMessage(message);

  return (
    <div
      style={
        singleLine
          ? {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: 'block',
            }
          : undefined
      }
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]} // This allows HTML tags like <u>
        components={{
          p: ({ children, ...props }) =>
            singleLine ? (
              <>{children}</>
            ) : (
              <p {...props} style={{ margin: noMargin ? 0 : undefined }}>
                {children}
              </p>
            ),
          br: () => (singleLine ? null : <br />),
          ul: ({ children }) =>
            singleLine ? <>{children}</> : <ul>{children}</ul>,
          ol: ({ children }) =>
            singleLine ? <>{children}</> : <ol>{children}</ol>,
          li: ({ children }) =>
            singleLine ? (
              <span style={{ marginRight: 8 }}>{children}</span>
            ) : (
              <li>{children}</li>
            ),
        }}
      >
        {normalizedMessage}
      </ReactMarkdown>
    </div>
  );
};

export default MessageContent;
