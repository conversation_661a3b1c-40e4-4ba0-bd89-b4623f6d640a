import React from 'react';
import {
  Vid<PERSON>ob<PERSON><PERSON>,
  Vid<PERSON>ob<PERSON><PERSON>,
  VidMob<PERSON>tack,
  VidMobTab,
  VidMobTabs,
} from '../../../../vidMobComponentWrappers';
import useCreativeManagerContext from '../hooks/useCreativeManagerContext';
import { CreativeManagerTabEnum } from '../helpers/CreativeManagerTypes';
import {
  CREATIVE_MANAGER_ADS_TAB_TITLE,
  CREATIVE_MANAGER_CREATIVE_TAB_TITLE,
  TAB_CHIP_LABEL_SELECTED,
} from '../helpers/CreativeManagerTexts';
import {
  AdIcon,
  CreativeIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import {
  creativeManagerTabStyle,
  creativeManagerTabsWrapperStyle,
} from '../helpers/CreativeManagerStyles';
import { PER_PAGE } from '../helpers/CreativeManagerConstants';
import TablePagination from '../../../../muiCustomComponents/TablePagination';

type Props = {};

export const CreativeManagerTabs = (_: Props) => {
  const {
    selectedTab,
    setSelectedTab,
    isAdPlatform,
    dataObject,
    updateDataObject,
    setAdData,
    adData,
    setCreativeData,
    creativeData,
    setSelectedItemsInReduxStore,
  } = useCreativeManagerContext();

  const handleTabChange = (
    _: React.SyntheticEvent,
    newValue: CreativeManagerTabEnum,
  ) => {
    setSelectedTab(newValue);
  };

  const handlePaginationChange = (
    _: React.MouseEvent<HTMLButtonElement> | null,
    page: number,
  ) => {
    updateDataObject({ pageNumber: page });
  };

  const handleSelectionChange = () => {
    setAdData((prev) => ({ ...prev, selectedItems: [] }));
    setCreativeData((prev) => ({ ...prev, selectedItems: [] }));
    setSelectedItemsInReduxStore([]);
  };

  const chipFragment = (label: string, selectedCount = 0) => {
    return (
      <>
        {label}
        {selectedCount > 0 && (
          <VidMobChip
            className="selectedItems"
            label={`${selectedCount} ${TAB_CHIP_LABEL_SELECTED}`}
            onDelete={handleSelectionChange}
            sx={{
              position: 'absolute',
              right: '12px',
            }}
          />
        )}
      </>
    );
  };

  return (
    <VidMobStack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={creativeManagerTabsWrapperStyle}
    >
      <VidMobTabs value={selectedTab} onChange={handleTabChange}>
        {isAdPlatform && (
          <VidMobTab
            label={chipFragment(
              CREATIVE_MANAGER_ADS_TAB_TITLE,
              adData.selectedItems.length,
            )}
            value={CreativeManagerTabEnum.AD}
            icon={<AdIcon />}
            sx={creativeManagerTabStyle}
          />
        )}
        <VidMobTab
          label={chipFragment(
            CREATIVE_MANAGER_CREATIVE_TAB_TITLE,
            creativeData.selectedItems.length,
          )}
          value={CreativeManagerTabEnum.CREATIVE}
          icon={<CreativeIcon />}
          sx={creativeManagerTabStyle}
        />
      </VidMobTabs>
      <VidMobBox width="200px">
        <TablePagination
          count={dataObject.totalCount}
          page={dataObject.pageNumber}
          rowsPerPage={PER_PAGE}
          onPageChange={handlePaginationChange}
        />
      </VidMobBox>
    </VidMobStack>
  );
};
