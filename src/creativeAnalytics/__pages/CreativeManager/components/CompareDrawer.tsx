import React, { useEffect } from 'react';
import { Vid<PERSON>ob<PERSON>utton, VidMobStack } from '../../../../vidMobComponentWrappers';
import siteMap from '../../../../routing/siteMap';
import CreativeCompareReportDrawer from '../../../components/CreativeGroups/CreativeCompareReportDrawer';
import { useDispatch, useSelector } from 'react-redux';
import {
  getCreativeGroupsStorage,
  getFilteredCreativeGroupsAndStatus,
} from '../../../../redux/selectors/creativeAnalytics/creativeGroups.selectors';
import { generatePath } from 'react-router';
import {
  ChevronDownIcon,
  ChevronUpIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import creativeGroupsSlice from '../../../../redux/slices/creativeAnalytics/creativeGroups.slice';
import useCreativeManagerContext from '../hooks/useCreativeManagerContext';
import {
  compareMediaDrawerStyle,
  creativeCompareTabStyle,
} from '../helpers/CreativeManagerStyles';
type Props = {};

export const CompareDrawer = (_: Props) => {
  const dispatch = useDispatch();
  const { filters, setIsCompareDrawerOpen } = useCreativeManagerContext();
  const { platform, adAccounts } = filters;
  const creativeGroupsStorage = useSelector(getCreativeGroupsStorage);
  const { filteredCreativeGroups } = useSelector(
    getFilteredCreativeGroupsAndStatus,
  );
  const { isComparingMedia } = creativeGroupsStorage;
  const customComparisonReportLink =
    adAccounts && adAccounts.length > 0 && platform
      ? generatePath(siteMap.creativeIntelligenceCustomCompareNew, {})
      : '';
  const openDrawerOnClick = () => {
    if (!filteredCreativeGroups?.length) {
      dispatch(creativeGroupsSlice.actions.refreshFilteredCreativeGroups());
    }
    dispatch(
      creativeGroupsSlice.actions.setIsComparingMedia({
        isComparingMedia: !isComparingMedia,
      }),
    );
  };

  useEffect(() => {
    setIsCompareDrawerOpen(isComparingMedia);
  }, [isComparingMedia]);

  return (
    <VidMobStack
      sx={{
        ...compareMediaDrawerStyle,
        bottom: isComparingMedia ? 0 : '-160px',
      }}
    >
      <CreativeCompareReportDrawer
        customComparisonReportLink={customComparisonReportLink}
      />
      <VidMobButton
        variant="contained"
        onClick={openDrawerOnClick}
        sx={creativeCompareTabStyle}
      >
        {isComparingMedia ? <ChevronDownIcon /> : <ChevronUpIcon />}
      </VidMobButton>
    </VidMobStack>
  );
};
