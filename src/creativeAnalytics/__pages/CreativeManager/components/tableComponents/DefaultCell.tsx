import React from 'react';
import { GridRenderCellParams } from '@mui/x-data-grid-pro';
import useCreativeManagerContext from '../../hooks/useCreativeManagerContext';
import {
  CreativeManagerClientDataStatusEnum,
  CreativeManagerTabEnum,
} from '../../helpers/CreativeManagerTypes';
import CreativeManagerV3LoaderCell from './CreativeManagerV3LoaderCell';
import {
  FIELD_AD_ACCOUNT,
  FIELD_CAMPAIGNS,
  FIELD_DURATION,
  FIELD_KPI,
  FIELD_NAME_AND_MEDIA,
} from '../../helpers/CreativeManagerConstants';
import { NameAndMediaCell } from './NameAndMediaCell';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { KpiCell } from './KpiCell';
import { creativeManagerNameAndMediaCellWrapperStyle } from '../../helpers/CreativeManagerStyles';
import { PopoverCell } from './PopoverCell';
import formatDuration from '../../../../../utils/formatDuration';

const { LOADING } = CreativeManagerClientDataStatusEnum;
const { AD } = CreativeManagerTabEnum;

export const DefaultCell = (props: GridRenderCellParams) => {
  const { dataObject, selectedTab } = useCreativeManagerContext();
  const isLoading = dataObject.status === LOADING;
  const field = props?.field;
  const value = props?.value as string | number;

  if (isLoading) {
    return (
      <CreativeManagerV3LoaderCell
        isWithThumbnailLoader={field === FIELD_NAME_AND_MEDIA}
      />
    );
  }

  if (field === FIELD_NAME_AND_MEDIA) {
    const shouldIncludeThumbnailLink = selectedTab !== AD;
    const { isCreativeInMultiAssetAd } = props?.row || {};

    return (
      <VidMobStack sx={creativeManagerNameAndMediaCellWrapperStyle}>
        <NameAndMediaCell
          isCreativeOfMultiAssetAds={isCreativeInMultiAssetAd ?? false}
          cellData={props}
          shouldIncludeThumbnailLink={shouldIncludeThumbnailLink}
        />
      </VidMobStack>
    );
  }

  if (field === FIELD_KPI) {
    return <KpiCell cellData={props} />;
  }

  const adAccount = FIELD_AD_ACCOUNT.replace(' ', '_');
  if (field === adAccount && Array.isArray(props.row[adAccount])) {
    const items = props.row[adAccount].map((item: any) => item.name);
    return (
      <VidMobStack sx={{ color: 'primary.main' }}>
        <PopoverCell items={items} />
      </VidMobStack>
    );
  }

  if (field === FIELD_CAMPAIGNS && Array.isArray(props.row[FIELD_CAMPAIGNS])) {
    const items = props.row[FIELD_CAMPAIGNS].map((item: any) => item.name);
    return (
      <VidMobStack sx={{ color: 'primary.main' }}>
        <PopoverCell items={items} />
      </VidMobStack>
    );
  }

  if (field === FIELD_DURATION) {
    return (
      <VidMobTypography variant="body2" color={'text.primary'}>
        {formatDuration(value as number)}
      </VidMobTypography>
    );
  }

  return (
    <VidMobTypography variant="body2" color={'text.primary'}>
      {value?.toLocaleString()}
    </VidMobTypography>
  );
};
