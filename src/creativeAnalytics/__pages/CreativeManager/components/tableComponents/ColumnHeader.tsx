import React from 'react';
import {
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { GridColumnHeaderParams } from '@mui/x-data-grid-pro';
import useCreativeManagerContext from '../../hooks/useCreativeManagerContext';
import { FIELD_KPI } from '../../helpers/CreativeManagerConstants';
import { CreativeManagerClientDataStatusEnum } from '../../helpers/CreativeManagerTypes';
import CreativeManagerV3LoaderCell from './CreativeManagerV3LoaderCell';

const { LOADING } = CreativeManagerClientDataStatusEnum;

export const ColumnHeader = (props: GridColumnHeaderParams) => {
  const { filters, dataObject } = useCreativeManagerContext();
  const isSortBy = dataObject.sort.sortBy === props.field;
  const color = isSortBy ? 'text.primary' : 'text.deEmphasized';

  if (props.field === FIELD_KPI) {
    const isLoading = dataObject.status === LOADING;
    if (isLoading) {
      return <CreativeManagerV3LoaderCell />;
    }

    const kpiName = filters.kpi?.name || '';
    return (
      <VidMobTooltip title={kpiName} placement="top">
        <VidMobTypography variant="body2" fontWeight={600} color={color}>
          {kpiName}
        </VidMobTypography>
      </VidMobTooltip>
    );
  }

  return (
    <VidMobTypography variant="body2" fontWeight={600} color={color}>
      {props.colDef?.headerName}
    </VidMobTypography>
  );
};
