import React from 'react';
import { GridCellParams } from '@mui/x-data-grid-pro';
import { VidMobTooltip } from '../../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';

const NO_KPI_VALUE_TOOLTIP_MESSAGE_INTL = {
  id: 'ui.creative.intelligence.creativeManager.kpiCell.noKpiValue',
  defaultMessage: 'No KPI data for this creative.',
};

export const KpiCell = ({ cellData }: { cellData: GridCellParams }) => {
  const intl = useIntl();
  const kpi = cellData?.row?.kpi;
  return (
    <span className="creative-manager-kpi-column-cell">
      {kpi?.kpiValue === undefined ? (
        <VidMobTooltip
          title={intl.formatMessage(NO_KPI_VALUE_TOOLTIP_MESSAGE_INTL)}
        >
          <span>{kpi?.formattedValue}</span>
        </VidMobTooltip>
      ) : (
        kpi?.formattedValue
      )}
    </span>
  );
};
