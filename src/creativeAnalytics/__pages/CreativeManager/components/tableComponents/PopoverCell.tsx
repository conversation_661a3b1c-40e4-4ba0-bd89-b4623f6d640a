import React, { useRef, useState } from 'react';
import {
  VidMobBox,
  VidMobPopover,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';

const containerSx = {
  width: '100%',
  color: '#0161f2',
};

const textSx = {
  fontSize: 14,
  textOverflow: 'ellipsis',
  overflow: 'hidden',
};

const popoverSx = {
  pointerEvents: 'none',
};

const popoverContentSx = {
  pointerEvents: 'auto',
  border: '1px solid #d9d9d9',
  '& .MuiTypography-root': {
    p: '10px',
    fontSize: 14,
  },
};

interface Props {
  items: string[];
}

export const PopoverCell = ({ items }: Props) => {
  const [anchorElement, setAnchorElement] = useState<Element | null>(null);
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);
  const closePopoverTimeout = useRef<any>(null);

  const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
    clearTimeout(closePopoverTimeout.current);
    setAnchorElement(event.currentTarget);
    setPopoverOpen(true);
  };

  const handlePopoverClose = () => {
    closePopoverTimeout.current = setTimeout(() => {
      setPopoverOpen(false);
    }, 200);
  };

  const handleContentMouseEnter = () => {
    clearTimeout(closePopoverTimeout.current);
    setPopoverOpen(true);
  };

  const handleContentMouseLeave = () => {
    closePopoverTimeout.current = setTimeout(() => {
      setPopoverOpen(false);
    }, 200);
  };

  const itemsInfoToDisplay =
    items.length > 1 ? `${items.length.toLocaleString()}` : items[0];

  return (
    <VidMobBox
      className="creative-manager-popover-column-cell"
      sx={containerSx}
      onMouseEnter={handlePopoverOpen}
      onMouseLeave={handlePopoverClose}
    >
      <VidMobTypography sx={textSx}>{itemsInfoToDisplay}</VidMobTypography>
      <VidMobPopover
        id="column-cell-popover"
        open={popoverOpen}
        anchorEl={anchorElement}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        disableRestoreFocus
        sx={popoverSx}
        PaperProps={{
          sx: popoverContentSx,
          onMouseEnter: handleContentMouseEnter,
          onMouseLeave: handleContentMouseLeave,
        }}
      >
        {items.map((item: string, index: number) => (
          <VidMobTypography
            key={index}
            onMouseEnter={handleContentMouseEnter}
            onMouseLeave={handleContentMouseLeave}
          >
            {item}
          </VidMobTypography>
        ))}
      </VidMobPopover>
    </VidMobBox>
  );
};
