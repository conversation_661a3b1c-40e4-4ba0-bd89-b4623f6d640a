import React, { Ref, useState } from 'react';
import { useIntl } from 'react-intl';
import { Link } from 'react-router-dom';
import useHover from '../../../../../hooks/useHover';
import { GridCellParams } from '@mui/x-data-grid-pro';
import { Box, CardMedia, Tooltip } from '@mui/material';
import ShimmerAndFadeLoadingState from '../../../../../components/ShimmerAndFadeLoadingState';
import { CREATIVE_MANAGER_TABLE_VIEWS } from '../../helpers/CreativeManagerConstants';
import MediaPreview from '../../../../components/MediaPreview/MediaPreview';
import { MultiAssetAdSelectionModal } from '../../../IndividualCreativeView/MultiAssetAdSelectionModal/MultiAssetAdSelectionModal';
import { getIndividualCreativeViewLinkProps } from '../../../IndividualCreativeViewV2/individualCreativeViewV2Utils/getIndividualCreativeViewLinkProps';
import { CREATIVE_MANAGER } from '../../../../../constants/creativeAnalytics.constants';
import { ORGANIC_PLATFORMS } from '../../../../../constants/ci.constants';

const CELL_BUTTON_LABEL_KEY =
  'ui.creative.intelligence.creativeManager.cell.button';

const thumbnailSx = {
  width: 50,
  height: 40,
};

export const NameAndMediaCell = ({
  cellData,
  shouldIncludeThumbnailLink,
  isCreativeOfMultiAssetAds,
}: {
  cellData: GridCellParams;
  shouldIncludeThumbnailLink: boolean;
  isCreativeOfMultiAssetAds: boolean;
}) => {
  const mediaInfo = cellData?.row?.nameAndMedia;
  const intl = useIntl();
  const { name, media, view, platform, ICVlink } = mediaInfo;
  const adAccountId = new URLSearchParams(ICVlink?.split('?')[1]).get(
    'adAccountId',
  );
  const [nameHoverRef, isNameHovered] = useHover();
  const [isICVAdSelectionModalOpen, setIsICVAdSelectionModalOpen] =
    useState(false);

  const thumbnail = media?.thumbnails?.[0];

  const isOrganic = ORGANIC_PLATFORMS.includes(platform?.toUpperCase());

  const shouldShowHoverLink =
    isNameHovered &&
    view === CREATIVE_MANAGER_TABLE_VIEWS.CREATIVE &&
    !isOrganic;

  const getCursor = () => (ICVlink ? 'pointer' : 'default');

  const [isMediaPreviewOpen, setIsMediaPreviewOpen] = useState(false);

  const nameColumnCellThumbnail = (
    <span className="creative-manager-name-column-cell-thumbnail">
      {thumbnail ? (
        <CardMedia
          onMouseEnter={() => setIsMediaPreviewOpen(true)}
          onMouseLeave={() => setIsMediaPreviewOpen(false)}
          image={thumbnail.url}
          sx={{ ...thumbnailSx, cursor: getCursor() }}
        />
      ) : (
        <span className="creative-manager-name-column-cell-thumbnail loading">
          <ShimmerAndFadeLoadingState height="40px" width="50px" />
        </span>
      )}
    </span>
  );

  const generateLinkProps = () => {
    return getIndividualCreativeViewLinkProps({
      link: ICVlink,
      isCreativeOfMultiAssetAds,
      setIsICVAdSelectionModalOpen,
      state: { cameFromVidmob: true },
    });
  };

  const thumbnailSection =
    shouldIncludeThumbnailLink && ICVlink ? (
      <Link {...generateLinkProps()}>{nameColumnCellThumbnail}</Link>
    ) : (
      nameColumnCellThumbnail
    );

  return (
    <span className="creative-manager-name-column-cell">
      {thumbnailSection}
      <Box
        className="creative-manager-name-column-cell-text"
        ref={nameHoverRef as Ref<unknown> | undefined}
      >
        <Tooltip title={name} disableInteractive placement="top-start">
          <span className="creative-manager-name-column-cell-name">{name}</span>
        </Tooltip>

        {shouldShowHoverLink && (
          <Link {...generateLinkProps()}>
            {intl.messages[CELL_BUTTON_LABEL_KEY] as string}
          </Link>
        )}
      </Box>
      {isICVAdSelectionModalOpen && (
        <MultiAssetAdSelectionModal
          isOpen={isICVAdSelectionModalOpen}
          onClose={() => setIsICVAdSelectionModalOpen(false)}
          platformMediaId={cellData?.row.id}
          mediaName={cellData?.row.nameAndMedia?.name}
          platform={platform}
          adAccountId={adAccountId || ''}
          source={CREATIVE_MANAGER}
        />
      )}
      {isMediaPreviewOpen && <MediaPreview mediaPreview={media} />}
    </span>
  );
};
