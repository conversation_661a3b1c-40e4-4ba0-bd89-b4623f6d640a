import React from 'react';
import {
  VidMobSkeleton,
  VidMobStack,
} from '../../../../../vidMobComponentWrappers';

type Props = {
  isWithThumbnailLoader?: boolean;
};
const CreativeManagerV3LoaderCell = (props: Props) => {
  const { isWithThumbnailLoader } = props;
  return (
    <VidMobStack direction="row" justifyContent="center" alignItems="center">
      {isWithThumbnailLoader && (
        <VidMobSkeleton
          variant="rounded"
          sx={{ marginRight: '12px', height: '40px', width: '50px' }}
        />
      )}
      <VidMobSkeleton
        variant="rounded"
        sx={{ height: '12px', width: '120px' }}
      />
    </VidMobStack>
  );
};

export default CreativeManagerV3LoaderCell;
