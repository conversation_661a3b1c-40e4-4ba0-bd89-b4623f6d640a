import React from 'react';
import useCreativeManagerContext from '../hooks/useCreativeManagerContext';
import {
  DataGridPro,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridColumnMenu,
  GridColumnMenuProps,
  GridFeatureMode,
} from '@mui/x-data-grid-pro';
import { CREATIVE_MANAGER_TABLE_COMMON_COLUMNS } from '../helpers/CreativeManagerConstants';
import { PER_PAGE } from '../helpers/CreativeManagerConstants';
import {
  CreativeManagerClientDataStatusEnum,
  CreativeManagerSelectAllStateEnum,
  CreativeManagerTabEnum,
} from '../helpers/CreativeManagerTypes';
import { convertToColumnVisibilityModel } from '../helpers/CreativeManagerUtils';
import {
  creativeManagerDisabledCheckboxStyle,
  creativeManagerTableStyle,
} from '../helpers/CreativeManagerStyles';
import useCreativeManagerTableClientSide from '../hooks/useCreativeManagerTableClientSide';
import useCreativeManagerTableServerSide from '../hooks/useCreativeManagerTableServerSide';
import { Checkbox } from '@mui/material';

const { NAME_AND_MEDIA } = CREATIVE_MANAGER_TABLE_COMMON_COLUMNS;
const { SELECT_ALL, NONE, UN_SELECT_ALL } = CreativeManagerSelectAllStateEnum;
const { CREATIVE } = CreativeManagerTabEnum;
const { SUCCEED } = CreativeManagerClientDataStatusEnum;

const CustomColumnMenu = (props: GridColumnMenuProps) => (
  <GridColumnMenu
    {...props}
    slots={{
      columnMenuColumnsItem: null,
    }}
  />
);

const CustomCheckbox = (props: any) => {
  const { selectAllStateRef } = useCreativeManagerContext();

  const handleChange = (e: any) => {
    let state = NONE;
    if (props.indeterminate !== undefined) {
      // This Checkbox is used for the select all checkbox in the header
      state = e.target.checked ? SELECT_ALL : UN_SELECT_ALL;
    }
    selectAllStateRef.current = state;
    props.onChange(e);
  };

  return <Checkbox {...props} onChange={handleChange} />;
};

type Props = {};

export const CreativeManagerTable = (_: Props) => {
  const {
    columns,
    dataObject,
    selectedTab,
    isCompareDrawerOpen,
    isFetchAllItems,
  } = useCreativeManagerContext();
  const { status, sort, filters } = dataObject;
  const isSelectDisable = selectedTab !== CREATIVE || status !== SUCCEED;
  const clientTable = useCreativeManagerTableClientSide();
  const serverTable = useCreativeManagerTableServerSide();
  const {
    handleFilterChange,
    handleSelectionChange,
    handleSortChange,
    rows,
    paginationMode,
    selectedItems,
  } = isFetchAllItems ? clientTable : serverTable;

  return (
    <DataGridPro
      sx={{
        ...creativeManagerTableStyle,
        ...(isSelectDisable ? creativeManagerDisabledCheckboxStyle : {}),
        marginBottom: isCompareDrawerOpen ? '160px' : 0,
      }}
      sortingMode="server"
      filterMode="server"
      paginationMode={paginationMode as GridFeatureMode}
      pagination
      slotProps={{}}
      paginationModel={{
        pageSize: PER_PAGE,
        page: dataObject.pageNumber,
      }}
      initialState={{
        pinnedColumns: {
          left: [GRID_CHECKBOX_SELECTION_COL_DEF.field, NAME_AND_MEDIA.field],
        },
      }}
      sortModel={[{ field: sort.sortBy, sort: sort.sortDirection }]}
      onSortModelChange={handleSortChange}
      filterModel={{ items: filters }}
      onFilterModelChange={(model) => handleFilterChange(model.items)}
      columnVisibilityModel={convertToColumnVisibilityModel(columns)}
      slots={{ columnMenu: CustomColumnMenu, baseCheckbox: CustomCheckbox }}
      rows={rows}
      columns={columns}
      checkboxSelection
      isRowSelectable={() => !isSelectDisable}
      hideFooter
      disableRowSelectionOnClick
      onRowSelectionModelChange={handleSelectionChange}
      rowSelectionModel={selectedItems}
      keepNonExistentRowsSelected
    />
  );
};
