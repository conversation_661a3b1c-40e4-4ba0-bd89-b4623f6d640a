import React from 'react';
import { useSelector } from 'react-redux';
import { getMediaForSelectedCreatives } from '../../../../redux/selectors/creativeAnalytics/creativeGroups.selectors';
import { Creative } from '../helpers/CreativeManagerTypes';
import { useIntl } from 'react-intl';
import { DownloadFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import {
  VidMobButton,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { controlBarButtonSx } from '../../../../components/ReportFilters/styles';
import { useToastAlert } from '../../SavedReports/SavedReportsCustomHooks';
import {
  downloadAndZip,
  generateFilename,
  generateZipFileName
} from '../helpers/CreativeManagerUtils';

const DOWNLOAD_CREATIVES_TEXT = 'ui.analytics.reports.downloadCreatives.text';

const buttonSx = {
  ...controlBarButtonSx,
  p: '6px 12px',
  minWith: '174px',
};

const iconSx = {
  color: 'text.secondary',
  height: '18px',
  width: '18px',
};

const textSx = {
  fontSize: '14px',
  fontWeight: '600',
  width: '100%',
};

const DownloadCreativesButton = () => {
  const selectedCreativesWithMedia = useSelector(getMediaForSelectedCreatives);
  const toast = useToastAlert();

  const intl = useIntl();

  const downloadMediaOnClick = () => {
    const mediaList = selectedCreativesWithMedia.map((creative: Creative) => {
      const url = creative.media.downloadUrl;
      const fileName = generateFilename(creative);

      return {
        url,
        fileName,
      };
    });
    const zipFileName = generateZipFileName();
    downloadAndZip(mediaList, zipFileName, () => {
      toast('ui.analytics.reports.downloadCreatives.error', 'error');
    });
  };

  if (!selectedCreativesWithMedia.length) {
    return null;
  }

  return (
    <VidMobButton
      sx={buttonSx}
      startIcon={<DownloadFilledIcon sx={iconSx} />}
      onClick={downloadMediaOnClick}
    >
      <VidMobTypography variant="body2" sx={textSx}>
        {intl.messages[DOWNLOAD_CREATIVES_TEXT] as string}
      </VidMobTypography>
    </VidMobButton>
  );
};

export default DownloadCreativesButton;
