import React from 'react';
import { useIntl } from 'react-intl';
import { InfoFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import {
  VidMob<PERSON>lert,
  VidMobButton,
  VidMobLink,
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';

const DATA_LIMITATION_WARNING_TEXT =
  'ui.user.compareReportsControlBar.dataLimitationWarning.text';
const DATA_LIMITATION_WARNING_LINK =
  'ui.user.compareReportsControlBar.dataLimitationWarning.link';
const DATA_LIMITATION_WARNING_LINK_URL =
  'https://help.vidmob.com/en/articles/7866329-why-might-my-report-be-missing-data-in-creative-analytics';
const DATA_LIMITATION_CLOSE_BUTTON_LABEL = 'button.global.close.label';

const warningBannerStyles = {
  backgroundColor: 'info.light',
  color: 'text.primary',
  marginBottom: '16px',
  marginLeft: '24px',
  marginRight: '24px',
  alignItems: 'center',
  '& .MuiAlert-message': {
    width: '100%',
  },
};

type Props = {
  setShowBanner: (showBanner: boolean) => void;
  showBanner: boolean;
};

export const DataLimitationBanner = (props: Props) => {
  const { setShowBanner, showBanner } = props;
  const intl = useIntl();

  const onClose = () => {
    setShowBanner(false);
  };

  if (!showBanner) {
    return null;
  }

  return (
    <VidMobAlert
      className="report-data-limitation-banner"
      severity="info"
      icon={
        <InfoFilledIcon
          sx={{
            color: 'text.primary',
          }}
        />
      }
      sx={warningBannerStyles}
    >
      <VidMobStack
        sx={{ width: '100%' }}
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <VidMobTypography variant="body2">
          {intl.formatMessage({
            id: DATA_LIMITATION_WARNING_TEXT,
            defaultMessage:
              'Note: The data in this report excludes the analysis of carousel ads and placement-specific data for Advantage+ and dynamic creative ads. Learn more ',
          })}
          <VidMobLink href={DATA_LIMITATION_WARNING_LINK_URL} target="_blank">
            {intl.formatMessage({
              id: DATA_LIMITATION_WARNING_LINK,
              defaultMessage: 'here',
            })}
          </VidMobLink>
        </VidMobTypography>
        <VidMobButton onClick={onClose}>
          <VidMobTypography
            sx={{
              fontSize: '13px',
              fontWeight: '600',
              color: 'text.primary',
            }}
          >
            {intl.formatMessage({
              id: DATA_LIMITATION_CLOSE_BUTTON_LABEL,
              defaultMessage: 'Close',
            })}
          </VidMobTypography>
        </VidMobButton>
      </VidMobStack>
    </VidMobAlert>
  );
};
