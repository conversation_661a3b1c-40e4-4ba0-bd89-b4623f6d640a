import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { CreativeManagerContext } from './CreativeManagerContext';
import siteMap from '../../../../routing/siteMap';
import { formatDate } from '../../SavedReports/SavedReportsUtils/exportReportUtils';
import { REPORT_PDF_DATA_LOCAL_STORAGE_KEY } from '../../../reports/components/AnalyticsReportComponents/AnalyticsReportPreview/AnalyticsReportPreview';
import { CREATIVE_MANAGER_TITLE } from '../helpers/CreativeManagerTexts';
import {
  CreativeManagerClientDataItemType,
  CreativeManagerClientDataStatusEnum,
  CreativeManagerClientDataType,
  CreativeManagerColumnsDropdownType,
  CreativeManagerFilterType,
  CreativeManagerSelectAllStateEnum,
  CreativeManagerTabEnum,
} from '../helpers/CreativeManagerTypes';
import {
  PLATFORMS_SUPPORT_MULTI_CREATIVE_ADS as AD_PLATFORMS,
  ORGANIC_PLATFORMS,
} from '../../../../constants/ci.constants';
import useAnalyticsFiltersV1 from '../hooks/useAnalyticsFiltersV1';
import {
  getCreativeManagerV3AdColumns,
  getCreativeManagerCreativeColumns,
  getCreativeManagerOrganicColumns,
} from '../helpers/CreativeManagerColumns';
import { getOrganizationId } from '../../../../redux/selectors/partner.selectors';
import { useDispatch, useSelector } from 'react-redux';
import useCreativeManagerCreativeData from '../hooks/useCreativeManagerCreativeData';
import {
  CREATIVE_MANAGER_SHARE_URL_PARAM_KEY,
  INITIAL_DATA,
} from '../helpers/CreativeManagerConstants';
import {
  getCreativeManagerParsedParams,
  getCreativeManagerSelectedItemsForView,
  isCreativeManagerHasMinimumFilters,
  isCreativeManagerSelectedItemsEqualToReduxStore,
} from '../helpers/CreativeManagerUtils';
import useCreativeManagerAdData from '../hooks/useCreativeManagerAdData';
import { exportCreativeManagerCSV } from '../helpers/CreativeManagerPlatformCsvUtil';
import creativeGroupsSlice from '../../../../redux/slices/creativeAnalytics/creativeGroups.slice';
import { getSelectedItemsByView } from '../../../../redux/selectors/creativeAnalytics/creativeGroups.selectors';
import { useToastAlert } from '../../SavedReports/SavedReportsCustomHooks';
import { useLocation } from 'react-router-dom';
import AnalyticsSavedReportsService from '../../../reports/services/AnalyticsSavedReportsService';

const { getSavedReportById } = AnalyticsSavedReportsService;
const { CREATIVE, AD } = CreativeManagerTabEnum;
const { LOADING, SUCCEED, FAILED } = CreativeManagerClientDataStatusEnum;
type ColumnsType = CreativeManagerColumnsDropdownType;

type Props = {
  children: ReactNode;
};

const CreativeManagerProvider = (props: Props) => {
  const { children } = props;
  const dispatch = useDispatch();
  const { search } = useLocation();
  const toast = useToastAlert();
  const showToast = (messageId: string, type: 'error' | 'info') =>
    toast(messageId, type);
  const organizationId = useSelector(getOrganizationId);
  const [selectedTab, setSelectedTab] = useState(CREATIVE);
  const [filters, setFilters] = useState<CreativeManagerFilterType>({});
  const [creativeData, setCreativeData] = useState(INITIAL_DATA);
  const [adData, setAdData] = useState(INITIAL_DATA);
  const [columns, setColumns] = useState<ColumnsType[]>([]);
  const [isLoadingShareUrlFilter, setIsLoadingShareUrlFilter] = useState(true);
  const [isCompareDrawerOpen, setIsCompareDrawerOpen] = useState(false);
  const selectAllStateRef = useRef(CreativeManagerSelectAllStateEnum.NONE);
  const [isFetchAllItems, setIsFetchAllItems] = useState(false);
  const [isAllItemsFetched, setIsAllItemsFetched] = useState(false);
  const commonProps = {
    filters,
    organizationId,
    isFetchAllItems,
    setIsFetchAllItems,
    isAllItemsFetched,
    setIsAllItemsFetched,
  };
  const { setAnalyticsFiltersV1 } = useAnalyticsFiltersV1({ setFilters });
  const dataObject = selectedTab === AD ? adData : creativeData;
  const isAdPlatform = AD_PLATFORMS.includes(filters.platform || '');
  const isOrganicPlatform = ORGANIC_PLATFORMS.includes(filters.platform || '');
  const adParams = { ...commonProps, dataObject: adData, showToast };
  const adResponse = useCreativeManagerAdData(adParams);
  const creativeParams = {
    ...commonProps,
    dataObject: creativeData,
    showToast,
  };
  const creativeResponse = useCreativeManagerCreativeData(creativeParams);
  const selectedItemIdsFromReduxStore = useSelector((state) =>
    getSelectedItemsByView(state, 'CREATIVE'),
  );

  useEffect(() => {
    const searchParams = new URLSearchParams(search);
    const id = searchParams.get(CREATIVE_MANAGER_SHARE_URL_PARAM_KEY);
    if (id) {
      handleLoadReportFromShareUrl(organizationId, id);
      return;
    }

    setIsLoadingShareUrlFilter(false);
  }, [search, organizationId]);

  useEffect(() => {
    const isEqual = isCreativeManagerSelectedItemsEqualToReduxStore(
      selectedItemIdsFromReduxStore,
      creativeData.selectedItems,
    );
    if (!isEqual) {
      const { selectedCreatives, selectedAds } =
        getCreativeManagerSelectedItemsForView(
          creativeData.items,
          selectedItemIdsFromReduxStore,
        );
      setCreativeData((prev) => ({
        ...prev,
        selectedItems: selectedCreatives,
      }));
      setAdData((prev) => ({
        ...prev,
        selectedItems: selectedAds as string[],
      }));
    }
  }, [selectedItemIdsFromReduxStore]);

  useEffect(() => {
    const { data, isFetching, isError } = adResponse;
    if (isFetching || isLoadingShareUrlFilter) {
      setAdData((prev) => ({
        ...prev,
        status: LOADING,
      }));
      return;
    }
    const status = isError ? FAILED : SUCCEED;
    const totalCount = data?.pagination?.totalSize || 0;
    const newItems: CreativeManagerClientDataItemType[] = data?.data || [];
    setAdData((prev) => ({
      ...prev,
      status,
      totalCount,
      items: newItems,
      fullItemList: newItems,
    }));
  }, [adResponse.isFetching]);

  useEffect(() => {
    const { data, isFetching, isError } = creativeResponse;
    if (isFetching || isLoadingShareUrlFilter) {
      setCreativeData((prev) => ({
        ...prev,
        status: LOADING,
      }));
      return;
    }

    const status = isError ? FAILED : SUCCEED;
    const totalCount = data?.pagination?.totalSize || 0;
    const newItems: CreativeManagerClientDataItemType[] = data?.data || [];

    if (isFetchAllItems) {
      const { selectedCreatives, selectedAds } =
        getCreativeManagerSelectedItemsForView(
          newItems,
          newItems.map((item) => item.id),
        );
      setAdData((prev) => ({
        ...prev,
        selectedItems: selectedAds as string[],
      }));
      setCreativeData((prev) => ({
        ...prev,
        status,
        totalCount,
        items: newItems,
        fullItemList: newItems,
        selectedItems: selectedCreatives,
      }));
      setTimeout(() => {
        // We want to set the selected items in redux store after the state is updated
        setSelectedItemsInReduxStore(selectedCreatives);
      }, 0);
      return;
    }

    setCreativeData((prev) => ({
      ...prev,
      status,
      totalCount,
      items: newItems,
      fullItemList: newItems,
    }));
  }, [creativeResponse.isFetching]);

  useEffect(() => {
    if (selectedTab === AD) {
      setColumns(getCreativeManagerV3AdColumns());
      return;
    }

    if (isOrganicPlatform) {
      setColumns(getCreativeManagerOrganicColumns());
      return;
    }

    setColumns(getCreativeManagerCreativeColumns());
  }, [selectedTab, isOrganicPlatform]);

  useEffect(() => {
    setSelectedTab(CREATIVE);
    setAdData(INITIAL_DATA);
    setCreativeData(INITIAL_DATA);
    setSelectedItemsInReduxStore([]);
    setIsAllItemsFetched(false);
    setIsFetchAllItems(false);
    setIsCompareDrawerOpen(false);
    dispatch(creativeGroupsSlice.actions.clearCompareGroups());
  }, [filters.mutationKey]);

  const handleLoadReportFromShareUrl = async (
    organizationId: string,
    reportId: string,
  ) => {
    try {
      const response = await getSavedReportById({ reportId, organizationId });
      const analyticsFilters = response?.data?.filters?.analyticsFilters;
      const currencyFilters = response?.data?.filters?.currencyFilters;
      if (analyticsFilters) {
        setAnalyticsFiltersV1(analyticsFilters, currencyFilters);
      } else {
        showToast('toastAlert.load.share.link.failed', 'error');
      }
    } catch (error) {
      console.error('Error creating saved report:', error);
      showToast('toastAlert.load.share.link.failed', 'error');
    } finally {
      setIsLoadingShareUrlFilter(false);
    }
  };

  const setSelectedItemsInReduxStore = (items: string[]) => {
    dispatch(
      creativeGroupsSlice.actions.setSelectedCreatives({
        selectedCreativeIds: items,
      }),
    );
  };

  const updateDataObject = (
    partialDataObject: Partial<CreativeManagerClientDataType>,
  ) => {
    if (selectedTab === AD) {
      setAdData((prev) => ({ ...prev, ...partialDataObject }));
      return;
    }
    setCreativeData((prev) => ({ ...prev, ...partialDataObject }));
  };

  const onDownloadCSV = async () => {
    const { status } = dataObject;
    const hasMinimumFilters = isCreativeManagerHasMinimumFilters(filters);
    if (!hasMinimumFilters && status !== SUCCEED) return;

    const isAd = selectedTab === AD;
    const view = isAd ? 'ads' : 'creatives';
    const parsedParams = getCreativeManagerParsedParams(
      isAd ? adParams : creativeParams,
    );
    const { payload, organizationId } = parsedParams;
    const response = await exportCreativeManagerCSV({
      view,
      payload,
      organizationId,
    });
    if (!response) {
      showToast('ui.analytics.reports.downloadCSV.error', 'error');
    }
  };

  const onDownloadPDF = () => {
    window.open(siteMap.creativeIntelligenceCreativeManagerPDF, '_blank');
    const reportDate = formatDate(new Date());
    localStorage.setItem(
      REPORT_PDF_DATA_LOCAL_STORAGE_KEY,
      JSON.stringify({
        reportTypeCopy: CREATIVE_MANAGER_TITLE,
        reportDate,
      }),
    );
  };

  return (
    <CreativeManagerContext.Provider
      value={{
        selectedTab,
        filters,
        isAdPlatform,
        columns,
        creativeData,
        adData,
        dataObject,
        isCompareDrawerOpen,
        isFetchAllItems,
        isAllItemsFetched,
        organizationId,
        selectAllStateRef,

        showToast,
        setIsFetchAllItems,
        setIsAllItemsFetched,
        setIsCompareDrawerOpen,
        setSelectedItemsInReduxStore,
        updateDataObject,
        setCreativeData,
        setAdData,
        onDownloadPDF,
        onDownloadCSV,
        setSelectedTab,
        setFilters,
        setColumns,
      }}
    >
      {children}
    </CreativeManagerContext.Provider>
  );
};

export default CreativeManagerProvider;
