import { useContext } from 'react';
import { CreativeManagerContext } from '../context/CreativeManagerContext';
import { CreativeManagerContextType } from '../helpers/CreativeManagerTypes';

const useCreativeManagerContext = (): CreativeManagerContextType => {
  const context = useContext(CreativeManagerContext);
  if (!context) {
    throw new Error(
      'useCreativeManagerContext must be used within an CreativeManagerProvider',
    );
  }
  return context;
};

export default useCreativeManagerContext;
