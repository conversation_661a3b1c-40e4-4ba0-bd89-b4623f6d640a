import { useQuery } from '@tanstack/react-query';
import BffCreativeManagerService from '../../../../apiServices/BffCreativeManagerService';
import {
  CreativeManagerRequestParams,
  CreativeManagerTabEnum,
} from '../helpers/CreativeManagerTypes';
import { getCreativeManagerPlatformMedia } from '../helpers/CreativeManagerPlatformMediaUtil';
import { EMPTY_ASPECT_RATIO } from '../../../../utils/getAspectRatio';
import {
  getCreativeManagerParsedParams,
  parseMediaInfo,
} from '../helpers/CreativeManagerUtils';
import {
  CreativeManagerHooksDataPropsType,
  CreativeManagerServerDataItemType,
} from '../helpers/CreativeManagerTypes';
import { useDispatch, useSelector } from 'react-redux';
import creativeGroupsSlice from '../../../../redux/slices/creativeAnalytics/creativeGroups.slice';
import { DEFAULT_CURRENCY } from '../../../components/AnalyticsFilters/AnalyticsFiltersConstants';
import { useCallback } from 'react';
import { getAccountMediaAsObject } from '../../../../redux/selectors/creativeAnalytics/creativeGroups.selectors';

const { CREATIVE } = CreativeManagerTabEnum;

const useCreativeManagerCreativeData = (
  props: CreativeManagerHooksDataPropsType,
) => {
  const mediaKeyedByPlatformMediaId = useSelector(getAccountMediaAsObject);
  const creativeParsedParams = useCallback(
    () => getCreativeManagerParsedParams(props),
    [props],
  );
  const { organizationId, pagination, payload, currency } =
    creativeParsedParams();
  const { totalCount, ...others } = pagination;
  const dispatch = useDispatch();
  const enabled =
    Boolean(!props.isAllItemsFetched) &&
    Boolean(organizationId) &&
    Boolean(payload.endDate) &&
    Boolean(payload.startDate) &&
    Boolean(payload.platform) &&
    Boolean(payload.adAccountIds) &&
    Boolean(payload.workspaceIds) &&
    Boolean(payload.kpiId);

  return useQuery({
    queryKey: [
      'creativeManagerCreativeData',
      organizationId,
      payload,
      others,
      props.isFetchAllItems,
    ],
    refetchOnWindowFocus: false,
    enabled,
    queryFn: async () => {
      try {
        const response = await BffCreativeManagerService.getCreatives(
          organizationId,
          payload as CreativeManagerRequestParams,
          pagination,
        );
        const platformMediaList = await getCreativeManagerPlatformMedia({
          workspaceIds: payload.workspaceIds || [],
          adAccountIds: payload.adAccountIds || [],
          platformMediaIds: response.data.map(
            (item: CreativeManagerServerDataItemType) => item.platformMediaId,
          ),
        });

        dispatch(
          creativeGroupsSlice.actions.setMediaKeyedByPlatformMediaId({
            mediaKeyedByPlatformMediaId: {
              ...(mediaKeyedByPlatformMediaId || {}),
              ...platformMediaList,
            },
          }),
        );

        if (props.isFetchAllItems) {
          props.setIsAllItemsFetched(true);
        }

        return {
          data: response.data.map((item: CreativeManagerServerDataItemType) => {
            const {
              platformMediaId,
              platformAdAccounts,
              impressions,
              adIds,
              duration,
              campaignNames,
              mediaFormat,
              isCreativeInMultiAssetAd,
              kpiValue,
            } = item;
            const { formattedValue, mediaInfo } = parseMediaInfo(
              isCreativeInMultiAssetAd || false,
              kpiValue || { value: 0, format: '' },
              platformMediaId || '',
              platformAdAccounts || [],
              platformMediaList,
              payload.platform || '',
              CREATIVE,
              currency?.id === item.currency ? currency : DEFAULT_CURRENCY,
            );

            return {
              id: platformMediaId,
              impressions: impressions,
              ads: adIds?.length,
              campaigns: campaignNames?.map((campaignName: string) => ({
                id: campaignName,
                name: campaignName,
              })),
              ad_accounts: platformAdAccounts,
              duration: duration || 0,
              format: mediaFormat || EMPTY_ASPECT_RATIO,
              kpi: { formattedValue },
              nameAndMedia: mediaInfo,
              isCreativeInMultiAssetAd: isCreativeInMultiAssetAd,
              adIds: adIds,
              posts: adIds?.length,
            };
          }),
          pagination: response.pagination,
        };
      } catch (error) {
        if (props.isFetchAllItems) {
          props.setIsAllItemsFetched(true);
        }

        props.showToast &&
          props.showToast(
            'ui.creative.intelligence.creativeManager.creative.error',
            'error',
          );

        return {
          data: [],
          pagination: { totalSize: 0 },
        };
      }
    },
  });
};

export default useCreativeManagerCreativeData;
