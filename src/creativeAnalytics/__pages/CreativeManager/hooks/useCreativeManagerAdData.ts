import { useQuery } from '@tanstack/react-query';
import BffCreativeManagerService from '../../../../apiServices/BffCreativeManagerService';
import {
  CreativeManagerRequestParams,
  CreativeManagerTabEnum,
} from '../helpers/CreativeManagerTypes';
import { getCreativeManagerPlatformMedia } from '../helpers/CreativeManagerPlatformMediaUtil';
import {
  getCreativeManagerParsedParams,
  parseMediaInfo,
} from '../helpers/CreativeManagerUtils';
import {
  CreativeManagerHooksDataPropsType,
  CreativeManagerServerDataItemType,
} from '../helpers/CreativeManagerTypes';
import { DEFAULT_CURRENCY } from '../../../components/AnalyticsFilters/AnalyticsFiltersConstants';
import { useCallback } from 'react';

const { AD } = CreativeManagerTabEnum;

const useCreativeManagerAdData = (props: CreativeManagerHooksDataPropsType) => {
  const adParsedParams = useCallback(
    () => getCreativeManagerParsedParams(props),
    [props],
  );
  const { organizationId, pagination, payload, currency } = adParsedParams();
  const { totalCount, ...others } = pagination;
  const enabled =
    Boolean(!props.isAllItemsFetched) &&
    Boolean(organizationId) &&
    Boolean(payload.endDate) &&
    Boolean(payload.startDate) &&
    Boolean(payload.platform) &&
    Boolean(payload.adAccountIds) &&
    Boolean(payload.workspaceIds) &&
    Boolean(payload.kpiId);

  return useQuery({
    queryKey: [
      'creativeManagerAdData',
      organizationId,
      payload,
      others,
      props.isFetchAllItems,
    ],
    refetchOnWindowFocus: false,
    enabled,
    queryFn: async () => {
      try {
        const response = await BffCreativeManagerService.getAds(
          organizationId,
          payload as CreativeManagerRequestParams,
          pagination,
        );

        const platformMediaList = await getCreativeManagerPlatformMedia({
          workspaceIds: payload.workspaceIds || [],
          adAccountIds: payload.adAccountIds || [],
          platformMediaIds: response.data.map(
            (item: CreativeManagerServerDataItemType) =>
              item.platformMediaIds ? item.platformMediaIds[0] : '',
          ),
          showToast: props.showToast,
        });

        if (props.isFetchAllItems) {
          props.setIsAllItemsFetched(true);
        }

        return {
          data: response.data.map((item: CreativeManagerServerDataItemType) => {
            const {
              platformMediaIds,
              platformAdAccounts,
              impressions,
              campaignNames,
              kpiValue,
              adId,
            } = item;
            const { formattedValue, mediaInfo } = parseMediaInfo(
              false,
              kpiValue || { value: 0, format: '' },
              item.platformMediaIds ? item.platformMediaIds[0] : '',
              platformAdAccounts || [],
              platformMediaList,
              payload.platform || '',
              AD,
              currency?.id === item.currency ? currency : DEFAULT_CURRENCY,
            );

            return {
              id: adId,
              nameAndMedia: mediaInfo,
              kpi: { formattedValue },
              ad_accounts: platformAdAccounts,
              campaigns: campaignNames?.map((campaignName: string) => ({
                id: campaignName,
                name: campaignName,
              })),
              impressions: impressions,
              creatives: platformMediaIds?.length,
              isCreativeInMultiAssetAd: false,
            };
          }),
          pagination: response.pagination,
        };
      } catch (error) {
        if (props.isFetchAllItems) {
          props.setIsAllItemsFetched(true);
        }

        props.showToast &&
          props.showToast(
            'ui.creative.intelligence.creativeManager.ads.error',
            'error',
          );

        return {
          data: [],
          pagination: { totalSize: 0 },
        };
      }
    },
  });
};

export default useCreativeManagerAdData;
