import useCreativeManagerContext from './useCreativeManagerContext';
import {
  GridFilterItem,
  GridRowSelectionModel,
  GridSortDirection,
  GridSortModel,
} from '@mui/x-data-grid-pro';
import {
  getCreativeManagerSelectedItemsForView,
  getCreativeManagerTableRows,
} from '../helpers/CreativeManagerUtils';
import { debounce } from '@mui/material';
import { useCallback } from 'react';
import { CreativeManagerSelectAllStateEnum } from '../helpers/CreativeManagerTypes';

const { SELECT_ALL, UN_SELECT_ALL } = CreativeManagerSelectAllStateEnum;

const useCreativeManagerTableServerSide = () => {
  const {
    columns,
    dataObject,
    selectedTab,
    setAdData,
    setCreativeData,
    creativeData,
    setSelectedItemsInReduxStore,
    updateDataObject,
    setIsFetchAllItems,
    selectAllStateRef,
  } = useCreativeManagerContext();
  const { sort, selectedItems } = dataObject;
  const rows = getCreativeManagerTableRows(columns, dataObject);

  const handleSelectionChange = (newSelectionModel: GridRowSelectionModel) => {
    if (selectAllStateRef.current === SELECT_ALL) {
      // SelectAll
      setIsFetchAllItems(true);
      return;
    }

    if (selectAllStateRef.current === UN_SELECT_ALL) {
      // ClearAll
      setCreativeData((prev) => ({
        ...prev,
        selectedItems: [],
      }));
      setAdData((prev) => ({ ...prev, selectedItems: [] }));
      setSelectedItemsInReduxStore([]);
      return;
    }

    const { selectedCreatives, selectedAds } =
      getCreativeManagerSelectedItemsForView(
        creativeData.items,
        newSelectionModel as string[],
      );
    setCreativeData((prev) => ({
      ...prev,
      selectedItems: selectedCreatives,
    }));
    setAdData((prev) => ({ ...prev, selectedItems: selectedAds as string[] }));
    setSelectedItemsInReduxStore(selectedCreatives);
  };

  const handleSortChange = (sortModel: GridSortModel) => {
    const tmpSort = {
      field: sort.sortBy,
      sort:
        sort.sortDirection === 'asc' ? 'desc' : ('asc' as GridSortDirection),
    };
    const newSort = sortModel[0] ? sortModel[0] : tmpSort;
    updateDataObject({
      sort: {
        sortBy: newSort.field,
        sortDirection: newSort.sort,
      },
      pageNumber: 0,
    });
  };

  const debouncedApplyFilterChange = useCallback(
    debounce((filterItems: GridFilterItem[]) => {
      updateDataObject({
        filters: filterItems,
        pageNumber: 0,
      });
    }, 500),
    [selectedTab],
  );

  const handleFilterChange = (filterItems: GridFilterItem[]) => {
    debouncedApplyFilterChange(filterItems);
  };

  return {
    handleSelectionChange,
    handleSortChange,
    handleFilterChange,
    rows,
    paginationMode: 'server',
    selectedItems,
  };
};

export default useCreativeManagerTableServerSide;
