import useCreativeManagerContext from './useCreativeManagerContext';
import {
  GridFilterItem,
  GridRowSelectionModel,
  GridSortDirection,
  GridSortModel,
} from '@mui/x-data-grid-pro';
import {
  getCreativeManagerSelectedItemsForView,
  sortCreativeManagerClientTableRows,
  filterCreativeManagerClientTableRows,
  getCreativeManagerTableRows,
} from '../helpers/CreativeManagerUtils';
import { debounce } from '@mui/material';
import { useCallback } from 'react';
import { CreativeManagerSelectAllStateEnum } from '../helpers/CreativeManagerTypes';

const { SELECT_ALL } = CreativeManagerSelectAllStateEnum;

const useCreativeManagerTableClientSide = () => {
  const {
    columns,
    dataObject,
    selectedTab,
    setAdData,
    setCreativeData,
    creativeData,
    setSelectedItemsInReduxStore,
    updateDataObject,
    selectAllStateRef,
  } = useCreativeManagerContext();
  const { sort, selectedItems } = dataObject;
  const rows = getCreativeManagerTableRows(columns, dataObject);

  const handleSelectionChange = (newSelectionModel: GridRowSelectionModel) => {
    let selectedIds = newSelectionModel as string[];
    if (selectAllStateRef.current === SELECT_ALL) {
      // SelectAll
      selectedIds = dataObject.fullItemList.map((item) => item.id);
    }

    const { selectedCreatives, selectedAds } =
      getCreativeManagerSelectedItemsForView(
        creativeData.fullItemList,
        selectedIds,
      );
    setCreativeData((prev) => ({
      ...prev,
      selectedItems: selectedCreatives,
    }));
    setAdData((prev) => ({ ...prev, selectedItems: selectedAds as string[] }));
    setSelectedItemsInReduxStore(selectedCreatives);
  };

  const handleSortChange = (sortModel: GridSortModel) => {
    const tmpSort = {
      field: sort.sortBy,
      sort:
        sort.sortDirection === 'asc' ? 'desc' : ('asc' as GridSortDirection),
    };
    const newSort = sortModel[0] ? sortModel[0] : tmpSort;
    const sortedItems = sortCreativeManagerClientTableRows(
      dataObject.fullItemList,
      newSort.field,
      newSort.sort,
    );
    updateDataObject({
      items: sortedItems,
      sort: {
        sortBy: newSort.field,
        sortDirection: newSort.sort,
      },
      pageNumber: 0,
    });
  };

  const debouncedApplyFilterChange = useCallback(
    debounce((filterItems: GridFilterItem[]) => {
      const filteredItems = filterCreativeManagerClientTableRows(
        dataObject.fullItemList,
        filterItems,
      );

      updateDataObject({
        items: filteredItems,
        filters: filterItems,
        pageNumber: 0,
      });
    }, 500),
    [selectedTab, dataObject],
  );

  const handleFilterChange = (filterItems: GridFilterItem[]) => {
    debouncedApplyFilterChange(filterItems);
  };

  return {
    handleSelectionChange,
    handleSortChange,
    handleFilterChange,
    rows,
    paginationMode: 'client',
    selectedItems: selectedItems,
  };
};

export default useCreativeManagerTableClientSide;
