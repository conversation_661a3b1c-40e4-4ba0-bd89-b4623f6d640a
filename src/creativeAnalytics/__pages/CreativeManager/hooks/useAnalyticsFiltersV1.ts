import { Dispatch, SetStateAction, useEffect } from 'react';
import { CreativeManagerFilterType } from '../helpers/CreativeManagerTypes';
import { useAnalyticsFilters } from '../../../components/AnalyticsFilters/hooks/useAnalyticsFilters';
import { convertAnyAnalyticsFiltersToCreativeManagerFilters } from '../helpers/CreativeManagerUtils';
import {
  AnalyticsFiltersType,
  MetricSectionChildrenType,
  ScopeSectionChildrenType,
} from '../../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import { useDispatch, useSelector } from 'react-redux';
import { getCurrentPartnerFeature } from '../../../../redux/selectors/partner.selectors';
import { GLOBALS } from '../../../../constants';
import { CREATIVE_MANAGER } from '../../../../constants/creativeAnalytics.constants';
import creativeManagerSlice from '../../../../redux/slices/creativeAnalytics/creativeManager.slice';
import { kpiEngine } from '../../../services/KpiEngine/KpiEngine';
import analyticsConfigurationSlice from '../../../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';
import { CREATIVE_GROUPS_FETCH_DATA_ACTION_TYPE } from '../helpers/CreativeManagerConstants';

const { PARTNER_SPECIFIC_FEATURES } = GLOBALS;
const { CREATIVE_INTELLIGENCE } = PARTNER_SPECIFIC_FEATURES;

const fetchCreativeGroupsData = (analyticsFilters: AnalyticsFiltersType) => ({
  type: CREATIVE_GROUPS_FETCH_DATA_ACTION_TYPE,
  payload: analyticsFilters,
});

type Props = {
  setFilters: Dispatch<SetStateAction<CreativeManagerFilterType>>;
};

// Fred, to use analyticsFiltersV2 replace this hook with your hook
const useAnalyticsFiltersV1 = (props: Props) => {
  const { setFilters } = props;
  const dispatch = useDispatch();
  const hasCIAccess = useSelector((state) =>
    getCurrentPartnerFeature(state, CREATIVE_INTELLIGENCE),
  );
  const {
    analyticsFilters,
    setIsDrawerOpen,
    setSelectedSections,
    resetAnalyticsFiltersState,
    setCurrentReportType,
    setAdvancedFiltersOverride,
    currency,
    setFilters: setAnalyticsFiltersV1,
  } = useAnalyticsFilters();

  const { globalFilters } = analyticsFilters;
  const { channel, kpi, adAccounts } = globalFilters;

  useEffect(() => {
    if (channel?.value && adAccounts?.value?.length > 0) {
      dispatch(creativeManagerSlice.actions.setAccountCreativesStatusPending());
      dispatch(
        creativeManagerSlice.actions.setAccountCreativesAndAdsStatusPending(),
      );
      dispatch(fetchCreativeGroupsData(analyticsFilters));
    }
  }, [channel?.value && JSON.stringify(adAccounts?.value)]);

  useEffect(() => {
    if (kpi?.value) {
      const selectedKpi = kpiEngine.getKpi({
        ...kpi.value,
        formula:
          typeof kpi?.value?.formula === 'string'
            ? JSON.parse(kpi?.value?.formula)
            : kpi?.value?.formula,
        variables:
          typeof kpi?.value?.variables === 'string'
            ? JSON.parse(kpi?.value?.variables)
            : kpi?.value?.variables,
      });
      dispatch(analyticsConfigurationSlice.actions.clearSharedFilterData());
      dispatch(
        analyticsConfigurationSlice.actions.setSelectedKpi({ selectedKpi }),
      );
    }
  }, [JSON.stringify(kpi?.value)]);

  useEffect(() => {
    setFilters(
      convertAnyAnalyticsFiltersToCreativeManagerFilters(
        analyticsFilters,
        currency,
      ),
    );
  }, [analyticsFilters.id]);

  useEffect(() => {
    if (hasCIAccess) {
      setCurrentReportType(CREATIVE_MANAGER);
      setAdvancedFiltersOverride(null);
      setIsDrawerOpen(true);
      const selectedSections = {
        SCOPE: [
          ScopeSectionChildrenType.WORKSPACES,
          ScopeSectionChildrenType.CHANNELS,
          ScopeSectionChildrenType.AD_ACCOUNTS_ADVANCED,
        ],
        METRIC: [MetricSectionChildrenType.KPI],
        ADVANCED: [],
      };

      setSelectedSections(selectedSections);
    }

    return () => {
      resetAnalyticsFiltersState();
    };
  }, [hasCIAccess]);

  return {
    setAnalyticsFiltersV1,
  };
};

export default useAnalyticsFiltersV1;
