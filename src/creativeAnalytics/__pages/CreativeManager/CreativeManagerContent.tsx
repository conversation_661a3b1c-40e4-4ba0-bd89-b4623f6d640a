import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { AnalyticsFiltersHeader } from '../../components/AnalyticsFilters/AnalyticsFiltersHeader/AnalyticsFiltersHeader';
import { VidMobStack } from '../../../vidMobComponentWrappers';
import { CREATIVE_MANAGER_TITLE } from './helpers/CreativeManagerTexts';
import { getFeatureFlag } from '../../../utils/featureFlagUtils';
import useCreativeManagerContext from './hooks/useCreativeManagerContext';
import ColumnDropdown from '../../../muiCustomComponents/ColumnDropdown';
import DownloadCreativesButton from './components/DownloadCreativesButton';
import {
  AnalyticsReportBlankState,
  AnalyticsReportNoFiltersBlankState,
} from '../../reports/components/AnalyticsReportComponents/AnalyticsReportBlankStates';
import DataProcessing from '../../../components/DataProcessing';
import { CreativeManagerTabs } from './components/CreativeManagerTabs';
import { DataLimitationBanner } from './components/DataLimitationBanner';
import { CreativeManagerTable } from './components/CreativeManagerTable';
import {
  convertToColumnDropdownType,
  generateReportShareUrl,
  isCreativeManagerHasMinimumFilters,
  isShareUrlEnabled,
} from './helpers/CreativeManagerUtils';
import { ColumnsDropdownType } from '../../../muiCustomComponents/ColumnDropdown/types';
import { CHANNELS } from '../../../types/channels.types';
import { getHasConnectedAccounts } from '../../../redux/selectors/platformAccounts.selectors';
import { getAreSomeAccountsStillProcessing } from '../../utils/getAreSomeAccountsStillProcessing';
import { CreativeManagerClientDataStatusEnum } from './helpers/CreativeManagerTypes';
import { CompareDrawer } from './components/CompareDrawer';
import {
  creativeManagerContentStyle,
  creativeManagerContentWithDataStyle,
} from './helpers/CreativeManagerStyles';

const isPdfExportEnabled = getFeatureFlag('isAnalyticsReportsPDFExportEnabled');
const { LOADING, SUCCEED, FAILED } = CreativeManagerClientDataStatusEnum;

const CreativeManagerContent = () => {
  const {
    onDownloadCSV,
    onDownloadPDF,
    columns,
    setColumns,
    filters,
    dataObject,
    organizationId,
    showToast,
  } = useCreativeManagerContext();
  const { platform, adAccounts, mutationKey } = filters;
  const { status, items, filters: columnFilters } = dataObject;
  const [showBanner, setShowBanner] = useState<boolean>(false);
  const columnsDropdown = convertToColumnDropdownType(columns);
  const hasMinimumFilters = isCreativeManagerHasMinimumFilters(filters);

  // Processing part start
  const hasConnectedAccounts = useSelector(getHasConnectedAccounts);
  const areSomeAdAccountsStillProcessing = getAreSomeAccountsStillProcessing(
    adAccounts || [],
  );
  const shouldShowDataProcessingState =
    hasConnectedAccounts && areSomeAdAccountsStillProcessing;
  // Processing part end

  useEffect(() => {
    setShowBanner(platform === CHANNELS.FACEBOOK && hasMinimumFilters);
  }, [mutationKey]);

  const handleColumnsUpdate = (columns: ColumnsDropdownType[]) => {
    setColumns((prev) => {
      return prev.map((column) => {
        const newColumn = columns.find((c) => c.id === column.field);
        if (newColumn) {
          return {
            ...column,
            hide: newColumn.hide,
          };
        }
        return column;
      });
    });
  };

  const handleShareUrl = async () => {
    const shareUrl = await generateReportShareUrl(organizationId);
    if (shareUrl) {
      await navigator.clipboard.writeText(shareUrl);
      showToast('toastAlert.share.link.copied', 'info');
      return;
    }

    showToast('toastAlert.share.link.failed', 'error');
  };

  const actionBarRightSlots = [
    <ColumnDropdown
      selectedColumns={
        columnsDropdown.filter(
          (column) => !column.hide,
        ) as ColumnsDropdownType[]
      }
      columns={columnsDropdown}
      updateColumns={handleColumnsUpdate}
      key="ColumnDropdown"
    />,
    <DownloadCreativesButton key="DownloadButton" />,
  ];

  const menuActions = {
    onDownloadCSV,
    ...(isPdfExportEnabled && {
      onDownloadPDF,
    }),
  };

  const renderMainView = () => {
    if (!hasMinimumFilters) {
      return (
        <AnalyticsReportNoFiltersBlankState className="creative-manager-no-filters-blank-state" />
      );
    }

    if (shouldShowDataProcessingState) {
      return <DataProcessing />;
    }

    if (
      status === SUCCEED &&
      items.length === 0 &&
      columnFilters.length === 0
    ) {
      return (
        <AnalyticsReportBlankState className="creative-manager-blank-state" />
      );
    }

    if (status !== FAILED) {
      const isLoading = status === LOADING;
      return (
        <VidMobStack
          sx={{
            ...creativeManagerContentWithDataStyle,
            pointerEvents: isLoading ? 'none' : 'unset',
          }}
        >
          <CreativeManagerTabs />
          <CreativeManagerTable />
          {!isLoading && <CompareDrawer />}
        </VidMobStack>
      );
    }

    // Need to add error state
    return null;
  };

  return (
    <VidMobStack sx={creativeManagerContentStyle}>
      <AnalyticsFiltersHeader
        headerTitle={CREATIVE_MANAGER_TITLE}
        actionBarRightSlots={actionBarRightSlots}
        menuActions={menuActions}
        areDownloadsDisabled={false}
        onCopyURL={handleShareUrl}
        isSavedReport={isShareUrlEnabled(filters)}
      />
      <DataLimitationBanner
        showBanner={showBanner}
        setShowBanner={setShowBanner}
      />
      {renderMainView()}
    </VidMobStack>
  );
};

export default CreativeManagerContent;
