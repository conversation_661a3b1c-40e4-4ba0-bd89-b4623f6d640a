import { Dispatch, SetStateAction, MutableRefObject } from 'react';
import { BasicWorkspace } from '../../../../types/workspace.types';
import { AdAccount } from '../../../../types/adAccount.types';
import { Kpi } from '../../../../types/kpi.types';
import {
  GridColDef,
  GridFilterItem,
  GridSortDirection,
} from '@mui/x-data-grid-pro';
import {
  AdvancedFiltersValueType,
  AnalyticsFiltersType,
  GlobalFiltersType,
  Operator,
} from '../../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import { Currency } from '../../../../types/currency.types';
import { IdAndName } from '../../../../types/common.types';
import { MediaType } from '../../../../types/mediaTypes.types';

export type CreativeManagerTableColumnType =
  | 'nameAndMedia'
  | 'kpi'
  | 'format'
  | 'duration'
  | 'ad accounts'
  | 'campaigns'
  | 'ads'
  | 'posts'
  | 'creatives'
  | 'impressions';

export type CreativeManagerTableColumnConfig = Record<string, GridColDef>;

export interface CreativeManagerFilters extends AnalyticsFiltersType {
  globalFilters: Omit<GlobalFiltersType, 'kpi'> & {
    kpi: {
      operator: Operator;
      value: Kpi;
    };
  };
}

export interface CreativeManagerRequestParams {
  startDate: string;
  endDate: string;
  workspaceIds: number[];
  platform: string;
  adAccountIds: string[];
  kpiId: string;
  advancedFilters?: Record<string, AdvancedFiltersValueType>;
  currency?: string;
}

export interface PlatformMediaRequestOptions {
  tagSource: string;
  tagSources: string[];
  tagTypes: string[];
  withMedia: boolean;
  withTags: boolean;
}

export interface Metadata {
  ad: IdAndName[];
  post: { id: string }[];
  advideo: string[];
  campaign: IdAndName[];
  account: IdAndName[];
  page: IdAndName[];
  isCreativeInMultiAssetAd: boolean;
}

interface Thumbnail {
  height: number;
  width: number;
  url: string;
}

export interface Media {
  id: number | string;
  name: string;
  displayName: string;
  fileType: MediaType;
  mediaType: string;
  downloadUrl: string;
  height: number;
  width: number;
  duration: number;
  processingState: string;
  streams: Record<string, string>;
  thumbnails: Thumbnail[];
  format: string | undefined;
}

export interface PlatformMedia {
  createdByVidmob: boolean;
  id: number;
  mediaId: number;
  media: Media;
  platform: string;
  platformAccountId: string;
  platformMediaId: string;
  error?: string;
}

export interface Creative {
  campaigns: IdAndName[];
  createdByVidmob: boolean;
  format: { title: string };
  formattedValue: string;
  id: string;
  impressions: number;
  kpiValue?: number;
  media: Media;
  mediaId: number;
  metadata: Metadata;
  name: string;
  platform: string;
  platformAccountId: string;
  platformMedia: PlatformMedia;
  platformMediaId: string;
  stats: number;
  title: string;
}

export interface Ad {
  format: { title: string };
  formattedValue: string;
  impressions: number;
  kpiValue?: number;
  metadata: Metadata;
  metrics: any[];
  stats: number;
  title: string;
}

export interface CreativeManagerContextType {
  selectedTab: CreativeManagerTabEnum;
  filters: CreativeManagerFilterType;
  isAdPlatform: boolean;
  columns: CreativeManagerColumnsDropdownType[];
  creativeData: CreativeManagerClientDataType;
  adData: CreativeManagerClientDataType;
  dataObject: CreativeManagerClientDataType;
  isCompareDrawerOpen: boolean;
  isFetchAllItems: boolean;
  isAllItemsFetched: boolean;
  organizationId: string;
  selectAllStateRef: MutableRefObject<CreativeManagerSelectAllStateEnum>;
  showToast: (messageId: string, type: 'error' | 'info') => void;
  setIsFetchAllItems: Dispatch<SetStateAction<boolean>>;
  setIsAllItemsFetched: Dispatch<SetStateAction<boolean>>;
  setIsCompareDrawerOpen: Dispatch<SetStateAction<boolean>>;
  updateDataObject: (
    dataObject: Partial<CreativeManagerClientDataType>,
  ) => void;
  onDownloadCSV: () => void;
  onDownloadPDF: () => void;
  setSelectedItemsInReduxStore: (items: string[]) => void;
  setSelectedTab: Dispatch<SetStateAction<CreativeManagerTabEnum>>;
  setFilters: Dispatch<SetStateAction<CreativeManagerFilterType>>;
  setColumns: Dispatch<SetStateAction<CreativeManagerColumnsDropdownType[]>>;
  setCreativeData: Dispatch<SetStateAction<CreativeManagerClientDataType>>;
  setAdData: Dispatch<SetStateAction<CreativeManagerClientDataType>>;
}

export interface CreativeManagerFilterType {
  workspaces?: BasicWorkspace[];
  platform?: string;
  adAccounts?: AdAccount[];
  kpi?: Kpi;
  startDate?: string;
  endDate?: string;
  advancedFilters?: { [key: string]: AdvancedFiltersValueType };
  currency?: Currency;
  mutationKey?: number;
}

export enum CreativeManagerTabEnum {
  AD = 'AD',
  CREATIVE = 'CREATIVE',
}

export type CreativeManagerTableView = keyof typeof CreativeManagerTabEnum;

export enum CreativeManagerClientDataStatusEnum {
  LOADING = 'LOADING',
  SUCCEED = 'SUCCEED',
  FAILED = 'FAILED',
}

export enum CreativeManagerSelectAllStateEnum {
  NONE = 'NONE',
  SELECT_ALL = 'SELECT_ALL',
  UN_SELECT_ALL = 'UN_SELECT_ALL',
}

export interface CreativeManagerClientDataType {
  pageNumber: number;
  totalCount: number;
  items: CreativeManagerClientDataItemType[];
  fullItemList: CreativeManagerClientDataItemType[];
  selectedItems: string[];
  status: CreativeManagerClientDataStatusEnum;
  sort: {
    sortBy: string;
    sortDirection: GridSortDirection;
  };
  filters: GridFilterItem[];
}

export type CreativeManagerColumnsDropdownType = GridColDef & {
  hide: boolean;
};

export interface CreativeManagerMediaInfoType {
  name: string;
  media: Media;
  view: string;
  ICVlink: string;
  platform: string;
  platformMediaId: string;
}

export interface CreativeManagerClientDataItemType {
  id: string;
  nameAndMedia?: CreativeManagerMediaInfoType;
  impressions?: number;
  ads?: number;
  campaigns?: { id: string; name: string }[];
  ad_accounts?: { id: string; name: string }[];
  duration?: number;
  creatives?: number;
  format?: string;
  kpi?: { formattedValue: string };
  isCreativeInMultiAssetAd?: boolean;
  adIds?: string[];
}

export interface CreativeManagerServerDataItemType {
  platformMediaIds?: string[];
  impressions?: number;
  platformAdAccounts?: { id: string; name: string }[];
  duration?: number;
  platformMediaId?: string;
  mediaFormat?: string;
  adId?: string;
  adIds?: string[];
  kpiValue?: { value: number; format: string };
  isCreativeInMultiAssetAd?: boolean;
  campaignNames?: string[];
  currency?: string;
}

export interface CreativeManagerNestApiPayloadType {
  startDate?: string;
  endDate?: string;
  workspaceIds?: number[];
  platform?: string;
  adAccountIds?: string[];
  kpiId?: string;
  advancedFilters?: { [key: string]: AdvancedFiltersValueType };
  sortBy: string;
  sortDirection: string;
  search?: CreativeManagerColumnSearchType;
  currency?: string;
}

export interface CreativeManagerColumnSearchType {
  [key: string]:
    | string[]
    | {
        greaterThanOrEqualTo?: number;
        lessThanOrEqualTo?: number;
        equalTo?: number;
      };
}

export interface CreativeManagerHooksDataPropsType {
  filters: CreativeManagerFilterType;
  dataObject: CreativeManagerClientDataType;
  organizationId: string;
  isFetchAllItems: boolean;
  isAllItemsFetched: boolean;
  setIsFetchAllItems: Dispatch<SetStateAction<boolean>>;
  setIsAllItemsFetched: Dispatch<SetStateAction<boolean>>;
  showToast?: (messageId: string, type: 'error' | 'info') => void;
}
