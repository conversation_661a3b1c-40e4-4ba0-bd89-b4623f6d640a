export const creativeManagerContentStyle = {
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: 'background.default',
  boxSizing: 'border-box',
};

export const creativeManagerContentWithDataStyle = {
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
  position: 'relative',
  p: '0 24px',
  minHeight: 0,
  backgroundColor: 'background.default',
};

export const creativeManagerTabStyle = {
  textTransform: 'none',
  width: '276px',
  justifyContent: 'flex-start',
  flexDirection: 'row',
  alignItems: 'center',
  height: '52px',
  minHeight: '52px',
  maxHeight: '52px',
  border: '1px solid #D6D6D6',
  borderBottom: 'none',
  borderTopRightRadius: '8px',
  borderTopLeftRadius: '8px',
  marginRight: '4px',
  gap: '12px',
  '& .MuiTab-iconWrapper': {
    margin: 0,
  },
};

export const creativeManagerTabsWrapperStyle = {
  width: '100%',
  '& .MuiTablePagination-root': {
    '& .MuiTablePagination-displayedRows': { margin: 0 },
    '& .MuiTablePagination-actions': {
      '& .MuiButtonBase-root': { height: '32px !important' },
    },
    '& .MuiToolbar-root': { minHeight: 0, paddingRight: 0 },
  },
};

export const creativeManagerNameAndMediaCellWrapperStyle = {
  '& .creative-manager-name-column-cell': {
    display: 'flex',
    width: '100%',
    alignItems: 'center',
    '& .creative-manager-name-column-cell-thumbnail, .MuiCardMedia-root, .shimmer-and-fade-loading-state':
      {
        borderRadius: '4px',
      },
    '& .creative-manager-name-column-cell-text': {
      marginLeft: '12px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      '& .creative-manager-name-column-cell-name': {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        width: '300px',
      },
    },
    '& a': {
      fontWeight: 600,
    },
  },
  '& .creative-manager-name-column-cell > a': {
    fontWeight: 600,
    height: 40,
  },
};

export const creativeManagerDisabledCheckboxStyle = {
  '& .MuiCheckbox-root': {
    color: '#BDBDBD',
    pointerEvents: 'none',
    cursor: 'default',
  },
};

export const creativeManagerTableStyle = {
  transition: 'margin-bottom 0.4s ease',
  '.MuiDataGrid-columnHeaderTitleContainerContent, .MuiDataGrid-menuIconButton':
    {
      mr: '2px',
    },
  '& .MuiDataGrid-columnSeparator': {
    opacity: '0 !important',
  },
  '& .MuiDataGrid-cell, .MuiDataGrid-columnHeader': {
    borderRightWidth: '1px',
    borderRightStyle: 'solid',
  },
  '& .MuiDataGrid-cell': { outline: 'unset !important' },
  '& .MuiDataGrid-columnHeader': {
    outline: 'unset !important',
  },
};

export const compareMediaDrawerStyle = {
  width: '100%',
  ml: '23px',
  position: 'absolute',
  transition: '0.4s ease-out',
  zIndex: 1,
  left: '-24px',
};

export const creativeCompareTabStyle = {
  position: 'absolute',
  bottom: '100%',
  right: '25px',
  minWidth: '30px',
  width: '50px',
  height: '47px',
  borderRadius: '50% 50% 0 0',
};
