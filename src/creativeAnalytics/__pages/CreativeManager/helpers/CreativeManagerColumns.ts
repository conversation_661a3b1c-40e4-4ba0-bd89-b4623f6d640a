import {
  CREATIVE_MANAGER_ADS_TABLE_COLUMNS,
  CREATIVE_MANAGER_CREATIVE_TABLE_COLUMNS,
  CREATIVE_MANAGER_CREATIVE_TABLE_COLUMNS_ORGANIC,
  CREATIVE_MANAGER_HEADER_FIELD_TO_LABEL,
  FIELD_KPI,
  FIELD_NAME_AND_MEDIA,
} from './CreativeManagerConstants';
import { CreativeManagerTableColumnType } from './CreativeManagerTypes';
import { DefaultCell } from '../components/tableComponents/DefaultCell';
import { ColumnHeader } from '../components/tableComponents/ColumnHeader';
import {
  FILTERED_COLUMNS_MAP,
  PER_PAGE,
  SORTED_COLUMNS_MAP,
} from './CreativeManagerConstants';
import { getIntl } from '../../../../utils/getIntl';
import { CreativeManagerColumnsDropdownType } from './CreativeManagerTypes';
import {
  getGridNumericOperators,
  getGridStringOperators,
} from '@mui/x-data-grid';

const intl = getIntl();

const CreativeManagerNumericOperators = getGridNumericOperators().filter(
  (operator) =>
    operator.value === '>=' ||
    operator.value === '<=' ||
    operator.value === '=',
);

const CreativeManagerStringOperators = getGridStringOperators().filter(
  (operator) => operator.value === 'contains',
);

export function getCreativeManagerLoaderRows(
  columns: CreativeManagerColumnsDropdownType[],
) {
  return Array.from({ length: PER_PAGE }, (_, i) => {
    const row: { [key: string]: string } = { id: `loading ${i}` };
    columns.forEach((col) => {
      row[col.field] = 'loading';
    });
    return row;
  });
}

export function getCreativeManagerV3AdColumns(): CreativeManagerColumnsDropdownType[] {
  return Object.values(CREATIVE_MANAGER_ADS_TABLE_COLUMNS).map((colItem) => {
    const field = colItem.field as CreativeManagerTableColumnType;
    const label = `${
      intl.messages[CREATIVE_MANAGER_HEADER_FIELD_TO_LABEL[field]]
    }`;
    const hideable = field !== FIELD_NAME_AND_MEDIA && field !== FIELD_KPI;
    const isString = colItem.type === 'string';

    return {
      ...colItem,
      field: colItem.field.replace(' ', '_'),
      headerName: label,
      align: 'left',
      headerAlign: 'left',
      hide: false,
      hideable,
      sortable: Boolean(SORTED_COLUMNS_MAP[field]),
      filterable: Boolean(FILTERED_COLUMNS_MAP[field]),
      renderHeader: ColumnHeader,
      renderCell: DefaultCell,
      filterOperators: isString
        ? CreativeManagerStringOperators
        : CreativeManagerNumericOperators,
    };
  });
}

export function getCreativeManagerCreativeColumns(): CreativeManagerColumnsDropdownType[] {
  return Object.values(CREATIVE_MANAGER_CREATIVE_TABLE_COLUMNS).map(
    (colItem) => {
      const field = colItem.field as CreativeManagerTableColumnType;
      const label = `${
        intl.messages[CREATIVE_MANAGER_HEADER_FIELD_TO_LABEL[field]]
      }`;
      const hideable = field !== FIELD_NAME_AND_MEDIA && field !== FIELD_KPI;
      const isString = colItem.type === 'string';

      return {
        ...colItem,
        field: colItem.field.replace(' ', '_'),
        headerName: label,
        align: 'left',
        headerAlign: 'left',
        hide: false,
        hideable,
        sortable: Boolean(SORTED_COLUMNS_MAP[field]),
        filterable: Boolean(FILTERED_COLUMNS_MAP[field]),
        renderHeader: ColumnHeader,
        renderCell: DefaultCell,
        filterOperators: isString
          ? CreativeManagerStringOperators
          : CreativeManagerNumericOperators,
      };
    },
  );
}

export function getCreativeManagerOrganicColumns(): CreativeManagerColumnsDropdownType[] {
  return Object.values(CREATIVE_MANAGER_CREATIVE_TABLE_COLUMNS_ORGANIC).map(
    (colItem) => {
      const field = colItem.field as CreativeManagerTableColumnType;
      const label = `${
        intl.messages[CREATIVE_MANAGER_HEADER_FIELD_TO_LABEL[field]]
      }`;
      const hideable = field !== FIELD_NAME_AND_MEDIA && field !== FIELD_KPI;
      const isString = colItem.type === 'string';

      return {
        ...colItem,
        field: colItem.field.replace(' ', '_'),
        headerName: label,
        align: 'left',
        headerAlign: 'left',
        hide: false,
        hideable,
        sortable: Boolean(SORTED_COLUMNS_MAP[field]),
        filterable: Boolean(FILTERED_COLUMNS_MAP[field]),
        renderHeader: ColumnHeader,
        renderCell: DefaultCell,
        filterOperators: isString
          ? CreativeManagerStringOperators
          : CreativeManagerNumericOperators,
      };
    },
  );
}
