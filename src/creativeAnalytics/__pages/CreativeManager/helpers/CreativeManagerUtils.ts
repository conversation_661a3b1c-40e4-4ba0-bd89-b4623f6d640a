import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
// @ts-expect-error: T<PERSON> thinks there is no default export, but there is
import J<PERSON><PERSON><PERSON>Utils from 'jszip-utils';
import { saveAs } from 'file-saver';
import {
  CREATIVE_MANAGER_SHARE_URL_PARAM_KEY,
  CREATIVE_MANAGER_TABLE_VIEWS,
  FILTERED_COLUMNS_MAP,
  MEDIA_TYPE_TO_DEFAULT_EXTENSION,
  PER_PAGE,
  SORTED_COLUMNS_MAP,
  SUPPORTED_FILE_EXTENSIONS,
} from './CreativeManagerConstants';
import {
  CreativeManagerClientDataItemType,
  CreativeManagerClientDataStatusEnum,
  CreativeManagerClientDataType,
  CreativeManagerColumnsDropdownType,
  CreativeManagerColumnSearchType,
  CreativeManagerFilterType,
  CreativeManagerHooksDataPropsType,
} from './CreativeManagerTypes';
import { ColumnsDropdownType } from '../../../../muiCustomComponents/ColumnDropdown/types';
import {
  Creative,
  CreativeManagerTableView,
  PlatformMedia,
} from './CreativeManagerTypes';
import { composeLinkHelperIndividualCreativeView } from '../../../helpers/composeLinkHelper';
import { CREATIVE_MANAGER } from '../../../../constants/creativeAnalytics.constants';
import { KpiFormatter } from '../../../services/KpiEngine/KpiFormatter';
import {
  AdvancedFiltersValueType,
  AdvancedFilterType,
  AnalyticsFiltersType,
  MultiValue,
} from '../../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import { getCreativeManagerLoaderRows } from './CreativeManagerColumns';
import { GridFilterItem, GridSortDirection } from '@mui/x-data-grid-pro';
import { Currency } from '../../../../types/currency.types';
import { DEFAULT_CURRENCY } from '../../../components/AnalyticsFilters/AnalyticsFiltersConstants';
import {
  BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS,
  Operator,
  REPORT_ADVANCED_FILTERS,
} from '../../../../components/ReportFilters/types';
import AnalyticsSavedReportsService from '../../../reports/services/AnalyticsSavedReportsService';
import {
  getAnalyticsFiltersFromLocalStorage,
  getCurrencyFromLocalStorage,
} from '../../../components/AnalyticsFilters/utils/localStorageFunctions';

const { SUCCEED } = CreativeManagerClientDataStatusEnum;
const { createSavedReport } = AnalyticsSavedReportsService;

export function convertAnyAnalyticsFiltersToCreativeManagerFilters(
  anyAnalyticsFilter: AnalyticsFiltersType,
  currency: Currency,
): CreativeManagerFilterType {
  const globalFilters = anyAnalyticsFilter?.globalFilters;
  const advancedFilters = anyAnalyticsFilter?.advancedFilters;

  return {
    workspaces: globalFilters?.workspaces?.value || undefined,
    platform: globalFilters?.channel?.value || undefined,
    adAccounts: globalFilters?.adAccounts?.value || undefined,
    kpi: globalFilters?.kpi?.value || undefined,
    startDate: globalFilters?.startDate || undefined,
    endDate: globalFilters?.endDate || undefined,
    advancedFilters: convertToClientAdvancedFiltersToServer(advancedFilters),
    currency,
    mutationKey: new Date().getTime(),
  };
}

export function convertToClientAdvancedFiltersToServer(
  input: AdvancedFilterType[],
) {
  const result: { [key: string]: AdvancedFiltersValueType } = {};
  const {
    AD_IDENTIFIER,
    AD_IMPRESSION,
    AD_PLACEMENT,
    AD_TYPE,
    AD_SET_IDENTIFIER,
    CAMPAIGN_IDENTIFIER,
    CAMPAIGN_OBJECTIVE,
    CREATIVE_BY_VIDMOB,
    CREATIVE_IMPRESSION,
  } = REPORT_ADVANCED_FILTERS;

  input.forEach((item) => {
    switch (item.id) {
      case AD_IDENTIFIER:
        result.adIdentifiers = (item.value as MultiValue)?.map(
          (v) => v.id || '',
        );
        break;
      case AD_IMPRESSION:
        result.adImpressions = convertToAggregateFilter(item);
        break;
      case AD_PLACEMENT:
        result.adPlacements = (item.value as MultiValue)?.map(
          (v) => v.originalName?.toString() || '',
        );
        break;
      case AD_TYPE:
        result.adTypes = (item.value as MultiValue)?.map(
          (v) => v.originalName?.toString() || '',
        );
        break;
      case AD_SET_IDENTIFIER:
        result.adsetIdentifiers = (item.value as MultiValue)?.map(
          (v) => v.id || '',
        );
        break;
      case CAMPAIGN_IDENTIFIER:
        result.campaignIdentifiers = (item.value as MultiValue)?.map(
          (v) => v.id || '',
        );
        break;
      case CAMPAIGN_OBJECTIVE:
        result.campaignObjectives = (item.value as MultiValue)?.map(
          (v) => v.originalName?.toString() || '',
        );
        break;
      case CREATIVE_BY_VIDMOB:
        result.createdByVidmob = (item.value as MultiValue)?.some(
          (v) =>
            v.id ===
            BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.CREATIVE_BY_VIDMOB,
        );
        break;
      case CREATIVE_IMPRESSION:
        result.creativeImpressions = convertToAggregateFilter(item);
        break;
      case 'creativeMediaType':
        result.mediaTypes = (item.value as MultiValue)?.map((v) => v.id || '');
        break;
    }
  });

  return result;
}

function convertToAggregateFilter(item: any) {
  const filter: { [key: string]: AdvancedFiltersValueType } = {};

  if (
    typeof item.value === 'object' &&
    'min' in item.value &&
    'max' in item.value
  ) {
    filter.greaterThanOrEqualTo = item.value.min;
    filter.lessThanOrEqualTo = item.value.max;
  } else if (item.operator === Operator.GREATER_THAN) {
    filter.greaterThanOrEqualTo = parseFloat(item.value);
  } else if (item.operator === Operator.LESS_THAN) {
    filter.lessThanOrEqualTo = parseFloat(item.value);
  } else if (item.operator === Operator.EQUAL_TO) {
    filter.equalTo = parseFloat(item.value);
  }

  return filter;
}

export function filterCreativeManagerClientTableRows(
  items: CreativeManagerClientDataItemType[],
  filterItems: GridFilterItem[],
): CreativeManagerClientDataItemType[] {
  const applyFilter = (
    item: CreativeManagerClientDataItemType,
    filter: GridFilterItem,
  ): boolean => {
    if (filter.value === null || filter.value === undefined) {
      return true;
    }

    const filterKey = FILTERED_COLUMNS_MAP[filter.field];

    const getValue = (
      item: CreativeManagerClientDataItemType,
    ): string | number => {
      switch (filterKey) {
        case 'displayName':
          return item.nameAndMedia?.name || '';
        case 'mediaFormat':
          return item.format || '';
        case 'kpiValue':
          return parseFloat(item.kpi?.formattedValue || '') || 0;
        case 'duration':
          return item.duration || 0;
        case 'adCount':
          return item.ads || 0;
        case 'creativeCount':
          return item.creatives || 0;
        default:
          return 0;
      }
    };

    const itemValue = getValue(item);
    const filterValue = filter.value;

    switch (filter.operator) {
      case '=':
        return itemValue == filterValue;
      case '>=':
        return itemValue >= filterValue;
      case '<=':
        return itemValue <= filterValue;
      case 'contains':
        if (typeof itemValue === 'string' && typeof filterValue === 'string') {
          return itemValue.toLowerCase().includes(filterValue.toLowerCase());
        }
        return false;
      default:
        return true;
    }
  };

  return items.filter((item) =>
    filterItems.every((filter) => applyFilter(item, filter)),
  );
}

export function sortCreativeManagerClientTableRows(
  items: CreativeManagerClientDataItemType[],
  field: string,
  sort: GridSortDirection,
): CreativeManagerClientDataItemType[] {
  const sortedKey = SORTED_COLUMNS_MAP[field];
  const copyItems = [...items];

  if (!sortedKey) return copyItems;

  const getValue = (item: CreativeManagerClientDataItemType) => {
    switch (sortedKey) {
      case 'displayName':
        return item.nameAndMedia?.name;
      case 'kpiValue':
        return item.kpi?.formattedValue;
      case 'duration':
        return item.duration;
      case 'adAccountNames':
        return item.ad_accounts ? item.ad_accounts[0].name : '';
      case 'campaignNames':
        return item.campaigns ? item.campaigns[0].name : '';
      case 'adCount':
        return item.ads;
      case 'impressions':
        return item.impressions;
      case 'creativeCount':
        return item.creatives;
      default:
        return 0;
    }
  };

  return copyItems.sort((a, b) => {
    const aValue = getValue(a);
    const bValue = getValue(b);

    if (aValue === undefined || bValue === undefined) return 0;

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sort === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sort === 'asc' ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });
}

export function getCreativeManagerTableRows(
  columns: CreativeManagerColumnsDropdownType[],
  dataObject: CreativeManagerClientDataType,
) {
  const { status, items } = dataObject;
  if (status === SUCCEED) {
    return items;
  }

  return getCreativeManagerLoaderRows(columns);
}

export function convertFiltersToSearch(columnFilters: GridFilterItem[]) {
  let search: CreativeManagerColumnSearchType | undefined = undefined;

  columnFilters.forEach((filter) => {
    const key = FILTERED_COLUMNS_MAP[filter.field.replace('_', ' ')];
    if (key && filter.value !== null && filter.value !== undefined) {
      search = search || {};
      switch (filter.operator) {
        case '>=':
          search[key] = { greaterThanOrEqualTo: filter.value };
          break;
        case '<=':
          search[key] = { lessThanOrEqualTo: filter.value };
          break;
        case '=':
          search[key] = { equalTo: filter.value };
          break;
        case 'contains':
          search[key] = [filter.value];
          break;
      }
    }
  });
  return search;
}

export function getCreativeManagerParsedParams(
  props: CreativeManagerHooksDataPropsType,
) {
  const { filters, dataObject, organizationId, isFetchAllItems } = props;
  const { pageNumber, totalCount, sort, filters: columnFilters } = dataObject;
  const offset = pageNumber * PER_PAGE;

  return {
    organizationId,
    pagination: {
      perPage: isFetchAllItems ? totalCount : PER_PAGE,
      offset: isFetchAllItems ? 0 : offset,
      totalCount,
    },
    payload: {
      startDate: filters.startDate,
      endDate: filters.endDate,
      workspaceIds: filters.workspaces?.map((w) => w.id),
      platform: filters.platform,
      adAccountIds: filters.adAccounts?.map((a) => a.platformAccountId),
      kpiId: filters.kpi?.id,
      advancedFilters: filters.advancedFilters,
      sortBy: SORTED_COLUMNS_MAP[sort.sortBy.replace('_', ' ')] || 'kpiValue',
      sortDirection: (sort.sortDirection || 'desc').toUpperCase(),
      search: convertFiltersToSearch(columnFilters),
      currency: filters.currency?.id,
    },
    currency: filters.currency,
  };
}

export function convertToColumnDropdownType(
  columns: CreativeManagerColumnsDropdownType[],
): ColumnsDropdownType[] {
  return columns
    .filter((column) => column.hideable)
    .map((column) => ({
      id: column.field,
      name: column.headerName,
      hide: column.hide,
    })) as ColumnsDropdownType[];
}

export function convertToColumnVisibilityModel(
  columns: CreativeManagerColumnsDropdownType[],
): {
  [key: string]: boolean;
} {
  return columns.reduce(
    (acc, item) => {
      acc[item.field] = !item.hide;
      return acc;
    },
    {} as { [key: string]: boolean },
  );
}

export function isCreativeManagerHasMinimumFilters(
  filters: CreativeManagerFilterType,
): boolean {
  const { endDate, startDate, workspaces, platform, adAccounts, kpi } = filters;

  return (
    Boolean(workspaces?.length) &&
    Boolean(adAccounts?.length) &&
    Boolean(platform) &&
    Boolean(kpi) &&
    Boolean(startDate) &&
    Boolean(endDate)
  );
}

export function parseMediaInfo(
  isCreativeInMultiAssetAd: boolean,
  kpiValue: { value: number; format: string },
  platformMediaId: string,
  platformAdAccounts: { id: string; name: string }[],
  platformMediaList: { [key: string]: PlatformMedia },
  platform: string,
  view: string,
  currency: Currency = DEFAULT_CURRENCY,
) {
  const platformMedia = (platformMediaList[platformMediaId] =
    platformMediaList[platformMediaId] || {});
  const { media } = platformMedia;
  const name = media?.displayName || media?.name || '';
  const adAccountId = (platformAdAccounts && platformAdAccounts[0]?.id) || '';

  const ICVlink = composeLinkHelperIndividualCreativeView({
    platformMediaId: platformMediaId,
    platform: platform,
    source: CREATIVE_MANAGER,
    isCreativeOfMultiAssetAds: isCreativeInMultiAssetAd,
    adAccountId,
  });

  const mediaInfo = {
    name,
    media,
    view,
    ICVlink,
    platform,
    platformMediaId,
  };

  const value = kpiValue?.value;
  const format = kpiValue?.format?.toLowerCase() || 'none';
  const kpiFormatter = KpiFormatter[format];
  const formattedValue = kpiFormatter(value, false, currency);

  return {
    mediaInfo,
    formattedValue,
  };
}

export function isCreativeManagerSelectedItemsEqualToReduxStore(
  arr1: string[],
  arr2: string[],
): boolean {
  if (arr1.length !== arr2.length) return false;

  const countMap = new Map<string, number>();

  for (const item of arr1) {
    countMap.set(item, (countMap.get(item) || 0) + 1);
  }

  for (const item of arr2) {
    if (!countMap.has(item)) return false;
    countMap.set(item, countMap.get(item)! - 1);
    if (countMap.get(item) === 0) countMap.delete(item);
  }

  return countMap.size === 0;
}

export function getCreativeManagerSelectedItemsForView(
  creativeItems: CreativeManagerClientDataItemType[],
  selectedItems: string[],
): { selectedCreatives: string[]; selectedAds: string[] } {
  const tmpAdIds: { [key: string]: boolean } = {};
  creativeItems.forEach((item) => {
    if (selectedItems.includes(item.id)) {
      item.adIds?.forEach((adId: string) => {
        tmpAdIds[adId] = true;
      });
    }
  });
  const selectedAds = Object.keys(tmpAdIds);

  return {
    selectedCreatives: selectedItems,
    selectedAds: selectedAds,
  };
}

export function isShareUrlEnabled(filters: CreativeManagerFilterType): boolean {
  const { platform, startDate, endDate, workspaces, adAccounts } = filters;
  return Boolean(
    platform &&
      startDate &&
      endDate &&
      workspaces != null &&
      workspaces.length > 0 &&
      adAccounts != null &&
      adAccounts.length > 0,
  );
}

export async function generateReportShareUrl(
  organizationId: string,
): Promise<string | undefined> {
  try {
    const jsonStr = getAnalyticsFiltersFromLocalStorage();
    const currencyStr = getCurrencyFromLocalStorage();
    const analyticsFilters = jsonStr ? JSON.parse(jsonStr) : null;

    const currencyFilters = currencyStr ? JSON.parse(currencyStr) : null;
    const { globalFilters } = analyticsFilters;
    if (!globalFilters) return;
    const adAccountIds = globalFilters?.adAccounts?.value?.map(
      (account: any) => account.platformAccountId,
    );
    const workspaceIds = globalFilters?.workspaces?.value?.map(
      (account: any) => account.id,
    );
    // We need to remove it from the payload because the payload is too large.
    if (analyticsFilters?.globalFilters?.adAccounts) {
      delete analyticsFilters.globalFilters.adAccounts;
    }
    const payload = {
      name: 'Creative Manager',
      description: 'Creative Manager share link',
      reportType: 'CREATIVE_MANAGER',
      filters: {
        mediaTypes: ['VIDEO'],
        adAccountIds: adAccountIds,
        workspaceIds: workspaceIds,
        platform: globalFilters?.channel?.value,
        dateRange: {
          startDate: globalFilters?.startDate,
          endDate: globalFilters?.endDate,
        },
        analyticsFilters,
        currencyFilters,
      },
      sortBy: {
        sortBy: 'none',
        sortOrder: 'ASC',
      },
      groupBy: {
        columns: 'none',
        rows: 'none',
      },
      filtersVersion: 1,
    };

    const response = await createSavedReport(organizationId, payload);
    const { origin, pathname } = window.location;
    const shareUrl = `${origin}${pathname}?${CREATIVE_MANAGER_SHARE_URL_PARAM_KEY}=${response.id}`;
    return shareUrl;
  } catch (error) {
    console.error('Error creating saved report:', error);
    return undefined;
  }
}

export const generateFilename = (creative: Creative): string => {
  const { media } = creative;

  const displayName = media.displayName || media.name;
  const { fileType } = media;

  const extension = media.name.split('.').pop()?.toLowerCase();

  if (
    extension &&
    displayName.endsWith(extension) &&
    SUPPORTED_FILE_EXTENSIONS.includes(extension)
  ) {
    return displayName;
  }

  const defaultExtension = MEDIA_TYPE_TO_DEFAULT_EXTENSION[fileType] || '';

  return `${displayName}.${defaultExtension}`;
};

export const deDuplicateFiles = (
  listOfFilesToDownload: {
    fileName: string;
    url: string;
  }[],
): { fileName: string; url: string }[] => {
  const fileNameMap: { [key: string]: number } = {};
  return listOfFilesToDownload.map((file) => {
    const { fileName } = file;
    if (fileNameMap[fileName]) {
      fileNameMap[fileName]++;
      const extension = fileName.split('.').pop()?.toLowerCase();
      const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');
      const newFileName = `${nameWithoutExtension} (${fileNameMap[fileName] - 1}).${extension}`;
      return {
        ...file,
        fileName: newFileName,
      };
    }
    fileNameMap[fileName] = 1;
    return file;
  });
};

export const downloadAndZip = (
  listOfFilesToDownload: any[],
  zipFileName: string,
  onError?: (err: any) => void,
) => {
  const zip = new JSZip();
  let count = 0;

  const deDuplicatedFiles = deDuplicateFiles(listOfFilesToDownload);

  deDuplicatedFiles.forEach((file) => {
    const { fileName, url } = file;
    JSZipUtils.getBinaryContent(url, (err: any, data: any) => {
      if (err) {
        onError?.(err);
        return;
      }

      zip.file(fileName, data, { binary: true });
      count++;
      if (count === deDuplicatedFiles.length) {
        zip.generateAsync({ type: 'blob' }).then((content) => {
          saveAs(content, zipFileName);
        });
      }
    });
  });
};

export const generateZipFileName = () => {
  const date = new Date();
  const zipFileString = `Creative_Examples_${date.toLocaleString()}.zip`;
  return zipFileString.replace(/ /g, '_');
};

export const generateCSVFileName = (view: CreativeManagerTableView) => {
  const table = view === CREATIVE_MANAGER_TABLE_VIEWS.AD ? 'Ads' : 'Creatives';
  const date = new Date();
  const csvFileString = `${table}_Creative_Manager_${date.toLocaleString()}`; // .csv extension is added by tableApiRef.current.exportDataAsCsv
  return csvFileString.replace(/ /g, '_');
};
