import {
  CreativeManagerClientDataStatusEnum,
  CreativeManagerClientDataType,
} from './CreativeManagerTypes';

import {
  CreativeManagerTableColumnConfig,
  CreativeManagerTableView,
  PlatformMediaRequestOptions,
} from './CreativeManagerTypes';
import { MediaType } from '../../../../types/mediaTypes.types';

export const CREATIVE_MANAGER_TABLE_VIEWS: {
  [key: string]: CreativeManagerTableView;
} = {
  AD: 'AD',
  CREATIVE: 'CREATIVE',
};

export const CREATIVE_MANAGER_FETCH_DATA_ACTION_TYPE =
  'FETCH_CREATIVE_MANAGER_DATA';
export const CREATIVE_GROUPS_FETCH_DATA_ACTION_TYPE =
  'FETCH_CREATIVE_GROUPS_DATA';

const STANDARD_COLUMN_WIDTH = 160;
const STANDARD_COLUMN_MAX_WIDTH = 900;
const KPI_COLUMN_WIDTH = 200;
const NAME_COLUMN_WIDTH = 400;

export const FIELD_NAME_AND_MEDIA = 'nameAndMedia';
export const FIELD_KPI = 'kpi';
export const FIELD_AD_ACCOUNT = 'ad accounts';
export const FIELD_CAMPAIGNS = 'campaigns';
export const FIELD_CREATIVES = 'creatives';
export const FIELD_IMPRESSIONS = 'impressions';
export const FIELD_FORMAT = 'format';
export const FIELD_DURATION = 'duration';
export const FIELD_ADS = 'ads';
export const FIELD_POSTS = 'posts';

const NAME_AND_MEDIA_COLUMN = {
  field: FIELD_NAME_AND_MEDIA,
  width: NAME_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'string',
};
const KPI_COLUMN = {
  field: FIELD_KPI,
  width: KPI_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'number',
};
const AD_ACCOUNT_COLUMN = {
  field: FIELD_AD_ACCOUNT,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'string',
};
const CAMPAIGNS_COLUMN = {
  field: FIELD_CAMPAIGNS,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'string',
};
const CREATIVES_COLUMN = {
  field: FIELD_CREATIVES,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'number',
};
const IMPRESSIONS_COLUMN = {
  field: FIELD_IMPRESSIONS,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'number',
};
const FORMAT_COLUMN = {
  field: FIELD_FORMAT,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'string',
};
const DURATION_COLUMN = {
  field: FIELD_DURATION,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'number',
};
const ADS_COLUMN = {
  field: FIELD_ADS,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'number',
};
// Organic platforms only
const POSTS_COLUMN = {
  field: FIELD_POSTS,
  width: STANDARD_COLUMN_WIDTH,
  maxWidth: STANDARD_COLUMN_MAX_WIDTH,
  type: 'number',
};

export const CREATIVE_MANAGER_TABLE_COMMON_COLUMNS: CreativeManagerTableColumnConfig =
  {
    NAME_AND_MEDIA: NAME_AND_MEDIA_COLUMN,
    KPI: KPI_COLUMN,
  };

export const CREATIVE_MANAGER_ADS_TABLE_COLUMNS: CreativeManagerTableColumnConfig =
  {
    ...CREATIVE_MANAGER_TABLE_COMMON_COLUMNS,
    AD_ACCOUNT: AD_ACCOUNT_COLUMN,
    CAMPAIGNS: CAMPAIGNS_COLUMN,
    CREATIVES: CREATIVES_COLUMN,
    IMPRESSIONS: IMPRESSIONS_COLUMN,
  };

// Ad Accounts, Campaigns and Impressions are in both tables, but they're rendered in order
export const CREATIVE_MANAGER_CREATIVE_TABLE_COLUMNS: CreativeManagerTableColumnConfig =
  {
    ...CREATIVE_MANAGER_TABLE_COMMON_COLUMNS,
    FORMAT: FORMAT_COLUMN,
    DURATION: DURATION_COLUMN,
    AD_ACCOUNT: AD_ACCOUNT_COLUMN,
    CAMPAIGNS: CAMPAIGNS_COLUMN,
    ADS: ADS_COLUMN,
    IMPRESSIONS: IMPRESSIONS_COLUMN,
  };

export const CREATIVE_MANAGER_CREATIVE_TABLE_COLUMNS_ORGANIC: CreativeManagerTableColumnConfig =
  {
    ...CREATIVE_MANAGER_TABLE_COMMON_COLUMNS,
    FORMAT: FORMAT_COLUMN,
    DURATION: DURATION_COLUMN,
    AD_ACCOUNT: AD_ACCOUNT_COLUMN,
    POSTS: POSTS_COLUMN,
    IMPRESSIONS: IMPRESSIONS_COLUMN,
  };

export const CREATIVE_MANAGER_TITLE_KEY =
  'ui.creative.intelligence.creativeManager.title';
export const ADS_TABLE_TAB_LABEL_KEY =
  'ui.creative.intelligence.creativeManager.tab.ads.label';
export const CREATIVE_TABLE_TAB_LABEL_KEY =
  'ui.creative.intelligence.creativeManager.tab.creative.label';
export const TAB_CHIP_LABEL_SELECTED_KEY =
  'ui.creative.intelligence.creativeManager.tab.chip.selected.label';

export const CREATIVE_MANAGER_HEADER_FIELD_TO_LABEL = {
  [FIELD_NAME_AND_MEDIA]:
    'ui.creative.intelligence.creativeManager.column.nameAndMedia.label',
  [FIELD_KPI]: 'ui.creative.intelligence.creativeManager.column.kpi.label',
  [FIELD_AD_ACCOUNT]:
    'ui.creative.intelligence.creativeManager.column.adAccount.label',
  [FIELD_CAMPAIGNS]:
    'ui.creative.intelligence.creativeManager.column.campaigns.label',
  [FIELD_CREATIVES]:
    'ui.creative.intelligence.creativeManager.column.creatives.label',
  [FIELD_IMPRESSIONS]:
    'ui.creative.intelligence.creativeManager.column.impressions.label',
  [FIELD_FORMAT]:
    'ui.creative.intelligence.creativeManager.column.format.label',
  [FIELD_DURATION]:
    'ui.creative.intelligence.creativeManager.column.duration.label',
  [FIELD_ADS]: 'ui.creative.intelligence.creativeManager.column.ads.label',
  [FIELD_POSTS]: 'ui.creative.intelligence.creativeManager.column.posts.label',
};

export const SUPPORTED_FILE_EXTENSIONS = [
  'mov',
  'mp4',
  'mpeg',
  'm4v',
  'wmv',
  'jpg',
  'jpeg',
  'png',
  'gif',
];

export const MEDIA_TYPE_TO_DEFAULT_EXTENSION = {
  [MediaType.VIDEO]: 'mov',
  [MediaType.IMAGE]: 'jpg',
  [MediaType.PHOTO]: 'jpg',
  [MediaType.CAROUSEL_ALBUM]: 'jpg',
  [MediaType.ANIMATED_IMAGE]: 'gif',
  [MediaType.HTML]: 'html',
};

export const PLATFORM_MEDIA_REQUEST_DEFAULT_OPTIONS: PlatformMediaRequestOptions =
  {
    tagSource: 'RECOGNITION',
    tagSources: ['RECOGNITION', 'COLOR', 'MEDIA_FACTS'],
    tagTypes: [],
    withMedia: true,
    withTags: false,
  };

export const CREATIVE_MANAGER_SHARE_URL_PARAM_KEY = 'share_id';

export const PER_PAGE = 50;

export const INITIAL_DATA: CreativeManagerClientDataType = {
  pageNumber: 0,
  totalCount: 0,
  items: [],
  fullItemList: [],
  selectedItems: [],
  status: CreativeManagerClientDataStatusEnum.LOADING,
  sort: {
    sortBy: 'kpi',
    sortDirection: 'desc',
  },
  filters: [],
};

export const SORTED_COLUMNS_MAP: { [key: string]: string } = {
  [FIELD_NAME_AND_MEDIA]: 'displayName',
  [FIELD_KPI]: 'kpiValue',
  [FIELD_DURATION]: 'duration',
  [FIELD_AD_ACCOUNT]: 'adAccountNames',
  [FIELD_CAMPAIGNS]: 'campaignNames',
  [FIELD_ADS]: 'adCount',
  [FIELD_IMPRESSIONS]: 'impressions',
  [FIELD_CREATIVES]: 'creativeCount',
};

export const FILTERED_COLUMNS_MAP: { [key: string]: string } = {
  [FIELD_NAME_AND_MEDIA]: 'displayName',
  [FIELD_FORMAT]: 'mediaFormat',
  [FIELD_KPI]: 'kpiValue',
  [FIELD_DURATION]: 'duration',
  [FIELD_ADS]: 'adCount',
  [FIELD_CREATIVES]: 'creativeCount',
};
