import { deDuplicateFiles } from './CreativeManagerUtils';

describe('CreativeManagerUtils download helpers', () => {
  it('should de-duplicate files', () => {
    const files = [
      { fileName: 'file.mp4', url: 'https://example.com/file.mp4' },
      { fileName: 'file.mp4', url: 'https://example.com/file.mp4' },
      { fileName: 'file.mp4', url: 'https://example.com/file.mp4' },
    ];
    const result = deDuplicateFiles(files);
    expect(result).toEqual([
      { fileName: 'file.mp4', url: 'https://example.com/file.mp4' },
      { fileName: 'file (1).mp4', url: 'https://example.com/file.mp4' },
      { fileName: 'file (2).mp4', url: 'https://example.com/file.mp4' },
    ]);
  });

  it('should not de-duplicate files if there are no duplicates', () => {
    const files = [
      { fileName: 'file 2.mp4', url: 'https://example.com/file.mp4' },
      { fileName: 'file.mp4', url: 'https://example.com/file.mp4' },
    ];
    const result = deDuplicateFiles(files);
    expect(result).toEqual(files);
  });

  it('should de-duplicate files with different extensions', () => {
    const files = [
      { fileName: 'file.mp4', url: 'https://example.com/file.mp4' },
      { fileName: 'file.mp3', url: 'https://example.com/file.mp4' },
    ];
    const result = deDuplicateFiles(files);
    expect(result).toEqual(files);
  });
});
