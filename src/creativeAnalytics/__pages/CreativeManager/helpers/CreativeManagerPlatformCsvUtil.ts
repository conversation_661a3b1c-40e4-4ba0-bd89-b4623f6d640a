import BffService from '../../../../apiServices/BffService';
import { CreativeManagerNestApiPayloadType } from './CreativeManagerTypes';

type Props = {
  view: 'creatives' | 'ads';
  organizationId: string;
  payload: CreativeManagerNestApiPayloadType;
};

export async function exportCreativeManagerCSV(props: Props): Promise<boolean> {
  try {
    const { view, payload, organizationId } = props;
    const endpoint = `v1/creative-manager/organization/${organizationId}/${view}/csv`;
    const response = await BffService.handleBffApiPost(endpoint, payload);
    downloadCSV(response);
    return true;
  } catch (error) {
    return false;
  }
}

function getFormattedTimestamp() {
  const now = new Date();
  const options: Intl.DateTimeFormatOptions = {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: true,
  };

  return new Intl.DateTimeFormat('en-US', options)
    .format(now)
    .replace(/\//g, '_')
    .replace(/, /g, '_')
    .replace(/:/g, '_');
}

function downloadCSV(csvString: string) {
  const filename = `Creatives_Creative_Manager_${getFormattedTimestamp()}.csv`;
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();

  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
