import PlatformAccountService from '../../../../apiServices/PlatformAccountService';
import { PLATFORM_MEDIA_REQUEST_DEFAULT_OPTIONS } from './CreativeManagerConstants';
import { PlatformMedia } from './CreativeManagerTypes';

type Props = {
  workspaceIds: number[];
  adAccountIds: string[];
  platformMediaIds: string[];
  showToast?: (messageId: string, type: 'error' | 'info') => void;
};
export async function getCreativeManagerPlatformMedia(
  props: Props,
): Promise<{ [key: string]: PlatformMedia }> {
  try {
    const params = {
      ...props,
      options: PLATFORM_MEDIA_REQUEST_DEFAULT_OPTIONS,
    };
    const response = await PlatformAccountService.postPlatformMedia(params);
    return response.data.platformMedias;
  } catch (error) {
    props.showToast &&
      props.showToast('error.api.leaderboard.platformMedia.failed', 'error');
    return {};
  }
}
