import React from 'react';
import AnalyticsFiltersProvider from '../../components/AnalyticsFilters/AnalyticsFiltersProvider';
import CreativeManagerProvider from './context/CreativeManagerProvider';
import CreativeManagerContent from './CreativeManagerContent';

const CreativeManager = () => {
  return (
    <AnalyticsFiltersProvider>
      <CreativeManagerProvider>
        <CreativeManagerContent />
      </CreativeManagerProvider>
    </AnalyticsFiltersProvider>
  );
};

export default CreativeManager;
