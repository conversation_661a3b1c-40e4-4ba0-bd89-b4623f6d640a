import { useSelector } from 'react-redux';
import { getOrganizationId } from '../../../../redux/selectors/partner.selectors';
import { GroupByDimensions } from '../../IndividualCreativeView/individualCreativeView.types';
import { useQuery } from '@tanstack/react-query';
import BffService from '../../../../apiServices/BffService';
import { IndividualCreativeViewRequestParams } from '../../../reports/redux/individualCreativeView/individualCreativeView.slice';
import { GridPaginationModel } from '@mui/x-data-grid-pro';
import { SortingModel } from '../../IndividualCreativeView/MultiAssetAdSelectionModal/MultiAssetAdSelectionDataGrid/MultiAssetAdSelectionDataGrid';
import {
  DIMENSIONS_REQUEST_PARAMS,
  DIMENSIONS_ENDPOINT_REQUEST_TYPE,
  AD_SELECTION_MODAL_DATA_GRID_FIELDS,
  AD_SELECTION_MODAL_SORT_BY_AD_NAME,
  AD_SELECTION_MODAL_DEFAULT_SORTING,
} from '../../IndividualCreativeView/individualCreativeView.constants';
import { KPI_FORMATS } from '../../../../types/kpi.types';

type Props = {
  requestParams: IndividualCreativeViewRequestParams;
  paginationModel?: GridPaginationModel;
  sortingModel?: SortingModel;
  requestType: string;
  refetchOnPlatformMediaIdChange?: boolean;
};

/*
Returns one of the following:
1. list of ads (to be used for ad selection modal)
2. list of mediaIds (to be used for selecting media preview in ICV for multi-asset)
3. kpi and impressions data
 */

export const useFetchIndividualCreativeViewDimensionsData = (props: Props) => {
  const {
    requestParams,
    paginationModel,
    sortingModel,
    requestType,
    refetchOnPlatformMediaIdChange,
  } = props;
  const organizationId = useSelector(getOrganizationId);

  const {
    kpi,
    kpiFormat,
    adAccountIds,
    workspaceIds,
    platformMediaId,
    platform,
    startDate,
    endDate,
    adId,
    currency,
  } = requestParams || {};

  const { page, pageSize } = paginationModel || {};
  const { sortOrder, sortBy } =
    sortingModel || AD_SELECTION_MODAL_DEFAULT_SORTING;

  const sortByKey =
    sortingModel?.sortBy === AD_SELECTION_MODAL_DATA_GRID_FIELDS.AD_NAME
      ? AD_SELECTION_MODAL_SORT_BY_AD_NAME // 'name' column is 'ad'
      : sortBy;

  const isRequestForAds = requestType === DIMENSIONS_ENDPOINT_REQUEST_TYPE.ADS;
  const isRequestForMedia =
    requestType === DIMENSIONS_ENDPOINT_REQUEST_TYPE.MEDIA;
  const isRequestForKpiAndImpressions =
    requestType === DIMENSIONS_ENDPOINT_REQUEST_TYPE.KPI_AND_IMPRESSIONS;
  const shouldAddCurrency = kpiFormat === KPI_FORMATS.SPEND && currency?.id;

  const requestBody = {
    requestType, // used for FE debugging
    kpiId: kpi,
    startDate,
    endDate,
    groupByDimension: isRequestForAds
      ? GroupByDimensions.AD
      : GroupByDimensions.ADVIDEO,
    adAccountIds,
    workspaceIds,
    filters:
      isRequestForAds || isRequestForKpiAndImpressions
        ? { platformMediaIds: [platformMediaId as string] }
        : { adIds: [adId] },
    platform,
    ...((isRequestForAds || isRequestForMedia) && {
      dimensions: [DIMENSIONS_REQUEST_PARAMS.CAMPAIGNS],
      offset: page && pageSize ? page * pageSize : 0,
      perPage: pageSize,
      sort: {
        sortBy: sortByKey,
        sortOrder,
      },
    }),
    ...(shouldAddCurrency && { currency: currency?.id }),
  };

  const refetchParams = [
    startDate,
    endDate,
    adId,
    page,
    pageSize,
    sortBy,
    sortOrder,
  ];

  if (refetchOnPlatformMediaIdChange) {
    refetchParams.push(platformMediaId);
  }

  const queryKey = isRequestForAds
    ? ['fetchAdsForICV', ...refetchParams]
    : isRequestForKpiAndImpressions
      ? ['fetchKpiAndImpressionsForICV', ...refetchParams]
      : ['fetchMediaIdsForICV', ...refetchParams];

  const { data, isLoading, isError, isFetching } = useQuery({
    queryKey,
    refetchOnWindowFocus: false,
    queryFn: async () => {
      const endpoint = `/v1/dimensions/organization/${organizationId}`;
      return await (isRequestForAds || isRequestForMedia
        ? BffService.handleBffApiPostWithPagination(endpoint, requestBody)
        : BffService.handleBffApiPost(endpoint, requestBody));
    },
  });

  return {
    kpiAndImpressionsData: isRequestForKpiAndImpressions ? data : null,
    mediaData: isRequestForMedia ? data?.data : null,
    adsData: isRequestForAds ? data?.data : null,
    paginationData:
      isRequestForAds || isRequestForMedia ? data?.pagination : null,
    isLoading: isLoading || isFetching,
    isError,
  };
};
