import { AnalyticsFiltersType } from '../../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import { ACCOUNT_ACCOUNT_TYPE } from '../../../../constants/ci.constants';
import { Currency } from '../../../../types/currency.types';
import { KPI_FORMATS } from '../../../../types/kpi.types';

export const getRequestParamsFromAnalyticsFilters = (
  analyticsFilters: AnalyticsFiltersType,
  adAccountIdForLeaderboard: string,
  currency?: Currency,
) => {
  const { globalFilters } = analyticsFilters || {};

  const { kpi, adAccounts, workspaces, channel, startDate, endDate } =
    globalFilters || {};

  const shouldAddCurrency =
    currency?.id && kpi?.value?.format === KPI_FORMATS.SPEND;

  return {
    kpi: kpi?.value?.id || '',
    kpiName: kpi?.value?.name || '',
    kpiFormat: kpi?.value?.format || '',
    adAccountIds: adAccountIdForLeaderboard
      ? [adAccountIdForLeaderboard]
      : adAccounts?.value?.map((account) => account.platformAccountId),
    workspaceIds: workspaces?.value?.map((workspace) => workspace.id),
    platform: channel?.value,
    startDate,
    endDate,
    platformMediaId: null,
    accountIds: null,
    accountType: ACCOUNT_ACCOUNT_TYPE.toLowerCase(),
    mediaId: null,
    ...(shouldAddCurrency && { currency }),
  };
};
