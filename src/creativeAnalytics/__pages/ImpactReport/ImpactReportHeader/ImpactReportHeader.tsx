import React, { MutableRefObject } from 'react';
import { useIntl } from 'react-intl';
import { useDispatch } from 'react-redux';
import { AnalyticsFiltersHeader } from '../../../components/AnalyticsFilters/AnalyticsFiltersHeader/AnalyticsFiltersHeader';
import ImpactReportBreakdownByDropdown from './ImpactReportBreakdownBy';
import {
  exportReportAsCsvClientSide,
  exportReportAsExcel,
  formatDate,
} from '../../SavedReports/SavedReportsUtils/exportReportUtils';
import {
  DUPLICATE_REPORT_TOAST_ERROR_MESSAGE_INTL,
  DUPLICATE_REPORT_TOAST_SUCCESS_MESSAGE_INTL,
  REPORT_DOWNLOADED_TOAST_TEXT,
  SAVED_REPORTS_BREADCRUMB_TEXT_INTL,
} from '../../SavedReports/SavedReportsConstants';
import { INSIGHT_TOOLTIP_TEXT_INTL } from '../../../components/AnalyticsFilters/AnalyticsFiltersConstants';
import {
  BREAKDOWNS_TO_CI_VIEW,
  BREAKDOWNS_TO_INTL_KEY,
  IMPACT_REPORT_TYPE_KEYS_TO_TYPES,
  IMPACT_REPORT_TYPES,
  MISSING_FILTERS_FIELDS_TEXT_INTL,
  PREVIOUS_PAGE_URL_PARAM,
  REPORT_VIEW_BY_OPTIONS,
  STAT_SETTING_DIRECTIONS_HORIZONTAL,
  STAT_SETTING_DIRECTIONS_VERTICAL,
  USER_DID_NOT_CREATE_REPORT_INTL_KEY,
} from '../ImpactReportConstants';
import analyticsConfigurationSlice from '../../../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';
import { GridApiPro } from '@mui/x-data-grid-pro/models/gridApiPro';
import {
  ImpactReportAveragePerformanceData,
  ImpactReportType,
  ReportBreakdown,
} from '../ImpactReportTypes';
import getJoinedString from '../../../../utils/getJoinedString';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { useSelector } from 'react-redux';
import { getReportTableView } from '../../../../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';
import {
  PLATFORMS_DO_NOT_SUPPORT_OBJECTIVES,
  REPORT_TABLE_VIEW,
} from '../../../../constants/ci.constants';
import { useToastAlert } from '../../SavedReports/SavedReportsCustomHooks';
import { DropdownOption } from '../../../../muiCustomComponents/Dropdowns/types';
import {
  AnalyticsFiltersType,
  MenuActions,
} from '../../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import {
  AnalyticsBffReportResponse,
  SavedReportMetadata,
} from '../../SavedReports/SavedReportsTypes';
import { duplicateReport } from '../../SavedReports/SavedReportsUtils/duplicateReport';
import { openImpactReport } from '../../SavedReports/SavedReportsUtils/openReport';
import { getSaveOrUpdateButtonLabel } from '../../SavedReports/SavedReportsUtils/getSaveOrUpdateButtonLabel';
import { copyReportUrl } from '../../SavedReports/SavedReportsUtils/copyReportUrl';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import NormsConfiguration from '../../../../components/NormsConfiguration/NormsConfiguration';
import {
  NormsConfigurationContextType,
  NormsConfigurationSelectionStateType,
} from '../../../../types/normsConfiguration.types';
import {
  NORMS_CONFIGURATION_HELP_CENTER_LINK_ANALYTICS,
  NORMS_CONFIGURATION_SUBTITLE_KEY_ANALYTICS,
  NORMS_CONFIGURATION_SWITCH_LABEL_KEY_ANALYTICS,
} from '../../../../components/NormsConfiguration/NormsConfigurationConstants';
import { ImpactReportNormativePerformance } from './ImpactReportNormativePerformance/ImpactReportNormativePerformance';
import { NormativePerformanceData } from '../../../services/NormativePerformance/normativePerformance.types';
import { GLOBALS } from '../../../../constants';
import { NormsConfigurationButton } from './NormsConfigurationButton';
import siteMap from '../../../../routing/siteMap';
import { generatePath } from 'react-router';
import { REPORT_PDF_DATA_LOCAL_STORAGE_KEY } from '../../../reports/components/AnalyticsReportComponents/AnalyticsReportPreview/AnalyticsReportPreview';
import { StatSettings } from '../../../reports/components/AnalyticsReportComponents/AnalyticsReportStatSettings/AnalyticsReportStatSettingsTypes';
import { AnalyticsReportStatSettingsButton } from '../../../reports/components/AnalyticsReportComponents/AnalyticsReportStatSettings/AnalyticsReportStatSettingsButton';
import { CurrencyDropdown } from '../../../reports/components/AnalyticsReportComponents/CurrencyDropdown/CurrencyDropdown';
import { Currency } from '../../../../types/currency.types';
import { ViewByDropdown } from '../../../reports/components/AnalyticsReportComponents/ViewByDropdown/ViewByDropdown';
import { ShowRowSelectionButton } from '../../../reports/components/AnalyticsReportComponents/ShowRowSelectionButton/ShowRowSelectionButton';
import { AnalyticsHeaderDivider } from '../../../reports/components/AnalyticsReportComponents/AnalyticsHeaderDivider/AnalyticsHeaderDivider';
import { CreateElementSetButton } from '../../../reports/components/AnalyticsReportComponents/CreateElementSetButton/CreateElementSetButton';
import { useAnalyticsFilters } from '../../../components/AnalyticsFilters/hooks/useAnalyticsFilters';
import { useSafeInsightModal } from '../../../insights/components/InsightsV2/components/InsightModal/hooks/useSafeInsightModal';

const { PENDING } = GLOBALS.REDUX_LOADING_STATUS;

const isAnalyticsReportsPDFExportEnabled = getFeatureFlag(
  'isAnalyticsReportsPDFExportEnabled',
);

interface ImpactReportHeaderProps {
  apiRef: MutableRefObject<GridApiPro>;
  reportType: ImpactReportType;
  titleText: string;
  descriptionText: string;
  onTitleChange: (titleText: string) => void;
  onSubtitleChange: (descriptionText: string) => void;
  searchParams: URLSearchParams;
  hasUnsavedUserChanges: boolean;
  reportId: string | null;
  organizationId: string;
  savedReport?: AnalyticsBffReportResponse | null;
  handleClickSave: () => void;
  availableBreakdowns: ReportBreakdown[];
  selectedBreakdown: ReportBreakdown;
  onBreakdownChange: (breakdown: ReportBreakdown) => void;
  areFiltersValid: boolean;
  doesUserHavePermissionToSaveReport: boolean;
  missingFields: string[];
  isLoadingSavedReport?: boolean | null;
  visibleRowsInSelectedRowView: GridRowSelectionModel;
  isSelectedRowsView: boolean;
  setIsSelectedRowsView: (isSelectedRowView: boolean) => void;
  openCustomGroupModal: () => void;
  setHasUnsavedUserChanges: (hasUnsavedUserChanges: boolean) => void;
  setSettingsChanged: (settingsChanged: number) => void;
  reportStatus?: string;
  impactReportFilter?: AnalyticsFiltersType;
  isMissingAssetLevelDataForKpi?: boolean;
  reportMetadata?: SavedReportMetadata | null;
  statSettings: StatSettings;
  setStatSettings: (statSettings: StatSettings) => void;
  toggleStatSettingsModal: () => void;
  normsConfiguration: NormsConfigurationSelectionStateType;
  setNormsConfiguration: (
    normsConfiguration: NormsConfigurationSelectionStateType,
  ) => void;
  averagePerformanceData: ImpactReportAveragePerformanceData | null;
  normativePerformanceData: NormativePerformanceData | null;
  isNormativePerformanceDataLoading: boolean;
  currency: Currency;
  updateCurrency: (currency: Currency) => void;
}

export const ImpactReportHeader = ({
  apiRef,
  reportType,
  titleText,
  descriptionText,
  onTitleChange,
  onSubtitleChange,
  searchParams,
  hasUnsavedUserChanges,
  reportId,
  organizationId,
  savedReport,
  handleClickSave,
  availableBreakdowns,
  selectedBreakdown,
  onBreakdownChange,
  areFiltersValid,
  doesUserHavePermissionToSaveReport,
  missingFields,
  isLoadingSavedReport,
  visibleRowsInSelectedRowView,
  isSelectedRowsView,
  setIsSelectedRowsView,
  openCustomGroupModal,
  setHasUnsavedUserChanges,
  setSettingsChanged,
  reportStatus,
  impactReportFilter,
  isMissingAssetLevelDataForKpi,
  reportMetadata,
  statSettings,
  setStatSettings,
  toggleStatSettingsModal,
  normsConfiguration,
  setNormsConfiguration,
  averagePerformanceData,
  normativePerformanceData,
  isNormativePerformanceDataLoading,
  currency,
  updateCurrency,
}: ImpactReportHeaderProps) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const reportTableView = useSelector(getReportTableView);
  const { setViewBy } = useAnalyticsFilters();
  const showToastAlert = useToastAlert();
  const insightModalCtx = useSafeInsightModal();

  const isLoading = Boolean(reportStatus === PENDING || isLoadingSavedReport);

  const editableHeaderObject = {
    titleText,
    descriptionText,
    onTitleChange: onTitleChange,
    onDescriptionChange: onSubtitleChange,
    prevPage: {
      label: SAVED_REPORTS_BREADCRUMB_TEXT_INTL,
      path: searchParams.get(PREVIOUS_PAGE_URL_PARAM) || '',
    },
    isEditable: true,
  };

  const breakdownOptions = availableBreakdowns.map((breakdown) => {
    const breakdownIntlKey = BREAKDOWNS_TO_INTL_KEY[breakdown];
    return {
      id: breakdown,
      name: intl.messages[breakdownIntlKey] as string,
    };
  });

  const duplicateImpactReport = async () => {
    if (!reportId || !savedReport) {
      return;
    }

    try {
      const reportCopy = await duplicateReport(organizationId, savedReport.id);

      const impactReportType = IMPACT_REPORT_TYPE_KEYS_TO_TYPES[reportType];
      if (reportCopy?.id && impactReportType) {
        openImpactReport(reportCopy.id, impactReportType);
      }
      showToastAlert(DUPLICATE_REPORT_TOAST_SUCCESS_MESSAGE_INTL, 'info');
    } catch {
      showToastAlert(DUPLICATE_REPORT_TOAST_ERROR_MESSAGE_INTL, 'error');
    }
  };

  const onDownloadCSV = () => {
    exportReportAsCsvClientSide({
      apiRef,
      titleText,
      reportTypeCopy: reportMetadata?.reportTypeCopy,
      startDate: impactReportFilter?.globalFilters?.startDate,
      endDate: impactReportFilter?.globalFilters?.endDate,
      useDataGridExportCsv: true,
      currencyCode: currency?.id,
    });
    showToastAlert(REPORT_DOWNLOADED_TOAST_TEXT, 'info');
  };

  const onDownloadExcel = () => {
    exportReportAsExcel({
      apiRef,
      titleText,
      reportTypeCopy: reportMetadata?.reportTypeCopy,
      startDate: impactReportFilter?.globalFilters?.startDate,
      endDate: impactReportFilter?.globalFilters?.endDate,
      currencyCode: currency?.id,
    });
    showToastAlert(REPORT_DOWNLOADED_TOAST_TEXT, 'info');
  };

  const onDownloadPDF = () => {
    const path = siteMap.creativeIntelligenceImpactReportPDF;
    const pathToPdfPreview = generatePath(path, { reportType });
    window.open(pathToPdfPreview, '_blank');
    const reportDate = formatDate(savedReport?.lastUpdated || new Date());
    localStorage.setItem(
      REPORT_PDF_DATA_LOCAL_STORAGE_KEY,
      JSON.stringify({
        reportTypeCopy: reportMetadata?.reportTypeCopy,
        reportName: titleText,
        reportDate,
      }),
    );
  };

  const menuActions: MenuActions = {
    onDuplicate: duplicateImpactReport,
    onDownloadCSV,
    onDownloadExcel,
    ...(isAnalyticsReportsPDFExportEnabled && { onDownloadPDF }),
  };

  const onCopyURL = () => copyReportUrl(location.href, showToastAlert);

  const onToggleSelectedRowView = () => {
    setIsSelectedRowsView(!isSelectedRowsView);
  };

  const getDisabledSaveButtonTooltipText = () => {
    if (!doesUserHavePermissionToSaveReport) {
      return intl.messages[USER_DID_NOT_CREATE_REPORT_INTL_KEY] as string;
    }

    if (missingFields?.length) {
      const joinedMissingFields = getJoinedString(missingFields);
      return intl.formatMessage(
        { id: MISSING_FILTERS_FIELDS_TEXT_INTL },
        { missingFields: joinedMissingFields },
      ) as string;
    }

    return null;
  };

  const handleReportTableViewChangeWithValidation = (
    selectedOption: DropdownOption | null,
  ) => {
    if (insightModalCtx?.isInsightModalOpen) {
      insightModalCtx.seChangeViewConfirmationModal({
        open: true,
        changeView: () => {
          insightModalCtx.seChangeViewConfirmationModal(null);
          insightModalCtx.closeInsightModal({ closeAfterCreation: true });
          handleReportTableViewChange(selectedOption);
        },
        keepEditing: () => {
          insightModalCtx.seChangeViewConfirmationModal(null);
        },
      });

      return;
    }
    handleReportTableViewChange(selectedOption);
  };

  const handleReportTableViewChange = (
    selectedOption: DropdownOption | null,
  ) => {
    const option = REPORT_VIEW_BY_OPTIONS.find(
      (view) => view.id === selectedOption?.id,
    );

    setViewBy(option?.value);

    if (!option) {
      return;
    }

    setHasUnsavedUserChanges(true);

    dispatch(
      analyticsConfigurationSlice.actions.setReportTableView({
        reportTableView: option,
      }),
    );

    if (option.value === REPORT_TABLE_VIEW.KPI.value) {
      setStatSettings({
        ...statSettings,
        direction: STAT_SETTING_DIRECTIONS_HORIZONTAL,
      });
    } else {
      setStatSettings({
        ...statSettings,
        direction: STAT_SETTING_DIRECTIONS_VERTICAL,
      });
    }

    setSettingsChanged(new Date().getTime());
  };

  const isSaveButtonDisabled =
    !titleText ||
    !doesUserHavePermissionToSaveReport ||
    !hasUnsavedUserChanges ||
    !areFiltersValid;

  const onSaveObject = {
    disabled: isSaveButtonDisabled,
    label: getSaveOrUpdateButtonLabel(Boolean(reportId)),
    action: handleClickSave,
    tooltip: getDisabledSaveButtonTooltipText(),
  };

  const shouldDisplayViewByDropdown =
    reportType === IMPACT_REPORT_TYPES.ELEMENT_PRESENCE;

  const shouldDisplayNormsButton =
    reportType === IMPACT_REPORT_TYPES.ELEMENT_IMPACT;

  const shouldDisplayCurrencyDropdown =
    reportType === IMPACT_REPORT_TYPES.MEDIA_IMPACT ||
    (reportType === IMPACT_REPORT_TYPES.ELEMENT_PRESENCE &&
      reportTableView?.value === REPORT_TABLE_VIEW.KPI.value);

  const shouldDisplayShowRowSelectionButton = Boolean(
    visibleRowsInSelectedRowView.length,
  );

  const shouldDisplayCreateElementSetButton =
    (reportType === IMPACT_REPORT_TYPES.ELEMENT_IMPACT ||
      (reportType === IMPACT_REPORT_TYPES.ELEMENT_PRESENCE &&
        reportTableView?.value === REPORT_TABLE_VIEW.ELEMENT.value)) &&
    isSelectedRowsView;

  const shouldHidePerformanceNormsObjectiveSection = Boolean(
    impactReportFilter?.globalFilters?.channel?.value &&
      PLATFORMS_DO_NOT_SUPPORT_OBJECTIVES.includes(
        impactReportFilter.globalFilters.channel.value,
      ),
  );

  const actionBarRightSlots = [
    shouldDisplayViewByDropdown ? (
      <ViewByDropdown
        key="ViewByDropdown"
        reportTableView={reportTableView}
        handleReportTableViewChange={handleReportTableViewChangeWithValidation}
      />
    ) : (
      <ImpactReportBreakdownByDropdown
        key="BreakdownByDropdown"
        value={selectedBreakdown || availableBreakdowns[0]}
        options={breakdownOptions}
        onChange={onBreakdownChange}
      />
    ),
    ...(shouldDisplayNormsButton
      ? [
          <NormsConfiguration
            key="NormsConfiguration"
            shouldUsePerformanceNormsScopes
            normsConfiguration={normsConfiguration}
            onNormsConfigurationChange={setNormsConfiguration}
            setDoesReportHaveUnsavedChanges={setHasUnsavedUserChanges}
            isDisabled={!impactReportFilter?.globalFilters?.kpi?.value}
            shouldHideObjectiveSection={
              shouldHidePerformanceNormsObjectiveSection
            }
            TriggerButton={NormsConfigurationButton}
            subtitleIntlKey={NORMS_CONFIGURATION_SUBTITLE_KEY_ANALYTICS}
            switchLabelIntlKey={NORMS_CONFIGURATION_SWITCH_LABEL_KEY_ANALYTICS}
            helpCenterLink={NORMS_CONFIGURATION_HELP_CENTER_LINK_ANALYTICS}
            context={NormsConfigurationContextType.ANALYTICS}
          />,
        ]
      : []),
    <AnalyticsReportStatSettingsButton
      key="SettingsButton"
      onClick={toggleStatSettingsModal}
    />,
    ...(shouldDisplayCurrencyDropdown
      ? [
          <AnalyticsHeaderDivider key="Divider" />,
          <CurrencyDropdown
            key="CurrencyDropdown"
            selectedCurrency={currency}
            setSelectedCurrency={updateCurrency}
            isDropdownDisabled={isLoading}
          />,
        ]
      : []),
    ...(shouldDisplayShowRowSelectionButton
      ? [
          <ShowRowSelectionButton
            key="ShowRowSelectionButton"
            onClick={onToggleSelectedRowView}
            isSelectedRowsView={isSelectedRowsView}
          />,
        ]
      : []),
    ...(shouldDisplayCreateElementSetButton
      ? [
          <CreateElementSetButton
            key="CreateElementSetButton"
            onClick={openCustomGroupModal}
          />,
        ]
      : []),
  ];

  const selectedKpi = impactReportFilter?.globalFilters?.kpi?.value;
  const isNormativePerformanceSectionLoading =
    isLoading || isNormativePerformanceDataLoading;

  return (
    <>
      <AnalyticsFiltersHeader
        editableHeaderObject={editableHeaderObject}
        actionBarRightSlots={actionBarRightSlots}
        onSaveObject={onSaveObject}
        onCopyURL={onCopyURL}
        menuActions={menuActions}
        areDownloadsDisabled={isLoadingSavedReport || !areFiltersValid}
        hasInsights
        areInsightsDisabled={isLoadingSavedReport || !reportId}
        disabledInsightButtonTooltipTextKey={
          INSIGHT_TOOLTIP_TEXT_INTL as string
        }
        reportStatus={reportStatus}
        currentCIView={BREAKDOWNS_TO_CI_VIEW[selectedBreakdown]}
        impactReportFilter={impactReportFilter}
        isMissingAssetLevelDataForKpi={isMissingAssetLevelDataForKpi}
        reportMetadata={reportMetadata}
        isSavedReport={Boolean(reportId)}
        isLoading={Boolean(isLoadingSavedReport)}
      />
      {normsConfiguration.isEnabled &&
        reportType === IMPACT_REPORT_TYPES.ELEMENT_IMPACT && (
          <ImpactReportNormativePerformance
            averagePerformanceData={averagePerformanceData}
            normativePerformanceData={normativePerformanceData}
            selectedKpi={selectedKpi}
            currency={currency}
            normsConfiguration={normsConfiguration}
            isLoading={isNormativePerformanceSectionLoading}
          />
        )}
    </>
  );
};
