import {
  AdvancedFilterType,
  AdvancedSectionChildrenType,
  AnalyticsFiltersType,
  ListItem,
  MultiValue,
} from '../../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import {
  CriteriaPerformanceReportGroupBy,
  CriteriaPerformanceReportRequest,
  CriteriaPerformanceRequestFilters,
} from '../CriteriaPerformanceReportTypes';
import { ADVANCED_FILTERS_NAME_TO_BACKEND_NAME_V1 } from '../../../components/AnalyticsFilters/AnalyticsFiltersConstants';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { CRITERIA_PERFORMANCE_DEFAULT_MEDIA_TYPE_FILTER_VALUES } from '../CriteriaPerformanceReportConstants';
import { getArrayFromValue } from '../../../components/AnalyticsFilters/utils/getters';
import { formatAdvancedFilterValuesForRequestV1 } from '../../../components/AnalyticsFilters/utils/formatters';

const hasSelectedCriteria = (advancedFilters: AdvancedFilterType[]) => {
  const selectedCriteria = advancedFilters?.find(
    (filter) => filter.type === AdvancedSectionChildrenType.CRITERIA,
  )?.value as MultiValue;

  return Boolean(selectedCriteria?.length);
};

const getAreFiltersValid = ({
  platform,
  workspaceIds,
  adAccountIds,
  startDate,
  endDate,
  criteriaGroupedRules,
  creativeMediaType,
  statisticalConfidence,
}: {
  platform: string;
  workspaceIds: number[];
  adAccountIds: string[];
  startDate: string;
  endDate: string;
  criteriaGroupedRules: {
    id: string | undefined;
    criteriaIds: number[] | undefined;
  }[];
  creativeMediaType: string[];
  statisticalConfidence: number;
}): boolean =>
  Boolean(
    platform &&
      workspaceIds?.length &&
      adAccountIds?.length &&
      startDate &&
      endDate &&
      criteriaGroupedRules?.length &&
      creativeMediaType?.length &&
      statisticalConfidence,
  );

interface FormatFiltersForReportRequestParams {
  criteriaPerformanceFilters: AnalyticsFiltersType;
  kpiIds: string[];
  statConfidence: string | number;
  currencyId?: string;
  groupBy: CriteriaPerformanceReportGroupBy;
}

export const formatFiltersForReportRequest = ({
  criteriaPerformanceFilters,
  kpiIds,
  statConfidence,
  currencyId,
  groupBy,
}: FormatFiltersForReportRequestParams): CriteriaPerformanceReportRequest | null => {
  const { globalFilters, advancedFilters } = criteriaPerformanceFilters;
  const { channel, workspaces, adAccounts, startDate, endDate, kpi } =
    globalFilters;

  if (hasSelectedCriteria(advancedFilters) === false) {
    return null;
  }
  const isCriteriaPerformanceIncludeStandardCriteria = getFeatureFlag(
    'isCriteriaPerformanceIncludeStandardCriteria',
  );
  const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
    'isCriteriaGroupsInReportsEnabled',
  );

  const platform = channel?.value;
  const workspaceIds = workspaces?.value.map((workspace) => workspace.id);
  const adAccountIds = adAccounts.value.map(
    (adAccount) => adAccount.platformAccountId,
  );
  const selectedKpi = kpi?.value;
  const selectedMediaTypes = advancedFilters.find(
    (filter) => filter.type === AdvancedSectionChildrenType.CREATIVE_MEDIA_TYPE,
  )?.value as MultiValue;
  const mediaTypes = selectedMediaTypes?.length
    ? selectedMediaTypes
    : CRITERIA_PERFORMANCE_DEFAULT_MEDIA_TYPE_FILTER_VALUES;
  const criteriaRules = advancedFilters?.find(
    (filter) => filter.type === AdvancedSectionChildrenType.CRITERIA,
  )?.value as MultiValue;

  const criteriaGroupedRules = criteriaRules?.reduce(
    (
      acc: { id: string | undefined; criteriaIds: number[] | undefined }[],
      criteriaRule: ListItem,
    ) => {
      acc.push({
        id: criteriaRule.id,
        criteriaIds: criteriaRule.criteriaIds,
      });
      return acc;
    },
    [],
  );

  if (!criteriaGroupedRules?.length) {
    return null;
  }

  const criteriaGroups = advancedFilters.find(
    (filter) => filter.type === AdvancedSectionChildrenType.CRITERIA_GROUPS,
  )?.value as { id: string }[];
  const criteriaGroupIds = criteriaGroups?.map((group) => group.id);

  const creativeMediaType = getArrayFromValue(mediaTypes, 'id');
  const statisticalConfidence = Number(statConfidence);

  const areFiltersValid = getAreFiltersValid({
    platform,
    workspaceIds,
    adAccountIds,
    startDate,
    endDate,
    criteriaGroupedRules,
    creativeMediaType,
    statisticalConfidence,
  });

  const formattedAdvancedFilters = formatAdvancedFilterValuesForRequestV1(
    advancedFilters,
    selectedKpi,
    true,
  );

  // avoid passing criteria and creativeMediaType as advanced filters (passed under CPR-specific 'filters' instead)
  delete formattedAdvancedFilters[
    ADVANCED_FILTERS_NAME_TO_BACKEND_NAME_V1.criteria
  ];
  delete formattedAdvancedFilters[
    ADVANCED_FILTERS_NAME_TO_BACKEND_NAME_V1.creativeMediaType
  ];

  if (areFiltersValid) {
    const paramsObject = {
      platform,
      workspaceIds,
      adAccountIds,
      startDate,
      endDate,
      kpiIds,
      currency: currencyId,
      groupBy: groupBy || CriteriaPerformanceReportGroupBy.UNGROUPED,
      statisticalConfidence,
      filters: {
        criteriaGroupedRules,
        creativeMediaType,
      } as CriteriaPerformanceRequestFilters,
      advancedFilters: formattedAdvancedFilters,
      shouldIncludeStandardCriteria:
        isCriteriaPerformanceIncludeStandardCriteria ? true : false,
    };

    if (isCriteriaGroupsInReportsEnabled && criteriaGroupIds?.length) {
      // Sending an empty array acts as All Selected, so ideally we should not send if empty
      paramsObject.filters.criteriaGroupIds = criteriaGroupIds;
    }

    return paramsObject;
  }

  return null;
};
