import React, { MutableRefObject } from 'react';
import { useIntl } from 'react-intl';
import { AnalyticsFiltersHeader } from '../../../components/AnalyticsFilters/AnalyticsFiltersHeader/AnalyticsFiltersHeader';
import ColumnDropdown from '../../../../muiCustomComponents/ColumnDropdown';
import {
  CriteriaPerformanceReportColumn,
  CriteriaPerformanceReportGroupBy,
} from '../CriteriaPerformanceReportTypes';
import { ColumnsDropdownType } from '../../../../muiCustomComponents/ColumnDropdown/types';
import { SAVED_REPORTS_BREADCRUMB_TEXT_INTL } from '../../SavedReports/SavedReportsConstants';
import {
  MISSING_FILTERS_FIELDS_TEXT_INTL,
  PREVIOUS_PAGE_URL_PARAM,
  USER_DID_NOT_CREATE_REPORT_INTL_KEY,
} from '../../ImpactReport/ImpactReportConstants';
import getJoinedString from '../../../../utils/getJoinedString';
import { SavedReportMetadata } from '../../SavedReports/SavedReportsTypes';
import { getSaveOrUpdateButtonLabel } from '../../SavedReports/SavedReportsUtils/getSaveOrUpdateButtonLabel';
import {
  getColumnsToDisplay,
  getDropdownColumns,
} from '../CriteriaPerformanceReportUtils/columnDropdownUtils';
import { AnalyticsReportStatSettingsButton } from '../../../reports/components/AnalyticsReportComponents/AnalyticsReportStatSettings/AnalyticsReportStatSettingsButton';
import { CurrencyDropdown } from '../../../reports/components/AnalyticsReportComponents/CurrencyDropdown/CurrencyDropdown';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { Currency } from '../../../../types/currency.types';
import { AnalyticsHeaderDivider } from '../../../reports/components/AnalyticsReportComponents/AnalyticsHeaderDivider/AnalyticsHeaderDivider';
import { copyReportUrl } from '../../SavedReports/SavedReportsUtils/copyReportUrl';
import { useToastAlert } from '../../SavedReports/SavedReportsCustomHooks';
import { GridApiPro } from '@mui/x-data-grid-pro';
import { CRITERIA_PERFORMANCE_REPORT_KEY } from '../CriteriaPerformanceReportConstants';
import {
  exportReportAsCsvClientSide,
  exportReportAsExcel,
  ExportReportProps,
} from '../../SavedReports/SavedReportsUtils/exportReportUtils';
import {
  AnalyticsFiltersType,
  MenuActions,
} from '../../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import { GroupByCriteriaGroupsDropdown } from './GroupByCriteriaGroupsDropdown';

const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

const groupByCriteriaGroupsDropdownButtonSx = {
  ml: 'auto',
  p: '6px',
};

interface CriteriaPerformanceReportHeaderProps {
  apiRef: MutableRefObject<GridApiPro>;
  columns: CriteriaPerformanceReportColumn[];
  setColumns: (columns: CriteriaPerformanceReportColumn[]) => void;
  isDataLoading: boolean;
  isReportLoading: boolean;
  titleText: string;
  descriptionText: string;
  filters: AnalyticsFiltersType;
  onTitleChange: (titleText: string) => void;
  onSubtitleChange: (descriptionText: string) => void;
  searchParams: URLSearchParams;
  hasUnsavedUserChanges: boolean;
  reportId: string | null;
  handleClickSave: () => void;
  doesUserHavePermissionToSaveReport: boolean;
  missingFilters: string[];
  reportMetadata?: SavedReportMetadata | null;
  toggleStatSettingsModal: () => void;
  currency: Currency;
  updateCurrency: (currency: Currency) => void;
  groupBy: CriteriaPerformanceReportGroupBy;
  setGroupBy: (groupBy: CriteriaPerformanceReportGroupBy) => void;
}

export const CriteriaPerformanceReportHeader = ({
  apiRef,
  columns,
  setColumns,
  isDataLoading,
  isReportLoading,
  titleText,
  descriptionText,
  filters,
  onTitleChange,
  onSubtitleChange,
  searchParams,
  hasUnsavedUserChanges,
  reportId,
  handleClickSave,
  doesUserHavePermissionToSaveReport,
  missingFilters,
  reportMetadata,
  toggleStatSettingsModal,
  currency,
  updateCurrency,
  groupBy,
  setGroupBy,
}: CriteriaPerformanceReportHeaderProps) => {
  const intl = useIntl();
  const showToastAlert = useToastAlert();

  const isSaveButtonDisabled =
    !titleText ||
    !doesUserHavePermissionToSaveReport ||
    !hasUnsavedUserChanges ||
    isDataLoading ||
    isReportLoading ||
    missingFilters?.length > 0;

  const getDisabledSaveButtonTooltipText = () => {
    if (!doesUserHavePermissionToSaveReport) {
      return intl.messages[USER_DID_NOT_CREATE_REPORT_INTL_KEY] as string;
    }

    if (missingFilters?.length) {
      const joinedMissingFields = getJoinedString(missingFilters);
      return intl.formatMessage(
        { id: MISSING_FILTERS_FIELDS_TEXT_INTL },
        { missingFields: joinedMissingFields },
      ) as string;
    }

    return null;
  };

  const onSaveObject = {
    disabled: isSaveButtonDisabled,
    label: getSaveOrUpdateButtonLabel(Boolean(reportId)),
    action: handleClickSave,
    tooltip: getDisabledSaveButtonTooltipText(),
  };

  const onCopyURL = () => copyReportUrl(location.href, showToastAlert);

  const editableHeaderObject = {
    titleText,
    descriptionText,
    onTitleChange: onTitleChange,
    onDescriptionChange: onSubtitleChange,
    prevPage: {
      label: SAVED_REPORTS_BREADCRUMB_TEXT_INTL,
      path: searchParams.get(PREVIOUS_PAGE_URL_PARAM) || '',
    },
    isEditable: true,
  };

  const exportReportProps: ExportReportProps = {
    apiRef,
    titleText,
    reportTypeCopy: intl.messages[CRITERIA_PERFORMANCE_REPORT_KEY] as string,
    startDate: filters?.globalFilters?.startDate,
    endDate: filters?.globalFilters?.endDate,
    useDataGridExportCsv: true,
    currencyCode: currency?.id,
  };

  const onDownloadCSV = () => exportReportAsCsvClientSide(exportReportProps);
  const onDownloadExcel = () => exportReportAsExcel(exportReportProps);

  const menuActions: MenuActions = {
    onDownloadCSV,
    onDownloadExcel,
  };

  const columnsForDropdown = getDropdownColumns(columns);
  const setColumnsFromDropdown = (
    columnsFromDropdown: CriteriaPerformanceReportColumn[],
  ) => {
    const columnsToDisplay = getColumnsToDisplay(columns, columnsFromDropdown);
    setColumns(columnsToDisplay);
  };

  const actionBarRightSlots = [
    <AnalyticsReportStatSettingsButton
      key="SettingsButton"
      onClick={toggleStatSettingsModal}
    />,
    <ColumnDropdown
      key="ColumnDropdown"
      selectedColumns={
        columnsForDropdown.filter(
          (column) => !column.hide,
        ) as ColumnsDropdownType[]
      }
      columns={columnsForDropdown as ColumnsDropdownType[]}
      updateColumns={
        setColumnsFromDropdown as (columns: ColumnsDropdownType[]) => void
      }
      isDropdownDisabled={isDataLoading}
    />,
    <AnalyticsHeaderDivider key="Divider" />,
    <CurrencyDropdown
      key="CurrencyDropdown"
      selectedCurrency={currency}
      setSelectedCurrency={updateCurrency}
      isDropdownDisabled={isDataLoading || isReportLoading}
    />,
    ...(isCriteriaGroupsInReportsEnabled
      ? [
          <GroupByCriteriaGroupsDropdown
            key="GroupByCriteriaGroupsDropdown"
            groupBy={groupBy}
            setGroupBy={setGroupBy}
            isDisabled={isDataLoading || isReportLoading}
            sx={groupByCriteriaGroupsDropdownButtonSx}
          />,
        ]
      : []),
  ];

  return (
    <AnalyticsFiltersHeader
      editableHeaderObject={editableHeaderObject}
      actionBarRightSlots={actionBarRightSlots}
      onSaveObject={onSaveObject}
      reportMetadata={reportMetadata}
      isLoading={isReportLoading}
      onCopyURL={onCopyURL}
      isSavedReport={Boolean(reportId)}
      menuActions={menuActions}
    />
  );
};
