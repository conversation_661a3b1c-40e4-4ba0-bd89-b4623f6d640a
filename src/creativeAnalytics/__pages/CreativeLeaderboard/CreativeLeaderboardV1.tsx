import React, { useEffect, useState, JSX } from 'react';
import { useIntl } from 'react-intl';
import { Box } from '@mui/material';
import CreativeLeaderboardContent from './CreativeLeaderboardContent';
import { GridSortModel, useGridApiRef } from '@mui/x-data-grid-pro';
import {
  CreativeLeaderboardColumn,
  CreativeLeaderboardDataItem,
  CreativeLeaderboardFilterType,
} from './CreativeLeaderboardTypes';
import { formatLeaderboardForSavingV1 } from './utils/formatters';
import { useQuery } from '@tanstack/react-query';
import { platformMediaListQuery } from './CreativeLeaderboardApiQueries';
import CreativeLeaderboardService from '../../reports/services/CreativeLeaderboardService';
import {
  AdvancedFilterType,
  GlobalFiltersType,
  MetricSectionChildrenType,
  ScopeSectionChildrenType,
} from '../../components/AnalyticsFilters/AnalyticsFiltersTypes';
import { CreativeLeaderboardSettingsModal } from './CreativeLeaderboardSettingsModal';
import ColumnDropdown from '../../../muiCustomComponents/ColumnDropdown';
import { useSelector } from 'react-redux';
import { useToastAlert } from '../SavedReports/SavedReportsCustomHooks';
import {
  getCurrentPartnerFeature,
  getOrganizationId,
} from '../../../redux/selectors/partner.selectors';
import AnalyticsFiltersProvider from '../../components/AnalyticsFilters/AnalyticsFiltersProvider';
import { GLOBALS } from '../../../constants';
import { AnalyticsReportNoAccess } from '../../reports/components/AnalyticsReportComponents/AnalyticsReportBlankStates';
import { CreativeLeaderboardHeader } from './CreativeLeaderboardHeader';
import {
  ANALYTICS_REPORTS_HELP_CENTER_LINKS,
  REPORT_CREATE_OPTIONS,
  SAVE_REPORT_ERROR_TOAST_TEXT_INTL,
  SAVE_REPORT_SUCCESS_TOAST_TEXT_INTL,
} from '../SavedReports/SavedReportsConstants';
import AnalyticsSavedReportsService from '../../reports/services/AnalyticsSavedReportsService';
import { useHistory, useParams } from 'react-router-dom';
import vmErrorLog from '../../../utils/vmErrorLog';
import { ReportInfo } from '../ImpactReport/ImpactReportTypes';
import { getAreFiltersValid } from './utils/getters';
import { getCurrentUserId } from '../../../redux/selectors/user.selectors';
import useCreativeLeaderboardFilters from './CreativeLeaderboardHooks/useCreativeLeaderboardFilters';
import { getDoesUserHavePermissionToSave } from '../ImpactReport/ImpactReportUtils/getDoesUserHavePermissionToSave';
import { AnalyticsReportNoFiltersBlankState } from '../../reports/components/AnalyticsReportComponents/AnalyticsReportBlankStates';
import { SavedReportMetadata } from '../SavedReports/SavedReportsTypes';
import { IMPACT_REPORT_TYPES } from '../ImpactReport/ImpactReportConstants';
import { CreativeLeaderboardMaxCreativeDropdown } from './CreativeLeaderboardMaxCreativeDropdown';
import { getFeatureFlag } from '../../../utils/featureFlagUtils';
import { AnalyticsReportStatSettingsButton } from '../../reports/components/AnalyticsReportComponents/AnalyticsReportStatSettings/AnalyticsReportStatSettingsButton';
import { CREATIVE_LEADERBOARD } from '../../../constants/creativeAnalytics.constants';
import { ColumnsDropdownType } from '../../../muiCustomComponents/ColumnDropdown/types';
import { getAnalyticsReportDefaultTitleWithDate } from '../../reports/utils/getAnalyticsReportDefaultTitleWithDate';
import { KPI_FORMATS } from '../../../types/kpi.types';
import { formatAdvancedFilterValuesForRequestV1 } from '../../components/AnalyticsFilters/utils/formatters';
import {
  addImpressionsColumn,
  createCreativeLeaderboardColumns,
} from './utils/dataGridUtils';
import { useAnalyticsFilters } from '../../components/AnalyticsFilters/hooks/useAnalyticsFilters';
import { trackCustomEventGainsight } from '../../../utils/gainsight';

const { createSavedReport, updateSavedReport } = AnalyticsSavedReportsService;

const creativeLeaderboardSx = {
  backgroundColor: 'white',
  padding: '0 24px 24px 24px',
  height: 'calc(100% - 200px)',
};

const isLeaderboardsAdvancedFiltersEnabled = getFeatureFlag(
  'isLeaderboardsAdvancedFiltersEnabled',
);

const getFilters = (
  globalFilters: GlobalFiltersType,
  advancedFilters: AdvancedFilterType[],
): CreativeLeaderboardFilterType | null => {
  const { startDate, endDate, kpi, channel, workspaces } = globalFilters;
  const isValidFilters =
    startDate &&
    endDate &&
    kpi?.value &&
    channel.value &&
    workspaces.value.length > 0;

  const preparedValuesForAdvancedFilters =
    formatAdvancedFilterValuesForRequestV1(advancedFilters, kpi?.value, true);

  if (isValidFilters) {
    return {
      startDate,
      endDate,
      workspaces: workspaces.value?.map((workspace) => workspace.id) || [],
      kpiId: kpi.value?.id || '',
      kpiName: kpi.value?.name || '',
      platform: channel.value,
      advancedFilters: isLeaderboardsAdvancedFiltersEnabled
        ? preparedValuesForAdvancedFilters
        : undefined,
    };
  }

  return null;
};

const CreativeLeaderboardV1 = () => {
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [columns, setColumns] = useState<CreativeLeaderboardColumn[]>([]);
  const [hasUnsavedUserChanges, setHasUnsavedUserChanges] = useState(false);
  const history = useHistory();
  const { pathname } = history.location;
  const { reportId: reportIdFromUrl } = useParams<ReportInfo>();
  const isExistingReport = Boolean(reportIdFromUrl);
  const defaultTitle = getAnalyticsReportDefaultTitleWithDate(
    IMPACT_REPORT_TYPES.CREATIVE_LEADERBOARD,
  );

  const {
    analyticsFilters,
    setIsDrawerOpen,
    setSelectedSections,
    resetAnalyticsFiltersState,
    setCurrentReportType,
    setAdvancedFiltersOverride,
    currency,
  } = useAnalyticsFilters();

  const { globalFilters, advancedFilters } = analyticsFilters;
  const filters = getFilters(globalFilters, advancedFilters);
  const intl = useIntl();
  const apiRef = useGridApiRef();
  const showToastAlert = useToastAlert();
  const [platformMediaIds, setPlatformMediaIds] = useState([]);
  const [platformMediaAdAccountIds, setPlatformMediaAdAccountIds] = useState<
    string[]
  >([]);
  const [leaderboardList, setLeaderboardList] = useState<{
    data: CreativeLeaderboardDataItem[];
  }>();
  const [leaderboardListLoadingError, setLeaderboardListLoadingError] =
    useState(null);
  const [isFetchingLeaderboardList, setIsFetchingLeaderboardList] =
    useState(true);
  const organizationId = useSelector(getOrganizationId);
  const hasCIAccess = useSelector((state) =>
    getCurrentPartnerFeature(
      state,
      GLOBALS.PARTNER_SPECIFIC_FEATURES.CREATIVE_INTELLIGENCE,
    ),
  );
  const currentUserId: number = useSelector(getCurrentUserId);

  const workspaceIds =
    globalFilters.workspaces.value?.map((workspace) => workspace.id) || [];

  const {
    isSavedReport,
    isSavedReportLoading,
    savedReport,
    titleText,
    setTitleText,
    descriptionText,
    setDescriptionText,
    settings,
    setSettings,
  } = useCreativeLeaderboardFilters(defaultTitle);

  const doesUserHavePermissionToSaveReport = getDoesUserHavePermissionToSave(
    currentUserId,
    savedReport?.createdBy?.id,
  );
  useEffect(() => {
    if (hasCIAccess) {
      setCurrentReportType(CREATIVE_LEADERBOARD);
      setAdvancedFiltersOverride(null);
      setIsDrawerOpen(true);
      setSelectedSections({
        SCOPE: [
          ScopeSectionChildrenType.WORKSPACES,
          ScopeSectionChildrenType.CHANNELS,
          ...(isLeaderboardsAdvancedFiltersEnabled
            ? [ScopeSectionChildrenType.AD_ACCOUNTS_ADVANCED]
            : []),
        ],
        METRIC: [MetricSectionChildrenType.KPI],
        ...(isLeaderboardsAdvancedFiltersEnabled && {
          ADVANCED: [],
        }),
      });
    }

    return () => {
      resetAnalyticsFiltersState();
      setTitleText(defaultTitle);
      setDescriptionText('');
    };
  }, []);

  useEffect(() => {
    if (!isSavedReportLoading || !isSavedReport) {
      setHasUnsavedUserChanges(true);
    }
  }, [
    analyticsFilters?.id,
    JSON.stringify(settings),
    titleText,
    descriptionText,
  ]);

  const {
    data: platformMediasMapping,
    isFetching: isFetchingPlatformMediaList,
    error: platformMediaListLoadingError,
  } = useQuery(
    platformMediaListQuery(
      platformMediaIds,
      workspaceIds,
      platformMediaAdAccountIds,
    ),
  );

  useEffect(() => {
    const selectedKpi = globalFilters.kpi?.value;
    if (filters && selectedKpi) {
      setLeaderboardListLoadingError(null);
      setIsFetchingLeaderboardList(true);

      if (isSavedReport && isSavedReportLoading) {
        return;
      }

      const tmpColumns = createCreativeLeaderboardColumns(
        intl,
        filters,
        settings,
        selectedKpi,
        currency,
      );

      const requestParams = { ...filters, ...settings };
      if (currency?.id && selectedKpi?.format === KPI_FORMATS.SPEND) {
        requestParams.currency = currency.id;
      }

      CreativeLeaderboardService.getCreativeLeaderboard(
        organizationId,
        requestParams,
      )
        .then((response) => {
          const initialColumns = addImpressionsColumn(
            intl,
            tmpColumns,
            response.data,
          );
          setColumns(initialColumns);
          setLeaderboardList(response);
          setPlatformMediaIds(
            response.data.map(
              (row: CreativeLeaderboardDataItem) => row.platformMediaId,
            ),
          );
          setPlatformMediaAdAccountIds(
            response.data.map(
              (row: CreativeLeaderboardDataItem) => row.platformAdAccount.id,
            ),
          );
        })
        .catch((error) => {
          setLeaderboardListLoadingError(error);
        })
        .finally(() => {
          setIsFetchingLeaderboardList(false);
        });
    }
  }, [
    analyticsFilters.id,
    JSON.stringify(settings),
    currency?.id,
    isSavedReportLoading,
    isSavedReport,
  ]);

  useEffect(() => {
    if (!isSavedReport) {
      setTitleText(defaultTitle);
      setDescriptionText('');
    }
  }, [isSavedReport]);

  const getSortingParams = () => {
    let sortParams = {
      sortBy: 'rank',
      sortOrder: 'ASC',
    };
    const sortingParamsFromDataGrid = apiRef?.current?.getSortModel?.();

    if (sortingParamsFromDataGrid && sortingParamsFromDataGrid?.length > 0) {
      sortParams = {
        sortBy: sortingParamsFromDataGrid[0].field,
        sortOrder: sortingParamsFromDataGrid[0].sort?.toUpperCase() || 'ASC',
      };
    }

    return sortParams;
  };

  const showSuccessToastAlert = () => {
    showToastAlert(SAVE_REPORT_SUCCESS_TOAST_TEXT_INTL, 'info');
  };

  const showErrorToastAlert = () => {
    showToastAlert(SAVE_REPORT_ERROR_TOAST_TEXT_INTL, 'error');
  };

  const trackEvent = (filters: any) => {
    try {
      const advancedAdAccounts = filters?.globalFilters?.advancedAdAccounts;
      const storageScope = advancedAdAccounts.value?.scope;
      const scope: string[] = [];
      storageScope?.adAccounts && scope.push('Ad Account');
      storageScope?.brands && scope.push('Brand');
      storageScope?.markets && scope.push('Market');
      trackCustomEventGainsight('Scope Selection', {
        reportType: CREATIVE_LEADERBOARD,
        scope: scope.length ? scope : ['No selections (default)'],
      });
    } catch (error) {
      console.error('Error tracking event:', error);
    }

    try {
      const advancedFilters = filters?.advancedFilters?.map(
        (filter: any) => filter.type,
      );
      if (advancedFilters && advancedFilters.length > 0) {
        trackCustomEventGainsight('Advanced Filters Tracking', {
          reportType: CREATIVE_LEADERBOARD,
          advancedFilters: advancedFilters.join(', '),
        });
      }
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  };

  const saveReport = (title: string, description: string) => {
    if (!analyticsFilters) {
      return;
    }

    const sortParams = getSortingParams();

    const filterParams = {
      globalFilters,
      advancedFilters: isLeaderboardsAdvancedFiltersEnabled
        ? advancedFilters
        : [],
      currency,
      ...settings,
    };

    trackEvent(filterParams);

    const saveParams = {
      titleText: title,
      descriptionText: description,
      filterParams,
      sortParams,
    };

    const reportForSaving = formatLeaderboardForSavingV1(saveParams);

    if (isExistingReport) {
      updateSavedReport(reportIdFromUrl, organizationId, reportForSaving)
        .then(() => {
          setHasUnsavedUserChanges(false);
          showSuccessToastAlert();
        })
        .catch((e) => {
          showErrorToastAlert();
          vmErrorLog(
            e,
            'CreativeLeaderboard.tsx, error updating leaderboard report',
          );
        });
    } else {
      createSavedReport(organizationId, reportForSaving)
        .then((data) => {
          const newReportId = data.id;
          history.push(`${pathname}/${newReportId}${location.search}`);
          setHasUnsavedUserChanges(false);
          showSuccessToastAlert();
        })
        .catch((e) => {
          showErrorToastAlert();
          vmErrorLog(
            e,
            'CreativeLeaderboard.tsx, error saving leaderboard report',
          );
        });
    }
  };

  const handleClickSave = () => {
    saveReport(titleText, descriptionText);
  };

  const setLimitPerAdAccount = (limitPerAdAccount: number) => {
    setSettings({ ...settings, limitPerAdAccount });
  };

  const actionBarItems = [
    <CreativeLeaderboardMaxCreativeDropdown
      key="CreativeLeaderboardMaxCreativeDropdown"
      value={settings.limitPerAdAccount}
      setLimitPerAdAccount={setLimitPerAdAccount}
    />,
    <AnalyticsReportStatSettingsButton
      key="SettingsButton"
      onClick={() => setIsSettingsModalOpen(true)}
    />,
    <ColumnDropdown
      key="ColumnDropdown"
      selectedColumns={columns.filter((column) => !column.hide)}
      columns={columns}
      updateColumns={setColumns as (columns: ColumnsDropdownType[]) => void}
    />,
  ];

  const getReportMetadata = (): SavedReportMetadata | null => {
    const reportType = IMPACT_REPORT_TYPES.CREATIVE_LEADERBOARD;
    const reportConfig = REPORT_CREATE_OPTIONS[reportType];
    const reportNameKey = `${reportConfig?.reportNameKey}.capitalized`;
    const reportDescriptionKey = reportConfig?.reportDescriptionKey;

    return {
      reportTypeCopy: intl.messages[reportNameKey] as string,
      reportDescription: intl.messages[reportDescriptionKey] as string,
      // @ts-expect-error ts-migrate
      reportIcon: reportConfig?.icon?.() as JSX.Element,
      learnMoreLink: ANALYTICS_REPORTS_HELP_CENTER_LINKS[reportType],
      owner: savedReport?.createdBy,
      lastUpdated: savedReport?.lastUpdated as string,
    };
  };

  const showCreativeLeaderboardContent = () => {
    if (filters && globalFilters.kpi?.value) {
      return (
        <Box className="creative-leaderboard" sx={creativeLeaderboardSx}>
          <CreativeLeaderboardContent
            apiRef={apiRef}
            leaderboardListLoadingError={leaderboardListLoadingError}
            platformMediaListLoadingError={platformMediaListLoadingError}
            isFetchingLeaderboardList={isFetchingLeaderboardList}
            isFetchingPlatformMediaList={isFetchingPlatformMediaList}
            columns={columns}
            filters={filters}
            currency={currency}
            leaderboardList={leaderboardList?.data}
            platformMediasMapping={platformMediasMapping}
            selectedKpi={globalFilters.kpi.value}
            initialSorting={
              savedReport?.sortBy
                ? ([
                    {
                      field: savedReport.sortBy.sortBy,
                      sort: savedReport.sortBy.sortOrder?.toLocaleLowerCase(),
                    },
                  ] as GridSortModel)
                : undefined
            }
          />
        </Box>
      );
    }

    return <AnalyticsReportNoFiltersBlankState />;
  };

  if (!hasCIAccess) {
    return <AnalyticsReportNoAccess noCIAccess pageHasFilters />;
  }

  const isLoading = isExistingReport && isSavedReportLoading;

  return (
    <>
      <CreativeLeaderboardHeader
        apiRef={apiRef}
        titleText={titleText}
        isExistingReport={isExistingReport}
        savedLeaderboard={savedReport}
        organizationId={organizationId}
        descriptionText={descriptionText}
        onTitleChange={setTitleText}
        onSubtitleChange={setDescriptionText}
        actionBarRightSlots={actionBarItems}
        handleClickSave={handleClickSave}
        hasUnsavedUserChanges={hasUnsavedUserChanges}
        areFiltersValid={getAreFiltersValid({ globalFilters, ...settings })}
        doesUserHavePermissionToSaveReport={doesUserHavePermissionToSaveReport}
        reportMetadata={getReportMetadata()}
        filters={filters}
        isLoading={isLoading}
        currency={currency}
      />
      {showCreativeLeaderboardContent()}
      <CreativeLeaderboardSettingsModal
        initialSettings={settings}
        isOpen={isSettingsModalOpen}
        onSubmit={(selectedOptions) => {
          setSettings(selectedOptions);
          setIsSettingsModalOpen(false);
        }}
        onClose={() => setIsSettingsModalOpen(false)}
      />
    </>
  );
};

export const CreativeLeaderboardWrapperV1 = () => (
  <AnalyticsFiltersProvider>
    <CreativeLeaderboardV1 />
  </AnalyticsFiltersProvider>
);
