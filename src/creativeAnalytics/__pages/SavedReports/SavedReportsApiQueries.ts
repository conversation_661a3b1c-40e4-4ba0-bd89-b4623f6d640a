import { GridSortModel } from '@mui/x-data-grid-pro';
import AnalyticsSavedReportsService from '../../reports/services/AnalyticsSavedReportsService';
import { SAVED_REPORTS_LIST_QUERY_KEY } from './SavedReportsConstants';

export const savedReportsListQuery = (requestParams: {
  organizationId: string;
  enabled: boolean;
  searchTerm?: string;
  sortModel?: GridSortModel;
}) => {
  const { organizationId, enabled } = requestParams;
  return {
    queryKey: [SAVED_REPORTS_LIST_QUERY_KEY, requestParams],
    queryFn: () =>
      AnalyticsSavedReportsService.getAllSavedReports(
        organizationId,
        requestParams,
      ),
    refetchOnWindowFocus: false,
    enabled: Boolean(organizationId) && enabled,
    keepPreviousData: true,
  };
};
