import React, { useEffect, useState } from 'react';
import useDebounce from '../../../hooks/useDebounce';
import { SearchInput } from '../../../muiCustomComponents/Dropdowns/SearchInput';
import { Box, SxProps } from '@mui/material';

interface SavedReportsSearchProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  isDisabled?: boolean;
  isClearable?: boolean;
  autoFocus?: boolean;
  boxSx?: SxProps;
}

const customSearchInputSx = {
  height: '32px',
  p: '0px',
  mb: '0px',
};

const SavedReportsSearch = (props: SavedReportsSearchProps) => {
  const {
    setSearchTerm,
    searchTerm,
    isDisabled = false,
    isClearable = false,
    autoFocus = true,
    boxSx = {},
  } = props;
  const [internalSearch, setInternalSearch] = useState(searchTerm);

  const debouncedSearchTerm = useDebounce(internalSearch, 500);

  useEffect(() => {
    setSearchTerm(internalSearch);
  }, [debouncedSearchTerm]);

  return (
    <Box sx={{ maxWidth: '220px', ...boxSx }} className="saved-reports-search">
      <SearchInput
        clearable={isClearable}
        value={internalSearch}
        onChange={(event) => setInternalSearch(event.target.value)}
        isDisabled={isDisabled}
        autoFocus={autoFocus}
        customSx={{
          searchInput: customSearchInputSx,
          textInput: {
            height: '32px',
            ...(isClearable && {
              paddingRight: '8px',
            }),
          },
        }}
      />
    </Box>
  );
};

export default SavedReportsSearch;
