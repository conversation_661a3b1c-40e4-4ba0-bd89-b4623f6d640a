import React from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { Box, ThemeProvider, useMediaQuery } from '@mui/material';
import {
  DataGridPro,
  GridCellParams,
  GridColumnVisibilityModel,
  GridPaginationModel,
  GridRowParams,
  GridSortModel,
} from '@mui/x-data-grid-pro';
import { createDatagridTheme } from '../../../reports/utils/datagrid.utils';
import {
  FIELD_DATE_UPDATED,
  REPORT_DELETE_ACTION_INTL,
  REPORT_DUPLICATE_ACTION_INTL,
  REPORT_RENAME_ACTION_INTL,
} from '../SavedReportsConstants';
import { datagridStyle } from './SavedReportsDatagridStyle';
import { AnalyticsBffReportResponse, ModalType } from '../SavedReportsTypes';
import { getCurrentUserId } from '../../../../redux/selectors/user.selectors';
import { createReportsColumns } from './SavedReportsDatagridUtils/createReportsColumns';
import { adjustRowData } from './SavedReportsDatagridUtils/adjustRowData';
import { savedReportsPerPageSize } from '../SavedReports';

interface Props {
  data: AnalyticsBffReportResponse[];
  totalSize: number;
  isFetching: boolean;
  openReport: (rowData: GridRowParams) => void;
  openModal: (modalType: ModalType, reportId: string) => void;
  duplicateReport: (reportId: string) => void;
  onPaginationModelChange: (paginationModel: GridPaginationModel) => void;
  paginationModel: GridPaginationModel;
  columnVisibilityModel: GridColumnVisibilityModel;
  sortModel: GridSortModel;
  onSortModelChange: (sortModel: GridSortModel) => void;
}

export const SavedReportsDatagrid = ({
  data,
  onPaginationModelChange,
  paginationModel,
  openModal,
  totalSize,
  openReport,
  duplicateReport,
  columnVisibilityModel,
  isFetching,
  sortModel,
  onSortModelChange,
}: Props) => {
  const currentUserId = useSelector(getCurrentUserId);
  const isSmallWindow = useMediaQuery('(max-width: 1136px)');
  const intl = useIntl();

  const getActionsForUser = (cell: GridCellParams, reportCreatorId: number) => {
    const didUserCreateReport = reportCreatorId === currentUserId;
    const RENAME_ACTION = {
      label: intl.formatMessage({
        id: REPORT_RENAME_ACTION_INTL,
        defaultMessage: 'Rename',
      }),
      onClick: () => {
        openModal(ModalType.RENAME_REPORT, cell.id as string);
      },
    };

    const DUPLICATE_ACTION = {
      label: intl.formatMessage({
        id: REPORT_DUPLICATE_ACTION_INTL,
        defaultMessage: 'Duplicate',
      }),
      onClick: () => {
        duplicateReport(cell.id as string);
      },
    };

    const DELETE_ACTION = {
      label: intl.formatMessage({
        id: REPORT_DELETE_ACTION_INTL,
        defaultMessage: 'Delete',
      }),
      isDestructive: true,
      onClick: () => {
        openModal(ModalType.DELETE_REPORT, cell.id as string);
      },
    };

    if (didUserCreateReport) {
      return [RENAME_ACTION, DUPLICATE_ACTION, DELETE_ACTION];
    }

    return [DUPLICATE_ACTION];
  };

  const columns = createReportsColumns(intl, getActionsForUser);

  const adjustedRowData = adjustRowData(data);

  return (
    <Box
      className="saved-reports-datagrid-container"
      sx={{
        height: isSmallWindow ? 'calc(100% - 224px)' : 'calc(100% - 180px)',
        width: '100%',
        marginTop: '24px',
        marginBottom: '72px',
      }}
    >
      <ThemeProvider theme={createDatagridTheme}>
        <DataGridPro
          key={`readyDataGrid-${JSON.stringify(columnVisibilityModel)}`}
          className="saved-reports-datagrid"
          disableRowSelectionOnClick
          columns={columns}
          rows={adjustedRowData}
          onRowClick={openReport}
          rowHeight={72}
          pagination
          loading={isFetching}
          rowCount={totalSize || 0}
          paginationMode="server"
          sortingMode="server"
          hideFooter
          hideFooterPagination
          pageSizeOptions={[savedReportsPerPageSize]}
          onPaginationModelChange={onPaginationModelChange}
          sortingOrder={['desc', 'asc']}
          initialState={{
            pagination: {
              paginationModel,
            },
            sorting: {
              sortModel: [{ field: FIELD_DATE_UPDATED, sort: 'desc' }],
            },
            columns: {
              columnVisibilityModel,
            },
          }}
          disableColumnMenu
          sx={datagridStyle}
          sortModel={sortModel}
          onSortModelChange={onSortModelChange}
        />
      </ThemeProvider>
    </Box>
  );
};
