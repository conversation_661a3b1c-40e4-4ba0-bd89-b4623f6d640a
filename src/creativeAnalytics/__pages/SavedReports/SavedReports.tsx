import React, { useState, useMemo, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { useToastAlert } from './SavedReportsCustomHooks';
import { Box, Button, Typography, useMediaQuery } from '@mui/material';
import {
  GridColumnVisibilityModel,
  GridPaginationModel,
  GridRowParams,
  GridSortModel,
} from '@mui/x-data-grid-pro';
import CreateReportModal from '../../../muiCustomComponents/CreateReportModal';
import { SavedReportsDatagrid } from './SavedReportsDatagrid';
import {
  AnalyticsBffReportRenameParams,
  AnalyticsBffReportResponse,
  ModalType,
  SavedReportsColumn,
  SavedReportsQueryParamsType,
} from './SavedReportsTypes';
import PageHeaderV2 from '../../../components/PageHeaderV2';
import GenericBlankState from '../../../muiCustomComponents/GenericBlankState';
import { SearchFilledIcon } from '../../../assets/vidmob-mui-icons/general';
import {
  BLANK_STATE_SUBTITLE_INTL,
  BLANK_STATE_TITLE_INTL,
  CREATE_REPORT_INTL,
  ERROR_STATE_MESSAGE_INTL,
  TITLE_TEXT_INTL,
  RENAME_REPORT_UPDATE_TOAST_SUCCESS_MESSAGE_INTL,
  RENAME_REPORT_UPDATE_TOAST_ERROR_MESSAGE_INTL,
  DELETE_REPORT_TOAST_SUCCESS_MESSAGE_INTL,
  DELETE_REPORT_TOAST_ERROR_MESSAGE_INTL,
  DUPLICATE_REPORT_TOAST_SUCCESS_MESSAGE_INTL,
  DUPLICATE_REPORT_TOAST_ERROR_MESSAGE_INTL,
  DUPLICATE_REPORT_TOAST_ERROR_NOREPORTFOUND,
  LOAD_REPORT_TOAST_ERROR_MESSAGE_INTL,
  REPORT_CREATE_OPTIONS,
  SAVED_REPORTS_PATH,
  REPORTS_COLUMNS,
  FIELD_DATE_UPDATED,
} from './SavedReportsConstants';
import { useQuery } from '@tanstack/react-query';
import { savedReportsListQuery } from './SavedReportsApiQueries';
import { RenameReportModal, DeleteReportModal } from './SavedReportsModals';
import AnalyticsSavedReportsService from '../../reports/services/AnalyticsSavedReportsService';
import { CustomDataReportImage } from '../../../assets/vidmob-mui-images';
import {
  getCurrentPartnerFeature,
  getOrganizationId,
} from '../../../redux/selectors/partner.selectors';
import analyticsConfigurationSlice from '../../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';
import { GLOBALS } from '../../../constants';
import {
  CREATIVE,
  CREATIVE_LEADERBOARD_REPORT_TYPE,
  ELEMENT_IMPACT_REPORT_TYPE,
  IMPACT_REPORT_TYPES,
  MEDIA_IMPACT_REPORT_TYPE,
  ReportTypeKey,
} from '../ImpactReport/ImpactReportConstants';
import {
  openCriteriaPerformanceReport,
  openCustomCompareReport,
  openImpactReport,
  openLeaderboard,
} from './SavedReportsUtils/openReport';
import vmErrorLog from '../../../utils/vmErrorLog';
import {
  AnalyticsReportErrorState,
  AnalyticsReportNoAccess,
} from '../../reports/components/AnalyticsReportComponents/AnalyticsReportBlankStates';
import { CRITERIA_PERFORMANCE } from '../../../constants/creativeAnalytics.constants';
import { duplicateReport } from './SavedReportsUtils/duplicateReport';
import { SavedReportControlBar } from './SavedReportsControlBar/SavedReportControlBar';
import {
  FilterSelectedValueType,
  LandingPageFilterId,
} from '../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import { useLocalStorage } from '../../../hooks/useLocalStorageState';
import {
  trackCustomEventGainsight,
  trackFeatureUsageGainsight,
} from '../../../utils/gainsight';
import { convertDateToDayJSObject } from '../../../utils/dateRangePickerMUIUtils';

const { deleteSavedReport, getSavedReportById, renameSavedReport } =
  AnalyticsSavedReportsService;

interface ModalState {
  activeModal: ModalType;
  activeReport: AnalyticsBffReportResponse | null;
}

interface ToastMessageKeys {
  success: string;
  error: string;
}

type OperationFn = (
  reportId: string,
  organizationId: string,
  renameReportParams?: AnalyticsBffReportRenameParams,
) => Promise<void>;

export const savedReportsPerPageSize = 20;

export const SavedReports = () => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const showToastAlert = useToastAlert();
  const isSmallWindow = useMediaQuery('(max-width: 1136px)');

  const organizationId = useSelector(getOrganizationId);
  const hasCIAccess = useSelector((state) =>
    getCurrentPartnerFeature(
      state,
      GLOBALS.PARTNER_SPECIFIC_FEATURES.CREATIVE_INTELLIGENCE,
    ),
  );

  const [modalState, setModalState] = useState<ModalState>({
    activeModal: ModalType.NONE,
    activeReport: null,
  });

  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: savedReportsPerPageSize,
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [columns, setColumns] = useLocalStorage<SavedReportsColumn[]>(
    'savedAnalyticsReportsColumns',
    REPORTS_COLUMNS,
    {
      isSessionStorage: true,
    },
  );

  useEffect(() => {
    trackFeatureUsageGainsight('Analytics Reports Landing Filters', {
      Context: 'Analytics Reports Landing with Filters',
    });
  }, []);

  const [filters, setFilters] = useLocalStorage<
    Record<string, FilterSelectedValueType>
  >(
    'savedAnalyticsReportsFilters',
    {
      [LandingPageFilterId.WORKSPACES]: [],
      [LandingPageFilterId.REPORT_TYPE]: Object.keys(IMPACT_REPORT_TYPES),
      [LandingPageFilterId.CREATED_BY]: [],
      [LandingPageFilterId.DATE_CREATED]: [],
      [LandingPageFilterId.DATE_UPDATED]: [],
      [LandingPageFilterId.CHANNELS]: [],
      [LandingPageFilterId.AD_ACCOUNTS]: [],
    },
    {
      isSessionStorage: true,
      parseValue: {
        [LandingPageFilterId.DATE_CREATED as string]: (value: string[]) =>
          value.length > 0
            ? [
                convertDateToDayJSObject(value[0]),
                convertDateToDayJSObject(value[1]),
              ]
            : [],

        [LandingPageFilterId.DATE_UPDATED as string]: (value: string[]) =>
          value.length > 0
            ? [
                convertDateToDayJSObject(value[0]),
                convertDateToDayJSObject(value[1]),
              ]
            : [],
      },
    },
  );

  const defaultSortModel = [
    { field: FIELD_DATE_UPDATED, sort: 'desc' },
  ] as GridSortModel;

  const [sortModel, setSortModel] = useState<GridSortModel>(defaultSortModel);

  const reportSelectionItems = Object.values(REPORT_CREATE_OPTIONS);

  const titleText = intl.messages[TITLE_TEXT_INTL] as string;
  const createReportText = intl.messages[CREATE_REPORT_INTL] as string;
  const blankStateTitleText = intl.messages[BLANK_STATE_TITLE_INTL] as string;
  const blankStateSubtitleText = intl.messages[
    BLANK_STATE_SUBTITLE_INTL
  ] as string;

  const queryOptions = useMemo(
    () => ({
      offset: paginationModel.page * paginationModel.pageSize,
      perPage: paginationModel.pageSize,
      sortModel: sortModel.length ? sortModel : defaultSortModel,
    }),
    [paginationModel, sortModel],
  );

  const formattedFiltersV2 = useMemo(() => {
    const newFilters = {
      ...filters,
      enabled: Boolean(organizationId),
      searchTerm,
      channels: filters[LandingPageFilterId.CHANNELS].map((channel) =>
        channel?.toString().toLowerCase(),
      ),
      createdBy: filters[LandingPageFilterId.CREATED_BY]
        .map((createdBy) => createdBy && Number(createdBy))
        .filter(Boolean),
      dateCreated: filters[LandingPageFilterId.DATE_CREATED][0]
        ? {
            startDate: filters[LandingPageFilterId.DATE_CREATED][0],
            endDate: filters[LandingPageFilterId.DATE_CREATED][1],
          }
        : undefined,
      lastUpdated: filters[LandingPageFilterId.DATE_UPDATED][0]
        ? {
            startDate: filters[LandingPageFilterId.DATE_UPDATED][0],
            endDate: filters[LandingPageFilterId.DATE_UPDATED][1],
          }
        : undefined,
    };

    return Object.fromEntries(
      Object.entries(newFilters).filter(
        ([_, value]) =>
          value !== undefined && (!Array.isArray(value) || value.length > 0),
      ),
    );
  }, [filters, organizationId, searchTerm]);

  const {
    data: savedReportsData,
    refetch: fetchSavedReportsList,
    isFetching: isFetchingSavedReportsList,
    error: savedReportsListLoadingError,
  } = useQuery(
    savedReportsListQuery({
      organizationId: organizationId,
      enabled: Boolean(organizationId),
      searchTerm,
      filters: formattedFiltersV2,
      ...queryOptions,
    } as SavedReportsQueryParamsType),
  );

  useEffect(() => {
    dispatch(analyticsConfigurationSlice.actions.resetReportTableView());
  }, []);

  const savedReportsList = useMemo(() => {
    return savedReportsData?.data.map(
      (report: AnalyticsBffReportResponse) => ({
        ...report,
        filters: {
          // adding workspaceIds and adAccountIds to filters since they are required by the API for any renaming or duplication of report
          ...report.filters,
          workspaceIds: report.filters.workspaces?.map(
            (workspace) => workspace.id,
          ),
          adAccountIds: report.filters.adAccounts?.map(
            (adAccount) => adAccount.platformAccountId,
          ),
        },
      }),
      [],
    );
  }, [JSON.stringify(savedReportsData?.data)]);

  const savedReportsPagination = savedReportsData?.pagination;
  const totalReports = savedReportsPagination?.totalSize;
  const hasReports = totalReports > 0;

  const openCreateReportModal = () => openModal(ModalType.CREATE_REPORT);

  const withReportOperation = (
    operation: OperationFn,
    toastMessageKeys: ToastMessageKeys,
  ) => {
    return async (
      reportId: string,
      organizationId: string,
      renameReportParams?: AnalyticsBffReportRenameParams,
    ) => {
      try {
        await operation(reportId, organizationId, renameReportParams);
        closeModal();
        showToastAlert(toastMessageKeys.success, 'info');
        await fetchSavedReportsList();
      } catch (error) {
        closeModal();
        showToastAlert(toastMessageKeys.error, 'error');
      }
    };
  };

  const trackEvent = (reportType: ReportTypeKey, reportId: string) => {
    try {
      trackCustomEventGainsight('Open Save Reports', {
        pillar: 'Analytics',
        reportType: reportType,
        reportId: reportId,
      });
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  };

  const openReport = async ({ row }: GridRowParams) => {
    const reportType = row.reportType as ReportTypeKey;
    const { id: reportId, adAccount } = row;

    if (!adAccount?.length) {
      vmErrorLog(
        new Error('Analytics Report is missing ad account information'),
        'SavedReports.tsx openReport',
        `report id ${reportId} of type ${reportType} does not have ad account information`,
      );
      return;
    }

    trackEvent(reportType, reportId);

    // TO DO: We need to find a better solve here. Right now custom compare report is triggered by the groupBy
    // This is no longer coming back from the saved report API so we are making a duplicative API call.
    // We should give Custom Compare Report its own report type and use that to trigger the custom compare report
    if (
      reportType === MEDIA_IMPACT_REPORT_TYPE ||
      reportType === ELEMENT_IMPACT_REPORT_TYPE
    ) {
      try {
        const { data } = await getSavedReportById({ reportId, organizationId });
        if (data.groupBy.columns === CREATIVE) {
          return openCustomCompareReport(reportId);
        }
      } catch (error) {
        showToastAlert(LOAD_REPORT_TOAST_ERROR_MESSAGE_INTL, 'error');
      }
    }

    loadReport(reportType, reportId);
  };

  const loadReport = (reportType: ReportTypeKey, reportId: string) => {
    switch (reportType) {
      case CREATIVE_LEADERBOARD_REPORT_TYPE:
        return openLeaderboard(reportId);
      case CRITERIA_PERFORMANCE:
        return openCriteriaPerformanceReport(reportId);
      default:
        return openImpactReport(reportId, reportType);
    }
  };

  const deleteReport = withReportOperation(deleteSavedReport, {
    success: DELETE_REPORT_TOAST_SUCCESS_MESSAGE_INTL,
    error: DELETE_REPORT_TOAST_ERROR_MESSAGE_INTL,
  });

  const renameReport = withReportOperation(renameSavedReport, {
    success: RENAME_REPORT_UPDATE_TOAST_SUCCESS_MESSAGE_INTL,
    error: RENAME_REPORT_UPDATE_TOAST_ERROR_MESSAGE_INTL,
  });

  const duplicateReportFromSavedReports = async (reportId: string) => {
    if (!reportId) {
      showToastAlert(DUPLICATE_REPORT_TOAST_ERROR_NOREPORTFOUND, 'error');
      return;
    }

    try {
      await duplicateReport(organizationId, reportId);
      showToastAlert(DUPLICATE_REPORT_TOAST_SUCCESS_MESSAGE_INTL, 'info');
      await fetchSavedReportsList();
    } catch (error) {
      showToastAlert(DUPLICATE_REPORT_TOAST_ERROR_MESSAGE_INTL, 'error');
    }
  };

  if (!hasCIAccess) {
    return <AnalyticsReportNoAccess noCIAccess />;
  }

  if (savedReportsListLoadingError) {
    return (
      <AnalyticsReportErrorState
        className="saved-reports-error-state"
        message={intl.messages[ERROR_STATE_MESSAGE_INTL] as string}
      />
    );
  }

  const renderSavedReportsBody = () => {
    const columnVisibilityModel = columns.reduce(
      (acc: GridColumnVisibilityModel, column: SavedReportsColumn) => {
        acc[column.field] = !column.hide;
        return acc;
      },
      {},
    );

    if (hasReports || isFetchingSavedReportsList) {
      return (
        <SavedReportsDatagrid
          data={(savedReportsList as AnalyticsBffReportResponse[]) || []}
          totalSize={totalReports}
          openReport={openReport}
          openModal={openModal}
          isFetching={isFetchingSavedReportsList}
          duplicateReport={duplicateReportFromSavedReports}
          onPaginationModelChange={setPaginationModel}
          paginationModel={paginationModel}
          columnVisibilityModel={columnVisibilityModel}
          sortModel={sortModel}
          onSortModelChange={setSortModel}
        />
      );
    }

    if (!hasReports && Boolean(searchTerm.length)) {
      return <NoSearchResultsBlankState />;
    }

    return <BlankState />;
  };

  const findReport = (targetReportId: string) =>
    savedReportsList?.find(
      (report: AnalyticsBffReportResponse) => report.id === targetReportId,
    );

  const openModal = (modalType: ModalType, activeReportId?: string | null) => {
    if (modalState.activeModal === ModalType.NONE) {
      const activeReport = activeReportId ? findReport(activeReportId) : null;
      setModalState({
        activeModal: modalType,
        activeReport: activeReport || null,
      });
    }
  };

  const closeModal = () => {
    setModalState({
      ...modalState,
      activeModal: ModalType.NONE,
      activeReport: null,
    });
  };

  const onSubmitRenameReportModal = (
    newName: string,
    newDescription: string,
  ) => {
    renameReport(modalState.activeReport?.id as string, organizationId, {
      name: newName,
      description: newDescription,
    });
  };

  const onSubmitDeleteModal = () => {
    deleteReport(modalState.activeReport?.id as string, organizationId);
  };

  const BlankState = () => (
    <GenericBlankState
      title={blankStateTitleText}
      subtitle={blankStateSubtitleText}
      image={<CustomDataReportImage sx={{ width: '5rem', height: '5rem' }} />}
    >
      <Button
        variant="contained"
        onClick={() => openModal(ModalType.CREATE_REPORT)}
      >
        <Typography variant="subtitle2">{createReportText}</Typography>
      </Button>
    </GenericBlankState>
  );

  const NoSearchResultsBlankState = () => (
    <GenericBlankState
      title={intl.messages['ui.workspaces.list.table.noMatch.title']}
      subtitle={intl.formatMessage(
        { id: 'ui.workspaces.list.table.noMatch.descriptionKeyword' },
        { searchKeyword: searchTerm },
      )}
      icon={<SearchFilledIcon />}
    />
  );

  return (
    <Box
      className="saved-reports"
      sx={{
        backgroundColor: 'white',
        height: '100%',
      }}
      paddingX={12}
      paddingBottom={isSmallWindow ? 32 : 0}
    >
      <PageHeaderV2
        title={titleText}
        additionalBoxStyles={{
          pl: 0,
          pr: 0,
          maxHeight: '36px',
          fontWeight: 600,
          fontSize: '14px',
          lineHeight: '20px',
        }}
      >
        <Box>
          <Button variant="contained" onClick={openCreateReportModal}>
            <Typography variant="subtitle2">{createReportText}</Typography>
          </Button>
        </Box>
      </PageHeaderV2>
      <SavedReportControlBar
        isLoading={isFetchingSavedReportsList && savedReportsList?.length === 0}
        columns={columns}
        setColumns={setColumns}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        filters={filters}
        onFilterChange={setFilters}
        pageSize={savedReportsPerPageSize}
        page={paginationModel.page}
        totalCount={savedReportsPagination?.totalSize as number}
        onPageChange={(page) =>
          setPaginationModel({ ...paginationModel, page })
        }
      />
      {renderSavedReportsBody()}
      {modalState.activeModal === ModalType.CREATE_REPORT && (
        <CreateReportModal
          isOpen={true}
          previousPagePath={SAVED_REPORTS_PATH}
          onClose={closeModal}
          reportOptions={reportSelectionItems}
        />
      )}
      {modalState.activeModal === ModalType.RENAME_REPORT &&
        modalState.activeReport && (
          <RenameReportModal
            isOpen={true}
            onClose={closeModal}
            onSubmit={(newName: string, newDescription: string) =>
              onSubmitRenameReportModal(newName, newDescription)
            }
            defaultName={modalState.activeReport.name as string}
            defaultDescription={modalState.activeReport.description as string}
          />
        )}
      {modalState.activeModal === ModalType.DELETE_REPORT &&
        modalState.activeReport && (
          <DeleteReportModal
            isOpen={true}
            onClose={closeModal}
            onSubmit={onSubmitDeleteModal}
            reportName={modalState.activeReport.name as string}
          />
        )}
    </Box>
  );
};
