import React from 'react';
import TagPill from '../../../../../../../components/TagPill';
import {
  VidMobBox,
  VidMobTooltip,
} from '../../../../../../../vidMobComponentWrappers';
import { FlagIcon } from '../../../../../../../assets/vidmob-mui-icons/general';
import { TAGS_COLORS_MAP } from '../../../../../../../constants/ci.constants';

import './AudienceEngagementElementSection.scss';
import {
  Tag,
  TagClip,
} from '../../../../../../reports/utils/TagProcessor/TagTransformer.types';
import { useIntl } from 'react-intl';

const ALL_ELEMENTS_FLAGGED_MESSAGE_INTL =
  'individualCreativeViewV2.flag.modal.allElementsFlagged';
const SOME_ELEMENTS_FLAGGED_MESSAGE_INTL =
  'individualCreativeViewV2.flag.modal.someElementsFlagged';

type AudienceEngagementElementSectionItemProps = {
  hideTag?: boolean;
  mediaDuration: number;
  isImage: boolean;
  handleOpenFlagModal?: (tag: Tag) => void;
  tag: Tag;
  flaggedTimeRanges?: number[];
  selectedTimeRange?: number[];
  onSelectTimeRange?: (timeRange: number) => void;
  onSeekTo?: (time: number) => void;
};

const getTagColor = (elementType: string): string | undefined =>
  TAGS_COLORS_MAP[elementType] || TAGS_COLORS_MAP[elementType.toLowerCase()];

const getTimelinePillPosition = (clip: TagClip<any>, mediaDuration: number) => {
  const isNoDuration = !clip.duration;
  return {
    start: isNoDuration ? 0 : (clip.startTime / mediaDuration) * 100,
    width: isNoDuration ? 100 : (clip.duration / mediaDuration) * 100,
  };
};

const AudienceEngagementElementSectionItem = ({
  hideTag = false,
  mediaDuration,
  isImage,
  handleOpenFlagModal,
  tag,
  flaggedTimeRanges,
  selectedTimeRange,
  onSelectTimeRange,
  onSeekTo,
}: AudienceEngagementElementSectionItemProps) => {
  const intl = useIntl();

  // renders pill for weighted tag types (e.g. text words per second, speech words per second)
  const renderWeightedTags = (clips: TagClip<any>[], tag: Tag) =>
    clips.map((clip, idx) => {
      const isFlagged = Boolean(flaggedTimeRanges?.length);
      const { width, start } = getTimelinePillPosition(clip, mediaDuration);
      const pillData = {
        start,
        width,
        height: (clip.value / tag.maxValue) * 44, // height of div = 44px
      };

      return (
        <div
          key={tag.title + clip.startTime + idx}
          className={`timeline-indicator-pill-weighted ${
            isFlagged ? 'timeline-indicator-pill-flagged' : ''
          }`}
          style={{
            left: `${pillData.start}%`,
            width: `${pillData.width}%`,
            height: `${pillData.height}px`,
            backgroundColor: getTagColor(tag.elementType),
          }}
        />
      );
    });

  // renders "regular" pills
  const renderPillTags = (clips: TagClip<any>[], tag: Tag) =>
    clips.map((clip) => {
      const { width, start } = getTimelinePillPosition(clip, mediaDuration);
      const isFlagged = flaggedTimeRanges?.includes(clip.startTime);
      const isSelected = selectedTimeRange?.includes(clip.startTime);
      return (
        <VidMobTooltip
          title={
            isFlagged
              ? intl.formatMessage({
                  id: 'individualCreativeViewV2.flag.modal.flaggedPill',
                  defaultMessage: 'This instance has already been flagged',
                })
              : null
          }
          placement="top"
          key={tag.title + clip.startTime}
        >
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (isFlagged) return;
              onSelectTimeRange?.(clip.startTime);
            }}
            key={tag.title + clip.startTime}
            className={`timeline-indicator-pill ${
              isFlagged ? 'timeline-indicator-pill-flagged' : ''
            } ${isSelected ? 'timeline-indicator-pill-selected' : ''}`}
            style={{
              left: `${start}%`,
              width: `${width}%`,
              backgroundColor: getTagColor(tag.elementType),
              cursor: onSelectTimeRange || isFlagged ? 'pointer' : 'default',
            }}
          />
        </VidMobTooltip>
      );
    });

  const handleSeekTo = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    const target = e.target as HTMLDivElement;
    const leftOffsetFromClick = e.clientX - target.getBoundingClientRect().left;
    const startTime =
      (leftOffsetFromClick / target.clientWidth) * mediaDuration;
    onSeekTo?.(startTime);
  };

  // renders line item for element (consists of element pill section and indicator pill section)
  const renderLineItem = (tag: Tag) => {
    const { clips, isWeighted } = tag;
    let tagTitle = tag.title;
    if (isWeighted) {
      if (tag.title === 'Text Density') {
        tagTitle += ` (max: ${Math.round(tag.maxValue)}%)`;
      } else {
        tagTitle += ` (max: ${Math.round(tag.maxValue)})`;
      }
    }
    const isAllFlagged = flaggedTimeRanges?.length === clips.length;
    const isSomeFlagged = flaggedTimeRanges && flaggedTimeRanges.length > 0;

    return (
      <div
        key={tagTitle}
        className="audience-engagement-element-line-item"
        onClick={handleSeekTo}
      >
        {!hideTag ? (
          <VidMobBox
            className="element-tag-pill-section"
            style={{
              borderRight: isImage ? undefined : '1px solid #d6d6d6',
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'row',
            }}
            gap={2}
          >
            <VidMobTooltip
              title={
                isAllFlagged || (isWeighted && isSomeFlagged)
                  ? intl.formatMessage({
                      id: ALL_ELEMENTS_FLAGGED_MESSAGE_INTL,
                      defaultMessage:
                        'All instances of this element have been flagged',
                    })
                  : isSomeFlagged
                    ? intl.formatMessage({
                        id: SOME_ELEMENTS_FLAGGED_MESSAGE_INTL,
                        defaultMessage:
                          'Some instances of this element have been flagged',
                      })
                    : tagTitle
              }
              position="above"
            >
              <div className="tooltip-container">
                <TagPill
                  isTextSmallBlack
                  capitalizeText
                  className="tag-pill-wrapper"
                  label={tagTitle}
                  color={getTagColor(tag.elementType)}
                  isFlagged={isSomeFlagged}
                  onClick={() => {
                    if (!isAllFlagged) {
                      handleOpenFlagModal?.(tag);
                    }
                  }}
                />
              </div>
            </VidMobTooltip>
            {handleOpenFlagModal && !isAllFlagged ? (
              <VidMobTooltip
                title={intl.formatMessage({
                  id: 'individualCreativeViewV2.flag.button.tooltip',
                  defaultMessage: 'Flag this element',
                })}
                position="above"
              >
                <button
                  className="flag-button"
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onClick={() => handleOpenFlagModal(tag)}
                >
                  <FlagIcon />
                </button>
              </VidMobTooltip>
            ) : null}
          </VidMobBox>
        ) : null}
        <div
          className="element-timeline-indicator-section"
          style={{
            width: hideTag ? '100%' : 'calc(100% - 281px)',
          }}
        >
          {isWeighted
            ? renderWeightedTags(clips, tag)
            : renderPillTags(clips, tag)}
        </div>
      </div>
    );
  };

  return renderLineItem(tag);
};

export default AudienceEngagementElementSectionItem;
