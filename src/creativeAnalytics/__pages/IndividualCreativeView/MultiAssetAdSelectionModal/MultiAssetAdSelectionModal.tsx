import React, { useState } from 'react';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';
import { useIntl } from 'react-intl';
import MultiAssetAdSelectionDataGrid from './MultiAssetAdSelectionDataGrid/MultiAssetAdSelectionDataGrid';
import { composeLinkHelperIndividualCreativeView } from '../../../helpers/composeLinkHelper';
import { useHistory } from 'react-router-dom';
import { Typography } from '@mui/material';
import {
  defaultHeaderStyle,
  headerSubTextStyle,
} from '../../../../muiCustomComponents/CustomDialog/CustomDialog';
import {
  VidMobBox,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { InfoFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import { CustomHistoryState } from '../../../services/AnalyticsConfiguration/AnalyticsConfiguration.types';

const headerTextSx = {
  width: '730px',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
};

const HEADER_TEXT_KEY = 'individualCreativeView.modal.adSelection.header';
const HEADER_TEXT_INITIAL_CREATIVE_KEY =
  'individualCreativeView.modal.adSelection.header.initialCreative';
const HEADER_TOOLTIP_KEY =
  'individualCreativeView.modal.adSelection.header.tooltip';
const SUB_HEADER_TEXT_KEY =
  'individualCreativeView.modal.adSelection.subheader';
const SELECT_BUTTON_TEXT_KEY = 'button.global.select';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  platformMediaId: string;
  mediaName?: string;
  ICVlink?: string;
  platform?: string;
  adAccountId?: string;
  adIdFromURL?: string;
  isICVPage?: boolean;
  source?: string;
  state?: CustomHistoryState;
};

export const MultiAssetAdSelectionModal = ({
  isOpen,
  onClose,
  platformMediaId,
  mediaName,
  platform,
  adAccountId,
  adIdFromURL,
  isICVPage = false,
  source,
  state,
}: Props) => {
  const intl = useIntl();
  const history = useHistory();

  const [selectedAd, setSelectedAd] = useState<string | null>(
    adIdFromURL || null,
  );

  const handleClose = () => {
    onClose();
    setSelectedAd(null);
  };

  const handleSubmit = () => {
    handleClose();

    const ICVLink = composeLinkHelperIndividualCreativeView({
      platformMediaId,
      source,
      platform,
      isCreativeOfMultiAssetAds: true,
      adAccountId: adAccountId as string,
      adId: selectedAd as string,
    });

    if (isICVPage) {
      window.location.href = ICVLink || '';
    } else {
      history.push({
        pathname: ICVLink || '/',
        state,
      });
    }
  };

  const headerTextFirstPart = intl.formatMessage({
    id: HEADER_TEXT_KEY,
    defaultMessage: 'Select an ad that includes',
  });
  const headerNameDefaultCreativeName = intl.formatMessage({
    id: HEADER_TEXT_INITIAL_CREATIVE_KEY,
    defaultMessage: 'initial selected creative',
  });

  const headerText = `${headerTextFirstPart} ${mediaName || headerNameDefaultCreativeName}`;
  const headerSubText = intl.formatMessage({
    id: SUB_HEADER_TEXT_KEY,
    defaultMessage:
      'This creative is part of multiple ads. Select an ad to analyze performance.',
  });

  const customHeader = (
    <VidMobBox sx={defaultHeaderStyle}>
      <VidMobStack direction="row" alignItems="center" spacing={4}>
        <VidMobTooltip title={mediaName} placement="top">
          <Typography variant="h6" sx={headerTextSx}>
            {headerText}
          </Typography>
        </VidMobTooltip>
        <VidMobTooltip
          title={intl.formatMessage({
            id: HEADER_TOOLTIP_KEY,
            defaultMessage: 'Initial selected creative',
          })}
        >
          <InfoFilledIcon className="small" />
        </VidMobTooltip>
      </VidMobStack>
      <VidMobTypography sx={headerSubTextStyle}>
        {headerSubText}
      </VidMobTypography>
    </VidMobBox>
  );

  return (
    <CustomDialog
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={handleSubmit}
      customHeaderChildren={customHeader}
      bodyChildren={
        <MultiAssetAdSelectionDataGrid
          platformMediaId={platformMediaId}
          selectedAd={selectedAd}
          setSelectedAd={setSelectedAd}
          adIdFromURL={adIdFromURL}
          adAccountIdForLeaderboard={adAccountId}
        />
      }
      explicitDialogWidth={800}
      explicitDialogHeight={'80vh'}
      submitButtonLabel={intl.messages[SELECT_BUTTON_TEXT_KEY]}
      isSubmitButtonDisabled={!selectedAd}
    />
  );
};
