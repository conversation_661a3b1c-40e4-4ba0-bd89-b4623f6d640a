import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useIntl } from 'react-intl';
import individualCreativeViewSlice from '../../reports/redux/individualCreativeView/individualCreativeView.slice';
import individualCreativeViewAudienceEngagementSlice from '../../reports/redux/individualCreativeView/IndividualCreativeViewAudienceEngagement/IndividualCreativeViewAudienceEngagement.slice';
import individualCreativeViewPerformanceOverTimeSlice from '../../reports/redux/individualCreativeView/IndividualCreativeViewPerformanceOverTime/IndividualCreativeViewPerformanceOverTime.slice';
import {
  getIndividualCreativeViewError,
  getIndividualCreativeViewMediaData,
  getIndividualCreativeViewMediaDataStatus,
} from '../../reports/redux/individualCreativeView/individualCreativeView.selectors';
import { getOrganizationId } from '../../../redux/selectors/partner.selectors';
import IndividualCreativeViewMediaPreview from './IndividualCreativeViewMediaPreview';
import BlankStateError from '../../../components/BlankStateError';
import BlankState from '../../../components/BlankState';
import IndividualCreativeViewDatePicker from './IndividualCreativeViewDatePicker';
import IndividualCreativeViewData from './IndividualCreativeViewData/IndividualCreativeViewData';
import { VidMobBox, VidMobStack } from '../../../vidMobComponentWrappers';
import IndividualCreativeViewHeader from './IndividualCreativeViewHeader';
import blankStateImage from '../../../assets/icons/blanks/bs-error.svg';
import IndividualCreativeViewMetrics from './IndividualCreativeViewMetrics';
import { FILE_TYPES } from '../../../constants/analytics.api.constants';
import { useIndividualCreativeViewUrl } from './useIndividualCreativeViewUrl';
import { ACCOUNT_ACCOUNT_TYPE } from '../../../constants/ci.constants';
import { setIndividualCreativeViewSliceRequestParams } from './setIndividualCreativeViewSliceRequestParams';
import { useAnalyticsFilters } from '../../components/AnalyticsFilters/hooks/useAnalyticsFilters';

const IndividualCreativeView = () => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const { search, pathname, state } = useLocation() || {};

  const error = useSelector(getIndividualCreativeViewError);
  const mediaData = useSelector(getIndividualCreativeViewMediaData);
  const mediaDataStatus = useSelector(getIndividualCreativeViewMediaDataStatus);
  const organizationId = useSelector(getOrganizationId);

  const { analyticsFilters } = useAnalyticsFilters();

  const {
    kpi,
    workspaces,
    startDate: analyticsStartDate,
    endDate: analyticsEndDate,
    channel: { value: platform },
  } = analyticsFilters?.globalFilters || {};

  const [analyticsDates, setAnalyticsDates] = useState({
    analyticsStartDate,
    analyticsEndDate,
  });

  const workspaceIds = workspaces?.value?.map((workspace) => workspace.id);

  const assetName = mediaData.displayName || mediaData.name;
  const { fileType } = mediaData;
  const routeParams = useIndividualCreativeViewUrl({ search, pathname, state });

  const { source, adAccountId: adAccountIdFromParams } = routeParams;

  useEffect(() => {
    setIndividualCreativeViewSliceRequestParams(dispatch, {
      ...routeParams,
      globalFilters: analyticsFilters?.globalFilters,
    });
  }, []);

  useEffect(() => {
    // TODO: This is what triggers the saga. When remove saga, this can be removed.
    dispatch(
      individualCreativeViewSlice.actions.setIndividualCreativeViewParams({
        ...routeParams,
        accountType: ACCOUNT_ACCOUNT_TYPE.toLowerCase(),
        kpi: kpi?.value?.id,
        organizationId,
        workspaceIds,
        accountId: adAccountIdFromParams,
        analyticsEndDate,
        analyticsStartDate,
        platform,
      }),
    );

    return () => {
      dispatch(
        individualCreativeViewSlice.actions.resetIndividualCreativeViewMediaPerformance(),
      ); // Clears out performance data when leave page
      dispatch(individualCreativeViewSlice.actions.reset()); // Clears out ICV data when leave page
      dispatch(
        individualCreativeViewAudienceEngagementSlice.actions.resetAudienceEngagementSlice(),
      ); // Reset buckets after exit
      dispatch(individualCreativeViewPerformanceOverTimeSlice.actions.reset());
    };
  }, []);

  const errorStyles = {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  };

  // Zig Zag Error
  if (error?.type === 'URL_ERROR') {
    return (
      <VidMobStack
        sx={errorStyles}
        className="individual-creative-view-page-invalid-url-error"
        data-testid="load-error"
      >
        <BlankStateError intl={intl} />
      </VidMobStack>
    );
  }

  if (error?.type === 'LOAD_ERROR') {
    return (
      <VidMobStack
        className="individual-creative-view-page-load-error"
        sx={errorStyles}
      >
        <BlankState
          message={intl.messages['error.api.link.error']}
          iconAsset={blankStateImage}
          className="error-state"
        />
      </VidMobStack>
    );
  }

  return (
    <VidMobBox
      className="individual-creative-view-page"
      sx={{
        height: '100%',
        width: 'calc(100vw - 5em)',
        mr: '28px',
      }}
    >
      <IndividualCreativeViewHeader title={assetName} source={source} />
      <VidMobStack
        className="main-section"
        alignItems="start"
        flexDirection="row"
        sx={{
          width: '100vw',
          height: 'calc(100vh - 190px)',
          overflow: 'hidden',
          gap: '24px',
          margin: '24px',
        }}
      >
        <IndividualCreativeViewMediaPreview
          mediaDataStatus={mediaDataStatus}
          mediaData={mediaData}
        />
        <VidMobStack
          className="main-section-right"
          sx={{ width: '100%', height: '100%' }}
        >
          <IndividualCreativeViewDatePicker
            analyticsDates={analyticsDates}
            setAnalyticsDates={setAnalyticsDates}
            mediaData={mediaData}
          />

          {fileType === FILE_TYPES.image && (
            <IndividualCreativeViewMetrics
              analyticsDates={analyticsDates}
              routeParams={routeParams}
              organizationId={organizationId}
            />
          )}
          <IndividualCreativeViewData
            fileType={fileType}
            platform={platform}
            analyticsStartDate={analyticsStartDate}
            analyticsEndDate={analyticsEndDate}
          />
        </VidMobStack>
      </VidMobStack>
    </VidMobBox>
  );
};

export default IndividualCreativeView;
