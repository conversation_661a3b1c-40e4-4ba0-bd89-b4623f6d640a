import React, { useEffect, useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { getOrganizationId } from '../../../../redux/selectors/partner.selectors';
import {
  VidMobBox,
  VidMobIconButton,
  VidMobStack,
  VidMobTooltip,
} from '../../../../vidMobComponentWrappers';
import InsightsHeader from './InsightsHeader';
import RemoveConfirmationDialog from './RemoveConfirmationDialog';
import InsightsViewAllModal from '../InsightsViewAllModal/InsightsViewAllModal';
import InsightDrawer from '../InsightsV2/components/InsightDetailsDrawer';
import { InsightDetails } from '../InsightsV2/insightsTypes';
import { CloseIcon } from '../../../../assets/vidmob-mui-icons/general';
import InsightCard from '../InsightCard/InsightCard';
import { useIntl } from 'react-intl';
import { INSIGHT_LOCATIONS } from '../../../../constants/insights.constants';
import { fetchProjectInsights } from '../InsightsV2/APIs/fetchProjectInsights';
import { removeInsightFromProject } from '../InsightsV2/APIs/removeInsightsFromProject';
import vmErrorLog from '../../../../utils/vmErrorLog';
import { useToastAlert } from '../../../__pages/SavedReports/SavedReportsCustomHooks';
import { GridRowId } from '@mui/x-data-grid-pro';
import { deleteInsightsToCreateProject } from '../../helpers/createProjectFromInsightsHelpers';

const { PROJECT_BRIEF_APPLIED } = INSIGHT_LOCATIONS;

interface ProjectBriefInsightsSectionProps {
  projectId: number;
  isInProjectCreate?: boolean;
}

const ProjectBriefInsightsSection: React.FC<
  ProjectBriefInsightsSectionProps
> = ({ projectId, isInProjectCreate = false }) => {
  const queryClient = useQueryClient();
  const intl = useIntl();
  const showToastAlert = useToastAlert();
  const organizationId = useSelector(getOrganizationId);

  const { data: insights = [] } = useQuery(['projectInsights', projectId], () =>
    fetchProjectInsights(projectId, organizationId),
  );

  const [selectedInsightId, setSelectedInsightId] = useState<GridRowId | null>(
    null,
  );

  const [insightToRemove, setInsightToRemove] = useState<InsightDetails | null>(
    null,
  );
  const [isViewAllModalOpen, setIsViewAllModalOpen] = useState<boolean>(false);

  const removeMutation = useMutation(removeInsightFromProject, {
    onSuccess: () =>
      queryClient.invalidateQueries(['projectInsights', projectId]),
    onError: (error) => {
      showToastAlert('ui.brief.projectInsights.remove.error', 'error');
      vmErrorLog(
        error as Error,
        'error removing insight from project',
        'ProjectBriefInsightsSection',
      );
    },
  });

  const handleSelectInsight = (insightId: GridRowId) => {
    setSelectedInsightId(insightId);
  };

  const handleConfirmRemove = () => {
    if (!insightToRemove) return;
    removeMutation.mutate({
      projectId,
      insightId: insightToRemove.id,
      organizationId,
    });
    setInsightToRemove(null);
  };

  const handleCancelRemove = () => {
    setInsightToRemove(null);
  };

  useEffect(() => {
    deleteInsightsToCreateProject();
  }, []);

  return (
    <>
      <VidMobBox
        margin="0 24px"
        minWidth="500px"
        flexDirection="column"
        sx={{ paddingBottom: '92px' }}
      >
        <InsightsHeader
          isInProjectCreate={isInProjectCreate}
          onViewAllInsights={() => setIsViewAllModalOpen(true)}
        />
        {insights.length > 0 && (
          <VidMobBox minWidth="500px" flexDirection="column">
            <VidMobStack gap={6}>
              {insights.map((insight) => (
                <InsightCard
                  key={insight.id}
                  shouldShowNormativeData
                  insight={insight}
                  insightLocation={PROJECT_BRIEF_APPLIED}
                  actionMenuComponent={
                    <VidMobTooltip
                      title={intl.formatMessage({
                        id: 'ui.brief.projectInsights.menu.remove.label',
                        defaultMessage: 'Remove insight',
                      })}
                    >
                      <span>
                        <VidMobIconButton
                          color="inherit"
                          size="small"
                          onClick={() => setInsightToRemove(insight)}
                        >
                          <CloseIcon />
                        </VidMobIconButton>
                      </span>
                    </VidMobTooltip>
                  }
                  onInsightCardClick={() => handleSelectInsight(insight.id)}
                />
              ))}
            </VidMobStack>
          </VidMobBox>
        )}
        {selectedInsightId && (
          <InsightDrawer
            insightId={selectedInsightId}
            organizationId={organizationId}
            onClose={() => setSelectedInsightId(null)}
            setAddToProjectState={() => {}}
            projectId={projectId}
          />
        )}
      </VidMobBox>
      <InsightsViewAllModal
        open={isViewAllModalOpen}
        onClose={() => setIsViewAllModalOpen(false)}
        organizationId={organizationId}
        selectedProjectId={projectId}
      />
      <RemoveConfirmationDialog
        isOpen={Boolean(insightToRemove)}
        onClose={handleCancelRemove}
        onConfirm={handleConfirmRemove}
        isSubmitting={removeMutation.isLoading}
      />
    </>
  );
};

export default ProjectBriefInsightsSection;
