import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useQueryClient } from '@tanstack/react-query';

import CustomDialog from '../../../../muiCustomComponents/CustomDialog/CustomDialog';
import { VidMobBox } from '../../../../vidMobComponentWrappers';
import {
  getIntlInsightTypeLabel,
  INSIGHT_TYPE_FOR_SORTING,
  INSIGHTS_SORT_BY_OPTIONS,
} from '../InsightsV2/insightsConstants';
import { useInfiniteInsightsQuery } from '../InsightsV2/hooks/useInsightsQuery';
import useInsightAddToProjectSubmit from '../InsightsV2/components/insightAddToProjectModal/useInsightAddToProjectSubmit';
import { InsightsFilters } from './InsightFilters';
import { InsightsList } from './InsightList';

interface InsightsViewAllModalProps {
  open: boolean;
  onClose: () => void;
  organizationId: string;
  selectedProjectId: number;
}

const InsightsViewAllModal: React.FC<InsightsViewAllModalProps> = ({
  open,
  onClose,
  organizationId,
  selectedProjectId,
}) => {
  const intl = useIntl();
  const queryClient = useQueryClient();

  const [selectedInsightIds, setSelectedInsightIdsLocal] = useState<string[]>(
    [],
  );

  const [viewSelectedIndex, setViewSelectedIndex] = useState<number>(0);
  const [sortSelectedIndex, setSortSelectedIndex] = useState<number>(0);

  const viewOptions = Object.values(INSIGHT_TYPE_FOR_SORTING).map((type) => ({
    label: intl.formatMessage(getIntlInsightTypeLabel(type)),
    value: [type],
  }));

  const sortOptions = Object.values(INSIGHTS_SORT_BY_OPTIONS).map((option) => ({
    label: option.name,
    value: [option.id],
    sortBy: option.option.sortBy,
    sortOrder: option.option.order,
  }));

  const selectedType = viewOptions[viewSelectedIndex]?.value[0] || '';
  const filters = selectedType === 'ALL' ? undefined : { type: [selectedType] };

  const selectedSortOption = sortOptions[sortSelectedIndex] || {
    value: [''],
    sortBy: '',
    sortOrder: '',
  };

  const initialPaginationParams = { offset: 0, perPage: 20 };
  const requestParams = {
    paginationParams: initialPaginationParams,
    organizationId,
    filters,
    searchTerm: '',
    sortBy: selectedSortOption.sortBy,
    sortOrder: selectedSortOption.sortOrder,
    isFilteredInsightsView: true,
  };

  const {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    refetch,
    isFetchingNextPage,
  } = useInfiniteInsightsQuery(requestParams);

  const observerRef = useRef<IntersectionObserver | null>(null);

  const handleClose = () => {
    setSelectedInsightIdsLocal([]);
    onClose();
  };

  const lastElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (isLoading || isFetchingNextPage) return;
      if (observerRef.current) observerRef.current.disconnect();
      observerRef.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage();
        }
      });
      if (node) observerRef.current.observe(node);
    },
    [isLoading, isFetchingNextPage, fetchNextPage, hasNextPage],
  );

  const handleInsightSelect = (insightId: string) => {
    if (selectedInsightIds?.includes(insightId)) {
      setSelectedInsightIdsLocal(
        (prev) => prev?.filter((id) => id !== insightId) || [],
      );
    } else {
      setSelectedInsightIdsLocal((prev) => [...prev, insightId]);
    }
  };

  const projectIdForRefetch = selectedProjectId;
  const [isApplyLoading, setIsApplyLoading] = useState<boolean>(false);
  const { handleSubmit } = useInsightAddToProjectSubmit({
    onClose,
    setIsLoading: setIsApplyLoading,
    selectedProjects: [selectedProjectId],
    organizationId,
    selectedInsightIds,
    onSuccessCallback: () => {
      queryClient.invalidateQueries(['projectInsights', projectIdForRefetch]);
    },
  });

  useEffect(() => {
    if (!open) {
      setSelectedInsightIdsLocal([]);
    }
  }, [open]);

  return (
    <CustomDialog
      isOpen={open}
      onClose={handleClose}
      onSubmit={() => handleSubmit()}
      headerText={intl.formatMessage({
        id: 'ui.user.insightsLibrary.header.viewAll',
        defaultMessage: 'View All Insights',
      })}
      customDialogStyles={{ zIndex: 1000 }}
      bodyChildren={
        <VidMobBox>
          <InsightsFilters
            intl={intl}
            viewOptions={viewOptions}
            sortOptions={sortOptions}
            viewSelectedIndex={viewSelectedIndex}
            sortSelectedIndex={sortSelectedIndex}
            setViewSelectedIndex={(index: number) => {
              setViewSelectedIndex(index);
              setSelectedInsightIdsLocal([]);
            }}
            setSortSelectedIndex={(index: number) => {
              setSortSelectedIndex(index);
              setSelectedInsightIdsLocal([]);
            }}
            onChange={() => {
              setSelectedInsightIdsLocal([]);
            }}
          />
          <InsightsList
            intl={intl}
            data={data}
            isLoading={isLoading}
            isError={isError}
            refetch={refetch}
            isFetchingNextPage={isFetchingNextPage}
            lastElementRef={lastElementRef}
            selectedInsightIds={selectedInsightIds}
            onInsightSelect={handleInsightSelect}
            organizationId={organizationId}
            projectId={selectedProjectId}
          />
        </VidMobBox>
      }
      submitButtonLabel={intl.formatMessage({
        id: 'ui.brief.insightRecommendation.insightCard.linkButton.button.label',
        defaultMessage: 'Apply to Project',
      })}
      isSubmitting={isApplyLoading}
      explicitDialogWidth="600px"
      explicitDialogHeight="720px"
    />
  );
};

export default InsightsViewAllModal;
