import React, { useState } from 'react';
import {
  VidMobBox,
  VidMobCircularProgress,
  VidMobTypography,
  VidMobButton,
} from '../../../../vidMobComponentWrappers';
import InsightCard from '../InsightCard/InsightCard';
import { INSIGHT_LOCATIONS } from '../../../../constants/insights.constants';
import InsightDrawer from '../InsightsV2/components/InsightDetailsDrawer';
import { InsightDetails } from '../InsightsV2/insightsTypes';

interface InsightsListProps {
  intl: any;
  data: any;
  isLoading: boolean;
  isError: boolean;
  refetch: () => void;
  isFetchingNextPage: boolean;
  lastElementRef: (node: HTMLDivElement) => void;
  selectedInsightIds: string[];
  onInsightSelect: (id: string) => void;
  organizationId: string;
  projectId?: number;
}
const { VIEW_ALL_INSIGHTS_MODAL } = INSIGHT_LOCATIONS;

export const InsightsList: React.FC<InsightsListProps> = ({
  intl,
  data,
  isLoading,
  isError,
  refetch,
  isFetchingNextPage,
  lastElementRef,
  selectedInsightIds,
  onInsightSelect,
  organizationId,
  projectId,
}) => {
  const [selectedInsight, setSelectedInsight] = useState<InsightDetails | null>(
    null,
  );

  if (isLoading) {
    return (
      <VidMobBox
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="200px"
      >
        <VidMobCircularProgress />
      </VidMobBox>
    );
  }
  if (isError) {
    return (
      <VidMobBox
        display="flex"
        flexDirection="column"
        alignItems="center"
        height="200px"
      >
        <VidMobTypography variant="body1" mb={2}>
          {intl.formatMessage({
            id: 'dashboard.widget.insightsStream.event.getInsights.error',
            defaultMessage: 'Error loading insights.',
          })}
        </VidMobTypography>
        <VidMobButton onClick={refetch}>
          {intl.formatMessage({
            id: 'button.global.retry.label',
            defaultMessage: 'Retry',
          })}
        </VidMobButton>
      </VidMobBox>
    );
  }

  const handleInsightCardClick = (insight: any) => {
    setSelectedInsight(insight);
  };
  const totalInsightsCount =
    data?.pages.reduce(
      (count: number, page: any) => count + (page.data?.length || 0),
      0,
    ) || 0;

  if (totalInsightsCount === 0) {
    return (
      <VidMobBox
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="200px"
      >
        <VidMobTypography variant="body1">
          {intl.formatMessage({
            id: 'ui.user.insightsLibrary.blankState.noInsights.modal',
            defaultMessage: 'No insights available.',
          })}
        </VidMobTypography>
      </VidMobBox>
    );
  }
  return (
    <VidMobBox display="flex" flexDirection="column" gap={2}>
      {data.pages.map((page: any, pageIndex: number) =>
        page.data.map((insight: any, index: number) => {
          const isLastElement =
            pageIndex === data.pages.length - 1 &&
            index === page.data.length - 1;
          return (
            <VidMobBox
              key={insight.id}
              ref={isLastElement ? lastElementRef : null}
            >
              <InsightCard
                insight={insight}
                hasCheckbox
                isSelected={selectedInsightIds.includes(insight.id)}
                onCheckboxClick={() => onInsightSelect(insight.id)}
                insightLocation={VIEW_ALL_INSIGHTS_MODAL}
                onInsightCardClick={() => handleInsightCardClick(insight)}
              />
            </VidMobBox>
          );
        }),
      )}
      {isFetchingNextPage && (
        <VidMobBox
          display="flex"
          justifyContent="center"
          alignItems="center"
          mt={2}
        >
          <VidMobCircularProgress />
        </VidMobBox>
      )}
      {selectedInsight && (
        <InsightDrawer
          insightId={selectedInsight.id}
          organizationId={organizationId}
          onClose={() => setSelectedInsight(null)}
          projectId={projectId}
          setAddToProjectState={() => {}}
        />
      )}
    </VidMobBox>
  );
};
