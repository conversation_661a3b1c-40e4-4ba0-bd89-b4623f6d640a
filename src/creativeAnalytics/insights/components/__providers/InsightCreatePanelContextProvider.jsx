// external packages
import React, { createContext, useEffect, useState } from 'react';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
// custom hooks, utils, and classes
import { showLocalizedFormattedBar } from '../../../../utils/showConfirmationBar';
// slices
import insightsSlice, {
  getInitialState,
} from '../../../../redux/slices/creativeAnalytics/insights.slice';
// selectors
import {
  getActiveInsight,
  getIsInsightCreatePanelVisible,
  getSelectedRowsCreativeExamples,
} from '../../../../redux/selectors/creativeAnalytics/insights.selectors';
import {
  getIsSelectedRowsViewActive,
  getReportTableView,
} from '../../../../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';
import { getAudienceFilterSelections } from '../../../../redux/selectors/creativeAnalytics/audienceReport.selectors';
// constants
import { INSIGHT_CREATE_CONTEXT_NAME } from '../../../../constants/insights.constants';
import { COMPARE_REPORT_AVERAGE_KEY } from '../../../../constants/creativeAnalytics.constants';
import { GLOBALS } from '../../../../constants';
import { REPORT_TABLE_VIEW } from '../../../../constants/ci.constants';
import analyticsConfigurationSlice from '../../../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';
import { getCampaignReportFilterSelections } from '../../../../redux/selectors/creativeAnalytics/campaignReport.selectors';
import { getCustomCompareReport } from '../../../../redux/selectors/creativeAnalytics/reports.selectors';
import { useParams } from 'react-router-dom';
import { getOrganizationId } from '../../../../redux/selectors/partner.selectors';
import { getFilterOptions } from '../../../components/AnalyticsFilters/AnalyticsFiltersApiServices/getAdvancedFilterOptions';
import {
  AUDIENCE,
  CAMPAIGN,
} from '../../../__pages/ImpactReport/ImpactReportConstants';
import siteMap from '../../../../routing/siteMap';
import { matchPath } from 'react-router';
import { analyticsFilteringReduxKeys } from '../../../../featureServices/AnalyticsFiltering/AnalyticsFilteringConstants';
import { kpiFilterTypeId } from '../../../components/AnalyticsFilters/AnalyticsFiltersConstants';
import { getFilterParams } from '../../../components/AnalyticsFilters/AnalyticsFiltersApiServices/getFilterParams';
import { useAnalyticsFilters } from '../../../components/AnalyticsFilters/hooks/useAnalyticsFilters';

const { SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const {
  clearActiveInsight,
  onDeleteInsightInCreate,
  onPublishInsight,
  onSaveDraftInsight,
  updateInsightOnPanelClose,
  setCustomGroupColumnsCreativeExamples,
} = insightsSlice.actions;

export const InsightCreatePanelContext = createContext({});

export const InsightCreatePanelContextProvider = ({ children }) => {
  const dispatch = useDispatch();
  const organizationId = useSelector(getOrganizationId);
  const isInsightCreatePanelVisible = useSelector(
    getIsInsightCreatePanelVisible,
  );
  const activeInsight = useSelector(getActiveInsight);
  const activeInsightSavingStatus = activeInsight.insightSavingStatus;
  const selectedRowsCreativeExamples = useSelector(
    getSelectedRowsCreativeExamples,
  );
  const selectedAudiences = useSelector(getAudienceFilterSelections);
  const isSelectedRowsViewActive = useSelector(getIsSelectedRowsViewActive);
  const reportTableView = useSelector(getReportTableView);
  const campaignFilterSelections = useSelector(
    getCampaignReportFilterSelections,
  );
  const customCompareReport = useSelector(getCustomCompareReport);
  const { reportId: savedAnalyticsReportId } = useParams();
  const { analyticsFilters, breakdownBy } = useAnalyticsFilters();
  const globalFilters = analyticsFilters?.globalFilters;
  const analyticsStartDate = moment(globalFilters.startDate);
  const analyticsEndDate = moment(globalFilters.endDate);
  const selectedKpi = globalFilters.kpi.value;
  const adAccountIds = globalFilters.adAccounts?.value?.map(
    (adAccount) => adAccount.platformAccountId,
  );
  const channel = globalFilters.channel?.value;
  const workspaceIds = globalFilters.workspaces?.value?.map(
    (workspace) => workspace.id,
  );
  const [availableKpis, setAvailableKpis] = useState([]);
  const [insightInEdit, setInsightInEdit] = useState({ ...activeInsight });
  const [currentPageIndex, setCurrentPageIndex] = useState(0);

  const isInAudienceReport = breakdownBy === AUDIENCE;
  const isInCampaignReport = breakdownBy === CAMPAIGN;
  const isCustomComparisonView =
    // eslint-disable-next-line no-undef
    matchPath(location.pathname, {
      path: siteMap.creativeIntelligenceCustomCompareDeprecated,
    })?.isExact ||
    // eslint-disable-next-line no-undef
    matchPath(location.pathname, {
      path: siteMap.creativeIntelligenceCustomCompareNew,
    })?.isExact;
  // TODO: VID-830 - stop reading selected rows from redux
  const isCreativeExamplesPanel =
    isSelectedRowsViewActive &&
    [
      REPORT_TABLE_VIEW.ELEMENT.value,
      REPORT_TABLE_VIEW.DURATION?.value,
    ].includes(reportTableView.value);
  const isKPIReportTableView =
    reportTableView?.id === REPORT_TABLE_VIEW?.KPI.id;
  const isKPITableViewOnSelectedRowsView =
    isSelectedRowsViewActive && isKPIReportTableView;
  const shouldShowCustomGroupCreativeExamples =
    isKPITableViewOnSelectedRowsView && isCustomComparisonView;

  useEffect(() => {
    if (
      activeInsight.insightSavingStatus === SUCCESS &&
      isInsightCreatePanelVisible === false
    ) {
      dispatch(clearActiveInsight());
      setInsightInEdit(getInitialState().activeInsight);
    }
  });

  const getKpiList = async (filters) => {
    const params = getFilterParams(kpiFilterTypeId, organizationId, filters);
    const kpis = await getFilterOptions({ organizationId, params });
    return kpis;
  };

  useEffect(() => {
    if (isInsightCreatePanelVisible && analyticsFilters) {
      getKpiList(analyticsFilters).then((kpis) => {
        setAvailableKpis(kpis);
      });
    }
  }, [
    isInsightCreatePanelVisible,
    JSON.stringify(analyticsFilters),
    organizationId,
  ]);

  useEffect(() => {
    setInsightInEdit(activeInsight);
  }, [activeInsight]);

  useEffect(() => {
    if (activeInsightSavingStatus === SUCCESS) {
      setCurrentPageIndex(0);
    }
  }, [activeInsightSavingStatus]);

  useEffect(() => {
    if (Object.keys(selectedRowsCreativeExamples)?.length === 0) {
      setCurrentPageIndex(0);
    }
  }, [selectedRowsCreativeExamples]);

  useEffect(() => {
    if (!isInsightCreatePanelVisible) {
      return;
    }

    if (isCustomComparisonView && isKPITableViewOnSelectedRowsView) {
      dispatch(
        setCustomGroupColumnsCreativeExamples({
          customGroupColumns: customCompareReport.columnItems,
        }),
      );
    }

    const autoPopulateTimestamp = moment();
    const insightInEditCopy = {
      ...insightInEdit,
      selectedKpi,
      analyticsStartDate,
      analyticsEndDate,
      autoPopulateTimestamp,
      adAccountIds,
      platform: channel,
      savedAnalyticsReportId,
    };

    if (isInAudienceReport) {
      insightInEditCopy.audienceIds = Object.values(selectedAudiences)
        .filter((audience) => audience.id !== COMPARE_REPORT_AVERAGE_KEY)
        .map((audience) => audience.child?.id ?? audience.parent.id);
    }

    if (isInCampaignReport) {
      insightInEditCopy.campaignsInfo = Object.values(campaignFilterSelections)
        .filter((selection) => selection.id !== COMPARE_REPORT_AVERAGE_KEY)
        .map((selection) =>
          selection.campaigns.map((campaign) => ({
            id: campaign.id,
            name: campaign.originalName,
          })),
        )
        .flat();
    }

    insightInEditCopy[analyticsFilteringReduxKeys.analyticsStartDateKey] =
      moment(analyticsFilters.globalFilters.startDate);
    insightInEditCopy[analyticsFilteringReduxKeys.analyticsEndDateKey] = moment(
      analyticsFilters.globalFilters.endDate,
    );

    setInsightInEdit(insightInEditCopy);
  }, [isInsightCreatePanelVisible]);

  const updateInsightInEdit = (propertyKey, updatedValue) => {
    setInsightInEdit((prevInsightInEdit) => ({
      ...prevInsightInEdit,
      isEditing: true,
      selectedKpi,
      [propertyKey]: updatedValue,
    }));
  };

  const handleDeleteInsightInEdit = () => {
    dispatch(onDeleteInsightInCreate({ updatedInsight: insightInEdit }));
    const activeInsightInitialState = getInitialState().activeInsight;
    setInsightInEdit(activeInsightInitialState);
    setCurrentPageIndex(0);
    showLocalizedFormattedBar(
      'ui.user.insightCreatePanel.confirmation.delete.label',
    );
  };

  const handlePublishInsight = () => {
    dispatch(
      onPublishInsight({
        activeInsight: { ...activeInsight, ...insightInEdit },
        workspaceIds,
        analyticsFilters: analyticsFilters,
      }),
    );
  };

  const handleSaveDraftInsight = () => {
    dispatch(
      onSaveDraftInsight({
        activeInsight: { ...activeInsight, ...insightInEdit },
        workspaceIds,
        analyticsFilters,
      }),
    );
  };

  const onClosePanel = () => {
    dispatch(
      updateInsightOnPanelClose({
        activeInsight: { ...activeInsight, ...insightInEdit },
        workspaceIds,
      }),
    );
  };

  const updateSelectedPlacementNestingOption = (selectedOption) => {
    dispatch(
      analyticsConfigurationSlice.actions.setSelectedPlacementNestingOption({
        selectedOption,
      }),
    );
  };

  const isSaveDraftEnabled = insightInEdit.finding?.length > 0;
  const isSubmitDisabled = !insightInEdit.finding?.length;

  return (
    <InsightCreatePanelContext.Provider
      value={{
        currentPageIndex,
        setCurrentPageIndex,
        availableKpis,
        updateInsightInEdit,
        updateSelectedPlacementNestingOption,
        insightInEdit,
        isSaveDraftEnabled,
        isSubmitDisabled,
        isCreativeExamplesPanel,
        isKPITableViewOnSelectedRowsView,
        isKPIReportTableView,
        shouldShowCustomGroupCreativeExamples,
        onClosePanel,
        contextName: INSIGHT_CREATE_CONTEXT_NAME,
        handleDelete: handleDeleteInsightInEdit,
        handleSubmit: handlePublishInsight,
        handleSaveDraft: handleSaveDraftInsight,
        handleOnClickOutsideModal: onClosePanel,
      }}
    >
      {children}
    </InsightCreatePanelContext.Provider>
  );
};
