import { Dispatch, SetStateAction, ReactNode, JSX } from 'react';
import {
  GridColDef,
  GridRowId,
  GridSortDirection,
  GridSortModel,
} from '@mui/x-data-grid-pro';
import { Insight, MainViewType } from '../../../../types/insight.types';
import { FilterSelectedValueType } from '../../../../types/filters.types';
import { UserInfo } from '../../../__pages/ImpactReport/ImpactReportTypes';
import { IdAndName } from '../../../../types/common.types';
import {
  BrandCopilotMetaData,
  NormativeCopilotMetaData,
} from '../../../../insights-copilot/types/chatInsightType';
import { Currency } from '../../../../types/currency.types';
import { PaginationParams } from '../../../../types/pagination.types';
import { QueryKey } from '@tanstack/react-query';

export type InsightsColumnType = GridColDef & {
  hide: boolean;
  getActions?: (props: any) => ReactNode;
  icon?: (props: any) => JSX.Element;
};

export interface InsightsFiltersType {
  [key: string]: FilterSelectedValueType;
}

export interface DeleteState {
  isOpen: boolean;
  insightId: GridRowId | null;
  insightName: string | null;
}

export interface InsightsContextType {
  insights: Insight[];
  userId: number;
  currentWorkspaceId: number;
  organizationId: string;
  isOrgAdmin: boolean;
  deleteState: DeleteState;
  isInEditMode: boolean;
  isLoading: boolean;
  columns: InsightsColumnType[];
  isError: boolean;
  searchTerm: string;
  selectedInsights: { [id: string]: Insight };
  selectedInsightId: GridRowId | null;
  sortOption: GridSortModel;
  filters: InsightsFiltersType;
  totalInsightsToShow: number;
  isSelectedRowsView: boolean;
  showFinding: boolean;
  folderModalState: FolderModalState;
  currentFolderId: string | null;
  deleteFolderState: { isOpen: boolean; folder: FolderType | null };
  folderGrid?: FolderType[];
  folderTree?: FolderType[];

  totalFolderCount: number;
  selectedFolderIds: string[];
  folderMap: Record<string, FolderType>;
  moveToFolderState: MoveToFolderState;
  isFilteredView: boolean;
  mainView: MainViewType | null;
  addToProjectState: AddToProjectState;
  hasNextPage: boolean;
  insightCacheKey: QueryKey;
  folderGridCacheKey: QueryKey;

  fetchNextPageInsights?: () => void;
  fetchNextPage: () => void;
  setFoldersView: () => void;
  setAllView: () => void;
  setDraftView: () => void;
  setStarredView: () => void;
  setIsFilteredView: Dispatch<SetStateAction<boolean>>;
  setAddToProjectState: Dispatch<SetStateAction<AddToProjectState>>;
  setMoveToFolderState: Dispatch<SetStateAction<MoveToFolderState>>;
  setSelectedFolderIds: Dispatch<SetStateAction<string[]>>;
  setDeleteFolderState: Dispatch<
    SetStateAction<{ isOpen: boolean; folder: FolderType | null }>
  >;
  setCurrentFolderId: (folderId: string | null) => void;
  setFolderModalState: Dispatch<SetStateAction<FolderModalState>>;
  setShowFinding: Dispatch<SetStateAction<boolean>>;
  onToggleSelectedRowView: () => void;
  handleEditMode: (id: GridRowId) => void;
  setColumns: Dispatch<SetStateAction<InsightsColumnType[]>>;
  setIsInEditMode: Dispatch<SetStateAction<boolean>>;
  setSearchTerm: (searchTerm: string) => void;
  handleDispatchFilterChange: (value: {
    filterId: string;
    value: FilterSelectedValueType;
  }) => void;
  setSelectedInsightId: Dispatch<SetStateAction<GridRowId | null>>;
  setSelectedInsights: Dispatch<SetStateAction<{ [id: string]: Insight }>>;
  setPaginationModel: Dispatch<
    SetStateAction<{ pageSize: number; page: number }>
  >;
  setDeleteState: Dispatch<SetStateAction<DeleteState>>;
  setIsSelectedRowsView: Dispatch<SetStateAction<boolean>>;
  setSortOption: Dispatch<SetStateAction<GridSortModel>>;
}

export type InsightsFilterServerModel = { [key: string]: any };

export interface InsightDetails extends Insight {
  createdBy: UserInfo;
  workspaceIds: number[];
  industry: string;
  kpis: string[];
  startDate: string;
  endDate: string;
  copilotChatId: string;
  projects: IdAndName[];
  mediaTypes: string[];
  categories: string[];
  campaigns: string[];
  objectives: string[];
  placements: string[];
  reportId: string;
  reportType: string;
  brandFilters: BrandFilters;
  platformMediaIds: string[];
  analysis: any;
  audiences: string[];
  recommendation: string;
  normativeMetadata?: NormativeCopilotMetaData;
  copilotBrandMetadata?: BrandCopilotMetaData;
  brandAnalysis?: BrandCopilotMetaData;
  parentFolderId: string | null;
  brandPlatformMedia: {
    kpiName?: string;
    tagName?: string;
    key: string;
    platformMediaIds: string[];
    tagType?: string;
    parents?: string[];
  }[];
}

export interface BrandFilters {
  currency?: Currency;
}

export interface InsightCreatorType {
  id: number;
  displayName: string;
  photo: string | null;
}

export interface InsightKpiServerType {
  [key: string]: {
    id: string;
    platform: string;
    name: string;
    description: string;
    formula: string;
    variables: string;
    format: string;
    tagTimeRange: number | null;
    tagTimeRangeType: string | null;
    inverseHealth: boolean;
    sequence: number;
    types: string;
    isDefaultOn: boolean;
  }[];
}

export interface InsightObjectives {
  [key: string]: {
    id: number;
    name: string;
    values: { id: number; name: string }[];
  }[];
}

export interface InsightProjectType {
  id: string;
  name: string;
  description: string;
  status: number;
  allowNewMedia: boolean;
  dateCreated: string;
  totalOutputVideos: number;
  numberOfApprovedConceptsRequired: number;
  numberOfApprovedVidscriptsRequired: number;
  reportFlightDateBegin: string;
  reportFlightDateEnd: string;
  reportAnalysisPeriodBegin: string;
  reportAnalysisPeriodEnd: string;
  productType: string;
  hasOutputGroups: boolean;
  requiresIdeation: boolean;
  restrictConceptCreatorInformation: boolean;
  isManaged: boolean;
  categoryId: string;
  finalAssetsStatus: string;
  allowsBriefDocs: boolean;
  allowsSowDocs: boolean;
  totalIterations: number;
}

export interface AnalysisDataType {
  kpiName: string;
  kpiValue: number;
  kpiFormat: string;
  currency?: Currency;
  adAccountIds?: string[];
  workspaceIds?: number[];
  elements: {
    isSignificant: boolean;
    isHigherGood: boolean;
    type: string;
    parent: string;
    name: string;
    kpi: number;
    key: string;
    lift: string | number;
    impressions: number;
    assets: number;
  }[];
}

export enum DraggableType {
  INSIGHT = 'INSIGHT',
  FOLDER = 'FOLDER',
}

export interface CombinedDraggedItem {
  folderIds: string[];
  insightIds: string[];
  itemType: string;
}

export interface FolderType {
  id: string;
  name: string;
  description?: string;
  dateCreated?: string;
  dateUpdated?: string;
  parentFolder?: IdAndName;
  parentFolderId?: string;
  createdBy?: string;
  isFavorited?: boolean;
  countChildren?: number;
  children?: FolderType[];
  type?: DraggableType;
}

export interface FolderModalState {
  isOpen: boolean;
  folderToEdit: FolderType | null;
  parentFolderId: string | null;
  origin: FolderOrigin | null;
}

export interface FolderBreadcrumb {
  id: string | null;
  name: string;
}

export interface MoveToFolderState {
  isOpen: boolean;
  payload: { folderIds: string[]; insightIds: string[] };
  parentFolderId: string | null;
}

export enum FolderOrigin {
  MOVE = 'move',
}
export interface DeleteFolderState {
  isOpen: boolean;
  folder: FolderType | null;
}

export interface AddToProjectState {
  isOpen: boolean;
  insightIds: string[];
}

export type InsightsQueryParams = {
  paginationParams: PaginationParams;
  organizationId: string;
  workspaceId?: number;
  parentFolderId?: string | null;
  searchTerm?: string;
  filters?: InsightsFiltersType;
  sortBy?: string;
  sortOrder?: GridSortDirection;
  isFilteredInsightsView: boolean;
  favorited?: boolean;
};
