import { makeTitleCase } from '../../../__pages/ImpactReport/ImpactReportDataGrid/utils/adjustStringCase';
import { getIntl } from '../../../../utils/getIntl';

const intl = getIntl();

export const INSIGHT_ACTIONS = {
  EDIT: 'edit',
  PUBLISH: 'publish',
  DELETE: 'delete',
  MOVE_TO_FOLDER: 'moveToFolder',
  ADD_TO_PROJECT: 'addToProject',
  SHARE_INSIGHT: 'shareInsight',
};

export const INSIGHT_GRID_COLUMN_FIELDS = {
  TITLE: 'title',
  FINDING: 'finding',
  TYPE: 'type',
  DATE_CREATED: 'dateCreated',
  ACTIONS: 'actions',
  IS_FAVORITED: 'favorited',
  CHANNEL: 'channel',
  STATUS: 'status',
  MARKET: 'markets',
  BRAND: 'brands',
  CREATED_BY: 'createdBy',
  LOCATION: 'location',
};

export const INSIGHT_TYPE = {
  BRAND: 'BRAND',
  COPILOT_BRAND: 'COPILOT_BRAND',
  COPILOT_INDUSTRY: 'COPILOT_INDUSTRY',
};

export const CONTROLBAR_FILTERS_CHANNELS_INTL =
  'ui.analytics.reports.controlbar.filters.channels';
export const CONTROLBAR_FILTERS_DATE_CREATED_INTL =
  'ui.analytics.reports.controlbar.filters.dateCreated';
export const CONTROLBAR_FILTERS_DATE_UPDATED_INTL =
  'ui.analytics.reports.controlbar.filters.dateUpdated';
export const CONTROLBAR_FILTERS_REPORT_TYPE_INTL =
  'ui.analytics.reports.controlbar.filters.reportType';
export const CONTROLBAR_FILTERS_CREATED_BY_INTL =
  'ui.analytics.reports.controlbar.filters.createdBy';
export const CONTROLBAR_FILTERS_PLATFORM_AD_ACCOUNT_INTL =
  'ui.analytics.reports.controlbar.filters.platformAdAccount';
export const FETCH_PLATFORM_MEDIA_ERROR =
  'ui.user.insightsLibrary.insights.details.platform.error';
export const FETCH_INSIGHTS_DETAILS_ERROR =
  'ui.user.insightsLibrary.insights.details.error';
export const FETCH_GENERATED_INSIGHT_TITLE_ERROR =
  'ui.user.insights.modal.title.aiSuggestButton.error';
export const NUMBER_OF_INSIGHTS =
  'ui.analytics.reports.controlbar.filters.numberOfInsights';

export const DELETE_INSIGHT_CONFIRMATION_TITLE =
  'ui.user.insightsLibrary.insights.delete.confirmation.title';
export const DELETE_INSIGHT_CONFIRMATION_DESCRIPTION =
  'ui.user.insightsLibrary.insights.delete.confirmation.description';
export const DELETE_INSIGHT_CONFIRMATION_SUBMIT_LABEL =
  'button.global.delete.label';

export const INSIGHT_FINDING_INTL =
  'ui.user.insightCreatePanel.finding.sectionHeader.title';
export const INSIGHT_INTL = 'ui.user.insightCreatePanel.insight.title';
export const INSIGHT_RECOMMENDATION_INTL =
  'ui.user.insightCreatePanel.Recommendation.sectionHeader.title';
export const NO_RECOMMENDATION_INTL =
  'ui.user.insightLibrary.details.noRecommendation';
export const NO_FINDING_INTL = 'ui.user.insightLibrary.details.noFinding';
export const TOOLTIP_STARRED_INTL =
  'ui.user.insightLibrary.tooltip.insight.starred';
export const TOOLTIP_NOT_STARRED_INTL =
  'ui.user.insightLibrary.tooltip.insight.notStarred';
export const TOOLTIP_ADD_TO_PROJECT_INTL =
  'ui.user.insightLibrary.tooltip.insight.addToProject';
export const TOOLTIP_ADD_TO_PROJECT_DISABLED_INTL =
  'ui.user.insightLibrary.tooltip.insight.addToProject.disabled';
export const TOOLTIP_MORE_ACTIONS_INTL =
  'ui.user.insightLibrary.tooltip.insight.moreActions';
export const TOOLTIP_CLOSE_INTL =
  'ui.user.insightLibrary.tooltip.insight.close';
export const ADD_TO_PROJECT_DISABLED = 'ui.user.insightLibrary.tooltip.project';
export const NORMATIVE_CARD_INDUSTRY =
  'ui.user.insightDetailsModal.header.industry.label';
export const STAR_FOLDER_INTL =
  'ui.user.insightsLibrary.folder.menu.starFolder';
export const UNSTAR_FOLDER_INTL =
  'ui.user.insightsLibrary.folder.menu.unstarFolder';

export const INSIGHT_DETAIL_LABELS = {
  KPI: 'ui.user.insightsLibrary.details.kpi',
  DATE_RANGE: 'ui.user.insightsLibrary.details.dateRange',
  CHANNEL: 'ui.user.insightsLibrary.details.channel',
  AD_ACCOUNT: 'ui.user.insightsLibrary.details.adAccount',
  DATE_CREATED: 'ui.user.insightsLibrary.details.dateCreated',
  CREATED_BY: 'ui.user.insightsLibrary.details.createdBy',
  BRAND: 'ui.user.insightsLibrary.details.brand',
  MARKET: 'ui.user.insightsLibrary.details.market',
  PROJECTS: 'ui.user.insightsLibrary.details.projects',
  MEDIA_TYPE: 'ui.user.insightsLibrary.details.mediaType',
  STATUS: 'ui.user.insightsLibrary.details.status',
  CATEGORY: 'ui.user.insightsLibrary.details.category',
  AUDIENCE: 'ui.user.insightsLibrary.details.audience',
};

export const INSIGHT_EXAMPLES_LABELS = {
  NAME: 'ui.user.insightsLibrary.details.name',
  MEDIA_TYPE: 'ui.user.insightsLibrary.details.mediaType',
};

export const INSIGHT_STATUSES = {
  PUBLISHED: 'PUBLISHED',
  DRAFT: 'DRAFT',
};

export const INSIGHT_TABS_LABELS = {
  ANALYSIS: 'ui.user.insightsLibrary.details.analysis',
  DETAILS: 'ui.user.insightsLibrary.details.details',
  EXAMPLES: 'ui.user.insightsLibrary.details.examples',
};

export const INSIGHT_TABS_EMPTY_STATE = {
  EXAMPLES: {
    TITLE: 'ui.user.insightsLibrary.details.examples.emptyState.title',
    DESCRIPTION:
      'ui.user.insightsLibrary.details.examples.emptyState.description',
  },
  ANALYSIS: {
    TITLE: 'ui.user.insightsLibrary.details.analysis.emptyState.title',
    EL_DESCRIPTION:
      'ui.user.insightsLibrary.details.analysis.emptyState.el.description',
    KPI_DESCRIPTION:
      'ui.user.insightsLibrary.details.analysis.emptyState.kpi.description',
  },
};

export const INSIGHT_MESSAGES = {
  UPDATE_SUCCESS: 'ui.user.insightsLibrary.updateSuccess',
  UPDATE_ERROR: 'ui.user.insightsLibrary.updateError',
  PUBLISH_SUCCESS: 'ui.user.insightsLibrary.publishSuccess',
  PUBLISH_ERROR: 'ui.user.insightsLibrary.publishError',
  FAVORITE_ERROR: 'ui.user.insightsLibrary.favoriteError',
  DELETE_SUCCESS: 'ui.user.insightsLibrary.deleteSuccess',
  DELETE_ERROR: 'ui.user.insightsLibrary.deleteError',
};

export const INSIGHT_ACTIONS_MESSAGES = {
  noAccessTitle: 'ui.user.insightsLibrary.noAccess.title',
  noAccessDescription: 'ui.user.insightsLibrary.noAccess.description',
};

export const INSIGHTS_SORT_BY_ORDERS = {
  ASCENDING: 'asc' as const,
  DESCENDING: 'desc' as const,
};

export const INSIGHTS_SORT_BY_VALUES = {
  DATE_CREATED: 'dateCreated',
  FAVORITED: 'favorited',
};

export const INSIGHT_SORT_BY_NEWEST = 'NEWEST';
export const INSIGHT_SORT_BY_OLDEST = 'OLDEST';
export const INSIGHT_SORT_BY_FAVORITES = 'FAVORITE';

// the View All modal is being defaulted to the first item on this list so the order matters
export const INSIGHTS_SORT_BY_OPTIONS = {
  NEWEST: {
    name: 'Newest',
    id: INSIGHT_SORT_BY_NEWEST,
    option: {
      sortBy: INSIGHTS_SORT_BY_VALUES.DATE_CREATED,
      order: INSIGHTS_SORT_BY_ORDERS.DESCENDING,
    },
  },
  OLDEST: {
    name: 'Oldest',
    id: INSIGHT_SORT_BY_OLDEST,
    option: {
      sortBy: INSIGHTS_SORT_BY_VALUES.DATE_CREATED,
      order: INSIGHTS_SORT_BY_ORDERS.ASCENDING,
    },
  },
  FAVORITE: {
    name: 'Favorites',
    id: INSIGHT_SORT_BY_FAVORITES,
    option: {
      sortBy: INSIGHTS_SORT_BY_VALUES.FAVORITED,
      order: INSIGHTS_SORT_BY_ORDERS.DESCENDING,
    },
  },
};

export const defaultSortOption = {
  field: INSIGHTS_SORT_BY_VALUES.DATE_CREATED,
  sort: INSIGHTS_SORT_BY_ORDERS.DESCENDING,
};

// the View All modal is being defaulted to the first item on this list so the order matters
export const INSIGHT_TYPE_FOR_SORTING = {
  ALL: 'ALL',
  ...INSIGHT_TYPE,
};

export const getIntlInsightTypeLabel = (
  type: string,
): { id: string; defaultMessage: string } => {
  switch (type) {
    case INSIGHT_TYPE_FOR_SORTING.ALL:
      return {
        id: 'ui.user.insightsLibrary.type.all.label',
        defaultMessage: 'All',
      };
    case INSIGHT_TYPE_FOR_SORTING.BRAND:
      return {
        id: 'ui.user.insightsLibrary.type.brand.label',
        defaultMessage: 'Brand',
      };
    case INSIGHT_TYPE_FOR_SORTING.COPILOT_BRAND:
      return {
        id: 'ui.user.insightsLibrary.type.copilot_brand.label',
        defaultMessage: 'Brand via Maddie',
      };
    case INSIGHT_TYPE_FOR_SORTING.COPILOT_INDUSTRY:
      return {
        id: 'ui.user.insightsLibrary.type.copilot_industry.label',
        defaultMessage: 'Industry via Maddie',
      };
    default:
      return { id: type, defaultMessage: makeTitleCase(type) };
  }
};

export const INSIGHT_DETAILS_ANALYSIS = {
  ELEMENT: intl.messages[
    'ui.user.insightLibrary.details.analysis.element'
  ] as string,
  KPI_AVERAGE: intl.messages[
    'ui.user.insightLibrary.details.analysis.kpiAverage'
  ] as string,
  KPI: intl.messages['ui.user.insightLibrary.details.analysis.kpi'] as string,
  PERFORMANCE: intl.messages[
    'ui.user.insightLibrary.details.analysis.performance'
  ] as string,
  LIFT: intl.messages['ui.user.insightLibrary.details.analysis.lift'] as string,
  IMPRESSIONS: intl.messages[
    'ui.user.insightLibrary.details.analysis.impressions'
  ] as string,
  ASSETS: intl.messages[
    'ui.user.insightLibrary.details.analysis.assets'
  ] as string,
};

export const MAX_FOLDER_DEPTH = 10;

export const FOLDER_PER_PAGE = 12;
export const FOLDER_EXPANDED_PER_PAGE = 36;
export const INSIGHTS_EXPANDED_PER_PAGE = 50;
export const ALL_LABEL = 'Library';
