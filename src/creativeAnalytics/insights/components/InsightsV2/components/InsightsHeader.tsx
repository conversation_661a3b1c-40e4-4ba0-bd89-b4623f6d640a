import React from 'react';
import { useIntl } from 'react-intl';
import PageHeaderV2 from '../../../../../components/PageHeaderV2/PageHeaderV2';
import {
  VidMobButton,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { useInsights } from '../hooks/useInsights';
import { MAX_FOLDER_DEPTH } from '../insightsConstants';
import { getFolderDepth } from './Folders/folderUtils';

export const InsightsHeader = () => {
  const intl = useIntl();
  const { setFolderModalState, currentFolderId, folderTree } = useInsights();

  const openCreateModal = () => {
    setFolderModalState({
      isOpen: true,
      folderToEdit: null,
      parentFolderId: currentFolderId,
      origin: null,
    });
  };

  const currentDepth = currentFolderId
    ? getFolderDepth(currentFolderId, folderTree) || 1
    : 1;

  const wouldExceedDepthLimit = currentDepth + 1 > MAX_FOLDER_DEPTH;

  return (
    <VidMobStack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
    >
      <PageHeaderV2
        title={intl.formatMessage({
          id: 'ui.site.pageTitle.ci.insights.library.title',
          defaultMessage: 'Insights Library',
        })}
      />

      <VidMobTooltip
        title={
          wouldExceedDepthLimit
            ? intl.formatMessage(
                {
                  id: 'ui.user.insightsLibrary.folder.maxLimitDisabled',
                  defaultMessage:
                    'You’ve reached the limit of {maxDepth} nested folders.',
                },
                { maxDepth: MAX_FOLDER_DEPTH },
              )
            : undefined
        }
      >
        <span>
          <VidMobButton
            variant="contained"
            color="primary"
            sx={{ height: '36px', mr: '24px' }}
            onClick={openCreateModal}
            disabled={wouldExceedDepthLimit}
          >
            <VidMobTypography variant="subtitle2">
              {intl.formatMessage({
                id: 'ui.user.insightsLibrary.header.button.label',
                defaultMessage: 'Create folder',
              })}
            </VidMobTypography>
          </VidMobButton>
        </span>
      </VidMobTooltip>
    </VidMobStack>
  );
};
