export const INSIGHT_MODAL_INTL_KEYS = {
  CREATE_TITLE_MAX: 'ui.user.insights.modal.createTitle.maximized',
  CREATE_TITLE_MIN: 'ui.user.insights.modal.createTitle.minimized',
  EDIT_TITLE: 'ui.user.insights.modal.editTitle',
  SAVE_BUTTON: {
    DRAFT: 'ui.user.insights.modal.saveBtn.draft',
    PUBLIC: 'ui.user.insights.modal.saveBtn.public',
  },
  ADD_EXAMPLES_BTN: 'ui.user.insights.modal.addExamplesBtn',
  SAVE_TYPE_DROPDOWN: {
    DRAFT: {
      TITLE: 'ui.user.insights.modal.saveTypeDropdown.draft.title',
      DESCRIPTION: 'ui.user.insights.modal.saveTypeDropdown.draft.description',
    },
    PUBLIC: {
      TITLE: 'ui.user.insights.modal.saveTypeDropdown.public.title',
      DESCRIPTION: 'ui.user.insights.modal.saveTypeDropdown.public.description',
    },
  },
  FIELD_VALIDATION: {
    REQUIRED: 'ui.user.insights.modal.validation.required',
    DRAFT: 'ui.user.insights.modal.validation.draft',
  },
  FINDING: {
    LABEL: 'ui.user.insights.modal.finding.label',
    HELPER_TEXT: 'ui.user.insights.modal.finding.helperText',
  },
  RECOMMENDATION: {
    LABEL: 'ui.user.insights.modal.recommendation.label',
    HELPER_TEXT: 'ui.user.insights.modal.recommendation.helperText',
  },
  DISABLED_EDITOR_TOOLTIP: 'ui.user.insights.modal.disabledEditor.tooltip',
  TITLE: {
    LABEL: 'ui.user.insights.modal.title.label',
    SUB_LABEL: 'ui.user.insights.modal.title.sublabel',
    AI_SUGGEST_BUTTON: {
      ENABLED: 'ui.user.insights.modal.title.aiSuggestButton.enabled',
      DISABLED: 'ui.user.insights.modal.title.aiSuggestButton.disabled',
    },
  },
  ASSETS_SELECTOR: {
    TITLE: 'ui.user.insights.modal.assetsSelector.modal.title',
    COLUMNS: {
      ASSETS: 'ui.user.insights.modal.assetsSelector.modal.columns.assets',
      KPI: 'ui.user.insights.modal.assetsSelector.modal.columns.kpi',
      IMPRESSIONS:
        'ui.user.insights.modal.assetsSelector.modal.columns.impressions',
      CREATIVE_TYPE:
        'ui.user.insights.modal.assetsSelector.modal.columns.creativeType',
      CHECKBOX: 'ui.user.insights.modal.assetsSelector.modal.checkbox',
    },
    BTN: 'ui.user.insights.modal.assetsSelector.modal.btn',
  },
  CREATIVE_ELEMENTS_SECTION: {
    TITLE: 'ui.user.insights.modal.creativeElements.title',
    SUBTITLE: 'ui.user.insights.modal.creativeElements.subtitle',
  },
  KPI: {
    TITLE: 'ui.user.insights.modal.kpi.title',
    SUBTITLE: 'ui.user.insights.modal.kpi.subtitle',
  },
  RESET_CONFIRMATION: {
    TITLE: 'ui.user.insights.modal.resetConfirmation.title',
    DESCRIPTION: 'ui.user.insights.modal.resetConfirmation.description',
    BUTTONS: {
      CANCEL: 'ui.user.insights.modal.resetConfirmation.btn.cancel',
      CONFIRM: 'ui.user.insights.modal.resetConfirmation.btn.confirm',
    },
  },
  CHANGE_VIEW_CONFIRMATION: {
    TITLE: 'ui.user.insights.modal.resetConfirmation.title',
    DESCRIPTION: 'ui.user.insights.modal.changeView.description',
    BUTTONS: {
      CANCEL: 'ui.user.insights.modal.changeView.btn.cancel',
      CONFIRM: 'ui.user.insights.modal.resetConfirmation.btn.confirm',
    },
  },
  UNSELECT_CONFIRMATION: {
    TITLE: 'ui.user.insights.modal.unselectConfirmation.title',
    DESCRIPTION: 'ui.user.insights.modal.unselectConfirmation.description',
    BUTTONS: {
      CANCEL: 'ui.user.insights.modal.unselectConfirmation.btn.cancel',
      CONFIRM: 'ui.user.insights.modal.unselectConfirmation.btn.confirm',
    },
  },
  TOAST: {
    SUCCESS: 'ui.user.insights.modal.toast.success',
    PUBLISHED: 'ui.user.insightsLibrary.publishSuccess',
    ERROR: 'ui.user.insights.modal.toast.error',
  },
};

export const MAX_SELECTED_ASSETS = 25;
