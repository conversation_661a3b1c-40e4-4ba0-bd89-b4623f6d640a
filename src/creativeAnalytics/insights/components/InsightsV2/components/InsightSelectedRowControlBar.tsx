import React from 'react';
import {
  VidMob<PERSON>utton,
  VidMobDivider,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { useInsights } from '../hooks/useInsights';
import { useIntl } from 'react-intl';
import { panelFilterButtonSx } from '../../../../../components/ReportFilters/styles';
import {
  CreateIcon,
  FolderFilledIcon,
} from '../../../../../assets/vidmob-mui-icons/general';
import {
  INSIGHT_STATUSES,
  TOOLTIP_ADD_TO_PROJECT_DISABLED_INTL,
} from '../insightsConstants';

export const InsightSelectedRowControlBar = () => {
  const intl = useIntl();
  const {
    isSelectedRowsView,
    setSelectedInsights,
    onToggleSelectedRowView,
    selectedInsights,
    setIsSelectedRowsView,
    setSelectedFolderIds,
    selectedFolderIds,
    setMoveToFolderState,
    currentFolderId,
    setAddToProjectState,
  } = useInsights();

  const handleToggleView = () => {
    onToggleSelectedRowView();
  };

  const totalSelectedInsights = Object.keys(selectedInsights).length;
  const totalSelectedFolders = selectedFolderIds.length;
  const totalItemCount = totalSelectedInsights + totalSelectedFolders;

  const getAddToProjectTooltip = () => {
    if (totalSelectedFolders > 0) {
      return intl.formatMessage({
        id: 'ui.user.insightsLibrary.folder.addProjectDisabled',
        defaultMessage: 'Folders cannot be added to projects',
      });
    }

    const selectedInsightsToAdd = Object.values(selectedInsights);
    if (
      selectedInsightsToAdd &&
      selectedInsightsToAdd.find(
        (insight) => insight.status !== INSIGHT_STATUSES.PUBLISHED,
      )
    ) {
      return intl.formatMessage({
        id: TOOLTIP_ADD_TO_PROJECT_DISABLED_INTL,
        defaultMessage: 'Only published insights can be added to projects',
      });
    }
    return '';
  };

  const isAddToProjectDisabled =
    totalSelectedFolders > 0 ||
    Object.values(selectedInsights)[0]?.status !== INSIGHT_STATUSES.PUBLISHED;

  return (
    <VidMobStack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      flexWrap="nowrap"
      className="control-bar-wrapper"
      sx={{ width: '100%', backgroundColor: 'background.default' }}
    >
      <VidMobStack
        direction="row"
        alignItems="center"
        flexWrap="wrap"
        sx={{ gap: '8px' }}
      >
        <VidMobTypography variant="subtitle2">
          {intl.formatMessage(
            {
              id: 'ui.analytics.reports.controlbar.filters.numberOfSelectedItems',
              defaultMessage: '{itemCount} selected items',
            },
            { itemCount: totalItemCount },
          )}
        </VidMobTypography>
        <VidMobDivider
          orientation="vertical"
          sx={{
            height: '20px',
            borderWidth: '1px',
            margin: '0 8px',
          }}
        />
        <VidMobButton
          onClick={handleToggleView}
          variant="outlined"
          sx={{
            ...panelFilterButtonSx,
            padding: '6px 12px',
          }}
        >
          <VidMobTypography variant="subtitle2">
            {intl.formatMessage({
              id: isSelectedRowsView
                ? 'ui.user.insightsLibrary.header.viewAll'
                : 'ui.user.insightsLibrary.header.viewSelected',
              defaultMessage: isSelectedRowsView
                ? 'View all items'
                : 'View selection only',
            })}
          </VidMobTypography>
        </VidMobButton>
        <VidMobButton
          variant="outlined"
          startIcon={<FolderFilledIcon sx={{ color: 'icon.secondary' }} />}
          sx={{ ...panelFilterButtonSx, padding: '6px 12px' }}
          onClick={() =>
            setMoveToFolderState({
              isOpen: true,
              payload: {
                folderIds: selectedFolderIds,
                insightIds: Object.keys(selectedInsights),
              },
              parentFolderId: currentFolderId,
            })
          }
        >
          <VidMobTypography variant="subtitle2">
            {intl.formatMessage({
              id: 'ui.user.insightsLibrary.header.addToFolder',
              defaultMessage: 'Move to folder',
            })}
          </VidMobTypography>
        </VidMobButton>
        <VidMobTooltip
          title={getAddToProjectTooltip()}
          placement="top"
          componentsProps={{
            tooltip: {
              sx: { maxWidth: '224px', whiteSpace: 'normal' },
            },
          }}
        >
          <span>
            <VidMobButton
              variant="outlined"
              startIcon={
                <CreateIcon
                  sx={{
                    color: isAddToProjectDisabled
                      ? 'text.disabled'
                      : 'icon.secondary',
                  }}
                />
              }
              sx={{ ...panelFilterButtonSx, padding: '6px 12px' }}
              onClick={() =>
                setAddToProjectState({
                  isOpen: true,
                  insightIds: Object.keys(selectedInsights),
                })
              }
              disabled={isAddToProjectDisabled}
            >
              <VidMobTypography variant="subtitle2">
                {intl.formatMessage({
                  id: 'ui.user.insightsLibrary.header.addProject',
                  defaultMessage: 'Add to project',
                })}
              </VidMobTypography>
            </VidMobButton>
          </span>
        </VidMobTooltip>
        <VidMobButton
          sx={{ height: '32px' }}
          onClick={() => {
            setSelectedInsights({});
            setIsSelectedRowsView(false);
            setSelectedFolderIds([]);
          }}
        >
          <VidMobTypography variant="subtitle2">
            {intl.formatMessage({
              id: 'ui.user.insightsLibrary.header.clearSelection',
              defaultMessage: 'Clear',
            })}
          </VidMobTypography>
        </VidMobButton>
      </VidMobStack>
    </VidMobStack>
  );
};
