import React, { Ref, useState } from 'react';
import { Typo<PERSON>, Tooltip } from '@mui/material';
import { GridCellParams } from '@mui/x-data-grid-pro';
import ShimmerAndFadeLoadingState from '../../../../../../components/ShimmerAndFadeLoadingState';
import useHover from '../../../../../../hooks/useHover';
import {
  VidMobBox,
  VidMobCardMedia,
  VidMobStack,
} from '../../../../../../vidMobComponentWrappers';
import MediaPreview from '../../../../../components/MediaPreview/MediaPreview';

const cellSx = {
  width: '100%',
  alignItems: 'center',
  color: 'black',
};

const thumbnailContainerSx = {
  borderRadius: '4px',
};

const thumbnailSx = {
  width: 50,
  height: 40,
  borderRadius: '4px',
};

const thumbnailNotAvailableSx = {
  '.shimmer-and-fade-loading-state': {
    borderRadius: '4px',
  },
};

const nameContainerSx = {
  ml: '12px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
};

interface Props {
  cellData: GridCellParams;
}

export const InsightDetailsNameAndMediaCell = ({ cellData }: Props) => {
  const cellInfo = cellData?.row;
  const { media } = cellInfo;

  const [nameHoverRef] = useHover();
  const [isMediaPreviewOpen, setIsMediaPreviewOpen] = useState(false);

  const thumbnail = media?.thumbnails?.[0];

  const nameColumnCellThumbnail = (
    <VidMobBox sx={thumbnailContainerSx}>
      {thumbnail ? (
        <VidMobCardMedia
          onMouseEnter={() => setIsMediaPreviewOpen(true)}
          onMouseLeave={() => setIsMediaPreviewOpen(false)}
          image={thumbnail.url}
          sx={thumbnailSx}
        />
      ) : (
        <VidMobBox sx={thumbnailNotAvailableSx}>
          <ShimmerAndFadeLoadingState height="40px" width="50px" />
        </VidMobBox>
      )}
    </VidMobBox>
  );

  const nameSection = (
    <VidMobBox
      vidmobRef={nameHoverRef as Ref<unknown> | undefined}
      sx={nameContainerSx}
    >
      <Tooltip title={media.displayName || media.name} disableInteractive>
        <Typography variant="body2">
          {media.displayName || media.name}
        </Typography>
      </Tooltip>
    </VidMobBox>
  );

  return (
    <VidMobStack direction="row" sx={cellSx}>
      {nameColumnCellThumbnail}
      {nameSection}
      {isMediaPreviewOpen && (
        <MediaPreview mediaPreview={media} rightPosition={20} />
      )}
    </VidMobStack>
  );
};
