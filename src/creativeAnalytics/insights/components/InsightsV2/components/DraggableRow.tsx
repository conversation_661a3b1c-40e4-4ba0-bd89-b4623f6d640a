import React, { useEffect, useMemo } from 'react';
import { GridRow, GridRowProps } from '@mui/x-data-grid';
import { DraggableType } from '../insightsTypes';
import { useInsights } from '../hooks/useInsights';
import { useCustomDrag } from '../hooks/useCustomDrag';
import { handleDragStart, getDraggedPlatforms } from '../utils/dragUtils';
import { VidMobBox } from '../../../../../vidMobComponentWrappers';

function DraggableRow(props: GridRowProps) {
  const { row } = props;
  const {
    selectedInsights,
    setSelectedInsights,
    selectedFolderIds,
    setSelectedFolderIds,
  } = useInsights();

  const rowId = String(row?.id);
  const isRowSelected = Object.keys(selectedInsights).includes(rowId);

  const dragItem = useMemo(() => {
    if (isRowSelected) {
      return {
        folderIds: selectedFolderIds || [],
        insightIds: Object.keys(selectedInsights),
        itemType: DraggableType.INSIGHT,
        draggedName: row?.title || '',
        draggedPlatforms: getDraggedPlatforms({
          rowPlatform: row?.platform,
          selectedInsights,
        }),
      };
    } else {
      return {
        folderIds: [],
        insightIds: [rowId],
        itemType: DraggableType.INSIGHT,
        draggedName: row?.title || '',
        draggedPlatforms: row?.platform ? [row.platform] : [],
      };
    }
  }, [isRowSelected, row, selectedFolderIds, selectedInsights, rowId]);

  const [{ isDragging }, dragRef] = useCustomDrag({
    type: DraggableType.INSIGHT,
    item: () => {
      return dragItem;
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  useEffect(() => {
    if (isDragging && !isRowSelected) {
      setSelectedInsights({});
      setSelectedFolderIds([]);
    }
  }, [isDragging, isRowSelected]);

  const onMouseDown = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement)?.closest('.nodrag')) {
      e.preventDefault();
    }
  };

  return (
    <VidMobBox
      ref={dragRef}
      onDragOver={(e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
      }}
      onDragStart={handleDragStart}
      onMouseDown={onMouseDown}
      sx={{
        userSelect: 'none',
        cursor: isDragging ? 'grabbing !important' : 'pointer',
      }}
    >
      <GridRow {...props} />
    </VidMobBox>
  );
}

export default DraggableRow;
