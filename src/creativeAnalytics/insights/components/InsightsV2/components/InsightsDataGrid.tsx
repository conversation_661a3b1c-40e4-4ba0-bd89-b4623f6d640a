import React, { useEffect, useRef } from 'react';
import { GridRowSelectionModel, GridSortModel } from '@mui/x-data-grid';
import { DataGridPro } from '@mui/x-data-grid-pro';
import { useInsights } from '../hooks/useInsights';
import {
  convertInsightToRowType,
  convertToColumnVisibilityModel,
  getDataGridSx,
  hasActiveFilters,
} from '../insightUtils';
import { Insight } from '../../../../../types/insight.types';
import InsightDrawer from './InsightDetailsDrawer';
import { NoRowsOverlay } from '../InsightGridBlankStates';
import { CustomColumnMenu } from './cells/InsightCustomColumnMenu';
import DraggableRow from './DraggableRow';
import { AddToProjectState } from '../insightsTypes';
import { VidMobCheckbox } from '../../../../../vidMobComponentWrappers';

interface InsightsDataGridProps {
  setVirtualScrollerRef?: (ref: HTMLDivElement | null) => void;
}

export const InsightsDataGrid = ({
  setVirtualScrollerRef,
}: InsightsDataGridProps) => {
  const {
    insights,
    selectedInsights,
    setSelectedInsights,
    isSelectedRowsView,
    columns,
    setIsSelectedRowsView,
    setPaginationModel,
    organizationId,
    selectedInsightId,
    setSelectedInsightId,
    setIsInEditMode,
    isLoading,
    isInEditMode,
    isError,
    searchTerm,
    sortOption,
    setSortOption,
    hasNextPage,
    setAddToProjectState,
    fetchNextPageInsights,
    filters,
    mainView,
    folderGrid,
  } = useInsights();
  const gridRef = useRef<HTMLDivElement | null>(null);

  const loadMoreInsights = () => {
    if (isLoading || !hasNextPage) return;
    fetchNextPageInsights && fetchNextPageInsights();
  };

  useEffect(() => {
    if (!gridRef.current || !setVirtualScrollerRef) return;

    const observer = new MutationObserver(() => {
      const virtualScroller = gridRef.current!.querySelector(
        '.MuiDataGrid-virtualScroller',
      ) as HTMLDivElement | null;
      if (virtualScroller) {
        setVirtualScrollerRef(virtualScroller);
        observer.disconnect();
      }
    });

    observer.observe(gridRef.current, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, [setVirtualScrollerRef]);

  const handleSelectionChange = (newSelectionModel: GridRowSelectionModel) => {
    setSelectedInsights((prev) => {
      const newSelectedRows = newSelectionModel.reduce(
        (acc, id) => {
          const row = insights.find((insight) => insight.id === id);
          if (row) acc[id] = row;
          return acc;
        },
        {} as { [id: string]: Insight },
      );

      const persistentRows = Object.fromEntries(
        Object.entries(prev).filter(([id]) => newSelectionModel.includes(id)),
      );

      const updatedSelectedRows = { ...persistentRows, ...newSelectedRows };

      const allDeselected = Object.keys(updatedSelectedRows).length === 0;

      if (
        !allDeselected &&
        Object.keys(newSelectedRows)?.length === 0 &&
        isSelectedRowsView
      ) {
        setPaginationModel((prevPagination) => ({
          ...prevPagination,
          page: Math.max(prevPagination.page - 1, 0),
        }));
      }

      if (allDeselected && isSelectedRowsView) {
        setIsSelectedRowsView(false);
        setPaginationModel((prevPagination) => ({
          ...prevPagination,
          page: 0,
        }));
      }

      return updatedSelectedRows;
    });
  };

  const handleDataGridSortChange = (sortModel: GridSortModel) => {
    setSortOption(sortModel);
  };

  return (
    <>
      <DataGridPro
        sx={getDataGridSx(Boolean(insights?.length))}
        autoHeight={insights.length > 0 && insights.length < 20}
        ref={gridRef}
        rowHeight={68}
        disableColumnReorder
        rowReordering={false}
        sortingMode="server"
        sortModel={sortOption}
        onSortModelChange={handleDataGridSortChange}
        loading={isLoading}
        rows={isError ? [] : convertInsightToRowType(insights)}
        columns={columns}
        columnVisibilityModel={convertToColumnVisibilityModel(columns)}
        checkboxSelection
        hideFooter
        onRowClick={(row) => setSelectedInsightId(row.id)}
        disableRowSelectionOnClick
        onRowSelectionModelChange={handleSelectionChange}
        rowSelectionModel={Object.keys(selectedInsights)}
        keepNonExistentRowsSelected
        slots={{
          noRowsOverlay: () => (
            <NoRowsOverlay
              hasFiltersOrSearch={Boolean(
                searchTerm || hasActiveFilters(filters),
              )}
              mainView={mainView}
              folders={folderGrid}
            />
          ),
          columnMenu: CustomColumnMenu,
          row: DraggableRow,
          baseCheckbox: (props) => (
            <VidMobCheckbox {...props} className="nodrag" />
          ),
        }}
        onRowsScrollEnd={loadMoreInsights}
      />
      <InsightDrawer
        insightId={selectedInsightId}
        organizationId={organizationId}
        onClose={() => {
          setSelectedInsightId(null), setIsInEditMode(false);
        }}
        setAddToProjectState={(params: AddToProjectState) => {
          setAddToProjectState(params);
        }}
        setSelectedInsightId={setSelectedInsightId}
        isInEditMode={isInEditMode}
        setIsInEditMode={setIsInEditMode}
      />
    </>
  );
};
