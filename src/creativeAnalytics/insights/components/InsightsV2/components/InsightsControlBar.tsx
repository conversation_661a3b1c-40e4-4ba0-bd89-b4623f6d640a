import React from 'react';
import { VidMobStack } from '../../../../../vidMobComponentWrappers';
import FiltersControlBar from '../../../../../muiCustomComponents/ControlBarFilters/ControlBar';
import {
  LandingPageContext,
  LandingPageFilterId,
} from '../../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import {
  getChannelList,
  getInsightBrandList,
  getInsightCreatorList,
  getInsightKPIList,
  getInsightMarketList,
  getInsightObjectiveList,
  getInsightStatus,
  getInsightType,
} from '../../../../__pages/SavedReports/SavedReportsControlBar/utils';
import SavedReportsSearch from '../../../../__pages/SavedReports/SavedReportsSearch';
import { useInsights } from '../hooks/useInsights';
import { INSIGHT_CONTROL_BAR_FILTERS } from '../../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.constants';
import { useIntl } from 'react-intl';
import { getDefaultActiveFilters } from '../insightUtils';
import { useInsightBrands } from '../hooks/useInsightBrands';
import { useInsightStatus } from '../hooks/useInsightStatus';
import { useInsightCreators } from '../hooks/useInsightCreators';
import useInsightKpis from '../hooks/useInsightKpis';
import useInsightMarkets from '../hooks/useInsightMarkets';
import useInsightObjectives from '../hooks/useInsightObjectives';
import { useInsightType } from '../hooks/useInsightType';
import { useSelector } from 'react-redux';
import { getUserWorkspacesInCurrentOrganization } from '../../../../../redux/selectors/user.selectors';
import { Workspace } from '../../../../../types/workspace.types';

export const InsightControlBar = () => {
  const {
    searchTerm,
    setSearchTerm,
    filters,
    handleDispatchFilterChange,
    totalInsightsToShow,
    organizationId,
    mainView,
    currentFolderId,
  } = useInsights();
  const intl = useIntl();

  const workspaceList = useSelector(getUserWorkspacesInCurrentOrganization);

  const { data: insightKpis } = useInsightKpis({ filters, organizationId });
  const { data: insightBrands } = useInsightBrands({ organizationId });
  const { data: insightCreators } = useInsightCreators({ organizationId });
  const { data: insightObjectives } = useInsightObjectives({
    filters,
    organizationId,
  });
  const { data: insightMarkets } = useInsightMarkets({
    organizationId,
  });
  const { data: insightStatuses } = useInsightStatus({
    organizationId,
  });
  const { data: insightTypes } = useInsightType({
    organizationId,
  });

  return (
    <FiltersControlBar
      key={mainView || currentFolderId}
      totalCount={totalInsightsToShow}
      searchComponent={
        <VidMobStack direction="row" spacing={4} alignItems="center">
          <SavedReportsSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            isClearable
            autoFocus={false}
          />
        </VidMobStack>
      }
      defaultActiveFilters={getDefaultActiveFilters(filters)}
      filters={INSIGHT_CONTROL_BAR_FILTERS}
      filtersListItems={{
        [LandingPageFilterId.BRANDS]: async () =>
          getInsightBrandList(insightBrands),
        [LandingPageFilterId.CHANNELS]: getChannelList(),
        [LandingPageFilterId.CREATED_BY]: async () =>
          getInsightCreatorList(insightCreators),
        [LandingPageFilterId.INSIGHT_TYPE]: getInsightType(intl, insightTypes),
        [LandingPageFilterId.KPI]: getInsightKPIList(insightKpis),
        [LandingPageFilterId.MARKETS]: getInsightMarketList(insightMarkets),
        [LandingPageFilterId.OBJECTIVE]:
          getInsightObjectiveList(insightObjectives),
        [LandingPageFilterId.STATUS]: getInsightStatus(intl, insightStatuses),
        [LandingPageFilterId.WORKSPACES]: workspaceList.map(
          (workspace: Workspace) => ({
            id: workspace.id.toString(),
            label: workspace.name,
          }),
        ),
      }}
      handleDispatchFilterChange={handleDispatchFilterChange}
      savedFilterParams={filters}
      context={LandingPageContext.INSIGHTS_LIBRARY}
    />
  );
};
