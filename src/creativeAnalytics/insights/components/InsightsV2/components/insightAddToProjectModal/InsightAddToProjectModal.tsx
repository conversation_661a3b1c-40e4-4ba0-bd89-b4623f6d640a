import React, { use<PERSON>allback, useEffect, useState } from 'react';
import CustomDialog from '../../../../../../muiCustomComponents/CustomDialog';
import {
  VidMobBox,
  VidMobButton,
  VidMobCheckbox,
  VidMobCircularProgress,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import SavedReportsSearch from '../../../../../__pages/SavedReports/SavedReportsSearch';
import TablePagination from '../../../../../../muiCustomComponents/TablePagination';
import {
  DataGridPro,
  GridRowId,
  GridRowSelectionModel,
} from '@mui/x-data-grid-pro';
import {
  ADD_BUTTON_TEXT,
  COLUMNS,
  DATAGRID_STYLE,
  HEADER_TEXT,
  LOADER_ROWS,
  NEW_BUTTON_TEXT,
  PAGINATION_STYLE,
  PER_PAGE,
  SUBTITLE_TEXT,
} from './InsightAddToProjectModalConstant';
import { InsightProjectType } from '../../insightsTypes';
import { useNavigate } from 'react-router-dom-v5-compat';
import useInsightAddToProjectSubmit from './useInsightAddToProjectSubmit';
import InsightAddToProjectEmptyState from './InsightAddToProjectEmptyState';
import { useDispatch, useSelector } from 'react-redux';
import useInsightProjects from '../../hooks/useInsightProjects';
import {
  getCurrentPartnerId,
  getOrganizationId,
} from '../../../../../../redux/selectors/partner.selectors';
import vmErrorLog from '../../../../../../utils/vmErrorLog';
import siteMap from '../../../../../../routing/siteMap';
import { LinkOutFilledIcon } from '../../../../../../assets/vidmob-mui-icons/general';
import { generatePath } from 'react-router';
import projectCreateSlice from '../../../../../../redux/slices/projectCreate.slice';
import { NoSearchResultsOverlay } from '../../InsightGridBlankStates';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedInsightIds?: GridRowId[];
  handleCreateInsight?: () => Promise<string[]>;
};

const InsightAddToProjectModal = ({
  isOpen,
  onClose,
  selectedInsightIds,
  handleCreateInsight,
}: Props) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [count, setCount] = useState(0);
  const [filteredRows, setFilteredRows] = useState<InsightProjectType[]>([]);
  const [selectedProjects, setSelectedProjects] = useState<number[]>([]);
  const currentWorkspaceId = useSelector(getCurrentPartnerId);
  const organizationId = useSelector(getOrganizationId);

  const { data: insightProjects, isLoading: insightProjectsLoading } =
    useInsightProjects({
      workspaceId: currentWorkspaceId,
    });

  const { handleSubmit } = useInsightAddToProjectSubmit({
    onClose,
    setIsLoading,
    selectedProjects,
    organizationId,
    selectedInsightIds,
    handleCreateInsight,
  });

  const isEmpty =
    !insightProjectsLoading &&
    (!insightProjects || insightProjects.length === 0);

  const handleCreateNewProject = async () => {
    // Clear any leftover selected insights and any project currently being created
    dispatch(projectCreateSlice.actions.onDeleteInsightsToCreateProject());
    dispatch(projectCreateSlice.actions.reset());

    let insightIds = selectedInsightIds;
    if (!insightIds) {
      if (handleCreateInsight) {
        await handleCreateInsight()
          .then((createdInsights) => {
            insightIds = createdInsights;
          })
          .catch((error) => {
            vmErrorLog(
              error as Error,
              'error creating new insight',
              'insight add to project modal',
            );
          });
      }
    }

    if (!insightIds) {
      return;
    }

    const createProjectAction = projectCreateSlice.actions
      .onCreateProjectFromMultipleInsights as (payload: any) => any;
    dispatch(createProjectAction({ insightsToCreateProject: insightIds }));

    navigate(generatePath(siteMap.projectCreateV2));
  };

  const submitButtonLabel = useCallback(() => {
    if (selectedProjects.length) {
      return `${ADD_BUTTON_TEXT} to ${selectedProjects.length} ${selectedProjects.length > 1 ? 'projects' : 'project'}`;
    }
    return ADD_BUTTON_TEXT;
  }, [selectedProjects]);

  useEffect(() => {
    if (insightProjects) {
      const filtered = insightProjects.filter((project) =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredRows(filtered);
      setCount(filtered.length);
      setPage(0);
    }
  }, [searchTerm, insightProjects, isOpen]);

  const handleSelectionChange = (newSelectionModel: GridRowSelectionModel) => {
    const filteredSelection = newSelectionModel.filter(
      (d) => !d.toString().includes('loading'),
    );
    setSelectedProjects(filteredSelection as number[]);
  };

  useEffect(() => {
    if (!isOpen) {
      setSelectedProjects([]);
      setSearchTerm('');
      setIsLoading(false);
    }
  }, [isOpen]);

  const CustomHeader = () => {
    return (
      <VidMobStack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <VidMobTypography variant="h6">{HEADER_TEXT}</VidMobTypography>
        <VidMobButton
          size="small"
          variant="text"
          onClick={handleCreateNewProject}
          endIcon={<LinkOutFilledIcon />}
        >
          <VidMobTypography variant="subtitle2">
            {NEW_BUTTON_TEXT}
          </VidMobTypography>
        </VidMobButton>
      </VidMobStack>
    );
  };

  return (
    <CustomDialog
      customHeaderChildren={<CustomHeader />}
      submitButtonLabel={submitButtonLabel()}
      renderHeaderDivider={false}
      isSubmitButtonDisabled={selectedProjects.length === 0}
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={handleSubmit}
      explicitDialogWidth="500px"
      cancelButtonVariant="outlined"
      customContentStyles={{ padding: '0 24px' }}
      bodyChildren={
        <VidMobStack
          minHeight="490px"
          height="490px"
          sx={{ position: 'relative' }}
        >
          <VidMobTypography sx={{ pb: '32px' }} variant="body2">
            {SUBTITLE_TEXT}
          </VidMobTypography>
          <VidMobStack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            sx={PAGINATION_STYLE}
          >
            <SavedReportsSearch
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              boxSx={{ maxWidth: '260px', width: '100%' }}
            />
            <VidMobBox width="200px">
              <TablePagination
                count={count}
                page={page}
                rowsPerPage={PER_PAGE}
                onPageChange={(_, page) => setPage(page)}
              />
            </VidMobBox>
          </VidMobStack>
          {isEmpty ? (
            <InsightAddToProjectEmptyState />
          ) : (
            <VidMobBox
              sx={{ height: '100%', width: '100%', overflowX: 'hidden' }}
            >
              <DataGridPro
                sx={DATAGRID_STYLE}
                pagination
                paginationModel={{
                  pageSize: PER_PAGE,
                  page,
                }}
                rows={insightProjectsLoading ? LOADER_ROWS : filteredRows}
                columns={COLUMNS}
                checkboxSelection
                hideFooter
                disableRowSelectionOnClick
                onRowSelectionModelChange={handleSelectionChange}
                rowSelectionModel={selectedProjects}
                keepNonExistentRowsSelected
                rowHeight={60}
                slotProps={{
                  baseCheckbox: {
                    disableRipple: true,
                  },
                }}
                slots={{
                  baseCheckbox: VidMobCheckbox,
                  noRowsOverlay: () => (
                    <NoSearchResultsOverlay searchQuery={searchTerm} />
                  ),
                }}
              />
            </VidMobBox>
          )}
          {isLoading && (
            <VidMobStack
              justifyContent="center"
              alignItems="center"
              sx={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
              }}
            >
              <VidMobCircularProgress />
            </VidMobStack>
          )}
        </VidMobStack>
      }
    />
  );
};

export default InsightAddToProjectModal;
