import { GridSortDirection } from '@mui/x-data-grid-pro';
import { InsightsApiResponse } from '../../../../../../types/insight.types';
import { PaginationParams } from '../../../../../../types/pagination.types';
import { InsightsFiltersType } from '../../insightsTypes';
import { useInsightsFoldersQuery } from '../useFolderInsightsQuery';
import { QueryKey } from '@tanstack/react-query';

type InsightsQueryParams = {
  paginationParams: PaginationParams;
  organizationId: string;
  workspaceId?: number;
  parentFolderId?: string | null;
  searchTerm?: string;
  filters?: InsightsFiltersType;
  sortBy?: string;
  sortOrder?: GridSortDirection;
  isFilteredInsightsView: boolean;
  favorited?: boolean;
};

export type UseInsightsQueryReturn = {
  data?: InsightsApiResponse;
  isLoading: boolean;
  isError: boolean;
  fetchNextPage?: () => void;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
};

export function useCombinedInsightsQuery(
  params: InsightsQueryParams,
  cacheKey: QueryKey,
): UseInsightsQueryReturn {
  const insightsFolders = useInsightsFoldersQuery(params, cacheKey);

  return {
    data: insightsFolders.data,
    isLoading: insightsFolders.isLoading,
    isError: insightsFolders.isError,
    fetchNextPage: insightsFolders.fetchNextPage,
    hasNextPage: insightsFolders.hasNextPage,
    isFetchingNextPage: insightsFolders.isFetchingNextPage,
  };
}
