import { useQuery } from '@tanstack/react-query';
import BffService from '../../../../../../apiServices/BffService';
import { FolderType } from '../../insightsTypes';
import vmErrorLog from '../../../../../../utils/vmErrorLog';
import { useToastAlert } from '../../../../../__pages/SavedReports/SavedReportsCustomHooks';

export const getFolderTree = async (
  organizationId: string,
  workspaceId: number,
): Promise<FolderType[]> => {
  const url = `v1/organization/${organizationId}/workspace/${workspaceId}/insight-folders/hierarchy`;
  return await BffService.handleBffApiGet(url);
};

export const useFolderTreeQuery = (
  organizationId: string,
  workspaceId: number,
) => {
  const showToastAlert = useToastAlert();

  const queryKey = ['folderTree', organizationId, workspaceId];

  return useQuery<FolderType[]>(
    queryKey,
    () => getFolderTree(organizationId, workspaceId),
    {
      keepPreviousData: true,

      onError: (error: any) => {
        showToastAlert('ui.user.insightsLibrary.folderTree.error', 'error');
        vmErrorLog(
          error as Error,
          'error loading folder tree',
          'useFolderTreeQuery',
        );
      },
    },
  );
};
