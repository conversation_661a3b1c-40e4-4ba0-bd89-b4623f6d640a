import {
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from '@tanstack/react-query';
import BffService from '../../../../../../apiServices/BffService';
import { FolderType } from '../../insightsTypes';
import vmErrorLog from '../../../../../../utils/vmErrorLog';
import { BffResponsePagination } from '../../../../../../types/pagination.types';
import { useToastAlert } from '../../../../../__pages/SavedReports/SavedReportsCustomHooks';

export interface FoldersQueryResponse {
  data: FolderType[];
  pagination: BffResponsePagination;
}

export const getFolders = async (
  organizationId: string,
  workspaceId: number,
  parentFolderId: string | null | undefined,
  page: number,
  pageSize: number,
): Promise<FoldersQueryResponse> => {
  let url = `v1/organization/${organizationId}/workspace/${workspaceId}/insight-folders/list?perPage=${pageSize}&offset=${page * pageSize}`;
  if (parentFolderId) {
    url += `&parentFolderId=${parentFolderId}`;
  }
  return await BffService.handleBffApiGetWithPagination(url);
};

export const useFoldersQuery = (
  organizationId: string,
  workspaceId: number,
  folderGridCacheKey: QueryKey,
  parentFolderId?: string | null,
  pageSize: number = 12,
  reactQueryOptions?: UseInfiniteQueryOptions<FoldersQueryResponse, unknown>,
) => {
  const showToastAlert = useToastAlert();

  const enabled =
    reactQueryOptions?.enabled !== undefined ? reactQueryOptions.enabled : true;

  return useInfiniteQuery<FoldersQueryResponse>(
    folderGridCacheKey,
    ({ pageParam = 0 }) =>
      getFolders(
        organizationId,
        workspaceId,
        parentFolderId,
        pageParam,
        pageSize,
      ),
    {
      enabled,
      keepPreviousData: true,
      getNextPageParam: (lastPage) => {
        const { offset, totalSize, perPage } = lastPage.pagination;
        const currentPage = offset / perPage;
        const totalPages = Math.ceil(totalSize / perPage);
        return currentPage + 1 < totalPages ? currentPage + 1 : undefined;
      },
      onError: (err: unknown) => {
        showToastAlert('ui.user.insightsLibrary.folderGrid.error', 'error');
        vmErrorLog(
          err as Error,
          'error loading insight folders',
          'useFoldersQuery',
        );
      },
      ...reactQueryOptions,
    },
  );
};
