import {
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from '@tanstack/react-query';
import BffService from '../../../../../../apiServices/BffService';
import { FolderType } from '../../insightsTypes';
import vmErrorLog from '../../../../../../utils/vmErrorLog';
import { BffResponsePagination } from '../../../../../../types/pagination.types';
import { useToastAlert } from '../../../../../__pages/SavedReports/SavedReportsCustomHooks';

export interface FoldersQueryResponse {
  data: FolderType[];
  pagination: BffResponsePagination;
}

export const getFilteredFolders = async (
  organizationId: string,
  workspaceId: number,
  page: number,
  pageSize: number,
  favorited: boolean,
): Promise<FoldersQueryResponse> => {
  const body = {
    favorited,
  };

  const url = `v1/organization/${organizationId}/workspace/${workspaceId}/insight-folders/list-filtered?perPage=${pageSize}&offset=${
    page * pageSize
  }`;

  return await BffService.handleBffApiPostWithPagination(url, body);
};

export const useFilteredFoldersQuery = (
  organizationId: string,
  workspaceId: number,
  folderGridCacheKey: QueryKey,
  favorited: boolean,
  pageSize: number = 12,
  reactQueryOptions?: UseInfiniteQueryOptions<FoldersQueryResponse, unknown>,
) => {
  const showToastAlert = useToastAlert();

  const enabled =
    reactQueryOptions?.enabled !== undefined ? reactQueryOptions.enabled : true;

  return useInfiniteQuery<FoldersQueryResponse, unknown>(
    folderGridCacheKey,
    ({ pageParam = 0 }) =>
      getFilteredFolders(
        organizationId,
        workspaceId,
        pageParam,
        pageSize,
        favorited,
      ),
    {
      enabled,
      keepPreviousData: true,
      getNextPageParam: (lastPage) => {
        const { offset, totalSize, perPage } = lastPage.pagination;
        const currentPage = offset / perPage;
        const totalPages = Math.ceil(totalSize / perPage);
        return currentPage + 1 < totalPages ? currentPage + 1 : undefined;
      },
      onError: (error: unknown) => {
        showToastAlert('ui.user.insightsLibrary.folderGrid.error', 'error');
        vmErrorLog(
          error as Error,
          'error loading insight folders',
          'useFilteredFoldersQuery',
        );
      },
      ...reactQueryOptions,
    },
  );
};
