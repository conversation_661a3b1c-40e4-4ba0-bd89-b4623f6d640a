import { QueryKey, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToastAlert } from '../../../../../__pages/SavedReports/SavedReportsCustomHooks';
import BffService from '../../../../../../apiServices/BffService';
import vmErrorLog from '../../../../../../utils/vmErrorLog';
import { removeInsightsFromCache } from '../../utils/queryUtils';
import { MainViewType } from '../../../../../../types/insight.types';

export interface MoveInsightPayload {
  parentFolderId: string | null;
  insightIds: string[];
}

export const moveInsight = async (
  { payload }: { payload: MoveInsightPayload },
  organizationId: string,
  workspaceId: number,
) => {
  const url = `v1/organization/${organizationId}/workspace/${workspaceId}/insight-folders/move-insight`;
  return BffService.handleBffApiPost(url, payload);
};

export const useMoveInsightMutation = (
  organizationId: string,
  workspaceId: number,
  insightCacheKey: QueryKey,
  mainView: MainViewType | null,
) => {
  const queryClient = useQueryClient();
  const showToastAlert = useToastAlert();

  return useMutation(
    ({ payload }: { payload: MoveInsightPayload }) =>
      moveInsight({ payload }, organizationId, workspaceId),
    {
      onMutate: async () => {
        await queryClient.cancelQueries({
          queryKey: ['insightsFolders', organizationId, workspaceId],
        });
      },
      onSuccess: (_response, { payload }) => {
        if (
          mainView !== MainViewType.STARRED &&
          mainView !== MainViewType.DRAFT
        ) {
          removeInsightsFromCache(
            queryClient,
            insightCacheKey,
            payload.insightIds,
          );
        }
      },
      onSettled: () => {
        queryClient.invalidateQueries({
          queryKey: ['insightsFolders', organizationId, workspaceId],
        });
      },
      onError: (error) => {
        showToastAlert('ui.user.insightsLibrary.moveInsights.error', 'error');
        vmErrorLog(
          error as Error,
          'error moving insights',
          'useMoveInsightMutation',
        );
      },
    },
  );
};
