import { QueryKey, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToastAlert } from '../../../../../__pages/SavedReports/SavedReportsCustomHooks';
import BffService from '../../../../../../apiServices/BffService';
import vmErrorLog from '../../../../../../utils/vmErrorLog';
import { removeFoldersFromCache } from '../../utils/queryUtils';
import { MainViewType } from '../../../../../../types/insight.types';

export interface MoveToFolderPayload {
  parentFolderId?: string | null;
  insightFolderIds?: string[];
}

export const moveFolder = async (
  payload: MoveToFolderPayload,
  organizationId: string,
  workspaceId: number,
) => {
  const url = `v1/organization/${organizationId}/workspace/${workspaceId}/insight-folders/move-folder`;
  return await BffService.handleBffApiPost(url, payload);
};

export const useMoveFolderMutation = (
  organizationId: string,
  workspaceId: number,
  folderGridCacheKey: QueryKey,
  mainView: MainViewType | null,
) => {
  const queryClient = useQueryClient();
  const showToastAlert = useToastAlert();

  return useMutation(
    ({ payload }: { payload: MoveToFolderPayload }) =>
      moveFolder(payload, organizationId, workspaceId),
    {
      onMutate: async () => {
        await queryClient.cancelQueries([
          'folderTree',
          organizationId,
          workspaceId,
        ]);
        await queryClient.cancelQueries(folderGridCacheKey);
      },
      onSuccess: (_data, { payload }) => {
        if (!payload.insightFolderIds) {
          return;
        }
        if (
          mainView !== MainViewType.STARRED &&
          mainView !== MainViewType.DRAFT
        ) {
          removeFoldersFromCache(
            queryClient,
            folderGridCacheKey,
            payload.insightFolderIds,
          );
        }
      },
      onError: (error) => {
        showToastAlert('ui.user.insightsLibrary.moveFolder.error', 'error');
        vmErrorLog(
          error as Error,
          'error updating folder parent',
          'useMoveFolderMutation',
        );
      },
      onSettled: () => {
        queryClient.invalidateQueries({
          queryKey: ['insightsFolders', organizationId, workspaceId],
        });
        queryClient.invalidateQueries([
          'folderTree',
          organizationId,
          workspaceId,
        ]);
        queryClient.invalidateQueries(folderGridCacheKey);
      },
    },
  );
};
