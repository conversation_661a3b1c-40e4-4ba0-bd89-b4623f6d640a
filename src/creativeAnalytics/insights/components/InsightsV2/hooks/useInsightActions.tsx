import { GridRowId } from '@mui/x-data-grid-pro';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import BffService from '../../../../../apiServices/BffService';
import { INSIGHT_MESSAGES } from '../insightsConstants';
import vmErrorLog from '../../../../../utils/vmErrorLog';
import { useInsights } from './useInsights';
import { InsightDetails } from '../insightsTypes';
import { ToastAlertSnackbarType } from '../../../../../redux/slices/toastAlert.slice';
import { Insight } from '../../../../../types/insight.types';
import {
  removeInsightsFromCache,
  updateInsightInCache,
} from '../utils/queryUtils';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import { getUpdatedValues } from '../insightUtils';

interface useInsightActionsProps {
  id: GridRowId | null;
  organizationId: string;
  showToastAlert: (
    message: string,
    severity: ToastAlertSnackbarType,
    values?: Record<string, string | number | boolean | JSX.Element>,
  ) => void;
}

export const useInsightActions = ({
  id,
  organizationId,
  showToastAlert,
}: useInsightActionsProps) => {
  const queryClient = useQueryClient();
  const {
    setSelectedInsightId,
    selectedInsights,
    setSelectedInsights,
    insightCacheKey,
  } = useInsights();

  const isInsightCreateModalEnabled = getFeatureFlag(
    'isInsightCreateModalEnabled',
  );

  const updateSelectedInsight = (update: Partial<Insight>) => {
    if (!id || !selectedInsights[id]) return;

    setSelectedInsights((prev) => {
      const existing = prev[id];
      if (!existing) return prev;

      return {
        ...prev,
        [id]: {
          ...existing,
          ...update,
        },
      };
    });
  };

  const editMutation = useMutation({
    mutationFn: async (
      updateData: Partial<{
        title: string;
        finding?: string;
        recommendation?: string;
        publish?: boolean;
      }>,
    ) => {
      return await BffService.handleBffApiPatch(
        `v1/organization/${organizationId}/insight/${id}`,
        updateData,
      );
    },
    onMutate: async (updateData) => {
      await queryClient.cancelQueries(['insightDetails', id, organizationId]);

      const previousDetail = queryClient.getQueryData<InsightDetails>([
        'insightDetails',
        id,
        organizationId,
      ]);

      const previousSelected = id ? selectedInsights[id] : undefined;

      updateSelectedInsight(getUpdatedValues(updateData));

      if (previousDetail) {
        queryClient.setQueryData<InsightDetails>(
          ['insightDetails', id, organizationId],
          (oldData) => ({ ...oldData!, ...updateData }),
        );
      }

      return { previousDetail, previousSelected };
    },
    onSuccess: (_response, updateData) => {
      if (!id) return;
      updateInsightInCache(queryClient, insightCacheKey, {
        id: String(id),
        ...getUpdatedValues(updateData),
      } as Insight);
      if (isInsightCreateModalEnabled) {
        setSelectedInsightId(null);
      }
    },

    onError: (err, updateData, context) => {
      if (context?.previousDetail) {
        queryClient.setQueryData(
          ['insightDetails', id, organizationId],
          context.previousDetail,
        );
      }
      if (id && context?.previousSelected) {
        setSelectedInsights((prev) => ({
          ...prev,
          [id]: context.previousSelected as Insight,
        }));
      }

      showToastAlert(
        updateData.publish
          ? INSIGHT_MESSAGES.PUBLISH_ERROR
          : INSIGHT_MESSAGES.UPDATE_ERROR,
        'error',
      );
      vmErrorLog(err as Error, 'Error updating insight', 'useInsightActions');
    },
  });

  const favoriteMutation = useMutation({
    mutationFn: async (favorite: boolean) => {
      return await BffService.handleBffApiPost(
        `v1/organization/${organizationId}/insight/${id}/favorite`,
        { favorite },
      );
    },
    onMutate: async (favorite) => {
      await queryClient.cancelQueries(['insightDetails', id, organizationId]);

      const previousDetail = queryClient.getQueryData<InsightDetails>([
        'insightDetails',
        id,
        organizationId,
      ]);

      const previousSelected = id && selectedInsights[id];

      updateSelectedInsight({ favorited: favorite });

      if (previousDetail) {
        queryClient.setQueryData<InsightDetails>(
          ['insightDetails', id, organizationId],
          (oldData) => ({ ...oldData!, favorited: favorite }),
        );
      }

      updateInsightInCache(queryClient, insightCacheKey, {
        id: String(id),
        favorited: favorite,
      } as Insight);

      return { previousDetail, previousSelected };
    },
    onError: (err, favorite, context) => {
      if (context?.previousDetail) {
        queryClient.setQueryData(
          ['insightDetails', id, organizationId],
          context.previousDetail,
        );
      }
      if (id && context?.previousSelected) {
        if (selectedInsights[id]) {
          setSelectedInsights((prev) => ({
            ...prev,
            [String(id)]: context.previousSelected as Insight,
          }));
        }
        updateInsightInCache(queryClient, insightCacheKey, {
          id: String(id),
          favorited: !favorite,
        } as Insight);
      }

      showToastAlert(INSIGHT_MESSAGES.FAVORITE_ERROR, 'error');
      vmErrorLog(err as Error, 'Error favoriting insight', 'useInsightActions');
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: insightCacheKey,
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async () => {
      return await BffService.handleBffApiDelete(
        `v1/organization/${organizationId}/insight/${id}`,
      );
    },
    onMutate: async () => {
      const previousSelected = id ? selectedInsights[id] : undefined;

      if (id && previousSelected) {
        setSelectedInsights((prev) => {
          const updated = { ...prev };
          delete updated[id];
          return updated;
        });
      }

      return { previousSelected };
    },
    onSuccess: (_data, { insightName }: { insightName?: string }) => {
      if (!id) return;
      removeInsightsFromCache(queryClient, insightCacheKey, [String(id)]);
      showToastAlert(INSIGHT_MESSAGES.DELETE_SUCCESS, 'success', {
        insightName: insightName || 'Insight',
      });
    },
    onError: (err, _vars, context) => {
      if (id && context?.previousSelected) {
        setSelectedInsights((prev) => ({
          ...prev,
          [id]: context.previousSelected as Insight,
        }));
      }

      showToastAlert(INSIGHT_MESSAGES.DELETE_ERROR, 'error');
      vmErrorLog(err as Error, 'Error deleting insight', 'useInsightActions');
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: ['insightsFolders', organizationId],
      });
      queryClient.invalidateQueries({
        queryKey: ['insights', organizationId],
      });
      setSelectedInsightId(null);
    },
  });

  const handleEdit = async (
    updateData: Partial<{
      title: string;
      finding?: string;
      recommendation?: string;
      publish?: boolean;
    }>,
  ) => {
    if (!id) return;
    return editMutation.mutateAsync(updateData);
  };

  const handleFavorite = async (favorite: boolean) => {
    if (!id) return;
    return favoriteMutation.mutateAsync(favorite);
  };

  const handleDelete = async (insightName: string) => {
    if (!id) return;
    return deleteMutation.mutateAsync({ insightName });
  };

  return { handleEdit, handleDelete, handleFavorite };
};
