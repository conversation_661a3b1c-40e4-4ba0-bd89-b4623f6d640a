import { useInfiniteQuery } from '@tanstack/react-query';
import { PaginationParams } from '../../../../../types/pagination.types';
import BffService from '../../../../../apiServices/BffService';
import { InsightsApiResponse } from '../../../../../types/insight.types';
import {
  InsightsFilterServerModel,
  InsightsQueryParams,
} from '../insightsTypes';
import { GridSortDirection } from '@mui/x-data-grid-pro';
import { convertFiltersToServerModel } from '../insightUtils';

async function fetchFilteredInsights(params: {
  paginationParams: PaginationParams;
  organizationId: string;
  serverModelFilters: InsightsFilterServerModel;
  sortBy?: string;
  sortOrder?: GridSortDirection;
}): Promise<InsightsApiResponse> {
  const {
    paginationParams,
    organizationId,
    serverModelFilters,
    sortBy,
    sortOrder,
  } = params;
  return BffService.handleBffApiPostWithPagination(
    `v1/organization/${organizationId}/insight/all?perPage=${paginationParams.perPage}&offset=${paginationParams.offset}`,
    {
      ...serverModelFilters,
      organizationId,
      sortBy,
      sortOrder,
    },
  );
}

/**
 * This is a hook that is used currently only in projects to fetch *all* insights regardless of folder
 */
export const useInfiniteInsightsQuery = (
  requestParams: InsightsQueryParams,
) => {
  const {
    paginationParams,
    organizationId,
    filters,
    searchTerm,
    sortBy,
    sortOrder,
  } = requestParams;
  const serverModelFilters = convertFiltersToServerModel(filters, searchTerm);

  return useInfiniteQuery({
    queryKey: [
      'projectInsights',
      organizationId,
      serverModelFilters,
      sortBy,
      sortOrder,
    ],
    queryFn: async ({ pageParam = paginationParams.offset }) => {
      const { data } = await fetchFilteredInsights({
        paginationParams: { ...paginationParams, offset: pageParam },
        organizationId,
        serverModelFilters,
        sortBy,
        sortOrder,
      });
      const nextOffset = pageParam + paginationParams.perPage;
      const hasMore = data?.length === paginationParams.perPage;
      return {
        data,
        nextOffset,
        hasMore,
      };
    },
    getNextPageParam: (lastPage) =>
      lastPage.hasMore ? lastPage.nextOffset : undefined,
    refetchOnWindowFocus: false,
  });
};
