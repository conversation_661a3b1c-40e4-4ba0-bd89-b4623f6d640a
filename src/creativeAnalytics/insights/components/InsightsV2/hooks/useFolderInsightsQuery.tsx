import { QueryKey, useInfiniteQuery } from '@tanstack/react-query';
import BffService from '../../../../../apiServices/BffService';
import { InsightsApiResponse } from '../../../../../types/insight.types';
import { InsightsQueryParams } from '../insightsTypes';
import { convertFiltersToServerModel } from '../insightUtils';
import { useMemo } from 'react';

export function useInsightsFoldersQuery(
  {
    paginationParams,
    organizationId,
    workspaceId,
    parentFolderId,
    filters,
    searchTerm,
    sortBy,
    sortOrder,
    isFilteredInsightsView,
    favorited,
  }: InsightsQueryParams,
  cacheKey: QueryKey,
) {
  const serverModelFilters = convertFiltersToServerModel(filters, searchTerm);
  const { perPage } = paginationParams;

  async function fetchFiltered(offset: number) {
    const response = await BffService.handleBffApiPostWithPagination(
      `v2/organization/${organizationId}/insight/workspace/${workspaceId}/all/with-folder-permission?perPage=${perPage}&offset=${offset}`,
      {
        ...serverModelFilters,
        favorited,
        organizationId,
        sortBy,
        sortOrder,
        ...(parentFolderId ? { parentFolderId } : {}),
      },
    );
    const { data, pagination } = response;
    const nextOffset = offset + perPage;
    const hasMore = data?.length === perPage;
    return { data, nextOffset, hasMore, pagination };
  }

  async function fetchFolders(offset: number, perPageOverride?: number) {
    const url = `v1/organization/${organizationId}/workspace/${workspaceId}/insight-folders/insights`;
    const perPageValue = perPageOverride || perPage;

    const response = await BffService.handleBffApiGetWithPagination(url, {
      perPage: perPageValue,
      offset,
      sortBy,
      sortOrder,
      ...(parentFolderId ? { parentFolderId } : {}),
    });
    const { data, pagination } = response;
    const nextOffset = offset + perPageValue;
    const hasMore = data?.length === perPageValue;
    return { data, nextOffset, hasMore, pagination };
  }

  const result = useInfiniteQuery(
    cacheKey,
    async ({ pageParam = paginationParams.offset }) => {
      return isFilteredInsightsView
        ? fetchFiltered(pageParam)
        : fetchFolders(pageParam);
    },
    {
      getNextPageParam: (lastPage) =>
        lastPage.hasMore ? lastPage.nextOffset : undefined,
      refetchOnWindowFocus: true,
    },
  );

  const flattenedData = useMemo(() => {
    return result.data?.pages?.flatMap((page) => page.data) || [];
  }, [result.data]);

  const lastPage = result.data?.pages?.[result.data.pages.length - 1];

  const pagination = useMemo(() => {
    return (
      lastPage?.pagination ?? {
        totalSize: flattenedData.length,
        perPage,
        offset: result.data?.pageParams?.[result.data.pages.length - 1] ?? 0,
      }
    );
  }, [lastPage, flattenedData.length, perPage, result.data?.pageParams]);

  return {
    data: {
      data: flattenedData,
      pagination,
    } as InsightsApiResponse,
    isLoading: result.isLoading,
    isError: result.isError,
    fetchNextPage: result.fetchNextPage,
    hasNextPage: result.hasNextPage,
    isFetchingNextPage: result.isFetchingNextPage,
  };
}
