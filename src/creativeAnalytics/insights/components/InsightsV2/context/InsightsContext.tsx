import React, {
  createContext,
  useState,
  ReactNode,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import {
  getCurrentPartner,
  getOrganizationId,
} from '../../../../../redux/selectors/partner.selectors';
import { useSelector } from 'react-redux';
import { getIsOrganizationAdmin } from '../../../../../userManagement/redux/selectors/organization.selectors';
import { getCurrentUserId } from '../../../../../redux/selectors/user.selectors';
import { INSIGHT_V2_COLUMNS } from '../insightGridColumns';
import {
  FolderModalState,
  InsightsContextType,
  InsightsFiltersType,
  DeleteFolderState,
  MoveToFolderState,
  AddToProjectState,
} from '../insightsTypes';
import { Insight, MainViewType } from '../../../../../types/insight.types';
import { GridRowId, GridSortModel } from '@mui/x-data-grid-pro';
import {
  defaultSortOption,
  FOLDER_EXPANDED_PER_PAGE,
  FOLDER_PER_PAGE,
  INSIGHT_STATUSES,
  INSIGHTS_EXPANDED_PER_PAGE,
} from '../insightsConstants';
import { useFolderManager } from '../hooks/FoldersHooks/useFolderManager';
import { FilterSelectedValueType } from '../../../../../types/filters.types';
import { LandingPageFilterId } from '../../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import { useCombinedInsightsQuery } from '../hooks/FoldersHooks/useCombinedInsights';
import { QueryKey, useQueryClient } from '@tanstack/react-query';
import { useFolderGridCacheKey, useInsightCacheKey } from '../utils/cacheKeys';
import { useInsightUrlParamLocation } from '../hooks/useInsightUrlParams';

export const InsightsContext = createContext<InsightsContextType>(
  {} as InsightsContextType,
);

interface InsightsProviderProps {
  children: ReactNode;
}

export const InsightsProvider: React.FC<InsightsProviderProps> = ({
  children,
}) => {
  const isOrgAdmin = useSelector(getIsOrganizationAdmin);
  const userId = useSelector(getCurrentUserId);
  const organizationId = useSelector(getOrganizationId);
  const currentWorkspace = useSelector(getCurrentPartner);
  const queryClient = useQueryClient();

  const [searchTerm, setSearchTerm] = useState('');
  const [columns, setColumns] = useState(INSIGHT_V2_COLUMNS);
  const [filters, setFilters] = useState<InsightsFiltersType>({});
  const [showFinding, setShowFinding] = useState<boolean>(true);
  const [isSelectedRowsView, setIsSelectedRowsView] = useState<boolean>(false);
  const [selectedInsights, setSelectedInsights] = useState<{
    [id: string]: Insight;
  }>({});
  const [selectedFolderIds, setSelectedFolderIds] = useState<string[]>([]);
  const [isInEditMode, setIsInEditMode] = useState<boolean>(false);
  const [selectedInsightId, setSelectedInsightId] = useState<GridRowId | null>(
    null,
  );
  // toggle that helps decide which insight or folder api to call -- either the one at the 'level' or ones that can be filtered
  const [isFilteredView, setIsFilteredView] = useState<boolean>(false);

  const [deleteState, setDeleteState] = useState<{
    isOpen: boolean;
    insightId: GridRowId | null;
    insightName: string | null;
  }>({
    isOpen: false,
    insightId: null,
    insightName: null,
  });
  const [deleteFolderState, setDeleteFolderState] = useState<DeleteFolderState>(
    {
      isOpen: false,
      folder: null,
    },
  );
  const [moveToFolderState, setMoveToFolderState] = useState<MoveToFolderState>(
    {
      isOpen: false,
      payload: { folderIds: [], insightIds: [] },
      parentFolderId: null,
    },
  );
  const [addToProjectState, setAddToProjectState] = useState<AddToProjectState>(
    {
      isOpen: false,
      insightIds: [],
    },
  );

  const [sortOption, setSortOption] = useState<GridSortModel>([]);

  const paginationInitialState = {
    page: 0,
    pageSize: INSIGHTS_EXPANDED_PER_PAGE,
  };
  const [paginationModel, setPaginationModel] = useState(
    paginationInitialState,
  );

  const [folderModalState, setFolderModalState] = useState<FolderModalState>({
    isOpen: false,
    folderToEdit: null,
    parentFolderId: null,
    origin: null,
  });

  const { urlState, updateUrlState } = useInsightUrlParamLocation();

  const prevOrgIdRef = useRef<string | null>(organizationId);
  const prevWorkspaceIdRef = useRef<number | null>(currentWorkspace?.id);

  useEffect(() => {
    const orgChanged = prevOrgIdRef.current !== organizationId;
    const workspaceChanged =
      prevWorkspaceIdRef.current !== currentWorkspace?.id;

    if ((orgChanged || workspaceChanged) && urlState.folderId) {
      updateUrlState({ folderId: null, mainView: MainViewType.ALL });
    }

    prevOrgIdRef.current = organizationId;
    prevWorkspaceIdRef.current = currentWorkspace?.id;
  }, [organizationId, currentWorkspace?.id]);

  useEffect(() => {
    setPaginationModel(paginationInitialState);
  }, [filters, searchTerm]);

  const isStarredView = urlState.mainView === MainViewType.STARRED;
  const isFolderView = urlState.mainView === MainViewType.FOLDER;

  const currentSort = sortOption.length > 0 ? sortOption[0] : defaultSortOption;
  const folderPageSize =
    urlState.mainView === MainViewType.FOLDER
      ? FOLDER_EXPANDED_PER_PAGE
      : FOLDER_PER_PAGE;
  const insightCacheKey: QueryKey = useInsightCacheKey({
    organizationId,
    currentWorkspaceId: currentWorkspace?.id,
    currentFolderId: urlState.folderId,
    paginationModel,
    filters,
    searchTerm,
    sortOption,
    defaultSortOption,
    isFilteredInsightsView: isFilteredView,
    favorited: isStarredView,
  });

  const folderGridCacheKey: QueryKey = useFolderGridCacheKey({
    organizationId,
    workspaceId: currentWorkspace.id,
    selectedFolderId: urlState.folderId,
    pageSize: folderPageSize,
    favorited: isStarredView,
  });

  const {
    data: insightList,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    isError,
    fetchNextPage: fetchNextPageInsights,
  } = useCombinedInsightsQuery(
    {
      paginationParams: {
        offset: paginationModel.page * paginationModel.pageSize,
        perPage: paginationModel.pageSize,
      },
      searchTerm,
      filters,
      workspaceId: currentWorkspace.id,
      parentFolderId: urlState.folderId,
      organizationId,
      sortBy: currentSort.field,
      sortOrder: currentSort.sort,
      isFilteredInsightsView: isFilteredView,
      favorited: isStarredView,
    },
    insightCacheKey,
  );
  const insights = insightList?.data || [];
  const totalItems = insightList?.pagination?.totalSize || 0;

  const isFilteredFoldersView = () => isStarredView || isFolderView;

  const { folderGrid, folderTree, totalFolderCount, folderMap, fetchNextPage } =
    useFolderManager({
      selectedFolderId: urlState.folderId,
      organizationId,
      workspaceId: currentWorkspace?.id,
      pageSize: folderPageSize,
      folderGridCacheKey,
      isFilteredFoldersView: isFilteredFoldersView(),
      favorited: isStarredView,
    });

  const finalInsightList = useCallback(() => {
    if (
      isSelectedRowsView &&
      Object.keys(selectedInsights).length === 0 &&
      selectedFolderIds.length === 0
    ) {
      setIsSelectedRowsView(false);
      return [];
    }
    return isSelectedRowsView
      ? Object.values(selectedInsights).slice(
          paginationModel.page * paginationModel.pageSize,
          (paginationModel.page + 1) * paginationModel.pageSize,
        )
      : insights;
  }, [
    isSelectedRowsView,
    insights,
    selectedInsights,
    paginationModel,
    selectedFolderIds,
  ]);

  const finalFolderGrid = useCallback(() => {
    return isSelectedRowsView && selectedFolderIds
      ? folderGrid.filter((folder) => selectedFolderIds.includes(folder.id))
      : folderGrid;
  }, [isSelectedRowsView, folderGrid, selectedFolderIds]);

  const totalSelectedItems = Object.values(selectedInsights)?.length;
  const onToggleSelectedRowView = () => {
    setIsSelectedRowsView(!isSelectedRowsView);
    setPaginationModel((prevPagination) => ({
      ...prevPagination,
      page: 0,
    }));
  };

  const totalInsightsToShow = isSelectedRowsView
    ? totalSelectedItems
    : totalItems;

  const totalFoldersToShow = isSelectedRowsView
    ? selectedFolderIds.length
    : totalFolderCount;

  const handleEditMode = (id: GridRowId) => {
    setSelectedInsightId(id);
    setIsInEditMode(true);
  };

  const applySearchTerm = (searchTerm: string) => {
    setSearchTerm(searchTerm);
    setIsFilteredView(
      Boolean(Object.keys(filters).length > 0 || searchTerm || isStarredView),
    );
  };

  const handleDispatchFilterChange = (value: {
    filterId: string;
    value: FilterSelectedValueType;
  }) => {
    setFilters((prevFilters) => {
      const newFilters = { ...prevFilters };

      const val = value.value;
      const isEmptyArray = Array.isArray(val) && val.length === 0;
      const isFalsy = val === null || val === undefined;

      if (isEmptyArray || isFalsy) {
        delete newFilters[value.filterId];
      } else {
        newFilters[value.filterId] = val;
      }

      setIsFilteredView(
        Boolean(
          Object.keys(newFilters).length > 0 || searchTerm || isStarredView,
        ),
      );
      return newFilters;
    });
  };

  const resetStateOnViewChange = () => {
    queryClient.resetQueries(folderGridCacheKey);
    setIsSelectedRowsView(false);
    setSelectedInsights({});
    setSelectedFolderIds([]);
    setSelectedInsightId(null);
  };

  const setStarredView = () => {
    updateUrlState({ folderId: null, mainView: MainViewType.STARRED });
    setFilters({});
    setIsFilteredView(true);
    resetStateOnViewChange();
  };

  const setDraftView = () => {
    updateUrlState({ folderId: null, mainView: MainViewType.DRAFT });
    setFilters({ [LandingPageFilterId.STATUS]: [INSIGHT_STATUSES.DRAFT] });
    setIsFilteredView(true);
    resetStateOnViewChange();
  };

  const setAllView = () => {
    updateUrlState({ folderId: null, mainView: MainViewType.ALL });
    setFilters({});
    setIsFilteredView(false);
    resetStateOnViewChange();
  };

  const setFoldersView = () => {
    updateUrlState({ folderId: null, mainView: MainViewType.FOLDER });
    setFilters({});
    setIsFilteredView(false);
    resetStateOnViewChange();
  };

  const handleSelectedFolderId = (folderId: string | null) => {
    updateUrlState({ folderId, mainView: null });
    setFilters({});
    setIsFilteredView(false);
    resetStateOnViewChange();
  };

  return (
    <InsightsContext.Provider
      value={{
        currentWorkspaceId: currentWorkspace?.id,
        organizationId,
        columns,
        searchTerm,
        filters,
        userId,
        deleteState,
        isOrgAdmin,
        insights: finalInsightList(),
        isSelectedRowsView,
        totalInsightsToShow,
        isLoading: Boolean(isLoading || isFetchingNextPage),
        isError,
        isInEditMode,
        selectedInsightId,
        selectedInsights,
        sortOption,
        showFinding,
        folderModalState,
        currentFolderId: urlState.folderId,
        deleteFolderState,
        folderGrid: finalFolderGrid(),
        folderTree,
        totalFolderCount: totalFoldersToShow,
        selectedFolderIds,
        folderMap,
        moveToFolderState,
        isFilteredView,
        mainView: urlState.mainView,
        addToProjectState,
        hasNextPage: Boolean(hasNextPage),
        insightCacheKey,
        folderGridCacheKey,

        fetchNextPageInsights,
        fetchNextPage,
        setAddToProjectState,
        setIsFilteredView: setIsFilteredView,
        setMoveToFolderState,
        setSelectedFolderIds,
        setDeleteFolderState,
        setCurrentFolderId: handleSelectedFolderId,
        setFolderModalState,
        setShowFinding,
        setColumns,
        setSearchTerm: applySearchTerm,
        handleEditMode,
        setDraftView,
        setAllView,
        setFoldersView,
        setStarredView,
        handleDispatchFilterChange,
        onToggleSelectedRowView,
        setDeleteState,
        setIsInEditMode,
        setIsSelectedRowsView,
        setPaginationModel,
        setSelectedInsightId,
        setSelectedInsights,
        setSortOption,
      }}
    >
      {children}
    </InsightsContext.Provider>
  );
};
