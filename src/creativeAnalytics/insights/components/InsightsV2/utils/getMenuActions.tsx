import React from 'react';
import { MenuAction } from '../../../../../muiCustomComponents/MoreHorizontalDropdown/MoreHorizontalDropdown';
import { InsightStatus } from '../../../../../types/insight.types';
import { IntlShape } from 'react-intl';
import { INSIGHT_ACTIONS } from '../insightsConstants';
import { GridRowId } from '@mui/x-data-grid-pro';
import {
  AddToProjectState,
  DeleteState,
  MoveToFolderState,
} from '../insightsTypes';
import { DisabledTooltipContent } from './getDisabledTooltipContent';
import { ToastAlertSnackbarType } from '../../../../../redux/slices/toastAlert.slice';
import { generateShareLink } from './generateShareLink';

interface MenuActionProps {
  status: InsightStatus;
  intl: IntlShape;
  userId: number;
  isOrgAdmin: boolean;
  createdById: number;
  handlers: {
    handleEdit: (
      updateData: Partial<{
        finding: string;
        recommendation: string;
        publish: boolean;
      }>,
    ) => void;
    handleDelete: (insightName: string) => void;
  };
  handleEditMode: (id: GridRowId) => void;
  selectedInsightId: GridRowId | null;
  setDeleteState: (deleteState: DeleteState) => void;
  setMoveToFolderState: (moveToFolderState: MoveToFolderState) => void;
  currentFolderId: string | null;
  setAddToProjectState: (state: AddToProjectState) => void;
  insightName?: string;
  showToastAlert: (messageKey: string, type: ToastAlertSnackbarType) => void;
  currentWorkspaceId: number;
  organizationId: string;
}

export const getMenuActions = ({
  status,
  intl,
  userId,
  isOrgAdmin,
  createdById,
  handlers,
  handleEditMode,
  selectedInsightId,
  setDeleteState,
  setMoveToFolderState,
  currentFolderId,
  setAddToProjectState,
  insightName = '',
  showToastAlert,
  currentWorkspaceId,
  organizationId,
}: MenuActionProps): MenuAction[] => {
  const disabled = createdById !== userId && !isOrgAdmin;

  return [
    {
      id: INSIGHT_ACTIONS.EDIT,
      name: intl.formatMessage({
        id: 'ui.user.insightsLibrary.menu.edit',
        defaultMessage: 'Edit insight',
      }),
      onClick: () => selectedInsightId && handleEditMode(selectedInsightId),
      disabled,
      disabledTooltip: disabled ? <DisabledTooltipContent /> : undefined,
    },
    ...(status === InsightStatus.DRAFT
      ? [
          {
            id: INSIGHT_ACTIONS.PUBLISH,
            name: intl.formatMessage({
              id: 'ui.user.insightsLibrary.menu.publish',
              defaultMessage: 'Publish insight',
            }),
            onClick: () => handlers.handleEdit({ publish: true }),
            disabled,
            disabledTooltip: disabled ? <DisabledTooltipContent /> : undefined,
          },
        ]
      : []),
    {
      id: INSIGHT_ACTIONS.MOVE_TO_FOLDER,
      name: intl.formatMessage({
        id: 'ui.user.insightsLibrary.menu.move',
        defaultMessage: 'Move to folder',
      }),
      onClick: () => {
        setMoveToFolderState({
          isOpen: true,
          payload: {
            insightIds: selectedInsightId
              ? ([selectedInsightId] as string[])
              : [],
            folderIds: [],
          },
          parentFolderId: currentFolderId,
        });
      },
      disabled: false,
    },
    ...(status === InsightStatus.PUBLISHED
      ? [
          {
            id: INSIGHT_ACTIONS.SHARE_INSIGHT,
            name: intl.formatMessage({
              id: 'ui.user.insightsLibrary.manageInsight.share',
              defaultMessage: 'Share',
            }),
            onClick: () =>
              generateShareLink({
                insightId: selectedInsightId as string,
                workspaceId: String(currentWorkspaceId),
                organizationId,
                showToastAlert,
              }),
            disabled: false,
          },
          {
            id: INSIGHT_ACTIONS.ADD_TO_PROJECT,
            name: intl.formatMessage({
              id: 'ui.user.insightsLibrary.menu.project',
              defaultMessage: 'Add to project',
            }),
            onClick: () => {
              setAddToProjectState({
                isOpen: true,
                insightIds: selectedInsightId
                  ? ([selectedInsightId] as string[])
                  : [],
              });
            },
            disabled: false,
          },
        ]
      : []),
    {
      id: INSIGHT_ACTIONS.DELETE,
      name: intl.formatMessage({
        id: 'ui.user.insightsLibrary.manageInsight.delete',
        defaultMessage: 'Delete',
      }),
      onClick: () =>
        setDeleteState({
          isOpen: true,
          insightId: selectedInsightId,
          insightName: insightName,
        }),
      disabled,
      disabledTooltip: disabled ? <DisabledTooltipContent /> : undefined,
      textStyles: {
        color: 'error.main',
        opacity: disabled ? 0.8 : 1,
        typography: 'body2',
      },
    },
  ];
};
