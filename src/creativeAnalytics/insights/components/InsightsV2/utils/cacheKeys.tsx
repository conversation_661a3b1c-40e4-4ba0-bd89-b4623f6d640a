import { useMemo } from 'react';
import { QueryKey } from '@tanstack/react-query';
import { GridSortItem, GridSortModel } from '@mui/x-data-grid-pro';
import { convertFiltersToServerModel } from '../insightUtils';
import { InsightsFiltersType } from '../insightsTypes';

export interface FolderGridCacheKeyParams {
  organizationId: string;
  workspaceId: number;
  selectedFolderId: string | null;
  pageSize: number;
  favorited: boolean;
}

export const useFolderGridCacheKey = ({
  organizationId,
  workspaceId,
  selectedFolderId,
  pageSize,
  favorited,
}: FolderGridCacheKeyParams): QueryKey =>
  useMemo((): QueryKey => {
    const baseKey = [
      organizationId,
      workspaceId,
      selectedFolderId,
      pageSize,
      favorited,
    ];

    return ['folderGrid', ...baseKey];
  }, [organizationId, workspaceId, selectedFolderId, pageSize, favorited]);

export interface InsightCacheKeyParams {
  organizationId: string;
  currentWorkspaceId?: number;
  currentFolderId: string | null;
  paginationModel: { page: number; pageSize: number };
  filters: InsightsFiltersType;
  searchTerm: string;
  sortOption: GridSortModel;
  defaultSortOption: GridSortItem;
  isFilteredInsightsView: boolean;
  favorited: boolean;
}

export const useInsightCacheKey = ({
  organizationId,
  currentWorkspaceId,
  currentFolderId,
  paginationModel,
  filters,
  searchTerm,
  sortOption,
  defaultSortOption,
  isFilteredInsightsView,
  favorited,
}: InsightCacheKeyParams): QueryKey =>
  useMemo((): QueryKey => {
    const baseKey = [
      organizationId,
      currentWorkspaceId,
      currentFolderId,
      {
        offset: paginationModel.page * paginationModel.pageSize,
        perPage: paginationModel.pageSize,
      },
      convertFiltersToServerModel(filters, searchTerm),
      sortOption[0]?.field ?? defaultSortOption.field,
      sortOption[0]?.sort ?? defaultSortOption.sort,
      isFilteredInsightsView,
      favorited,
    ];

    return ['insightsFolders', ...baseKey];
  }, [
    organizationId,
    currentWorkspaceId,
    currentFolderId,
    paginationModel,
    filters,
    searchTerm,
    sortOption,
    defaultSortOption,
    isFilteredInsightsView,
    favorited,
  ]);
