import { InfiniteData, QueryClient, QueryKey } from '@tanstack/react-query';
import { Insight } from './dragUtils';
import { FoldersQueryResponse } from '../hooks/FoldersHooks/useFoldersQuery';
import { FolderType } from '../insightsTypes';

type InfiniteQueryData = {
  pages: { data: Insight[]; nextOffset: number; hasMore: boolean }[];
};

export const updateInsightInCache = (
  queryClient: QueryClient,
  queryKey: QueryKey,
  updatedInsight: Insight,
) => {
  queryClient.setQueryData<InfiniteQueryData>(queryKey, (oldData) => {
    if (!oldData?.pages) return oldData;

    return {
      ...oldData,
      pages: oldData.pages.map((page) => ({
        ...page,
        data: page.data.map((insight) =>
          insight.id === updatedInsight.id
            ? { ...insight, ...updatedInsight }
            : insight,
        ),
      })),
    };
  });
};

export const removeInsightsFromCache = (
  queryClient: QueryClient,
  queryKey: QueryKey,
  idsToRemove: string[],
) => {
  queryClient.setQueryData<InfiniteQueryData>(queryKey, (oldData) => {
    if (!oldData?.pages) return oldData;

    return {
      ...oldData,
      pages: oldData.pages.map((page) => ({
        ...page,
        data: page.data.filter((insight) => !idsToRemove.includes(insight.id)),
      })),
    };
  });
};

export const removeFoldersFromCache = (
  queryClient: QueryClient,
  key: QueryKey,
  folderIdsToRemove: string[],
): void => {
  queryClient.setQueryData<InfiniteData<FoldersQueryResponse>>(
    key,
    (oldData) => {
      if (!oldData) return oldData;
      const newPages = oldData.pages.map((page) => ({
        ...page,
        data: page.data.filter(
          (folder: FolderType) => !folderIdsToRemove.includes(folder.id),
        ),
      }));

      return {
        ...oldData,
        pages: newPages,
      };
    },
  );
};

export const updateFolderInCache = (
  queryClient: QueryClient,
  key: QueryKey,
  folderId: string,
  updatedData: Partial<FolderType>,
): void => {
  queryClient.setQueryData<InfiniteData<FoldersQueryResponse>>(
    key,
    (oldData) => {
      if (!oldData) return oldData;
      const updatedPages = oldData.pages.map((page) => ({
        ...page,
        data: page.data.map((folder: FolderType) =>
          folder.id === folderId ? { ...folder, ...updatedData } : folder,
        ),
      }));
      return {
        ...oldData,
        pages: updatedPages,
      };
    },
  );
};
