import { IntlShape } from 'react-intl';
import { InsightIcon } from '../../../../assets/vidmob-mui-icons/general';
import { GLOBALS } from '../../../../constants';
import { ColumnsDropdownType } from '../../../../muiCustomComponents/ColumnDropdown/types';
import { LandingPageFilterId } from '../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import { Insight, InsightStatus } from '../../../../types/insight.types';
import { INSIGHT_GRID_COLUMN_FIELDS } from './insightsConstants';
import {
  InsightsColumnType,
  InsightsFilterServerModel,
  InsightsFiltersType,
} from './insightsTypes';

const { EM_DASH_UNICODE } = GLOBALS;

export function convertToColumnVisibilityModel(columns: InsightsColumnType[]): {
  [key: string]: boolean;
} {
  return columns.reduce(
    (acc, item) => {
      acc[item.field] = !item.hide;
      return acc;
    },
    {} as { [key: string]: boolean },
  );
}

export const convertToColumnDropdownType = (
  columns: InsightsColumnType[],
  showFinding: boolean,
  intl: IntlShape,
): ColumnsDropdownType[] => {
  const columnsDropdown = [
    {
      field: INSIGHT_GRID_COLUMN_FIELDS.FINDING,
      headerName: intl.formatMessage({
        id: 'ui.user.insightsLibraryFilterPanel.grid.finding',
        defaultMessage: 'Finding',
      }),
      hide: !showFinding,
      hideable: true,
      icon: InsightIcon,
    },
    ...columns,
  ];

  return columnsDropdown
    .filter((column) => column.hideable)
    .map((column) => ({
      id: column.field,
      name: column.headerName,
      hide: column.hide,
      icon: column.icon,
    })) as ColumnsDropdownType[];
};

export function isNotEmpty(obj: any): boolean {
  if (obj === null || obj === undefined) {
    return false;
  }

  if (Array.isArray(obj)) {
    return obj.length > 0;
  }

  if (typeof obj === 'object') {
    return Object.keys(obj).length > 0;
  }

  return true;
}

export function convertDateToServerModel(dates: Date[]): {
  startDate?: string;
  endDate?: string;
} {
  if (!isNotEmpty(dates[0]) && !isNotEmpty(dates[1])) {
    return {};
  }

  return {
    startDate: dates[0].toISOString().split('T')[0],
    endDate: dates[1].toISOString().split('T')[0],
  };
}

export function convertFiltersToServerModel(
  filters?: InsightsFiltersType,
  searchTerm?: string,
): InsightsFilterServerModel {
  try {
    let serverFilters: InsightsFilterServerModel = {};

    if (!filters && !searchTerm) {
      return serverFilters;
    }

    if (searchTerm) {
      serverFilters.searchTerm = searchTerm;
    }

    if (!filters) {
      return serverFilters;
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (isNotEmpty(value)) {
        let finalId = key;
        let finalValue: any = value;

        switch (key) {
          case LandingPageFilterId.INSIGHT_TYPE: {
            finalId = 'type';
            finalValue = value
              .join()
              .replace('NORMATIVE', 'INDUSTRY')
              .split(',');
            break;
          }
          case LandingPageFilterId.WORKSPACES: {
            finalId = 'workspaceIds';
            finalValue = value.map((val) => Number(val));
            break;
          }
          case LandingPageFilterId.KPI: {
            finalId = 'kpiIds';
            finalValue = value;
            break;
          }
          case LandingPageFilterId.OBJECTIVE: {
            finalId = 'objectiveIds';
            finalValue = value
              .join(',')
              .split(',')
              .map((val) => Number(val));
            break;
          }
          case LandingPageFilterId.DATE_CREATED: {
            serverFilters = {
              ...serverFilters,
              ...convertDateToServerModel(value as Date[]),
            };
            return;
          }
          case LandingPageFilterId.BRANDS: {
            finalId = 'brandIds';
            break;
          }
          case LandingPageFilterId.CREATED_BY: {
            finalId = 'createdBy';
            break;
          }
          case LandingPageFilterId.MARKETS: {
            finalId = 'marketIds';
            break;
          }
          case LandingPageFilterId.CHANNELS: {
            finalId = 'platforms';
            break;
          }
        }

        serverFilters[finalId] = finalValue;
      }
    });

    return serverFilters;
  } catch (error) {
    return {};
  }
}

export function convertInsightToRowType(insights: Insight[]) {
  return insights.map((insight) => ({
    ...insight,
    id: insight.id,
    channel: insight.platform,
    favorited: insight.favorited,
    location: insight.parentFolder,
    dateCreated: insight.dateCreated
      ? new Date(insight.dateCreated).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: '2-digit',
        })
      : EM_DASH_UNICODE,
  }));
}

export const getDefaultActiveFilters = (filters: InsightsFiltersType) => {
  return Object.entries(filters)
    .map(([key, value]) => (value.length > 0 ? key : undefined))
    .filter((key) => key !== undefined) as LandingPageFilterId[];
};

interface HandleColumnsUpdateProps {
  updatedColumns: ColumnsDropdownType[];
  setShowFinding: React.Dispatch<React.SetStateAction<boolean>>;
  setColumns: React.Dispatch<React.SetStateAction<InsightsColumnType[]>>;
  showFinding: boolean;
}

export const handleColumnsUpdate = ({
  updatedColumns,
  setShowFinding,
  setColumns,
}: HandleColumnsUpdateProps) => {
  const findingOption = updatedColumns.find(
    (opt) => opt.id === INSIGHT_GRID_COLUMN_FIELDS.FINDING,
  );
  if (findingOption) {
    setShowFinding(!findingOption.hide);
  }

  setColumns((prevColumns) => {
    return prevColumns.map((col) => {
      const updatedOption = updatedColumns.find((opt) => opt.id === col.field);
      if (updatedOption) {
        return { ...col, hide: updatedOption.hide };
      }
      return col;
    });
  });
};

export const getDataGridSx = (hasInsights: boolean) => ({
  border: 'unset',
  height: hasInsights ? 'calc(100vh - 270px)' : '600px',
  minHeight: '600px',
  '& .MuiDataGrid-virtualScroller': {
    scrollbarWidth: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
  },
  '& .MuiDataGrid-cell': { outline: 'unset !important' },
  '& .MuiDataGrid-columnHeader': { outline: 'unset !important' },
  '& .MuiDataGrid-columnHeaderTitle': { fontWeight: 500 },
  '& .Mui-selected': { backgroundColor: 'transparent !important' },
  '& .MuiDataGrid-row:hover': {
    backgroundColor: '#EEEEEE !important',
  },
  '& .MuiDataGrid-row.Mui-selected:hover': {
    backgroundColor: '#EEEEEE !important',
  },
  '& .MuiDataGrid-columnHeaderCheckbox .MuiDataGrid-columnHeaderTitleContainer':
    {
      display: 'none',
    },
});

export const getUpdatedValues = (updatedData: {
  title?: string;
  finding?: string;
  recommendation?: string;
  publish?: boolean;
}) => {
  const { title, finding, publish, recommendation } = updatedData;
  return {
    ...(title ? { title } : {}),
    ...(finding ? { finding } : {}),
    ...(recommendation ? { recommendation } : {}),
    ...(publish ? { status: InsightStatus.PUBLISHED } : {}),
  };
};

export const hasActiveFilters = (filters: InsightsFiltersType): boolean => {
  if (!filters || Object.keys(filters).length === 0) {
    return false;
  }

  return Object.values(filters).some((filterValue) => {
    if (Array.isArray(filterValue)) {
      if (filterValue.length === 0) {
        return false;
      }

      if (filterValue.length === 2) {
        const [first, second] = filterValue;
        if (!first && !second) {
          return false;
        }
      }
      return true;
    }
    return false;
  });
};
