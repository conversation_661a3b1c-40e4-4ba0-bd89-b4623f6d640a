import React from 'react';
import { InsightsProvider } from './context/InsightsContext';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import InsightsLibraryFolders from './components/FoldersMainPage/InsightsLibraryFolders';
import { InsightModalProvider } from './components/InsightModal/context/InsightModalProvider';

const InsightsLibrary = () => {
  const isInsightCreateModalEnabled = getFeatureFlag(
    'isInsightCreateModalEnabled',
  );

  const WrappedContent = isInsightCreateModalEnabled ? (
    <InsightModalProvider>
      <InsightsLibraryFolders />
    </InsightModalProvider>
  ) : (
    <InsightsLibraryFolders />
  );

  return <InsightsProvider>{WrappedContent}</InsightsProvider>;
};

export default InsightsLibrary;
