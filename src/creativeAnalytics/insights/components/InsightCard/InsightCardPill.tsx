import React from 'react';
import {
  ARCHIVED_STATUSES,
  INSIGHT_USER_EVENTS,
  INSIGHTS_STATUSES,
} from '../../../../constants/insights.constants';
import classnames from 'classnames';
import { useIntl } from 'react-intl';
import { Typography } from '@mui/material';
import './InsightCardPill.scss';

const { DRAFT, SCHEDULED, ARCHIVED_DRAFT, ARCHIVED, ARCHIVED_SCHEDULED } =
  INSIGHTS_STATUSES;
const { LINKED_TO_PROJECT } = INSIGHT_USER_EVENTS;

interface Props {
  pillText: string;
}

export const InsightCardPill = ({ pillText }: Props) => {
  const intl = useIntl();

  const pillClassNames = classnames({
    'insight-card-pill': true,
    'insight-card-status-pill-default': [DRAFT, SCHEDULED].includes(pillText),
    'insight-card-status-pill-archived': ARCHIVED_STATUSES.includes(pillText),
    'insight-card-project-pill': pillText === LINKED_TO_PROJECT,
  });

  const pillLabels = {
    [DRAFT]: intl.messages['ui.user.insightsLibrary.status.draft.label'],
    [SCHEDULED]:
      intl.messages['ui.user.insightsLibrary.status.scheduled.label'],
    [ARCHIVED]: intl.messages['ui.user.insightsLibrary.status.archived.label'],
    [ARCHIVED_DRAFT]:
      intl.messages['ui.user.insightsLibrary.status.archived.label'],
    [ARCHIVED_SCHEDULED]:
      intl.messages['ui.user.insightsLibrary.status.archived.label'],
    [LINKED_TO_PROJECT]:
      intl.messages['ui.user.insightsLibrary.insightCard.projectPill.text'],
  };

  if (typeof pillLabels[pillText] !== 'string') {
    return null;
  }

  return (
    <Typography className={pillClassNames} variant="body2" component="div">
      {pillLabels[pillText] as string}
    </Typography>
  );
};
