@import '../../../../styles/_colors.scss';

.insight-card-pill {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 4px;
  height: 28px;
  border-radius: 0.75rem;
  flex: none;
  order: 0;
  flex-grow: 0;
  &.insight-card-status-pill-archived {
    background-color: $interactive-neutral-bg;
    color: $type-deemphasized;
  }
  &.insight-card-status-pill-default {
    background-color: $info-bg;
    color: $on-info-bg;
  }
  &.insight-card-project-pill {
    background-color: $info-bg;
    color: $on-info-bg;
    margin-right: 14px;
  }
}
