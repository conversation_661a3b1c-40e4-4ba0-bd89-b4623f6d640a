import React from 'react';
import { Checkbox } from '@mui/material';
import { InsightDetails } from '../InsightsV2/insightsTypes';
import { InsightCardPill } from './InsightCardPill';
import { BarChartIcon } from '../../../../assets/vidmob-mui-icons/general';
import {
  VidMobTooltip,
  VidMobBox,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { getPlatformIdentifierForLogo } from '../../../../utils/feConstantsUtils';
import { INSIGHT_LOCATIONS } from '../../../../constants/insights.constants';
import { InsightStatus } from '../../../../types/insight.types';
import { useIntl } from 'react-intl';
import { makeTitleCase } from '../../../__pages/ImpactReport/ImpactReportDataGrid/utils/adjustStringCase';
import { NORMATIVE_CARD_INDUSTRY } from '../InsightsV2/insightsConstants';

interface InsightCardProps {
  insight: InsightDetails;
  insightLocation: string;
  actionMenuComponent?: React.ReactNode;
  hasCheckbox?: boolean;
  hasStatusPill?: boolean;
  isSelected?: boolean;
  shouldShowNormativeData?: boolean;
  onCheckboxClick?: () => void;
  onInsightCardClick: () => void;
  disabled?: boolean;
}

const InsightCard: React.FC<InsightCardProps> = ({
  insight,
  insightLocation,
  actionMenuComponent,
  hasCheckbox = false,
  hasStatusPill = false,
  isSelected = false,
  shouldShowNormativeData = false,
  onCheckboxClick,
  onInsightCardClick,
  disabled = false,
}) => {
  const { title, type, platform, status, industry, normativeMetadata } =
    insight;
  const intl = useIntl();
  const shouldShowIndustryText = !!industry;
  const isInsightInLibrary =
    insightLocation === INSIGHT_LOCATIONS.INSIGHTS_LIBRARY;
  const showProjectPill =
    isInsightInLibrary && status === InsightStatus.PUBLISHED;
  const showStatusPill = hasStatusPill && status !== InsightStatus.PUBLISHED;

  const platformIdentifier = platform?.toUpperCase();
  const platformIconVDSIdentifier =
    getPlatformIdentifierForLogo(platformIdentifier);

  const shouldShowNormativeMetaData =
    shouldShowNormativeData && normativeMetadata;
  const normativeMetaDataText =
    shouldShowNormativeMetaData && normativeMetadata.industryName
      ? intl.formatMessage(
          {
            id: NORMATIVE_CARD_INDUSTRY,
            defaultMessage: 'Based on {industryName}',
          },
          {
            industryName: normativeMetadata.industryName,
          },
        )
      : null;

  return (
    <VidMobBox
      sx={{
        display: 'flex',
        p: '7px 16px 16px 7px',
        borderRadius: '8px',
        border: '1px solid',
        borderColor: isSelected ? 'primary.main' : 'divider',
        minWidth: '100%',
        backgroundColor: 'background.default',
      }}
    >
      {hasCheckbox && (
        <Checkbox
          sx={{ m: '3px 5px 0 0' }}
          checked={isSelected}
          onChange={onCheckboxClick}
          disabled={disabled}
        />
      )}
      <VidMobBox
        sx={{
          mt: '7px',
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
        }}
      >
        <VidMobBox
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: showProjectPill || showStatusPill ? '60%' : '80%',
          }}
        >
          <VidMobBox onClick={onInsightCardClick}>
            <VidMobTooltip title={title} variant="basic" position="above">
              <span>
                <VidMobTypography
                  sx={{
                    cursor: 'pointer',
                    mb: 1,
                    '&:hover': { textDecoration: 'underline' },
                    textWeight: 'bold',
                  }}
                  variant="subtitle1"
                >
                  {title}
                </VidMobTypography>
              </span>
            </VidMobTooltip>
          </VidMobBox>
          <VidMobBox
            sx={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: 'background.default',
            }}
          >
            <VidMobTooltip
              title={intl.formatMessage({
                id: `ui.user.insightsLibrary.insightCard.type.${type.toLowerCase()}.tooltip`,
                defaultMessage: makeTitleCase(type),
              })}
              variant="basic"
              position="above"
            >
              <span>
                <VidMobTypography
                  sx={{
                    textDecoration: 'underline dashed 1px',
                    textUnderlineOffset: '4px',
                    cursor: 'pointer',
                    color: 'text.secondary',
                  }}
                >
                  {intl.formatMessage({
                    id: `ui.user.insightsLibrary.insightCard.${type.toLowerCase()}.label`,
                    defaultMessage: makeTitleCase(type),
                  })}
                </VidMobTypography>
              </span>
            </VidMobTooltip>
            {platform && (
              <>
                <VidMobBox
                  sx={{
                    width: 4,
                    height: 4,
                    backgroundColor: 'divider',
                    mx: '15px',
                    borderRadius: '50%',
                  }}
                />
                {getMUIIconForChannel(platformIconVDSIdentifier)}
              </>
            )}
            {shouldShowIndustryText && (
              <VidMobBox sx={{ display: 'flex', alignItems: 'center' }}>
                <VidMobBox
                  sx={{
                    width: 4,
                    height: 4,
                    backgroundColor: 'divider',
                    mx: '15px',
                    borderRadius: '50%',
                  }}
                />
                <VidMobTypography
                  sx={{ textTransform: 'capitalize', color: 'text.secondary' }}
                >
                  {industry}
                </VidMobTypography>
              </VidMobBox>
            )}
            {normativeMetaDataText && (
              <VidMobBox sx={{ display: 'flex', alignItems: 'center' }}>
                <VidMobBox
                  sx={{
                    width: 4,
                    height: 4,
                    backgroundColor: 'divider',
                    mx: '15px',
                    borderRadius: '50%',
                  }}
                />
                <BarChartIcon
                  color="iconDeEmphasized"
                  sx={{ fontSize: 'small' }}
                />
                <VidMobTooltip title={normativeMetaDataText} variant="basic">
                  <VidMobTypography
                    sx={{
                      ml: '15px',
                      cursor: 'default',
                      color: 'text.secondary',
                    }}
                  >
                    {normativeMetaDataText}
                  </VidMobTypography>
                </VidMobTooltip>
              </VidMobBox>
            )}
          </VidMobBox>
        </VidMobBox>

        <VidMobBox
          sx={{
            display: 'flex',
            flexDirection: 'row',
            height: '28px',
            alignItems: 'center',
          }}
        >
          {showProjectPill && <InsightCardPill pillText="Linked to Project" />}
          {showStatusPill && <InsightCardPill pillText={status} />}
          {actionMenuComponent}
        </VidMobBox>
      </VidMobBox>
    </VidMobBox>
  );
};

export default InsightCard;
