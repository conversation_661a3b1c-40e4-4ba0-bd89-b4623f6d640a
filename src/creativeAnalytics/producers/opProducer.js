// AGL-6002 checked
/**
 * This file includes classes that help hold object from high level api
 */
import getPlatformAnalytics from '../services/PlatformAnalytics/index';
import HighLevelApi from '../../analyticsApiSupport/highLevelApi/HighLevelApi';
import { REPORT_TABLE_VIEW_IDS } from '../../constants/ci.constants';
import { ciStabilityErrorHandler } from '../../utils/vmErrorLog';
import { sortResult } from '../../analyticsApiSupport/highLevelApi/resultSorters';
import getKeyForCIAdAccount from '../../utils/getKeyForCIAdAccount';
import AbstractProducer from './abstractProducer';
import { getFactoryKpiList } from '../components/AnalyticsFilters/AnalyticsFiltersApiServices/getKpiList';

const orderFormatDefault = {
  format: ['title', 'asc'],
};

/*
 * Producer for Objective and Placement Compare Report
 */
export class ObjectivePlacementProducer extends AbstractProducer {
  /**
   * @param {string} platform String platform name
   * @param {object} account Object {id, name, type}
   * @param {Function} updateLoading Function callback while waiting for request
   */
  setPlatformAccount(platform, account, updateLoading) {
    this.platform = platform;
    this.account = account;
    this.platformAnalytics = getPlatformAnalytics(platform);
    this.highLevelApi = new HighLevelApi();
    this.updateLoading = (reportId, percentage) => {
      if (this.apiCall && reportId === this.apiCall.reportId) {
        updateLoading(percentage);
      }
    };
  }

  /**
   * Get compare data object
   *
   * @param {object} compareOption Object {formats, formatter, name, dimension} from opCompareHelper
   * @param {string} averageType String from redux store
   * @param {object} filter Object additional filter if needed
   * @param {object} kpi Object kpi
   * @param {object} analyticsFilters list of applied analytics filters from redux
   * @param {boolean} isLifetimeReport boolean to determine whether to use date range or not
   * @param {boolean} overriddenDates overridden dates
   * @param {string} organizationId organization ID
   * @param {number[]} workspaceIds workspace IDs
   * @param {string} currency currency in case a spend-based KPI is selected
   * @returns {Promise} a promise that result is {elements, formats, hasData}
   */
  // eslint-disable-next-line max-params
  getReport = async (
    compareOption,
    averageType,
    filter,
    kpi,
    analyticsFilters,
    isLifetimeReport,
    overriddenDates,
    organizationId,
    workspaceIds,
    currency,
  ) => {
    const reportOptions = {
      extraDimensions: this.extraDimensions,
      requestType: compareOption?.type,
      requestTypeOptions: compareOption,
      metrics: this.platformAnalytics.getMetrics(),
      updateLoading: this.updateLoading,
      elementAverage: averageType,
    };

    const report = await this.requestReport(
      filter,
      analyticsFilters,
      reportOptions,
      isLifetimeReport,
      kpi,
      overriddenDates,
      false,
      true,
      organizationId,
      workspaceIds,
      currency?.id,
    );
    const { reportTableView, statSettings, selectedMediaTypeFilters } =
      analyticsFilters;
    if (report && !report.error && !report.cause) {
      if (
        reportTableView &&
        reportTableView.id === REPORT_TABLE_VIEW_IDS.KPIS_TABLE_VIEW
      ) {
        if (Array.isArray(filter?.account)) {
          kpi = await getFactoryKpiList(organizationId, {
            mediaTypes: selectedMediaTypeFilters,
            platform: this.platform,
            adAccountIds: filter?.account,
            workspaceIds,
          });
        } else {
          kpi = await this.platformAnalytics.getKpiList(
            selectedMediaTypeFilters,
            this.account,
          );
        }
      } else {
        kpi = [kpi];
      }

      const data = sortResult(
        report,
        kpi,
        reportTableView,
        orderFormatDefault,
        statSettings?.isDefaultKPITimeRangeActive,
        this.platformAnalytics,
        currency,
      );
      return {
        elements: data.elements,
        groups: data.groups,
        flags: data.flags,
        hasData: Boolean(data.elements.length && data.groups.length),
        done: true,
      };
    }

    if (report && report.error) {
      ciStabilityErrorHandler(
        report.error,
        'opProducer.js',
        'getReport()',
        'report',
      );
      return {
        done: true,
        hasData: false,
        error: report.error,
        cause: report.cause || '',
      };
    }

    if (report && !this.querying && !report.ignore) {
      return {
        hasData: false,
        done: true,
        elements: [],
        groups: [],
        error: report.error,
      };
    }

    return { done: false, ignore: Boolean(report && report.ignore) };
  };
}

let objectiveProducers = {};
let placementProducers = {};

export const clearObjectiveProducers = () => {
  objectiveProducers = {};
  return true;
};

export const clearPlacementProducers = () => {
  placementProducers = {};
  return true;
};

export const getFilterV2ObjectiveProducer = (
  accounts,
  updateLoading,
  channel,
) => {
  if (Array.isArray(accounts)) {
    let accountKey = '';
    accounts.forEach((item) => {
      const { platform, id, type } = item;
      accountKey += `${platform}-${id}-${type}`;
    });

    if (!objectiveProducers[accountKey]) {
      objectiveProducers[accountKey] = new ObjectivePlacementProducer(channel);
      objectiveProducers[accountKey].setPlatformAccount(
        channel,
        accounts,
        updateLoading,
      );
    }

    return objectiveProducers[accountKey];
  }
};

export const getObjectiveProducer = (account, updateLoading) => {
  const accountKey = getKeyForCIAdAccount(account);
  if (!objectiveProducers[accountKey]) {
    objectiveProducers[accountKey] = new ObjectivePlacementProducer(
      account.platform,
    );
    objectiveProducers[accountKey].setPlatformAccount(
      account.platform,
      account,
      updateLoading,
    );
  }

  return objectiveProducers[accountKey];
};

export const getFilterV2PlacementProducer = (
  accounts,
  updateLoading,
  channel,
) => {
  if (Array.isArray(accounts)) {
    let accountKey = '';
    accounts.forEach((item) => {
      const { platform, id, type } = item;
      accountKey += `${platform}-${id}-${type}`;
    });

    if (!placementProducers[accountKey]) {
      placementProducers[accountKey] = new ObjectivePlacementProducer(channel);
      placementProducers[accountKey].setPlatformAccount(
        channel,
        accounts,
        updateLoading,
      );
    }

    return placementProducers[accountKey];
  }
};

export const getPlacementProducer = (account, updateLoading) => {
  const accountKey = getKeyForCIAdAccount(account);
  if (!placementProducers[accountKey]) {
    placementProducers[accountKey] = new ObjectivePlacementProducer(
      account.platform,
    );
    placementProducers[accountKey].setPlatformAccount(
      account.platform,
      account,
      updateLoading,
    );
  }

  return placementProducers[accountKey];
};
