import BffService from '../../../apiServices/BffService';
import { getFeatureFlag } from '../../../utils/featureFlagUtils';
import vmErrorLog from '../../../utils/vmErrorLog';
import {
  AnalyticsBffReportRenameParams,
  AnalyticsBffReportRequestParams,
} from '../../__pages/SavedReports/SavedReportsTypes';

const {
  handleBffApiGetWithPagination,
  handleBffApiDelete,
  handleBffApiPatch,
  handleBffApiPost,
  handleBffApiPostWithPagination,
} = BffService;

class AnalyticsSavedReportsService {
  getAllSavedReports = async (organizationId: string, requestParams: any) => {
    const { filters, sortModel } = requestParams;

    const isAnalyticsSortingEnabled = getFeatureFlag(
      'isAnalyticsSortingEnabled',
    );

    const URLParams = new URLSearchParams({
      offset: requestParams.offset || 0,
      perPage: requestParams.perPage || 10,
      sortBy: sortModel[0].field,
      sortOrder: sortModel[0].sort,
    });

    const version = isAnalyticsSortingEnabled ? 'v3' : 'v2';

    try {
      const savedReportsEndpoint = `/${version}/analytics-report/organization/${organizationId}/?${URLParams.toString()}`;

      return await handleBffApiPostWithPagination(
        savedReportsEndpoint,
        filters,
      );
    } catch (error) {
      vmErrorLog(
        error as Error,
        'AnalyticsSavedReportsService getAllSavedReports',
        'Something went wrong loading all Saved Reports in Analytics.',
      );

      throw error;
    }
  };

  getSavedReportById = async (requestParams: {
    reportId: string;
    organizationId: string;
  }) => {
    const { reportId, organizationId, ...otherParams } = requestParams;

    try {
      const savedReportsEndpoint = `/v1/analytics-report/organization/${organizationId}/report/${reportId}`;
      return await handleBffApiGetWithPagination(
        savedReportsEndpoint,
        otherParams,
      );
    } catch (error) {
      vmErrorLog(
        error as Error,
        'AnalyticsSavedReportsService getSavedReportById',
        `Something went wrong loading Saved Report in Analytics at id ${reportId}.`,
      );

      throw error;
    }
  };

  duplicateReport = async (organizationId: string, reportId: string) => {
    try {
      const savedReportsEndpoint = `/v1/analytics-report/organization/${organizationId}/report/${reportId}`;
      return await handleBffApiPost(savedReportsEndpoint);
    } catch (error) {
      vmErrorLog(
        error as Error,
        'AnalyticsSavedReportsService duplicateReport',
        `Something went wrong duplicating Saved Report in Analytics at id ${reportId}.`,
      );

      throw error;
    }
  };

  renameSavedReport = async (
    reportId: string,
    organizationId: string,
    renameReportParams?: AnalyticsBffReportRenameParams,
  ) => {
    try {
      const renameReportEndpoint = `v1/analytics-report/organization/${organizationId}/report/${reportId}/rename`;
      return await handleBffApiPatch(renameReportEndpoint, renameReportParams);
    } catch (error) {
      vmErrorLog(
        error as Error,
        'AnalyticsSavedReportsService renameReport',
        `Something went wrong renaming Saved Report in Analytics at id ${reportId}.`,
      );

      throw error;
    }
  };

  createSavedReport = async (
    organizationId: string,
    requestParams?: Record<string, unknown> | AnalyticsBffReportRequestParams,
  ) => {
    try {
      const savedReportsEndpoint = `/v1/analytics-report/organization/${organizationId}`;
      return await handleBffApiPost(savedReportsEndpoint, requestParams);
    } catch (error) {
      vmErrorLog(
        error as Error,
        'AnalyticsSavedReportsService createSavedReport',
        'Something went wrong creating new Saved Report in Analytics.',
      );

      throw error;
    }
  };

  updateSavedReport = async (
    reportId: string,
    organizationId: string,
    requestParams?: Record<string, unknown> | AnalyticsBffReportRequestParams,
  ) => {
    try {
      const savedReportsEndpoint = `/v1/analytics-report/organization/${organizationId}/report/${reportId}`;
      return await handleBffApiPatch(savedReportsEndpoint, requestParams);
    } catch (error) {
      vmErrorLog(
        error as Error,
        'AnalyticsSavedReportsService updateSavedReport',
        `Something went wrong updating Saved Report in Analytics at id ${reportId}.`,
      );

      throw error;
    }
  };

  deleteSavedReport = async (reportId: string, organizationId: string) => {
    try {
      const savedReportsEndpoint = `/v1/analytics-report/organization/${organizationId}/report/${reportId}`;
      return await handleBffApiDelete(savedReportsEndpoint);
    } catch (error) {
      vmErrorLog(
        error as Error,
        'AnalyticsSavedReportsService deleteSavedReport',
        `Something went wrong deleting Saved Report in Analytics at id ${reportId}.`,
      );

      throw error;
    }
  };
}

export default new AnalyticsSavedReportsService();
