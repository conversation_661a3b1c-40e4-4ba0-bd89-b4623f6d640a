import { onSetColumnFilter } from './elementPresenceReport.sagas';
import { expectSaga } from 'redux-saga-test-plan';
import MOCK_DATA from '../../../../redux/sagas/__mocks__/mockReportData';
import { getAnalyticsConfiguration } from '../../../../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';
import * as matchers from 'redux-saga-test-plan/matchers';
import { getCurrentPlatformAccount } from '../../../../redux/selectors/platformAccounts.selectors';
import { getElementPresenceReportFilterSelections } from '../../../../redux/selectors/creativeAnalytics/elementPresenceReport.selectors';
import { requestCustomCompareReport } from '../../../../redux/sagas/creativeAnalytics/customCompare.sagas';
import { showLocalizedErrorBar } from '../../../../utils/showConfirmationBar';
import analyticsConfigurationSlice from '../../../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';
import elementPresenceReportSlice from '../../../../redux/slices/creativeAnalytics/elementPresenceReport.slice';
import getPlatformAnalytics from '../../../services/PlatformAnalytics';
import toastAlertSlice from '../../../../redux/slices/toastAlert.slice';

/**
 * MOCKS
 */
const {
  MOCK_ADV_ELEMENT_PRESENCE_REPORT,
  MOCK_ADVANCED_FILTER_SELECTION,
  MOCK_ADVANCED_FILTER_SELECTIONS,
  MOCK_ANALYTICS_FILTERS,
  MOCK_CURRENT_ACCOUNT,
  MOCK_SET_COLUMN_FILTER_ADV_ACTION,
  MOCK_PAYLOAD_ERROR_RESPONSE,
} = MOCK_DATA;

const MOCK_ADVANCED_FILTER_SELECTION_VALUE = Object.values(
  MOCK_ADVANCED_FILTER_SELECTION,
)[0];

jest.mock('../../../../utils/vmErrorLog.js', () => {
  return {
    ciStabilityErrorHandler: jest.fn(),
    getErrorLogFunction: jest.fn(),
    vmErrorLog: jest.fn(),
  };
});

describe('Advanced Element Presence Report Saga', () => {
  describe('Get data for column', () => {
    it('handles non-200 response', () => {
      return expectSaga(onSetColumnFilter, MOCK_SET_COLUMN_FILTER_ADV_ACTION)
        .provide([
          [
            matchers.select(getElementPresenceReportFilterSelections),
            MOCK_ADVANCED_FILTER_SELECTIONS,
          ],
          [matchers.select(getAnalyticsConfiguration), MOCK_ANALYTICS_FILTERS],
          [matchers.select(getCurrentPlatformAccount), MOCK_CURRENT_ACCOUNT],
          [matchers.call.fn(getPlatformAnalytics), null],
          [matchers.call.fn(requestCustomCompareReport), undefined],
        ])
        .call(
          showLocalizedErrorBar,
          'ui.creative.intelligence.elementPresence.addColumn.banner.error',
        )
        .put(
          analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
            isAddNewColumnTransitioning: false,
          }),
        )
        .put(
          elementPresenceReportSlice.actions.updateReportOnRequestFailure({
            missingGroups: undefined,
          }),
        )
        .silentRun();
    });
    it('handles 1 of many column request failing', () => {
      return expectSaga(onSetColumnFilter, MOCK_SET_COLUMN_FILTER_ADV_ACTION)
        .provide([
          [
            matchers.select(getElementPresenceReportFilterSelections),
            MOCK_ADVANCED_FILTER_SELECTIONS,
          ],
          [matchers.select(getAnalyticsConfiguration), MOCK_ANALYTICS_FILTERS],
          [matchers.call.fn(getPlatformAnalytics), null],
          [
            matchers.call.fn(requestCustomCompareReport),
            MOCK_ADV_ELEMENT_PRESENCE_REPORT,
          ],
        ])
        .call(
          showLocalizedErrorBar,
          'ui.creative.intelligence.elementPresence.addColumn.banner.error',
        )
        .put(
          analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
            isAddNewColumnTransitioning: true,
          }),
        )
        .put(
          analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
            isAddNewColumnTransitioning: false,
          }),
        )
        .put(
          elementPresenceReportSlice.actions.updateReportOnRequestFailure({
            missingGroups: [MOCK_ADVANCED_FILTER_SELECTION_VALUE],
          }),
        )
        .silentRun();
    });
    it('handles payload size errors', () => {
      return expectSaga(onSetColumnFilter, MOCK_SET_COLUMN_FILTER_ADV_ACTION)
        .provide([
          [
            matchers.select(getElementPresenceReportFilterSelections),
            MOCK_ADVANCED_FILTER_SELECTIONS,
          ],
          [matchers.select(getAnalyticsConfiguration), MOCK_ANALYTICS_FILTERS],
          [matchers.call.fn(getPlatformAnalytics), null],
          [
            matchers.call.fn(requestCustomCompareReport),
            MOCK_PAYLOAD_ERROR_RESPONSE,
          ],
        ])
        .put(
          toastAlertSlice.actions.showToastAlert({
            message:
              'ui.creative.intelligence.elementPresence.addColumn.banner.error.payloadSize',
            type: 'error',
          }),
        )
        .put(
          analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
            isAddNewColumnTransitioning: false,
          }),
        )
        .put(
          elementPresenceReportSlice.actions.updateReportOnRequestFailure({
            missingGroups: undefined,
          }),
        )
        .silentRun();
    });
  });
});
