import { expectSaga, testSaga } from 'redux-saga-test-plan';
import * as matchers from 'redux-saga-test-plan/matchers';
// redux
import individualCreativeViewSlice from './individualCreativeView.slice';
import individualCreativeViewPerformanceOverTimeSlice from './IndividualCreativeViewPerformanceOverTime/IndividualCreativeViewPerformanceOverTime.slice';
import {
  loadPlatformMedia,
  loadPlatformMediaPerformanceForIndividualCreative,
  loadPlatformMediaPerformanceForAccountAverage,
  loadPlatformMediaPerformanceForNormativeIndustryAverage,
  returnNewAnalyticsQueryService,
  reloadPlatformMediaPerformanceForIndividualCreative,
  formatTagData,
  loadDailyMediaPerformanceData,
  loadKpiListIfNeeded,
} from './individualCreativeView.sagas';
import { getIndividualCreativeViewParams } from './individualCreativeView.selectors';
// helpers
import { getFilters } from '../../../helpers/dataHelper';
// constants
import { GLOBALS } from '../../../../constants';
// mock data
import MOCK_DATA from '../../__mocks__/mockIndividualCreativeViewData';
// services
import PlatformAccountService from '../../../../apiServices/PlatformAccountService';
import KpiNormsService from '../../../../analyticsApiSupport/KpiNormsService';
import getPlatformAnalytics from '../../../services/PlatformAnalytics';
import IndividualCreativeViewPerformanceOverTimeSlice from './IndividualCreativeViewPerformanceOverTime/IndividualCreativeViewPerformanceOverTime.slice';
import DailyMediaPerformanceService from '../../services/dailyMediaPerformanceService';

const {
  setIndividualCreativeViewMediaPerformanceStatus,
  setIndividualCreativeViewDateRangeParams,
} = individualCreativeViewSlice.actions;

const { PENDING, FAILED, SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const {
  mockPlatformMediaId,
  mockIndividualCreativeViewParams,
  mockSuccessMediaData,
  mockAnalyticsQueryService,
  mockQueryPlatformMediaResultFailure,
  mockPlatformAnalyticsService,
  mockPostPlatformDataSuccess,
  mockPlatformForPostPlatformRequest,
  mockRequestBodyForPostPlatformData,
  mockNoDataPostPlatformDataSuccess,
  mockImpressionMetricKey,
  mockFilters,
  mockLoadStatData,
  mockAdVideoDimension,
  mockGetPlatformIndustryKpiNormData,
  mockNormativePlatformViewThroughData,
  mockNormativeIndustryViewThroughData,
  mockDateRange,
  mockQueryPlatformMediasResultSuccess,
  mockSuccessDailyPerformanceData,
} = MOCK_DATA;

const {
  accountId,
  accountType,
  platform,
  analyticsStartDate,
  analyticsEndDate,
} = mockIndividualCreativeViewParams;

const { duration } = mockSuccessMediaData;

xdescribe('Individual Creative View Sagas', () => {
  it('gets platform media data success', () => {
    testSaga(loadPlatformMedia, mockPlatformMediaId)
      .next()
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewMediaDataStatus(
          PENDING,
        ),
      )
      .next()
      .call(returnNewAnalyticsQueryService)
      .next(mockAnalyticsQueryService)
      .call([mockAnalyticsQueryService, 'queryPlatformMedias'], {
        platformMediaIds: [mockPlatformMediaId],
        options: {
          includeMedia: true,
          includeTags: true,
        },
      })
      .next(mockQueryPlatformMediasResultSuccess)
      .call(formatTagData, {})
      .next([])
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewTagData(
          [],
        ),
      )
      .next()
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewMediaData(
          mockSuccessMediaData,
        ),
      )
      .next()
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewMediaDataStatus(
          SUCCESS,
        ),
      )
      .next()
      .isDone();
  });
  it('gets platform media data api failure', () => {
    testSaga(loadPlatformMedia, mockPlatformMediaId)
      .next()
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewMediaDataStatus(
          PENDING,
        ),
      )
      .next()
      .call(returnNewAnalyticsQueryService)
      .next(mockAnalyticsQueryService)
      .call([mockAnalyticsQueryService, 'queryPlatformMedias'], {
        platformMediaIds: [mockPlatformMediaId],
        options: {
          includeMedia: true,
          includeTags: true,
        },
      })
      .next({})
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewMediaDataStatus(
          FAILED,
        ),
      )
      .next()
      .isDone();
  });

  it('gets platform media data bad data failure', () => {
    testSaga(loadPlatformMedia, mockPlatformMediaId)
      .next()
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewMediaDataStatus(
          PENDING,
        ),
      )
      .next()
      .call(returnNewAnalyticsQueryService)
      .next(mockAnalyticsQueryService)
      .call([mockAnalyticsQueryService, 'queryPlatformMedias'], {
        platformMediaIds: [mockPlatformMediaId],
        options: {
          includeMedia: true,
          includeTags: true,
        },
      })
      .next(mockQueryPlatformMediaResultFailure)
      .put(
        individualCreativeViewSlice.actions.setIndividualCreativeViewMediaDataStatus(
          FAILED,
        ),
      )
      .next()
      .isDone();
  });

  it('gets performance media data for Individual Creative success', () => {
    testSaga(
      loadPlatformMediaPerformanceForIndividualCreative,
      mockIndividualCreativeViewParams,
    )
      .next()
      .call(getPlatformAnalytics, platform)
      .next(mockPlatformAnalyticsService)
      .call([mockPlatformAnalyticsService, 'getImpressionMetricKey'])
      .next(mockImpressionMetricKey)
      .call(getFilters, { id: accountId, type: accountType, platform })
      .next(mockFilters)
      .call([mockPlatformAnalyticsService, 'getAdVideoDimension'])
      .next(mockAdVideoDimension)
      .call([mockPlatformAnalyticsService, 'getAnalyticsPlatformName'])
      .next(mockPlatformForPostPlatformRequest)
      .call(
        [PlatformAccountService, 'postPlatformData'],
        mockPlatformForPostPlatformRequest,
        mockRequestBodyForPostPlatformData,
      )
      .next(mockPostPlatformDataSuccess)
      .call(
        [mockPlatformAnalyticsService, 'loadStat'],
        mockPostPlatformDataSuccess.data.stats[0],
      )
      .next(mockLoadStatData)
      .call([mockLoadStatData, 'getViewThroughRates'])
      .next()
      .isDone();
  });

  it('gets performance media data for Individual Creative success when no data', () => {
    const analyticsStartDate = '2001-07-02';
    const analyticsEndDate = '2002-05-10';

    testSaga(loadPlatformMediaPerformanceForIndividualCreative, {
      ...mockIndividualCreativeViewParams,
      analyticsStartDate,
      analyticsEndDate,
    })
      .next()
      .call(getPlatformAnalytics, platform)
      .next(mockPlatformAnalyticsService)
      .call([mockPlatformAnalyticsService, 'getImpressionMetricKey'])
      .next(mockImpressionMetricKey)
      .call(getFilters, {
        id: accountId,
        type: accountType,
        platform,
      })
      .next(mockFilters)
      .call([mockPlatformAnalyticsService, 'getAdVideoDimension'])
      .next(mockAdVideoDimension)
      .call([mockPlatformAnalyticsService, 'getAnalyticsPlatformName'])
      .next(mockPlatformForPostPlatformRequest)
      .call(
        [PlatformAccountService, 'postPlatformData'],
        mockPlatformForPostPlatformRequest,
        {
          ...mockRequestBodyForPostPlatformData,
          startDate: analyticsStartDate,
          endDate: analyticsEndDate,
        },
      )
      .next(mockNoDataPostPlatformDataSuccess)
      .isDone();
  });

  it('gets performance media data for Account Average success', () => {
    testSaga(
      loadPlatformMediaPerformanceForAccountAverage,
      mockIndividualCreativeViewParams,
      mockSuccessMediaData,
    )
      .next()
      .call(
        [PlatformAccountService, 'getPlatformAccountAverage'],
        platform,
        accountId,
        accountType.toUpperCase(),
        duration,
      )
      .next(mockPostPlatformDataSuccess)
      .call(getPlatformAnalytics, platform)
      .next(mockPlatformAnalyticsService)
      .call(
        [mockPlatformAnalyticsService, 'loadStat'],
        mockPostPlatformDataSuccess.data.stats[0],
      )
      .next(mockLoadStatData)
      .call([mockLoadStatData, 'getViewThroughRates'])
      .next()
      .isDone();
  });

  it('gets performance media data for Normative Platform and Normative Industry success', () => {
    testSaga(
      loadPlatformMediaPerformanceForNormativeIndustryAverage,
      mockIndividualCreativeViewParams,
      mockSuccessMediaData,
      true,
    )
      .next()
      .call(
        [KpiNormsService, 'getPlatformIndustryKpiNormData'],
        accountId,
        platform,
        duration,
        true,
      )
      .next(mockGetPlatformIndustryKpiNormData)
      .call(getPlatformAnalytics, platform)
      .next(mockPlatformAnalyticsService)
      .call([mockPlatformAnalyticsService, 'getViewThroughKpis'], {
        stats: [{ ...mockGetPlatformIndustryKpiNormData.stats[0] }],
      })
      .next(mockNormativePlatformViewThroughData)
      .call([mockPlatformAnalyticsService, 'getViewThroughKpis'], {
        stats: [{ ...mockGetPlatformIndustryKpiNormData.stats[1] }],
      })
      .next(mockNormativeIndustryViewThroughData)
      .isDone();
  });

  it('setting date range in individual creative view triggers request to load new data for time range', () => {
    return expectSaga(reloadPlatformMediaPerformanceForIndividualCreative)
      .provide([
        [
          matchers.select(getIndividualCreativeViewParams),
          mockIndividualCreativeViewParams,
        ],
      ])
      .put(setIndividualCreativeViewMediaPerformanceStatus(PENDING))
      .dispatch(setIndividualCreativeViewDateRangeParams(mockDateRange))
      .silentRun();
  });

  it('gets daily performance data', () => {
    const queryParams = {
      platform,
      accountId,
      accountType,
      mediaId: mockPlatformMediaId,
      startDate: analyticsStartDate,
      endDate: analyticsEndDate,
    };

    testSaga(loadDailyMediaPerformanceData, mockIndividualCreativeViewParams)
      .next()
      .put(
        individualCreativeViewPerformanceOverTimeSlice.actions.setPerformanceOverTimeDailyPerformanceDataStatus(
          PENDING,
        ),
      )
      .next()
      .call(
        [DailyMediaPerformanceService, 'getListOfDailyMediaPerformance'],
        queryParams,
      )
      .next({ data: mockSuccessDailyPerformanceData })
      .put(
        IndividualCreativeViewPerformanceOverTimeSlice.actions.setPerformanceOverTimeDailyPerformanceData(
          mockSuccessDailyPerformanceData,
        ),
      )
      .next()
      .put(
        IndividualCreativeViewPerformanceOverTimeSlice.actions.setPerformanceOverTimeDailyPerformanceDataStatus(
          SUCCESS,
        ),
      )
      .next()
      .isDone();
  });
});
