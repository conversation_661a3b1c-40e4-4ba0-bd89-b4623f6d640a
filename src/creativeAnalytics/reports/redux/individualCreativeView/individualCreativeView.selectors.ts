import individualCreativeViewSlice, {
  IndividualCreativeViewStateError,
  IndividualCreativeViewStateParams,
  LoadStatus,
  IndividualCreativeViewMediaPerformance,
  IndividualCreativeViewStateMediaData,
} from './individualCreativeView.slice';
import { MediaObjectType } from '../../../../creativeScoring/components/individualAssetViewV2/individualAsset.types';
import { IndividualCreativeViewRequestParams } from './individualCreativeView.slice';

const { name } = individualCreativeViewSlice;

interface Selector<P = any> {
  (state: any): P;
}

export const getIndividualCreativeViewParams: Selector<
  IndividualCreativeViewStateParams
> = (state) => state[name]?.params;

export const getIndividualCreativeViewRequestParamsV2: Selector<
  IndividualCreativeViewRequestParams
> = (state) => state[name].requestParamsV2;

export const getIndividualCreativeViewError: Selector<
  IndividualCreativeViewStateError
> = (state) => state[name]?.error;

export const getIndividualCreativeViewMediaData: Selector<
  MediaObjectType | IndividualCreativeViewStateMediaData
> = (state) => state[name]?.mediaData;

export const getIndividualCreativeViewMediaDataStatus: Selector<LoadStatus> = (
  state,
) => state[name]?.mediaDataStatus;

export const getIndividualCreativeViewPerformanceData: Selector<
  IndividualCreativeViewMediaPerformance
> = (state) => state[name]?.mediaPerformance;

export const getIndividualCreativeViewMediaPerformanceDataStatus: Selector<
  LoadStatus
> = (state) => state[name]?.mediaPerformanceStatus;

export const getMediaPreviewCurrentTimeByMediaId: (
  mediaId: number,
) => Selector<number> = (mediaId) => (state) => {
  const mediaIdString = `media-${mediaId}`;
  const player = state.jwPlayer?.players[mediaIdString];
  if (!player) {
    return 0;
  }

  return player.position;
};
