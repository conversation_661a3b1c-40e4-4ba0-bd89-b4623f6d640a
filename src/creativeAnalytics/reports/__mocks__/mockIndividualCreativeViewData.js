/* eslint-disable camelcase */
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../constants';
const { NOT_LOADED, SUCCESS, PENDING, FAILED } = GLOBALS.REDUX_LOADING_STATUS;
import { INDIVIDUAL_CREATIVE_VIEW_AUDIENCE_ENGAGEMENT_BUCKET_HEADERS } from '../../../constants/creativeAnalytics.constants';
import { ACCOUNT_ACCOUNT_TYPE } from '../../../constants/ci.constants';

/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///
///                   Mock Dummy Data                       ///
/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///
const mockPlatformMediaId = 'bMtaf0uawZg';

// url params for ICV page
const mockIndividualCreativeViewParams = {
  accountId: '**********',
  accountType: 'account',
  platformMediaId: 'bMtaf0uawZg',
  platform: 'ADWORDS',
  analyticsStartDate: '2021-07-02',
  analyticsEndDate: '2022-05-10',
  kpi: '67',
  organizationId: 'd2068f6c-a912-43e6-9d8b-6bf0a8f51a91',
  workspaceIds: [22520, 11111],
};

// media data for media preview
const mockSuccessMediaData = {
  id: 150270,
  name: '52686503_471133457901455_2518299738889439575_n.mp4',
  displayName: 'finserv-stand-out-gif.mp4',
  downloadUrl:
    'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/52686503_471133457901455_2518299738889439575_n.mp4?response-content-disposition=attachment%3B%20filename%3D52686503_471133457901455_2518299738889439575_n.mp4&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=530b201131cb878e3afde81ef678ad677645fb59e9a2397e901353d75e227571',
  mediaType: 'BACKGROUND_PLATFORM',
  fileType: 'VIDEO',
  processingState: 'COMPLETE',
  width: 1280,
  height: 720,
  duration: 6.08,
  thumbnails: [
    {
      height: 360,
      width: 640,
      url: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/304401_thumb_640_52686503_471133457901455_2518299738889439575_n.jpg?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=2a359a466417f534ca45f311a158b534e0ec3b07f434e278e373ca567d710072',
    },
    {
      height: 422,
      width: 750,
      url: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/304402_thumb_750_52686503_471133457901455_2518299738889439575_n.jpg?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=c5a81d4bdf4017a352f48f2a5c1dcd2a09d52bceba25630239886a026cb432a5',
    },
    {
      height: 699,
      width: 1242,
      url: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/304403_thumb_1242_52686503_471133457901455_2518299738889439575_n.jpg?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=d7598212c95afd0768049fd74751c190f1adfc03f5d98a9f6f9562de9fa970b8',
    },
  ],
  streams: {
    wifi: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/stream/hls_wifi_partial_40721fccb7b9ec6fbc5b26cff9d3f39abff8e626e019f879eb51753a1b68e209.m3u8?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=c24bf1ce1d149bdaf312d3cae0927835291ab297889e91aa13f022322fbc23e7',
    cellular:
      'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/stream/hls_cellular_partial_40721fccb7b9ec6fbc5b26cff9d3f39abff8e626e019f879eb51753a1b68e209.m3u8?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=4f00a69df839b9c27652c9b52c7ed087a89f50d0cd1b4a71a590f7265cdaa8c5',
  },
};

// request body for requesting performance data for individual creative
const mockPlatformForPostPlatformRequest = 'ADWORDS';
const mockFilters = { accountGroup: ['144'] };
const mockImpressionMetricKey = 'impressions';

const mockRequestBodyForPostPlatformData = {
  cache: true,
  dimensions: [],
  endDate: mockIndividualCreativeViewParams.analyticsEndDate,
  filter: {
    ...mockFilters,
    advideo: [mockIndividualCreativeViewParams.platformMediaId],
  },
  metrics: [
    mockImpressionMetricKey,
    'video_quartile25_value',
    'video_quartile50_value',
    'video_quartile75_value',
    'video_quartile100_value',
  ],
  startDate: mockIndividualCreativeViewParams.analyticsStartDate,
};

// performance data for individual creative
const mockPostPlatformDataSuccess = {
  data: {
    stats: [
      {
        impressions: 703547,
        video_p25_watched_actions: { video_view: 51500 },
        video_p50_watched_actions: { video_view: 26329 },
        video_p75_watched_actions: { video_view: 17779 },
        video_p95_watched_actions: { video_view: 12114 },
        video_p100_watched_actions: { video_view: 10781 },
      },
    ],
  },
};

// no data for performance for individual creative
const mockNoDataPostPlatformDataSuccess = {
  data: {
    stats: [],
  },
};

// performance data for platform and industry
const mockGetPlatformIndustryKpiNormData = {
  stats: [
    {
      platform: {
        video_p100_watched_actions: {
          video_view: 0.11622374563569783,
        },
        video_p50_watched_actions: {
          video_view: 0.1906507379676401,
        },
        video_p95_watched_actions: {
          video_view: 0.12065713807362251,
        },
        actions: {
          video_view: 0.19240899508068027,
        },
        video_p25_watched_actions: {
          video_view: 0.****************,
        },
        video_p75_watched_actions: {
          video_view: 0.****************,
        },
      },
      industry: {
        video_p100_watched_actions: {
          video_view: 0.11622374563569783,
        },
        video_p50_watched_actions: {
          video_view: 0.1906507379676401,
        },
        video_p95_watched_actions: {
          video_view: 0.12065713807362251,
        },
        actions: {
          video_view: 0.19240899508068027,
        },
        video_p25_watched_actions: {
          video_view: 0.****************,
        },
        video_p75_watched_actions: {
          video_view: 0.****************,
        },
      },
    },
  ],
};

// performance data translated to {x,y} points
const mockSuccessMediaPerformanceData = {
  individualCreative: {
    data: [
      { x: 0, y: 100 },
      { x: 25, y: 28.4 },
      { x: 50, y: 20.1 },
      { x: 75, y: 15.2 },
      { x: 95, y: 7.8 },
      { x: 100, y: 2.3 },
    ],
  },
  accountAverage: {
    data: [
      { x: 0, y: 100 },
      { x: 25, y: 31.2 },
      { x: 50, y: 19.4 },
      { x: 75, y: 17.5 },
      { x: 95, y: 6.5 },
      { x: 100, y: 1.4 },
    ],
  },
  normativePlatformAverage: {
    data: [
      { x: 0, y: 100 },
      { x: 25, y: 21.0 },
      { x: 50, y: 15.6 },
      { x: 75, y: 14.6 },
      { x: 95, y: 8.7 },
      { x: 100, y: 4.3 },
    ],
  },
  normativeIndustryAverage: {
    data: [
      { x: 0, y: 100 },
      { x: 25, y: 22.0 },
      { x: 50, y: 17.2 },
      { x: 75, y: 13.4 },
      { x: 95, y: 4.3 },
      { x: 100, y: 2.1 },
    ],
  },
};

const mockIndividualCreativeViewThroughData =
  mockSuccessMediaPerformanceData.individualCreative;
const mockNormativePlatformViewThroughData =
  mockSuccessMediaPerformanceData.normativePlatformAverage;
const mockNormativeIndustryViewThroughData =
  mockSuccessMediaPerformanceData.normativeIndustryAverage;

const mockMediaPerformanceDataStatus = SUCCESS;
const mockMediaDataStatus = SUCCESS;
const mockError = {
  ...new Error(),
  code: 401,
};

const mockMediaPreviewPlayerData = {
  players: {
    ['media-' + mockSuccessMediaData.id]: {
      position: 0,
    },
  },
};

const mockSuccessDailyPerformanceData = [
  {
    date: '2021-01-01',
    impressions: 1000,
    kpiValues: {
      0: 12,
      1: 34,
      2: 10.2,
      3: 12.67,
    },
  },
  {
    date: '2021-01-02',
    impressions: 1000,
    kpiValues: {
      0: 14,
      1: 54,
      2: 16.2,
      3: 28.12,
    },
  },
];

const mockSuccessDailyPerformanceDataFormattedForGraph = [
  { x: *************, y: 80.************** },
  { x: *************, y: 77.************** },
  { x: *************, y: 73.************** },
  { x: *************, y: 84.************* },
  { x: *************, y: 15.*************** },
];

const mockSuccessAccountAverageData = {
  0: 12,
  1: 34,
  2: 10.2,
  3: 12.67,
};

const mockSuccessAccountAverageFormattedForGraph = [
  { x: *************, y: 14 },
  { x: *************, y: 14 },
  { x: *************, y: 14 },
  { x: *************, y: 14 },
  { x: *************, y: 14 },
];

const mockICVSelectedKpiObject = {
  inverseHealth: false,
  format: {
    name: 'percentage',
  },
};

/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///
///                   Mock Services                         ///
/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///

const mockAnalyticsQueryService = {
  queryPlatformMedia: () => mockQueryPlatformMediaResultSuccess,
  queryPlatformMedias: () => mockQueryPlatformMediasResultSuccess,
};

const mockQueryPlatformMediaResultSuccess = {
  status: 'success',
  apiConnection: {
    vmApiManager: {
      sessionManager: {
        sessionIdStorageName: 'vm_session',
        localStorage: 'localStorage',
        sessionStorage: 'sessionStorage',
        storageObj: {
          partnerId: '29732',
          'jwplayer.bandwidthEstimate': '107375970.06102017',
          'intercom.intercom-state-kam5unzd':
            '{"app":{"color":"#333333","secondaryColor":"#333333","selfServeSuggestionsMatch":false,"name":"VidMob [TEST]","features":{"anonymousInboundMessages":true,"googleAnalytics":false,"hubspotInstalled":false,"inboundMessages":true,"marketoEnrichmentInstalled":false,"outboundMessages":true},"launcherLogoUrl":null,"boundWebEvents":[],"inboundConversationsDisabled":false,"isInstantBootEnabled":true,"alignment":"right","horizontalPadding":20,"verticalPadding":20,"isDeveloperWorkspace":false,"customGoogleAnalyticsTrackerId":null},"launcher":{"isLauncherEnabled":true},"launcherDiscoveryMode":{"hasDiscoveredLauncher":true},"user":{"role":"user","locale":"en","hasConversations":false},"message":{},"conversations":{"byId":{}},"openOnBoot":{"type":null,"metadata":{}},"operator":{"lastComposerEvent":0}}',
          'jwplayer.mute': 'false',
          'platform-account': '{"id":159,"type":"GROUP","platform":"FACEBOOK"}',
          tusSupport: 'null',
          vm_session:
            '{"sessionId":"0151b95a-7cdd-4de1-8c7d-2bebeb136409","accessToken":"HO/cvab8+uvphBQjhSvnYlumG1RCWzDCxzbUbHQlODU=","expiresTimestamp":*************,"store":"localStorage"}',
          'jwplayer.volume': '17.**************',
          jwplayerLocalId: 'lme5sd1vq9ci',
          'dashboard-platform-account':
            '{"id":"**********","type":"ACCOUNT","platform":"ADWORDS","name":"Autumn Games","industries":[{"id":65,"name":"Mobile Games","fullName":"Entertainment - Games-Video Games - Mobile Games","identifier":"ENTERTAINMENT:GAMES-VIDEO_GAMES:MOBILE_GAMES","parentId":33}],"processingComplete":true}',
          featureFlags:
            '{"isNudgeFeatureEnabled":true,"isSafeZoneOverlayEnabled":false,"isExcludeAccountAverageInFBCustomComparison":false,"isPlatformBestPracticesEnabled":false,"isLocationDropdownEnabled":false,"isDeveloperProjectCreateEnabled":false,"isDurationRowViewInsightCreativeExamplesEnabled":false,"isBetaIntegrationsEnabled":false,"isAppsflyerBetaEnabled":false,"isDurationReportKeyDataEnabled":true,"isSsoLoginEnabled":false,"isScoreFeedbackFlagEnabled":false,"isRequestNewDraftEnabled":false,"isImpressionsFilterEnabled":false,"isIndividualViewKpiEnabled":true,"isAssetFirstViewEnabled":false,"isCriteriaManagementGroupingEnabled":false,"isUploadBarProgressEnabled":true,"isPreFlightComplianceCriteriaAndBatchesEnabled":true,"isAdAccountIndustryEnabled":true,"isBatchResubmissionEnabled":true,"isJnJCriteriaEnabled":false,"isBrandGovIndividualAssetCsvButtonEnabled":false,"isInsightsLibraryEnabled":true,"isEnrichedFeedProjectTypeEnabled":false,"isVMPerformanceReportEnabled":false,"isCreateInsightsPanelEnabled":true,"isWatchBoardAmazonAdvertisingEnabled":false,"isProjectCreateSelfManagedEnabled":false,"isActiveProjectsWidgetEnabled":true,"isAmazonAdvertisingEnabled":false,"isInsightLibraryEnabled":false,"isAccountSharingPartnerListEnabled":true,"isBrandGovDatePickerEnabled":false,"isIndividualAssetViewEnabled":true,"isElementPresenceReportEnabled":false,"isAggregateCriteriaScoreEnabled":false}',
          'intercom.intercom-state-kam5unzd-kam5unzd':
            '{"app":{"color":"#333333","secondaryColor":"#333333","selfServeSuggestionsMatch":false,"name":"VidMob [TEST]","features":{"anonymousInboundMessages":true,"googleAnalytics":false,"hubspotInstalled":false,"inboundMessages":true,"marketoEnrichmentInstalled":false,"outboundMessages":true},"launcherLogoUrl":null,"boundWebEvents":[],"inboundConversationsDisabled":false,"isInstantBootEnabled":true,"alignment":"right","horizontalPadding":20,"verticalPadding":20,"isDeveloperWorkspace":false,"customGoogleAnalyticsTrackerId":null},"launcher":{"isLauncherEnabled":true},"launcherDiscoveryMode":{"hasDiscoveredLauncher":true},"user":{"role":"user","locale":"en","hasConversations":false},"message":{},"conversations":{"byId":{}},"openOnBoot":{"type":null,"metadata":{}},"operator":{"lastComposerEvent":0}}',
        },
        sessionInfo: {
          sessionId: '0151b95a-7cdd-4de1-8c7d-2bebeb136409',
          accessToken: 'HO/cvab8+uvphBQjhSvnYlumG1RCWzDCxzbUbHQlODU=',
          expiresTimestamp: *************,
          store: 'localStorage',
        },
        clientToldToLogout: false,
      },
      baseApiUrl: 'https://api-analytics-dev.vidmob.com',
      apiVersion: null,
      lastTokenStored: 'HO/cvab8+uvphBQjhSvnYlumG1RCWzDCxzbUbHQlODU=',
    },
    url: '/api/v1/platformMedia/:platformMediaId?includeMedia=true',
    tokenRequired: true,
  },
  requestParams: {
    platformMediaId: '***************',
  },
  debug: {},
  data: {
    id: 19547,
    mediaId: 150270,
    platform: 'BACKGROUND_FACEBOOK_AD_ACCOUNT',
    platformMediaId: '***************',
    platformAccountId: 'act_398662667005103',
    media: {
      id: 150270,
      name: '52686503_471133457901455_2518299738889439575_n.mp4',
      displayName: 'finserv-stand-out-gif.mp4',
      downloadUrl:
        'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/52686503_471133457901455_2518299738889439575_n.mp4?response-content-disposition=attachment%3B%20filename%3D52686503_471133457901455_2518299738889439575_n.mp4&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=530b201131cb878e3afde81ef678ad677645fb59e9a2397e901353d75e227571',
      mediaType: 'BACKGROUND_PLATFORM',
      fileType: 'VIDEO',
      processingState: 'COMPLETE',
      width: 1280,
      height: 720,
      duration: 6.08,
      thumbnails: [
        {
          height: 360,
          width: 640,
          url: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/304401_thumb_640_52686503_471133457901455_2518299738889439575_n.jpg?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=2a359a466417f534ca45f311a158b534e0ec3b07f434e278e373ca567d710072',
        },
        {
          height: 422,
          width: 750,
          url: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/304402_thumb_750_52686503_471133457901455_2518299738889439575_n.jpg?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=c5a81d4bdf4017a352f48f2a5c1dcd2a09d52bceba25630239886a026cb432a5',
        },
        {
          height: 699,
          width: 1242,
          url: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/304403_thumb_1242_52686503_471133457901455_2518299738889439575_n.jpg?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=d7598212c95afd0768049fd74751c190f1adfc03f5d98a9f6f9562de9fa970b8',
        },
      ],
      streams: {
        wifi: 'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/stream/hls_wifi_partial_40721fccb7b9ec6fbc5b26cff9d3f39abff8e626e019f879eb51753a1b68e209.m3u8?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=c24bf1ce1d149bdaf312d3cae0927835291ab297889e91aa13f022322fbc23e7',
        cellular:
          'https://vidmob-storage-dev.s3.amazonaws.com/VMR6VRJW4U/platformMedia/19547/stream/hls_cellular_partial_40721fccb7b9ec6fbc5b26cff9d3f39abff8e626e019f879eb51753a1b68e209.m3u8?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEEEaCXVzLWVhc3QtMSJHMEUCIEUyba7zAGzcnp8ts%2Bt7Ydtvlh8G%2F7xVdSx7bBJmv3XTAiEA%2FXFMVQLZh88Q2BIe%2B7UcGZck7Sx2Vfm%2FNIt5Qp5OplUq0wQIGRAEGgw0NTE2OTAzMDQ4MTUiDLz9mgmIFpSIskPRuyqwBPqiuX1aLAzn2feP4d0LbjbHTXg0jn1rFQY9WSVqkDwD5nbSos%2BVMmrNlD4Z%2FIxzhtILBAzz9zsu5remUUu3U7hX1EPzBB7Yi92B2w1Ux8O4x7t8kT0J8X%2F%2BbAvdt95vKVKDqH69HVzUKa0xiX5wwKqA%2BQ431yJk23O0%2BrmUu5rj1sTOvJIkS34Yzd9S25PfRLTwtolR%2Fa%2FS7y5pqEHbrZnndUNvDuQQckShZ9kFMWhM3yWKIzAMCrEXQFK%2BDMRfMDdzHW%2FTTzekH2vWT%2BGK502%2BW7nX863ZZTPXxBHgI1qEczObsgPD4Ia4Tc3LlFsUSqQU6Ra5zALr%2FZRq6RyU3PIo8ojkbGx%2FARGW%2FN6mv5ng6Vq7c5EbtJmabZbBRjdMO2yqHsyfkdfGunJDrjoKSumS58d7z8MMA%2BHOeTagbSbIQhaZfsmiMEhjIj67%2BCCJKLfynEZXxxTneuqkPnRubfCalJylnA83v9VhEIQrgEhp6UMLFDYnWts9UrcCMva6xzFaSOumyWTXn4fwSqQYhWYBrHnGfi08iMTDOi4FhldFS5vz5%2FgOP1e64pdJo84QHJTMCffiGFCmyv1VKZuhwpfKaHBzFD8lhwdhBthbO9eybCfW5FiRfIs9FWQah9EgYxZRmVpp7E3r1Z75PDqflH82P76aCo8drzcQHK1q%2BSTkMLSWNhPu7Kc0pvYN9QYDG2w3d%2Brf5cLrWtbUN8IYtv2OBuLCcLfBd975C6f%2Fb9aaMMaf6pMGOqkBBe%2BJV6t8yIIs6SIQKy7SqYIVYKvL6vhAWN4ZHW%2FqiBQCA%2BMUe7bP8BmEpjcQxCz%2Bpwxk%2BL77jNBHsoOl3Z%2FcY3lt3fR%2FekM3Qu0Eu3MgsrIkmEbSjmn0TEE7alVxsP4y6qfWMuEWt0p5eEWilJLSG19%2FGlu12%2BCWnAPyz7d0zM1wU%2BjSfSXIZDs96ysxuUqnPJZ5OKjmPsHUycj26qJlExWR9eGpVOopog%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220510T164906Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=ASIAWSKWXCUXXJXPE6RY%2F20220510%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=4f00a69df839b9c27652c9b52c7ed087a89f50d0cd1b4a71a590f7265cdaa8c5',
      },
    },
    tags: {},
    createdByVidmob: false,
  },
};

const mockQueryPlatformMediasResultSuccess = {
  [mockPlatformMediaId]: mockQueryPlatformMediaResultSuccess.data,
};

const mockQueryPlatformMediaResultFailure = {
  status: 'failure',
  apiConnection: {
    vmApiManager: {
      sessionManager: {
        sessionIdStorageName: 'vm_session',
        localStorage: 'localStorage',
        sessionStorage: 'sessionStorage',
        storageObj: {
          partnerId: '29732',
          'jwplayer.bandwidthEstimate': '107375970.06102017',
          'intercom.intercom-state-kam5unzd':
            '{"app":{"color":"#333333","secondaryColor":"#333333","selfServeSuggestionsMatch":false,"name":"VidMob [TEST]","features":{"anonymousInboundMessages":true,"googleAnalytics":false,"hubspotInstalled":false,"inboundMessages":true,"marketoEnrichmentInstalled":false,"outboundMessages":true},"launcherLogoUrl":null,"boundWebEvents":[],"inboundConversationsDisabled":false,"isInstantBootEnabled":true,"alignment":"right","horizontalPadding":20,"verticalPadding":20,"isDeveloperWorkspace":false,"customGoogleAnalyticsTrackerId":null},"launcher":{"isLauncherEnabled":true},"launcherDiscoveryMode":{"hasDiscoveredLauncher":true},"user":{"role":"user","locale":"en","hasConversations":false},"message":{},"conversations":{"byId":{}},"openOnBoot":{"type":null,"metadata":{}},"operator":{"lastComposerEvent":0}}',
          'jwplayer.mute': 'false',
          'platform-account': '{"id":159,"type":"GROUP","platform":"FACEBOOK"}',
          tusSupport: 'null',
          vm_session:
            '{"sessionId":"0151b95a-7cdd-4de1-8c7d-2bebeb136409","accessToken":"HO/cvab8+uvphBQjhSvnYlumG1RCWzDCxzbUbHQlODU=","expiresTimestamp":*************,"store":"localStorage"}',
          'jwplayer.volume': '17.**************',
          jwplayerLocalId: 'lme5sd1vq9ci',
          'dashboard-platform-account':
            '{"id":"**********","type":"ACCOUNT","platform":"ADWORDS","name":"Autumn Games","industries":[{"id":65,"name":"Mobile Games","fullName":"Entertainment - Games-Video Games - Mobile Games","identifier":"ENTERTAINMENT:GAMES-VIDEO_GAMES:MOBILE_GAMES","parentId":33}],"processingComplete":true}',
          featureFlags:
            '{"isNudgeFeatureEnabled":true,"isSafeZoneOverlayEnabled":false,"isExcludeAccountAverageInFBCustomComparison":false,"isPlatformBestPracticesEnabled":false,"isLocationDropdownEnabled":false,"isDeveloperProjectCreateEnabled":false,"isDurationRowViewInsightCreativeExamplesEnabled":false,"isBetaIntegrationsEnabled":false,"isAppsflyerBetaEnabled":false,"isDurationReportKeyDataEnabled":true,"isSsoLoginEnabled":false,"isScoreFeedbackFlagEnabled":false,"isRequestNewDraftEnabled":false,"isImpressionsFilterEnabled":false,"isIndividualViewKpiEnabled":true,"isAssetFirstViewEnabled":false,"isCriteriaManagementGroupingEnabled":false,"isUploadBarProgressEnabled":true,"isPreFlightComplianceCriteriaAndBatchesEnabled":true,"isAdAccountIndustryEnabled":true,"isBatchResubmissionEnabled":true,"isJnJCriteriaEnabled":false,"isBrandGovIndividualAssetCsvButtonEnabled":false,"isInsightsLibraryEnabled":true,"isEnrichedFeedProjectTypeEnabled":false,"isVMPerformanceReportEnabled":false,"isCreateInsightsPanelEnabled":true,"isWatchBoardAmazonAdvertisingEnabled":false,"isProjectCreateSelfManagedEnabled":false,"isActiveProjectsWidgetEnabled":true,"isAmazonAdvertisingEnabled":false,"isInsightLibraryEnabled":false,"isAccountSharingPartnerListEnabled":true,"isBrandGovDatePickerEnabled":false,"isIndividualAssetViewEnabled":true,"isElementPresenceReportEnabled":false,"isAggregateCriteriaScoreEnabled":false}',
          'intercom.intercom-state-kam5unzd-kam5unzd':
            '{"app":{"color":"#333333","secondaryColor":"#333333","selfServeSuggestionsMatch":false,"name":"VidMob [TEST]","features":{"anonymousInboundMessages":true,"googleAnalytics":false,"hubspotInstalled":false,"inboundMessages":true,"marketoEnrichmentInstalled":false,"outboundMessages":true},"launcherLogoUrl":null,"boundWebEvents":[],"inboundConversationsDisabled":false,"isInstantBootEnabled":true,"alignment":"right","horizontalPadding":20,"verticalPadding":20,"isDeveloperWorkspace":false,"customGoogleAnalyticsTrackerId":null},"launcher":{"isLauncherEnabled":true},"launcherDiscoveryMode":{"hasDiscoveredLauncher":true},"user":{"role":"user","locale":"en","hasConversations":false},"message":{},"conversations":{"byId":{}},"openOnBoot":{"type":null,"metadata":{}},"operator":{"lastComposerEvent":0}}',
        },
        sessionInfo: {
          sessionId: '0151b95a-7cdd-4de1-8c7d-2bebeb136409',
          accessToken: 'HO/cvab8+uvphBQjhSvnYlumG1RCWzDCxzbUbHQlODU=',
          expiresTimestamp: *************,
          store: 'localStorage',
        },
        clientToldToLogout: false,
      },
      baseApiUrl: 'https://api-analytics-dev.vidmob.com',
      apiVersion: null,
      lastTokenStored: 'HO/cvab8+uvphBQjhSvnYlumG1RCWzDCxzbUbHQlODU=',
    },
    url: '/api/v1/platformMedia/:platformMediaId?includeMedia=true',
    tokenRequired: true,
  },
  requestParams: {
    platformMediaId: '***************',
  },
  debug: {},
  data: {
    id: 19547,
    mediaId: 150270,
    platform: 'BACKGROUND_FACEBOOK_AD_ACCOUNT',
    platformMediaId: '***************',
    platformAccountId: 'act_398662667005103',
    createdByVidmob: false,
  },
};

const mockAdVideoDimension = 'advideo';

const mockLoadStatData = {
  getViewThroughRates: () => mockIndividualCreativeViewThroughData,
};

const mockPlatformAnalyticsService = {
  getImpressionMetricKey: () => mockImpressionMetricKey,
  loadStat: () => mockLoadStatData,
  getViewThroughKpis: () => {},
  getAdVideoDimension: () => mockAdVideoDimension,
  getAnalyticsPlatformName: () => mockPlatformForPostPlatformRequest,
};

/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///
///   Mock Expected States For Individual Creative View SLICE   ///
/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///

const expectedInitialState = {
  params: {
    accountId: null,
    accountType: 'account',
    platformMediaId: null,
    platform: null,
    analyticsStartDate: null,
    analyticsEndDate: null,
    kpi: null,
    organizationId: null,
    workspaceIds: null,
  },
  requestParamsV2: {
    accountType: ACCOUNT_ACCOUNT_TYPE.toLowerCase(),
    platformMediaId: null,
    mediaId: null,
    platform: null,
    startDate: null,
    endDate: null,
    kpi: null,
    kpiName: null,
    kpiFormat: null,
    workspaceIds: null,
    adAccountIds: null,
    adId: null,
    isCreativeOfMultiAssetAds: null,
    currency: null,
  },
  mediaData: {
    thumbnails: [],
    fileType: null,
  },
  selectedKpiId: null,
  tagData: [],
  mediaDataStatus: NOT_LOADED,
  data: {},
  mediaPerformance: {},
  mediaPerformanceStatus: NOT_LOADED,
  error: null,
};

const expectedStateAfterParamsData = {
  ...expectedInitialState,
  params: { ...mockIndividualCreativeViewParams },
  selectedKpiId: mockIndividualCreativeViewParams.kpi,
};

const expectedStateAfterMediaDataSuccess = {
  ...expectedInitialState,
  mediaData: {
    ...mockSuccessMediaData,
  },
  mediaDataStatus: SUCCESS,
};

const expectedStateAfterMediaDataFailure = {
  ...expectedInitialState,
  mediaDataStatus: FAILED,
};

const expectedStateAfterMediaDataPending = {
  ...expectedInitialState,
  mediaDataStatus: PENDING,
};

const expectedStateAfterMediaPerformanceDataSuccess = {
  ...expectedInitialState,
  mediaPerformance: {
    ...mockSuccessMediaPerformanceData,
  },
  mediaPerformanceStatus: SUCCESS,
};

const expectedStateAfterMediaPerformanceDataPending = {
  ...expectedInitialState,
  mediaPerformanceStatus: PENDING,
};

const expectedStateAfterMediaPerformanceDataFailure = {
  ...expectedInitialState,
  mediaPerformanceStatus: FAILED,
};

const mockDateRange = {
  analyticsEndDate: '2020-10-02',
  analyticsStartDate: '2022-02-26',
};

const expectedStateAfterSetDateRange = {
  ...expectedInitialState,
  params: {
    ...expectedInitialState.params,
    ...mockDateRange,
  },
};

/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///
///     Mock Expected States For Audience Engagement SLICE      ///
/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///

const expectedInitialStateForAudienceEngagement = {
  selectedBucket:
    INDIVIDUAL_CREATIVE_VIEW_AUDIENCE_ENGAGEMENT_BUCKET_HEADERS.MOST_VIEWERS,
};

const expectedStateAfterSelectBucket = {
  selectedBucket:
    INDIVIDUAL_CREATIVE_VIEW_AUDIENCE_ENGAGEMENT_BUCKET_HEADERS.SOME_VIEWERS,
};

/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///
///   Mock Expected States For Performance Over Time SLICE      ///
/// /// /// /// /// /// /// /// /// /// /// /// /// /// /// /// ///

const expectedInitialStateForPerformanceOverTime = {
  dailyPerformanceData: [],
  dailyPerformanceDataStatus: NOT_LOADED,
  accountAverage: {},
  accountAverageStatus: NOT_LOADED,
};

const expectedStateAfterDailyPerformanceDataPending = {
  ...expectedInitialStateForPerformanceOverTime,
  dailyPerformanceDataStatus: PENDING,
};

const expectedStateAfterDailyPerformanceDataSuccess = {
  ...expectedInitialStateForPerformanceOverTime,
  dailyPerformanceData: mockSuccessDailyPerformanceData,
  dailyPerformanceDataStatus: SUCCESS,
};

const expectedStateAfterDailyPerformanceDataFailure = {
  ...expectedInitialStateForPerformanceOverTime,
  dailyPerformanceDataStatus: FAILED,
};

const expectedStateAfterAccountAverageDataPending = {
  ...expectedInitialStateForPerformanceOverTime,
  accountAverageStatus: PENDING,
};

const expectedStateAfterAccountAverageDataSuccess = {
  ...expectedInitialStateForPerformanceOverTime,
  accountAverage: mockSuccessAccountAverageData,
  accountAverageStatus: SUCCESS,
};

const expectedStateAfterAccountAverageDataFailure = {
  ...expectedInitialStateForPerformanceOverTime,
  accountAverageStatus: FAILED,
};

export default {
  // Insert expected states for ICV here
  mockMediaPreviewPlayerData,
  expectedInitialState,
  expectedStateAfterParamsData,
  expectedStateAfterMediaDataSuccess,
  expectedStateAfterMediaDataPending,
  expectedStateAfterMediaDataFailure,
  expectedStateAfterMediaPerformanceDataSuccess,
  expectedStateAfterMediaPerformanceDataPending,
  expectedStateAfterMediaPerformanceDataFailure,
  // Insert expected states for Audience Engagement here
  expectedInitialStateForAudienceEngagement,
  expectedStateAfterSelectBucket,
  // Insert expected states for Performance Over Time here
  expectedInitialStateForPerformanceOverTime,
  expectedStateAfterDailyPerformanceDataPending,
  expectedStateAfterDailyPerformanceDataSuccess,
  expectedStateAfterDailyPerformanceDataFailure,
  expectedStateAfterAccountAverageDataSuccess,
  expectedStateAfterAccountAverageDataPending,
  expectedStateAfterAccountAverageDataFailure,
  // Insert mocks here
  mockPlatformMediaId,
  mockIndividualCreativeViewParams,
  mockAdVideoDimension,
  mockSuccessMediaData,
  mockAnalyticsQueryService,
  mockQueryPlatformMediaResultSuccess,
  mockQueryPlatformMediaResultFailure,
  mockSuccessMediaPerformanceData,
  mockError,
  mockPlatformAnalyticsService,
  mockPlatformForPostPlatformRequest,
  mockRequestBodyForPostPlatformData,
  mockPostPlatformDataSuccess,
  mockNoDataPostPlatformDataSuccess,
  mockImpressionMetricKey,
  mockFilters,
  mockLoadStatData,
  mockGetPlatformIndustryKpiNormData,
  mockNormativePlatformViewThroughData,
  mockNormativeIndustryViewThroughData,
  mockDateRange,
  expectedStateAfterSetDateRange,
  mockQueryPlatformMediasResultSuccess,
  mockMediaPerformanceDataStatus,
  mockMediaDataStatus,
  mockSuccessDailyPerformanceData,
  mockSuccessDailyPerformanceDataFormattedForGraph,
  mockSuccessAccountAverageData,
  mockSuccessAccountAverageFormattedForGraph,
  mockICVSelectedKpiObject,
};
