import swapObjectKeyToValue from '../swapObjectKeyToValue';

export const ELEMENT_TYPES = {
  MESSAGING: 'messaging',
  CELEBRITY: 'celebrity',
  OBJECT: 'object',
  COLOR: 'color',
  ELEMENT_SET: 'element set',
  GAZE_AND_EMOTION: 'gaze and emotion',
  CUSTOM_ELEMENT: 'custom element',
  BRANDING: 'branding',
  PRODUCTION: 'Production',
  SOUND: 'Sound',
  STORY_TELLING: 'Storytelling',
  TONE: 'tone',
};

export const TAG_TYPES = {
  LABEL: 'LABEL',
  LABEL_PARENT: 'LABEL:PARENT',
  LABEL_CUSTOM: 'LABEL:CUSTOM',
  FACE_GENDER: 'FACE:GENDER',
  FACE_EMOTION: 'FACE:EMOTION',
  FACE_SMILE: 'FACE:SMILE',
  FACE_GAZE_DIRECTION: 'FACE:GAZE_DIRECTION',
  CELEBRITY_NAME: 'CELEBRITY:NAME',
  TEXT_LINE_EMPHASIS: 'TEXT:LINE_EMPHASIS',
  TEXT_WORD_EMPHASIS: 'TEXT:WORD_EMPHASIS',
  TEXT_WORD: 'TEXT:WORD',
  TEXT_WORDS_PER_SECOND: 'TEXT:WORDS_PER_SECOND',
  LOGO: 'LOGO',
  COLOR_DOMINANT_COLOR_CATEGORY: 'COLOR:DOMINANT_COLORS:CATEGORY',
  COLOR_TEMPERATURE_CATEGORY: 'COLOR:TEMPERATURE:CATEGORY',
  COLOR_CONTRAST_CATEGORY: 'COLOR:CONTRAST:CATEGORY',
  COLOR_TEXT_CONTRAST_CATEGORY: 'COLOR:TEXT_CONTRAST:CATEGORY',
  COLOR_VIBRANCY_VALUE: 'COLOR:VIBRANCY:VALUE',
  COLOR_VIBRANCY_CATEGORY: 'COLOR:VIBRANCY:CATEGORY',
  SPEECH_WORDS_PER_SECOND: 'SPEECH:WORDS_PER_SECOND',
  TEXT_BOUNDING_BOX_PERCENT: 'TEXT:BOUNDING_BOX_PERCENT',
  CTA_BY_LINE: 'CTA:BY_LINE',
  CTA_BY_WORD: 'CTA:BY_WORD',
  CTA_TRANSCRIPTS: 'CTA:TRANSCRIPTS',
  APERTURE_MESSAGING: 'APERTURE:MESSAGING',
  APERTURE_MESSAGING_BENEFIT: 'APERTURE:MESSAGING:BENEFIT',
  APERTURE_MESSAGING_CTA: 'APERTURE:MESSAGING:CTA',
  APERTURE_PRODUCTION_LEVEL: 'APERTURE:PRODUCTION_LEVEL',
  APERTURE_PRODUCTION_LEVEL_LABEL: 'Production Level',
  APERTURE_PRODUCTION_TYPE: 'APERTURE:PRODUCTION_TYPE',
  APERTURE_PRODUCTION_TYPE_LABEL: 'Production Type',
  APERTURE_STORY_TYPE: 'APERTURE:STORY_TYPE',
  PRODUCTION_LEVEL_CLASSIFIER: 'PRODUCTION_LEVEL_CLASSIFIER',
  APERTURE_SOUND_TYPE: 'APERTURE:SOUND_TYPE',
  APERTURE_STRATEGIC_TONE: 'APERTURE:STRATEGIC_TONE',
};

export const TAG_ELEMENT_MAP = {
  [TAG_TYPES.LABEL]: ELEMENT_TYPES.OBJECT,
  [TAG_TYPES.LABEL_PARENT]: ELEMENT_TYPES.OBJECT,
  [TAG_TYPES.LABEL_CUSTOM]: ELEMENT_TYPES.OBJECT,
  [TAG_TYPES.FACE_GENDER]: ELEMENT_TYPES.GAZE_AND_EMOTION,
  [TAG_TYPES.FACE_EMOTION]: ELEMENT_TYPES.GAZE_AND_EMOTION,
  [TAG_TYPES.FACE_SMILE]: ELEMENT_TYPES.GAZE_AND_EMOTION,
  [TAG_TYPES.FACE_GAZE_DIRECTION]: ELEMENT_TYPES.GAZE_AND_EMOTION,
  [TAG_TYPES.CELEBRITY_NAME]: ELEMENT_TYPES.CELEBRITY,
  [TAG_TYPES.TEXT_LINE_EMPHASIS]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.TEXT_WORD_EMPHASIS]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.TEXT_WORD]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.TEXT_WORDS_PER_SECOND]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.TEXT_BOUNDING_BOX_PERCENT]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.SPEECH_WORDS_PER_SECOND]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.CTA_BY_WORD]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.LOGO]: ELEMENT_TYPES.BRANDING,
  [TAG_TYPES.COLOR_DOMINANT_COLOR_CATEGORY]: ELEMENT_TYPES.COLOR,
  [TAG_TYPES.COLOR_TEMPERATURE_CATEGORY]: ELEMENT_TYPES.COLOR,
  [TAG_TYPES.COLOR_CONTRAST_CATEGORY]: ELEMENT_TYPES.COLOR,
  [TAG_TYPES.COLOR_TEXT_CONTRAST_CATEGORY]: ELEMENT_TYPES.COLOR,
  [TAG_TYPES.COLOR_VIBRANCY_VALUE]: ELEMENT_TYPES.COLOR,
  [TAG_TYPES.COLOR_VIBRANCY_CATEGORY]: ELEMENT_TYPES.COLOR,
  [TAG_TYPES.CTA_BY_LINE]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.CTA_BY_WORD]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.CTA_TRANSCRIPTS]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.APERTURE_MESSAGING]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.APERTURE_MESSAGING_BENEFIT]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.APERTURE_MESSAGING_CTA]: ELEMENT_TYPES.MESSAGING,
  [TAG_TYPES.APERTURE_PRODUCTION_LEVEL]: ELEMENT_TYPES.PRODUCTION,
  [TAG_TYPES.APERTURE_PRODUCTION_LEVEL_LABEL]: ELEMENT_TYPES.PRODUCTION,
  [TAG_TYPES.PRODUCTION_LEVEL_CLASSIFIER]: ELEMENT_TYPES.PRODUCTION,
  [TAG_TYPES.APERTURE_PRODUCTION_TYPE]: ELEMENT_TYPES.PRODUCTION,
  [TAG_TYPES.APERTURE_PRODUCTION_TYPE_LABEL]: ELEMENT_TYPES.PRODUCTION,
  [TAG_TYPES.APERTURE_SOUND_TYPE]: ELEMENT_TYPES.SOUND,
  [TAG_TYPES.APERTURE_STORY_TYPE]: ELEMENT_TYPES.STORY_TELLING,
  [TAG_TYPES.APERTURE_STRATEGIC_TONE]: ELEMENT_TYPES.TONE,
};

export const COLOR_CONTRAST_NAME_MAP: Record<string, Record<string, string>> = {
  [TAG_TYPES.COLOR_CONTRAST_CATEGORY]: {
    low: 'Low Color Contrast',
    medium: 'Medium Color Contrast',
    high: 'High Color Contrast',
  },
  [TAG_TYPES.COLOR_TEMPERATURE_CATEGORY]: {
    cool: 'Cool Color Temperature',
    neutral: 'Neutral Color Temperature',
    warm: 'Warm Color Temperature',
  },
  [TAG_TYPES.COLOR_TEXT_CONTRAST_CATEGORY]: {
    low: 'Low Text Contrast',
    medium: 'Medium Text Contrast',
    high: 'High Text Contrast',
  },
};

export const TEXT_TAG_NAME_MAP: Record<string, string> = {
  [TAG_TYPES.SPEECH_WORDS_PER_SECOND]: 'Speech Words Per Second',
  [TAG_TYPES.TEXT_WORDS_PER_SECOND]: 'Text Words Per Second',
  [TAG_TYPES.TEXT_BOUNDING_BOX_PERCENT]: 'Text Density',
  [TAG_TYPES.TEXT_WORD_EMPHASIS]: 'Emphasis Word: ',
};

export const FACE_EMOTION_MAP: Record<string, string> = {
  CALM: 'Calm',
  SURPRISED: 'Surprised',
  HAPPY: 'Happy',
  FEAR: 'Fear',
  SAD: 'Sad',
};

export const FACE_GAZE_DIRECTION_MAP: Record<string, string> = {
  middle: 'Gaze Straight Ahead',
  left: 'Gaze Left',
  right: 'Gaze Right',
  'up-right': 'Gaze Up Right',
  'up-left': 'Gaze Up Left',
  up: 'Gaze Up',
  down: 'Gaze Down',
};

export const MIRRORED_FACE_GAZE_DIRECTION_MAP = swapObjectKeyToValue(
  FACE_GAZE_DIRECTION_MAP,
);

export const FACE_SMILE = 'Smile';

export const DOMINANT_COLOR_TAG_TYPES = [0, 1, 2, 3, 4, 5].map(
  (index) => `${TAG_TYPES.COLOR_DOMINANT_COLOR_CATEGORY}:${index}`,
);

export const TEXT_TAG_TYPES = [
  TAG_TYPES.SPEECH_WORDS_PER_SECOND,
  TAG_TYPES.TEXT_WORDS_PER_SECOND,
];

export const PROCESSABLE_TAG_TYPES = [
  TAG_TYPES.TEXT_WORD_EMPHASIS,
  TAG_TYPES.TEXT_WORD,
  TAG_TYPES.LOGO,
  TAG_TYPES.CELEBRITY_NAME,
  TAG_TYPES.FACE_SMILE,
  TAG_TYPES.FACE_EMOTION,
  TAG_TYPES.LABEL,
  TAG_TYPES.FACE_GAZE_DIRECTION,
  TAG_TYPES.LABEL_CUSTOM,
  TAG_TYPES.COLOR_VIBRANCY_CATEGORY,
  TAG_TYPES.COLOR_TEMPERATURE_CATEGORY,
  TAG_TYPES.COLOR_CONTRAST_CATEGORY,
  TAG_TYPES.COLOR_TEXT_CONTRAST_CATEGORY,
  TAG_TYPES.CTA_BY_WORD,
  TAG_TYPES.SPEECH_WORDS_PER_SECOND,
  ...DOMINANT_COLOR_TAG_TYPES,
];
