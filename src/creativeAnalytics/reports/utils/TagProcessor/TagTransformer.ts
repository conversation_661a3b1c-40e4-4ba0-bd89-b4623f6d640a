import { Tag, TagClip, TagData } from './TagTransformer.types';
import {
  COLOR_CONTRAST_NAME_MAP,
  DOMINANT_COLOR_TAG_TYPES,
  FACE_EMOTION_MAP,
  FACE_GAZE_DIRECTION_MAP,
  FACE_SMILE,
  TAG_TYPES,
  TEXT_TAG_NAME_MAP,
} from './tagConstants';
import ClipMap from './ClipMap';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';

interface TagTransformerOptions {
  minimumConfidence: number;
  overlapBuffer: number;
  minimumColorValue: number;
}

type ValueFnType<ValueType> =
  | ValueType
  | ((a: ValueType, b: ValueType) => ValueType);

export const TagTransformerDefaultOptions = {
  minimumConfidence: 75,
  overlapBuffer: 0.5,
  minimumColorValue: 0.6,
};

const isMessagingApertureEnabled = getFeatureFlag('isMessagingApertureEnabled');
class TagTransformer {
  private readonly tags: TagData = {};

  // @ts-ignore
  private elements: Record<string, Record<string, TagGroup>>;

  // @ts-ignore
  private options: TagTransformerOptions;

  constructor(tags: TagData, options?: TagTransformerOptions) {
    this.tags = tags;
    this.options = options
      ? { ...options, ...TagTransformerDefaultOptions }
      : TagTransformerDefaultOptions;
  }

  getOptions() {
    return this.options;
  }

  getTags() {
    return this.tags;
  }

  getAllTagsClipMaps(): Record<string, ClipMap<number> | ClipMap<undefined>> {
    return {
      ...this.getDominantColorsClipMaps(),
      ...this.getColorContrastTagsClipMaps(),
      ...this.getSpeechWordsPerSecondClipMaps(),
      ...this.getTextWordsPerSecondClipMaps(),
      ...this.getTextDensityClipMaps(),
      ...this.getEmphasisWordsClipMaps(),
      ...this.getCelebrityTagsClipMaps(),
      ...this.getLabelTagsClipMaps(),
      ...this.getFaceTagsClipsMaps(),
      ...this.getLogoTagsClipMaps(),
      ...this.getCustomLabelsClipMaps(),
      ...this.getCTAClipMaps(),
      ...(isMessagingApertureEnabled
        ? {
            ...this.getApertureBenefitMessagingTagsClipMaps(),
            ...this.getApertureCTAMessagingTagsClipMaps(),
            ...this.getApertureProductionTypeClipMaps(),
            ...this.getApertureProductionLevelClipMaps(),
            ...this.getApertureStoryTypeClipMaps(),
            ...this.getAudioTypeClipMaps(),
            ...this.getApertureStrategicToneClipMaps(),
          }
        : {}),
      ...this.getProductionLevelClassifierClipMaps(),
    };
  }

  getAllTagsAsSortedArray() {
    return Object.values(this.getAllTagsClipMaps())
      .map((clipMap) => {
        return {
          title: clipMap.title,
          elementType: clipMap.elementType,
          isWeighted: clipMap.isWeighted,
          maxValue: clipMap.maxValue,
          clips: clipMap.getBucketedClips(),
        };
      })
      .sort((a, b) => {
        const firstClipA = a.clips[0]?.startTime;
        const firstClipB = b.clips[0]?.startTime;
        return firstClipA - firstClipB;
      })
      .sort((a, b) => {
        const firstClipA = a.clips[0];
        const firstClipB = b.clips[0];
        const aDefined = firstClipA === null || firstClipA === undefined;
        const bDefined = firstClipB === null || firstClipB === undefined;
        if (bDefined && !aDefined) {
          return -1;
        } else if (aDefined && !bDefined) {
          return 1;
        }
        return 1;
      });
  }

  testClipOverlap(
    a: TagClip<unknown>,
    b: TagClip<unknown>,
    overlapBuffer?: number,
  ) {
    const overlapBufferDefined = (overlapBuffer ||
      this.options.overlapBuffer) as number;
    return a.startTime + a.duration + overlapBufferDefined >= b.startTime;
  }

  getMergeOverlappingClips<ValueType>(
    valueFn?: ValueFnType<ValueType>,
    confidence?: number,
  ) {
    return (a: TagClip<ValueType>, b: TagClip<ValueType>) => {
      let value: ValueType;
      if (typeof valueFn === 'undefined') {
        value = a.value;
      } else if (typeof valueFn === 'function') {
        const getValue = valueFn as (a: ValueType, b: ValueType) => ValueType;
        value = getValue(a.value, b.value);
      } else {
        value = valueFn;
      }

      return {
        startTime: a.startTime,
        duration: b.startTime - a.startTime + b.duration,
        confidence: confidence || a.confidence,
        value,
      };
    };
  }

  processTags(
    tags: Tag[],
    elementType: string,
    options: {
      getName: (tag: Tag) => string;
    },
  ) {
    return tags.reduce((acc: Record<string, ClipMap<undefined>>, tag) => {
      const name = options.getName(tag);

      if (tag.confidence && tag.confidence < this.options.minimumConfidence) {
        return acc;
      }

      if (!acc[name]) {
        acc[name] = new ClipMap<undefined>(name, elementType, {
          shouldMergeClips: this.testClipOverlap.bind(this),
          mergeClips: this.getMergeOverlappingClips(undefined, tag.confidence),
        });
      }

      const clipMap = acc[name];
      tag.clips.forEach((clip) => {
        clipMap.addClip({
          startTime: clip.startTime,
          duration: clip.duration,
          value: undefined,
          confidence: tag.confidence,
        });
      });

      return acc;
    }, {});
  }

  processWeightedTags<ValueType>(
    tags: Tag[],
    elementType: string,
    options: {
      getName: (tag: Tag) => string;
      getValue: (tag: Tag) => ValueType;
      testValues: (a: TagClip<ValueType>, b: TagClip<ValueType>) => boolean;
    },
  ) {
    return tags.reduce((acc: Record<string, ClipMap<ValueType>>, tag) => {
      const name = options.getName(tag);

      if (!acc[name]) {
        acc[name] = new ClipMap<ValueType>(name, elementType, {
          shouldMergeClips: (a, b) => {
            return this.testClipOverlap(a, b) && options.testValues(a, b);
          },
          mergeClips: this.getMergeOverlappingClips((a, _b) => a),
        });
      }

      tag.clips.forEach((clip) => {
        const value = options.getValue(tag);

        if (value) {
          acc[name].addClip({
            startTime: clip.startTime,
            duration: clip.duration,
            value,
          });
        }
      });

      return acc;
    }, {});
  }

  // Dominant Colors
  private getDominantColorTags(): Tag[] {
    return DOMINANT_COLOR_TAG_TYPES.reduce((acc: Tag[], tagType: string) => {
      if (this.tags[tagType]) {
        return [...acc, ...this.tags[tagType]];
      }

      return acc;
    }, []);
  }

  getDominantColorsClipMaps(): Record<string, ClipMap<number>> {
    const tags = this.getDominantColorTags();
    const colors: Record<string, ClipMap<number>> = {};
    tags.forEach((tagData) => {
      const [name, value] = tagData.value.split(':');
      let tagValue = 0;
      try {
        tagValue = parseFloat(value);
      } catch {}

      if (tagValue < this.options.minimumColorValue) {
        return false;
      }

      const tagName = name
        .split('_')
        .map((str) => str.charAt(0).toUpperCase() + str.substring(1))
        .join(' ');

      if (!colors[tagName]) {
        colors[tagName] = new ClipMap<number>(
          tagName,
          TAG_TYPES.COLOR_DOMINANT_COLOR_CATEGORY,
          {
            shouldMergeClips: this.testClipOverlap.bind(this),
            mergeClips: this.getMergeOverlappingClips<number>((a, b) =>
              Math.max(a, b),
            ),
          },
        );
      }

      const clipMap = colors[tagName];
      tagData.clips.forEach((clip) => {
        clipMap.addClip({
          ...clip,
          value: tagValue,
        });
      });

      return true;
    });

    return colors;
  }

  // Color Contrast Tags
  getColorContrastTagsClipMaps(): Record<string, ClipMap<undefined>> {
    return [
      TAG_TYPES.COLOR_CONTRAST_CATEGORY,
      TAG_TYPES.COLOR_TEMPERATURE_CATEGORY,
      TAG_TYPES.COLOR_TEXT_CONTRAST_CATEGORY,
    ].reduce((acc: Record<string, ClipMap<undefined>>, tagType) => {
      return {
        ...acc,
        ...this.processTags(this.tags[tagType] || [], tagType, {
          getName: (tag) => COLOR_CONTRAST_NAME_MAP[tag.type][tag.value],
        }),
      };
    }, {});
  }

  getSpeechWordsPerSecondClipMaps(): Record<string, ClipMap<number>> {
    const speechWordsPerSecondTags =
      this.tags[TAG_TYPES.SPEECH_WORDS_PER_SECOND] || [];

    return this.processWeightedTags<number>(
      speechWordsPerSecondTags,
      TAG_TYPES.SPEECH_WORDS_PER_SECOND,
      {
        getName: (tag) => TEXT_TAG_NAME_MAP[tag.type],
        getValue: (tag) => Number(tag.value),
        testValues: (a, b) => a.value === b.value,
      },
    );
  }

  getTextWordsPerSecondClipMaps(): Record<string, ClipMap<number>> {
    const textWordsPerSecondTags =
      this.tags[TAG_TYPES.TEXT_WORDS_PER_SECOND] || [];

    return this.processWeightedTags<number>(
      textWordsPerSecondTags,
      TAG_TYPES.TEXT_WORDS_PER_SECOND,
      {
        getName: (tag) => TEXT_TAG_NAME_MAP[tag.type],
        getValue: (tag) => Number(tag.value),
        testValues: (a, b) => a.value === b.value,
      },
    );
  }

  getTextDensityClipMaps(): Record<string, ClipMap<number>> {
    const textBoundingBoxTags =
      this.tags[TAG_TYPES.TEXT_BOUNDING_BOX_PERCENT] || [];

    return this.processWeightedTags<number>(
      textBoundingBoxTags,
      TAG_TYPES.TEXT_BOUNDING_BOX_PERCENT,
      {
        getName: (tag) => TEXT_TAG_NAME_MAP[tag.type],
        getValue: (tag) => Number(tag.value),
        testValues: (a, b) => a.value === b.value,
      },
    );
  }

  // Emphasis Words
  getEmphasisWordsClipMaps(): Record<string, ClipMap<undefined>> {
    const tags = this.tags[TAG_TYPES.TEXT_WORD_EMPHASIS] || [];

    return this.processTags(tags, TAG_TYPES.TEXT_WORD_EMPHASIS, {
      getName: (tag) =>
        TEXT_TAG_NAME_MAP[TAG_TYPES.TEXT_WORD_EMPHASIS] + tag.value,
    });
  }

  // Celebrity Tags (CELEBRITY:NAME)
  getCelebrityTagsClipMaps(): Record<string, ClipMap<undefined>> {
    const celebritiesTags = this.tags[TAG_TYPES.CELEBRITY_NAME] || [];

    return this.processTags(celebritiesTags, TAG_TYPES.CELEBRITY_NAME, {
      getName: (tag) => tag.value,
    });
  }

  // Label Tags
  getLabelTagsClipMaps(): Record<string, ClipMap<undefined>> {
    const labelTags = this.tags[TAG_TYPES.LABEL] || [];

    return this.processTags(labelTags, TAG_TYPES.LABEL, {
      getName: (tag) => tag.value,
    });
  }

  // Face Tags (FACE:EMOTION, FACE:SMILE, FACE:GAZE_DIRECTION)
  getFaceTagsClipsMaps(): Record<string, ClipMap<undefined>> {
    const emotionTags = this.tags[TAG_TYPES.FACE_EMOTION] || [];

    // FACE:SMILE tags are saved as an array of two objects (one with value: true, one with value: false). Only using 'true' ones.
    const smileTags =
      (this.tags[TAG_TYPES.FACE_SMILE] || []).filter((tag) => {
        return tag.value === 'true';
      }) || [];

    const gazeTags = this.tags[TAG_TYPES.FACE_GAZE_DIRECTION] || [];

    const tags = [...smileTags, ...emotionTags, ...gazeTags];
    const faces: Record<string, ClipMap<undefined>> = {};

    tags
      .filter((tag) => {
        // filter out tags that are below minimumConfidence
        const confidence = tag.confidence || 0;
        return confidence > this.options.minimumConfidence;
      })
      .forEach((tag) => {
        let name;
        if (tag.type === TAG_TYPES.FACE_SMILE) {
          name = FACE_SMILE;
        } else if (tag.type === TAG_TYPES.FACE_EMOTION) {
          name = FACE_EMOTION_MAP[tag.value];
        } else {
          name = FACE_GAZE_DIRECTION_MAP[tag.value];
        }

        if (!name) {
          return false;
        }

        if (!faces[name]) {
          const clipMap = new ClipMap<undefined>(name, tag.type, {
            shouldMergeClips: this.testClipOverlap.bind(this),
            mergeClips: this.getMergeOverlappingClips(
              undefined,
              tag.confidence,
            ),
          });
          faces[name] = clipMap;
        }

        const clipMap = faces[name];
        tag.clips.forEach((clip) => {
          clipMap.addClip({
            startTime: clip.startTime,
            duration: Math.max(clip.duration, 0.5), // if duration is < 0.5, default to 0.5.
            value: undefined,
            confidence: tag.confidence,
          });
        });
        return true;
      });

    return faces;
  }

  // Logos
  getLogoTagsClipMaps(): Record<string, ClipMap<undefined>> {
    const logoTags = this.tags[TAG_TYPES.LOGO] || [];
    return this.processTags(logoTags, TAG_TYPES.LOGO, {
      getName: (tag) => tag.value,
    });
  }

  // Custom Labels
  getCustomLabelsClipMaps(): Record<string, ClipMap<undefined>> {
    const customLabelTags = this.tags[TAG_TYPES.LABEL_CUSTOM] || [];
    return this.processTags(customLabelTags, TAG_TYPES.LABEL_CUSTOM, {
      getName: (tag) => tag.value,
    });
  }

  // CTA
  getCTAClipMaps(): Record<string, ClipMap<undefined>> {
    const ctaTags = this.tags[TAG_TYPES.CTA_BY_LINE] || [];
    return this.processTags(ctaTags, TAG_TYPES.CTA_BY_LINE, {
      getName: (tag) => 'CTA: ' + tag.value,
    });
  }

  // Aperture
  getApertureBenefitMessagingTagsClipMaps(): Record<
    string,
    ClipMap<undefined>
  > {
    const labelTags = this.tags[TAG_TYPES.APERTURE_MESSAGING_BENEFIT] || [];
    return this.processTags(labelTags, TAG_TYPES.APERTURE_MESSAGING_BENEFIT, {
      getName: (tag) => tag.value,
    });
  }

  getApertureCTAMessagingTagsClipMaps(): Record<string, ClipMap<undefined>> {
    const labelTags = this.tags[TAG_TYPES.APERTURE_MESSAGING_CTA] || [];
    return this.processTags(labelTags, TAG_TYPES.APERTURE_MESSAGING_CTA, {
      getName: (tag) => tag.value,
    });
  }

  getApertureProductionTypeClipMaps(): Record<string, ClipMap<undefined>> {
    const labelTags = this.tags[TAG_TYPES.APERTURE_PRODUCTION_TYPE] || [];
    return {
      ...this.processTags(labelTags, TAG_TYPES.APERTURE_PRODUCTION_TYPE, {
        getName: (tag) => tag.value,
      }),
      ...this.processTags(labelTags, TAG_TYPES.APERTURE_PRODUCTION_TYPE_LABEL, {
        getName: (tag) => tag.value,
      }),
    };
  }

  getProductionLevelClassifierClipMaps(): Record<string, ClipMap<undefined>> {
    const labelTags = this.tags[TAG_TYPES.PRODUCTION_LEVEL_CLASSIFIER] || [];
    return this.processTags(labelTags, TAG_TYPES.PRODUCTION_LEVEL_CLASSIFIER, {
      getName: (tag) => tag.value,
    });
  }

  getApertureProductionLevelClipMaps(): Record<string, ClipMap<undefined>> {
    const labelTags = this.tags[TAG_TYPES.APERTURE_PRODUCTION_LEVEL] || [];
    return {
      ...this.processTags(labelTags, TAG_TYPES.APERTURE_PRODUCTION_LEVEL, {
        getName: (tag) => tag.value,
      }),
      ...this.processTags(
        labelTags,
        TAG_TYPES.APERTURE_PRODUCTION_LEVEL_LABEL,
        {
          getName: (tag) => tag.value,
        },
      ),
    };
  }

  getAudioTypeClipMaps(): Record<string, ClipMap<undefined>> {
    const labelTags = this.tags[TAG_TYPES.APERTURE_SOUND_TYPE] || [];
    return this.processTags(labelTags, TAG_TYPES.APERTURE_SOUND_TYPE, {
      getName: (tag) => tag.value,
    });
  }

  getApertureStoryTypeClipMaps(): Record<string, ClipMap<undefined>> {
    const labelTags = this.tags[TAG_TYPES.APERTURE_STORY_TYPE] || [];
    return this.processTags(labelTags, TAG_TYPES.APERTURE_STORY_TYPE, {
      getName: (tag) => tag.value,
    });
  }
  getApertureStrategicToneClipMaps(): Record<string, ClipMap<undefined>> {
    const toneTags = this.tags[TAG_TYPES.APERTURE_STRATEGIC_TONE] || [];
    return this.processTags(toneTags, TAG_TYPES.APERTURE_STRATEGIC_TONE, {
      getName: (tag) => tag.value,
    });
  }
}

export default TagTransformer;
