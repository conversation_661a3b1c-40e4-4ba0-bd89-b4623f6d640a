interface ErrorResponse {
  message: string;
  name: string;
  stack: string;
  config: RequestConfig;
}

interface RequestConfig {
  url: string;
  method: string;
  headers: {
    Accept: string;
    Authorization: string;
  };
  params: {
    partnerId: number;
  };
  baseURL: string;
  transformRequest: Array<null>;
  transformResponse: Array<null>;
  timeout: number;
  xsrfCookieName: string;
  xsrfHeaderName: string;
  maxContentLength: number;
  maxBodyLength: number;
  transitional: {
    silentJSONParsing: boolean;
    forcedJSONParsing: boolean;
    clarifyTimeoutError: boolean;
  };
}

interface ResponseData {
  status: string;
  traceId: string;
  error: {
    identifier: string;
    system: string;
    message: string;
    type: string;
  };
}

export interface ResponseError {
  identifier: string;
  system: string;
  message: string;
  type: string;
}

export interface APIErrorObject {
  baseError: ErrorResponse;
  responseStatus: number;
  responseData: ResponseData;
  responseError: ResponseError;
  responseErrorCode: string;
  responseErrorData: string;
  responseErrorMessage: string;
  message: string;
  status: number;
}
