import { combineReducers } from 'redux';
import { RESET } from '../action.types';
import localeReducer from './../reducers/locale.reducer';
import resetReducer from './../reducers/reset.reducer';
import uploadReducer from './../reducers/upload.reducer';
import confirmationBarReducer from '../reducers/confirmationBar.reducer';
import confirmationModalReducer from '../reducers/modal.confirmation.reducer';
import userReducer from './../reducers/user.reducer';
import jwPlayerReducer from './../reducers/jwplayer.reducer';
import webSocketReducer from './../reducers/webSocket.reducer';
import projectReducer from '../reducers/project.reducer';
import projectOutputsReducer from '../reducers/projectOutputs.reducer';
import projectOutputsReducerV2 from '../reducers/projectOutputs.v2.reducer';
import intercomTourReducer from '../reducers/intercomTour.reducer';
import mediaPreviewReducer from '../reducers/mediaPreview.reducer';
import inviteModalReducer from '../reducers/modal.invite.reducer';
import deepLinkReducer from '../reducers/deepLink.reducer';
import adAccountsReducer from '../reducers/adAccounts.reducer';
import platformScopesReducer from '../reducers/platformScopes.reducer';
import subscriptionsReducer from '../reducers/subscriptions.reducer';
import featureFlagsReducer from '../reducers/featureFlags.reducer';
import analyticsUserPreferencesReducer from '../reducers/analyticsUserPreferences.reducer';
import industryReducer from '../reducers/industry.reducer';
import partnerProjectsReducer from '../reducers/partnerProjects.reducer';
import projectMediaDataSlice from '../slices/projectMediaData.slice';
import projectMediaFolderDataSlice from '../slices/projectMediaFolderData.slice';
import projectStockMediaDataSlice from '../slices/projectStockMediaData.slice';
import projectStockMediaFolderDataSlice from '../slices/projectStockMediaFolderData.slice';
import projectPartnerMediaDataSlice from '../slices/projectPartnerMediaData.slice';
import projectPartnerFolderDataSlice from '../slices/projectPartnerFolderData.slice';
import assetsViewInteractionSlice from '../slices/assetsViewInteraction.slice';
import assetsMoveModalInteractionSlice from '../slices/assetsMoveModalInteraction.slice';
import complianceContentAuditSlice from '../../creativeScoring/redux/slices/complianceContentAudit.slice';
import complianceShared from '../../creativeScoring/redux/slices/complianceShared.slice';
import complianceMobileFitnessSlice from '../../creativeScoring/redux/slices/complianceMobileFitness.slice';
import projectUploadMediaQueueSlice from '../slices/projectUploadQueueMedia.slice';
import activitySlice from '../slices/activity.slice';
import failedLandingSlice from '../slices/failedLandingSlice.slice';
import { creativeIntelligenceReducers } from '../slices/creativeAnalytics';
import adAcctSharingSlice from '../slices/adAcctSharing/adAcctSharing.slice';
import complianceCriteriaManagementSlice from '../../creativeScoring/redux/slices/complianceCriteriaManagement.slice';
import criteriaManagementSlice from '../../creativeScoring/redux/slices/criteriaManagement.slice';
import complianceBatchesSlice from '../../creativeScoring/redux/slices/complianceBatches.slice';
import complianceUploadsSlice from '../../creativeScoring/redux/slices/complianceUploads.slice';
import complianceIndividualAssetSlice from '../../creativeScoring/redux/slices/complianceIndividualAsset.slice';
import executiveDashboardSlice from '../../creativeScoring/redux/slices/complianceDashboard.slice';
import DAMIndividualAssetSlice from '../../creativeScoring/redux/slices/DAMIndividualAsset.slice';
import scoringFilterPanelV2Slice from '../../creativeScoring/redux/slices/filterPanelV2.slice';

import partnerSlice from '../slices/partner.slice';
import projectCreateSlice from '../slices/projectCreate.slice';
import projectSlice from '../slices/project.slice';
import countriesSlice from '../../creativeScoring/redux/slices/countries.slice';
import normsConfigurationSlice from '../../creativeScoring/redux/slices/normsConfiguration.slice';
import paymentSlice from '../slices/payment.slice';
import creativeScoringSlice from '../../creativeScoring/redux/slices/creativeScoring.slice';
import outputsV2Slice from '../slices/outputsV2.slice';
import scoreOverrideSlice from '../../creativeScoring/redux/slices/scoreOverride.slice';
import navigationSlice from '../slices/navigation.slice';
import workspacesSlice from '../../userManagement/redux/slices/workspaces.slice';
import workspacesAdAccountsSlice from '../../userManagement/redux/slices/workspaces.adAccounts.slice';
import rollUpReportsSlice from '../../creativeScoring/redux/slices/rollUpReports.slice';
import rollupReportsManagementSlice from '../../creativeScoring/redux/slices/rollupReportsManagement.slice';
import workspaceShowSlice from '../../userManagement/redux/slices/workspace.details.slice';
import toastAlertSlice from '../slices/toastAlert.slice';
import organizationSlice from '../../userManagement/redux/slices/organization.slice';
import peopleSlice from '../../userManagement/redux/slices/people.slice';
import convertedMediaSlice from '../../creativeScoring/redux/slices/convertedMedia.slice';
import adAccountsSlice from '../slices/adAccountHealthDashboard.slice';
import feConstantsSlice from '../slices/feConstants.slice';
import exportPDFSlice from '../slices/exportPDF.slice';

export const staticReducers = {
  confirmations: confirmationBarReducer,
  partnerProjects: partnerProjectsReducer,
  deepLink: deepLinkReducer,
  featureFlags: featureFlagsReducer,
  industry: industryReducer,
  intercomTour: intercomTourReducer,
  inviteModal: inviteModalReducer,
  confirmationModal: confirmationModalReducer,
  locale: localeReducer,
  reset: resetReducer,
  user: userReducer,
  upload: uploadReducer,
  jwPlayer: jwPlayerReducer,
  webSocket: webSocketReducer,
  project: projectReducer,
  projectOutputs: projectOutputsReducer,
  projectOutputsV2: projectOutputsReducerV2,
  [creativeScoringSlice.name]: creativeScoringSlice.reducer,
  [projectMediaFolderDataSlice.name]: projectMediaFolderDataSlice.reducer,
  [projectMediaDataSlice.name]: projectMediaDataSlice.reducer,
  [projectStockMediaDataSlice.name]: projectStockMediaDataSlice.reducer,
  [projectStockMediaFolderDataSlice.name]:
    projectStockMediaFolderDataSlice.reducer,
  [projectPartnerMediaDataSlice.name]: projectPartnerMediaDataSlice.reducer,
  [projectPartnerFolderDataSlice.name]: projectPartnerFolderDataSlice.reducer,
  [assetsViewInteractionSlice.name]: assetsViewInteractionSlice.reducer,
  [assetsMoveModalInteractionSlice.name]:
    assetsMoveModalInteractionSlice.reducer,
  [projectUploadMediaQueueSlice.name]: projectUploadMediaQueueSlice.reducer,
  [partnerSlice.name]: partnerSlice.reducer,
  [projectSlice.name]: projectSlice.reducer,
  [projectCreateSlice.name]: projectCreateSlice.reducer,
  [outputsV2Slice.name]: outputsV2Slice.reducer,
  [countriesSlice.name]: countriesSlice.reducer,
  [normsConfigurationSlice.name]: normsConfigurationSlice.reducer,
  [paymentSlice.name]: paymentSlice.reducer,
  mediaPreview: mediaPreviewReducer,
  adAccounts: adAccountsReducer,
  platformScopes: platformScopesReducer,
  subscriptions: subscriptionsReducer,
  analyticsUserPreferences: analyticsUserPreferencesReducer,
  [complianceContentAuditSlice.name]: complianceContentAuditSlice.reducer,
  [complianceMobileFitnessSlice.name]: complianceMobileFitnessSlice.reducer,
  [complianceCriteriaManagementSlice.name]:
    complianceCriteriaManagementSlice.reducer,
  [complianceBatchesSlice.name]: complianceBatchesSlice.reducer,
  [scoringFilterPanelV2Slice.name]: scoringFilterPanelV2Slice.reducer,
  [complianceUploadsSlice.name]: complianceUploadsSlice.reducer,
  [complianceShared.name]: complianceShared.reducer,
  [complianceIndividualAssetSlice.name]: complianceIndividualAssetSlice.reducer,
  [executiveDashboardSlice.name]: executiveDashboardSlice.reducer,
  [DAMIndividualAssetSlice.name]: DAMIndividualAssetSlice.reducer,
  [executiveDashboardSlice.name]: executiveDashboardSlice.reducer,
  [activitySlice.name]: activitySlice.reducer,
  [failedLandingSlice.name]: failedLandingSlice.reducer,
  [adAcctSharingSlice.name]: adAcctSharingSlice.reducer,
  [scoreOverrideSlice.name]: scoreOverrideSlice.reducer,
  [convertedMediaSlice.name]: convertedMediaSlice.reducer,
  [adAccountsSlice.name]: adAccountsSlice.reducer,
  [navigationSlice.name]: navigationSlice.reducer,
  [workspacesSlice.name]: workspacesSlice.reducer,
  [workspacesAdAccountsSlice.name]: workspacesAdAccountsSlice.reducer,
  [workspaceShowSlice.name]: workspaceShowSlice.reducer,
  [rollUpReportsSlice.name]: rollUpReportsSlice.reducer,
  [rollupReportsManagementSlice.name]: rollupReportsManagementSlice.reducer,
  [criteriaManagementSlice.name]: criteriaManagementSlice.reducer,
  [toastAlertSlice.name]: toastAlertSlice.reducer,
  [organizationSlice.name]: organizationSlice.reducer,
  [peopleSlice.name]: peopleSlice.reducer,
  ...creativeIntelligenceReducers,
  [feConstantsSlice.name]: feConstantsSlice.reducer,
  [exportPDFSlice.name]: exportPDFSlice.reducer,
};

const reducers = combineReducers(staticReducers);

export default (state, action) => {
  if (action.type === RESET) {
    /*
     * this will wipe the entire store state
     * look into reset.actions.js and reset.reducer.js files for details
     * */

    /*
     * Due to the nature of our invite system we need to store any information from Branch.io or our JWT that is
     * required to accept the invite here. inviteData is the information needed to accept invites to project, partner, etc.
     * Since the login page resets state we would loose the inviteData and not be able to process the invite.
     * Adding the exception here lets us have the information to process the invite after the user has signed up/in.
     */
    const { inviteData } = state.deepLink;
    if (inviteData) {
      const { deepLink } = state;
      state = { deepLink };
    } else {
      state = {};
    }
  }

  return reducers(state, action);
};

/**
 * @param {object} asyncReducers  dynamic loaded reducers for code splitting
 * @returns {Function} rootReducer static reducers as well as newly loaded reducers
 */
export function createReducer(asyncReducers) {
  const reducers = combineReducers({
    ...staticReducers,
    ...asyncReducers,
  });

  return (state, action) => {
    if (action.type === RESET) {
      /*
       * this will wipe the entire store state
       * look into reset.actions.js and reset.reducer.js files for details
       * */

      /*
       * Due to the nature of our invite system we need to store any information from Branch.io or our JWT that is
       * required to accept the invite here. inviteData is the information needed to accept invites to project, partner, etc.
       * Since the login page resets state we would loose the inviteData and not be able to process the invite.
       * Adding the exception here lets us have the information to process the invite after the user has signed up/in.
       */
      const { inviteData } = state.deepLink;
      if (inviteData) {
        const { deepLink } = state;
        state = { deepLink };
      } else {
        state = {};
      }
    }

    return reducers(state, action);
  };
}
