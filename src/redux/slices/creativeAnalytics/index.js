import creativeManagerSlice from './creativeManager.slice';
import objectiveReportSlice from './objectiveReport.slice';
import creativeGroupsSlice from './creativeGroups.slice';
import formatReportSlice from './formatReport.slice';
import mediaPostTypeSlice from './mediaPostType.slice';
import customCompareSlice from './customCompare.slice';
import adTypeReportSlice from './adTypeReport.slice';
import elementPresenceReport from './elementPresenceReport.slice';
import placementReportSlice from './placementReport.slice';
import audienceReportSlice from './audienceReport.slice';
import analyticsConfigurationSlice from './analyticsConfiguration.slice';
import platformAccountsSlice from './platformAccounts.slice';
import creativeElementsSlice from './creativeElements.slices';
import campaignReportSlice from './campaignReport.slice';
import brandReportSlice from './brandReport.slice';
import marketReportSlice from './marketReport.slice';
import vidMobPerformanceReportSlice from './vidMobPerformanceReport.slice';
import insightsSlice from './insights.slice';
import industriesSlice from './industries.slice';
import durationReportSlice from './durationReport.slice';
import individualCreativeViewSlice from '../../../creativeAnalytics/reports/redux/individualCreativeView/individualCreativeView.slice';
import individualCreativeViewAudienceEngagementSlice from '../../../creativeAnalytics/reports/redux/individualCreativeView/IndividualCreativeViewAudienceEngagement/IndividualCreativeViewAudienceEngagement.slice';

const creativeIntelligenceSlices = {
  [adTypeReportSlice.name]: adTypeReportSlice,
  [audienceReportSlice.name]: audienceReportSlice,
  [creativeGroupsSlice.name]: creativeGroupsSlice,
  [creativeManagerSlice.name]: creativeManagerSlice,
  [customCompareSlice.name]: customCompareSlice,
  [elementPresenceReport.name]: elementPresenceReport,
  [formatReportSlice.name]: formatReportSlice,
  [mediaPostTypeSlice.name]: mediaPostTypeSlice,
  [placementReportSlice.name]: placementReportSlice,
  [vidMobPerformanceReportSlice.name]: vidMobPerformanceReportSlice,
  [objectiveReportSlice.name]: objectiveReportSlice,
  [analyticsConfigurationSlice.name]: analyticsConfigurationSlice,
  [platformAccountsSlice.name]: platformAccountsSlice,
  [creativeElementsSlice.name]: creativeElementsSlice,
  [campaignReportSlice.name]: campaignReportSlice,
  [insightsSlice.name]: insightsSlice,
  [industriesSlice.name]: industriesSlice,
  [durationReportSlice.name]: durationReportSlice,
  [individualCreativeViewSlice.name]: individualCreativeViewSlice,
  [individualCreativeViewAudienceEngagementSlice.name]:
    individualCreativeViewAudienceEngagementSlice,
  [brandReportSlice.name]: brandReportSlice,
  [marketReportSlice.name]: marketReportSlice,
};

export const creativeIntelligenceReducers = {
  [adTypeReportSlice.name]: adTypeReportSlice.reducer,
  [audienceReportSlice.name]: audienceReportSlice.reducer,
  [creativeGroupsSlice.name]: creativeGroupsSlice.reducer,
  [creativeManagerSlice.name]: creativeManagerSlice.reducer,
  [elementPresenceReport.name]: elementPresenceReport.reducer,
  [formatReportSlice.name]: formatReportSlice.reducer,
  [mediaPostTypeSlice.name]: mediaPostTypeSlice.reducer,
  [customCompareSlice.name]: customCompareSlice.reducer,
  [placementReportSlice.name]: placementReportSlice.reducer,
  [vidMobPerformanceReportSlice.name]: vidMobPerformanceReportSlice.reducer,
  [objectiveReportSlice.name]: objectiveReportSlice.reducer,
  [analyticsConfigurationSlice.name]: analyticsConfigurationSlice.reducer,
  [platformAccountsSlice.name]: platformAccountsSlice.reducer,
  [creativeElementsSlice.name]: creativeElementsSlice.reducer,
  [campaignReportSlice.name]: campaignReportSlice.reducer,
  [insightsSlice.name]: insightsSlice.reducer,
  [industriesSlice.name]: industriesSlice.reducer,
  [durationReportSlice.name]: durationReportSlice.reducer,
  [individualCreativeViewSlice.name]: individualCreativeViewSlice.reducer,
  [individualCreativeViewAudienceEngagementSlice.name]:
    individualCreativeViewAudienceEngagementSlice.reducer,
  [brandReportSlice.name]: brandReportSlice.reducer,
  [marketReportSlice.name]: marketReportSlice.reducer,
};

export default creativeIntelligenceSlices;
