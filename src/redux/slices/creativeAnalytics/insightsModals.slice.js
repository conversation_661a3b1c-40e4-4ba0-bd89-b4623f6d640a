import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../constants';
import { INSIGHT_TYPES } from '../../../constants/insights.constants';

const { NOT_LOADED, PENDING, SUCCESS, FAILED } = GLOBALS.REDUX_LOADING_STATUS;

const getInitialState = () => ({
  isInsightCreatePanelVisible: false,
  isInsightsAddToProjectModalOpen: false,
  isInsightsAddToProjectConfirmationModalOpen: false,
  selectedRowsCreativeExamples: {},
  creativeExampleToReload: {},
  partnerProjectsLoadingStatus: NOT_LOADED,
  partnerProjects: [],
  selectedProjectsToAddInsights: [],
  activeInsight: {
    // insight currently being interacted with. used by modals and insight create panel
    insightId: '',
    isEditing: false,
    isSaved: false,
    isDraft: false,
    isPublished: false,
    isArchived: false,
    autoPopulateTimestamp: null,
    insightSavingStatus: NOT_LOADED,
    finding: '', // aka title
    recommendation: '',
    type: INSIGHT_TYPES.BRAND, // these are the only kind of insights we create right now
    categories: [], // aka detailed insight bucket
    adAccountIds: [],
    platform: null,
    analyticsStartDate: null,
    analyticsEndDate: null,
    selectedKpi: null,
    additionalKpis: [],
    selectedTagsAndCreativeExampleMedia: [],
    savedAnalyticsReportId: null,
    sharedFilterObjectStatus: NOT_LOADED,
    sharedFilterObject: null,
    keyData: null,
    keyDataLoadingStatus: NOT_LOADED,
    linkedProjects: {
      loadingStatus: NOT_LOADED,
      projectsList: [],
    },
  },
  workspaceIds: [],
  customGroupColumnsCreativeExamples: {},
});

/*
 * This slice handles state for insight create (panel) and insight modals
 */
const insightsModalsSlice = {
  initialState: getInitialState(),
  reducers: {
    // Insight Create Panel
    onOpenInsightCreatePanel(state) {
      state.isInsightCreatePanelVisible = true;
    },
    clearActiveInsight(state) {
      state.activeInsight = getInitialState().activeInsight;
      state.selectedRowsCreativeExamples = {};
      state.customGroupColumnsCreativeExamples = {};
    },
    updateInsightOnPanelClose(state, action) {
      const { activeInsight, workspaceIds } = action.payload;
      state.activeInsight = activeInsight;
      state.isInsightCreatePanelVisible = false;
      state.workspaceIds = workspaceIds;
    },
    onSaveDraftInsight(state, action) {
      const { activeInsight, workspaceIds } = action.payload;
      state.activeInsight = activeInsight;
      state.activeInsight.isSaved = true;
      state.activeInsight.isDraft = true;
      state.activeInsight.insightSavingStatus = PENDING;
      state.workspaceIds = workspaceIds;
    },
    onSaveInsightSuccess(state) {
      state.activeInsight.insightSavingStatus = SUCCESS;
      state.isInsightCreatePanelVisible = false;
      state.selectedRowsCreativeExamples = {};
    },
    onSaveInsightFailure(state) {
      state.activeInsight.insightSavingStatus = FAILED;
      state.activeInsight.isSaved = false;
      state.activeInsight.isDraft = false;
      state.activeInsight.isPublished = false;
    },
    onDeleteInsightInCreate(state) {
      state.activeInsight = getInitialState().activeInsight;
      state.workspaceIds = [];
      state.isInsightCreatePanelVisible = false;
      state.selectedRowsCreativeExamples = {};
    },
    onPublishInsight(state, action) {
      const { activeInsight, workspaceIds } = action.payload;

      state.activeInsight = activeInsight;
      state.activeInsight.isSaved = true;
      state.activeInsight.isPublished = true;
      state.activeInsight.insightSavingStatus = PENDING;
      state.workspaceIds = workspaceIds;
    },

    // Creative Examples
    setSelectedRowCreativeExamplePending(state, action) {
      const { row } = action.payload;
      state.selectedRowsCreativeExamples[row.id] = {
        ...state.selectedRowsCreativeExamples[row.id],
        status: PENDING,
      };
    },
    setSelectedRowsForCreativeExamples(state, action) {
      const { filteredSelectedRowsWithInfo } = action.payload;
      const selectedRowsCreativeExamplesObj = {};
      filteredSelectedRowsWithInfo.forEach((selectedRow) => {
        if (state.selectedRowsCreativeExamples[selectedRow.id]) {
          selectedRowsCreativeExamplesObj[selectedRow.id] = {
            ...state.selectedRowsCreativeExamples[selectedRow.id],
          };
        } else {
          selectedRowsCreativeExamplesObj[selectedRow.id] = {
            status: NOT_LOADED,
            platformMediaIds: [],
            platformMediaInfo: [],
            topConfidentMedia: [],
            selectedPlatformMediaIds: [],
            rowInfo: { ...selectedRow },
          };
        }
      });
      state.selectedRowsCreativeExamples = {
        ...selectedRowsCreativeExamplesObj,
      };
    },
    updateCreativeExamplesOnSuccess(state, action) {
      const { elementMediaIds, elementMedia, topConfidentMedia, row } =
        action.payload;
      state.selectedRowsCreativeExamples[row.id] = {
        ...state.selectedRowsCreativeExamples[row.id],
        status: SUCCESS,
        platformMediaIds: elementMediaIds,
        platformMediaInfo: elementMedia,
        topConfidentMedia,
      };
    },
    updateCreativeExamplesOnFailure(state, action) {
      const { row } = action.payload;

      state.selectedRowsCreativeExamples[row.id] = {
        ...state.selectedRowsCreativeExamples[row.id],
        status: FAILED,
      };
    },
    onSelectCreativeExample(state, action) {
      const { row, platformMediaId } = action.payload;

      const selectedRowObject = state.selectedRowsCreativeExamples[row.id];
      if (selectedRowObject.platformMediaIds.includes(platformMediaId)) {
        if (
          selectedRowObject.selectedPlatformMediaIds.includes(platformMediaId)
        ) {
          selectedRowObject.selectedPlatformMediaIds =
            selectedRowObject.selectedPlatformMediaIds.filter(
              (id) => id !== platformMediaId,
            );
        } else {
          selectedRowObject.selectedPlatformMediaIds = [
            ...selectedRowObject.selectedPlatformMediaIds,
            platformMediaId,
          ];
        }

        state.selectedRowsCreativeExamples[row.id] = selectedRowObject;
      }
    },
    onReloadCreativeExampleData(state, action) {
      const { row } = action.payload;

      state.selectedRowsCreativeExamples[row.rowInfo.id] = {
        ...state.selectedRowsCreativeExamples[row.rowInfo.id],
        status: PENDING,
      };

      state.creativeExampleToReload =
        state.selectedRowsCreativeExamples[row.rowInfo.id];
    },

    // Custom Grouping KPI Creative Examples
    resetSelectedColumnsForCreativeExamples(state) {
      state.customGroupColumnsCreativeExamples = {};
    },
    setCustomGroupColumnsCreativeExamples(state, action) {
      const { customGroupColumns } = action.payload;
      const selectedColumnsCreativeExamplesObj = {};
      customGroupColumns.forEach((column) => {
        const id = column.id - 1; // We remove __average column which is id 0, so we need to decrement each subsequent id
        if (column?.title === '__average') {
          return;
        }

        if (state.customGroupColumnsCreativeExamples[id]) {
          selectedColumnsCreativeExamplesObj[id] = {
            ...state.customGroupColumnsCreativeExamples[id],
          };
        } else {
          selectedColumnsCreativeExamplesObj[id] = {
            status: NOT_LOADED,
            platformMediaIds: column?.platformMediaIds || [],
            selectedPlatformMediaIds: [],
            columnInfo: { ...column },
          };
        }
      });
      state.customGroupColumnsCreativeExamples = {
        ...selectedColumnsCreativeExamplesObj,
      };
    },
    onSelectCustomGroupCreativeExample(state, action) {
      const { column, platformMediaId } = action.payload;
      const id = column.id - 1; // We remove __average column which is id 0, so we need to decrement each subsequent id

      const selectedColumnObject = state.customGroupColumnsCreativeExamples[id];
      if (selectedColumnObject?.platformMediaIds.includes(platformMediaId)) {
        if (
          selectedColumnObject.selectedPlatformMediaIds.includes(
            platformMediaId,
          )
        ) {
          selectedColumnObject.selectedPlatformMediaIds =
            selectedColumnObject.selectedPlatformMediaIds.filter(
              (id) => id !== platformMediaId,
            );
        } else {
          selectedColumnObject.selectedPlatformMediaIds = [
            ...selectedColumnObject.selectedPlatformMediaIds,
            platformMediaId,
          ];
        }

        state.customGroupColumnsCreativeExamples[id] = selectedColumnObject;
      }
    },
  },
};

export default insightsModalsSlice;
