import { createSlice } from '@reduxjs/toolkit';
import { CREATIVE_INTELLIGENCE, G<PERSON><PERSON><PERSON><PERSON> } from '../../../constants';
import { PERCENTAGE_100 } from '../../../constants/analytics.api.constants';

const { NOT_LOADED, PENDING, SUCCESS, FAILED } = GLOBALS.REDUX_LOADING_STATUS;

const getInitialState = () => {
  return {
    status: NOT_LOADED,
    rowItems: [],
    columnItems: [],
    loadingPercentage: 0,
    hasDataForFilters: false,
    hasDataForKpi: false,
    hasAssetLevelDataForKpi: false,
    savedReport: null,
    savedReportLoadingStatus: NOT_LOADED,
  };
};

const customCompareSlice = createSlice({
  name: CREATIVE_INTELLIGENCE.CREATIVE_INTELLIGENCE_REPORTS
    .CUSTOM_COMPARE_REPORT.REDUX_SLICE_NAME,
  initialState: getInitialState(),
  reducers: {
    reset: () => {
      return getInitialState();
    },
    setReportStatusPending: (state) => {
      state.status = PENDING;
    },
    setReportStatusSuccessSaga: (state) => {
      state.status = SUCCESS;
    },
    updateReportOnRequestSuccess: (state, action) => {
      const {
        elements,
        groups,
        hasDataForKpi,
        hasAssetLevelDataForKpi,
        hasDataForFilters,
      } = action.payload;

      // add ids to groups
      const reportGroups = groups
        .filter((val) => Boolean(val))
        .map((format, idx) => {
          return { id: idx, ...format };
        });

      state.status = SUCCESS;
      state.rowItems = elements;
      state.columnItems = reportGroups;
      state.hasDataForKpi = hasDataForKpi;
      state.hasAssetLevelDataForKpi = hasAssetLevelDataForKpi;
      state.hasDataForFilters = hasDataForFilters;
      state.loadingPercentage = PERCENTAGE_100;
    },
    updateReportOnRequestFailure: (state, action) => {
      const { error } = action.payload || {};
      const result = {
        ...getInitialState(),
        done: true,
        status: FAILED,
      };

      if (error !== undefined) {
        result.error = error;
      }

      return result;
    },
    updateGroupByIndex: (state, action) => {
      const { updatedGroup, updatedGroupIndex } = action.payload;

      state.columnItems = [
        ...state.columnItems.slice(0, updatedGroupIndex),
        updatedGroup,
        ...state.columnItems.slice(updatedGroupIndex + 1),
      ];
    },
    deleteCreativeGroup: (state, action) => {
      const { groupId } = action.payload;
      state.columnItems = state.columnItems.filter(
        (column) => column.group.id !== groupId,
      );
    },
    loadSavedReportFromUrl: (state) => {
      state.savedReportLoadingStatus = PENDING;
    },
    onSaveReport: (state) => {
      state.savedReportLoadingStatus = PENDING;
    },
    onSaveReportSuccess: (state, action) => {
      state.savedReport = action.payload.savedReport;
      state.savedReportLoadingStatus = SUCCESS;
    },
    onSaveReportFailure: (state) => {
      state.savedReportLoadingStatus = FAILED;
    },
  },
});

export default customCompareSlice;
