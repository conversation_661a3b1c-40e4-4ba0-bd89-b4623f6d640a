import insightsModalsSlice from './insightsModals.slice';
import { createSlice } from '@reduxjs/toolkit';
import { CREATIVE_INTELLIGENCE } from '../../../constants';

export const getInitialState = () => ({
  ...insightsModalsSlice.initialState,
});

const insightsSlice = createSlice({
  name: CREATIVE_INTELLIGENCE.INSIGHTS_SLICE_NAME,
  initialState: getInitialState(),
  reducers: {
    reset: () => {
      return getInitialState();
    },
    ...insightsModalsSlice.reducers,
  },
});

export default insightsSlice;
