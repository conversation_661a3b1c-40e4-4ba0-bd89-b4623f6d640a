import { createSlice } from '@reduxjs/toolkit';
import { GLOBALS, CREATIVE_INTELLIGENCE } from '../../../constants';

const { NOT_LOADED, PENDING, FAILED, SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const getInitialState = () => {
  return {
    view: null,
    accountCreativesStatus: NOT_LOADED, // isMediaLoading in component state
    accountAdsStatus: NOT_LOADED,
    assetDetailStatus: NOT_LOADED, // isGettingAllMedia in component state
    loadingStatus: NOT_LOADED, // general loading status for both ads and creatives views
    hasDataForFilters: false,
    creatives: [],
    ads: [],
    previewedMedia: null,
  };
};

const creativeManagerSlice = createSlice({
  name: CREATIVE_INTELLIGENCE.CREATIVE_INTELLIGENCE_REPORTS.CREATIVE_MANAGER
    .REDUX_SLICE_NAME,
  initialState: getInitialState(),
  reducers: {
    reset: () => {
      return getInitialState();
    },

    setAccountCreativesStatusPending: (state) => {
      state.accountCreativesStatus = PENDING;
      state.loadingStatus = PENDING;
    },
    setAccountCreativesAndAdsStatusPending: (state) => {
      state.accountAdsStatus = PENDING;
      state.accountCreativesStatus = PENDING;
      state.loadingStatus = PENDING;
    },

    updateAccountCreativesOnRequestSuccess: (state, action) => {
      const { creatives, hasDataForFilters } = action.payload;
      state.hasDataForFilters = hasDataForFilters;
      state.creatives = creatives || [];
      state.assetDetailStatus = SUCCESS;
      state.accountCreativesStatus = SUCCESS;
      if (state.accountAdsStatus === NOT_LOADED) {
        // if only creative-level data is requested, set overall loading status to SUCCESS
        state.loadingStatus = SUCCESS;
      } else {
        state.loadingStatus = state.accountAdsStatus;
      }
    },
    updateAccountAdsOnRequestSuccess: (state, action) => {
      const { ads, hasDataForFilters } = action.payload;
      state.ads = ads;
      state.hasDataForFilters = hasDataForFilters;
      state.accountAdsStatus = SUCCESS;
      if (state.accountCreativesStatus === NOT_LOADED) {
        // if only ad-level data is requested, set overall loading status to SUCCESS
        state.loadingStatus = SUCCESS;
      } else {
        state.loadingStatus = state.accountCreativesStatus;
      }
    },

    updateAccountCreativesStatusFailed: (state) => {
      state.accountCreativesStatus = FAILED;
      state.loadingStatus = FAILED;
    },
    updateAccountAdsOnRequestFailed: (state) => {
      state.accountAdsStatus = FAILED;
      state.loadingStatus = FAILED;
    },
  },
});

export default creativeManagerSlice;
