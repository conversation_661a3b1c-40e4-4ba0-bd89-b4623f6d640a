import { createSlice } from '@reduxjs/toolkit';
import moment from 'moment';
// utils
import getKeyForCIAdAccount from '../../../utils/getKeyForCIAdAccount';
// constants
import { GLOBALS, CREATIVE_INTELLIGENCE } from '../../../constants';
import { FILE_TYPES } from '../../../constants/ci.constants';
import { defaultReportTableViewOptions } from '../../../constants/ci.constants';

const { NOT_LOADED, PENDING, SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

export const getSelectedAnalyticsFiltersInitialState = () => ({
  selectedObjectiveFilters: [],
  selectedPlacementFilters: {},
  selectedPlacementNestingOption: null,
  selectedCampaignFilters: [],
  selectedAdSetFilters: [],
  selectedMediaTypeFilters: [FILE_TYPES.video],
  createdByVidmob: undefined,
  isShowAppAdsActive: false,
  isSponsoredAdsActive: false,
  isSparkAdsFilterActive: false,
});

export const getInitialState = () => {
  return {
    addNewColumnIsTransitioning: false,
    shouldOverrideReportReset: false,
    sharedFilterId: null,
    sharedFilterObject: null,
    needsToReLoadViewOnCustomTagSave: false,
    currentCreativeIntelligenceView: null,
    availableReportBreakdownsForPlatform: [],
    currentReportBreakdown: null,
    isAnalyticsFilterPanelVisible: false,
    isTagGroupUpdating: false,
    isNeedingNewAnalyticsReport: false, // this was originally default true but we're trying to trigger loading other ways now
    placementsLoadingStatus: NOT_LOADED,
    objectivesLoadingStatus: NOT_LOADED,
    campaignsLoadingStatus: NOT_LOADED,
    adSetsLoadingStatus: NOT_LOADED,
    mediaTypesLoadingStatus: NOT_LOADED,
    kpiLoadingStatus: NOT_LOADED, // this wasn't in the original but having this in the same place might be good
    haveFiltersLoaded: false,
    havePreliminarySharedFiltersBeenApplied: false,
    haveSharedFiltersBeenApplied: false,
    filters: {
      impressionFilterMinValue: null,
      impressionFilterMaxValue: null,
      availableObjectiveFilters: {},
      availablePlacementFilters: {},
      availablePlacementNestingOptions: [],
      availableCampaignFilters: [],
      availableAdSetFilters: [],
      availableMediaTypeFilters: [],
      analyticsStartDate: moment().subtract(3, 'month'),
      analyticsEndDate: moment(),
      tempAnalyticsStartDate: null,
      tempAnalyticsEndDate: null,
      ...getSelectedAnalyticsFiltersInitialState(),
    },
    reportTableView: defaultReportTableViewOptions[0],
    newReportTableView: null,
    selectedKpi: null,
    kpiList: [],
    isWatchViewDisabled: false,
    isAddNewColumnTransitioning: false,
    // this section for disabling dimension only applies to logic about requesting reports
    // it doesn't control what's visually disabled in the panels, that lives in the panel config
    arePlacementsDisabled: false,
    areObjectivesDisabled: false,
    areMediaTypesDisabled: false,
    areCampaignsDisabled: false,
    areBrandsDisabled: false,
    areMarketsDisabled: false,
    areAdSetsDisabled: false,
    isShowAppAdsDisabled: false,
    isSponsoredAdsDisabled: false,
    // end disabling dimension section
    isPercentLiftViewEnabled: false,
    statsAccounts: {},
    selectedOpCompareOption: null,
    availableOpCompareOptions: [],
    selectedRows: new Set(),
    isSelectedRowsViewActive: false,
    elementMediaModal: {
      isElementMediaModalOpenRedux: false,
      element: null,
      elementMediaLoadingStatus: NOT_LOADED,
      elementMediaIds: [], // get rid of this when new modal is finished
      elementMedia: [],
      topConfidentMedia: [],
    },
    hasAnalyticsFilterPanelBeenChangedSinceLastSave: false,
  };
};

const analyticsConfigurationSlice = createSlice({
  name: CREATIVE_INTELLIGENCE.ANALYTICS_CONFIGURATION_SLICE_NAME,
  initialState: getInitialState(),
  reducers: {
    reset: () => {
      return getInitialState();
    },
    resetShareUrlConfigAndSelectedRows: (state) => {
      state.sharedFilterId = null;
      state.sharedFilterObject = null;
      state.selectedRows = new Set();
      state.isSelectedRowsViewActive = false;
    },
    setCurrentCreativeIntelligenceView: (state, action) => {
      const { currentView } = action.payload;

      state.currentCreativeIntelligenceView = currentView;
      state.selectedOpCompareOption = null;
      state.availableOpCompareOptions = [];
      state.selectedRows = new Set();
      state.isSelectedRowsViewActive = false;

      // reset disabled dimensions
      state.arePlacementsDisabled = false;
      state.areObjectivesDisabled = false;
      state.areMediaTypesDisabled = false;
      state.shouldSendOnlyVideoMediaType = false;
      state.isShowAppAdsDisabled = false;
      state.isSponsoredAdsDisabled = false;
      state.areCampaignsDisabled = false;
      state.areBrandsDisabled = false;
      state.areMarketsDisabled = false;
      state.areAdSetsDisabled = false;

      sessionStorage.setItem(
        'analyticsFilterLastUpdatedTimestamp',
        moment().format(),
      );
    },
    setAvailableReportBreakdowns: (state, action) => {
      const { availableBreakdowns } = action.payload;
      state.availableReportBreakdownsForPlatform = availableBreakdowns;
    },
    setCurrentReportBreakdown: (state, action) => {
      const { currentBreakdown } = action.payload;
      state.currentReportBreakdown = currentBreakdown;
    },
    setIsAddNewColumnTransitioning: (state, action) => {
      state.isAddNewColumnTransitioning =
        action.payload.isAddNewColumnTransitioning;
    },
    setSelectedRows: (state, action) => {
      state.selectedRows = action.payload.selectedRows;
    },
    setHasAnalyticsFilterPanelBeenChangedSinceLastSave: (state, action) => {
      state.hasAnalyticsFilterPanelBeenChangedSinceLastSave = action.payload;
    },
    setSharedAnalyticsFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetReportTableView: (state) => {
      state.newReportTableView = null;
      state.selectedRows = new Set();
      state.isSelectedRowsViewActive = false;
      state.currentReportBreakdown = null;
      state.currentCreativeIntelligenceView = null;
      state.reportTableView = defaultReportTableViewOptions[0];
    },
    setReportTableView: (state, action) => {
      const { reportTableView } = action.payload;

      state.reportTableView = reportTableView;
    },
    setSelectedKpi: (state, action) => {
      const { selectedKpi } = action.payload;

      state.selectedKpi = selectedKpi;
    },
    setSelectedPlacementNestingOption: (state, action) => {
      state.filters.selectedPlacementNestingOption =
        action.payload.selectedOption;
    },
    setSharedFilterId: (state, action) => {
      const { sharedFilterId } = action.payload;

      state.sharedFilterId = sharedFilterId;
    },
    clearSharedFilterId: (state) => {
      state.sharedFilterId = null;
    },
    clearSharedFilterData: (state) => {
      state.sharedFilterId = null;
      state.sharedFilterObject = null;
      state.havePreliminarySharedFiltersBeenApplied = false;
      state.haveSharedFiltersBeenApplied = false;
    },
    saveMediaStats: (state, action) => {
      const { account, platform, mediaDates, stats } = action.payload;
      const accountKey = getKeyForCIAdAccount(account, platform);

      state.statsAccounts[accountKey] = {
        startDate: mediaDates.startDate,
        endDate: mediaDates.endDate,
        expirationDate: mediaDates.expirationDate,
        mediaList: stats,
      };
    },
    setAvailableObjectivePlacementCompareOptions: (state, action) => {
      state.availableOpCompareOptions =
        action.payload.availableOpCompareOptions;
      if (
        !action.payload.availableOpCompareOptions.find(
          (option) => option.id === state.selectedOpCompareOption?.id,
        )
      ) {
        state.selectedOpCompareOption =
          action.payload.availableOpCompareOptions[0];
      }
    },
    setIsTagGroupUpdating: (state, action) => {
      state.isTagGroupUpdating = action.payload.isTagGroupUpdating;
    },
    setIsAddGroupModalOpen: (state, action) => {
      state.isAddGroupModalOpen = action.payload.isAddGroupModalOpen;
    },
    openElementMediaModal: (state, action) => {
      const { element } = action.payload;
      state.elementMediaModal.isElementMediaModalOpenRedux = true;
      state.elementMediaModal.element = element;
    },
    setElementMediaModalPending: (state) => {
      state.elementMediaModal.elementMediaLoadingStatus = PENDING;
    },
    updateElementMediaModalOnSuccess: (state, action) => {
      const { elementMediaIds, elementMedia, topConfidentMedia } =
        action.payload;

      state.elementMediaModal.elementMediaIds = elementMediaIds;
      state.elementMediaModal.elementMedia = elementMedia;
      state.elementMediaModal.topConfidentMedia = topConfidentMedia;
      state.elementMediaModal.elementMediaLoadingStatus = SUCCESS;
    },
    resetElementMediaModal: (state) => {
      state.elementMediaModal = getInitialState().elementMediaModal;
    },

    // IMPACT REPORT ONLY
    // triggers loadImpactReport in analyticsConfiguration.sagas.js
    setNewImpactReport: (state) => {
      return state;
    },
    // triggers loadExistingImpactReport in analyticsConfiguration.sagas.js
    setExistingImpactReport: (state) => {
      return state;
    },
  },
});

export default analyticsConfigurationSlice;
