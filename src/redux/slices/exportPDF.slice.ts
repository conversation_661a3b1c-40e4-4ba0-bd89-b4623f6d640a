import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ExportPDFState {
  isPageCurrentlyExporting: boolean;
}

const getInitialState = (): ExportPDFState => ({
  isPageCurrentlyExporting: false,
});

const exportPDFSlice = createSlice({
  name: 'exportPDF',
  initialState: getInitialState(),
  reducers: {
    setIsPDFExporting: (state, action: PayloadAction<boolean>) => {
      state.isPageCurrentlyExporting = action.payload;
    },
    reset: () => {
      return getInitialState();
    },
  },
});

export default exportPDFSlice;
