import { createSlice } from '@reduxjs/toolkit';
import { GLOBALS, PROJECT_CREATE } from '../../constants';
const { REDUX_LOADING_STATUS } = GLOBALS;
const { SUCCESS, PENDING, FAILED, NOT_LOADED } = REDUX_LOADING_STATUS;
const { NEUTRAL, NEEDS_PROCESSING, PROCESSED } = PROJECT_CREATE.STEP_STATUS;
const { NEXT, PREV } = PROJECT_CREATE.STEP_DIRECTION;

const getInitialState = () => ({
  projectStatus: null,
  projectBeingCreated: null,
  projectAPIRequestStatus: NOT_LOADED,
  projectLoadingAPIRequestStatus: NOT_LOADED,
  priceAPIRequestStatus: NOT_LOADED,
  errorCode: null,
  priceErrorCode: null,
  currentStepStatus: NEUTRAL,
  stepDirection: NEXT,
  loadProjectForEditFailed: false,
  saveAndClose: false,
  projectCreatedFromInsightBannerVisible: false,
  projectFinanceDetails: null,
  projectFundingOptions: null,
  projectFundingOptionsAPIStatus: NOT_LOADED,
  projectStatusErrors: null,
  projectStatusAPIrequestStatus: NOT_LOADED,
  insightsToCreateProject: [],
});

const projectCreate = createSlice({
  name: 'projectCreate',
  initialState: getInitialState(),
  reducers: {
    reset: (state) => {
      // do not reset insightsToCreateProject
      const { insightsToCreateProject } = state;
      return {
        ...getInitialState(),
        insightsToCreateProject,
      };
    },
    updateProjectAPIRequest: () => {},
    createProjectAPIRequest: () => {},
    loadProjectIntoState: () => {},
    getPriceQuote: () => {},
    getProjectFundingOptions: () => {},
    changeProjectStatus: () => {},
    setSaveAndCloseProjectCreate: (state, action) => {
      state.saveAndClose = action.payload;
    },
    setProjectBeingCreated: (state, action) => {
      const { projectBeingCreated } = action.payload;
      state.projectBeingCreated = projectBeingCreated;
    },
    updateProjectWithCreativeOutputs: (state, action) => {
      const { projectUpdates } = action.payload;
      state.projectBeingCreated.outputGroupsData =
        projectUpdates.outputGroupsData;
    },
    updateProjectStatus: (state, action) => {
      const { newProjectStatus } = action.payload;
      state.projectStatus = newProjectStatus;
    },
    setProjectAPIRequestStatusPending: (state) => {
      state.projectAPIRequestStatus = PENDING;
    },
    setProjectAPIRequestStatusFailed: (state) => {
      state.projectAPIRequestStatus = FAILED;
    },
    setProjectAPIRequestStatusSuccess: (state) => {
      state.projectAPIRequestStatus = SUCCESS;
    },
    setProjectAPIRequestStatusNotLoaded: (state) => {
      state.projectAPIRequestStatus = NOT_LOADED;
    },
    setProjectAPIErrorCode: (state, action) => {
      const { errorCode } = action.payload;
      state.errorCode = errorCode;
    },
    setPriceAPIRequestStatusPending: (state) => {
      state.priceAPIRequestStatus = PENDING;
    },
    setPriceAPIRequestStatusFailed: (state) => {
      state.priceAPIRequestStatus = FAILED;
    },
    setPriceAPIRequestStatusSuccess: (state) => {
      state.priceAPIRequestStatus = SUCCESS;
    },
    setPriceAPIRequestStatusNotLoaded: (state) => {
      state.priceAPIRequestStatus = NOT_LOADED;
    },
    setPriceAPIErrorCode: (state, action) => {
      const { priceErrorCode } = action.payload;
      state.priceErrorCode = priceErrorCode;
    },
    setProjectCreateStep: (state, action) => {
      const { currentStep } = action.payload;
      state.currentStep = currentStep;
    },
    setCurrentStepNeutral: (state) => {
      state.currentStepStatus = NEUTRAL;
    },
    setCurrentStepNeedsProcessing: (state) => {
      state.currentStepStatus = NEEDS_PROCESSING;
    },
    setCurrentStepProcessed: (state) => {
      state.currentStepStatus = PROCESSED;
    },
    setStepDirectionNext: (state) => {
      state.stepDirection = NEXT;
    },
    setStepDirectionPrev: (state) => {
      state.stepDirection = PREV;
    },
    setLoadProjectForEditFailed: (state, action) => {
      state.loadProjectForEditFailed = action.payload;
    },
    setPriceQuote: (state, action) => {
      const { priceQuote } = action.payload;
      state.projectBeingCreated.priceQuote = priceQuote;
    },
    setProjectLoadingAPIRequestStatusNotLoaded: (state) => {
      state.projectLoadingAPIRequestStatus = NOT_LOADED;
    },
    setProjectLoadingAPIRequestStatusPending: (state) => {
      state.projectLoadingAPIRequestStatus = PENDING;
    },
    setProjectLoadingAPIRequestStatusSuccess: (state) => {
      state.projectLoadingAPIRequestStatus = SUCCESS;
    },
    setProjectLoadingAPIRequestStatusFailed: (state) => {
      state.projectLoadingAPIRequestStatus = FAILED;
    },
    showProjectCreatedFromInsightBanner: (state) => {
      state.projectCreatedFromInsightBannerVisible = true;
    },
    hideProjectCreatedFromInsightBanner: (state) => {
      state.projectCreatedFromInsightBannerVisible = false;
    },
    setProjectFinanceDetails: (state, action) => {
      state.projectFinanceDetails = action.payload;
    },
    setFundingOptions: (state, action) => {
      state.projectFundingOptions = action.payload;
    },
    setProjectFundingOptionsAPIStatusSuccess: (state) => {
      state.projectFundingOptionsAPIStatus = SUCCESS;
    },
    setProjectFundingOptionsAPIStatusNotLoaded: (state) => {
      state.projectFundingOptionsAPIStatus = NOT_LOADED;
    },
    setProjectFundingOptionsAPIStatusPending: (state) => {
      state.projectFundingOptionsAPIStatus = PENDING;
    },
    setProjectFundingOptionsAPIStatusFailed: (state) => {
      state.projectFundingOptionsAPIStatus = FAILED;
    },
    setProjectStatusAPIRequestStatusNotLoaded: (state) => {
      state.projectStatusAPIrequestStatus = NOT_LOADED;
    },
    setProjectStatusAPIRequestStatusPending: (state) => {
      state.projectStatusAPIrequestStatus = PENDING;
    },
    setProjectStatusAPIRequestStatusSuccess: (state) => {
      state.projectStatusAPIrequestStatus = SUCCESS;
    },
    setProjectStatusAPIRequestStatusFailed: (state) => {
      state.projectStatusAPIrequestStatus = FAILED;
    },
    setProjectStatusAPIErrors: (state, action) => {
      state.projectStatusErrors = action.payload.projectStatusErrors;
    },
    resetProjectFinanceFields: (state) => {
      state.projectFinanceDetails = null;
      state.projectFundingOptions = null;
      state.projectFundingOptionsAPIStatus = NOT_LOADED;
      state.projectStatusErrors = null;
      state.projectStatusAPIrequestStatus = NOT_LOADED;
    },
    onCreateProjectFromMultipleInsights: (state, action) => {
      const { insightsToCreateProject } = action.payload;
      state.insightsToCreateProject = insightsToCreateProject;
    },
    onDeleteInsightsToCreateProject: (state) => {
      state.insightsToCreateProject = [];
    },
  },
});

export { getInitialState };
export default projectCreate;
