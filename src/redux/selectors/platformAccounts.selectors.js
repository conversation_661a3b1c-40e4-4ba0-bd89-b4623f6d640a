// NOTE: This is a selectors file that corresponds with the platform accounts reducer in the src/creativeAnalytics folder.
// This is a little weird, but since we're trying not to add new code to src/creativeAnalytics, it seemed like the less confusing thing to do.
import {
  getSanitizedAdAccountListData,
  isValidAdAccountForCreativeScoring,
} from '../../creativeScoring/featureServices/transforms';
import { createSelector } from 'reselect';
import { PAGE_PLATFORMS } from '../../constants/platform.constants';
import {
  ACCOUNT_ACCOUNT_TYPE,
  GROUP_ACCOUNT_TYPE,
} from '../../constants/ci.constants';
import { PLATFORM } from '../../constants';
import { getMinimalAdAccountData } from '../../creativeScoring/featureServices/transforms';

const { platformColorIconUrls } = PLATFORM;

export const getIsPlatformAccountsLoaded = (state) =>
  state.platformAccounts.accountsLoaded;
export const getPlatformAccountsLoadingStatus = (state) =>
  state.platformAccounts.status;
export const getAccounts = (state) => state.platformAccounts.accounts;
export const getInactiveAccounts = (state) =>
  state.platformAccounts.inactiveAccounts;
export const getActiveAccounts = (state) =>
  state.platformAccounts.activeAccounts;
export const getAccountGroups = (state) => state.platformAccounts.accountGroups;
export const getPlatformCount = (state) => state.platformAccounts.platformCount;
export const getCurrentPlatformAccount = (state) =>
  state.platformAccounts.currentSelectedAccount;
export const getCurrentSelectedAccountType = (state) =>
  state.platformAccounts.currentSelectedAccount?.type;
export const getCurrentSelectedAccountPlatform = (state) =>
  state.platformAccounts.currentSelectedAccount?.platform.toLowerCase();

export const getHasConnectedAccounts = (state) =>
  Boolean(
    state.platformAccounts.accountsLoaded &&
      state.platformAccounts.activeAccounts.length,
  );

export const getIsCurrentAccountProcessingComplete = (state) => {
  const { currentSelectedAccount } = state.platformAccounts;

  return (
    (currentSelectedAccount?.type === ACCOUNT_ACCOUNT_TYPE &&
      currentSelectedAccount.processingComplete) ||
    currentSelectedAccount?.type === GROUP_ACCOUNT_TYPE
  );
};

export const getHasCompletedProcessingAccounts = (state) => {
  const accountsCompletedImport =
    state.platformAccounts?.activeAccounts?.filter(
      (account) => account.processingComplete,
    );
  return Boolean(accountsCompletedImport && accountsCompletedImport.length);
};

export const getAdAccountCount = (state) =>
  state.platformAccounts.accounts.length +
  state.platformAccounts.accountGroups.length;

export const getActiveAccountsExceptPages = createSelector(
  getActiveAccounts,
  (activeAccounts) => {
    const upperCasedPagePlatforms = new Set(
      PAGE_PLATFORMS.map((platform) => platform.toUpperCase()),
    );
    return activeAccounts.filter(
      (account) =>
        !upperCasedPagePlatforms.has(account.platform?.toUpperCase()),
    );
  },
);

export const getActiveAccountsWithPages = createSelector(
  getActiveAccounts,
  (activeAccounts) => {
    const filteredAccounts = activeAccounts.reduce((accounts, account) => {
      if (PAGE_PLATFORMS.includes(account.platform)) {
        accounts.push(account);
      }

      return accounts;
    }, []);
    return filteredAccounts;
  },
);

export const getAccountWithAdAccountId = (state, adAccountId) => {
  return state.platformAccounts?.accounts?.find(
    (account) =>
      account.adAccountId === adAccountId || account.accountId === adAccountId,
  );
};

export const getGroupWithIdAndPlatformIdentifier = (
  state,
  adAccountGroupId,
  platform,
) => {
  const accountGroup = state.platformAccounts?.accountGroups?.find((group) => {
    return (
      group.id?.toString() === adAccountGroupId?.toString() &&
      platform?.toLowerCase() === group.platform?.toLowerCase()
    );
  });

  return { ...accountGroup };
};

export const hasActiveAccounts = createSelector(
  getActiveAccountsExceptPages,
  (activeAccounts) => {
    return Boolean(activeAccounts && activeAccounts.length);
  },
);

export const getActiveAccountsWithPagesForCompliance = createSelector(
  getActiveAccountsWithPages,
  (activeAccounts) => {
    return getSanitizedAdAccountListData(activeAccounts);
  },
);

export const getInactiveAccountsForCompliance = createSelector(
  getInactiveAccounts,
  (inactiveAccounts) => {
    return getSanitizedAdAccountListData(inactiveAccounts);
  },
);

// Although similar, the two selector creators below are meaningfully distinct modules given their input selectors and the data shapes they return.

export const getActiveAccountsExceptPagesForCompliance = createSelector(
  getActiveAccounts,
  (activeAccounts) => {
    const filteredAccountsList = [];

    for (const account of activeAccounts) {
      const newAccountObject = getMinimalAdAccountData(account);
      if (isValidAdAccountForCreativeScoring(newAccountObject)) {
        filteredAccountsList.push(newAccountObject);
      }
    }

    return filteredAccountsList;
  },
);

export const getActiveAccountsForReportsModal = createSelector(
  [getActiveAccounts],
  (activeAccounts) => {
    const filteredAccountsList = [];

    for (const account of activeAccounts) {
      const newAccountObject = getMinimalAdAccountData(account);
      if (isValidAdAccountForCreativeScoring(newAccountObject)) {
        filteredAccountsList.push({
          id: Number(newAccountObject.id),
          accountId: newAccountObject.id,
          name: newAccountObject.name,
          iconUrl: platformColorIconUrls[newAccountObject.platformIdentifier],
          ...newAccountObject,
        });
      }
    }

    return filteredAccountsList;
  },
);
