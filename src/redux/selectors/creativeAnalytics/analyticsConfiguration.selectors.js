export const getAnalyticsConfiguration = (state) =>
  state.analyticsConfiguration;
export const getReportTableView = (state) =>
  state.analyticsConfiguration.reportTableView;
export const getSelectedOpCompareOption = (state) =>
  state.analyticsConfiguration.selectedOpCompareOption;
export const getSelectedRows = (state) =>
  state.analyticsConfiguration.selectedRows;
export const getIsSelectedRowsViewActive = (state) =>
  state.analyticsConfiguration.isSelectedRowsViewActive;
export const getAnalyticsFiltersSelectedObjectives = (state) =>
  state.analyticsConfiguration.filters.selectedObjectiveFilters;
export const getAnalyticsFiltersSelectedPlacements = (state) =>
  state.analyticsConfiguration.filters.selectedPlacementFilters;
export const getAnalyticsFiltersAvailableCampaigns = (state) =>
  state.analyticsConfiguration.filters.availableCampaignFilters;
export const getAnalyticsFiltersSelectedCampaigns = (state) =>
  state.analyticsConfiguration.filters.selectedCampaignFilters;
export const getAnalyticsFiltersSelectedAdSets = (state) =>
  state.analyticsConfiguration.filters.selectedAdSetFilters;
export const getAnalyticsFiltersSelectedMediaTypes = (state) =>
  state.analyticsConfiguration.filters.selectedMediaTypeFilters;
export const getAreObjectivesDisabled = (state) =>
  state.analyticsConfiguration.areObjectivesDisabled;
export const getArePlacementsDisabled = (state) =>
  state.analyticsConfiguration.arePlacementsDisabled;
export const getAreCampaignsDisabled = (state) =>
  state.analyticsConfiguration.areCampaignsDisabled;
export const getAnalyticsFiltersCreatedWithVidmob = (state) =>
  state.analyticsConfiguration.filters.createdByVidmob;
export const getAnalyticsFiltersSparkAds = (state) =>
  state.analyticsConfiguration.filters.isSparkAdsFilterActive;
export const getAnalyticsFiltersSponsoredAds = (state) =>
  state.analyticsConfiguration.filters.isSponsoredAdsActive;
export const getAnalyticsFilterShowAppAds = (state) =>
  state.analyticsConfiguration.filters.isShowAppAdsActive;

export const getAnalyticsFiltersSelectedStartDate = (state) =>
  state.analyticsConfiguration.filters.analyticsStartDate;
export const getAnalyticsFiltersSelectedEndDate = (state) =>
  state.analyticsConfiguration.filters.analyticsEndDate;

export const getImpressionFilterValues = (state) => {
  const { impressionFilterMinValue, impressionFilterMaxValue } =
    state.analyticsConfiguration.filters;

  return { impressionFilterMinValue, impressionFilterMaxValue };
};

export const getKpiLoadingStatus = (state) =>
  state.analyticsConfiguration.kpiLoadingStatus;
export const getKpiList = (state) => state.analyticsConfiguration.kpiList;

export const getSelectedKpi = (state) =>
  state.analyticsConfiguration.selectedKpi;
export const getIsAddNewColumnTransitioning = (state) =>
  state.analyticsConfiguration.isAddNewColumnTransitioning;
export const getIsPercentLiftViewEnabled = (state) =>
  state.analyticsConfiguration.isPercentLiftViewEnabled;

export const getCurrentCreativeIntelligenceView = (state) =>
  state.analyticsConfiguration.currentCreativeIntelligenceView;
export const getCurrentReportBreakdown = (state) =>
  state.analyticsConfiguration.currentReportBreakdown;

export const getIsTagGroupUpdating = (state) =>
  state.analyticsConfiguration.isTagGroupUpdating;

export const getIsElementMediaModalOpen = (state) =>
  state.analyticsConfiguration.elementMediaModal.isElementMediaModalOpenRedux;

export const getElementMediaModalData = (state) =>
  state.analyticsConfiguration.elementMediaModal;

export const getSelectedPlacementNestingOption = (state) =>
  state.analyticsConfiguration.filters.selectedPlacementNestingOption;

export const getSharedFilterId = (state) =>
  state.analyticsConfiguration.sharedFilterId;
export const getSharedFilterObject = (state) =>
  state.analyticsConfiguration.sharedFilterObject;
export const getHaveSharedFiltersBeenApplied = (state) =>
  state.analyticsConfiguration.haveSharedFiltersBeenApplied;
export const getAnalyticsDates = (state) => ({
  analyticsStartDate: state.analyticsConfiguration.filters.analyticsStartDate,
  analyticsEndDate: state.analyticsConfiguration.filters.analyticsEndDate,
});
