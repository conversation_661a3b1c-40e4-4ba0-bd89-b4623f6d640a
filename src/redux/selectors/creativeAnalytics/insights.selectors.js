import {
  CREATIVE_EXAMPLE_TYPE_MEDIA,
  CREATIVE_EXAMPLE_VIEW_TYPES,
} from '../../../constants/insights.constants';
import { createSelector } from 'reselect';
import { CUSTOM_COMPARE_REPORT } from '../../../constants/creativeAnalytics.constants';
import {
  getCurrentCreativeIntelligenceView,
  getIsSelectedRowsViewActive,
  getReportTableView,
} from './analyticsConfiguration.selectors';
import { REPORT_TABLE_VIEW } from '../../../constants/ci.constants';

export const getIsInsightCreatePanelVisible = (state) =>
  state.insights.isInsightCreatePanelVisible;
export const getActiveInsight = (state) => state.insights.activeInsight;
export const getCreativeExampleToReload = (state) =>
  state.insights.creativeExampleToReload;
export const getCustomGroupColumnsCreativeExamples = (state) =>
  state.insights.customGroupColumnsCreativeExamples;
export const getSelectedRowsCreativeExamples = (state) =>
  state.insights.selectedRowsCreativeExamples;
export const getInsightWorkspaceIds = (state) => state.insights.workspaceIds;

const getSelectedCustomGroupCreativeExamples = (
  selectedCustomGroupCreativeExamples,
) => {
  const insightCreativeExamples = [];
  const columnsValues = Object.values(selectedCustomGroupCreativeExamples);

  if (columnsValues?.length) {
    columnsValues?.forEach((column) => {
      const { selectedPlatformMediaIds: examplePlatformMediaIds } = column;
      if (examplePlatformMediaIds.length) {
        insightCreativeExamples.push({
          exampleType: CREATIVE_EXAMPLE_TYPE_MEDIA,
          tagValue: column.columnInfo.group.name,
          examplePlatformMediaIds,
        });
      }
    });
  }

  return insightCreativeExamples;
};

const getSelectedRowsCreativeExamplesForInsightCreation = (
  selectedRowsCreativeExamples,
) => {
  const selectedRowsValues = Object.values(selectedRowsCreativeExamples);

  return selectedRowsValues?.map((selectedRow) => {
    const { tagType, tagValue, view } = selectedRow.rowInfo;
    const { selectedPlatformMediaIds: examplePlatformMediaIds } = selectedRow;

    return {
      exampleType: CREATIVE_EXAMPLE_VIEW_TYPES[view],
      ...(tagType && REPORT_TABLE_VIEW.ELEMENT.value === view
        ? { tagType }
        : {}),
      ...([
        REPORT_TABLE_VIEW.ELEMENT.value,
        REPORT_TABLE_VIEW.DURATION?.value,
        REPORT_TABLE_VIEW.KPI.value,
      ].includes(view)
        ? { tagValue }
        : {}),
      ...(examplePlatformMediaIds.length ? { examplePlatformMediaIds } : {}),
    };
  });
};

export const getActiveInsightCreativeExamplesForInsightCreation =
  createSelector(
    [
      getSelectedRowsCreativeExamples,
      getCustomGroupColumnsCreativeExamples,
      getReportTableView,
      getCurrentCreativeIntelligenceView,
      getIsSelectedRowsViewActive,
    ],
    (
      selectedRowsCreativeExamples,
      selectedCustomGroupCreativeExamples,
      rowView,
      currentCreativeIntelligenceView,
      isSelectedRowsViewActive,
    ) => {
      if (!isSelectedRowsViewActive) {
        return [];
      }

      const isCustomComparisonView =
        currentCreativeIntelligenceView === CUSTOM_COMPARE_REPORT;
      const isKPIReportTableView = rowView?.id === REPORT_TABLE_VIEW?.KPI.id;
      const shouldShowCustomGroupCreativeExamples =
        isSelectedRowsViewActive &&
        isKPIReportTableView &&
        isCustomComparisonView;

      if (shouldShowCustomGroupCreativeExamples) {
        return getSelectedCustomGroupCreativeExamples(
          selectedCustomGroupCreativeExamples,
        );
      }

      return getSelectedRowsCreativeExamplesForInsightCreation(
        selectedRowsCreativeExamples,
      );
    },
  );
