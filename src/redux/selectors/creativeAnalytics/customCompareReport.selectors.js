export const getCustomCompareLoadingStatus = (state) =>
  state.customCompareReport.status;
export const getCustomCompareFetchingError = (state) =>
  state.customCompareReport?.error;
export const getCreativeGroupsInCustomCompareReport = (state) =>
  state.customCompareReport.columnItems
    .map((col) => col.group)
    .filter((group) => group.isSavedGroup);
export const getSavedReport = (state) => state.customCompareReport.savedReport;
export const getSavedReportId = (state) =>
  state.customCompareReport.savedReport?.id;
export const getSavedReportLoadingStatus = (state) =>
  state.customCompareReport.savedReportLoadingStatus;
