import { createSelector } from 'reselect';
import moment from 'moment';
import GLOBALS from '../../../constants/global.constants';
import { formatAndFilterCreativeGroups } from '../../../utils/formatCreativeGroupingsObject';
import {
  getAnalyticsFiltersSelectedEndDate,
  getAnalyticsFiltersSelectedStartDate,
} from './analyticsConfiguration.selectors';
import { CREATIVE_MANAGER_TABLE_VIEWS } from '../../../creativeAnalytics/__pages/CreativeManager/helpers/CreativeManagerConstants';

const { DEFAULT_DATE_FORMAT } = GLOBALS;

export const getSelectedItems = (state) =>
  state.creativeGroupsStorage.selectedItems;
export const getSelectedItemsByView = (state, view) =>
  view === CREATIVE_MANAGER_TABLE_VIEWS.CREATIVE
    ? state.creativeGroupsStorage.selectedItems.creatives
    : state.creativeGroupsStorage.selectedItems.ads;
export const getItemsInInactiveGroupsByView = (state, view) => {
  const inactiveCreatives = state.creativeGroupsStorage.compareGroups
    .filter(
      (group) =>
        group.groupIndex !== state.creativeGroupsStorage.activeGroupIndex &&
        group.media.length,
    )
    .map((group) => group.media.map((media) => media.platformMediaId))
    .flat(2);
  if (view === CREATIVE_MANAGER_TABLE_VIEWS.CREATIVE) {
    return inactiveCreatives;
  }

  return inactiveCreatives
    .map(
      (creativeId) =>
        state.creativeGroupsStorage.adsKeyedByCreativeId[creativeId],
    )
    .flat();
};

export const getMediaForSelectedCreatives = (state) =>
  state.creativeGroupsStorage.selectedItems.creatives.map(
    (creativeId) =>
      state.creativeGroupsStorage.mediaKeyedByPlatformMediaId[creativeId],
  );
export const getCreativeGroupsStorage = (state) => state.creativeGroupsStorage;
export const getCreativeGroupsLoadingStatus = (state) =>
  state.creativeGroupsStorage.creativeGroupsLoadingStatus;
export const getMediaKeyedByPlatformMediaIdLoadingStatus = (state) =>
  state.creativeGroupsStorage.mediaKeyedByPlatformMediaIdLoadingStatus;
export const getFilteredCreativeGroupsLoadingStatus = (state) =>
  state.creativeGroupsStorage.filteredCreativeGroupsLoadingStatus;
export const getActiveGroupCreateUpdateLoadingStatus = (state) =>
  state.creativeGroupsStorage.activeGroupCreateUpdateLoadingStatus;
export const getFilteredCreativeGroupsAndStatus = (state) => ({
  filteredCreativeGroups: state.creativeGroupsStorage.filteredCreativeGroups,
  filteredCreativeGroupsLoadingStatus:
    state.creativeGroupsStorage.filteredCreativeGroupsLoadingStatus,
});
export const getSelectedCreativeGroupsInDrawer = (state) =>
  state.creativeGroupsStorage.compareGroups.filter(
    (group) => group.isSavedGroup,
  );
export const getCreativeGroups = (state) =>
  state.creativeGroupsStorage.creativeGroups;
export const getCompareGroups = (state) =>
  state.creativeGroupsStorage.compareGroups;
export const getActiveGroupIndex = (state) =>
  state.creativeGroupsStorage.activeGroupIndex;
export const getActiveCompareGroup = (state) =>
  state.creativeGroupsStorage.compareGroups[
    state.creativeGroupsStorage.activeGroupIndex
  ];
export const getAccountMediaAsObject = (state) =>
  state.creativeGroupsStorage.mediaKeyedByPlatformMediaId;
export const getCompareGroupsFormattedForLocalStorageOrReport = (state) => {
  const compareGroups = [...state.creativeGroupsStorage.compareGroups];
  const formattedGroups = formatAndFilterCreativeGroups(compareGroups);
  return formattedGroups;
};

export const getStartAndEndDateForPersistingGroup = createSelector(
  [
    getAnalyticsFiltersSelectedStartDate,
    getAnalyticsFiltersSelectedEndDate,
    getActiveCompareGroup,
  ],
  (analyticsStartDate, analyticsEndDate, activeCompareGroup) => {
    const earliestStartDate =
      activeCompareGroup.startDate &&
      moment(activeCompareGroup.startDate).isBefore(analyticsStartDate)
        ? moment(activeCompareGroup.startDate).format(DEFAULT_DATE_FORMAT)
        : moment(analyticsStartDate).format(DEFAULT_DATE_FORMAT);
    const latestEndDate =
      activeCompareGroup.endDate &&
      moment(activeCompareGroup.endDate).isAfter(analyticsEndDate)
        ? moment(activeCompareGroup.endDate).format(DEFAULT_DATE_FORMAT)
        : moment(analyticsEndDate).format(DEFAULT_DATE_FORMAT);
    return { startDate: earliestStartDate, endDate: latestEndDate };
  },
);

export const getFilteredCreativeGroups = createSelector(
  [getCreativeGroups, getAccountMediaAsObject],
  (creativeGroups, assetsKeyedByPlatformMediaId) => {
    const filteredCreativeGroups = [];
    creativeGroups.forEach((group) => {
      let willGroupBeShown = false;
      const groupMedia = [];
      group.platformMedias.forEach((media) => {
        const detailedMediaIfAvailable =
          assetsKeyedByPlatformMediaId[media.platformMediaId];
        if (detailedMediaIfAvailable) {
          willGroupBeShown = true;
          groupMedia.push({
            ...detailedMediaIfAvailable,
            isMediaViewable: true,
          });
        } else {
          groupMedia.push({
            ...media,
            isMediaViewable: false,
          });
        }
      });
      if (willGroupBeShown) {
        filteredCreativeGroups.push({
          ...group,
          media: groupMedia,
          willGroupBeShown,
        });
      }
    });
    return filteredCreativeGroups;
  },
);

export const getCompareGroupsWithUpdatedMediaAvailability = createSelector(
  [getCompareGroups, getAccountMediaAsObject],
  (compareGroups, assetsKeyedByPlatformMediaId) => {
    return compareGroups.map((compareGroup) => {
      return {
        ...compareGroup,
        media: compareGroup.media?.map((media) => {
          const { platformMediaId } = media;
          if (
            platformMediaId &&
            assetsKeyedByPlatformMediaId[platformMediaId]
          ) {
            return {
              ...assetsKeyedByPlatformMediaId[platformMediaId],
              isMediaViewable: true,
            };
          }

          return media;
        }),
      };
    });
  },
);
