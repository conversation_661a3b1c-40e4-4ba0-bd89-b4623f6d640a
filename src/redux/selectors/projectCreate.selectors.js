export const getProjectBeingCreated = (state) => {
  return state.projectCreate.projectBeingCreated;
};

export const getProjectBeingCreatedId = (state) => {
  return state.projectCreate?.projectBeingCreated?.id;
};

export const getProjectStatus = (state) => {
  return state.projectCreate.projectStatus;
};

export const getProjectCreateAPIRequestStatus = (state) => {
  return state.projectCreate.projectAPIRequestStatus;
};

export const getProjectCreateAPIRequestErrorCode = (state) => {
  return state.projectCreate.errorCode;
};

export const getProjectCreateCurrentStep = (state) => {
  return state.projectCreate.currentStep;
};

export const getProjectCreateStepStatus = (state) => {
  return state.projectCreate.currentStepStatus;
};

export const getProjectCreateStepDirection = (state) => {
  return state.projectCreate.stepDirection;
};

export const getProjectBeingCreatedOutputGroupsData = (state) => {
  return state.projectCreate?.projectBeingCreated?.outputGroupsData;
};

export const getPriceQuote = (state) => {
  return state.projectCreate?.projectBeingCreated?.priceQuote;
};

export const getPriceAPIRequestStatus = (state) => {
  return state.projectCreate.priceAPIRequestStatus;
};

export const getPriceAPIErrorMessage = (state) => {
  return state.projectCreate?.priceErrorCode;
};

export const getLoadProjectForEditFailed = (state) => {
  return state.projectCreate?.loadProjectForEditFailed;
};

export const getCheckForSaveAndClose = (state) => {
  return state.projectCreate.saveAndClose;
};

export const getOutputGroupType = (state) => {
  return state.projectCreate?.projectBeingCreated?.outputGroupType;
};

export const getProjectBeingCreatedProduct = (state) => {
  return state.projectCreate?.projectBeingCreated?.product;
};

export const getProjectBeingCreatedVariationTypes = (state) => {
  return state.projectCreate?.projectBeingCreated?.variationTypes;
};

export const getProjectLoadingAPIRequestStatus = (state) => {
  return state.projectCreate?.projectLoadingAPIRequestStatus;
};

export const getProjectCreatedFromInsightBannerVisible = (state) => {
  return state.projectCreate?.projectCreatedFromInsightBannerVisible;
};

export const getProjectFinanceDetails = (state) => {
  return state.projectCreate?.projectFinanceDetails;
};

export const getProjectFundingOptions = (state) => {
  return state.projectCreate?.projectFundingOptions;
};

export const getProjectFundingOptionsAPIRequestStatus = (state) => {
  return state.projectCreate?.projectFundingOptionsAPIStatus;
};

export const getPartnerDefaultFundingMethodName = (state) => {
  return state.projectCreate?.projectFundingOptions?.partnerDefaultFundingMethod
    ?.fundingMethod?.name;
};

export const getProjectStatusAPIRequestStatus = (state) => {
  return state.projectCreate?.projectStatusAPIrequestStatus;
};

export const getProjectStatusErrors = (state) => {
  return state.projectCreate?.projectStatusErrors;
};

export const getInsightsToCreateProject = (state) => {
  return state.projectCreate?.insightsToCreateProject;
};
