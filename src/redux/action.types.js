export const SET_LOCALE = 'SET_LOCALE';
// REDUX
export const RESET = 'RESET';
// Ad accounts
export const AD_ACCOUNTS_PENDING = 'AD_ACCOUNTS_PENDING';
export const AD_ACCOUNTS_FAILED = 'AD_ACCOUNTS_FAILED';
export const AD_ACCOUNTS_LOADED = 'AD_ACCOUNTS_LOADED';
export const SET_AD_ACCOUNT_IS_ENABLED = 'SET_AD_ACCOUNT_IS_ENABLED';

// Assets actions not tied to slices
export const LOAD_ASSETS_PAGE = 'LOAD_ASSETS_PAGE';
export const RENAME_ASSETS_FOLDER = 'RENAME_ASSETS_FOLDER';
export const CREATE_ASSETS_FOLDER = 'CREATE_ASSETS_FOLDER';
export const LOAD_ASSETS_ITEMS = 'LOAD_ASSETS_ITEMS';
export const MOVE_ASSETS_ITEMS = 'MOVE_ASSETS_ITEMS';
export const DELETE_ASSETS_ITEMS = 'DELETE_ASSETS_ITEMS';
export const PAGINATE_ASSETS_PAGE_ITEMS = 'PAGINATE_ASSETS_PAGE_ITEMS';
export const SELECT_ALL_ASSETS_PAGE_ITEMS = 'SELECT_ALL_ASSETS_PAGE_ITEMS';
export const CHANGE_ASSETS_PAGE_SORT = 'CHANGE_ASSETS_PAGE_SORT';
export const UPDATE_STOCK_ASSET_IS_PENDING_PURCHASE =
  'UPDATE_STOCK_ASSET_IS_PENDING_PURCHASE';
export const DRAG_DROP_FOLDER_CREATE = 'DRAG_DROP_FOLDER_CREATE';
export const BULK_UPLOAD_ASSETS = 'BULK_UPLOAD_ASSETS';

// Ad accounts scopes
export const PLATFORM_ACCOUNT_SCOPES_PENDING =
  'PLATFORM_ACCOUNT_SCOPES_PENDING';
export const PLATFORM_ACCOUNT_SCOPES_FAILED = 'PLATFORM_ACCOUNT_SCOPES_FAILED';
export const PLATFORM_ACCOUNT_SCOPES_LOADED = 'PLATFORM_ACCOUNT_SCOPES_LOADED';

// ORGANIZATION
export const SET_ORGANIZATION_PERMISSIONS = 'SET_ORGANIZATION_PERMISSIONS';
export const SET_ORGANIZATION_BRAND_LABELS = 'SET_ORGANIZATION_BRAND_LABELS';
export const CREATE_BRAND_LABEL = 'CREATE_BRAND_LABEL';
export const UPDATE_BRAND_LABEL = 'UPDATE_BRAND_LABEL';
export const SET_AD_ACCOUNTS_BY_ORGANIZATION =
  'SET_AD_ACCOUNTS_BY_ORGANIZATION';
export const SET_USER_ORGANIZATIONS = 'SET_USER_ORGANIZATIONS';
export const UPDATE_AD_ACCOUNTS_FOR_ORGANIZATION =
  'UPDATE_AD_ACCOUNTS_FOR_ORGANIZATION';
export const SET_LIGHTWEIGHT_WORKSPACES_FOR_ORG =
  'SET_LIGHTWEIGHT_WORKSPACES_FOR_ORG';
export const SET_LIGHTWEIGHT_MEMBERS_FOR_ORG =
  'SET_LIGHTWEIGHT_MEMBERS_FOR_ORG';
export const SET_WORKSPACE_ROLES = 'SET_WORKSPACE_ROLES';
export const SUBMIT_BULK_INVITES_TO_WORKSPACES =
  'SUBMIT_BULK_INVITES_TO_WORKSPACES';
export const SET_ORG_PENDING_INVITES = 'SET_ORG_PENDING_INVITES';
export const RESEND_ORG_USER_INVITE = 'RESEND_ORG_USER_INVITE';
export const CANCEL_ORG_USER_INVITE = 'CANCEL_ORG_USER_INVITE';
export const REMOVE_USER_FROM_ORG = 'REMOVE_USER_FROM_ORG';
export const SET_WORKSPACE_PROJECTS = 'SET_WORKSPACE_PROJECTS';

// USER
// - User actions from the standard API
export const SET_USER = 'SET_USER';
export const RESET_USER = 'RESET_USER';
export const LOGIN_ERROR = 'LOGIN_ERROR';
export const LOGIN_ERROR_RESET = 'LOGIN_ERROR_RESET';
export const UPDATE_PARTNER_IN_PARTNER_LIST = 'UPDATE_PARTNER_IN_PARTNER_LIST';
export const ADD_NEW_PARTNER_IN_PARTNER_LIST =
  'ADD_NEW_PARTNER_IN_PARTNER_LIST';
// - User actions from the analytics API
export const ANALYTICS_USER_DATA_FAILED = 'ANALYTICS_USER_DATA_FAILED';
export const ANALYTICS_USER_DATA_UNLOADED = 'ANALYTICS_USER_DATA_UNLOADED';
export const ANALYTICS_USER_DATA_LOADED = 'ANALYTICS_USER_DATA_LOADED';
export const ANALYTICS_USER_DATA_PENDING = 'ANALYTICS_USER_DATA_PENDING';
// - User actions for retrieving platform accounts
export const PLATFORM_ACCOUNTS_DATA_FAILED = 'PLATFORM_ACCOUNTS_DATA_FAILED';
export const PLATFORM_ACCOUNTS_DATA_UNLOADED =
  'PLATFORM_ACCOUNTS_DATA_UNLOADED';
export const PLATFORM_ACCOUNTS_DATA_LOADED = 'PLATFORM_ACCOUNTS_DATA_LOADED';
export const PLATFORM_ACCOUNTS_DATA_PENDING = 'PLATFORM_ACCOUNTS_DATA_PENDING';
// - Misc user actions
export const UPDATE_PLATFORM_ACCOUNT = 'UPDATE_PLATFORM_ACCOUNT';
// DEEP LINKS
export const SET_DEEP_LINK_INFO = 'SET_DEEP_LINK_INFO';
export const REMOVE_DEEP_LINK_INFO = 'REMOVE_DEEP_LINK_INFO';
// UPLOADS
export const ADD_UPLOAD_TO_LIST = 'ADD_UPLOAD_TO_LIST';
export const CANCEL_UPLOAD = 'CANCEL_UPLOAD';
export const CANCEL_UPLOAD_ITERATION_MEDIA = 'CANCEL_UPLOAD_ITERATION_MEDIA';
export const COMPLETE_UPLOAD = 'COMPLETE_UPLOAD';
export const FAIL_UPLOAD = 'FAIL_UPLOAD';
export const REMOVED_UPLOAD_FROM_LIST = 'REMOVED_UPLOAD_FROM_LIST';
export const TOGGLE_UPLOADER = 'TOGGLE_UPLOADER';
export const UPDATE_CURRENT_UPLOAD = 'UPDATE_CURRENT_UPLOAD';
export const CANCEL_TUS_UPLOAD = 'CANCEL_TUS_UPLOAD';
// CONFIRMATION BARS
export const DISMISS_CONFIRMATION_BAR = 'DISMISS_CONFIRMATION_BAR';
export const ADD_CONFIRMATION_BAR = 'ADD_CONFIRMATION_BAR';
// Generic confirmation modal
export const SHOW_CONFIRMATION_MODAL = 'SHOW_CONFIRMATION_MODAL';
export const HIDE_CONFIRMATION_MODAL = 'HIDE_CONFIRMATION_MODAL';
// Invite modal
export const SHOW_INVITE_MODAL = 'SHOW_INVITE_MODAL';
export const HIDE_INVITE_MODAL = 'HIDE_INVITE_MODAL';
export const SET_PROJECT_TEAM_PAGE_OUTDATED_FLAG =
  'SET_PROJECT_TEAM_PAGE_OUTDATED_FLAG';
// JWPlayer
export const REGISTER_PLAYER = 'REGISTER_PLAYER';
export const UNREGISTER_PLAYER = 'UNREGISTER_PLAYER';
export const TOGGLE_PLAY = 'TOGGLE_PLAY';
export const PLAY_VIDEO = 'PLAY_VIDEO';
export const PAUSE_VIDEO = 'PAUSE_VIDEO';
export const SET_VIDEO_VOLUME = 'SET_VIDEO_VOLUME';
export const SET_VIDEO_QUALITY = 'SET_VIDEO_QUALITY';
export const SET_VIDEO_POSITION = 'SET_VIDEO_POSITION';
export const TOGGLE_MUTE = 'TOGGLE_MUTE';
export const MUTE_VIDEO = 'MUTE_VIDEO';
export const UNMUTE_VIDEO = 'UNMUTE_VIDEO';
export const PAUSE_PLAYERS = 'PAUSE_PLAYERS';
export const TOGGLE_VIDEO_FULLSCREEN = 'TOGGLE_VIDEO_FULLSCREEN';
export const SET_VIDEO_DURATION = 'SET_VIDEO_DURATION';
export const TOGGLE_VIDEO_REPEAT = 'TOGGLE_VIDEO_REPEAT';
// Partner
export const UPDATE_CURRENT_PARTNER = 'UPDATE_CURRENT_PARTNER';
// Platform Accounts
export const ACTIVATE_ACCOUNT = 'ACTIVATE_ACCOUNT';
// WEBSOCKET
export const SET_WEB_SOCKET_STATE = 'SET_WEB_SOCKET_EVENT';
export const SET_WEB_SOCKET_EVENT_PAYLOAD = 'SET_WEB_SOCKET_EVENT_PAYLOAD';
// Current project
export const CLEAR_CURRENT_PROJECT = 'CLEAR_CURRENT_PROJECT';
export const SET_CURRENT_PROJECT = 'SET_CURRENT_PROJECT';
export const CHECK_CURRENT_PROJECT_KICK_OFF_REQUIREMENTS =
  'CHECK_CURRENT_PROJECT_KICK_OFF_REQUIREMENTS';
export const KICK_OFF_CURRENT_PROJECT = 'KICK_OFF_CURRENT_PROJECT';
// Project outputs
export const CLEAR_PROJECT_OUTPUTS = 'CLEAR_PROJECT_OUTPUTS';
export const CLEAR_PROJECT_OUTPUT_GROUPS = 'CLEAR_PROJECT_OUTPUT_GROUPS';
export const ADD_PROJECT_OUTPUTS = 'ADD_PROJECT_OUTPUTS';
export const ADD_PROJECT_OUTPUT_GROUPS = 'ADD_PROJECT_OUTPUT_GROUPS';
// Project outputs v2
export const START_LOADING_OUTPUTS_PAGE = 'START_LOADING_OUTPUTS_PAGE';
export const UPDATE_OUTPUT_ELEMENTS = 'UPDATE_OUTPUT_ELEMENTS';
export const CLEAR_OUTPUT_ELEMENTS = 'CLEAR_OUTPUT_ELEMENTS';
export const UPDATE_OUTPUT_LOADING_STATE = 'UPDATE_OUTPUT_LOADING_STATE';
export const SET_MEDIA_BUNDLE_PERCENTAGE = 'SET_MEDIA_BUNDLE_PERCENTAGE';
// Partner projects (used in partner project combo invites)
export const CLEAR_PARTNER_PROJECTS = 'CLEAR_PARTNER_PROJECTS';
export const UPDATE_PARTNER_PROJECTS = 'UPDATE_PARTNER_PROJECTS';
// Media Preview
export const PREVIEW_MEDIA = 'PREVIEW_MEDIA';
export const PREVIEW_MEDIA_LIST = 'PREVIEW_MEDIA_LIST';
export const CLOSE_PREVIEW = 'CLOSE_PREVIEW';
// subscriptions
export const GET_CURRENT_SUBSCRIPTIONS = 'GET_CURRENT_SUBSCRIPTIONS';
// Feature Flags
export const GET_FEATURE_FLAGS = 'GET_FEATURE_FLAGS';
export const SET_FEATURE_FLAGS = 'SET_FEATURE_FLAGS';
export const RESET_FEATURE_FLAGS = 'RESET_FEATURE_FLAGS';
// Intercom event actions
export const CLEAR_TOUR_QUERY_PARAM_REQUIREMENT =
  'CLEAR_TOUR_QUERY_PARAM_REQUIREMENT';
export const COMPLETE_TOUR_QUERY_PARAM_REQUIREMENT =
  'COMPLETE_TOUR_QUERY_PARAM_REQUIREMENT';
// analytics user preferences
export const SET_ANALYTICS_USER_PREFERENCES = 'SET_ANALYTICS_USER_PREFERENCES';
// industry actions
export const UPDATE_INDUSTRIES = 'UPDATE_INDUSTRIES';
export const UPDATE_INDUSTRY_LOADING_STATE = 'UPDATE_INDUSTRY_LOADING_STATE';

// Compliance action types
export const SET_CONTENT_AUDIT_AGGREGATE_CRITERIA_RESULTS =
  'SET_CONTENT_AUDIT_AGGREGATE_CRITERIA_RESULTS';
export const SET_CONTENT_AUDIT_MEDIA_CRITERIA_RESULTS =
  'SET_CONTENT_AUDIT_MEDIA_CRITERIA_RESULTS';
export const SET_MOBILE_FITNESS_SCORE_RESULTS =
  'SET_MOBILE_FITNESS_SCORE_RESULTS';
export const SET_CONTENT_AUDIT_CSV_DATA = 'SET_CONTENT_AUDIT_CSV_DATA';
export const SET_CONTENT_AUDIT_ASSET_VIEW_RESULTS =
  'SET_CONTENT_AUDIT_ASSET_VIEW_RESULTS';
export const SET_AND_LOAD_SCORECARD_FROM_URL =
  'SET_AND_LOAD_SCORECARD_FROM_URL';
export const SET_SCORECARD_DETAILS_AGGREGATE_SCORES_V3 =
  'SET_SCORECARD_DETAILS_AGGREGATE_SCORES_V3';
export const SET_SCORECARD_DETAILS_MEDIA_SCORES_V3 =
  'SET_SCORECARD_DETAILS_MEDIA_SCORES_V3';
export const SET_CONTENT_AUDIT_ASSET_VIEW_RESULTS_V2 =
  'SET_CONTENT_AUDIT_ASSET_VIEW_RESULTS_V2';
export const GET_HAS_SCORECARDS = 'GET_HAS_SCORECARDS';

export const SET_FILTERED_BATCHES = 'SET_FILTERED_BATCHES';
export const SUBMIT_BATCH = 'SUBMIT_BATCH';
export const SUBMIT_BATCH_UPDATE_SCORE = 'SUBMIT_BATCH_UPDATE_SCORE';
export const RESUBMIT_INFLIGHT_BATCH = 'RESUBMIT_INFLIGHT_BATCH';
export const CREATE_INFLIGHT_SCORECARD = 'CREATE_INFLIGHT_SCORECARD';
export const RESUBMIT_INFLIGHT_BATCH_UPDATE_SCORE =
  'RESUBMIT_INFLIGHT_BATCH_UPDATE_SCORE';
export const RETRIEVE_COUNTRIES_LIST = 'RETRIEVE_COUNTRIES_LIST';
export const SET_CRITERIA_MGMT_PLATFORM = 'SET_CRITERIA_MGMT_PLATFORM';
export const CLEAR_CRITERIA_MGMT_PLATFORM = 'CLEAR_CRITERIA_MGMT_PLATFORM';
export const LOAD_PARTNER_CRITERIA_SETS = 'LOAD_PARTNER_CRITERIA_SETS';
export const LOAD_PARTNER_CRITERIA_TEMPLATES =
  'LOAD_PARTNER_CRITERIA_TEMPLATES';
export const SET_PARTNER_BRAND_IDENTIFIERS = 'SET_PARTNER_BRAND_IDENTIFIERS';
export const SET_NEW_BRAND_IDENTIFIER = 'SET_NEW_BRAND_IDENTIFIER';
export const DELETE_PARTNER_CRITERIA = 'DELETE_PARTNER_CRITERIA';
export const CREATE_PARTNER_CRITERIA = 'CREATE_PARTNER_CRITERIA';
export const CREATE_BEST_PRACTICE_CRITERIA = 'CREATE_BEST_PRACTICE_CRITERIA';
export const GET_BEST_PRACTICE_CRITERIA = 'GET_BEST_PRACTICE_CRITERIA';

export const CREATE_BATCH = 'CREATE_BATCH';
export const UPDATE_BATCH = 'UPDATE_BATCH';
export const UPDATE_BATCH_STATUS = 'UPDATE_BATCH_STATUS';
export const UPLOAD_FILES = 'UPLOAD_FILES';
export const LOAD_FOLDER = 'LOAD_FOLDER';
export const REMOVE_ASSET = 'REMOVE_ASSET';
export const REMOVE_ALL_ASSETS = 'REMOVE_ALL_ASSETS';
export const CANCEL_CLEAN_UP = 'CANCEL_CLEAN_UP';
export const UPLOAD_ASSET_VERSION = 'UPLOAD_ASSET_VERSION';
export const UPDATE_SCORECARD = 'UPDATE_SCORECARD';

export const SET_INDIVIDUAL_CRITERIA_RESULT = 'SET_INDIVIDUAL_CRITERIA_RESULT';
export const SET_CRITERIA_IDS = 'SET_CRITERIA_IDS';
export const SET_INDIVIDUAL_ASSET_SCORES = 'SET_INDIVIDUAL_ASSET_SCORES';
export const SET_INFLIGHT_INDIVIDUAL_ASSET_SCORES =
  'SET_INFLIGHT_INDIVIDUAL_ASSET_SCORES';
export const SET_INDIVIDUAL_VIEW_KPI_METRICS =
  'SET_INDIVIDUAL_VIEW_KPI_METRICS';
export const SET_ASSET_VERSIONS = 'SET_ASSET_VERSIONS';
export const SET_SELECTED_ASSET_VERSION_MEDIA_OBJECT =
  'SET_SELECTED_ASSET_VERSION_MEDIA_OBJECT';
export const SET_SELECTED_ASSET_VERSION_SCORES =
  'SET_SELECTED_ASSET_VERSION_SCORES';
export const SET_BATCH_FOR_INDIVIDUAL_ASSET = 'SET_BATCH_FOR_INDIVIDUAL_ASSET';

export const SET_DAM_INDIVIDUAL_MEDIA_SCORES =
  'SET_DAM_INDIVIDUAL_MEDIA_SCORES';
export const SET_DAM_CRITERIA_IDS = 'SET_DAM_CRITERIA_IDS';
export const SET_AND_VERIFY_TOKEN = 'SET_AND_VERIFY_TOKEN';
export const SET_MEDIA_OBJECT = 'SET_MEDIA_OBJECT';
export const SET_CAN_ACCESS_EXECUTIVE_DASHBOARD =
  'SET_CAN_ACCESS_EXECUTIVE_DASHBOARD';

// Creative Scoring Filter Action Types
export const LOAD_FILTERS = 'LOAD_FILTERS';
export const SET_FILTERS_V2 = 'SET_FILTERS';
export const APPLY_REPORT_DETAILS_DATE_FILTER =
  'APPLY_REPORT_DETAILS_DATE_FILTER';
export const LOAD_AD_ACCOUNT_SCORECARD_LIFETIME_DATES =
  'LOAD_AD_ACCOUNT_SCORECARD_LIFETIME_DATES';

// Ad Account Sharing action types
export const REVOKE_ACCT_SHARING_GRANT = 'REVOKE_ACCT_SHARING_GRANT';
export const SET_ACCT_SHARING_AD_ACCOUNT = 'SET_ACCT_SHARING_AD_ACCOUNT';
export const SET_ACCT_SHARING_PLATFORM = 'SET_ACCT_SHARING_PLATFORM';
export const SET_INITIATED_ACCT_SHARING_GRANTS =
  'SET_INITIATED_ACCT_SHARING_GRANTS';
export const SET_ACCT_SHARING_ROLE = 'SET_ACCT_SHARING_ROLE';
export const SET_PARTNER_TEAM_MEMBERS = 'SET_PARTNER_TEAM_MEMBERS';
export const GET_INDUSTRY_LIST = 'GET_INDUSTRY_LIST';
export const SET_INDUSTRY_TO_AD_ACCT = 'SET_INDUSTRY_TO_AD_ACCT';
export const GET_INDUSTRY_FOR_AD_ACCOUNT = 'GET_INDUSTRY_FOR_AD_ACCOUNT';

// Executive Dashboard Action Types
export const LOAD_AGGREGATE_DASHBOARD_DATA = 'LOAD_AGGREGATE_DASHBOARD_DATA';

// Rollup Reports Action Types
export const LOAD_ADHERENCE_REPORT_FILTERS = 'LOAD_ADHERENCE_REPORT_FILTERS';
export const LOAD_ROLL_UP_REPORTS_FILTERS = 'LOAD_ROLL_UP_REPORTS_FILTERS';
