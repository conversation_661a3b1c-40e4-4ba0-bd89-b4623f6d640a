import { watchAssetsPage } from './assets.sagas';
import { watchOutputsPage } from './outputs.sagas';
import { watchComplianceShared } from '../../creativeScoring/redux/sagas/complianceShared.sagas';
import { watchActionsForDataDog } from './actionLogging.sagas';
import { watchUploadQueue } from './projectUploadQueueMedia.sagas';
import { watchComplianceContentAudit } from '../../creativeScoring/redux/sagas/contentAudit.sagas';
import { watchComplianceUploads } from '../../creativeScoring/redux/sagas/complianceUploads.sagas';
import { watchIntegrations } from './integrations.sagas';
import { spawn, all } from 'redux-saga/effects';
import { watchMobileFitnessScore } from '../../creativeScoring/redux/sagas/mobileFitness.sagas';
import { watchActivities } from './activity.sagas.js';
import { watchAdAccountSharing } from './adAcctSharing/adAcctSharing.sagas';
import { watchProject } from './project/project.sagas';
import { watchComplianceBatches } from '../../creativeScoring/redux/sagas/complianceBatches.sagas';
import { watchComplianceCriteriaManagement } from '../../creativeScoring/redux/sagas/complianceCriteriaManagement.sagas';
import { watchIndividualAsset } from '../../creativeScoring/redux/sagas/complianceIndividualAsset.sagas';
import { watchPartners } from './partner/partner.sagas';
import ciRoot from './creativeAnalytics/ciRoot';
import { watchProjectCreate } from './projectCreate/projectCreate.sagas';
import { watchCountries } from './countries/countries.sagas';
import { watchNormsConfiguration } from './normsConfiguration/normsConfiguration.sagas';
import { watchPayment } from './payment/payment.sagas';
import { watchDAMSingleAsset } from '../../creativeScoring/redux/sagas/DAMIntegrationIndividualAssetView.sagas';
import { watchCreativeScoring } from '../../creativeScoring/redux/sagas/creativeScoring.sagas';
import { watchOutputsV2 } from './projectCreate/outputsV2.sagas';
import { watchFiltersV2 } from '../../creativeScoring/redux/sagas/filterPanelV2.sagas';
import { watchScoreOverride } from '../../creativeScoring/redux/sagas/scoreOverride.sagas';
import { watchRollUpReport } from '../../creativeScoring/redux/sagas/rollUpReports.sagas';
import { watchRollupReportsManagement } from '../../creativeScoring/redux/sagas/rollupReportsManagement.sagas';
import { watchOrganization } from '../../userManagement/redux/sagas/organization.sagas';
import { watchConvertedMedia } from './gifSupport/convertedMedia.sagas';
import { watchAdAccounts } from './adAccountHealthDashboard/adAccountHealthDashboard.saga';
import { watchPeople } from '../../userManagement/redux/sagas/peoples.sagas';
import { watchCriteriaManagement } from '../../creativeScoring/redux/sagas/criteriaManagement.sagas';

/**
 *
 */
export default function* root() {
  yield all([
    spawn(watchUploadQueue),
    spawn(watchAssetsPage),
    spawn(watchOutputsPage),
    spawn(watchActionsForDataDog),
    spawn(watchComplianceContentAudit),
    spawn(watchComplianceUploads),
    spawn(watchComplianceShared),
    spawn(watchIntegrations),
    spawn(watchMobileFitnessScore),
    spawn(watchActivities),
    spawn(watchAdAccountSharing),
    spawn(watchProject),
    spawn(watchComplianceBatches),
    spawn(watchComplianceCriteriaManagement),
    spawn(watchIndividualAsset),
    spawn(watchPartners),
    spawn(watchProjectCreate),
    spawn(watchOutputsV2),
    spawn(ciRoot),
    spawn(watchCountries),
    spawn(watchPayment),
    spawn(watchDAMSingleAsset),
    spawn(watchCreativeScoring),
    spawn(watchFiltersV2),
    spawn(watchScoreOverride),
    spawn(watchRollUpReport),
    spawn(watchRollupReportsManagement),
    spawn(watchOrganization),
    spawn(watchConvertedMedia),
    spawn(watchAdAccounts),
    spawn(watchPeople),
    spawn(watchCriteriaManagement),
    spawn(watchNormsConfiguration),
  ]);
}
