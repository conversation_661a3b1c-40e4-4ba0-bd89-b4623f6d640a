import { select, call, put } from 'redux-saga/effects';
import placementReportSlice from '../../slices/creativeAnalytics/placementReport.slice';
import { buildCompareReportFilterObject } from '../../../featureServices/CICompareReports/buildCompareReportFilterObject';
import { getPlatformAnalyticsWithoutInstantiatingAsync } from '../../../creativeAnalytics/services/PlatformAnalytics/index';
import {
  getPlacementProducer,
  getFilterV2PlacementProducer,
} from '../../../creativeAnalytics/producers/opProducer';
import { userRequestReport } from '../../../featureServices/CICompareReports/recordUserCompareReportActions';
import { ciStabilityErrorHandler } from '../../../utils/vmErrorLog';
import { CREATIVE_INTELLIGENCE } from '../../../constants';
import analyticsConfigurationSlice from '../../slices/creativeAnalytics/analyticsConfiguration.slice';
import {
  getAnalyticsConfiguration,
  getSelectedOpCompareOption,
} from '../../selectors/creativeAnalytics/analyticsConfiguration.selectors';
import { updatePayloadFiltersV2BeforeRequest } from '../../../creativeAnalytics/components/AnalyticsFilters/utils/formatters';
const { PLACEMENT_REPORT } =
  CREATIVE_INTELLIGENCE.CREATIVE_INTELLIGENCE_REPORTS;

/**
 * @param AnalyticsClass
 */
export function getPlatformAnalyticsClass(AnalyticsClass) {
  return new AnalyticsClass();
}

/**
 *
 */
export function* requestPlacementReport({
  currentSelectedAccount,
  platformAnalytics,
  analyticsConfiguration,
  selectedOpCompareOption,
  statSettings,
  insightKeyDataConfig,
  payload,
}) {
  const { selectedKpi, filters } = analyticsConfiguration;
  const {
    availableCampaignFilters,
    selectedCampaignFilters,
    selectedObjectiveFilters,
    selectedPlacementFilters,
    isShowAppAdsActive,
  } = filters;
  const analyticsFiltersWithStatSettings = {
    ...analyticsConfiguration,
    ...filters,
    statSettings,
  };

  let filter = yield call(
    buildCompareReportFilterObject,
    currentSelectedAccount,
    platformAnalytics,
    selectedObjectiveFilters,
    selectedPlacementFilters,
    availableCampaignFilters,
    selectedCampaignFilters,
    isShowAppAdsActive,
    null,
    {},
  );

  if (insightKeyDataConfig) {
    filter = { ...filter, ...insightKeyDataConfig };
  }

  updatePayloadFiltersV2BeforeRequest(
    filter,
    analyticsFiltersWithStatSettings,
    payload?.advancedFilters,
  );

  let placementProducer;
  if (payload) {
    // request from filter v2
    placementProducer = yield call(
      getFilterV2PlacementProducer,
      payload.adAccounts,
      (loadingPercentage) =>
        placementReportSlice.actions.setLoadingPercentage({
          loadingPercentage,
        }),
      payload.channel,
    );
  } else {
    placementProducer = yield call(
      getPlacementProducer,
      currentSelectedAccount,
      (loadingPercentage) =>
        placementReportSlice.actions.setLoadingPercentage({
          loadingPercentage,
        }),
    );
  }

  const rumData = {
    platform: payload?.channel || currentSelectedAccount.platform,
    account: currentSelectedAccount,
    selectedKpi: payload?.selectedKpi ? payload?.selectedKpi : selectedKpi,
    ...analyticsFiltersWithStatSettings,
  };

  yield call(userRequestReport, PLACEMENT_REPORT.ID, rumData);

  const placementReport = yield call(
    [placementProducer, 'getReport'],
    selectedOpCompareOption,
    statSettings.average,
    filter,
    payload?.selectedKpi ? payload?.selectedKpi : selectedKpi,
    analyticsFiltersWithStatSettings,
    false,
    payload?.overriddenDates,
    payload?.organizationId,
    payload?.workspaceIds,
    payload?.currency,
  );

  return placementReport;
}

/**
 *
 */
export function* onGetNewPlacementReport(payload) {
  try {
    yield put(placementReportSlice.actions.setReportPendingSaga());
    const currentSelectedAccount = payload?.adAccounts;
    const platform = payload?.channel;
    const statSettings = payload?.statSettings;

    const analyticsConfiguration = yield select(getAnalyticsConfiguration);

    const PlatformAnalyticsClass = yield call(
      getPlatformAnalyticsWithoutInstantiatingAsync,
      platform,
    );
    const platformAnalytics = yield call(
      getPlatformAnalyticsClass,
      PlatformAnalyticsClass,
    );
    let selectedOpCompareOption = yield select(getSelectedOpCompareOption);
    const placementCompareOptions = yield call([
      platformAnalytics,
      'placementCompareOptions',
    ]);
    const optionsArray = yield call(
      [Object, 'values'],
      placementCompareOptions,
    );
    if (payload?.breakdownBy) {
      selectedOpCompareOption = optionsArray.filter(
        (option) => option.breakdownId === payload.breakdownBy,
      )[0];
    }

    yield put(
      analyticsConfigurationSlice.actions.setAvailableObjectivePlacementCompareOptions(
        { availableOpCompareOptions: optionsArray },
      ),
    );
    if (!selectedOpCompareOption) {
      selectedOpCompareOption = optionsArray[0];
    }

    const placementReport = yield call(requestPlacementReport, {
      currentSelectedAccount,
      platformAnalytics,
      analyticsConfiguration,
      selectedOpCompareOption,
      statSettings,
      payload,
    });

    if (placementReport.error && !placementReport.cause) {
      throw new Error('Placement report failed to fetch report.');
    }

    if (placementReport.done && !placementReport.cause) {
      yield put(
        placementReportSlice.actions.setReportSuccess({
          ...placementReport,
          selectedOpCompareOption,
        }),
      );
    }

    if (placementReport.cause) {
      yield put(
        placementReportSlice.actions.setReportFailed({
          error: {
            cause: placementReport.cause,
          },
        }),
      );
    }
  } catch (e) {
    yield call(
      ciStabilityErrorHandler,
      e,
      'placementReport.sagas.js',
      'onGetNewReport',
    );
    yield put(placementReportSlice.actions.setReportFailed({ error: e }));
  }
}
