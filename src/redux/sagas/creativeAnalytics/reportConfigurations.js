import creativeManagerSlice from '../../slices/creativeAnalytics/creativeManager.slice';
import objectiveReportSlice from '../../slices/creativeAnalytics/objectiveReport.slice';
import audienceReportSlice from '../../slices/creativeAnalytics/audienceReport.slice';
import placementReportSlice from '../../slices/creativeAnalytics/placementReport.slice';
import formatReportSlice from '../../slices/creativeAnalytics/formatReport.slice';
import campaignReportSlice from '../../slices/creativeAnalytics/campaignReport.slice.js';
import adTypeReportSlice from '../../slices/creativeAnalytics/adTypeReport.slice';
import mediaPostTypeSlice from '../../slices/creativeAnalytics/mediaPostType.slice';
import elementPresenceReportSlice from '../../slices/creativeAnalytics/elementPresenceReport.slice';
import vidMobPerformanceReportSlice from '../../slices/creativeAnalytics/vidMobPerformanceReport.slice';
import customCompareSlice from '../../slices/creativeAnalytics/customCompare.slice';
import creativeGroupsSlice from '../../slices/creativeAnalytics/creativeGroups.slice';
import durationReportSlice from '../../slices/creativeAnalytics/durationReport.slice';
import analyticsConfigurationSlice from '../../slices/creativeAnalytics/analyticsConfiguration.slice';
import brandReportSlice from '../../slices/creativeAnalytics/brandReport.slice';
import marketReportSlice from '../../slices/creativeAnalytics/marketReport.slice';
import { onGetAccountCreativeManagerData } from './creativeManager.sagas';
import {
  onGetNewObjectiveReport,
  requestObjectiveReport,
} from './objectiveReport.sagas';
import {
  getNewAudienceReport,
  requestAudienceReport,
} from './audienceReport.sagas';
import {
  onGetNewPlacementReport,
  requestPlacementReport,
} from './placementReport.sagas';
import {
  onGetNewFormatReport,
  requestFormatReport,
} from './formatReport.sagas';
import {
  onGetNewAdTypeReport,
  requestAdTypeReport,
} from './adTypeReport.sagas';
import {
  onGetNewMediaPostTypeReport,
  requestMediaPostTypeReport,
} from './mediaPostTypeReport.sagas';
import {
  onGetNewVidMobPerformanceReport,
  requestVidMobPerformanceReport,
} from './vidMobPerformanceReport.sagas';

import {
  onGetNewCustomCompareReport,
  requestCustomCompareReport,
} from './customCompare.sagas';
import {
  onGetNewCampaignReport,
  requestCampaignReport,
} from './campaignReport.sagas';
import { onGetNewBrandReport, requestBrandReport } from './brandReport.sagas';
import {
  onGetNewMarketReport,
  requestMarketReport,
} from './marketReport.sagas';
import {
  getObjectiveReport,
  getPlacementReport,
  getFormatReport,
  getCampaignReport,
  getBrandReport,
  getMarketReport,
  getAdTypeReport,
  getMediaPostTypeReport,
  getVidMobPerformanceReport,
  getCustomCompareReport,
} from '../../selectors/creativeAnalytics/reports.selectors';
import { getAudienceReport } from '../../selectors/creativeAnalytics/audienceReport.selectors';
import { getElementPresenceReport } from '../../selectors/creativeAnalytics/elementPresenceReport.selectors';
import { GLOBALS } from '../../../constants';
import {
  CREATIVE_MANAGER,
  OBJECTIVE_REPORT,
  AUDIENCE_REPORT,
  PLACEMENT_REPORT,
  FORMAT_REPORT,
  AD_TYPE_REPORT,
  MEDIA_POST_TYPE_REPORT,
  ELEMENT_PRESENCE_REPORT,
  CUSTOM_COMPARE_REPORT,
  CAMPAIGN_REPORT,
  DURATION_REPORT,
  VIDMOB_PERFORMANCE_REPORT,
  DEFAULT,
} from '../../../constants/creativeAnalytics.constants';
import { REQUEST_TYPES } from '../../../constants/analytics.api.constants';
import siteMapRouteParams from '../../../routing/siteMapRouteParams';
import { PLATFORM_AMAZON_ADVERTISING } from '../../../constants/platform.constants';
import { getCurrentPlatformAccount } from '../../selectors/platformAccounts.selectors';
import store from '../../store';
import {
  onGetNewDurationReport,
  requestDurationReport,
} from './durationReport.sagas';
import { getDurationReport } from '../../selectors/creativeAnalytics/durationReport.selectors';
import {
  onLoadAdvancedElementPresenceReport,
  requestElementPresenceReportForInsight,
} from '../../../creativeAnalytics/reports/redux/elementPresenceReport/elementPresenceReport.sagas';
import {
  BRAND_REPORT,
  MARKET_REPORT,
} from '../../../creativeAnalytics/__pages/ImpactReport/ImpactReportConstants';
import { convertFiltersWithMultipleElementsToSingleElement } from '../../../creativeAnalytics/__pages/ImpactReport/ImpactReportUtils/convertFiltersWithMultipleElementsToSingleElement';
import { uniqueId } from '../../../utils/uniqueIdGenerator';
const { NOT_LOADED } = GLOBALS.REDUX_LOADING_STATUS;

const {
  objective,
  format,
  placement,
  mediaType,
  audience,
  adType,
  campaign,
  elementPresence,
  vidMobPerformance,
  duration,
  brand,
  market,
} = siteMapRouteParams.tabs.creativeIntelligenceCompare;

const handleCampaignGroupUpdate = ({
  groupId,
  groupData,
  dispatch,
  isNewColumn,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.length) {
    return;
  }

  if (isNewColumn) {
    dispatch(
      analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
        isAddNewColumnTransitioning: true,
      }),
    );
  }

  dispatch(
    campaignReportSlice.actions.setCampaignColumnPending({
      columnId: groupId,
      columnData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
  dispatch(campaignReportSlice.actions.updateColumnLoadCount());
};

const handleCampaignGroupDuplicate = ({
  groupId,
  groupData,
  dispatch,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.length) {
    return;
  }

  dispatch(
    analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
      isAddNewColumnTransitioning: true,
    }),
  );
  dispatch(
    campaignReportSlice.actions.setCampaignColumnPending({
      columnId: groupId,
      columnData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
  dispatch(campaignReportSlice.actions.updateColumnLoadCount());
};

const handleCampaignGroupRemove = (
  groupId,
  dispatch,
  updateReduxWithoutApiRequest,
) => {
  dispatch(
    campaignReportSlice.actions.removeFilter({
      columnId: groupId,
      updateReduxWithoutApiRequest,
    }),
  );
};

const handleBrandGroupUpdate = ({
  groupId,
  groupData,
  dispatch,
  isNewColumn,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.length) {
    return;
  }

  if (isNewColumn) {
    dispatch(
      analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
        isAddNewColumnTransitioning: true,
      }),
    );
  }

  dispatch(
    brandReportSlice.actions.setBrandColumnPending({
      columnId: groupId,
      columnData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
  dispatch(brandReportSlice.actions.updateColumnLoadCount());
};

const handleBrandGroupDuplicate = ({
  groupId,
  groupData,
  dispatch,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.length) {
    return;
  }

  dispatch(
    analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
      isAddNewColumnTransitioning: true,
    }),
  );
  dispatch(
    brandReportSlice.actions.setBrandColumnPending({
      columnId: groupId,
      columnData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
  dispatch(brandReportSlice.actions.updateColumnLoadCount());
};

const handleBrandGroupRemove = (
  groupId,
  dispatch,
  updateReduxWithoutApiRequest,
) => {
  dispatch(
    brandReportSlice.actions.removeFilter({
      columnId: groupId,
      updateReduxWithoutApiRequest,
    }),
  );
};

const handleMarketGroupUpdate = ({
  groupId,
  groupData,
  dispatch,
  isNewColumn,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.length) {
    return;
  }

  if (isNewColumn) {
    dispatch(
      analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
        isAddNewColumnTransitioning: true,
      }),
    );
  }

  dispatch(
    marketReportSlice.actions.setMarketColumnPending({
      columnId: groupId,
      columnData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
  dispatch(marketReportSlice.actions.updateColumnLoadCount());
};

const handleMarketGroupDuplicate = ({
  groupId,
  groupData,
  dispatch,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.length) {
    return;
  }

  dispatch(
    analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
      isAddNewColumnTransitioning: true,
    }),
  );
  dispatch(
    marketReportSlice.actions.setMarketColumnPending({
      columnId: groupId,
      columnData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
  dispatch(marketReportSlice.actions.updateColumnLoadCount());
};

const handleMarketGroupRemove = (
  groupId,
  dispatch,
  updateReduxWithoutApiRequest,
) => {
  dispatch(
    marketReportSlice.actions.removeFilter({
      columnId: groupId,
      updateReduxWithoutApiRequest,
    }),
  );
};

const handleAudienceGroupUpdate = ({
  groupId,
  groupData,
  dispatch,
  isNewColumn,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.parent) {
    dispatch(
      audienceReportSlice.actions.removeFilter({
        groupId,
        updateReduxWithoutApiRequest,
      }),
    );
    return;
  }

  if (isNewColumn) {
    dispatch(
      analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
        isAddNewColumnTransitioning: true,
      }),
    );
  }

  dispatch(
    audienceReportSlice.actions.setAudiencePending({
      filterId: groupId,
      filterData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
};

const handleAudienceGroupDuplicate = ({
  groupId,
  groupData,
  dispatch,
  updateReduxWithoutApiRequest,
}) => {
  if (!groupData?.parent) {
    dispatch(
      audienceReportSlice.actions.removeFilter({
        groupId,
        updateReduxWithoutApiRequest,
      }),
    );
    return;
  }

  dispatch(
    analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
      isAddNewColumnTransitioning: true,
    }),
  );
  dispatch(
    audienceReportSlice.actions.setAudiencePending({
      filterId: groupId,
      filterData: groupData,
      updateReduxWithoutApiRequest,
    }),
  );
};

const handleAudienceGroupRemove = (
  groupId,
  dispatch,
  updateReduxWithoutApiRequest,
) => {
  dispatch(
    audienceReportSlice.actions.removeFilter({
      filterId: groupId,
      updateReduxWithoutApiRequest,
    }),
  );
};

const handleCustomGroupRemove = (groupId, dispatch) => {
  dispatch(customCompareSlice.actions.deleteCreativeGroup({ groupId }));
  dispatch(creativeGroupsSlice.actions.removeCompareGroupById({ groupId }));
};

const handleElementPresenceGroupUpdate = ({
  groupId,
  groupData,
  dispatch,
  isNewColumn,
  updateReduxWithoutApiRequest,
}) => {
  if (isNewColumn) {
    dispatch(
      analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
        isAddNewColumnTransitioning: true,
      }),
    );
  }

  const {
    columnName,
    platformMediaIds,
    removedPlatformMediaIds,
    filters,
    operator,
  } = groupData;

  dispatch(
    elementPresenceReportSlice.actions.setColumnFilterAdv({
      columnId: groupId,
      columnName,
      platformMediaIds,
      removedPlatformMediaIds,
      filters: convertFiltersWithMultipleElementsToSingleElement(filters),
      operator,
      updateReduxWithoutApiRequest,
    }),
  );

  dispatch(elementPresenceReportSlice.actions.setColumnFilterAdvSelections());
};

const handleElementPresenceGroupEdit = ({
  group,
  dispatch,
  analyticsFilters,
}) => {
  dispatch(
    elementPresenceReportSlice.actions.openElementPresenceModalForEdit({
      columnId: group.group.id,
      analyticsFilters,
    }),
  );
};

const handleElementPresenceGroupDuplicate = ({
  groupId,
  dispatch,
  analyticsFilters,
}) => {
  dispatch(
    elementPresenceReportSlice.actions.duplicateExistingColumn({
      columnIdToDuplicate: groupId,
      analyticsFilters,
    }),
  );
  dispatch(
    analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
      isAddNewColumnTransitioning: true,
    }),
  );
};

const handleElementPresenceGroupRemove = (groupId, dispatch) => {
  dispatch(
    elementPresenceReportSlice.actions.deleteColumn({ columnId: groupId }),
  );
};

const handleDurationRemove = (groupId, dispatch) => {
  dispatch(durationReportSlice.actions.deleteColumn({ columnId: groupId }));
};

const handleDurationDuplicate = ({ groupId, dispatch, analyticsFilters }) => {
  dispatch(
    durationReportSlice.actions.duplicateExistingColumn({
      columnIdToDuplicate: groupId,
      analyticsFilters,
    }),
  );
  dispatch(
    analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
      isAddNewColumnTransitioning: true,
    }),
  );
};

const handleDurationGroupUpdate = ({
  groupId,
  groupData,
  dispatch,
  isNewColumn,
  updateReduxWithoutApiRequest,
}) => {
  if ([undefined, null].includes(groupData?.min)) {
    return;
  }

  if (isNewColumn) {
    dispatch(
      analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
        isAddNewColumnTransitioning: true,
      }),
    );
  }

  dispatch(
    durationReportSlice.actions.setTempUpdatedColumnValue({
      ...groupData,
      id: groupId || uniqueId(),
      updateReduxWithoutApiRequest,
    }),
  );
};

const defaultReportSelector = () => ({
  status: NOT_LOADED,
  rowItems: [],
  columnItems: [],
  hasData: false,
});

export const reportConfigurations = {
  [DEFAULT]: {
    reportSelector: defaultReportSelector,
  },
  [CREATIVE_MANAGER]: {
    load: onGetAccountCreativeManagerData,
    reset: creativeManagerSlice.actions.reset,
    requestType: REQUEST_TYPES.creative,
    reportSelector: defaultReportSelector,
    disableAutomaticLoad: true,
  },
  [CUSTOM_COMPARE_REPORT]: {
    load: onGetNewCustomCompareReport,
    reset: customCompareSlice.actions.reset,
    handleGroupRemove: handleCustomGroupRemove,
    isFilterReport: true,
    setPending: customCompareSlice.actions.setReportStatusPending,
    requestType: REQUEST_TYPES.creativeGrouping,
    reportSelector: getCustomCompareReport,
    disableAutomaticLoad: true,
  },
  [OBJECTIVE_REPORT]: {
    load: onGetNewObjectiveReport,
    reset: objectiveReportSlice.actions.reset,
    setPending: objectiveReportSlice.actions.setReportPending,
    isFilterReport: false,
    requestType: REQUEST_TYPES.objective,
    reportSelector: getObjectiveReport,
    disableAutomaticLoad: true,
  },
  [AUDIENCE_REPORT]: {
    load: getNewAudienceReport,
    reset: audienceReportSlice.actions.reset,
    handleGroupUpdate: handleAudienceGroupUpdate,
    handleGroupDuplicate: handleAudienceGroupDuplicate,
    handleGroupRemove: handleAudienceGroupRemove,
    setPending: audienceReportSlice.actions.setReportStatusPending,
    isFilterReport: true,
    requestType: REQUEST_TYPES.audience,
    reportSelector: getAudienceReport,
    disableAutomaticLoad: true,
  },
  [PLACEMENT_REPORT]: {
    load: onGetNewPlacementReport,
    reset: placementReportSlice.actions.reset,
    setPending: placementReportSlice.actions.setReportPendingSaga,
    isFilterReport: false,
    requestType: REQUEST_TYPES.placement,
    reportSelector: getPlacementReport,
    disableAutomaticLoad: true,
  },
  [FORMAT_REPORT]: {
    load: onGetNewFormatReport,
    reset: formatReportSlice.actions.reset,
    setPending: formatReportSlice.actions.setReportPendingSaga,
    requestType: REQUEST_TYPES.format,
    isFilterReport: false,
    reportSelector: getFormatReport,
    disableAutomaticLoad: true,
  },
  [CAMPAIGN_REPORT]: {
    load: onGetNewCampaignReport,
    reset: campaignReportSlice.actions.reset,
    handleGroupUpdate: handleCampaignGroupUpdate,
    handleGroupDuplicate: handleCampaignGroupDuplicate,
    handleGroupRemove: handleCampaignGroupRemove,
    isFilterReport: true,
    setPending: campaignReportSlice.actions.setReportStatusPending,
    requestType: REQUEST_TYPES.campaign,
    reportSelector: getCampaignReport,
    disableAutomaticLoad: true,
  },
  [BRAND_REPORT]: {
    load: onGetNewBrandReport,
    reset: brandReportSlice.actions.reset,
    handleGroupUpdate: handleBrandGroupUpdate,
    handleGroupDuplicate: handleBrandGroupDuplicate,
    handleGroupRemove: handleBrandGroupRemove,
    isFilterReport: true,
    setPending: brandReportSlice.actions.setReportStatusPending,
    requestType: REQUEST_TYPES.brand,
    reportSelector: getBrandReport,
    disableAutomaticLoad: true,
  },
  [MARKET_REPORT]: {
    load: onGetNewMarketReport,
    reset: marketReportSlice.actions.reset,
    handleGroupUpdate: handleMarketGroupUpdate,
    handleGroupDuplicate: handleMarketGroupDuplicate,
    handleGroupRemove: handleMarketGroupRemove,
    isFilterReport: true,
    setPending: marketReportSlice.actions.setReportStatusPending,
    requestType: REQUEST_TYPES.market,
    reportSelector: getMarketReport,
    disableAutomaticLoad: true,
  },
  [AD_TYPE_REPORT]: {
    load: onGetNewAdTypeReport,
    reset: adTypeReportSlice.actions.reset,
    setPending: adTypeReportSlice.actions.setReportPending,
    requestType: REQUEST_TYPES.placement,
    isFilterReport: false,
    reportSelector: getAdTypeReport,
    disableAutomaticLoad: true,
  },
  [MEDIA_POST_TYPE_REPORT]: {
    load: onGetNewMediaPostTypeReport,
    reset: mediaPostTypeSlice.actions.reset,
    setPending: mediaPostTypeSlice.actions.setReportPendingSaga,
    isFilterReport: false,
    requestType: REQUEST_TYPES.placement,
    reportSelector: getMediaPostTypeReport,
    disableAutomaticLoad: true,
  },
  [ELEMENT_PRESENCE_REPORT]: {
    load: onLoadAdvancedElementPresenceReport,
    reset: elementPresenceReportSlice.actions.reset,
    handleGroupEdit: handleElementPresenceGroupEdit,
    handleGroupUpdate: handleElementPresenceGroupUpdate,
    handleGroupDuplicate: handleElementPresenceGroupDuplicate,
    handleGroupRemove: handleElementPresenceGroupRemove,
    isFilterReport: true,
    setPending: elementPresenceReportSlice.actions.setReportStatusPending,
    requestType: REQUEST_TYPES.elementPresence,
    reportSelector: getElementPresenceReport,
    disableAutomaticLoad: true,
  },
  [VIDMOB_PERFORMANCE_REPORT]: {
    load: onGetNewVidMobPerformanceReport,
    reset: vidMobPerformanceReportSlice.actions.reset,
    isFilterReport: true,
    setPending: vidMobPerformanceReportSlice.actions.setReportStatusPending,
    requestType: REQUEST_TYPES.vidMobPerformance,
    reportSelector: getVidMobPerformanceReport,
    disableAutomaticLoad: true,
  },
  [DURATION_REPORT]: {
    load: onGetNewDurationReport,
    reset: durationReportSlice.actions.reset,
    isFilterReport: true,
    setPending: durationReportSlice.actions.setReportStatusPending,
    requestType: REQUEST_TYPES.duration,
    reportSelector: getDurationReport,
    disableAutomaticLoad: true,
    handleGroupUpdate: handleDurationGroupUpdate,
    handleGroupDuplicate: handleDurationDuplicate,
    handleGroupRemove: handleDurationRemove,
  },
};
