/* eslint-disable no-console */
import moment from 'moment';
import { select, put, call, all, takeLatest } from 'redux-saga/effects';
import creativeManagerSlice from '../../slices/creativeAnalytics/creativeManager.slice';
import analyticsConfigurationSlice from '../../slices/creativeAnalytics/analyticsConfiguration.slice';
import getPlatformAnalytics from '../../../creativeAnalytics/services/PlatformAnalytics';
import { buildCompareReportFilterObject } from '../../../featureServices/CICompareReports/buildCompareReportFilterObject';
import {
  getAccountSnapshotProducer,
  getFilterV2AccountSnapshotProducer,
} from '../../../creativeAnalytics/producers/accountSnapshotProducer';
import { ciStabilityErrorHandler } from '../../../utils/vmErrorLog';
import {
  getAnalyticsConfiguration,
  getAnalyticsFiltersSelectedCampaigns,
  getAnalyticsFiltersAvailableCampaigns,
} from '../../selectors/creativeAnalytics/analyticsConfiguration.selectors';
import platformAccountsSlice from '../../slices/creativeAnalytics/platformAccounts.slice';
import { REQUEST_TYPES } from '../../../constants/analytics.api.constants';
import creativeGroupsSlice from '../../slices/creativeAnalytics/creativeGroups.slice';
import { PLATFORMS_SUPPORT_MULTI_CREATIVE_ADS } from '../../../constants/ci.constants';
import { CREATIVE_MANAGER_FETCH_DATA_ACTION_TYPE } from '../../../creativeAnalytics/__pages/CreativeManager/helpers/CreativeManagerConstants';
import { updatePayloadFiltersV2BeforeRequest } from '../../../creativeAnalytics/components/AnalyticsFilters/utils/formatters';

export function* watchCreativeManagerData() {
  yield takeLatest(
    CREATIVE_MANAGER_FETCH_DATA_ACTION_TYPE,
    onGetAccountCreativeManagerData,
  );
}

export function* onGetAccountCreativeManagerData(action) {
  if (action) {
    const platform = action?.payload?.channel;
    const doesPlatformSupportAdView =
      PLATFORMS_SUPPORT_MULTI_CREATIVE_ADS.includes(platform?.toUpperCase());

    if (doesPlatformSupportAdView) {
      yield call(onGetAccountAdsAndCreatives, action?.payload);
      return;
    }

    yield call(onGetAccountCreatives, action?.payload);
  }
}

export function* onGetAccountCreatives(payload) {
  try {
    const analyticsConfiguration = yield select(getAnalyticsConfiguration);
    const currentSelectedAccount = payload?.adAccounts;
    const platform = payload?.channel;
    const statSettings = payload?.statSettings;

    const selectedCampaigns = yield select(
      getAnalyticsFiltersSelectedCampaigns,
    );
    const availableCampaigns = yield select(
      getAnalyticsFiltersAvailableCampaigns,
    );

    yield put(creativeManagerSlice.actions.setAccountCreativesStatusPending());
    const platformAnalytics = yield call(getPlatformAnalytics, platform);
    const {
      selectedObjectiveFilters,
      selectedPlacementFilters,
      isShowAppAdsActive,
    } = analyticsConfiguration.filters;
    const filter = yield call(
      buildCompareReportFilterObject,
      currentSelectedAccount,
      platformAnalytics,
      selectedObjectiveFilters,
      selectedPlacementFilters,
      availableCampaigns,
      selectedCampaigns,
      isShowAppAdsActive,
      null,
      {},
    );

    const analyticsFilterWithStatSettings = {
      ...analyticsConfiguration,
      ...analyticsConfiguration.filters,
      statSettings,
    };
    updatePayloadFiltersV2BeforeRequest(
      filter,
      analyticsFilterWithStatSettings,
      payload?.advancedFilters,
    );

    let accountSnapshotProducer;
    if (payload) {
      // request from filter v2
      accountSnapshotProducer = yield call(
        getFilterV2AccountSnapshotProducer,
        payload.adAccounts,
        (percentage) => console.log(`loading ${percentage}%`),
        null,
        null,
        payload.channel,
      );
    } else {
      accountSnapshotProducer = yield call(
        getAccountSnapshotProducer,
        currentSelectedAccount,
        (loadingPercentage) => console.log(`loading ${loadingPercentage}%`),
        null,
        null,
        platform,
      );
    }

    const accountAssetListData = yield call(
      [accountSnapshotProducer, 'getReportForCreatives'],
      filter,
      payload?.selectedKpi,
      false,
      analyticsFilterWithStatSettings,
      true,
      payload?.organizationId,
      payload?.workspaceIds,
      payload?.adAccounts?.map((adAccount) => adAccount.id),
      payload?.overriddenDates,
      payload?.currency,
    );

    if (accountAssetListData.error) {
      throw new Error('Creative manager failed to fetch assets.');
    }

    const { creatives, hasDataForFilters } = accountAssetListData;

    const mediaKeyedByPlatformMediaId = creatives?.reduce((acc, asset) => {
      acc[asset.platformMediaId] = asset;
      return acc;
    }, {});

    const mediaDates = {
      analyticsStartDate: moment(
        payload?.overriddenDates?.startDate ||
          analyticsConfiguration.filters.analyticsStartDate,
      ).format('YYYY-MM-DD'),
      analyticsEndDate: moment(
        payload?.overriddenDates?.endDate ||
          analyticsConfiguration.filters.analyticsEndDate,
      ).format('YYYY-MM-DD'),
      expirationDate: moment().add(6, 'hours'),
    };

    yield all([
      put(
        creativeManagerSlice.actions.updateAccountAdsOnRequestSuccess({
          ads: [],
          hasDataForFilters: false,
        }),
      ),
      put(
        creativeGroupsSlice.actions.setCreativeAndAdsRelationshipData({
          creativesKeyedByAdId: {},
        }),
      ),
      put(
        creativeManagerSlice.actions.updateAccountCreativesOnRequestSuccess({
          creatives,
          hasDataForFilters,
        }),
      ),
      put(
        creativeGroupsSlice.actions.setMediaKeyedByPlatformMediaId({
          mediaKeyedByPlatformMediaId,
        }),
      ),
      put(
        analyticsConfigurationSlice.actions.saveMediaStats({
          account: currentSelectedAccount,
          platform,
          mediaDates,
          stats: accountAssetListData.creatives,
        }),
      ),
      put(
        platformAccountsSlice.actions.setIsShowingReportLoadingStateOnAccountSwitch(
          { isShowingReportLoadingStateOnAccountSwitch: false },
        ),
      ),
    ]);
  } catch (e) {
    yield put(
      creativeManagerSlice.actions.updateAccountCreativesStatusFailed(),
    );
    yield call(
      ciStabilityErrorHandler,
      e,
      'creativeManager.sagas.js',
      'onGetAccountCreatives',
    );
  }
}

/**
 *
 */
export function* onGetAccountAdsAndCreatives(payload) {
  const analyticsConfiguration = yield select(getAnalyticsConfiguration);
  const currentSelectedAccount = payload?.adAccounts;
  const platform = payload?.channel;
  const statSettings = payload?.statSettings;
  const selectedCampaigns = yield select(getAnalyticsFiltersSelectedCampaigns);
  const availableCampaigns = yield select(
    getAnalyticsFiltersAvailableCampaigns,
  );
  yield put(
    creativeManagerSlice.actions.setAccountCreativesAndAdsStatusPending(),
  );
  const platformAnalytics = yield call(getPlatformAnalytics, platform);

  const {
    selectedObjectiveFilters,
    selectedPlacementFilters,
    isShowAppAdsActive,
  } = analyticsConfiguration.filters;
  const filter = yield call(
    buildCompareReportFilterObject,
    currentSelectedAccount,
    platformAnalytics,
    selectedObjectiveFilters,
    selectedPlacementFilters,
    availableCampaigns,
    selectedCampaigns,
    isShowAppAdsActive,
    null,
    {},
  );

  const analyticsFilterWithStatSettings = {
    ...analyticsConfiguration,
    ...analyticsConfiguration.filters,
    statSettings,
  };
  updatePayloadFiltersV2BeforeRequest(
    filter,
    analyticsFilterWithStatSettings,
    payload?.advancedFilters,
  );

  const requestConfig = {
    currentSelectedAccount,
    selectedKpi: payload?.selectedKpi,
    selectedCampaigns,
    availableCampaigns,
    filter,
    platform,
    analyticsFilter: analyticsFilterWithStatSettings,
  };

  if (payload?.overriddenDates) {
    requestConfig.overriddenDates = payload?.overriddenDates;
  }

  if (payload?.organizationId) {
    requestConfig.organizationId = payload?.organizationId;
  }

  if (payload?.workspaceIds) {
    requestConfig.workspaceIds = payload?.workspaceIds;
  }

  yield all([
    call(requestAccountCreativesData, requestConfig, payload),
    call(requestAccountAdsData, requestConfig, payload),
  ]);
  yield put(
    platformAccountsSlice.actions.setIsShowingReportLoadingStateOnAccountSwitch(
      { isShowingReportLoadingStateOnAccountSwitch: false },
    ),
  );
}

function* requestAccountAdsData(requestConfig, payload) {
  try {
    const {
      currentSelectedAccount,
      selectedKpi,
      filter,
      analyticsFilter,
      organizationId,
      workspaceIds,
      overriddenDates,
      platform,
    } = requestConfig;
    const currency = payload?.currency;

    let accountSnapshotProducer;
    if (payload) {
      // request from filter v2
      accountSnapshotProducer = yield call(
        getFilterV2AccountSnapshotProducer,
        payload.adAccounts,
        (loadingPercentage) => console.log(`loading ads ${loadingPercentage}%`),
        null,
        REQUEST_TYPES.ad,
        payload.channel,
      );
    } else {
      accountSnapshotProducer = yield call(
        getAccountSnapshotProducer,
        currentSelectedAccount,
        (loadingPercentage) => console.log(`loading ads ${loadingPercentage}%`),
        null,
        REQUEST_TYPES.ad,
        platform,
      );
    }

    const accountAdsData = yield call([accountSnapshotProducer, 'getReport'], {
      filter,
      kpi: selectedKpi,
      isLifetimeReport: false,
      analyticsFilter,
      requestType: REQUEST_TYPES.ad,
      disablePagination: true,
      organizationId,
      workspaceIds,
      overriddenDates,
      currency,
    });

    if (accountAdsData.error) {
      throw new Error(accountAdsData.error);
    }

    const ads = accountAdsData.result;
    const { hasDataForFilters } = accountAdsData;

    const creativesKeyedByAdId = ads.reduce((acc, ad) => {
      acc[ad.title] = acc[ad.title]
        ? [...acc[ad.title], ...ad.metadata.advideo]
        : ad.metadata.advideo;
      return acc;
    }, {});

    yield all([
      put(
        creativeManagerSlice.actions.updateAccountAdsOnRequestSuccess({
          ads,
          hasDataForFilters,
        }),
      ),
      put(
        creativeGroupsSlice.actions.setCreativeAndAdsRelationshipData({
          creativesKeyedByAdId,
        }),
      ),
    ]);
  } catch (err) {
    yield put(creativeManagerSlice.actions.updateAccountAdsOnRequestFailed());
    yield call(
      ciStabilityErrorHandler,
      err,
      'creativeManager.sagas.js',
      'requestAccountAdsData',
    );
  }
}

function* requestAccountCreativesData(requestConfig, payload) {
  try {
    const {
      currentSelectedAccount,
      selectedKpi,
      filter,
      analyticsFilter,
      organizationId,
      workspaceIds,
      overriddenDates,
      platform,
    } = requestConfig;
    let accountSnapshotProducer;
    if (payload) {
      // request from filter v2
      accountSnapshotProducer = yield call(
        getFilterV2AccountSnapshotProducer,
        payload.adAccounts,
        (loadingPercentage) =>
          console.log(`loading creatives ${loadingPercentage}%`),
        null,
        REQUEST_TYPES.creative,
        payload.channel,
      );
    } else {
      accountSnapshotProducer = yield call(
        getAccountSnapshotProducer,
        currentSelectedAccount,
        (loadingPercentage) =>
          console.log(`loading creatives ${loadingPercentage}%`),
        null,
        REQUEST_TYPES.creative,
        platform,
      );
    }

    const accountCreativesData = yield call(
      [accountSnapshotProducer, 'getReportForCreatives'],
      filter,
      selectedKpi,
      false,
      analyticsFilter,
      true,
      organizationId,
      workspaceIds,
      payload?.adAccounts?.map((adAccount) => adAccount.id),
      overriddenDates,
      payload?.currency,
    );

    if (accountCreativesData.error) {
      throw new Error('Creative manager failed to fetch creatives.');
    }

    const { creatives, hasDataForFilters } = accountCreativesData;

    const mediaKeyedByPlatformMediaId = creatives?.reduce((acc, creative) => {
      acc[creative.platformMediaId] = creative;
      return acc;
    }, {});
    const mediaDates = {
      analyticsStartDate: moment(analyticsFilter.analyticsStartDate).format(
        'YYYY-MM-DD',
      ),
      analyticsEndDate: moment(analyticsFilter.analyticsEndDate).format(
        'YYYY-MM-DD',
      ),
      expirationDate: moment().add(6, 'hours'),
    };

    yield all([
      put(
        creativeManagerSlice.actions.updateAccountCreativesOnRequestSuccess({
          creatives,
          hasDataForFilters,
        }),
      ),
      put(
        creativeGroupsSlice.actions.setMediaKeyedByPlatformMediaId({
          mediaKeyedByPlatformMediaId,
        }),
      ),
      put(
        analyticsConfigurationSlice.actions.saveMediaStats({
          account: currentSelectedAccount,
          platform: currentSelectedAccount.platform,
          mediaDates,
          stats: accountCreativesData.creatives,
        }),
      ),
    ]);
  } catch (err) {
    yield put(
      creativeManagerSlice.actions.updateAccountCreativesStatusFailed(),
    );
    yield call(
      ciStabilityErrorHandler,
      err,
      'creativeManager.sagas.js',
      'requestAccountCreativesData',
    );
  }
}
