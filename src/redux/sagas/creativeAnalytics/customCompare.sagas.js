/* eslint-disable no-console */
import { select, put, call, all, fork, takeLatest } from 'redux-saga/effects';
import {
  getAccountMediaAsObject,
  getCompareGroupsFormattedForLocalStorageOrReport,
} from '../../selectors/creativeAnalytics/creativeGroups.selectors';
import customCompareSlice from '../../slices/creativeAnalytics/customCompare.slice';
import { saveToLocalStorage } from './creativeGroups.sagas';
import { ciStabilityErrorHandler } from '../../../utils/vmErrorLog';
import { userRequestReport } from '../../../featureServices/CICompareReports/recordUserCompareReportActions';
import { buildCompareReportFilterObject } from '../../../featureServices/CICompareReports/buildCompareReportFilterObject';
import getPlatformAnalytics from '../../../creativeAnalytics/services/PlatformAnalytics/index';
import {
  getCreativeCompareProducer,
  getFilterV2CreativeCompareProducer,
} from '../../../creativeAnalytics/producers/creativeCompareProducer';
import { CREATIVE_INTELLIGENCE } from '../../../constants';
import {
  ORGANIC_PLATFORMS,
  CURRENT_COMPARE_GROUPS_STORAGE_KEY,
  KPI_REPORT_TABLE_VIEW_OPTION,
  ELEMENT_REPORT_TABLE_VIEW_OPTION,
} from '../../../constants/ci.constants';
import analyticsConfigurationSlice from '../../slices/creativeAnalytics/analyticsConfiguration.slice';
import {
  getAnalyticsConfiguration,
  getCurrentCreativeIntelligenceView,
} from '../../selectors/creativeAnalytics/analyticsConfiguration.selectors';
import platformAccountsSlice from '../../slices/creativeAnalytics/platformAccounts.slice';
import {
  CUSTOM_COMPARE_REPORT,
  COMPARE_REPORT_AVERAGE_KEY,
} from '../../../constants/creativeAnalytics.constants';
import AnalyticsSavedReportsService from '../../../creativeAnalytics/reports/services/AnalyticsSavedReportsService';
import { getSavedReport } from '../../selectors/creativeAnalytics/customCompareReport.selectors';
import { getOrganizationId } from '../../selectors/partner.selectors';
import {
  CUSTOM_COMPARE_REPORT_FETCH_DATA_ACTION_TYPE,
  MEDIA_IMPACT_REPORT_TYPE,
} from '../../../creativeAnalytics/__pages/ImpactReport/ImpactReportConstants';
import { updatePayloadFiltersV2BeforeRequest } from '../../../creativeAnalytics/components/AnalyticsFilters/utils/formatters';

const { OBJECTIVE_REPORT } =
  CREATIVE_INTELLIGENCE.CREATIVE_INTELLIGENCE_REPORTS;
const { createSavedReport, updateSavedReport, getSavedReportById } =
  AnalyticsSavedReportsService;

export function* watchCustomCompare() {
  yield all([
    fork(watchCustomCompareReportData),
    fork(watchOnSaveCustomCompareReport),
    fork(watchLoadSavedReportFromUrl),
  ]);
}

export function* watchCustomCompareReportData() {
  yield takeLatest(
    CUSTOM_COMPARE_REPORT_FETCH_DATA_ACTION_TYPE,
    customCompareReportSaga,
  );
}

export function* watchOnSaveCustomCompareReport() {
  yield takeLatest(
    customCompareSlice.actions.onSaveReport,
    createOrUpdateSavedReport,
  );
}

export function* watchLoadSavedReportFromUrl() {
  yield takeLatest(
    customCompareSlice.actions.loadSavedReportFromUrl,
    onLoadSavedReportFromUrl,
  );
}

/**
 *
 */
export function* onGetNewCustomCompareReport() {
  yield put(customCompareSlice.actions.setReportStatusPending());
}

export function* requestCustomCompareReport({
  currentSelectedAccount,
  platformAnalytics,
  analyticsConfiguration,
  statSettings,
  currentCompareGroups,
  insightKeyDataConfig,
  isGenericApiErrorBarEnabled = true,
  payload,
}) {
  let creativeCompareProducer;
  if (payload) {
    // request from filter v2
    yield put(customCompareSlice.actions.setReportStatusPending());
    creativeCompareProducer = yield call(
      getFilterV2CreativeCompareProducer,
      payload.adAccounts,
      payload.channel,
      (percentage) => console.log(`loading ${percentage}`),
    );
  } else {
    creativeCompareProducer = yield call(
      getCreativeCompareProducer,
      currentSelectedAccount,
      (percentage) => console.log(`loading ${percentage}%`),
    );
  }

  const assetList = yield select(getAccountMediaAsObject);
  yield call(
    [creativeCompareProducer, 'setGroups'],
    currentCompareGroups,
    payload.workspaceIds,
    payload.adAccounts.map((account) => account.id),
  );
  yield call(
    [creativeCompareProducer, 'setAccountAssetList'],
    Object.values(assetList),
  );

  const isLifetime = ORGANIC_PLATFORMS.includes(
    currentSelectedAccount.platform,
  );
  const selectedKpi = payload?.selectedKpi
    ? payload?.selectedKpi
    : analyticsConfiguration.selectedKpi;
  const { filters } = analyticsConfiguration;

  const {
    selectedObjectiveFilters,
    selectedPlacementFilters,
    isShowAppAdsActive,
    availableCampaignFilters,
    selectedCampaignFilters,
  } = filters;
  const analyticsFiltersWithStatSettings = {
    ...analyticsConfiguration,
    ...filters,
    statSettings,
  };
  const audienceFilter = {};

  let filter = yield call(
    buildCompareReportFilterObject,
    currentSelectedAccount,
    platformAnalytics,
    selectedObjectiveFilters,
    selectedPlacementFilters,
    availableCampaignFilters,
    selectedCampaignFilters,
    isShowAppAdsActive,
    null,
    audienceFilter,
  );

  if (insightKeyDataConfig) {
    filter = { ...filter, ...insightKeyDataConfig };
  }

  const rumData = {
    platform: platformAnalytics,
    account: currentSelectedAccount,
    selectedKpi,
    ...analyticsFiltersWithStatSettings,
  };

  yield call(userRequestReport, OBJECTIVE_REPORT.ID, rumData);

  updatePayloadFiltersV2BeforeRequest(
    filter,
    analyticsFiltersWithStatSettings,
    payload?.advancedFilters,
  );

  const compareReport = yield call([creativeCompareProducer, 'getReport'], {
    filter,
    kpi: selectedKpi,
    averageType: statSettings.average,
    analyticsFilter: analyticsFiltersWithStatSettings,
    isLifetimeReport: isLifetime,
    shouldIncludeMissingGroups: true,
    isGenericApiErrorBarEnabled,
    overriddenDates: payload?.overriddenDates,
    organizationId: payload?.organizationId,
    workspaceIds: payload?.workspaceIds,
    currency: payload?.currency,
  });

  if (compareReport.errorMessage) {
    return {
      ...compareReport,
      error: {
        cause: compareReport.cause,
      },
    };
  }

  if (compareReport.done) {
    compareReport.hasDataForKpi =
      selectedKpi && selectedKpi.id && compareReport.availableKpiIds
        ? compareReport.availableKpiIds.includes(selectedKpi.id)
        : false;

    return compareReport;
  }
}

/**
 *
 */
export function* customCompareReportSaga(action) {
  const currentCreativeAnalyticsView = yield select(
    getCurrentCreativeIntelligenceView,
  );
  if (currentCreativeAnalyticsView !== CUSTOM_COMPARE_REPORT) {
    return;
  }

  try {
    const analyticsConfiguration = yield select(getAnalyticsConfiguration);
    const currentSelectedAccount = action?.payload?.adAccounts;
    const platform = action?.payload?.channel;
    const statSettings = action?.payload?.statSettings;

    let currentCompareGroups = yield select(
      getCompareGroupsFormattedForLocalStorageOrReport,
    );

    if (currentCompareGroups?.length > 0) {
      yield call(saveToLocalStorage);
    } else {
      const currentCompareGroupsJson = yield call(
        { context: localStorage, fn: localStorage.getItem },
        CURRENT_COMPARE_GROUPS_STORAGE_KEY,
      );
      currentCompareGroups = yield call(
        { context: JSON, fn: JSON.parse },
        currentCompareGroupsJson,
      );
    }

    if (!currentCompareGroups || !currentSelectedAccount?.length || !platform) {
      return;
    }

    const platformAnalytics = yield call(getPlatformAnalytics, platform);
    const compareReport = yield call(requestCustomCompareReport, {
      currentSelectedAccount,
      platformAnalytics,
      analyticsConfiguration,
      statSettings,
      currentCompareGroups,
      payload: action?.payload,
    });

    if (compareReport.done && compareReport.errorMessage) {
      yield put(
        customCompareSlice.actions.updateReportOnRequestFailure({
          error: {
            cause: compareReport.errorMessage,
          },
        }),
      );
      return;
    }

    if (compareReport.error) {
      throw new Error('Custom compare failed to fetch report.');
    }

    const selectedKpi = action?.payload?.selectedKpi
      ? action?.payload?.selectedKpi
      : analyticsConfiguration.selectedKpi;
    if (compareReport.done) {
      const orderedGroups = [];
      const averageGroup = compareReport.groups.find((group) =>
        [group.title, group?.group?.id].includes(COMPARE_REPORT_AVERAGE_KEY),
      );
      if (averageGroup) {
        orderedGroups.push(averageGroup);
      }

      currentCompareGroups.forEach((compareGroup) => {
        orderedGroups.push(
          compareReport.groups.find((reportGroup) => {
            return reportGroup.group.id === compareGroup.group.id;
          }),
        );
      });
      compareReport.hasDataForKpi =
        selectedKpi && selectedKpi.id && compareReport.availableKpiIds
          ? compareReport.availableKpiIds.includes(selectedKpi.id)
          : false;

      const reportWithOrderedGroups = {
        ...compareReport,
        groups: orderedGroups,
      };
      yield put(
        customCompareSlice.actions.updateReportOnRequestSuccess(
          reportWithOrderedGroups,
        ),
      );
      yield put(
        analyticsConfigurationSlice.actions.setIsAddNewColumnTransitioning({
          isAddNewColumnTransitioning: false,
        }),
      );
      yield put(
        platformAccountsSlice.actions.setIsShowingReportLoadingStateOnAccountSwitch(
          { isShowingReportLoadingStateOnAccountSwitch: false },
        ),
      );
    }
  } catch (error) {
    yield call(
      ciStabilityErrorHandler,
      error,
      'customCompare.sagas.js',
      'onGetNewReport',
    );
    yield put(
      customCompareSlice.actions.updateReportOnRequestFailure({ error }),
    );
  }
}

function* createOrUpdateSavedReport(action) {
  const { reportToSave } = action.payload;

  try {
    const existingSavedReport = yield select(getSavedReport);
    const organizationId = yield select(getOrganizationId);

    if (existingSavedReport?.id) {
      const savedReport = yield call(
        updateSavedReport,
        existingSavedReport.id,
        organizationId,
        reportToSave,
      );
      yield put(
        customCompareSlice.actions.onSaveReportSuccess({ savedReport }),
      );
      return;
    }

    const savedReport = yield call(
      createSavedReport,
      organizationId,
      reportToSave,
    );
    yield put(customCompareSlice.actions.onSaveReportSuccess({ savedReport }));
  } catch (error) {
    yield call(
      ciStabilityErrorHandler,
      error,
      'customCompare.sagas.js',
      'createOrUpdateSavedReport',
    );
    yield put(customCompareSlice.actions.onSaveReportFailure());
  }
}

function* onLoadSavedReportFromUrl(action) {
  const { reportIdFromUrl } = action.payload;

  try {
    const organizationId = yield select(getOrganizationId);
    const { data: savedReport } = yield call(getSavedReportById, {
      reportId: reportIdFromUrl,
      organizationId,
    });
    yield put(customCompareSlice.actions.onSaveReportSuccess({ savedReport }));

    if (savedReport.reportType === MEDIA_IMPACT_REPORT_TYPE) {
      yield put(
        analyticsConfigurationSlice.actions.setReportTableView({
          reportTableView: KPI_REPORT_TABLE_VIEW_OPTION,
        }),
      );
    } else {
      yield put(
        analyticsConfigurationSlice.actions.setReportTableView({
          reportTableView: ELEMENT_REPORT_TABLE_VIEW_OPTION,
        }),
      );
    }
  } catch (error) {
    yield call(
      ciStabilityErrorHandler,
      error,
      'customCompare.sagas.js',
      'createOrUpdateSavedReport',
    );
    yield put(customCompareSlice.actions.onSaveReportFailure());
  }
}
