import { takeLatest, select, all, fork, put, call } from 'redux-saga/effects';
import getPlatformAnalytics, {
  getPlatformAnalyticsWithoutInstantiatingAsync,
} from '../../../creativeAnalytics/services/PlatformAnalytics/index';
import analyticsConfigurationSlice from '../../slices/creativeAnalytics/analyticsConfiguration.slice';
import creativeGroupsSlice from '../../slices/creativeAnalytics/creativeGroups.slice';
import {
  getCurrentPlatformAccount,
  getAccountGroups,
  getActiveAccounts,
} from '../../selectors/platformAccounts.selectors';
import {
  getCurrentReportBreakdown,
  getCurrentCreativeIntelligenceView,
  getAnalyticsConfiguration,
  getKpiList,
} from '../../selectors/creativeAnalytics/analyticsConfiguration.selectors';
import { AccountSnapshotProducer } from '../../../creativeAnalytics/producers/accountSnapshotProducer';
import { ciStabilityErrorHandler } from '../../../utils/vmErrorLog';
import { showLocalizedErrorBar } from '../../../utils/showConfirmationBar';
import { reportConfigurations } from './reportConfigurations';
import { buildCompareReportFilterObject } from '../../../featureServices/CICompareReports/buildCompareReportFilterObject';
import StoredDataService from '../../../apiServices/StoredDataService';
import { CUSTOM_COMPARE_REPORT } from '../../../constants/creativeAnalytics.constants';
import platformAccountsSlice from '../../slices/creativeAnalytics/platformAccounts.slice';
import { REPORT_TABLE_VIEW } from '../../../constants/ci.constants';
import { PLATFORM_ADWORDS } from '../../../constants/platform.constants';
import { ADWORDS_COST_PER_1000_IMPRESSIONS_KPI_NAME } from '../../../creativeAnalytics/services/PlatformAnalytics/AdwordsAnalytics';
import { noCreativeExamples } from '../../../featureServices/CICompareReports/recordUserCompareReportActions';
import { buildAndSaveCIUrlFilters } from '../../../utils/buildAndSaveCIUrlFilters';
import {
  BREAKDOWNS_TO_CI_VIEW,
  ELEMENT,
  ELEMENT_PRESENCE_REPORT,
  IMPACT_REPORT_TYPES,
  IMPACT_REPORT_TYPES_TO_TABLE_VIEWS,
} from '../../../creativeAnalytics/__pages/ImpactReport/ImpactReportConstants';
import { updatePayloadFiltersV2BeforeRequest } from '../../../creativeAnalytics/components/AnalyticsFilters/utils/formatters';

export const getSharedFilterRequest =
  StoredDataService.getStoredData.bind(StoredDataService);

/**
 *
 */
export function* watchAnalyticsConfiguration() {
  yield all([
    fork(watchSetNewImpactReport),
    fork(watchSetExistingImpactReport),
    fork(watchResetReportTableView),
    fork(watchAddCompareGroups),
    fork(watchOpenElementMediaModal),
  ]);
}

/**
 *
 */
export function* watchSetNewImpactReport() {
  yield takeLatest(
    [analyticsConfigurationSlice.actions.setNewImpactReport],
    loadNewImpactReport,
  );
}

export function* watchSetExistingImpactReport() {
  yield takeLatest(
    [analyticsConfigurationSlice.actions.setExistingImpactReport],
    loadExistingImpactReport,
  );
}

/**
 *
 */
export function* watchResetReportTableView() {
  yield takeLatest(
    [analyticsConfigurationSlice.actions.resetReportTableView],
    onResetReportTableView,
  );
}

/**
 *
 */
export function* watchAddCompareGroups() {
  yield takeLatest(
    creativeGroupsSlice.actions.addCompareGroupsToReport,
    onAddCompareGroups,
  );
}

/**
 *
 */
export function* watchOpenElementMediaModal() {
  yield takeLatest(
    analyticsConfigurationSlice.actions.openElementMediaModal,
    onOpenElementMediaModal,
  );
}

/**
 *
 */
function* onAddCompareGroups() {
  const currentCiView = yield select(getCurrentCreativeIntelligenceView);
  if (currentCiView === CUSTOM_COMPARE_REPORT) {
    const { load } = reportConfigurations[currentCiView];
    yield call(load);
  }
}

/**
 * @param action
 */
function* loadNewImpactReport(action) {
  const reportType = action?.payload?.reportType;
  const currentPlatformAccount = yield select(getCurrentPlatformAccount);
  const currentReportBreakdown = yield select(getCurrentReportBreakdown);
  const currentPlatform = currentPlatformAccount?.platform;
  if (!reportType || !currentPlatform) {
    return;
  }

  yield put(
    analyticsConfigurationSlice.actions.setReportTableView({
      reportTableView: IMPACT_REPORT_TYPES_TO_TABLE_VIEWS[reportType],
    }),
  );
  if (reportType === IMPACT_REPORT_TYPES.ELEMENT_PRESENCE) {
    yield call(setAnalyticsSliceForElementPresenceReport);
    return;
  }

  const platformAnalytics = yield call(getPlatformAnalytics, currentPlatform);
  const availableBreakdowns = yield call([
    platformAnalytics,
    'getSupportedViewByOptions',
  ]);

  yield put(
    analyticsConfigurationSlice.actions.setAvailableReportBreakdowns({
      availableBreakdowns,
    }),
  );
  if (availableBreakdowns.includes(currentReportBreakdown)) {
    return;
  }

  const newReportBreakdown = availableBreakdowns[0];
  yield put(
    analyticsConfigurationSlice.actions.setCurrentReportBreakdown({
      currentBreakdown: newReportBreakdown,
    }),
  );
  const newCIView = BREAKDOWNS_TO_CI_VIEW[newReportBreakdown];
  yield put(
    analyticsConfigurationSlice.actions.setCurrentCreativeIntelligenceView({
      currentView: newCIView,
    }),
  );
}

/**
 * @param action
 */
function* loadExistingImpactReport(action) {
  const { reportType, currentPlatform } = action.payload;

  if (reportType === IMPACT_REPORT_TYPES.ELEMENT_PRESENCE) {
    yield call(setAnalyticsSliceForElementPresenceReport);
    return;
  } else {
    yield put(
      analyticsConfigurationSlice.actions.setReportTableView({
        reportTableView: IMPACT_REPORT_TYPES_TO_TABLE_VIEWS[reportType],
      }),
    );
  }

  const platformAnalytics = yield call(getPlatformAnalytics, currentPlatform);
  const availableBreakdowns = yield call([
    platformAnalytics,
    'getSupportedViewByOptions',
  ]);
  yield put(
    analyticsConfigurationSlice.actions.setAvailableReportBreakdowns({
      availableBreakdowns,
    }),
  );
}

/**
 */
function* setAnalyticsSliceForElementPresenceReport() {
  yield put(
    analyticsConfigurationSlice.actions.setAvailableReportBreakdowns({
      availableBreakdowns: [ELEMENT],
    }),
  );
  yield put(
    analyticsConfigurationSlice.actions.setCurrentReportBreakdown({
      currentBreakdown: ELEMENT,
    }),
  );
  yield put(
    analyticsConfigurationSlice.actions.setCurrentCreativeIntelligenceView({
      currentView: ELEMENT_PRESENCE_REPORT,
    }),
  );
}

/**
 */
function* onResetReportTableView() {
  const currentCIView = yield select(getCurrentCreativeIntelligenceView);
  const reset = reportConfigurations[currentCIView]?.reset;
  if (reset) {
    yield put(reset());
  }
}

/**
 * @param sharedPlatformAccountUniqueKey
 */
function* setSharedPlatformAdAccount(sharedPlatformAccountUniqueKey) {
  const activeAccounts = yield select(getActiveAccounts);
  const accountGroups = yield select(getAccountGroups);
  const accountsList = [...activeAccounts, ...accountGroups];
  const accountForRedux = accountsList.find(
    (account) => account.uniqueKey === sharedPlatformAccountUniqueKey,
  );
  if (accountForRedux) {
    yield put(
      platformAccountsSlice.actions.setSharedPlatformAccount(accountForRedux),
    );
  } else {
    showLocalizedErrorBar('ui.ci.noAccessUrlAccount.error');
  }
}

/**
 * @param action
 */
function* onOpenElementMediaModal(action) {
  try {
    yield put(
      analyticsConfigurationSlice.actions.setElementMediaModalPending(),
    );

    const { elementMedia, elementMediaIds } = yield call(
      getElementMedia,
      action.payload,
    );

    const topConfidentMedia = elementMedia
      .sort((a, b) => {
        return a.confidence - b.confidence;
      })
      .slice(0, 4);

    yield put(
      analyticsConfigurationSlice.actions.updateElementMediaModalOnSuccess({
        elementMediaIds,
        elementMedia,
        topConfidentMedia,
      }),
    );
    // eslint-disable-next-line no-unused-vars
  } catch (e) {
    yield call(showLocalizedErrorBar, 'ui.user.elementMediaModal.error');
  }
}

/**
 * @param action
 */
export function* getElementMedia(payload) {
  try {
    const { element, analyticsFilters } = payload;
    const adAccounts = analyticsFilters?.adAccounts;
    const platform = analyticsFilters?.channel;
    const statSettings = analyticsFilters?.statSettings;
    const accountSnapshotProducer = yield call(
      () => new AccountSnapshotProducer(platform, adAccounts, () => {}),
    );
    const PlatformAnalyticsClass = yield call(
      getPlatformAnalyticsWithoutInstantiatingAsync,
      platform,
    );
    const platformAnalytics = yield call(() => new PlatformAnalyticsClass());
    const analyticsConfiguration = yield select(getAnalyticsConfiguration);
    const selectedKpi =
      analyticsFilters?.selectedKpi || analyticsConfiguration.selectedKpi;
    const workspaceIds = analyticsFilters?.workspaceIds;
    const overriddenDates = analyticsFilters?.overriddenDates;
    const organizationId = analyticsFilters?.organizationId;
    const adAccountIds = analyticsFilters?.adAccounts?.map(
      (adAccount) => adAccount.id,
    );

    const { filters } = analyticsConfiguration;
    const {
      selectedObjectiveFilters,
      selectedPlacementFilters,
      availableCampaignFilters,
      selectedCampaignFilters,
      isShowAppAdsActive,
    } = filters;
    const audienceFilter = {};
    const analyticsFiltersWithStatSettings = {
      ...analyticsConfiguration,
      ...filters,
      statSettings,
    };
    const filter = yield call(
      buildCompareReportFilterObject,
      adAccounts,
      platformAnalytics,
      selectedObjectiveFilters,
      selectedPlacementFilters,
      availableCampaignFilters,
      selectedCampaignFilters,
      isShowAppAdsActive,
      null,
      audienceFilter,
    );

    updatePayloadFiltersV2BeforeRequest(
      filter,
      analyticsFiltersWithStatSettings,
      analyticsFilters?.advancedFilters,
    );

    // this is a messy fix for an issue where the selected KPI in KPI report table view may not have data depending on the status of isShowAppAds filter
    // fix is to always request elements for the selection modal with "Cost Per 1000 Impressions" since all ads (app and non-app) should have cost & impressions data
    let kpiForMediaModalRequest = selectedKpi;
    if (
      platform === PLATFORM_ADWORDS &&
      analyticsFiltersWithStatSettings.reportTableView.id ===
        REPORT_TABLE_VIEW.KPI.id
    ) {
      const kpiList = yield select(getKpiList);
      kpiForMediaModalRequest = kpiList.find(
        (kpi) => kpi.name === ADWORDS_COST_PER_1000_IMPRESSIONS_KPI_NAME,
      );
    }

    const elementMedia = yield call(
      [accountSnapshotProducer, 'getPlatformMediaInfoForElement'],
      {
        filter,
        kpi: kpiForMediaModalRequest,
        analyticsFilters: analyticsFiltersWithStatSettings,
        isLifetimeReport: false,
        element,
        overriddenDates,
        organizationId,
        workspaceIds,
        adAccountIds,
      },
    );

    const elementMediaIds = yield call(
      [accountSnapshotProducer, 'getPlatformMediaIdsByElement'],
      filter,
      kpiForMediaModalRequest,
      analyticsFiltersWithStatSettings,
      false,
      element,
      overriddenDates,
      organizationId,
      workspaceIds,
    );

    if (elementMedia.length === 0 || elementMediaIds.length === 0) {
      const shareUrlForReport = yield call(buildAndSaveCIUrlFilters);
      yield call(noCreativeExamples, shareUrlForReport);

      throw new Error(
        `No platform media was found for element ${element.title}`,
      );
    }

    return {
      elementMedia,
      elementMediaIds,
    };
  } catch (e) {
    // If this call is for insight creative examples, the element will have an id
    if (payload.element.id) {
      yield call(
        ciStabilityErrorHandler,
        e,
        'analyticsConfiguration.sagas',
        'loadCreativeExampleForRow',
      );
      yield call(
        showLocalizedErrorBar,
        'ui.user.insightCreatePanel.creativeExamples.loading.noResults',
      );
    } else {
      yield call(
        ciStabilityErrorHandler,
        e,
        'analyticsConfiguration.sagas',
        'getElementMedia',
      );
    }
  }
}
