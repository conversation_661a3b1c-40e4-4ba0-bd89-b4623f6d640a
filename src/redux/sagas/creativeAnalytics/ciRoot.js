import { watchCreativeGroups } from './creativeGroups.sagas';
import { watchAnalyticsConfiguration } from './analyticsConfiguration.sagas';
import {
  watchAudienceReport,
  watchAudienceReportForOverlappingAudiences,
  watchCustomAudiencePagination,
  watchCustomAudienceSearch,
} from './audienceReport.sagas';
import { watchCampaignReport } from './campaignReport.sagas';
import { watchImpactReportFetchData } from './impactReport.sagas';
import { watchPlatformAccounts } from './platformAccounts.sagas';
import { fork, all, spawn } from 'redux-saga/effects';
import { watchCustomCompare } from './customCompare.sagas';
import { watchCreativeElements } from './creativeElements.sagas';
import { watchInsightsLibrary } from './insights.sagas';
import { watchIndustries } from './industries.sagas';
import { watchDurationReport } from './durationReport.sagas';
import {
  watchIndividualCreativeView,
  watchLoadPlatformMedia,
  watchLoadPlatformMediaPerformance,
} from '../../../creativeAnalytics/reports/redux/individualCreativeView/individualCreativeView.sagas';
import { watchMediaFiltering } from '../../../creativeAnalytics/reports/redux/mediaFiltering/mediaFiltering.sagas';
import { watchAdvancedElementPresenceReport } from '../../../creativeAnalytics/reports/redux/elementPresenceReport/elementPresenceReport.sagas';
import { watchCreativeManagerData } from './creativeManager.sagas';

/**
 *
 */
export default function* ciRoot() {
  yield all([
    spawn(watchCreativeManagerData),
    spawn(watchCustomCompare),
    fork(watchAnalyticsConfiguration),
    spawn(watchCreativeGroups),
    fork(watchCreativeElements),
    fork(watchAudienceReport),
    fork(watchAudienceReportForOverlappingAudiences),
    fork(watchCustomAudiencePagination),
    fork(watchCustomAudienceSearch),
    fork(watchAdvancedElementPresenceReport),
    fork(watchCampaignReport),
    fork(watchImpactReportFetchData),
    fork(watchDurationReport),
    // the audience report need to update when additional audiences are added and is the only report that behaves this way
    // all other audience report load/reload conditions are still handled in analytics config
    spawn(watchPlatformAccounts),
    fork(watchInsightsLibrary),
    fork(watchIndustries),
    fork(watchIndividualCreativeView),
    fork(watchLoadPlatformMediaPerformance),
    fork(watchLoadPlatformMedia),
    fork(watchMediaFiltering),
  ]);
}
