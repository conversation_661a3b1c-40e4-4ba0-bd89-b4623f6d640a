import { all, call, fork, put, select, takeLatest } from 'redux-saga/effects';

// slices
import insightsSlice from '../../slices/creativeAnalytics/insights.slice';
import analyticsConfigurationSlice from '../../../redux/slices/creativeAnalytics/analyticsConfiguration.slice';
import toastAlertSlice from '../../slices/toastAlert.slice';

// sagas
import { getElementMedia } from './analyticsConfiguration.sagas';

// selectors
import {
  getCreativeExampleToReload,
  getSelectedRowsCreativeExamples,
  getInsightWorkspaceIds,
  getActiveInsightCreativeExamplesForInsightCreation,
} from '../../selectors/creativeAnalytics/insights.selectors';
import {
  getCurrentPartnerId,
  getOrganizationId,
} from '../../selectors/partner.selectors';
import {
  getCurrentCreativeIntelligenceView,
  getIsSelectedRowsViewActive,
  getReportTableView,
  getSelectedRows,
} from '../../selectors/creativeAnalytics/analyticsConfiguration.selectors';

// services
import InsightService from '../../../analyticsApiSupport/InsightServices/InsightService';
import PlatformAccountService from '../../../apiServices/PlatformAccountService';

// utils
import vmErrorLog, { ciStabilityErrorHandler } from '../../../utils/vmErrorLog';
import { getCreateUpdateInsightRequest } from '../../../utils/buildAndSaveCIUrlFilters';
import { getSelectedRowsInfo } from '../../../utils/tagSelectUtil';
import { formatAndFilterCreativeGroups } from '../../../utils/formatCreativeGroupingsObject';
import { getIntl } from '../../../utils/getIntl';

// constants
import { reportConfigurations } from './reportConfigurations';
import {
  CUSTOM_COMPARE_REPORT,
  DEFAULT,
} from '../../../constants/creativeAnalytics.constants';
import { INSIGHTS_STATUSES } from '../../../constants/insights.constants';
import { REPORT_TABLE_VIEW } from '../../../constants/ci.constants';
import { GLOBALS } from '../../../constants';
import siteMapRouteParams from '../../../routing/siteMapRouteParams';

const {
  onPublishInsight,
  onSaveDraftInsight,
  onSaveInsightSuccess,
  onSaveInsightFailure,
  onOpenInsightCreatePanel,
  setSelectedRowsForCreativeExamples,
  setSelectedRowCreativeExamplePending,
  updateCreativeExamplesOnSuccess,
  updateCreativeExamplesOnFailure,
  onReloadCreativeExampleData,
  clearActiveInsight,
  resetSelectedColumnsForCreativeExamples,
} = insightsSlice.actions;

const { setCurrentCreativeIntelligenceView } =
  analyticsConfigurationSlice.actions;

const { SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const {
  campaign,
  elementPresence,
  audience,
  objective: objectiveReportKey,
  placement: placementReportKey,
  format: formatReportKey,
} = siteMapRouteParams.tabs.creativeIntelligenceCompare;

const { showToastAlert } = toastAlertSlice.actions;
const intl = getIntl();

export function* watchInsightsLibrary() {
  yield all([
    fork(watchInsightSubmit),
    fork(watchOnOpenInsightCreatePanel),
    fork(watchSetSelectedRowsForCreativeExamples),
    fork(watchOnReloadCreativeExampleData),
    fork(watchForReportTypeChange),
  ]);
}

export function* watchInsightSubmit() {
  yield takeLatest(
    [onPublishInsight, onSaveDraftInsight],
    handleOnSubmitInsight,
  );
}

export function* watchOnOpenInsightCreatePanel() {
  yield takeLatest([onOpenInsightCreatePanel], setSelectedRows);
}

export function* watchSetSelectedRowsForCreativeExamples() {
  yield takeLatest([setSelectedRowsForCreativeExamples], loadCreativeExamples);
}

export function* watchOnReloadCreativeExampleData() {
  yield takeLatest([onReloadCreativeExampleData], reloadCreativeExample);
}

export function* watchForReportTypeChange() {
  yield takeLatest(
    [setCurrentCreativeIntelligenceView],
    clearActiveInsightOnReportTypeChange,
  );
}

export function* handleOnSubmitInsight(action) {
  const { analyticsFilters, activeInsight } = action.payload;
  const organizationId = yield select(getOrganizationId);
  const workspaceIds = yield select(getInsightWorkspaceIds);
  const currentWorkspaceId = yield select(getCurrentPartnerId);
  const rowView = yield select(getReportTableView);
  const insightCreativeExamples = yield select(
    getActiveInsightCreativeExamplesForInsightCreation,
  );

  try {
    const newInsight = yield call(getCreateUpdateInsightRequest, {
      activeInsight,
      analyticsFilters,
      rowView,
      insightCreativeExamples,
      organizationId,
      activeWorkspaceId: currentWorkspaceId,
    });
    if (!newInsight.savedAnalyticsReportId) {
      yield call(
        vmErrorLog,
        new Error('Insight saved report URL is missing'),
        'insights.sagas.js',
        'handleOnSubmitInsight',
      );
      yield put(
        showToastAlert({
          message: intl.messages['ui.user.analyticsReport.insights.save.error'],
          type: 'error',
        }),
      );
      return;
    }

    yield call(
      [InsightService, 'createInsight'],
      newInsight,
      organizationId,
      workspaceIds,
    );
    yield put(onSaveInsightSuccess());
    yield call(resetSelectedColumnsForCreativeExamples);
    yield call(showInsightCreationConfirmationToast, newInsight);
  } catch (e) {
    yield call(
      ciStabilityErrorHandler,
      e,
      'insights.sagas.js',
      'handleOnSubmitInsight',
    );
    yield put(onSaveInsightFailure());
    yield put(
      showToastAlert({
        message: intl.messages['error.api.insight.create.failed'],
        type: 'error',
      }),
    );
  }
}

function* showInsightCreationConfirmationToast(newInsight) {
  const { status } = newInsight;

  if (status === INSIGHTS_STATUSES.DRAFT) {
    yield put(
      showToastAlert({
        message:
          intl.messages['ui.user.insightCreatePanel.confirmation.draft.label'],
        type: 'info',
      }),
    );
  }

  if (
    [INSIGHTS_STATUSES.PUBLISHED, INSIGHTS_STATUSES.SCHEDULED].includes(status)
  ) {
    yield put(
      showToastAlert({
        message:
          intl.messages[
            'ui.user.insightCreatePanel.confirmation.published.label'
          ],
        type: 'success',
      }),
    );
  }
}

function* loadCreativeExamples() {
  const reportTableView = yield select(getReportTableView);

  // only load creative example medias for elements and duration, not kpis
  if (reportTableView?.id === REPORT_TABLE_VIEW.KPI.id) {
    return;
  }

  const selectedRowsCreativeExamples = yield select(
    getSelectedRowsCreativeExamples,
  );
  yield all([
    ...Object.values(selectedRowsCreativeExamples)
      .filter((selectedRow) => {
        return selectedRow.status !== SUCCESS;
      })
      .map((selectedRow) => {
        return call(loadCreativeExampleForRow, selectedRow.rowInfo);
      }),
  ]);
}

function* reloadCreativeExample() {
  const { rowInfo } = yield select(getCreativeExampleToReload);
  yield call(loadCreativeExampleForRow, rowInfo);
}

function* loadCreativeExampleForRow(row) {
  try {
    yield put(setSelectedRowCreativeExamplePending({ row }));
    const { elementMedia, elementMediaIds } = yield call(getElementMedia, row);

    /* NOTE: Acknowledging this is janky way to check for errors.
     * If AnalyticsQueryService.queryPlatformMediasAdvanced fails the media objects
     * will only have the platformMediaId property.
     * To throw this exception in AnalyticsQueryService would require rework of how the service handles errors
     */
    if (
      elementMedia[0].media &&
      Object.keys(elementMedia[0].media).length === 1
    ) {
      throw new Error(
        `An error occurred getting platform media for tag ${row.id}`,
      );
    }

    const topConfidentMedia = elementMedia.sort((a, b) => {
      return a.confidence - b.confidence;
    });

    yield put(
      updateCreativeExamplesOnSuccess({
        elementMediaIds,
        elementMedia,
        topConfidentMedia,
        row,
      }),
    );
  } catch (e) {
    yield call(
      ciStabilityErrorHandler,
      e,
      'insights.sagas.js',
      'loadCreativeExampleForRow',
    );
    yield put(
      showToastAlert({
        message:
          intl.messages[
            'ui.user.insightCreatePanel.creativeExamples.loading.error'
          ],
        type: 'error',
      }),
    );
    yield put(updateCreativeExamplesOnFailure({ row }));
  }
}

function* setSelectedRows() {
  const selectedRows = yield select(getSelectedRows);
  const reportTableView = yield select(getReportTableView);
  const isSelectedRowsViewActive = yield select(getIsSelectedRowsViewActive);

  if (isSelectedRowsViewActive) {
    const currentCIView = yield select(getCurrentCreativeIntelligenceView);
    const { reportSelector } = reportConfigurations[currentCIView || DEFAULT];
    const reportData = yield select(reportSelector);
    const { rowItems } = reportData;
    const selectedRowsWithInfo = getSelectedRowsInfo(
      selectedRows,
      rowItems,
      reportTableView,
    );
    const filteredSelectedRowsWithInfo = selectedRowsWithInfo.filter(
      (row) => row.id !== row.genericType,
    );

    yield put(
      setSelectedRowsForCreativeExamples({ filteredSelectedRowsWithInfo }),
    );
  }
}

function* clearActiveInsightOnReportTypeChange() {
  yield put(clearActiveInsight());
}

// eslint-disable-next-line max-params
function* getBaseReportConfig(
  sharedFilterObject,
  activeInsight,
  selectedKpi,
  reportTableView,
  reportKey,
  insightAccounts,
  { organizationId, workspaceIds },
) {
  const {
    selectedOpCompareOption,
    filterPanel,
    isDefaultKPITimeRangeFilterActive,
    audienceFilterSelections,
    campaignFilterSelections,
    elementFilterSelections,
  } = sharedFilterObject;
  const { objectives: insightObjectives, placements: insightPlacements } =
    activeInsight;
  const {
    media: mediaTypes,
    impressionFilterMinValue,
    impressionFilterMaxValue,
    createdByVidmob,
    showAppAds,
    selectedPlacementNestingOption,
    dateRange,
    campaigns,
    availableObjectiveFilters,
    objectives: filterPanelObjectives,
    availablePlacementFilters,
    placements: filterPanelPlacements,
  } = filterPanel;
  const { analyticsStartDate, analyticsEndDate } = dateRange;

  // when in objective report, if insightObjectives do not intersect availableObjectives, no objective filter for report
  const canFilterByInsightsObjectives =
    reportKey === objectiveReportKey &&
    insightObjectives?.some((objective) =>
      availableObjectiveFilters?.includes(objective),
    );
  const objectiveFiltersForKeyData = canFilterByInsightsObjectives
    ? insightObjectives
    : filterPanelObjectives;

  // when in placement report, if insightPlacements do not intersect availablePlacements, no placement filter for report
  const canFilterByInsightsPlacements =
    reportKey === placementReportKey &&
    insightPlacements?.some((placement) =>
      availablePlacementFilters?.includes(placement),
    );
  const placementFiltersForKeyData = canFilterByInsightsPlacements
    ? insightPlacements
    : filterPanelPlacements;

  const analyticsConfiguration = {
    selectedKpi,
    selectedOpCompareOption,
    reportTableView,
    isDefaultKPITimeRangeFilterActive,
    isPercentLiftViewEnabled: true,
    filters: {
      selectedPlacementFilters: placementFiltersForKeyData, // don't send placement filters when disabled
      selectedObjectiveFilters: objectiveFiltersForKeyData,
      selectedMediaTypeFilters: mediaTypes,
      impressionFilterMinValue,
      impressionFilterMaxValue,
      createdByVidmob,
      isShowAppAdsActive: showAppAds,
      selectedPlacementNestingOption,
      analyticsStartDate,
      analyticsEndDate,
    },
  };

  if (Array.isArray(campaigns) && campaigns?.length > 0) {
    // this step is a little hacky, but it's needed so buildCompareReportFilterObject correctly filters the selected campaigns
    // because of filter panel behavior, if available and selected have the same length it will omit the filter
    const dummyAvailableCampaigns = [{ id: '1' }, { id: '2' }];
    analyticsConfiguration.filters.selectedCampaignFilters = [];
    campaigns.forEach((campaignId) => {
      dummyAvailableCampaigns.push({ id: campaignId });
      analyticsConfiguration.filters.selectedCampaignFilters.push({
        id: campaignId,
      });
    });

    analyticsConfiguration.filters.availableCampaignFilters =
      dummyAvailableCampaigns;
  }

  const hasCompareGroups =
    reportKey === CUSTOM_COMPARE_REPORT &&
    sharedFilterObject?.compareGroups?.length > 0;
  const currentCompareGroups = hasCompareGroups
    ? yield call(
        getCompareGroups,
        insightAccounts,
        sharedFilterObject.compareGroups,
        organizationId,
        workspaceIds,
      )
    : null;

  return {
    analyticsConfiguration,
    ...(selectedOpCompareOption ? { selectedOpCompareOption } : {}),
    ...(hasCompareGroups ? { currentCompareGroups } : {}),
    ...(reportKey === audience && audienceFilterSelections
      ? { audienceFilterSelections }
      : {}),
    ...(reportKey === campaign && campaignFilterSelections
      ? { campaignFilterSelections }
      : {}),
    ...(reportKey === elementPresence && elementFilterSelections
      ? { elementFilterSelections }
      : {}),
  };
}

function* getCompareGroups(
  insightAccount,
  compareGroups,
  organizationId,
  workspaceIds,
) {
  const { id, platform } = insightAccount;
  const creativeGroupsResponse = yield call(
    [PlatformAccountService, 'getCreativeGroups'],
    {
      platform,
      adAccountIds: [id],
      organizationId,
      workspaceIds,
    },
  );
  const accountCreativeGroups = creativeGroupsResponse.data;
  const groupsInInsight = accountCreativeGroups
    .filter((accountCreativeGroup) =>
      compareGroups.includes(accountCreativeGroup.id),
    )
    .map((group) => {
      // the format and filter will fail if there's no media prop on the group
      // and we judge if the media is viewable by what's been requested in the creative manager
      // which we don't have access to here
      return {
        ...group,
        media: group.platformMedias.map((platformMedia) => ({
          ...platformMedia,
          isMediaViewable: true,
        })),
      };
    });

  return formatAndFilterCreativeGroups(groupsInInsight);
}
