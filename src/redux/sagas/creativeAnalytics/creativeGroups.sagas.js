import { takeLatest, select, all, fork, put, call } from 'redux-saga/effects';
import creativeGroupsSlice from '../../slices/creativeAnalytics/creativeGroups.slice';
import { getCurrentPlatformAccount } from '../../selectors/platformAccounts.selectors';
import PlatformAccountService from '../../../apiServices/PlatformAccountService';
import {
  showConfirmationBar,
  showLocalizedErrorBar,
} from '../../../utils/showConfirmationBar';
import { isNumber } from '../../../utils/typeCheckUtils';
import {
  getCreativeGroupsLoadingStatus,
  getFilteredCreativeGroups,
  getActiveCompareGroup,
  getStartAndEndDateForPersistingGroup,
  getCompareGroupsFormattedForLocalStorageOrReport,
  getCreativeGroups,
  getCompareGroupsWithUpdatedMediaAvailability,
  getAccountMediaAsObject,
  getMediaKeyedByPlatformMediaIdLoadingStatus,
  getFilteredCreativeGroupsLoadingStatus,
} from '../../selectors/creativeAnalytics/creativeGroups.selectors';
import {
  getAnalyticsFiltersSelectedCampaigns,
  getSharedFilterId,
  getSharedFilterObject,
} from '../../selectors/creativeAnalytics/analyticsConfiguration.selectors';
import { GLOBALS } from '../../../constants';
import {
  CURRENT_COMPARE_GROUPS_STORAGE_KEY,
  CURRENT_SELECTED_CAMPAIGNS_STORAGE_KEY,
  CURRENT_AD_ACCOUNT_ID_STORAGE_KEY,
  CURRENT_AD_ACCOUNT_TYPE_STORAGE_KEY,
} from '../../../constants/ci.constants';
import { getIntl } from '../../../utils/getIntl';
import platformAccountsSlice from '../../slices/creativeAnalytics/platformAccounts.slice';
import vmErrorLog from '../../../utils/vmErrorLog';
import { ANALYTICS_FILTERS_LOCAL_STORAGE_KEY } from '../../../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersConstants';
import { PLATFORM_ACCOUNT_TYPES } from '../../../constants/platform.constants';
import { getOrganizationId } from '../../selectors/partner.selectors';
import { CREATIVE_GROUPS_FETCH_DATA_ACTION_TYPE } from '../../../creativeAnalytics/__pages/CreativeManager/helpers/CreativeManagerConstants';
const { NOT_LOADED, SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

/**
 *
 */
export function* watchCreativeGroups() {
  yield all([
    fork(watchAccountAssetListStatusPending),
    fork(watchAccountAssetAndCreativeGroupSuccess),
    fork(watchSaveUpdateCreativeGroup),
    fork(watchClearCompareGroups),
    fork(watchCurrentSelectedAccountChange),
    fork(watchAddOrRemoveCompareGroups),
  ]);
}

/**
 *
 */

export function* watchAccountAssetListStatusPending() {
  yield takeLatest(CREATIVE_GROUPS_FETCH_DATA_ACTION_TYPE, onGetCreativeGroups);
}

/**
 *
 */
export function* watchAccountAssetAndCreativeGroupSuccess() {
  yield takeLatest(
    [
      creativeGroupsSlice.actions.setMediaKeyedByPlatformMediaId,
      creativeGroupsSlice.actions.updateOnCreativeGroupsLoadingSuccessSaga,
      creativeGroupsSlice.actions.refreshFilteredCreativeGroups,
    ],
    onFilterCreativeGroups,
  );
}

/**
 *
 */
export function* watchSaveUpdateCreativeGroup() {
  yield takeLatest(
    creativeGroupsSlice.actions.onSaveOrUpdateActiveGroupSetPending,
    onSaveUpdateActiveGroup,
  );
}

/**
 *
 */
export function* watchClearCompareGroups() {
  yield takeLatest(
    creativeGroupsSlice.actions.clearCompareGroups,
    clearLocalStorage,
  );
}

/**
 *
 */
export function* watchCurrentSelectedAccountChange() {
  yield takeLatest(
    platformAccountsSlice.actions.updateCurrentSelectedPlatformAccount,
    onCurrentSelectedAccountChange,
  );
}

/**
 *
 */
export function* watchAddOrRemoveCompareGroups() {
  yield takeLatest(
    [
      creativeGroupsSlice.actions.setIsComparingMedia,
      creativeGroupsSlice.actions.clearCompareGroups,
      creativeGroupsSlice.actions.onDeleteGroupSuccess,
    ],
    saveToLocalStorage,
  );
}

/**
 *
 */
function* onCurrentSelectedAccountChange() {
  yield put(creativeGroupsSlice.actions.reset());
  yield call(clearLocalStorage);
}

/**
 *
 */
function* onFilterCreativeGroups() {
  const mediaLoadingStatus = yield select(
    getMediaKeyedByPlatformMediaIdLoadingStatus,
  );
  const groupsLoadingStatus = yield select(getCreativeGroupsLoadingStatus);
  const filteredGroupLoadingStatus = yield select(
    getFilteredCreativeGroupsLoadingStatus,
  );
  const sharedFilterId = yield select(getSharedFilterId);
  const sharedFilterObject = yield select(getSharedFilterObject);
  if (
    mediaLoadingStatus === SUCCESS &&
    groupsLoadingStatus === SUCCESS &&
    filteredGroupLoadingStatus === NOT_LOADED
  ) {
    yield put(
      creativeGroupsSlice.actions.setFilteredCreativeGroupsLoadingStatusPending(),
    );
    const filteredCreativeGroups = yield select(getFilteredCreativeGroups);
    if (sharedFilterId && sharedFilterObject) {
      yield call(applySharedCreativeGroups);
    } else {
      yield call(retrieveFromLocalStorage);
    }

    yield put(
      creativeGroupsSlice.actions.updateOnFilteredCreativeGroupsSuccess({
        filteredCreativeGroups,
      }),
    );
    const compareGroupsWithUpdatedMediaAvailability = yield select(
      getCompareGroupsWithUpdatedMediaAvailability,
    );
    yield put(
      creativeGroupsSlice.actions.updateCompareGroupsAndActiveIndex({
        compareGroups: compareGroupsWithUpdatedMediaAvailability,
      }),
    );
  }
}

/**
 *
 */
export function* onGetCreativeGroups(action) {
  try {
    yield put(
      creativeGroupsSlice.actions.setCreativeGroupsLoadingStatusPending(),
    );
    yield put(creativeGroupsSlice.actions.clearFilteredCreativeGroups());

    const organizationId = yield select(getOrganizationId);
    const { adAccounts, workspaces, channel } = action.payload.globalFilters;
    const workspaceIds = workspaces?.value?.map((workspace) => workspace.id);
    if (!adAccounts?.value || !workspaceIds) {
      return;
    }

    const adAccountIds = adAccounts.value.map(
      (account) => account.platformAccountId,
    );

    let offset = 0;
    const perPage = 100;
    const allCreativeGroups = [];
    let hasMorePages = true;

    while (hasMorePages) {
      const { data, pagination } = yield call(
        [PlatformAccountService, 'getCreativeGroups'],
        {
          platform: channel.value,
          adAccountIds,
          workspaceIds,
          organizationId,
          perPage,
          offset,
        },
      );
      allCreativeGroups.push(...data);
      offset = pagination.nextOffset;
      hasMorePages = pagination.totalSize > allCreativeGroups.length;
    }
    yield put(
      creativeGroupsSlice.actions.updateOnCreativeGroupsLoadingSuccessSaga({
        creativeGroups: allCreativeGroups,
      }),
    );
  } catch (error) {
    console.error(error);
  }
}

/**
 * @param action
 */
function* onSaveUpdateActiveGroup(action) {
  const { groupName, analyticsFilters } = action.payload;
  const organizationId = yield select(getOrganizationId);
  const { adAccounts, workspaces, channel } = analyticsFilters.globalFilters;

  if (!adAccounts?.value || !workspaces?.value) {
    return;
  }

  const workspaceIds = workspaces.value.map((workspace) => workspace.id);
  const activeCompareGroup = yield select(getActiveCompareGroup);
  const { platformMediaIds, nonUniqueAdAccountIds } =
    activeCompareGroup.media.reduce(
      (acc, asset) => {
        acc.platformMediaIds.push(asset.platformMediaId);
        acc.nonUniqueAdAccountIds.push(
          asset.platformAccountId.split('act_').pop(),
        );

        return acc;
      },
      { platformMediaIds: [], nonUniqueAdAccountIds: [] },
    );
  const adAccountIds = [...new Set(nonUniqueAdAccountIds)];
  const intl = yield call(getIntl);
  const { startDate, endDate } = yield select(
    getStartAndEndDateForPersistingGroup,
  );
  const isGroupExisting =
    (isNumber(activeCompareGroup.id) && activeCompareGroup.id > 0) ||
    activeCompareGroup.isSavedGroup;
  const groupRequestFunction = isGroupExisting
    ? PlatformAccountService.updateCreativeGroup
    : PlatformAccountService.createCreativeGroup;

  const groupForCreateRequest = {
    name: groupName,
    platform: channel.value,
    startDate,
    endDate,
    platformMediaIds,
    privacyLevel: activeCompareGroup.privacyLevel,
  };

  const groupForUpdateRequest = {
    name: groupName,
    platformMediaGroupId: activeCompareGroup?.id,
    startDate,
    endDate,
    platformMediaIds,
    privacyLevel: activeCompareGroup.privacyLevel,
  };

  const groupForRequest = isGroupExisting
    ? groupForUpdateRequest
    : groupForCreateRequest;

  try {
    const response = yield call(
      { context: PlatformAccountService, fn: groupRequestFunction },
      {
        platform: channel.value,
        adAccountIds,
        accountType: PLATFORM_ACCOUNT_TYPES.ACCOUNT,
        group: groupForRequest,
        workspaceIds,
        organizationId,
      },
    );
    const updatedGroup = {
      ...response,
      media: activeCompareGroup.media,
      isSavedGroup: true,
    };
    yield put(
      creativeGroupsSlice.actions.updateActiveGroupOnSaveUpdateRequestSuccess({
        updatedGroup,
      }),
    );
    yield call(
      showConfirmationBar,
      intl.messages['ui.user.creativeDrawer.groups.groupSaved'],
    );
    yield call(saveToLocalStorage);
    yield call(onGetCreativeGroups, { payload: analyticsFilters });
  } catch (error) {
    yield call(
      showLocalizedErrorBar,
      'ui.user.creativeDrawer.groups.groupSaved.error',
    );
    yield call(
      vmErrorLog,
      error,
      'creativeGroups.sagas onSaveUpdateActiveGroup',
      `error saving group ${groupForRequest.name}`,
    );
    yield put(
      creativeGroupsSlice.actions.updateActiveGroupOnSaveUpdateRequestFailure(),
    );
  }
}

/**
 *
 */
export function* saveToLocalStorage() {
  try {
    const analyticsFilterStr = yield call(
      { context: localStorage, fn: localStorage.getItem },
      ANALYTICS_FILTERS_LOCAL_STORAGE_KEY,
    );
    const analyticsFilter = JSON.parse(analyticsFilterStr);
    if (analyticsFilter?.globalFilters?.adAccounts?.value.length === 1) {
      const currentSelectedAccount =
        analyticsFilter.globalFilters.adAccounts?.value[0];
      const selectedCampaigns = yield select(
        getAnalyticsFiltersSelectedCampaigns,
      );
      const compareGroupsFormatted = yield select(
        getCompareGroupsFormattedForLocalStorageOrReport,
      );
      const stringifiedCompareGroups = yield call(
        { context: JSON, fn: JSON.stringify },
        compareGroupsFormatted,
      );
      const stringifiedCampaigns = yield call(
        { context: JSON, fn: JSON.stringify },
        selectedCampaigns,
      );
      yield call(
        // eslint-disable-next-line no-undef
        { context: localStorage, fn: localStorage.setItem },
        CURRENT_COMPARE_GROUPS_STORAGE_KEY,
        stringifiedCompareGroups,
      );
      yield call(
        // eslint-disable-next-line no-undef
        { context: localStorage, fn: localStorage.setItem },
        CURRENT_SELECTED_CAMPAIGNS_STORAGE_KEY,
        stringifiedCampaigns,
      );
      yield call(
        // eslint-disable-next-line no-undef
        { context: localStorage, fn: localStorage.setItem },
        CURRENT_AD_ACCOUNT_ID_STORAGE_KEY,
        currentSelectedAccount.platformAccountId,
      );
      yield call(
        // eslint-disable-next-line no-undef
        { context: localStorage, fn: localStorage.setItem },
        CURRENT_AD_ACCOUNT_TYPE_STORAGE_KEY,
        'account',
      );
    }
  } catch (error) {
    console.error(error);
  }
}

/**
 *
 */
export function* clearLocalStorage() {
  yield call(
    // eslint-disable-next-line no-undef
    { context: localStorage, fn: localStorage.removeItem },
    CURRENT_COMPARE_GROUPS_STORAGE_KEY,
  );
  yield call(
    // eslint-disable-next-line no-undef
    { context: localStorage, fn: localStorage.removeItem },
    CURRENT_SELECTED_CAMPAIGNS_STORAGE_KEY,
  );
  yield call(
    // eslint-disable-next-line no-undef
    { context: localStorage, fn: localStorage.removeItem },
    CURRENT_AD_ACCOUNT_ID_STORAGE_KEY,
  );
  yield call(
    // eslint-disable-next-line no-undef
    { context: localStorage, fn: localStorage.removeItem },
    CURRENT_AD_ACCOUNT_TYPE_STORAGE_KEY,
  );
}

/**
 *
 */
export function* retrieveFromLocalStorage() {
  const existingGroupsJson = yield call(
    // eslint-disable-next-line no-undef
    { context: localStorage, fn: localStorage.getItem },
    CURRENT_COMPARE_GROUPS_STORAGE_KEY,
  );
  const existingGroups = yield call(
    { context: JSON, fn: JSON.parse },
    existingGroupsJson,
  );

  if (existingGroups && existingGroups.length > 0) {
    const currentSelectedAccount = yield select(getCurrentPlatformAccount);
    const existingGroupsAccountId = yield call(
      // eslint-disable-next-line no-undef
      { context: localStorage, fn: localStorage.getItem },
      CURRENT_AD_ACCOUNT_ID_STORAGE_KEY,
    );

    if (
      currentSelectedAccount?.id?.toString() ===
      existingGroupsAccountId.toString()
    ) {
      const newCompareGroups = [];
      const accountMedia = yield select(getAccountMediaAsObject);
      newCompareGroups.push(
        ...existingGroups.map((group) => {
          const groupMedia = group.group.media.map((asset) => {
            const { platformMediaId } = asset;
            if (platformMediaId && accountMedia[platformMediaId]) {
              return {
                ...accountMedia[platformMediaId],
                isMediaViewable: true,
              };
            }

            return asset;
          });

          return {
            ...group.group,
            groupId: group.group.id,
            media: groupMedia,
          };
        }),
      );
      if (newCompareGroups.length > 0) {
        const newActiveGroupIndex = newCompareGroups.length - 1;
        yield put(
          creativeGroupsSlice.actions.updateCompareGroupsAndActiveIndex({
            compareGroups: newCompareGroups,
            activeGroupIndex: newActiveGroupIndex,
          }),
        );
      }
    } else {
      yield call(clearLocalStorage);
    }
  }
}

export function* applySharedCreativeGroups() {
  const sharedFilterObject = yield select(getSharedFilterObject);
  if (sharedFilterObject?.compareGroups?.length > 0) {
    const creativeGroups = yield select(getCreativeGroups);
    const accountMedia = yield select(getAccountMediaAsObject);
    const sharedCompareGroups = creativeGroups
      .filter((group) => sharedFilterObject.compareGroups.includes(group.id))
      .map((group) => {
        return {
          ...group,
          isSavedGroup: true,
          media: group.media.map((asset) => {
            const { platformMediaId } = asset;
            if (platformMediaId && accountMedia[platformMediaId]) {
              return {
                ...accountMedia[platformMediaId],
                isMediaViewable: true,
              };
            }

            return asset;
          }),
        };
      });
    yield put(
      creativeGroupsSlice.actions.updateCompareGroupsAndActiveIndex({
        compareGroups: sharedCompareGroups,
      }),
    );
  }
}
