import { all, fork, put, call, select, takeLatest } from 'redux-saga/effects';
import projectCreateSlice from '../../slices/projectCreate.slice';
import partnerSlice from '../../slices/partner.slice';
import {
  getProjectBeingCreated,
  getInsightsToCreateProject,
} from '../../selectors/projectCreate.selectors';
import { getPartner } from '../../../featureServices/partner/getUserPartners';
import ProjectService from '../../../apiServices/ProjectService';
import ProductService from '../../../apiServices/ProductService';
import PartnerService from '../../../apiServices/PartnerService';
import PaymentService from '../../../apiServices/PaymentService';
import OutputVariationTypeService from '../../../apiServices/OutputVariationTypeService';
import OutputMediaVariationTypeService from '../../../apiServices/OutputMediaVariationTypeService';
import vmErrorLog from '../../../utils/vmErrorLog';
import ProjectOutputGroupService from '../../../apiServices/ProjectOutputGroupService';
import ProjectOutputVideoService from '../../../apiServices/ProjectOutputVideoService';
import { displayToastAlert } from '../../../muiCustomComponents/ToastAlert/displayToastAlert';
import { getProjectPermissionsForProject } from '../../../featureServices/permissions/ProjectPermissions';
import { formatOutputGroupsAndOutputVideosForRedux } from '../../../featureServices/ProjectCreate/creativeOutputsProjectCreate';
import { PROJECT_CREATE } from '../../../constants/';
import { isByocSelfManaged } from '../../../featureServices/ProjectCreate/byocSelfManaged';
import insightsSlice from '../../slices/creativeAnalytics/insights.slice';
import { getProjectCreateFlowSteps } from '../../../creativeProduction/utils/getProjectCreateFlowSteps';
import { getIntl } from '../../../utils/getIntl';
import { getOrganizationId } from '../../selectors/partner.selectors';
import BffService from '../../../apiServices/BffService';

const { OUTPUT_VARIATION_TYPES_OPTIONS } = PROJECT_CREATE;

function* watchProjectCreate() {
  yield all([
    fork(watchCreateProjectAPIRequest),
    fork(watchUpdateProjectAPIRequest),
    fork(watchLoadProjectIntoState),
    fork(watchGetPriceQuote),
    fork(watchGetProjectFundingOptions),
    fork(watchChangeProjectStatus),
  ]);
}

export function* watchCreateProjectAPIRequest() {
  yield takeLatest(
    [projectCreateSlice.actions.createProjectAPIRequest],
    createProjectAPIRequest,
  );
}

export function* watchUpdateProjectAPIRequest() {
  yield takeLatest(
    [
      projectCreateSlice.actions.updateProjectAPIRequest,
      projectCreateSlice.actions.updateProjectWithCreativeOutputs,
    ],
    updateProjectAPIRequest,
  );
}

export function* watchLoadProjectIntoState() {
  yield takeLatest(
    [projectCreateSlice.actions.loadProjectIntoState],
    loadProjectIntoState,
  );
}

export function* watchGetPriceQuote() {
  yield takeLatest(
    [projectCreateSlice.actions.getPriceQuote],
    getProjectPriceOnCreativeOutputs,
  );
}

export function* watchGetProjectFundingOptions() {
  yield takeLatest(
    [projectCreateSlice.actions.getProjectFundingOptions],
    getFundingOptions,
  );
}

export function* watchChangeProjectStatus() {
  yield takeLatest(
    [projectCreateSlice.actions.changeProjectStatus],
    changeProjectStatus,
  );
}

export const getCurrentPartner = (state) => state.partner.currentPartner;

export const getPartnerAPICall = PartnerService.getPartner.bind(PartnerService);
export const getProjectAPICall = ProjectService.getProject.bind(ProjectService);
export const createProjectAPICall =
  ProjectService.createProject.bind(ProjectService);
export const updateProjectAPICall =
  ProjectService.updateProject.bind(ProjectService);
export const moveProjectToEditApiCall =
  ProjectService.moveProjectToEdit.bind(ProjectService);
export const getProductAPICall = ProductService.getProduct.bind(ProductService);
export const getVariationsAPICall =
  OutputVariationTypeService.getVariationTypes.bind(OutputVariationTypeService);
export const getOutputMediaVariationsAPICall =
  OutputMediaVariationTypeService.getVariationTypes.bind(
    OutputMediaVariationTypeService,
  );
export const getOutputGroupList =
  ProjectOutputGroupService.getOutputGroupList.bind(ProjectOutputGroupService);
export const getOutputVideoList =
  ProjectOutputVideoService.getOutputVideoList.bind(ProjectOutputVideoService);
export const getProjectPriceQuote =
  PaymentService.getProjectPrice.bind(PaymentService);
export const getProjectPriceWithId =
  PaymentService.getProjectV2Price.bind(PaymentService);
export const getProjectFinanceAPICall =
  ProjectService.getProjectFinanceDetails.bind(ProjectService);
export const updateProjectFinanceAPICall =
  ProjectService.updateProjectFinanceDetails.bind(ProjectService);
export const getProjectFundingOptionsAPICall =
  ProjectService.getProjectFundingOptions.bind(ProjectService);
export const getPartnerFundingOptionsAPICall =
  PartnerService.getPartnerFundingOptions.bind(ProjectService);

export function* createProjectAPIRequest(projectAction) {
  const { projectCreateData, partnerId, projectCreateFinanceData } =
    projectAction.payload;
  const { productId } = projectCreateData;
  yield put(projectCreateSlice.actions.setProjectAPIRequestStatusPending());
  try {
    yield call(setNewCurrentPartner, partnerId);
    const apiResponse = yield call(
      createProjectAPICall,
      { ...projectCreateData },
      partnerId,
    );
    let projectBeingCreated = apiResponse.data;

    const variationTypes = yield call(getOutputMediaVariationTypes);
    const product = yield call(getCompleteProductWithFormats, productId);

    projectBeingCreated = {
      ...projectBeingCreated,
      outputGroupsData: [],
      product,
      variationTypes,
    };

    yield put(
      projectCreateSlice.actions.setProjectBeingCreated({
        projectBeingCreated,
      }),
    );
    yield call(
      updateProjectFinanceDetails,
      projectCreateFinanceData,
      projectBeingCreated.id,
    );

    yield put(projectCreateSlice.actions.setProjectAPIRequestStatusSuccess());

    // link insights to project if project create was kicked off from the insights library
    yield call(linkInsightsToProject, projectBeingCreated.id);
  } catch (e) {
    yield put(
      projectCreateSlice.actions.setProjectAPIErrorCode({
        errorCode: e.responseErrorCode,
      }),
    );
    yield put(projectCreateSlice.actions.setProjectAPIRequestStatusFailed());
    yield call(
      vmErrorLog,
      e,
      'An API error occurred in project saga create action',
    );
  }
}

export function* updateProjectAPIRequest(projectAction) {
  const { projectUpdates, projectFinanceUpdates, toEdit } =
    projectAction.payload;

  yield put(projectCreateSlice.actions.setProjectAPIRequestStatusPending());
  try {
    const currentProjectBeingCreated = yield select(getProjectBeingCreated);
    const apiResponse = yield call(
      updateProjectAPICall,
      currentProjectBeingCreated.id,
      projectUpdates,
    );
    const projectBeingCreated = {
      ...currentProjectBeingCreated,
      ...apiResponse.data,
    };
    yield put(
      projectCreateSlice.actions.setProjectBeingCreated({
        projectBeingCreated,
      }),
    );
    yield call(
      updateProjectFinanceDetails,
      projectFinanceUpdates,
      currentProjectBeingCreated.id,
    );

    if (toEdit) {
      yield put(
        projectCreateSlice.actions.changeProjectStatus({ toEdit: true }),
      );
    }

    yield put(projectCreateSlice.actions.setProjectAPIRequestStatusSuccess());
  } catch (e) {
    yield put(
      projectCreateSlice.actions.setProjectAPIErrorCode({
        errorCode: e.responseErrorCode,
      }),
    );
    yield put(projectCreateSlice.actions.setProjectAPIRequestStatusFailed());
    yield call(
      vmErrorLog,
      e,
      'An API error occurred in project saga update action',
    );
  }
}

export function* loadProjectIntoState(projectAction) {
  const { projectId } = projectAction.payload;
  yield put(
    projectCreateSlice.actions.setProjectLoadingAPIRequestStatusPending(),
  );
  try {
    const apiResponse = yield call(getProjectAPICall, projectId, 'industry');
    let projectBeingLoaded = apiResponse.data;
    const { productId, partner } = projectBeingLoaded;
    const permissionsAsShape = yield call(
      getProjectPermissionsForProject,
      projectBeingLoaded.id,
    );

    const currentPartner = yield call(setNewCurrentPartner, partner.id);

    const product = yield call(getCompleteProductWithFormats, productId);

    const variationTypes = yield call(getOutputMediaVariationTypes);

    projectBeingLoaded = {
      ...projectBeingLoaded,
      product,
      variationTypes,
      permissions: permissionsAsShape,
    };

    const PROJECT_CREATE_STEP_ORDER = yield call(
      getProjectCreateFlowSteps,
      currentPartner,
      projectBeingLoaded,
    );
    yield call(getProjectFinanceDetails, projectId, PROJECT_CREATE_STEP_ORDER);

    if (
      PROJECT_CREATE?.PROJECT_CREATE_FLOWS[currentPartner.accountTypeIdentifier]
        ?.CREATIVE_OUTPUTS
    ) {
      const outputGroupListRequest = yield call(getOutputGroupList, projectId);
      const outputGroupList = outputGroupListRequest.data;

      if (outputGroupList) {
        const outputVideoListRequest = yield call(
          getOutputVideoList,
          projectId,
          { extraFields: 'format,variation' },
        );
        const outputVideoList = outputVideoListRequest.data;
        const formattedOutputs = yield call(
          formatOutputGroupsAndOutputVideosForRedux,
          outputGroupList,
          outputVideoList,
        );
        projectBeingLoaded = {
          ...projectBeingLoaded,
          outputGroupsData: formattedOutputs,
        };
        if (!isByocSelfManaged()) {
          const priceRequest = yield call(getProjectPriceWithId, projectId);
          const price = priceRequest.data;
          projectBeingLoaded = { ...projectBeingLoaded, priceQuote: price };
        }
      } else {
        projectBeingLoaded = {
          ...projectBeingLoaded,
          outputGroupsData: [],
          priceQuote: 0,
        };
      }
    }

    yield put(
      projectCreateSlice.actions.setProjectBeingCreated({
        projectBeingCreated: projectBeingLoaded,
      }),
    );
    yield put(
      projectCreateSlice.actions.setProjectLoadingAPIRequestStatusSuccess(),
    );
  } catch (e) {
    yield put(projectCreateSlice.actions.setLoadProjectForEditFailed(true));
    yield put(
      projectCreateSlice.actions.setProjectLoadingAPIRequestStatusFailed(),
    );
    yield call(
      vmErrorLog,
      e,
      'An API error occurred in project saga update action',
    );
  }
}

export function* getProjectPriceOnCreativeOutputs(projectAction) {
  const { partnerId, outputGroupsData } = projectAction.payload;
  yield put(projectCreateSlice.actions.setPriceAPIRequestStatusPending());
  try {
    const priceQuoteRequest = yield call(
      getProjectPriceQuote,
      outputGroupsData,
      partnerId,
    );
    const priceQuote = priceQuoteRequest.data;
    yield put(projectCreateSlice.actions.setPriceQuote({ priceQuote }));
    yield put(projectCreateSlice.actions.setPriceAPIRequestStatusSuccess());
  } catch (e) {
    yield put(
      projectCreateSlice.actions.setPriceAPIErrorCode({
        priceErrorCode: e.responseErrorCode,
      }),
    );
    yield put(projectCreateSlice.actions.setPriceAPIRequestStatusFailed());
    yield call(
      vmErrorLog,
      e,
      'An API error occurred in project saga get price on creative outputs action',
    );
  }
}

export function* setInvoicePartnerOrFundingAccount(financeDetailsObject) {
  if (
    financeDetailsObject.fundingMethod.name ===
    PROJECT_CREATE.PROJECT_FUNDING_METHODS.CUSTOM_INVOICE_PARTNER.name
  ) {
    yield put(
      projectCreateSlice.actions.setProjectFinanceDetails({
        ...financeDetailsObject,
        fundingMethod: financeDetailsObject.fundingMethod.name,
        invoicePartnerId: financeDetailsObject.invoicePartnerId,
        fundingAccount: null,
      }),
    );
  } else {
    yield put(
      projectCreateSlice.actions.setProjectFinanceDetails({
        ...financeDetailsObject,
        fundingMethod: financeDetailsObject.fundingMethod.name,
        fundingAccount: financeDetailsObject.fundingAccount.id,
        invoicePartnerId: null,
      }),
    );
  }
}

export function* getProjectFinanceDetails(projectId, steps) {
  if (
    steps?.includes(PROJECT_CREATE.PROJECT_CREATE_STEPS.ADMIN_PROJECT_DETAILS)
  ) {
    const financeInformationResponse = yield call(
      getProjectFinanceAPICall,
      projectId,
    );
    const financeDetailsObject = financeInformationResponse.data;
    yield call(setInvoicePartnerOrFundingAccount, financeDetailsObject);
  }
}

export function* updateProjectFinanceDetails(financeDetails, projectId) {
  if (financeDetails) {
    const updatedFinanceDetails = yield call(
      updateProjectFinanceAPICall,
      financeDetails,
      projectId,
    );
    const financeDetailsObject = updatedFinanceDetails.data;
    yield call(setInvoicePartnerOrFundingAccount, financeDetailsObject);
  }
}

export function* getFundingOptions(projectAction) {
  const { projectId, partnerId } = projectAction.payload;
  yield put(
    projectCreateSlice.actions.setProjectFundingOptionsAPIStatusPending(),
  );
  try {
    let projectFundingOptionsResponse;
    if (projectId) {
      projectFundingOptionsResponse = yield call(
        getProjectFundingOptionsAPICall,
        projectId,
      );
    } else {
      projectFundingOptionsResponse = yield call(
        getPartnerFundingOptionsAPICall,
        partnerId,
      );
    }

    const formattedPartnerDefaultFundingMethod = {
      ...projectFundingOptionsResponse.data.partnerDefaultFundingMethod,
    };
    if (
      formattedPartnerDefaultFundingMethod.fundingMethod.name ===
      PROJECT_CREATE.PROJECT_FUNDING_METHODS.INVOICE_OVERRIDE.name
    ) {
      formattedPartnerDefaultFundingMethod.fundingMethod.name =
        PROJECT_CREATE.PROJECT_FUNDING_METHODS.CUSTOM_INVOICE_PARTNER.name;
    }

    const currentPartner = yield select(getCurrentPartner);
    const intl = yield call(getIntl);
    const formattedProjectFundingMethods = getFormattedProjectFundingMethods(
      projectFundingOptionsResponse.data.fundingMethods,
    );
    yield put(
      projectCreateSlice.actions.setFundingOptions({
        ...projectFundingOptionsResponse.data,
        internalFundingAccounts:
          projectFundingOptionsResponse.data.internalFundingAccounts,
        fundingMethods: formattedProjectFundingMethods,
        partnerDefaultFundingMethod: formattedPartnerDefaultFundingMethod,
        prepaidFundingMethod: [
          {
            name: intl.messages[
              PROJECT_CREATE.PROJECT_FUNDING_METHODS.PREPAID.label
            ],
          },
        ],
        invoiceDealFundingMethod: [{ name: currentPartner.name }],
      }),
    );

    yield put(
      projectCreateSlice.actions.setProjectFundingOptionsAPIStatusSuccess(),
    );
  } catch (e) {
    yield put(
      projectCreateSlice.actions.setProjectFundingOptionsAPIStatusFailed(),
    );
    yield call(
      vmErrorLog,
      e,
      'An API error occurred in project saga getting funding options action',
    );
  }
}

export function getFormattedProjectFundingMethods(projectFundingMethods) {
  const formattedProjectFundingMethods = projectFundingMethods.map(
    (projectFundingMethod) => {
      const formattedProjectFundingMethod = { ...projectFundingMethod };
      formattedProjectFundingMethod.label =
        PROJECT_CREATE.PROJECT_FUNDING_METHODS[projectFundingMethod.name].label;
      return formattedProjectFundingMethod;
    },
  );

  return formattedProjectFundingMethods;
}

export function* setNewCurrentPartner(partnerId) {
  const currentPartner = yield select(getCurrentPartner);
  let completePartner = currentPartner;
  if (currentPartner.id !== partnerId) {
    const partnerFromCreateForm = yield call(getPartnerAPICall, partnerId);
    completePartner = yield call(getPartner, partnerFromCreateForm.data);
    yield put(
      partnerSlice.actions.updateCurrentPartner({
        newPartner: completePartner,
      }),
    );
  }

  return completePartner;
}

export function* getAndFormatVariationTypes() {
  const variationTypesAPIResponse = yield call(getVariationsAPICall);
  const variationTypesData = variationTypesAPIResponse.data.filter(
    (variationType) => {
      return variationType.active;
    },
  );
  const variationTypes = variationTypesData
    .map((variationType) => {
      const variationTypeCopyChanges = { ...variationType };
      variationTypeCopyChanges.label =
        PROJECT_CREATE.VARIATION_TYPES_OPTIONS[variationType.identifier].label;
      variationTypeCopyChanges.description =
        PROJECT_CREATE.VARIATION_TYPES_OPTIONS[
          variationType.identifier
        ].description;
      return variationTypeCopyChanges;
    })
    .reverse();
  return variationTypes;
}

export function* getOutputMediaVariationTypes() {
  const variationTypesAPIResponse = yield call(getOutputMediaVariationsAPICall);
  const variationTypesData = variationTypesAPIResponse.data.filter(
    (variationType) => {
      return variationType;
    },
  );

  const variationTypes = variationTypesData
    .map((variationType) => {
      const variationTypeCopyChanges = { ...variationType };
      variationTypeCopyChanges.label =
        OUTPUT_VARIATION_TYPES_OPTIONS[variationType.media_type][
          variationType.variation_type
        ].label;
      variationTypeCopyChanges.description =
        OUTPUT_VARIATION_TYPES_OPTIONS[variationType.media_type][
          variationType.variation_type
        ].description;

      return variationTypeCopyChanges;
    })
    .reverse();
  return variationTypes;
}

export function* linkInsightsToProject(projectId) {
  const insightsToAdd = yield select(getInsightsToCreateProject);
  if (insightsToAdd && insightsToAdd.length > 0) {
    const organizationId = yield select(getOrganizationId);
    if (!organizationId) {
      yield call(
        vmErrorLog,
        new Error('Missing organizationId'),
        'Missing organizationId in linkInsightsToProject saga (v2)',
      );
    } else {
      try {
        yield call(
          BffService.handleBffApiPost,
          `/v1/organization/${organizationId}/project-insight/bulk`,
          {
            insightIds: insightsToAdd,
            projectIds: [projectId],
          },
        );
      } catch (error) {
        yield call(vmErrorLog, error, 'Error linking insight to project (v2)');
      }
    }
  }
}

export function* getCompleteProductWithFormats(productId) {
  const productAPIResponse = yield call(getProductAPICall, productId);
  const completeProduct = productAPIResponse.data;
  return completeProduct;
}

export function* changeProjectStatus(action) {
  const { toEdit } = action.payload;
  yield put(
    projectCreateSlice.actions.setProjectStatusAPIRequestStatusPending(),
  );

  try {
    const currentProjectBeingCreated = yield select(getProjectBeingCreated);
    let projectBeingCreated;

    if (toEdit) {
      const apiResponse = yield call(
        moveProjectToEditApiCall,
        currentProjectBeingCreated.id,
      );
      projectBeingCreated = {
        ...currentProjectBeingCreated,
        ...apiResponse.data,
      };
    }

    yield put(
      projectCreateSlice.actions.setProjectBeingCreated({
        projectBeingCreated,
      }),
    );
    yield put(
      projectCreateSlice.actions.setProjectStatusAPIRequestStatusSuccess(),
    );
  } catch (error) {
    if (error.responseErrorMessage) {
      displayToastAlert({
        message: error.responseErrorMessage,
        type: 'error',
      });
    }

    const projectStatusErrorMessage = error.responseErrorMessage.split(',');
    const filteredProjectStatusErrors = projectStatusErrorMessage.filter(
      (projectStatusError) =>
        Boolean(
          PROJECT_CREATE.PROJECT_STATUS_ERRORS[projectStatusError.trim(' ')],
        ) === true,
    );
    const intl = yield call(getIntl);
    const projectStatusErrors = filteredProjectStatusErrors.map(
      (projectStatusError) =>
        intl.messages[
          PROJECT_CREATE.PROJECT_STATUS_ERRORS[projectStatusError.trim(' ')]
        ],
    );

    yield put(
      projectCreateSlice.actions.setProjectStatusAPIErrors({
        projectStatusErrors,
      }),
    );
    yield put(
      projectCreateSlice.actions.setProjectStatusAPIRequestStatusFailed(),
    );
    yield call(
      vmErrorLog,
      error,
      `An API error occurred in project saga change project status ${toEdit ? 'to edit' : 'to build'}`,
    );
  }
}

export { watchProjectCreate };
