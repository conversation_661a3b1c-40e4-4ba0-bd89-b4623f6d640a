/**
 * This test is commented out and we will come back to it. See AGL-10154
 */

import { expectSaga } from 'redux-saga-test-plan';
import { call, select, put } from 'redux-saga/effects';
import * as matchers from 'redux-saga-test-plan/matchers';
import projectCreateSlice from '../../slices/projectCreate.slice';
import { throwError } from 'redux-saga-test-plan/providers';
import partnerSlice from '../../slices/partner.slice';
import { getPartner } from '../../../featureServices/partner/getUserPartners';
import { getProjectPermissionsForProject } from '../../../featureServices/permissions/ProjectPermissions';
import {
  getProjectBeingCreated,
  getProjectFinanceDetails as getProjectFinanceDetailsSelector,
} from '../../selectors/projectCreate.selectors';
import { mockFundingOptionsAPIResponse } from '../../../creativeProduction/testingResources/testData/MockFundingOptionsAPIResponse';
import { PROJECT_CREATE } from '../../../constants';
import { getIntl } from '../../../utils/getIntl';
import { getMockIntlProp } from '../../../../testResources/mockIntlProp';

import {
  watchCreateProjectAPIRequest,
  watchLoadProjectIntoState,
  createProjectAPICall,
  getProjectAPICall,
  getCurrentPartner,
  getAndFormatVariationTypes,
  getCompleteProductWithFormats,
  linkInsightsToProject,
  getProductAPICall,
  getPartnerAPICall,
  setNewCurrentPartner,
  getVariationsAPICall,
  getProjectFinanceDetails,
  getProjectFinanceAPICall,
  updateProjectFinanceAPICall,
  updateProjectFinanceDetails,
  getOutputGroupList,
  getOutputVideoList,
  getPartnerFundingOptionsAPICall,
  getProjectFundingOptionsAPICall,
  watchGetProjectFundingOptions,
  getFormattedProjectFundingMethods,
  watchGetPriceQuote,
  getProjectPriceQuote,
  watchUpdateProjectAPIRequest,
  updateProjectAPICall,
  watchChangeProjectStatus,
  moveProjectToEditApiCall,
  setInvoicePartnerOrFundingAccount,
  getOutputMediaVariationTypes,
} from './projectCreate.sagas';

import { productResponse } from '../../../creativeProduction/testingResources/testData/MockProductResponse';
import {
  variationTypesAPIResponse,
  variationTypesData,
} from '../../../creativeProduction/testingResources/testData/MockVariationTypes';

const financeDetailsObject = {
  status: 'OK',
  data: {
    fundingMethod: {
      name: 'INTERNAL_MARKETING',
    },
    fundingAccount: {
      id: 165,
    },
    externalSowIdentifier: '123456789012345678',
    projectPriceOverride: 0.0,
    flexCredits: 0.0,
    invoicePartnerId: null,
  },
};

const partnerFromCreateForm = {
  status: 'OK',
  data: {
    id: 30375,
    personal: false,
    logoUrl: null,
    name: 'Cowabunga Enterprise',
    industry: 'B2B',
    isEnterprise: true,
    publicAccountTypeName: null,
    accountTypeIdentifier: 'ENTERPRISE',
    notifyFavoriteEditorEnabled: false,
    snapIntegrationEnabled: false,
    draftDownloadEnabled: false,
  },
};

const completePartner = {
  accountTypeIdentifier: 'ENTERPRISE',
  color: '#00d789',
  featureList: {
    'INVITE-INTERNAL-CREATORS': false,
    'ANALYTICS-API': false,
    'CREATIVE-INTELLIGENCE': false,
  },
  hasAdminRole: true,
  hasManagerRole: false,
  id: 30448,
  industry: 'Alcohol - Beer',
  isEnterprise: true,
  isFindMyTeamEnabled: false,
  isPersonal: false,
  logoUrl: null,
  name: 'whoa enterprise',
  publicAccountTypeName: null,
};
describe('The Project Update Forms Update and Create a Project', () => {
  const projectCreateDataAction = {
    projectCreateData: {
      name: 'Mr Darcy',
      productId: 72,
    },
    partnerId: 1234,
  };

  const mockProjectApiResponse = {
    data: {
      id: 20624,
      name: 'Mr Darcy',
      description: null,
      totalOutputVideos: 1,
      daysToDeliver: 2,
      productType: 'CREATIVE',
      outputGroupType: 'HERO_AND_VARIATION',
      launchDate: '2022-01-21T20:00:00Z',
      productId: 72,
      expectedDate: '2022-01-23T00:00:00.000Z',
      totalIterations: 1,
      user: { id: 1234 },
    },
  };

  const mockEnterprisePartnerForLoading = {
    id: 654,
    accountTypeIdentifier: 'ENTERPRISE',
  };

  const mockProjectApiResponseForLoading = {
    data: {
      id: 987,
      name: 'Test Loading',
      description: null,
      totalOutputVideos: 1,
      daysToDeliver: 2,
      productType: 'CREATIVE',
      outputGroupType: 'HERO_AND_VARIATION',
      launchDate: '2022-01-21T20:00:00Z',
      productId: 72,
      expectedDate: '2022-01-23T00:00:00.000Z',
      totalIterations: 1,
      partner: mockEnterprisePartnerForLoading,
      permissions: [
        {
          description:
            'Read project details, such as name, description, notes.',
          id: 2,
          resource: 'project',
          subresource: 'details',
          type: 'read',
        },
      ],
    },
  };

  const mockLoadedEnterpriseProject = {
    id: 987,
    name: 'Test Loading',
    description: null,
    totalOutputVideos: 1,
    daysToDeliver: 2,
    productType: 'CREATIVE',
    outputGroupType: 'HERO_AND_VARIATION',
    launchDate: '2022-01-21T20:00:00Z',
    productId: 72,
    expectedDate: '2022-01-23T00:00:00.000Z',
    totalIterations: 1,
    partner: { id: 654, accountTypeIdentifier: 'ENTERPRISE' },
    product: {},
    variationTypes: [],
    permissions: ['project.details.read'],
  };

  const mockProjectBeingCreated = {
    id: 20624,
    name: 'Mr Darcy',
    description: null,
    totalOutputVideos: 1,
    daysToDeliver: 2,
    productType: 'CREATIVE',
    outputGroupType: 'HERO_AND_VARIATION',
    launchDate: '2022-01-21T20:00:00Z',
    productId: 72,
    expectedDate: '2022-01-23T00:00:00.000Z',
    totalIterations: 1,
    outputGroupsData: [],
    product: {},
    variationTypes: [],
    user: { id: 1234 },
  };

  const mockProjectUpdateAction = {
    projectDetailType: 'test',
    projectUpdates: { name: 'Kiki' },
  };

  const mockUpdatedProject = {
    data: {
      id: 20624,
      name: 'Kiki',
      description: null,
      totalOutputVideos: 1,
      daysToDeliver: 2,
      productType: 'CREATIVE',
      outputGroupType: 'HERO_AND_VARIATION',
      launchDate: '2022-01-21T20:00:00Z',
      productId: 72,
      expectedDate: '2022-01-23T00:00:00.000Z',
      totalIterations: 1,
      outputGroupsData: [],
      product: { identifier: 'TEST' },
      variationTypes: [],
      user: { id: 1234 },
    },
  };

  it('creates a project', () => {
    const product = {};
    const variationTypes = [];
    return expectSaga(
      watchCreateProjectAPIRequest,
      projectCreateSlice.actions.createProjectAPIRequest,
    )
      .provide([
        [select(getCurrentPartner), { id: 1234 }],
        [matchers.call.fn(createProjectAPICall), mockProjectApiResponse],
        [matchers.call.fn(getOutputMediaVariationTypes), variationTypes],
        [matchers.call.fn(getCompleteProductWithFormats), product],
        [matchers.call.fn(linkInsightsToProject)],
      ])
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusPending())
      .put(
        projectCreateSlice.actions.setProjectBeingCreated({
          projectBeingCreated: mockProjectBeingCreated,
        }),
      )
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusSuccess())
      .dispatch(
        projectCreateSlice.actions.createProjectAPIRequest(
          projectCreateDataAction,
        ),
      )
      .run();
  });

  it('creates a project with finance details', () => {
    const product = {};
    const variationTypes = [];
    const financeDetails = { externalSowIdentifier: '123456789123456789' };
    return expectSaga(
      watchCreateProjectAPIRequest,
      projectCreateSlice.actions.createProjectAPIRequest,
    )
      .provide([
        [select(getCurrentPartner), { id: 1234 }],
        [matchers.call.fn(createProjectAPICall), mockProjectApiResponse],
        [matchers.call.fn(getOutputMediaVariationTypes), variationTypes],
        [matchers.call.fn(getCompleteProductWithFormats), product],
        [matchers.call.fn(linkInsightsToProject)],
        [matchers.call.fn(updateProjectFinanceDetails)],
      ])
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusPending())
      .put(
        projectCreateSlice.actions.setProjectBeingCreated({
          projectBeingCreated: mockProjectBeingCreated,
        }),
      )
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusSuccess())
      .dispatch(
        projectCreateSlice.actions.createProjectAPIRequest({
          ...projectCreateDataAction,
          financeDetails,
        }),
      )
      .run();
  });

  it('catches errors when creating a project', () => {
    const error = new Error();

    return expectSaga(
      watchCreateProjectAPIRequest,
      projectCreateSlice.actions.createProjectAPIRequest,
    )
      .provide([
        [select(getCurrentPartner), { id: 1234 }],
        [matchers.call.fn(createProjectAPICall), throwError(error)],
        [matchers.call.fn(getAndFormatVariationTypes)],
        [matchers.call.fn(getCompleteProductWithFormats)],
        [matchers.call.fn(linkInsightsToProject)],
      ])
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusPending())
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusFailed())
      .dispatch(
        projectCreateSlice.actions.createProjectAPIRequest(
          projectCreateDataAction,
        ),
      )
      .run();
  });

  it('loads in a ENTERPRISE project to be edited', () => {
    const projectId = 987;
    const product = {};
    const variationTypes = [];
    const outputGroupList = {};
    const outputVideoList = {};
    const permissionsAsShape = ['project.details.read'];

    return expectSaga(
      watchLoadProjectIntoState,
      projectCreateSlice.actions.loadProjectIntoState,
    )
      .provide([
        [matchers.call.fn(getProjectAPICall), mockProjectApiResponseForLoading],
        [matchers.call.fn(setNewCurrentPartner), { id: 654 }],
        [matchers.call.fn(getCompleteProductWithFormats), product],
        [matchers.call.fn(getOutputMediaVariationTypes), variationTypes],
        [matchers.call.fn(getOutputGroupList), outputGroupList],
        [matchers.call.fn(getOutputVideoList), outputVideoList],
        [matchers.call.fn(getProjectPermissionsForProject), permissionsAsShape],
      ])
      .put(
        projectCreateSlice.actions.setProjectLoadingAPIRequestStatusPending(),
      )
      .put(
        projectCreateSlice.actions.setProjectBeingCreated({
          projectBeingCreated: mockLoadedEnterpriseProject,
        }),
      )
      .put(
        projectCreateSlice.actions.setProjectLoadingAPIRequestStatusSuccess(),
      )
      .dispatch(projectCreateSlice.actions.loadProjectIntoState({ projectId }))
      .run();
  });

  it('updates a project', () => {
    return expectSaga(
      watchUpdateProjectAPIRequest,
      projectCreateSlice.actions.updateProjectAPIRequest,
    )
      .provide([
        [matchers.call.fn(updateProjectAPICall), mockUpdatedProject],
        [select(getProjectBeingCreated), mockProjectBeingCreated],
        [select(getProjectFinanceDetailsSelector), financeDetailsObject],
      ])
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusPending())
      .put(
        projectCreateSlice.actions.setProjectBeingCreated({
          projectBeingCreated: mockUpdatedProject.data,
        }),
      )
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusSuccess())
      .dispatch(
        projectCreateSlice.actions.updateProjectAPIRequest(
          mockProjectUpdateAction,
        ),
      )
      .run();
  });

  it('errors if update fails', () => {
    const error = new Error({ responseErrorCode: 'an error occurred' });
    return expectSaga(
      watchUpdateProjectAPIRequest,
      projectCreateSlice.actions.updateProjectAPIRequest,
    )
      .provide([
        [matchers.call.fn(updateProjectAPICall), throwError(error)],
        [select(getProjectBeingCreated), mockProjectBeingCreated],
      ])
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusPending())
      .put(
        projectCreateSlice.actions.setProjectAPIErrorCode({
          errorCode: error.responseErrorCode,
        }),
      )
      .put(projectCreateSlice.actions.setProjectAPIRequestStatusFailed())
      .dispatch(
        projectCreateSlice.actions.updateProjectAPIRequest(
          mockProjectUpdateAction,
        ),
      )
      .run();
  });
});

describe('The product API returns a product', () => {
  it('gets the complete product with formats', () => {
    const productId = 72;
    const completeProductWithFormats = getCompleteProductWithFormats(productId);
    const apiProductCall = completeProductWithFormats.next().value;
    expect(apiProductCall).toEqual(call(getProductAPICall, productId));
    const productResult =
      completeProductWithFormats.next(productResponse).value;
    expect(productResult).toEqual(productResponse.data);
    expect(completeProductWithFormats.next().done).toEqual(true);
  });
});

describe('The setNewCurrentPartner function', () => {
  let newCurrentPartnerFunction;
  const partnerId = 30448;

  beforeEach(() => {
    newCurrentPartnerFunction = setNewCurrentPartner(partnerId);
  });
  it('changes the partner in redux if the current partner is not the project partner', () => {
    const currentPartner = { id: 24074 };
    expect(newCurrentPartnerFunction.next().value).toEqual(
      select(getCurrentPartner),
    );
    expect(newCurrentPartnerFunction.next(currentPartner).value).toEqual(
      call(getPartnerAPICall, partnerId),
    );
    expect(newCurrentPartnerFunction.next(partnerFromCreateForm).value).toEqual(
      call(getPartner, partnerFromCreateForm.data),
    );
    expect(newCurrentPartnerFunction.next(completePartner).value).toEqual(
      put(
        partnerSlice.actions.updateCurrentPartner({
          newPartner: completePartner,
        }),
      ),
    );
    expect(newCurrentPartnerFunction.next().done).toEqual(true);
  });

  it('does not change the partner in redux if the current partner is the project partner', () => {
    const currentPartner = { id: 30448 };
    expect(newCurrentPartnerFunction.next().value).toEqual(
      select(getCurrentPartner),
    );
    expect(newCurrentPartnerFunction.next(currentPartner).done).toEqual(true);
  });
});

describe('The getAndFormatVariationTypes function', () => {
  it('gets variations and formats them appropriately', () => {
    const formatVariationTypes = getAndFormatVariationTypes();
    expect(formatVariationTypes.next().value).toEqual(
      call(getVariationsAPICall),
    );
    expect(formatVariationTypes.next(variationTypesAPIResponse).value).toEqual(
      variationTypesData,
    );
    expect(formatVariationTypes.next().done).toEqual(true);
  });
});

describe('The price API returns a price quote', () => {
  const priceQuoteRequest = {
    data: 4500,
  };

  const currentProject = {
    id: 20624,
    name: 'Ms Tina',
    description: null,
    totalOutputVideos: 1,
    daysToDeliver: 2,
    outputGroupType: 'HERO_AND_VARIATION',
    outputGroupsData: [
      {
        outputVideos: [
          {
            formatId: 1,
            outputType: 'HERO',
          },
        ],
      },
    ],
  };

  const partnerId = 29926;

  it('updates the price', () => {
    return expectSaga(
      watchGetPriceQuote,
      projectCreateSlice.actions.getPriceQuote,
    )
      .provide([[matchers.call.fn(getProjectPriceQuote), priceQuoteRequest]])
      .put(projectCreateSlice.actions.setPriceAPIRequestStatusPending())
      .put(
        projectCreateSlice.actions.setPriceQuote({
          priceQuote: priceQuoteRequest.data,
        }),
      )
      .put(projectCreateSlice.actions.setPriceAPIRequestStatusSuccess())
      .dispatch(
        projectCreateSlice.actions.getPriceQuote({
          outputGroupsData: currentProject.outputGroupsData,
          partnerId,
        }),
      )
      .run();
  });

  it('errors if the price quote api fails', () => {
    const error = new Error({ responseErrorCode: 'An error occurred' });
    return expectSaga(
      watchGetPriceQuote,
      projectCreateSlice.actions.getPriceQuote,
    )
      .provide([[matchers.call.fn(getProjectPriceQuote), throwError(error)]])
      .put(projectCreateSlice.actions.setPriceAPIRequestStatusPending())
      .put(
        projectCreateSlice.actions.setPriceAPIErrorCode({
          priceErrorCode: error.responseErrorCode,
        }),
      )
      .put(projectCreateSlice.actions.setPriceAPIRequestStatusFailed())
      .dispatch(
        projectCreateSlice.actions.getPriceQuote({
          outputGroupsData: currentProject.outputGroupsData,
          partnerId,
        }),
      )
      .run();
  });
});

describe('the getProjectFinanceDetails function', () => {
  it('gets and adds finance fields to redux', () => {
    const projectId = 12345;
    const steps = ['admin-project-details'];
    const financeDetails = getProjectFinanceDetails(projectId, steps);
    const projectFinanceInfoResponse = financeDetails.next(projectId).value;
    expect(projectFinanceInfoResponse).toEqual(
      call(getProjectFinanceAPICall, projectId),
    );
    financeDetails.next(financeDetailsObject);
    expect(financeDetails.next().done).toEqual(true);
    const partnerOrFundingAccount = setInvoicePartnerOrFundingAccount(
      financeDetailsObject.data,
    ).next().value;
    expect(partnerOrFundingAccount).toEqual(
      put(
        projectCreateSlice.actions.setProjectFinanceDetails({
          ...financeDetailsObject.data,
          fundingMethod: financeDetailsObject.data.fundingMethod.name,
          fundingAccount: financeDetailsObject.data.fundingAccount.id,
        }),
      ),
    );
  });
});

describe('the updateProjectFinanceDetails function', () => {
  it('updates and adds finance fields to redux', () => {
    const projectId = 12345;
    const financeUpdates = { externalSowIdentifier: '123456789123456789' };
    financeDetailsObject.data.fundingMethod.name = 'CUSTOM_INVOICE_PARTNER';
    financeDetailsObject.data.partnerInvoiceId = 31262;
    financeDetailsObject.data.fundingAccount = null;
    const financeUpdateDetails = updateProjectFinanceDetails(
      financeUpdates,
      projectId,
    );
    const projectFinanceInfoResponse = financeUpdateDetails.next(
      financeUpdates,
      projectId,
    ).value;
    expect(projectFinanceInfoResponse).toEqual(
      call(updateProjectFinanceAPICall, financeUpdates, projectId),
    );
    financeUpdateDetails.next(financeDetailsObject);
    expect(financeUpdateDetails.next().done).toEqual(true);
    const partnerOrFundingAccount = setInvoicePartnerOrFundingAccount(
      financeDetailsObject.data,
    ).next().value;
    expect(partnerOrFundingAccount).toEqual(
      put(
        projectCreateSlice.actions.setProjectFinanceDetails({
          ...financeDetailsObject.data,
          fundingMethod: financeDetailsObject.data.fundingMethod.name,
          partnerInvoiceId: financeDetailsObject.data.partnerInvoiceId,
        }),
      ),
    );
  });
});

const mockProjectFundingSourceApiResponseData = {
  data: {
    ...mockFundingOptionsAPIResponse,
  },
};

describe('the getFundingOptions function', () => {
  const formattedProjectFundingMethods = getFormattedProjectFundingMethods(
    mockProjectFundingSourceApiResponseData.data.fundingMethods,
  );
  const mockIntlProp = getMockIntlProp();
  const currentPartner = { name: 'Partner Name' };

  it('adds funding options to redux by project id', () => {
    return expectSaga(
      watchGetProjectFundingOptions,
      projectCreateSlice.actions.getProjectFundingOptions,
    )
      .provide([
        [
          matchers.call.fn(getProjectFundingOptionsAPICall),
          mockProjectFundingSourceApiResponseData,
        ],
        [matchers.call.fn(getFormattedProjectFundingMethods)],
        [matchers.call.fn(getIntl), mockIntlProp],
        [select(getCurrentPartner), currentPartner],
      ])
      .put(
        projectCreateSlice.actions.setProjectFundingOptionsAPIStatusPending(),
      )
      .put(
        projectCreateSlice.actions.setFundingOptions({
          ...mockProjectFundingSourceApiResponseData.data,
          fundingMethods: formattedProjectFundingMethods,
          prepaidFundingMethod: [
            {
              name: mockIntlProp.messages[
                PROJECT_CREATE.PROJECT_FUNDING_METHODS.PREPAID.label
              ],
            },
          ],
          invoiceDealFundingMethod: [{ name: currentPartner.name }],
        }),
      )
      .put(
        projectCreateSlice.actions.setProjectFundingOptionsAPIStatusSuccess(),
      )
      .dispatch(
        projectCreateSlice.actions.getProjectFundingOptions({
          projectId: 24321,
        }),
      )
      .run();
  });

  it('adds funding options to redux by partner id', () => {
    return expectSaga(
      watchGetProjectFundingOptions,
      projectCreateSlice.actions.getProjectFundingOptions,
    )
      .provide([
        [
          matchers.call.fn(getPartnerFundingOptionsAPICall),
          mockProjectFundingSourceApiResponseData,
        ],
        [matchers.call.fn(getFormattedProjectFundingMethods)],
        [matchers.call.fn(getIntl), mockIntlProp],
        [select(getCurrentPartner), currentPartner],
      ])
      .put(
        projectCreateSlice.actions.setProjectFundingOptionsAPIStatusPending(),
      )
      .put(
        projectCreateSlice.actions.setFundingOptions({
          ...mockProjectFundingSourceApiResponseData.data,
          fundingMethods: formattedProjectFundingMethods,
          prepaidFundingMethod: [
            {
              name: mockIntlProp.messages[
                PROJECT_CREATE.PROJECT_FUNDING_METHODS.PREPAID.label
              ],
            },
          ],
          invoiceDealFundingMethod: [{ name: currentPartner.name }],
        }),
      )
      .put(
        projectCreateSlice.actions.setProjectFundingOptionsAPIStatusSuccess(),
      )
      .dispatch(
        projectCreateSlice.actions.getProjectFundingOptions({
          partnerId: 1234,
        }),
      )
      .run();
  });

  it('catches errors when getting funding options', () => {
    const error = new Error();
    return expectSaga(
      watchGetProjectFundingOptions,
      projectCreateSlice.actions.getProjectFundingOptions,
    )
      .provide([
        [matchers.call.fn(getProjectFundingOptionsAPICall), throwError(error)],
      ])
      .put(
        projectCreateSlice.actions.setProjectFundingOptionsAPIStatusPending(),
      )
      .put(projectCreateSlice.actions.setProjectFundingOptionsAPIStatusFailed())
      .dispatch(
        projectCreateSlice.actions.getProjectFundingOptions({
          projectId: 1234,
        }),
      )
      .run();
  });

  describe('the changeProjectStatus function', () => {
    const mockProjectBeingCreated = {
      id: 20624,
      status: 0,
    };

    const mockProjectBeingCreatedApiResponseData = {
      data: {
        id: 20624,
        status: 2,
      },
    };
    it('changes project status from building to editing', () => {
      return expectSaga(
        watchChangeProjectStatus,
        projectCreateSlice.actions.changeProjectStatus,
      )
        .provide([
          [
            matchers.call.fn(moveProjectToEditApiCall),
            mockProjectBeingCreatedApiResponseData,
          ],
          [matchers.call.fn(getIntl), mockIntlProp],
          [select(getProjectBeingCreated), mockProjectBeingCreated],
        ])
        .put(
          projectCreateSlice.actions.setProjectStatusAPIRequestStatusPending(),
        )
        .put(
          projectCreateSlice.actions.setProjectBeingCreated({
            projectBeingCreated: {
              ...mockProjectBeingCreated,
              ...mockProjectBeingCreatedApiResponseData.data,
            },
          }),
        )
        .put(
          projectCreateSlice.actions.setProjectStatusAPIRequestStatusSuccess(),
        )
        .dispatch(
          projectCreateSlice.actions.changeProjectStatus({ toEdit: true }),
        )
        .run();
    });

    it('throws errors when moving project status from building to editing and criteria are not met', () => {
      const mockError = {
        responseErrorMessage:
          'Project is missing brief, Project is missing assets, Project is missing output videos,' +
          'Project is missing creators,Project is missing a creative director,Project SOW is invalid,Project is missing price, ,Project price is more than remains on its SOW',
      };
      return expectSaga(
        watchChangeProjectStatus,
        projectCreateSlice.actions.changeProjectStatus,
      )
        .provide([
          [matchers.call.fn(moveProjectToEditApiCall), throwError(mockError)],
          [matchers.call.fn(getIntl), mockIntlProp],
          [select(getProjectBeingCreated), mockProjectBeingCreated],
        ])
        .put(
          projectCreateSlice.actions.setProjectStatusAPIRequestStatusPending(),
        )
        .put(
          projectCreateSlice.actions.setProjectStatusAPIRequestStatusFailed(),
        )
        .dispatch(
          projectCreateSlice.actions.changeProjectStatus({ toEdit: true }),
        )
        .run();
    });
  });
});
