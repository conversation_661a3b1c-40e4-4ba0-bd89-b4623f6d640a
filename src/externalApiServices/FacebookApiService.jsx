/**
 * Facebook API service. All methods return a promise
 */
import axios from 'axios';
const { FB } = window;

/**
 * Returns a promise to let you know user has logged in.
 *
 * @returns {Promise<any>}
 */
export const logInFacebookUser = () =>
  new Promise((resolve, reject) => {
    FB.getLoginStatus((response) => {
      if (response.status === 'connected') {
        resolve(response);
      } else {
        FB.login((response) => {
          if (!response || response.error) {
            reject(response);
          } else {
            resolve(response);
          }
        });
      }
    });
  });

/**
 * Returns a promise to get the user ID for publishing
 *
 * @returns {Promise<any>}
 */
export const getUserId = () =>
  new Promise((resolve, reject) => {
    FB.api(
      '/me',
      {
        fields: 'id',
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to get the list of ad accounts for the active user
 *
 * @returns {Promise<any>}
 */
export const getUserAdAccounts = () =>
  new Promise((resolve, reject) => {
    FB.api(
      '/me/adaccounts',
      {
        fields: 'name',
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to get the list of pages the active user has a role on
 *
 * @returns {Promise<any>}
 */
export const getUserPages = () =>
  new Promise((resolve, reject) => {
    FB.api(
      '/me',
      {
        fields: 'accounts{can_post,name}',
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to get the first page of video assets for an ad account
 *
 * @param accountId
 * @param afterCursor
 * @returns {Promise<any>}
 */
export const getAdAccountVideos = (accountId, afterCursor) =>
  new Promise((resolve, reject) => {
    FB.api(
      `/${accountId}/advideos`,
      {
        fields: 'description,title,id,picture,source,length,created_time',
        after: afterCursor,
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to get the first page of image assets for an ad account
 *
 * @param accountId
 * @param afterCursor
 * @returns {Promise<any>}
 */
export const getAdAccountImages = (accountId, afterCursor) =>
  new Promise((resolve, reject) => {
    FB.api(
      `/${accountId}/adimages`,
      {
        fields: 'name,id,url,height,width,created_time',
        after: afterCursor,
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to the next page of video or image ad account assets.
 *
 * @param nextPageUrl
 * @returns {AxiosPromise<any>}
 */
export const getAdAccountNextPage = (nextPageUrl) => axios.get(nextPageUrl);

/**
 * Returns a promise to get the first page of user uploaded videos
 *
 * @param accountId
 * @param afterCursor
 * @returns {Promise<any>}
 */
export const getUserVideos = (afterCursor) =>
  new Promise((resolve, reject) => {
    FB.api(
      '/me/videos/uploaded',
      {
        fields: 'icon,description,length,picture,source,created_time',
        after: afterCursor,
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to get the first page of user photo albums
 *
 * @param afterCursor
 * @returns {Promise<any>}
 */
export const getUserAlbums = (afterCursor) =>
  new Promise((resolve, reject) => {
    FB.api(
      '/me/albums',
      {
        fields: 'id,cover_photo,name,count',
        after: afterCursor,
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to get the first page up photos from an album or root if no album specified
 *
 * @param albumId
 * @param afterCursor
 * @returns {Promise<any>}
 */
export const getUserPhotos = (albumId, afterCursor) => {
  const endpoint = albumId ? `/${albumId}/photos` : '/me/photos';
  return new Promise((resolve, reject) => {
    FB.api(
      endpoint,
      {
        after: afterCursor,
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });
};

/**
 * Returns a promise to get the first page up photos from an album or root if no album specified
 *
 * @param albumId
 * @param afterCursor
 * @returns {Promise<any>}
 */
export const getUserPhotoInfo = (photoId) =>
  new Promise((resolve, reject) => {
    FB.api(
      `/${photoId}`,
      {
        fields: 'id,album,icon,name,picture,created_time,images',
      },
      (response) => {
        if (!response || response.error) {
          reject(response);
        } else {
          resolve(response);
        }
      },
    );
  });

/**
 * Returns a promise to the next page of video or image media assets.
 *
 * @param nextPageUrl
 * @returns {AxiosPromise<any>}
 */
export const getMediaAssetsNextPage = (nextPageUrl) => axios.get(nextPageUrl);
