import React, { useState, useRef } from 'react';
import {
  Button,
  Box,
  Typography,
  Popper,
  Grow,
  Paper,
  ClickAwayListener,
  MenuList,
  useTheme,
} from '@mui/material';
import {
  CaretDownIcon,
  CaretUpIcon,
} from '../../../../../../assets/vidmob-mui-icons/general';
import { AD_ACCOUNT_HEALTH_DASHBOARD } from '../../../../../../constants';
import { getIntl } from '../../../../../../utils/getIntl';
import { createLabelAsDropdownMenuItem } from './createLabelAsDropdownMenuItem';
import {
  AdAccountHealthDashboardPopover,
  AdAccountHealthDashboardPopoverContent,
} from '../../AdAccountHealthDashboardPopover';
import { IdAndName } from '../../../../../../types/common.types';
import { Brand } from '../../../../../../types/brand.types';
import { Market } from '../../../../../../types/market.types';
import { Industry } from '../../../../../../types/industry.types';

export interface LabelAsDropdownProps {
  brands: Brand[] | null;
  markets: Market[] | null;
  selectedBrands: IdAndName[];
  selectedMarkets: IdAndName[];
  setSelectedBrands: (brands: IdAndName[]) => void;
  setSelectedMarkets: (markets: IdAndName[]) => void;
  handleUpdateAdAccountsWithBrands: (params: any) => void;
  handleUpdateAdAccountsWithMarkets: (params: any) => void;
  handleUpdateAdAccountsWithIndustryGroup: (
    industryGroups: Industry[] | null,
  ) => void;
  handleUpdateAdAccountsWithIndustry: (industries: Industry[] | null) => void;
  handleUpdateAdAccountsWithSubIndustry: (
    subIndustries: Industry[] | null,
  ) => void;
  selectedAccountsIds: string[];
  adAccounts: any[];
  industries: Industry[] | null;
  selectedIndustries: Industry[] | null;
  setSelectedIndustries: (industries: Industry[] | null) => void;
  industryGroups: Industry[] | null;
  selectedIndustryGroups: Industry[] | null;
  setSelectedIndustryGroups: (industryGroups: Industry[] | null) => void;
  subIndustries: Industry[] | null;
  selectedSubIndustries: Industry[] | null;
  setSelectedSubIndustries: (subIndustries: Industry[] | null) => void;
}

const LabelAsDropdown = ({
  brands,
  markets,
  selectedBrands,
  selectedMarkets,
  setSelectedBrands,
  setSelectedMarkets,
  handleUpdateAdAccountsWithBrands,
  handleUpdateAdAccountsWithMarkets,
  handleUpdateAdAccountsWithIndustryGroup,
  handleUpdateAdAccountsWithIndustry,
  handleUpdateAdAccountsWithSubIndustry,
  selectedAccountsIds,
  adAccounts,
  industries,
  selectedIndustries,
  setSelectedIndustries,
  industryGroups,
  selectedIndustryGroups,
  setSelectedIndustryGroups,
  subIndustries,
  selectedSubIndustries,
  setSelectedSubIndustries,
}: LabelAsDropdownProps) => {
  const [mainAnchorEl, setMainAnchorEl] = useState<HTMLElement | null>(null);
  const isMainOpen = Boolean(mainAnchorEl);
  const intl = getIntl();
  const wrapperRef = useRef<HTMLDivElement | null>(null);
  const theme = useTheme();

  const [openDropdownField, setOpenDropdownField] = useState<string | null>(
    null,
  );

  const handleMainClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMainAnchorEl(event.currentTarget);
    event.stopPropagation();
    event.preventDefault();
  };

  const handleSelectionClick = (
    selectedField: string,
    items: any[] | null,
    selectedItemsSetter: (items: any[]) => void,
    adAccountKey: string,
  ) => {
    setOpenDropdownField(selectedField);

    const selectionCounter: Record<string, number> = {};
    selectedAccountsIds.forEach((accountId) => {
      const adAccount = adAccounts.find(
        (acc) => acc.platformAccountId === accountId,
      );
      if (adAccount) {
        adAccount[adAccountKey].forEach((selectedItem: string) => {
          selectionCounter[selectedItem] =
            (selectionCounter[selectedItem] || 0) + 1;
        });
      }
    });

    const selectedItems = Object.keys(selectionCounter).map((name) => {
      const itemFromApi = items?.find((item: any) => item.name === name);
      return {
        name,
        id: itemFromApi?.id,
        partiallySelected:
          selectionCounter[name] !== selectedAccountsIds.length,
      };
    });

    selectedItemsSetter(selectedItems);
  };

  const handleIndustrySelectionClick = (
    selectedField: string,
    items: any[] | null,
    selectedItemsSetter: (items: any[]) => void,
    adAccountKey: string,
  ) => {
    setOpenDropdownField(selectedField);

    const selectedItems: any[] = [];
    selectedAccountsIds.forEach((accountId) => {
      const adAccount = adAccounts.find(
        (acc) => acc.platformAccountId === accountId,
      );
      if (adAccount && adAccount[adAccountKey]) {
        const selectedItem = adAccount[adAccountKey];
        const itemFromApi = items?.find(
          (item: any) => item.name === selectedItem.name,
        );
        if (
          itemFromApi &&
          !selectedItems.some((item) => item.name === selectedItem.name)
        ) {
          selectedItems.push({
            name: selectedItem.name,
            id: itemFromApi.id,
          });
        }
      }
    });

    selectedItemsSetter(selectedItems);
  };

  const handleBrandsClick = () =>
    handleSelectionClick(
      AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.BRANDS,
      brands,
      setSelectedBrands,
      'brands',
    );

  const handleMarketsClick = () =>
    handleSelectionClick(
      AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.MARKETS,
      markets,
      setSelectedMarkets,
      'markets',
    );

  const handleIndustryGroupsClick = () =>
    handleIndustrySelectionClick(
      AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.INDUSTRY_GROUP,
      industryGroups,
      setSelectedIndustryGroups,
      'industryGroup',
    );

  const handleIndustriesClick = () =>
    handleIndustrySelectionClick(
      AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.INDUSTRY,
      industries,
      setSelectedIndustries,
      'industry',
    );

  const handleSubIndustriesClick = () =>
    handleIndustrySelectionClick(
      AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.SUB_INDUSTRY,
      subIndustries,
      setSelectedSubIndustries,
      'subIndustry',
    );

  const dropdownMenuItems = [
    {
      label:
        'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs.brands',
      handler: handleBrandsClick,
      show: brands && brands.length > 0,
    },
    {
      label:
        'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs.markets',
      handler: handleMarketsClick,
      show: true,
    },
    {
      label:
        'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs.industryGroups',
      handler: handleIndustryGroupsClick,
      show: true,
    },
    {
      label:
        'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs.industries',
      handler: handleIndustriesClick,
      isDisabled:
        !selectedIndustryGroups || selectedIndustryGroups.length !== 1,
      show: true,
      tooltip:
        selectedIndustryGroups && selectedIndustryGroups.length > 1
          ? 'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs.industries.tooltip'
          : null,
    },
    {
      label:
        'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs.subIndustries',
      handler: handleSubIndustriesClick,
      isDisabled: !selectedIndustries || selectedIndustries.length !== 1,
      show: true,
      tooltip:
        selectedIndustryGroups && selectedIndustryGroups.length > 1
          ? 'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs.subIndustries.tooltip'
          : null,
    },
  ];

  const renderMenuItems = () =>
    dropdownMenuItems
      .filter((item) => item.show)
      .map(({ label, handler, isDisabled, tooltip }) =>
        createLabelAsDropdownMenuItem(label, handler, isDisabled, tooltip),
      );

  const modalContent = (
    <AdAccountHealthDashboardPopoverContent
      brands={brands}
      markets={markets}
      selectedBrands={selectedBrands}
      selectedMarkets={selectedMarkets}
      setSelectedBrands={setSelectedBrands}
      setSelectedMarkets={setSelectedMarkets}
      handleUpdateAdAccountsWithBrands={handleUpdateAdAccountsWithBrands}
      handleUpdateAdAccountsWithMarkets={handleUpdateAdAccountsWithMarkets}
      handleUpdateAdAccountsWithIndustryGroup={
        handleUpdateAdAccountsWithIndustryGroup
      }
      handleUpdateAdAccountsWithIndustry={handleUpdateAdAccountsWithIndustry}
      handleUpdateAdAccountsWithSubIndustry={
        handleUpdateAdAccountsWithSubIndustry
      }
      industries={industries}
      selectedIndustries={selectedIndustries}
      setSelectedIndustries={setSelectedIndustries}
      industryGroups={industryGroups}
      selectedIndustryGroups={selectedIndustryGroups}
      setSelectedIndustryGroups={setSelectedIndustryGroups}
      subIndustries={subIndustries}
      selectedSubIndustries={selectedSubIndustries}
      setSelectedSubIndustries={setSelectedSubIndustries}
      openDropdownField={openDropdownField}
      setOpenDropdownField={setOpenDropdownField}
      setMainAnchorEl={setMainAnchorEl}
    />
  );

  return (
    <Box>
      <Button
        variant="outlined"
        sx={{
          // marginLeft: '8px',
          color: isMainOpen ? 'primary.main' : 'text.primary',
          height: '36px',
          borderColor: isMainOpen ? 'transparent' : '#BDBDBD',
          backgroundColor: isMainOpen ? 'action.hover' : 'white',
          '&:hover': {
            borderColor: 'transparent',
            backgroundColor: 'action.hover',
            color: 'primary.main',
          },
        }}
        size="medium"
        onClick={handleMainClick}
        endIcon={isMainOpen ? <CaretUpIcon sx={{}} /> : <CaretDownIcon />}
      >
        <Typography variant="subtitle2">
          {intl.formatMessage({
            id: 'ui.adAccountHealthDashboard.dataGrid.tagAdAccounts.dropdown.labelAs',
          })}
        </Typography>
      </Button>
      <Popper
        modifiers={[
          {
            name: 'preventOverflow',
            enabled: true,
          },
        ]}
        open={isMainOpen}
        anchorEl={mainAnchorEl}
        placement="bottom-start"
        transition
        disablePortal
        keepMounted
        sx={{
          height: '88px',
          width: '220px',
          position: 'absolute',
          '& .MuiMenuItem-root:hover': {
            backgroundColor: '#EEEEEE',
            borderRadius: '6px',
          },
          zIndex: 10,
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              ref={wrapperRef}
              elevation={2}
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                maxHeight: '280px',
                width: '220px',
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setMainAnchorEl(null);
                  setOpenDropdownField(null);
                }}
              >
                <MenuList
                  sx={{
                    paddingRight: '8px !important',
                    paddingLeft: '8px !important',
                  }}
                  id="composition-menu"
                  aria-labelledby="composition-button"
                >
                  {renderMenuItems()}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
      <AdAccountHealthDashboardPopover
        anchorEl={wrapperRef.current}
        open={Boolean(openDropdownField)}
        placement="right-start"
        setOpenDropdownField={setOpenDropdownField}
        modalContent={modalContent}
      />
    </Box>
  );
};

export default LabelAsDropdown;
