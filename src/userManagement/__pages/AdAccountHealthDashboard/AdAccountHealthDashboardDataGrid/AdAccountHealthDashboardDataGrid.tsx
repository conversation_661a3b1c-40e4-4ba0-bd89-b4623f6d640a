import React, { useEffect, useState, FC } from 'react';
import { Box, Stack } from '@mui/material';
import {
  DataGridPro,
  GridColDef,
  GridRowSelectionModel,
} from '@mui/x-data-grid-pro';
import { getIntl } from '../../../../utils/getIntl';
import { adAccountHealthDashboardDataGridStyles } from '../../../utils/dataGridStyles';
import { useSelector } from 'react-redux';
import { GLOBALS, AD_ACCOUNT_HEALTH_DASHBOARD } from '../../../../constants';
import {
  getAdAccountsApiStatus,
  getAdAccounts,
  getAdAccountsPagination,
  getCreateBrandsToAdAccountsApiStatus,
  getCreateMarketsToAdAccountsApiStatus,
  getBrandsToAdAccountsApiStatus,
  getMarketsToAdAccountsApiStatus,
} from '../../../../redux/selectors/adAccountHealthDashboard.selectors';
import {
  getCurrentPartner,
  getCurrentPartnerPermissions,
  getOrganizationId,
} from '../../../../redux/selectors/partner.selectors';
import { getOrganizationPermissions } from '../../../redux/selectors/organization.selectors';
import {
  brandsSelector,
  countriesSelector,
} from '../../../redux/selectors/workspaces.selectors';
import history from '../../../../routing/history';
import routes from '../../../../routing/siteMapRoutes';
import { SEARCH_TERM_DEBOUNCE_DELAY_MS } from '../../../../constants/organization.constants';
import useDebounce from '../../../../hooks/useDebounce';
import {
  AdAccount,
  BrandsFromApi,
  ColumnVisibility,
  Props,
  Row,
} from './types';
import { styles } from '../styles';
import workspaceSlice from '../../../redux/slices/workspaces.slice';
import ControlBar from './ControlBar';
import SnackBar from './SnackBar';
import BrandsOrMarketsErrorBanner from './BrandsOrMarketsErrorBanner';
import { useAppDispatch } from '../../../../redux/store/appDispatch';
import {
  getInitialColumnVisibilities,
  getColumnVisibilityModel,
  updateColumnVisibilityAttributes,
  getImportStatusOptions,
} from './AdAccountHealthDashboardDataGridUtils';
import {
  useAdAccountSlots,
  useFetchAndSetBrandsMarkets,
  useGetColumns,
  useGetRows,
  useHandleApiStatuses,
  useShowAndHideSnackBar,
} from './AdAccountHealthDashboardDataGridHooks';
import {
  dispatchAddAccounts,
  handleUpdateMultipleAdAccountsWithBrands,
  handleUpdateMultipleAdAccountsWithMarkets,
  handleUpdateOneAdAccountWithBrands,
  handleUpdateOneAdAccountWithMarkets,
} from './AdAccountHealthDashboardDataGridActionDispatchers';
import './AdAccountHealthDashboard.scss';
import {
  AdAccountHealthDashboardPopover,
  AdAccountHealthDashboardPopoverContent,
} from './AdAccountHealthDashboardPopover';
import { IdAndName } from '../../../../types/common.types';
import {
  useGetIndustries,
  useGetIndustryGroups,
  useGetSubIndustries,
} from './AdAccountHealthDashboardDataGridHooks';
import { useDropdownHandler } from './AdAccountHealthDashboardDataGridHooks/useDropdownHandler';
import {
  useUpdateIndustry,
  useUpdateIndustryGroup,
  useUpdateSubIndustry,
} from './AdAccountHealthDashboardDataGridHooks/useUpdateIndustryData';
import { Brand } from '../../../../types/brand.types';
import { Market } from '../../../../types/market.types';
import { Industry } from '../../../../types/industry.types';
import FiltersControlBar from '../../../../muiCustomComponents/ControlBarFilters/ControlBar';
import {
  FilterSelectedValueType,
  LandingPageContext,
  LandingPageFilterId,
  LandingPageFilters,
  ValueType,
} from '../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';

const filters: LandingPageFilters = [
  {
    id: LandingPageFilterId.IMPORT_STATUS,
    name: 'Import status',
    type: ValueType.MULTI,
    showSearch: false,
  },
];

const AdAccountHealthDashboardDataGrid: FC<Props> = (props) => {
  const {
    dataGridRef,
    isExportTriggered,
    setSearchQuery,
    searchQuery,
    setIsSearchTriggered,
    isSearchTriggered,
    setIsViewByGroupTriggered,
    setHasError,
  } = props;
  const { PENDING, FAILED } = GLOBALS.REDUX_LOADING_STATUS;
  const intl = getIntl();
  const dispatch = useAppDispatch();
  const debouncedSearchTerm = useDebounce(
    searchQuery,
    SEARCH_TERM_DEBOUNCE_DELAY_MS,
  );
  const adAccountsPerPage = 100;

  const adAccounts = useSelector(getAdAccounts);
  const adAccountsApiStatus = useSelector(getAdAccountsApiStatus);
  const organizationId = useSelector(getOrganizationId);
  const currentPartner = useSelector(getCurrentPartner);
  const orgPermissions = useSelector((state: any) =>
    getOrganizationPermissions(state),
  );
  const partnerPermissions = useSelector(getCurrentPartnerPermissions);
  const adAccountsPagination = useSelector(getAdAccountsPagination);
  const createBrandsToAdAccountsApiStatus = useSelector(
    getCreateBrandsToAdAccountsApiStatus,
  );
  const createMarketsToAdAccountsApiStatus = useSelector(
    getCreateMarketsToAdAccountsApiStatus,
  );
  const brandsToAdAccountsApiStatus = useSelector(
    getBrandsToAdAccountsApiStatus,
  );
  const marketsToAdAccountsApiStatus = useSelector(
    getMarketsToAdAccountsApiStatus,
  );
  const brandsFromApi: BrandsFromApi = useSelector(
    brandsSelector,
  ) as BrandsFromApi;
  const marketsFromApi = useSelector(countriesSelector);

  const [slots, setSlots] = useState({});
  const [isAutoHeightEnabled, setIsAutoHeightEnabled] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [singlePlatformAccountId, setSinglePlatformAccountId] = useState<
    string | null
  >(null);
  const [openDropdownField, setOpenDropdownField] = useState<string | null>(
    null,
  );
  const [selectedMarkets, setSelectedMarkets] = useState<IdAndName[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<IdAndName[]>([]);
  const [selectedIndustryGroups, setSelectedIndustryGroups] = useState<
    Industry[] | null
  >(null);
  const [selectedIndustries, setSelectedIndustries] = useState<
    Industry[] | null
  >(null);
  const [selectedSubIndustries, setSelectedSubIndustries] = useState<
    Industry[] | null
  >(null);
  const [snackBarHasBeenOpenOnce, setSnackBarHasBeenOpenOnce] = useState(false);
  const [selectedAccountsIds, setSelectedAccountsIds] = useState<string[]>([]);
  const [markets, setMarkets] = useState<Market[] | null>(null);
  const [brands, setBrands] = useState<Brand[] | null>(null);
  const [industryGroups, setIndustryGroups] = useState<Industry[] | null>(null);
  const [industries, setIndustries] = useState<Industry[] | null>(null);
  const [subIndustries, setSubIndustries] = useState<Industry[] | null>(null);
  const [columnVisibilityAttributes, setColumnVisibilityAttributes] = useState<
    ColumnVisibility[]
  >([]);
  const [importStatus, setImportStatus] = useState('');
  const [viewByGroup, setViewByGroup] = useState<{
    key: string;
    value: string;
  } | null>(null);
  const [snackBarOpen, setSnackBarOpen] = useState(false);
  const [brandsOrMarketsErrorBannerOpen, setBrandsOrMarketsErrorBannerOpen] =
    useState(false);
  const [brandsOrMarketsErrorBannerLabel, setBrandsOrMarketsErrorBannerLabel] =
    useState('');
  const [sortModel, setSortModel] = useState<
    Array<{
      field: string;
      sort: 'asc' | 'desc';
    }>
  >([
    {
      field: 'channelColumn',
      sort: 'asc',
    },
  ]);
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: adAccountsPerPage,
  });

  const columns = useGetColumns({
    adAccountsApiStatus,
    brands,
    openDropdownField,
    singlePlatformAccountId,
    orgPermissions,
    intl,
  });

  const rows: Row[] = useGetRows(adAccountsApiStatus, adAccounts);

  const { data: industryGroupsFromApi } = useGetIndustryGroups(true);

  useEffect(() => {
    setIndustryGroups(industryGroupsFromApi);
  }, [industryGroupsFromApi]);

  const { data: industriesFromApi } = useGetIndustries(
    true,
    selectedIndustryGroups,
  );

  useEffect(() => {
    setIndustries(industriesFromApi);
  }, [industriesFromApi]);

  const { data: subIndustriesFromApi } = useGetSubIndustries(
    true,
    selectedIndustries,
  );

  useEffect(() => {
    setSubIndustries(subIndustriesFromApi);
  }, [subIndustriesFromApi]);

  const { mutate: updateIndustryGroup } = useUpdateIndustryGroup();

  const { mutate: updateIndustry } = useUpdateIndustry();

  const { mutate: updateSubIndustry } = useUpdateSubIndustry();

  useEffect(() => {
    const initialColumnVisibilities = getInitialColumnVisibilities(columns);
    setColumnVisibilityAttributes(initialColumnVisibilities);

    // Clean up when the component unmounts
    return () => {
      dispatch(workspaceSlice.actions.resetWorkspaceState());
    };
  }, []);

  useFetchAndSetBrandsMarkets({
    brandsFromApi,
    marketsFromApi,
    dispatch,
    setBrands,
    setMarkets,
  });

  const dispatchAddAccountsParams = {
    paginationModel,
    sortModel,
    isExportTriggered,
    adAccountsPagination,
    adAccountsPerPage,
    debouncedSearchTerm,
    viewByGroup,
    setIsViewByGroupTriggered,
    dispatch,
    currentPartner,
    organizationId,
    importStatus,
  };

  useHandleApiStatuses({
    brandsToAdAccountsApiStatus,
    createBrandsToAdAccountsApiStatus,
    createMarketsToAdAccountsApiStatus,
    currentPartner,
    dispatch,
    dispatchAddAccountsParams,
    marketsToAdAccountsApiStatus,
    organizationId,
    selectedAccountsIds,
    setBrandsOrMarketsErrorBannerLabel,
    setBrandsOrMarketsErrorBannerOpen,
    setSinglePlatformAccountId,
    singlePlatformAccountId,
  });

  useEffect(() => {
    dispatchAddAccounts(dispatchAddAccountsParams);
  }, [
    paginationModel,
    debouncedSearchTerm,
    sortModel,
    isExportTriggered,
    viewByGroup,
    importStatus,
  ]);

  const canSeeManageTab =
    partnerPermissions?.canAdminViewListAdAccounts?.() ||
    orgPermissions?.canOrgAdminViewListAdAccounts();

  useEffect(() => {
    if (!canSeeManageTab) {
      history.push(routes.organizationIntegrations);
    }
  }, [canSeeManageTab]);

  useEffect(() => {
    if (
      isSearchTriggered &&
      adAccountsApiStatus !== PENDING &&
      adAccounts.length > 0
    ) {
      setIsSearchTriggered(false);
    }
  }, [adAccountsApiStatus, isSearchTriggered, adAccounts]);

  useShowAndHideSnackBar({
    adAccountsApiStatus,
    snackBarHasBeenOpenOnce,
    isExportTriggered,
    setSnackBarOpen,
    setSnackBarHasBeenOpenOnce,
  });

  useEffect(() => {
    if (
      adAccountsApiStatus === FAILED ||
      brandsFromApi?.hasError ||
      marketsFromApi?.hasError
    ) {
      setHasError(true);
    }
  }, [adAccountsApiStatus, brandsFromApi, marketsFromApi]);

  useAdAccountSlots({
    adAccounts,
    adAccountsApiStatus,
    rows,
    searchQuery,
    setSlots,
    setIsAutoHeightEnabled,
    viewByGroup,
    importStatus,
  });

  const { handleCellClick } = useDropdownHandler({
    brands,
    markets,
    openDropdownField,
    setAnchorEl,
    setSinglePlatformAccountId,
    setOpenDropdownField,
    setSelectedBrands,
    setSelectedMarkets,
    setIndustryGroups,
    setSelectedIndustryGroups,
    setIndustries,
    setSelectedIndustries,
    setSubIndustries,
    setSelectedSubIndustries,
  });

  const handleSnackBarClose = () => {
    setSnackBarOpen(false);
  };

  const updateOneAdAccountWithBrandsParams = {
    currentPartner,
    dispatch,
    brands,
    openDropdownField,
    organizationId,
    rows,
    selectedBrands,
    setSelectedBrands,
    singlePlatformAccountId,
  };

  const updateOneAdAccountWithMarketsParams = {
    currentPartner,
    dispatch,
    markets,
    openDropdownField,
    organizationId,
    rows,
    selectedMarkets,
    setSelectedMarkets,
    singlePlatformAccountId,
  };

  const updateMultipleAdAccountsWithBrandsBaseParams = {
    adAccounts,
    brands,
    currentPartner,
    dispatch,
    organizationId,
    selectedAccountsIds,
    selectedBrands,
  };

  const updateMultipleAdAccountsWithMarketsBaseParams = {
    adAccounts,
    currentPartner,
    dispatch,
    markets,
    organizationId,
    selectedAccountsIds,
    selectedMarkets,
  };

  const handleRowSelectionModelChange = (
    rowSelectionModel: GridRowSelectionModel,
  ) => {
    const stringIds = rowSelectionModel.map((id) => id.toString());
    setSelectedAccountsIds(stringIds);
  };

  const findMatchingAdAccounts = (accountIds: string[]): AdAccount[] => {
    return adAccounts.filter((adAccount: AdAccount) =>
      accountIds.includes(adAccount.platformAccountId),
    );
  };

  const handleUpdateAdAccountsWithIndustryGroupForSelectedRows = (
    industryGroups: Industry[] | null,
  ) => {
    handleUpdateAdAccountsWithIndustryGroup(
      selectedAccountsIds,
      industryGroups,
    );
  };

  const handleUpdateAdAccountWithIndustryGroupInline = (
    industryGroups: Industry[] | null,
  ) => {
    if (singlePlatformAccountId) {
      handleUpdateAdAccountsWithIndustryGroup(
        [singlePlatformAccountId],
        industryGroups,
      );
    }
  };

  const handleUpdateAdAccountsWithIndustryGroup = (
    adAccountIdsToUpdate: string[],
    industryGroupsForUpdate: Industry[] | null,
  ) => {
    if (!industryGroupsForUpdate) return;

    if (industryGroupsForUpdate.length === 0) {
      clearIndustryGroups(adAccountIdsToUpdate);
    } else {
      addIndustryGroup(adAccountIdsToUpdate, industryGroupsForUpdate);
    }
  };

  const clearIndustryGroups = (adAccountIdsToUpdate: string[]) => {
    const selectedAdAccounts = findMatchingAdAccounts(adAccountIdsToUpdate);
    const originalIndustryGroupIds = selectedAdAccounts.reduce<number[]>(
      (acc, account) => {
        const industryGroupId = account.industryGroup?.id;
        if (industryGroupId) {
          acc.push(Number(industryGroupId));
        }
        return acc;
      },
      [],
    );

    if (originalIndustryGroupIds) {
      updateIndustryGroup(
        {
          organizationId: organizationId,
          industryData: {
            accountIds: adAccountIdsToUpdate,
            unselectedIndustryGroupIds: originalIndustryGroupIds,
          },
        },
        {
          onSuccess: () => {
            dispatchAddAccounts(dispatchAddAccountsParams);
          },
        },
      );
    }
  };

  const addIndustryGroup = (
    adAccountIdsToUpdate: string[],
    industryGroupsForUpdate: Industry[],
  ) => {
    updateIndustryGroup(
      {
        organizationId: organizationId,
        industryData: {
          accountIds: adAccountIdsToUpdate,
          selectedIndustryGroupId: industryGroupsForUpdate[0].id,
        },
      },
      {
        onSuccess: () => {
          dispatchAddAccounts(dispatchAddAccountsParams);
        },
      },
    );
  };

  const handleUpdateAdAccountsWithIndustryForSelectedRows = (
    industriesForUpdate: Industry[] | null,
  ) => {
    handleUpdateAdAccountsWithIndustry(
      selectedAccountsIds,
      industriesForUpdate,
    );
  };

  const handleUpdateAdAccountWithIndustryInline = (
    industriesForUpdate: Industry[] | null,
  ) => {
    if (singlePlatformAccountId) {
      handleUpdateAdAccountsWithIndustry(
        [singlePlatformAccountId],
        industriesForUpdate,
      );
    }
  };

  const handleUpdateAdAccountsWithIndustry = (
    adAccountIdsToUpdate: string[],
    industriesForUpdate: Industry[] | null,
  ) => {
    if (!industriesForUpdate) return;
    const selectedAdAccounts = findMatchingAdAccounts(adAccountIdsToUpdate);
    const industryGroupId = selectedAdAccounts?.[0].industryGroup.id;

    if (industriesForUpdate.length === 0) {
      clearIndustries(
        adAccountIdsToUpdate,
        selectedAdAccounts,
        industryGroupId,
      );
    } else {
      addIndustry(adAccountIdsToUpdate, industriesForUpdate, industryGroupId);
    }
  };

  const clearIndustries = (
    adAccountIdsToUpdate: string[],
    selectedAdAccounts: AdAccount[],
    industryGroupId: number,
  ) => {
    const originalIndustryIds = selectedAdAccounts.reduce<number[]>(
      (acc, account) => {
        const industryId = account.industry?.id;
        if (industryId) {
          acc.push(Number(industryId));
        }
        return acc;
      },
      [],
    );

    if (originalIndustryIds) {
      updateIndustry(
        {
          organizationId: organizationId,
          industryGroupId: industryGroupId,
          industryData: {
            accountIds: adAccountIdsToUpdate,
            unselectedIndustryIds: originalIndustryIds,
          },
        },
        {
          onSuccess: () => {
            dispatchAddAccounts(dispatchAddAccountsParams);
          },
        },
      );
    }
  };

  const addIndustry = (
    adAccountIdsToUpdate: string[],
    industriesForUpdate: Industry[],
    industryGroupId: number,
  ) => {
    updateIndustry(
      {
        organizationId: organizationId,
        industryGroupId: industryGroupId,
        industryData: {
          accountIds: adAccountIdsToUpdate,
          selectedIndustryId: industriesForUpdate[0].id,
        },
      },
      {
        onSuccess: () => {
          dispatchAddAccounts(dispatchAddAccountsParams);
        },
      },
    );
  };

  const handleUpdateAdAccountsWithSubIndustryForSelectedRows = (
    subIndustriesForUpdate: Industry[] | null,
  ) => {
    handleUpdateAdAccountsWithSubIndustry(
      selectedAccountsIds,
      subIndustriesForUpdate,
    );
  };

  const handleUpdateAdAccountWithSubIndustryInline = (
    subIndustriesForUpdate: Industry[] | null,
  ) => {
    if (singlePlatformAccountId) {
      handleUpdateAdAccountsWithSubIndustry(
        [singlePlatformAccountId],
        subIndustriesForUpdate,
      );
    }
  };

  const handleUpdateAdAccountsWithSubIndustry = (
    adAccountIdsToUpdate: string[],
    subIndustriesForUpdate: Industry[] | null,
  ) => {
    if (!subIndustriesForUpdate) return;
    const selectedAdAccounts = findMatchingAdAccounts(adAccountIdsToUpdate);
    const industryId = selectedAdAccounts?.[0].industry.id;

    if (subIndustriesForUpdate.length === 0) {
      clearSubIndustries(adAccountIdsToUpdate, selectedAdAccounts, industryId);
    } else {
      addSubIndustry(adAccountIdsToUpdate, subIndustriesForUpdate, industryId);
    }
  };

  const clearSubIndustries = (
    adAccountIdsToUpdate: string[],
    selectedAdAccounts: AdAccount[],
    industryId: number,
  ) => {
    const originalSubIndustryIds = selectedAdAccounts.reduce<number[]>(
      (acc, account) => {
        const subIndustryId = account.subIndustry?.id;
        if (subIndustryId) {
          acc.push(Number(subIndustryId));
        }
        return acc;
      },
      [],
    );

    if (originalSubIndustryIds) {
      updateSubIndustry(
        {
          organizationId: organizationId,
          industryId: industryId,
          industryData: {
            accountIds: adAccountIdsToUpdate,
            unselectedSubIndustryIds: originalSubIndustryIds,
          },
        },
        {
          onSuccess: () => {
            dispatchAddAccounts(dispatchAddAccountsParams);
          },
        },
      );
    }
  };

  const addSubIndustry = (
    adAccountIdsToUpdate: string[],
    subIndustriesForUpdate: Industry[],
    industryId: number,
  ) => {
    updateSubIndustry(
      {
        organizationId: organizationId,
        industryId: industryId,
        industryData: {
          accountIds: adAccountIdsToUpdate,
          selectedSubIndustryId: subIndustriesForUpdate[0].id,
        },
      },
      {
        onSuccess: () => {
          dispatchAddAccounts(dispatchAddAccountsParams);
        },
      },
    );
  };

  const getOnCloseHandler = (): ((params?: any) => void) => {
    switch (openDropdownField) {
      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.BRANDS:
        return () => {
          handleUpdateOneAdAccountWithBrands(
            updateOneAdAccountWithBrandsParams,
          );
        };

      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.MARKETS:
        return () => {
          handleUpdateOneAdAccountWithMarkets(
            updateOneAdAccountWithMarketsParams,
          );
        };

      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS
        .INDUSTRY_GROUP:
        return (industryGroups?: Industry[]) => {
          if (industryGroups) {
            handleUpdateAdAccountWithIndustryGroupInline(industryGroups);
          }
        };

      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.INDUSTRY:
        return (industries?: Industry[]) => {
          if (industries) {
            handleUpdateAdAccountWithIndustryInline(industries);
          }
        };

      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.SUB_INDUSTRY:
        return (subIndustries?: Industry[]) => {
          if (subIndustries) {
            handleUpdateAdAccountWithSubIndustryInline(subIndustries);
          }
        };

      default:
        return () => {};
    }
  };

  const modalContent = (
    <AdAccountHealthDashboardPopoverContent
      brands={brands}
      markets={markets}
      selectedBrands={selectedBrands}
      selectedMarkets={selectedMarkets}
      setSelectedBrands={setSelectedBrands}
      setSelectedMarkets={setSelectedMarkets}
      handleUpdateAdAccountsWithIndustryGroup={
        handleUpdateAdAccountWithIndustryGroupInline
      }
      handleUpdateAdAccountsWithIndustry={
        handleUpdateAdAccountWithIndustryInline
      }
      handleUpdateAdAccountsWithSubIndustry={
        handleUpdateAdAccountWithSubIndustryInline
      }
      industries={industries}
      selectedIndustries={selectedIndustries}
      setSelectedIndustries={setSelectedIndustries}
      industryGroups={industryGroups}
      selectedIndustryGroups={selectedIndustryGroups}
      setSelectedIndustryGroups={setSelectedIndustryGroups}
      subIndustries={subIndustries}
      selectedSubIndustries={selectedSubIndustries}
      setSelectedSubIndustries={setSelectedSubIndustries}
      openDropdownField={openDropdownField}
      setOpenDropdownField={setOpenDropdownField}
      onClose={getOnCloseHandler()}
    />
  );

  const handleDispatchFilterChange = (params: {
    filterId: string;
    value: FilterSelectedValueType;
  }) => {
    if (params.filterId === LandingPageFilterId.IMPORT_STATUS) {
      setImportStatus(params.value.join(','));
    }
  };

  const handleAnyFilterRemove = (value: string) => {
    if (value === LandingPageFilterId.IMPORT_STATUS) {
      setImportStatus('');
    }
  };

  return (
    <div
      style={{ width: '100%', marginBottom: '65px' }}
      className="ad-account-health-dashboard"
    >
      <Box
        sx={{
          height: '100%',
          width: '100%',
        }}
      >
        <Stack sx={{ position: 'relative', zIndex: 5 }}>
          <FiltersControlBar
            pageSize={0}
            page={0}
            totalCount={0}
            onPageChange={() => {}}
            searchComponent={
              <ControlBar
                headerStyles={styles.headerStyles}
                searchQuery={searchQuery}
                setIsSearchTriggered={setIsSearchTriggered}
                setSearchQuery={setSearchQuery}
                brands={brands}
                markets={markets}
                selectedBrands={selectedBrands}
                selectedMarkets={selectedMarkets}
                setSelectedBrands={setSelectedBrands}
                setSelectedMarkets={setSelectedMarkets}
                totalSelectedRows={selectedAccountsIds.length}
                totalAdAccounts={
                  adAccountsPagination?.totalSize
                    ? Number(adAccountsPagination.totalSize)
                    : 0
                }
                columns={columnVisibilityAttributes}
                updateColumns={setColumnVisibilityAttributes}
                handleUpdateAdAccountsWithBrands={(dropdownField) =>
                  handleUpdateMultipleAdAccountsWithBrands({
                    ...updateMultipleAdAccountsWithBrandsBaseParams,
                    dropdownField,
                  })
                }
                handleUpdateAdAccountsWithMarkets={(dropdownField) =>
                  handleUpdateMultipleAdAccountsWithMarkets({
                    ...updateMultipleAdAccountsWithMarketsBaseParams,
                    dropdownField,
                  })
                }
                handleUpdateAdAccountsWithIndustryGroup={
                  handleUpdateAdAccountsWithIndustryGroupForSelectedRows
                }
                handleUpdateAdAccountsWithIndustry={
                  handleUpdateAdAccountsWithIndustryForSelectedRows
                }
                handleUpdateAdAccountsWithSubIndustry={
                  handleUpdateAdAccountsWithSubIndustryForSelectedRows
                }
                selectedAccountsIds={selectedAccountsIds}
                adAccounts={adAccounts}
                setViewByGroup={setViewByGroup}
                industries={industries}
                selectedIndustries={selectedIndustries}
                setSelectedIndustries={setSelectedIndustries}
                industryGroups={industryGroups}
                selectedIndustryGroups={selectedIndustryGroups}
                setSelectedIndustryGroups={setSelectedIndustryGroups}
                subIndustries={subIndustries}
                selectedSubIndustries={selectedSubIndustries}
                setSelectedSubIndustries={setSelectedSubIndustries}
              />
            }
            filters={filters}
            filtersListItems={{
              [LandingPageFilterId.IMPORT_STATUS]: getImportStatusOptions(intl),
            }}
            handleDispatchFilterChange={handleDispatchFilterChange}
            savedFilterParams={{}}
            onAnyFilterRemove={handleAnyFilterRemove}
            context={LandingPageContext.ORGANIZATION}
          />
        </Stack>
        <DataGridPro
          sx={(theme) =>
            adAccountHealthDashboardDataGridStyles(
              theme,
              !isAutoHeightEnabled,
              rows.length,
            )
          }
          checkboxSelection
          onRowSelectionModelChange={handleRowSelectionModelChange}
          rows={rows}
          rowCount={
            adAccountsPagination
              ? Number(adAccountsPagination.totalSize)
              : rows.length
          }
          columns={columns as GridColDef[]}
          apiRef={dataGridRef}
          onCellClick={(params, event) => handleCellClick(params, event)}
          disableRowSelectionOnClick={true}
          autoHeight={isAutoHeightEnabled}
          rowHeight={58}
          pagination
          paginationMode="server"
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[adAccountsPerPage]}
          paginationModel={paginationModel}
          sortingMode="server"
          sortModel={sortModel}
          slots={slots}
          onSortModelChange={(newSortModel) => {
            if (newSortModel.length === 0) {
              setSortModel([{ field: 'channelColumn', sort: 'asc' }]);
            } else {
              const updatedSortModel = newSortModel.map((item) => ({
                field: item.field,
                sort: item.sort || 'asc', // Provide a default sort direction if undefined
              }));
              setSortModel(updatedSortModel);
            }
          }}
          initialState={{
            pinnedColumns: {
              left: ['__check__', 'accountNameColumn'],
            },
          }}
          hideFooter={!isAutoHeightEnabled}
          hideFooterSelectedRowCount
          columnVisibilityModel={getColumnVisibilityModel(
            columnVisibilityAttributes,
          )}
          onColumnVisibilityModelChange={(newVisibilityModel) => {
            updateColumnVisibilityAttributes(
              newVisibilityModel,
              columnVisibilityAttributes,
              setColumnVisibilityAttributes,
            );
          }}
        />
        <AdAccountHealthDashboardPopover
          anchorEl={anchorEl}
          open={Boolean(openDropdownField)}
          placement="right-start"
          setOpenDropdownField={setOpenDropdownField}
          modalContent={modalContent}
        />
      </Box>
      <SnackBar
        snackBarOpen={snackBarOpen}
        handleSnackBarClose={handleSnackBarClose}
      />
      <BrandsOrMarketsErrorBanner
        brandsOrMarketsErrorBannerOpen={brandsOrMarketsErrorBannerOpen}
        handleBrandsOrMarketsErrorBannerClose={
          setBrandsOrMarketsErrorBannerOpen
        }
        label={brandsOrMarketsErrorBannerLabel}
      />
    </div>
  );
};

export default AdAccountHealthDashboardDataGrid;
