import React from 'react';
import { useIntl } from 'react-intl';
import { AD_ACCOUNT_HEALTH_DASHBOARD } from '../../../../../constants';
import { AdAccountHealthDashboardMultiSelect } from './AdAccountHealthDashboardMultiSelect';
import { IndustryDropdownModal } from './IndustryDropdownModal';
import { IdAndName } from '../../../../../types/common.types';
import { Brand } from '../../../../../types/brand.types';
import { Market } from '../../../../../types/market.types';
import { Industry } from '../../../../../types/industry.types';

const NO_RESULTS_SUB_INDUSTRY =
  'ui.adAccountHealthDashboard.dataGrid.popover.noResultsSubIndustry';

interface AdAccountHealthDashboardPopoverContentProps {
  brands: Brand[] | null;
  markets: Market[] | null;
  selectedBrands: IdAndName[];
  selectedMarkets: IdAndName[];
  setSelectedBrands: (brands: IdAndName[]) => void;
  setSelectedMarkets: (markets: IdAndName[]) => void;
  handleUpdateAdAccountsWithBrands?: (param: string) => void;
  handleUpdateAdAccountsWithMarkets?: (param: string) => void;
  handleUpdateAdAccountsWithIndustryGroup: (
    industryGroups: Industry[] | null,
  ) => void;
  handleUpdateAdAccountsWithIndustry: (industries: Industry[] | null) => void;
  handleUpdateAdAccountsWithSubIndustry: (
    subIndustries: Industry[] | null,
  ) => void;
  industries: Industry[] | null;
  selectedIndustries: Industry[] | null;
  setSelectedIndustries: (industries: Industry[] | null) => void;
  industryGroups: Industry[] | null;
  selectedIndustryGroups: Industry[] | null;
  setSelectedIndustryGroups: (industryGroups: Industry[] | null) => void;
  subIndustries: Industry[] | null;
  selectedSubIndustries: Industry[] | null;
  setSelectedSubIndustries: (subIndustries: Industry[] | null) => void;
  openDropdownField: string | null;
  setOpenDropdownField: (openDropdownField: string | null) => void;
  setMainAnchorEl?: (anchorEl: HTMLElement | null) => void;
  onClose?: (industryGroups?: Industry[]) => void;
}

export const AdAccountHealthDashboardPopoverContent = ({
  brands,
  markets,
  selectedBrands,
  selectedMarkets,
  setSelectedBrands,
  setSelectedMarkets,
  handleUpdateAdAccountsWithBrands,
  handleUpdateAdAccountsWithMarkets,
  handleUpdateAdAccountsWithIndustryGroup,
  handleUpdateAdAccountsWithIndustry,
  handleUpdateAdAccountsWithSubIndustry,
  industries,
  selectedIndustries,
  setSelectedIndustries,
  industryGroups,
  selectedIndustryGroups,
  setSelectedIndustryGroups,
  subIndustries,
  selectedSubIndustries,
  setSelectedSubIndustries,
  openDropdownField,
  setOpenDropdownField,
  setMainAnchorEl,
  onClose,
}: AdAccountHealthDashboardPopoverContentProps) => {
  const intl = useIntl();

  const handleDefaultClose = (industryDataForSaving?: Industry[]) => {
    if (setMainAnchorEl) {
      setMainAnchorEl(null);
    }
    switch (openDropdownField) {
      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.BRANDS:
        if (handleUpdateAdAccountsWithBrands) {
          handleUpdateAdAccountsWithBrands(openDropdownField);
        }
        break;
      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.MARKETS:
        if (handleUpdateAdAccountsWithMarkets) {
          handleUpdateAdAccountsWithMarkets(openDropdownField);
        }
        break;
      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS
        .INDUSTRY_GROUP:
        if (handleUpdateAdAccountsWithIndustryGroup) {
          handleUpdateAdAccountsWithIndustryGroup(
            industryDataForSaving || null,
          );
          setSelectedIndustries(null);
          setSelectedSubIndustries(null);
        }
        break;
      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.INDUSTRY:
        if (handleUpdateAdAccountsWithIndustry) {
          handleUpdateAdAccountsWithIndustry(industryDataForSaving || null);
          setSelectedSubIndustries(null);
        }
        break;
      case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.SUB_INDUSTRY:
        if (handleUpdateAdAccountsWithSubIndustry) {
          handleUpdateAdAccountsWithSubIndustry(industryDataForSaving || null);
        }
        break;
      default:
        break;
    }
  };

  const handleClose = onClose || handleDefaultClose;

  switch (openDropdownField) {
    case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.BRANDS:
      return (
        <AdAccountHealthDashboardMultiSelect
          selectedItems={selectedBrands}
          setSelectedItems={setSelectedBrands}
          setOpenDropdownField={setOpenDropdownField}
          onClose={handleClose}
          items={brands}
        />
      );
    case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.MARKETS:
      return (
        <AdAccountHealthDashboardMultiSelect
          selectedItems={selectedMarkets}
          setSelectedItems={setSelectedMarkets}
          setOpenDropdownField={setOpenDropdownField}
          onClose={handleClose}
          items={markets}
        />
      );
    case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.INDUSTRY_GROUP:
      return (
        <IndustryDropdownModal
          selectedItems={selectedIndustryGroups}
          setSelectedItems={setSelectedIndustryGroups}
          setOpenDropdownField={setOpenDropdownField}
          onSave={handleClose}
          items={industryGroups}
        />
      );
    case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.INDUSTRY:
      if (selectedIndustryGroups && selectedIndustryGroups.length === 1) {
        return (
          <IndustryDropdownModal
            selectedItems={selectedIndustries}
            setSelectedItems={setSelectedIndustries}
            setOpenDropdownField={setOpenDropdownField}
            onSave={handleClose}
            items={industries}
          />
        );
      }
      return null;
    case AD_ACCOUNT_HEALTH_DASHBOARD.TAGGING_AD_ACCOUNTS_LABELS.SUB_INDUSTRY:
      if (selectedIndustries && selectedIndustries.length === 1) {
        return (
          <IndustryDropdownModal
            selectedItems={selectedSubIndustries}
            setSelectedItems={setSelectedSubIndustries}
            setOpenDropdownField={setOpenDropdownField}
            onSave={handleClose}
            items={subIndustries}
            customEmptyStateText={intl.formatMessage({
              id: NO_RESULTS_SUB_INDUSTRY,
            })}
          />
        );
      }
      return null;
    default:
      return null;
  }
};
