import { useMemo } from 'react';
import { ApiStatus, PermissionsKeySet } from '../types';
import {
  accountNameColumn,
  brandColumn,
  channelColumn,
  connectedByColumn,
  connectionStatusColumn,
  currencyColumn,
  ImportStatusColumn,
  industryColumn,
  industryGroupColumn,
  lastConnectedOnColumn,
  lastImportedOnColumn,
  marketsColumn,
  platformAccountIdColumn,
  platformAccountNameColumn,
  subIndustryColumn,
  workspacesColumn,
} from '../Columns';
import { GLOBALS } from '../../../../../constants';
import { IntlShape } from 'react-intl';
import { Brand } from '../../../../../types/brand.types';

const { PENDING, NOT_LOADED } = GLOBALS.REDUX_LOADING_STATUS;

interface UseGetColumnsParams {
  adAccountsApiStatus: ApiStatus;
  brands: Brand[] | null;
  openDropdownField: string | null;
  singlePlatformAccountId: string | null;
  orgPermissions: PermissionsKeySet;
  intl: IntlShape;
}

export const useGetColumns = ({
  adAccountsApiStatus,
  brands,
  openDropdownField,
  singlePlatformAccountId,
  orgPermissions,
  intl,
}: UseGetColumnsParams) =>
  useMemo(() => {
    const isLoading = getIsLoading(adAccountsApiStatus);
    return [
      accountNameColumn(intl, isLoading),
      platformAccountNameColumn(),
      platformAccountIdColumn(),
      channelColumn(intl, isLoading),
      connectionStatusColumn(intl, isLoading),
      lastConnectedOnColumn(intl, isLoading),
      ImportStatusColumn(intl, isLoading, 0),
      lastImportedOnColumn(intl, isLoading),
      workspacesColumn(intl, isLoading),
      connectedByColumn(intl, isLoading),
      brandColumn({
        intl,
        isLoading,
        brands,
        openDropdownField,
        singlePlatformAccountId,
        orgPermissions,
      }),
      industryGroupColumn({
        intl,
        isLoading,
        openDropdownField,
        singlePlatformAccountId,
      }),
      industryColumn({
        intl,
        isLoading,
        openDropdownField,
        singlePlatformAccountId,
      }),
      subIndustryColumn({
        intl,
        isLoading,
        openDropdownField,
        singlePlatformAccountId,
      }),
      marketsColumn({
        intl,
        isLoading,
        openDropdownField,
        singlePlatformAccountId,
      }),
      currencyColumn(intl, isLoading),
    ];
  }, [
    adAccountsApiStatus,
    brands,
    openDropdownField,
    singlePlatformAccountId,
    orgPermissions,
    intl,
  ]);

const getIsLoading = (loadingStatus: string) => {
  return loadingStatus === PENDING || loadingStatus === NOT_LOADED;
};
