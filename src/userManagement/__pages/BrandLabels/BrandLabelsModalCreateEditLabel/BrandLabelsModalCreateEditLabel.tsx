import React, { useState, ChangeEvent } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';
import { Theme } from '@mui/material/styles';
import { ErrorFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import { useIntl } from 'react-intl';
import { getOrgBrandLabelsModalState } from '../../../redux/selectors/organization.selectors';
import { getOrganizationId } from '../../../../redux/selectors/partner.selectors';
import {
  createOrgBrandLabel,
  updateOrgBrandLabel,
} from '../../../redux/actions/organization.actions';
import organization from '../../../redux/slices/organization.slice';
import { brandLabelsPerPage } from '../BrandLabelsTable/BrandLabelsTable';
import {
  VidMobBox,
  VidMobText<PERSON>ield,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';

const { resetBrandLabelsModalState } = organization.actions;

const textFieldStyles: Record<string, any> = {
  height: '44px !important',
  width: '372px',
  opacity: 1,
  visibility: 'visible',
  transition: 'opacity 0.3s ease-in-out',
  '& .Mui-error': {
    borderColor: (theme: Theme) => theme.palette.error.main,
  },
  '& .MuiInputBase-root': {
    height: '36px',
    borderRadius: '6px',
  },
  '& .MuiInputBase-input': {
    padding: '6px 12px 6px 12px',
    fontSize: '14px',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderWidth: '1px',
  },
  '& .MuiInputBase-input::placeholder': {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '20px',
  },
  '&:hover .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline': {
    borderColor: (theme: Theme) => theme.palette.primary.main,
  },
};

const alertStyles = {
  borderRadius: '6px',
  backgroundColor: '#FEEBEE',
  display: 'flex',
  padding: '12px',
  alignItems: 'center',
  marginBottom: '12px',
  gap: '4px',
};

interface BrandLabelsModalCreateEditLabelProps {
  getBrandLabelsCallParams?: () => any;
}

const BrandLabelsModalCreateEditLabel = ({
  getBrandLabelsCallParams,
}: BrandLabelsModalCreateEditLabelProps) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const organizationId = useSelector((state: any) => getOrganizationId(state));
  const brandLabelsModalState = useSelector((state: any) =>
    getOrgBrandLabelsModalState(state),
  );
  const { type, isOpen, params } = brandLabelsModalState;
  const { id, isDuplicateBrand } = params;
  const workspaceCount = params?.workspaceCount;

  const isCreateModal = type === 'create';
  const isEditModal = type === 'edit';

  const [name, setName] = useState<string>(
    isEditModal ? (params?.name as string) : '',
  );
  const [description, setDescription] = useState<string>(
    isEditModal ? (params?.description as string) : '',
  );
  const [descriptionError, setDescriptionError] = useState<boolean>(false);

  const isUnchanged =
    isEditModal &&
    name?.trim() === params?.name &&
    description?.trim() === params?.description;

  const isNameEmpty = name?.trim().length === 0;

  const isSubmitButtonDisabled = isNameEmpty || isUnchanged || descriptionError;

  let headerText = '';
  let submitButtonLabel = '';

  if (isCreateModal) {
    headerText = intl.messages[
      'ui.organization.brandLabels.landing.modal.create.label.title'
    ] as string;
    submitButtonLabel = intl.messages[
      'ui.organization.brandLabels.landing.modal.create.label.submit.button'
    ] as string;
  }

  if (isEditModal && params) {
    headerText = intl.messages[
      'ui.organization.brandLabels.landing.modal.edit.label.title'
    ] as string;
    submitButtonLabel = intl.messages[
      'ui.organization.brandLabels.landing.modal.edit.label.submit.button'
    ] as string;
  }

  const onModalClose = () => {
    dispatch(resetBrandLabelsModalState());
  };

  const onModalSubmit = () => {
    const trimmedName = name.trim();
    const trimmedDescription = description?.trim();

    const callParams = getBrandLabelsCallParams
      ? getBrandLabelsCallParams()
      : {
          search: '',
          paginationParams: {
            perPage: brandLabelsPerPage,
            offset: 0,
          },
        };

    if (isCreateModal) {
      dispatch(
        createOrgBrandLabel(
          organizationId,
          trimmedName,
          trimmedDescription,
          callParams,
        ),
      );
    }

    if (isEditModal && id) {
      dispatch(
        updateOrgBrandLabel(
          organizationId,
          id,
          trimmedName,
          trimmedDescription,
          callParams,
        ),
      );
    }
  };

  const handleNameInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setName(newValue);
  };

  const handleDescriptionInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setDescription(newValue);

    if (newValue.length > 1000) {
      setDescriptionError(true);
    } else {
      setDescriptionError(false);
    }
  };

  const showEditLabelWarning = isEditModal && workspaceCount > 0;

  const textInputComponent = (
    <>
      {showEditLabelWarning && (
        <VidMobBox sx={alertStyles}>
          <VidMobBox sx={{ width: '32px' }}>
            <ErrorFilledIcon
              sx={{
                height: '20px',
                width: '20px',
                color: '#212121',
                marginRight: '8px',
              }}
            />
          </VidMobBox>
          <VidMobTypography
            sx={{
              fontSize: '14px',
              fontWeight: 400,
              lineHeight: '20px',
              color: '#212121',
            }}
          >
            {intl.formatMessage(
              {
                id: 'ui.organization.brandLabels.landing.modal.edit.label.subTitle',
              },
              {
                workspaceCount: String(workspaceCount),
                bold: (chunks: React.ReactNode) => <b>{chunks}</b>,
              },
            )}
          </VidMobTypography>
        </VidMobBox>
      )}
      <VidMobTypography variant="subtitle2" sx={{ paddingBottom: '8px' }}>
        {intl.formatMessage({
          id: 'ui.organization.brandLabels.landing.modal.text.input.label',
        })}
      </VidMobTypography>
      <VidMobTextField
        value={name}
        onChange={handleNameInputChange}
        variant="outlined"
        sx={textFieldStyles}
      />
      {isDuplicateBrand && (
        <VidMobBox sx={{ display: 'flex' }}>
          <ErrorFilledIcon
            sx={{
              height: '18px',
              width: '18px',
              color: '#D32F2F',
              marginRight: '8px',
            }}
          />
          <VidMobTypography
            sx={{
              color: '#D32F2F',
              fontSize: '12px',
              fontWeight: '500',
              lineHeight: '18px',
            }}
          >
            {intl.formatMessage({
              id: 'ui.organization.brandLabels.landing.modal.error.duplicate.name',
            })}
          </VidMobTypography>
        </VidMobBox>
      )}

      <VidMobTooltip
        title={intl.formatMessage({
          id: 'ui.organization.brandLabels.landing.modal.description.input.subtitle',
        })}
        disableHoverListener={description === ''}
      >
        <VidMobBox display={'inline-flex'}>
          <VidMobTypography
            variant="subtitle2"
            sx={{ paddingBottom: '8px', paddingTop: '8px' }}
          >
            {intl.formatMessage({
              id: 'ui.organization.brandLabels.landing.modal.description.input.label',
            })}
          </VidMobTypography>
        </VidMobBox>
      </VidMobTooltip>
      <VidMobTextField
        value={description}
        onChange={handleDescriptionInputChange}
        variant="outlined"
        multiline
        error={descriptionError}
        minRows={6}
        maxRows={6}
        placeholder={intl.formatMessage({
          id: 'ui.organization.brandLabels.landing.modal.description.input.subtitle',
        })}
        sx={{
          ...textFieldStyles,
          height: 'auto !important',
          '& .MuiInputBase-root': {
            ...textFieldStyles['& .MuiInputBase-root'],
            height: 'auto',
            padding: 6,
          },
          '& .MuiInputBase-input': {
            padding: 0,
            fontSize: '14px',
          },
        }}
      />
      {descriptionError && (
        <VidMobBox sx={{ display: 'flex', mt: 4 }}>
          <ErrorFilledIcon
            sx={{
              height: '18px',
              width: '18px',
              color: '#D32F2F',
              marginRight: '8px',
            }}
          />
          <VidMobTypography
            sx={{
              color: '#D32F2F',
              fontSize: '12px',
              fontWeight: '500',
              lineHeight: '18px',
            }}
          >
            {intl.formatMessage(
              {
                id: 'ui.organization.brandLabels.landing.modal.error.maxLength.description',
              },
              { maxLength: 1000 },
            )}
          </VidMobTypography>
        </VidMobBox>
      )}
    </>
  );

  return (
    <CustomDialog
      isOpen={isOpen}
      headerText={headerText}
      onClose={onModalClose}
      onSubmit={onModalSubmit}
      submitButtonLabel={submitButtonLabel}
      bodyChildren={textInputComponent}
      renderHeaderDivider={false}
      isSubmitButtonDisabled={isSubmitButtonDisabled}
      explicitDialogWidth={'420px'}
    />
  );
};

export default BrandLabelsModalCreateEditLabel;
