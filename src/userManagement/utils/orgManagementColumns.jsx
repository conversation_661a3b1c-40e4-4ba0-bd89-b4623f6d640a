import React from 'react';
import MuiAvatar from '../../muiCustomComponents/Avatar/Avatar';
import { ORG_MANAGEMENT } from '../../constants';
import OverflowMoreTextWithTooltip from '../../muiCustomComponents/OverflowMoreTextWithTooltip';
import moment from 'moment';
import { getIntl } from '../../utils/getIntl';
import { Box, Typography } from '@mui/material';
import getMUIIconForChannel from '../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { buildDataGridColumn } from '../../utils/buildDataGridColumns';
import { convertUTCDateToTimeZone } from '../../utils/dateUtils';
import { DATE_TIME_ZONES } from '../../utils/dateUtils';
import {
  getPlatformDisplayIntlText,
  getPlatformIdentifierForLogo,
} from '../../utils/feConstantsUtils';
import { renderCellLastLoginDate } from '../__pages/Peoples/renderCellLastLoginDate';
import { PEOPLES_PAGE_TABS } from '../../constants/organization.constants';
import { ImportStatusColumn } from '../__pages/AdAccountHealthDashboard/AdAccountHealthDashboardDataGrid/Columns/ImportStatusColumn';

const {
  BRAND_LABELS_LANDING_COLUMN_KEYS,
  WORKSPACE_LANDING_COLUMN_KEYS,
  PEOPLES_PAGE_PENDING_INVITES_COLUMN_KEYS,
} = ORG_MANAGEMENT;

export const getWorkspaceTableColumns = () => {
  const { NAME, LINKED_AD_ACCOUNTS_COUNT } = WORKSPACE_LANDING_COLUMN_KEYS;

  const nameColumn = buildDataGridColumn({
    field: NAME,
    headerCopy: getIntl().messages['ui.workspaces.list.table.header.name'],
    customCellRenderer: (params) => (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flexShrink: 0,
          alignSelf: 'stretch',
        }}
      >
        <MuiAvatar
          style={{ width: '40px', height: '40px' }}
          variant="rounded"
          src={params.row.logoUrl}
          alt={params.value[0]}
        />
        <Typography
          variant="body2"
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {params.value}
        </Typography>
      </Box>
    ),
  });

  const linkedAdAccountsColumn = buildDataGridColumn({
    field: LINKED_AD_ACCOUNTS_COUNT,
    headerCopy:
      getIntl().messages['ui.workspaces.list.table.header.linkedAdAccounts'],
    cellCopySuffix:
      getIntl().messages['ui.workspaces.list.table.rows.linkedAdAccounts'],
  });

  return [nameColumn, linkedAdAccountsColumn];
};

export const getPeoplesGridColumns = (
  intl,
  renderRoleDropdown,
  renderEllipsisDropdown,
  canRemoveUserFromOrg,
  tab,
) => {
  return [
    {
      field: 'name',
      headerName: intl.messages['ui.workspaces.people.table.header.name'],
      flex: 5,
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {intl.messages['ui.workspaces.people.table.header.name']}
        </Typography>
      ),
      renderCell: (params) => (
        <>
          <MuiAvatar
            variant="circular"
            src={params.row.photo}
            alt={params.row.displayName}
            style={{ width: '32px', height: '32px' }}
          />
          <span style={{ marginLeft: '10px' }}>{params.row.displayName}</span>
        </>
      ),
      valueGetter: (params) => params.row.displayName,
      sortable: false,
    },
    {
      field: 'role',
      headerName: intl.messages['ui.workspaces.people.table.header.role'],
      flex: 4,
      renderHeader: () => (
        <Typography variant={'subtitle2'} sx={{ paddingLeft: '16px' }}>
          {intl.messages['ui.workspaces.people.table.header.role']}
        </Typography>
      ),
      renderCell: renderRoleDropdown,
    },
    {
      field: 'email',
      headerName: intl.messages['ui.workspaces.people.table.header.email'],
      flex: 4,
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {intl.messages['ui.workspaces.people.table.header.email']}
        </Typography>
      ),
      renderCell: (params) => <span>{params.row.email}</span>,
      valueGetter: (params) => params.row.email,
      sortable: false,
    },
    tab === PEOPLES_PAGE_TABS.MEMBERS && {
      field: 'lastLoginDate',
      headerName:
        intl.messages['ui.workspaces.people.table.header.lastLoginDate'],
      flex: 4,
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {intl.messages['ui.workspaces.people.table.header.lastLoginDate']}
        </Typography>
      ),
      renderCell: (params) => renderCellLastLoginDate(params.row.lastLoginDate),
      valueGetter: (params) => params.row.lastLoginDate,
      sortable: false,
    },
    ...(canRemoveUserFromOrg
      ? [
          {
            field: 'ellipsisDropdown',
            headerName: '',
            flex: 1,
            renderCell: renderEllipsisDropdown,
            sortable: false,
          },
        ]
      : []),
  ];
};

export const getBrandLabelsTableColumns = (intl, renderCell) => {
  const { NAME, CREATED_BY, CREATED_ON, DESCRIPTION, ELLIPSIS_DROPDOWN } =
    BRAND_LABELS_LANDING_COLUMN_KEYS;

  return [
    {
      field: NAME,
      flex: 4,
      sortable: false,
      renderCell: renderCell,
      renderHeader: () => (
        <Typography variant="subtitle2">
          {
            intl.messages[
              'ui.organization.brandLabels.landing.table.header.name'
            ]
          }
        </Typography>
      ),
    },
    {
      field: DESCRIPTION,
      flex: 4,
      sortable: false,
      renderCell: renderCell,
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {
            intl.messages[
              'ui.organization.brandLabels.landing.table.header.description'
            ]
          }
        </Typography>
      ),
    },
    {
      field: CREATED_ON,
      flex: 2,
      sortable: false,
      renderCell: renderCell,
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {
            intl.messages[
              'ui.organization.brandLabels.landing.table.header.createdOn'
            ]
          }
        </Typography>
      ),
    },
    {
      field: CREATED_BY,
      flex: 2,
      sortable: false,
      renderCell: renderCell,
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {
            intl.messages[
              'ui.organization.brandLabels.landing.table.header.createdBy'
            ]
          }
        </Typography>
      ),
    },
    {
      field: ELLIPSIS_DROPDOWN,
      headerName: '',
      flex: 1,
      sortable: false,
      renderCell: renderCell,
    },
  ];
};

export const getAdAccountsGridColumns = (intl, isExpanded) => {
  const columns = [
    {
      field: 'platformAccountName',
      headerName: intl.messages['ui.workspaces.adAccounts.column.name'],
      flex: 1,
    },
    {
      field: 'platform',
      headerName: intl.messages['ui.workspaces.adAccounts.column.platform'],
      flex: 1,
      renderCell: (params) => {
        const platformIdentifier = params.row.platform;
        const platformIdentifierForLogo =
          getPlatformIdentifierForLogo(platformIdentifier);
        const platformDisplayIntlText = getPlatformDisplayIntlText(
          platformIdentifier,
          intl,
        );
        return (
          <>
            {getMUIIconForChannel(platformIdentifierForLogo)}
            <Typography
              variant="body2"
              style={{ marginLeft: '10px', textTransform: 'capitalize' }}
            >
              {platformDisplayIntlText || platformIdentifier.toLowerCase()}
            </Typography>
          </>
        );
      },
    },
    {
      field: 'dateCreated',
      headerName: intl.messages['ui.workspaces.adAccounts.column.connectedOn'],
      flex: 1,
      valueGetter: (params) =>
        moment(params.row.dateCreated).format('MMM D, YYYY'),
    },
    ImportStatusColumn(intl, false, 1),
    {
      field: 'lastSuccessfulProcessingDate',
      headerName:
        intl.messages['ui.adAccountHealthDashboard.dataGrid.lastImportedOn'],
      flex: 1,
      filterable: false,
      sortable: true,
      renderCell: (params) => {
        function formatDate(isoDate) {
          const date = new Date(isoDate);
          return new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            timeZone: 'UTC',
          }).format(date);
        }
        const formattedValue = params.value ? formatDate(params.value) : '–';
        return <Typography variant="body2">{formattedValue}</Typography>;
      },
    },
  ];

  if (isExpanded) {
    columns.push({
      field: 'avatarUrl',
      headerName: intl.messages['ui.workspaces.adAccounts.column.connectedBy'],
      flex: 1,
      renderCell: (params) => {
        return (
          <OverflowMoreTextWithTooltip stringsList={params.row.connectedBy} />
        );
      },
    });
  }

  return columns;
};

export const getPeoplesPagePendingInvitesGridColumns = (
  intl,
  renderDropdown,
) => {
  const { EMAIL, INVITE_LAST_SENT, INVITED_BY, ELLIPSIS_DROPDOWN } =
    PEOPLES_PAGE_PENDING_INVITES_COLUMN_KEYS;

  return [
    {
      field: EMAIL,
      flex: 4,
      sortable: false,
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {
            intl.messages[
              'ui.peoples.page.tabs.pendingInvites.grid.columnHeader.email'
            ]
          }
        </Typography>
      ),
      renderCell: (params) => (
        <Typography variant={'body2'}>{params.row.userEmail}</Typography>
      ),
    },
    {
      field: INVITE_LAST_SENT,
      flex: 3,
      sortable: false,
      renderCell: (params) => {
        const date = params.row.lastUpdated;
        const formattedDate = convertUTCDateToTimeZone(
          date,
          DATE_TIME_ZONES.AMERICA_NEW_YORK,
        );

        return <Typography variant={'body2'}>{formattedDate}</Typography>;
      },
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {
            intl.messages[
              'ui.peoples.page.tabs.pendingInvites.grid.columnHeader.lastInviteDate'
            ]
          }
        </Typography>
      ),
    },
    {
      field: INVITED_BY,
      flex: 3,
      sortable: false,
      renderCell: (params) => (
        <>
          <MuiAvatar
            variant="circular"
            src={params.row?.invitedBy.photo}
            alt={params.row.invitedBy.displayName}
            style={{ width: '36px', height: '36px' }}
          />
          <Typography sx={{ marginLeft: '12px' }} variant={'body2'}>
            {params.row.invitedBy.displayName}
          </Typography>
        </>
      ),
      renderHeader: () => (
        <Typography variant={'subtitle2'}>
          {
            intl.messages[
              'ui.peoples.page.tabs.pendingInvites.grid.columnHeader.invitedBy'
            ]
          }
        </Typography>
      ),
    },
    {
      field: ELLIPSIS_DROPDOWN,
      headerName: '',
      flex: 2,
      sortable: false,
      renderCell: renderDropdown,
    },
  ];
};
