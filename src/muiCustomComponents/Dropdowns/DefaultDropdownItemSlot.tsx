import React from 'react';
import { Box, Stack, Divider, Checkbox } from '@mui/material';
import { Check } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import OverflowTip from '../OverflowTip';
import { DropdownSlotProps } from './types';
import { useIntl } from 'react-intl';
import { VidMobBox } from '../../vidMobComponentWrappers';

const dropdownItemSx = {
  width: '100%',
  p: '8px',
  borderRadius: '6px',
};

const checkSx = {
  height: '20px',
  width: '20px',
  mr: '8px',
};

const checkBoxSx = {
  '&:hover': {
    bgcolor: 'transparent',
  },
};

export const DefaultDropdownItemSlot = ({
  multi,
  index,
  id,
  name,
  isSelected,
  isDivider,
  disabled,
  tooltipTitle,
  icon,
}: DropdownSlotProps) => {
  const theme = useTheme();
  const intl = useIntl();
  const textColor = disabled
    ? theme.palette.text.disabled
    : theme.palette.text.primary;
  const tooltipTitleIntl = tooltipTitle
    ? intl.formatMessage({ id: tooltipTitle })
    : '';

  const iconElement = icon ? (
    <VidMobBox sx={{ display: 'flex', alignItems: 'center' }}>{icon}</VidMobBox>
  ) : null;

  const DropdownItem = () => (
    <Box
      sx={{
        ...dropdownItemSx,
        color: textColor,
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        width="100%"
        overflow="hidden"
      >
        <Stack
          direction="row"
          alignItems="center"
          maxWidth="100%"
          overflow="hidden"
        >
          {iconElement}
          {multi && <Checkbox sx={checkBoxSx} checked={isSelected} />}
          <OverflowTip
            customSx={{ ml: multi ? 0 : '8px' }}
            title={tooltipTitleIntl}
            unDisableHoverListener={Boolean(tooltipTitleIntl)}
          >
            {name}
          </OverflowTip>
        </Stack>
        {!multi && isSelected && <Check sx={checkSx} />}
      </Stack>
    </Box>
  );

  if (isDivider && index !== 0) {
    return (
      <Stack direction="column" width="100%">
        <Divider sx={{ marginBottom: '8px' }} />
        <DropdownItem key={id} />
      </Stack>
    );
  }

  return <DropdownItem key={id} />;
};
