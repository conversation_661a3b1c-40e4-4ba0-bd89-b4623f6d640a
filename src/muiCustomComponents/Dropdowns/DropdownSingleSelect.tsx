import React, { MouseEvent, useEffect, useState } from 'react';
import { Box, MenuItem } from '@mui/material';
import { DropdownSingleSelectProps, DropdownOption } from './types';
import { DefaultDropdownItemSlot } from './DefaultDropdownItemSlot';
import { TriggerButton } from './TriggerButton';
import { DropdownMenu } from './DropdownMenu';
import { menuItemSx } from './DropdownStyles';
import { onSearchChange } from './utils';
import { VidMobBox, VidMobTooltip } from '../../vidMobComponentWrappers';
import DropdownBlankState from './DropdownBlankState';

export const DropdownSingleSelect = (props: DropdownSingleSelectProps) => {
  const {
    options,
    selectedOption,
    setSelectedOption,
    disabled,
    displaySearch,
    LabelElement,
    SlotElement = DefaultDropdownItemSlot,
    onDropdownOpen,
    customButtonSx,
    customMenuSx,
    customMenuItemSx = menuItemSx,
    maxHeight,
    autoSelectIfSingleValue = false,
    onBlur,
    onFocus,
    disableButtonRipple = false,
    buttonPlaceholder,
    displayActionButtons,
    saveButtonLabel,
    cancelButtonLabel,
    defaultNumberOfDisplayedOptions = 50,
    labelPrefixKey,
    adjustLabelText,
    canClearSelection = false,
    hasGroupSelect = true,
    hoverComponent,
    handleMouseEnter = () => {},
    handleMouseLeave = () => {},
    onSearchChange: onSearchChangeHandler,
    handleInputMouseEnter = () => {},
    handleInputMouseLeave = () => {},
  } = props;

  const [filteredOptions, setFilteredOptions] =
    useState<DropdownOption[]>(options);
  const [internalSelectedOption, setInternalSelectedOption] = useState<
    DropdownOption | null | undefined
  >(selectedOption);
  const [search, setSearch] = useState('');
  const [showNoResultsState, setShowNoResultsState] = useState(false);
  const [numberOfDisplayedOptions, setNumberOfDisplayedOptions] = useState(
    defaultNumberOfDisplayedOptions,
  );
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const handleOpen = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (onDropdownOpen) {
      onDropdownOpen(true);
    }
    if (onFocus) {
      onFocus();
    }
    setAnchorEl(e.currentTarget);
  };

  const handleClose = () => {
    if (onDropdownOpen) {
      onDropdownOpen(false);
    }

    if (
      displayActionButtons &&
      selectedOption?.id !== internalSelectedOption?.id
    ) {
      setInternalSelectedOption(selectedOption);
    }

    setSearch('');
    setAnchorEl(null);
    setNumberOfDisplayedOptions(defaultNumberOfDisplayedOptions);
    setFilteredOptions(options);
    if (onBlur) {
      onBlur();
    }
  };

  const handleSelectionChange = (newSelectedOption: DropdownOption) => {
    setInternalSelectedOption(newSelectedOption);
    if (displayActionButtons) {
      return;
    }

    if (
      !internalSelectedOption ||
      newSelectedOption?.id !== internalSelectedOption.id
    ) {
      setSelectedOption(newSelectedOption);
    }

    handleClose();
  };

  const handleClearSelection = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setInternalSelectedOption(null);
    setSelectedOption(null);
  };

  const handleSave = () => {
    if (
      internalSelectedOption &&
      selectedOption?.id !== internalSelectedOption.id
    ) {
      setSelectedOption(internalSelectedOption);
      handleClose();
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChangeHandler?.(event?.target.value);
    onSearchChange({
      event,
      options,
      numberOfDisplayedOptions,
      defaultNumberOfDisplayedOptions,
      setNumberOfDisplayedOptions,
      setSearch,
      setFilteredOptions,
    });
  };

  useEffect(() => {
    if (search && displaySearch && filteredOptions.length === 0) {
      setShowNoResultsState(true);
    } else {
      setShowNoResultsState(false);
    }
  }, [search, filteredOptions]);

  useEffect(() => {
    setFilteredOptions(
      options.filter((option) =>
        option.name?.toLowerCase().includes(search.toLocaleLowerCase()),
      ),
    );
    if (autoSelectIfSingleValue && options.length === 1) {
      setInternalSelectedOption(options[0]);
      setSelectedOption(options[0]);
    }
  }, [options]);

  useEffect(() => {
    if (!anchorEl) {
      return;
    }

    setInternalSelectedOption(selectedOption);
  }, [anchorEl]);

  useEffect(() => {
    setInternalSelectedOption(selectedOption);
  }, [selectedOption]);

  const handleShowMoreButtonClick = () => {
    setNumberOfDisplayedOptions(
      numberOfDisplayedOptions + defaultNumberOfDisplayedOptions,
    );
  };

  const displayShowMoreButton =
    filteredOptions.length > defaultNumberOfDisplayedOptions;
  const showMoreButtonDisabled =
    numberOfDisplayedOptions > filteredOptions.length;

  const renderDropdownOptions = () => {
    if (!options.length) {
      return (
        <VidMobBox sx={{ maxWidth: 250 }}>
          <DropdownBlankState descriptionTranslationKey="ui.reportFilters.multiValue.noOptions" />
        </VidMobBox>
      );
    }

    return filteredOptions
      .slice(0, numberOfDisplayedOptions)
      .map((option, index) => (
        <VidMobTooltip
          title={option.disabledReason}
          placement="top"
          disableHoverListener={!option.disabled}
          key={option.id}
        >
          <Box
            sx={{ position: 'relative' }}
            key={option.id}
            onMouseEnter={() => handleMouseEnter(option)}
            onMouseLeave={handleMouseLeave}
          >
            <MenuItem
              sx={{
                // @ts-ignore
                pointerEvents: 'auto !important',
                ...customMenuItemSx,
                ...(option.isDivider && !hasGroupSelect
                  ? {
                      '&:hover': {
                        backgroundColor: 'transparent',
                      },
                      cursor: 'default',
                    }
                  : {}),
              }}
              disabled={option.disabled}
              disableRipple={
                (option.isDivider && !hasGroupSelect) || option.disabled
              }
              onClick={
                option.disabled
                  ? undefined
                  : (e) => {
                      if (option.isDivider && !hasGroupSelect) {
                        return;
                      }
                      handleSelectionChange(option);
                      e.stopPropagation();
                      e.preventDefault();
                    }
              }
            >
              <SlotElement
                index={index}
                id={option.id}
                name={option.name}
                type={option.type}
                parentId={option.parentId}
                description={option.description}
                symbol={option.symbol}
                isSelected={option.id === internalSelectedOption?.id}
                isDivider={option.isDivider}
                disabled={option.disabled}
                tooltipTitle={option.tooltipTitle}
                icon={option.icon}
              />
            </MenuItem>
          </Box>
        </VidMobTooltip>
      ));
  };

  return (
    <Box sx={{ maxWidth: '100%' }}>
      <TriggerButton
        disabled={disabled}
        onClick={handleOpen}
        isDropdownOpen={Boolean(anchorEl)}
        selectedOption={selectedOption}
        buttonPlaceholder={buttonPlaceholder}
        customButtonSx={customButtonSx}
        disableRipple={disableButtonRipple}
        LabelElement={LabelElement}
        labelPrefixKey={labelPrefixKey}
        adjustLabelText={adjustLabelText}
        canClearSelection={canClearSelection}
        onClearSelection={handleClearSelection}
        handleInputMouseEnter={handleInputMouseEnter}
        handleInputMouseLeave={handleInputMouseLeave}
      />
      <DropdownMenu
        anchorEl={anchorEl}
        onClose={handleClose}
        onSave={handleSave}
        renderDropdownOptions={renderDropdownOptions}
        displaySearch={displaySearch}
        searchText={search}
        handleSearchChange={handleSearchChange}
        showNoResultsState={showNoResultsState}
        displayShowMoreButton={displayShowMoreButton}
        showMoreButtonDisabled={showMoreButtonDisabled}
        onShowMoreButtonClick={handleShowMoreButtonClick}
        displayActionButtons={displayActionButtons}
        cancelButtonLabel={cancelButtonLabel}
        saveButtonLabel={saveButtonLabel}
        customMenuSx={customMenuSx}
        maxHeight={maxHeight}
        hoverComponent={hoverComponent}
      />
    </Box>
  );
};
