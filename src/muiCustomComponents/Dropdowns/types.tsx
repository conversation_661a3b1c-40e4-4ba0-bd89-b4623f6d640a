import { SxProps } from '@mui/material';
import { MessageFormatElement } from 'react-intl';
import { JSX, ReactNode } from 'react';

export interface DropdownOption {
  id: string | number;
  name: string;
  index?: number;
  isSelected?: boolean;
  isDivider?: boolean;
  disabled?: boolean;
  icon?: JSX.Element;
  originalName?: string;
  parent?: string;
  color?: string;
  criteriaIds?: number[];
  parentId?: string | number;
  description?: string;
  symbol?: string;
  learnMoreLink?: string;
  popoverName?: string;
  tooltipTitle?: string;
  disabledReason?: string;
  type?: string;
}

export interface DropdownSlotProps extends DropdownOption {
  multi?: boolean;
  color?: string;
  selectAllChildren?: (parentId: string | number) => void;
  areAllChildrenSelected?: boolean;
  isChannelDropdown?: boolean;
  handleMouseEnter?: (
    description?: string | null,
    name?: string | null,
  ) => void;
  handleMouseLeave?: () => void;
}

interface DropdownCommonProps {
  options: DropdownOption[];
  disabled?: boolean;
  displaySearch?: boolean;
  LabelElement?: (props: any) => JSX.Element;
  SlotElement?: (props: any) => JSX.Element;
  onDropdownOpen?: (isOpen: boolean) => void;
  customButtonSx?: SxProps;
  customButtonOpenDropdownSx?: SxProps;
  customMenuSx?: SxProps;
  customMenuItemSx?: SxProps;
  maxHeight?: number;
  autoSelectIfSingleValue?: boolean;
  onBlur?: () => void;
  onFocus?: () => void;
  disableButtonRipple?: boolean;
  buttonPlaceholder?: string | MessageFormatElement[];
  displayActionButtons?: boolean;
  saveButtonLabel?: string | MessageFormatElement[];
  cancelButtonLabel?: string | MessageFormatElement[];
  defaultNumberOfDisplayedOptions?: number;
  labelPrefixKey?: string;
  adjustLabelText?: (text: string) => string;
  hasGroupSelect?: boolean;
  hoverComponent?: ReactNode;
  onSearchChange?: (text: string) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isFetchingMore?: boolean;
}

export interface DropdownSingleSelectProps extends DropdownCommonProps {
  selectedOption?: DropdownOption | null;
  setSelectedOption: (selectedOption: DropdownOption | null) => void;
  canClearSelection?: boolean;
  handleMouseEnter?: (option: DropdownOption | null) => void;
  handleMouseLeave?: () => void;
  handleInputMouseEnter?: (e: React.MouseEvent<HTMLElement>) => void;
  handleInputMouseLeave?: (e: React.MouseEvent<HTMLElement>) => void;
}

export interface DropdownMultiSelectProps extends DropdownCommonProps {
  selectedOptions?: DropdownOption[];
  setSelectedOptions: (selectedOptions: DropdownOption[]) => void;
  SelectAllElement?: (props: {
    isAllSelected: boolean;
    isIndeterminate: boolean;
  }) => JSX.Element;
  isMandatory?: boolean;
  shouldShowSelectAll?: boolean;
  shouldShowRemoveAll?: boolean;
  onlySelectWithinDivider?: boolean;
  showTextInsteadOfDropdown?: string;
  MenuHeader?: (props: any) => JSX.Element;
}
