import React, { useMemo, RefObject } from 'react';
import { DropdownOption } from './types';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobChip,
  VidMobStack,
  VidMobTypography,
} from '../../vidMobComponentWrappers';
import { Tooltip } from '@mui/material';
import { getTextColor } from '../../creativeScoring/components/criteriaManagement/CriteriaManagementDataGrid/DataGridCells/CriteriaGroupCell';
import { organizeChipsIntoRows } from './utils';

const emptyStateLabelSx = {
  width: '100%',
};

const emptyStateLabelTextSx = {
  textAlign: 'start',
};

const containerSx = {
  width: '100%',
  flexDirection: 'row',
  flexWrap: 'wrap',
  columnGap: '4px',
  rowGap: '3px',
  overflow: 'hidden',
};

const defaultChipSx = {
  '.MuiChip-deleteIcon:hover': {
    opacity: '0.5 !important',
  },
};

const chipTextSx = {
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
};

interface Props {
  // in order to organize the chips into truncated maxRows,
  // we need to tell RemovableChipsDropdownLabel the width of the parent before it renders
  // either by passing a ref to the parent(parentRef) or by passing the width (explicitParentWidth) directly
  parentRef?: RefObject<HTMLDivElement>;
  explicitParentWidth?: number;
  selectedItems?: DropdownOption[];
  setSelectedItems: (selectedItems: DropdownOption[]) => void;
  buttonPlaceholder?: string;
  maxRows?: number | null; // null means no row limit (i. e. all items are visible), in which case we don't need the parent width
}

export const RemovableChipsDropdownLabel = ({
  parentRef,
  explicitParentWidth,
  selectedItems = [],
  setSelectedItems,
  buttonPlaceholder,
  maxRows = 2,
}: Props) => {
  const intl = useIntl();

  const placeholder =
    buttonPlaceholder ||
    intl.formatMessage({
      id: 'ui.dropdown.placeholder.v2',
      defaultMessage: '-select an option-',
    });

  const removeItem = (id: string | number) => {
    const updatedListItems = selectedItems.filter((item) => item.id !== id);
    setSelectedItems(updatedListItems);
  };

  const { rowCount, visibleItems, hiddenItems } = useMemo(() => {
    const parentWidth = explicitParentWidth || parentRef?.current?.clientWidth;
    return organizeChipsIntoRows(selectedItems, maxRows, parentWidth);
  }, [selectedItems.length]);

  if (!selectedItems.length) {
    return (
      <VidMobBox sx={emptyStateLabelSx}>
        <VidMobTypography sx={emptyStateLabelTextSx} variant="body2" noWrap>
          {placeholder}
        </VidMobTypography>
      </VidMobBox>
    );
  }

  const itemsToDisplay = maxRows === null ? selectedItems : visibleItems;

  const getChipSx = (item: DropdownOption) => {
    if (!item) {
      return defaultChipSx;
    }

    const deleteIconColor = getTextColor(item.color);

    return {
      ...defaultChipSx,
      backgroundColor: item.color,
      '.MuiChip-deleteIcon': {
        color: deleteIconColor,
        '&:hover': {
          color: deleteIconColor,
          opacity: '0.5 !important',
        },
      },
    };
  };

  return (
    <VidMobStack
      sx={{
        ...containerSx,
        ...(maxRows !== null && { minHeight: `${rowCount * 34}px` }),
      }}
    >
      {itemsToDisplay.map((item) => (
        <VidMobChip
          key={item.id}
          sx={getChipSx(item)}
          label={
            <VidMobStack direction="row" alignItems="center">
              {item.icon}
              <Tooltip title={item.name} placement="top" disableInteractive>
                <VidMobTypography
                  variant="subtitle3"
                  sx={{ color: getTextColor(item.color), ...chipTextSx }}
                >
                  {item.name}
                </VidMobTypography>
              </Tooltip>
            </VidMobStack>
          }
          onDelete={() => removeItem(item.id)}
        />
      ))}
      {hiddenItems.length > 0 && (
        <VidMobChip
          label={
            <Tooltip
              title={hiddenItems.map((item) => item.name).join(', ')}
              placement="top"
            >
              <VidMobTypography variant="subtitle3">
                {`+${hiddenItems.length}`}
              </VidMobTypography>
            </Tooltip>
          }
        />
      )}
    </VidMobStack>
  );
};
