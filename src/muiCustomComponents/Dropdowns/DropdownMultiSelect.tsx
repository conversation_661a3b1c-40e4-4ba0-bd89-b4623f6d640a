import React, {
  useEffect,
  useState,
  useRef,
  ChangeEvent,
  MouseEvent,
  useCallback,
} from 'react';
import { Divider } from '@mui/material';
import { DropdownMultiSelectProps, DropdownOption } from './types';
import { DefaultDropdownItemSlot } from './DefaultDropdownItemSlot';
import { DefaultSelectAllOption } from './DefaultSelectAllOption';
import { TriggerButton } from './TriggerButton';
import { DropdownMenu } from './DropdownMenu';
import { menuItemSx } from './DropdownStyles';
import {
  checkIsAllSelected,
  checkIsIndeterminate,
  deselectAllFilteredOptions,
  getAllChildrenFromParent,
  getAreAllChildrenSelected,
  onSearchChange,
  selectAllFilteredOptions,
} from './utils';
import DropdownBlankState from './DropdownBlankState';
import {
  VidMobBox,
  VidMobMenuItem,
  VidMobTooltip,
} from '../../vidMobComponentWrappers';
import { DefaultRemoveAllOption } from './DefaultRemoveAllOption';

const dividerSx = {
  marginTop: '8px',
  marginBottom: '8px',
};

const DEBOUNCED_IS_ALL_SELECTED_TIME = 100;

export const DropdownMultiSelect = (props: DropdownMultiSelectProps) => {
  const {
    options,
    selectedOptions = [],
    setSelectedOptions,
    disabled,
    displaySearch = false,
    LabelElement,
    MenuHeader = null,
    SlotElement = DefaultDropdownItemSlot,
    SelectAllElement = DefaultSelectAllOption,
    onDropdownOpen,
    customButtonSx,
    customButtonOpenDropdownSx,
    customMenuItemSx,
    customMenuSx,
    maxHeight,
    autoSelectIfSingleValue = false,
    onBlur,
    onFocus,
    disableButtonRipple = false,
    buttonPlaceholder,
    displayActionButtons,
    saveButtonLabel,
    cancelButtonLabel,
    isMandatory,
    defaultNumberOfDisplayedOptions = 50,
    shouldShowSelectAll = true,
    shouldShowRemoveAll = false,
    onlySelectWithinDivider = false,
    showTextInsteadOfDropdown,
    labelPrefixKey,
    adjustLabelText,
  } = props;

  const [filteredOptions, setFilteredOptions] = useState<DropdownOption[]>([]);
  const [internalSelectedOptions, setInternalSelectedOptions] =
    useState<DropdownOption[]>(selectedOptions);
  const [search, setSearch] = useState('');
  const [isAllSelected, setIsAllSelected] = useState(
    checkIsAllSelected(options, selectedOptions),
  );
  const [isIndeterminate, setIsIndeterminate] = useState(
    checkIsIndeterminate(options, selectedOptions),
  );
  const [showNoResultsState, setShowNoResultsState] = useState(false);
  const [numberOfDisplayedOptions, setNumberOfDisplayedOptions] = useState(
    defaultNumberOfDisplayedOptions,
  );
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const debouncedIsAllSelectedRef = useRef<ReturnType<
    typeof setTimeout
  > | null>(null);

  const optionsString = JSON.stringify(
    options.map((option) => [option.id, option.name]),
  );

  const selectedOptionsString = JSON.stringify(
    selectedOptions.map((option) => option.id),
  );
  const filteredOptionsString = JSON.stringify(
    filteredOptions.map((option) => option.id),
  );
  const internalSelectedOptionsString = JSON.stringify(
    internalSelectedOptions.map((option) => option.id),
  );

  useEffect(() => {
    if (search && displaySearch && filteredOptions.length === 0) {
      setShowNoResultsState(true);
    } else {
      setShowNoResultsState(false);
    }

    if (search) {
      setDebouncedIsAllSelected();
    }
  }, [search]);

  useEffect(() => {
    setIsAllSelected(checkIsAllSelected(options, selectedOptions));
    setIsIndeterminate(checkIsIndeterminate(options, selectedOptions));
    setFilteredOptions(
      options.filter((option) => option.name?.toLowerCase().includes(search)),
    );
    if (
      autoSelectIfSingleValue &&
      options.length === 1 &&
      selectedOptions.length === 0
    ) {
      setInternalSelectedOptions([options[0]]);
      setSelectedOptions([options[0]]);
    }
  }, [optionsString]);

  useEffect(() => {
    let value = selectedOptions;
    if (isMandatory && selectedOptions.length === 0 && options.length > 0) {
      value = [options[0]];
    }

    setInternalSelectedOptions(value);
  }, [selectedOptionsString]);

  useEffect(() => {
    setIsAllSelected(
      checkIsAllSelected(filteredOptions, internalSelectedOptions),
    );
    setIsIndeterminate(
      checkIsIndeterminate(filteredOptions, internalSelectedOptions),
    );
  }, [filteredOptionsString, internalSelectedOptionsString]);

  const setDebouncedIsAllSelected = useCallback(() => {
    if (debouncedIsAllSelectedRef.current) {
      clearTimeout(debouncedIsAllSelectedRef.current);
    }

    debouncedIsAllSelectedRef.current = setTimeout(() => {
      setIsAllSelected(
        checkIsAllSelected(filteredOptions, internalSelectedOptions),
      );
      setIsIndeterminate(
        checkIsIndeterminate(filteredOptions, internalSelectedOptions),
      );
    }, DEBOUNCED_IS_ALL_SELECTED_TIME);
  }, [filteredOptions, internalSelectedOptions]);

  useEffect(() => {
    search && setDebouncedIsAllSelected();
  }, [search]);

  const handleOpen = (e: MouseEvent<HTMLButtonElement>) => {
    if (onDropdownOpen) {
      onDropdownOpen(true);
    }

    if (onFocus) {
      onFocus();
    }

    setAnchorEl(e.currentTarget);
  };

  const handleClose = () => {
    if (onDropdownOpen) {
      onDropdownOpen(false);
    }

    if (onBlur) {
      onBlur();
    }

    if (
      !displayActionButtons &&
      selectedOptionsString !== internalSelectedOptionsString
    ) {
      setSelectedOptions(internalSelectedOptions);
    }

    setSearch('');
    setAnchorEl(null);
    setFilteredOptions(options);
    setNumberOfDisplayedOptions(defaultNumberOfDisplayedOptions);
  };

  const handleChoicesWithinDividerOnly = (newOption: DropdownOption) => {
    const newSelectedOptions = internalSelectedOptions.filter(
      (option) => option.parent === newOption.parent,
    );
    setInternalSelectedOptions([...newSelectedOptions, newOption]);
    setIsAllSelected(checkIsAllSelected(filteredOptions, newSelectedOptions));
    setIsIndeterminate(
      checkIsIndeterminate(filteredOptions, newSelectedOptions),
    );
  };

  const handleCheckboxChange = (checked: boolean, option: DropdownOption) => {
    if (option.disabled || option.isDivider) {
      return;
    }

    if (checked) {
      if (isMandatory && internalSelectedOptions.length === 1) {
        setIsAllSelected(true);
        setIsIndeterminate(false);
        return;
      }

      const optionsToSelect = internalSelectedOptions.filter(
        (opt) => opt.id !== option.id,
      );
      setInternalSelectedOptions(optionsToSelect);
      setIsAllSelected(false);
      setIsIndeterminate(optionsToSelect.length > 0);
    } else if (onlySelectWithinDivider) {
      handleChoicesWithinDividerOnly(option);
    } else {
      const optionsToSelect = [...internalSelectedOptions, option];
      setInternalSelectedOptions(optionsToSelect);
      setIsAllSelected(checkIsAllSelected(filteredOptions, optionsToSelect));
      setIsIndeterminate(
        checkIsIndeterminate(filteredOptions, optionsToSelect),
      );
    }
  };

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newSearch = event.target.value;
    onSearchChange({
      event,
      options,
      numberOfDisplayedOptions,
      defaultNumberOfDisplayedOptions,
      setNumberOfDisplayedOptions,
      setSearch,
      setFilteredOptions,
    });

    if (newSearch === '') {
      setIsAllSelected(checkIsAllSelected(options, selectedOptions));
      setIsIndeterminate(checkIsIndeterminate(options, selectedOptions));
    } else {
      setDebouncedIsAllSelected();
    }
  };

  const handleSave = () => {
    setSelectedOptions(internalSelectedOptions);
    handleClose();
  };

  const handleSelectAll = () => {
    if (isAllSelected) {
      const newInternalSelectedOptions = deselectAllFilteredOptions(
        filteredOptions,
        internalSelectedOptions,
      );

      let value = newInternalSelectedOptions;
      if (isMandatory && newInternalSelectedOptions.length === 0) {
        value = [options[0]];
      }

      setInternalSelectedOptions(value);
      setIsAllSelected(false);
    } else {
      const newInternalSelectedOptions = selectAllFilteredOptions(
        options,
        filteredOptions,
        internalSelectedOptions,
      );
      setInternalSelectedOptions(newInternalSelectedOptions);
      setIsAllSelected(true);
    }
    setIsIndeterminate(false);
  };

  const handleSelectAllChildren = (parentId: string | number) => {
    const allChildren = getAllChildrenFromParent(parentId, filteredOptions);
    const areAllChildrenSelected = getAreAllChildrenSelected(
      parentId,
      filteredOptions,
      internalSelectedOptions,
    );
    if (areAllChildrenSelected) {
      const optionsToSelect = internalSelectedOptions.filter(
        (option) => !allChildren.some((child) => child.id === option.id),
      );
      setInternalSelectedOptions(optionsToSelect);
    } else {
      const allChildrenToSelect = allChildren.filter(
        (child) =>
          !internalSelectedOptions.some((option) => option.id === child.id),
      );
      const optionsToSelect = [
        ...internalSelectedOptions,
        ...allChildrenToSelect,
      ];
      setInternalSelectedOptions(optionsToSelect);
    }
  };

  const handleShowMoreButtonClick = () => {
    setNumberOfDisplayedOptions(
      numberOfDisplayedOptions + defaultNumberOfDisplayedOptions,
    );
  };

  const displayShowMoreButton =
    filteredOptions.length > defaultNumberOfDisplayedOptions;
  const showMoreButtonDisabled =
    numberOfDisplayedOptions > filteredOptions.length;

  const renderSelectAllOption = () => (
    <>
      <VidMobBox sx={{ position: 'relative' }}>
        <VidMobMenuItem
          sx={menuItemSx}
          onClick={(e) => {
            handleSelectAll();
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <SelectAllElement
            isAllSelected={isAllSelected}
            isIndeterminate={isIndeterminate}
          />
        </VidMobMenuItem>
      </VidMobBox>
      <Divider sx={dividerSx} />
    </>
  );

  const renderClearAllElement = () => {
    if (!shouldShowSelectAll && shouldShowRemoveAll) {
      return (
        <DefaultRemoveAllOption
          isDisabled={internalSelectedOptions.length === 0}
          onClick={(e) => {
            const newInternalSelectedOptions = deselectAllFilteredOptions(
              filteredOptions,
              internalSelectedOptions,
            );

            let value = newInternalSelectedOptions;
            if (isMandatory && newInternalSelectedOptions.length === 0) {
              value = [options[0]];
            }

            setInternalSelectedOptions(value);
            setIsAllSelected(false);
            e.stopPropagation();
            e.preventDefault();
          }}
        />
      );
    }

    return null;
  };

  const renderDropdownOptions = () => {
    if (!options.length) {
      return (
        <VidMobBox sx={{ maxWidth: 250 }}>
          <DropdownBlankState descriptionTranslationKey="ui.reportFilters.multiValue.noOptions" />
        </VidMobBox>
      );
    }

    return (
      <>
        {MenuHeader && <MenuHeader />}
        {shouldShowSelectAll && renderSelectAllOption()}
        {filteredOptions
          .slice(0, numberOfDisplayedOptions)
          .map((option, index) => {
            const checked =
              internalSelectedOptions.filter((opt) => opt.id === option.id)
                .length > 0;
            return (
              <VidMobBox
                sx={{ position: 'relative' }}
                key={`${option.id}-${index}`}
              >
                {option.isDivider ? (
                  <SlotElement
                    index={index}
                    id={option.id}
                    name={option.name}
                    isSelected={checked}
                    isDivider={option.isDivider}
                    disabled={option.disabled}
                    areAllChildrenSelected={getAreAllChildrenSelected(
                      option.id,
                      filteredOptions,
                      internalSelectedOptions,
                    )}
                    selectAllChildren={handleSelectAllChildren}
                  />
                ) : (
                  <VidMobTooltip
                    title={option.disabledReason}
                    placement="top"
                    disableHoverListener={!option.disabled}
                  >
                    <VidMobBox>
                      <VidMobMenuItem
                        sx={{ ...menuItemSx, ...customMenuItemSx }}
                        disabled={option.disabled}
                        onClick={(e) => {
                          handleCheckboxChange(checked, option);
                          e.stopPropagation();
                          e.preventDefault();
                        }}
                      >
                        <SlotElement
                          multi
                          index={index}
                          id={option.id}
                          name={option.name}
                          isSelected={checked}
                          isDivider={false}
                          disabled={option.disabled}
                          color={option.color}
                          icon={option.icon}
                        />
                      </VidMobMenuItem>
                    </VidMobBox>
                  </VidMobTooltip>
                )}
              </VidMobBox>
            );
          })}
      </>
    );
  };

  return (
    <VidMobBox sx={{ maxWidth: '100%' }}>
      <TriggerButton
        multi
        disabled={disabled}
        onClick={handleOpen}
        isDropdownOpen={Boolean(anchorEl)}
        selectedOptions={internalSelectedOptions}
        buttonPlaceholder={buttonPlaceholder}
        customButtonSx={customButtonSx}
        customButtonOpenDropdownSx={customButtonOpenDropdownSx}
        disableRipple={disableButtonRipple}
        LabelElement={LabelElement}
        labelPrefixKey={labelPrefixKey}
        adjustLabelText={adjustLabelText}
      />
      <DropdownMenu
        anchorEl={anchorEl}
        onClose={handleClose}
        onSave={handleSave}
        renderDropdownOptions={renderDropdownOptions}
        renderClearAllElement={renderClearAllElement}
        displaySearch={options.length ? displaySearch : false}
        searchText={search}
        handleSearchChange={handleSearchChange}
        showNoResultsState={showNoResultsState}
        displayShowMoreButton={displayShowMoreButton}
        showMoreButtonDisabled={showMoreButtonDisabled}
        onShowMoreButtonClick={handleShowMoreButtonClick}
        displayActionButtons={displayActionButtons}
        cancelButtonLabel={cancelButtonLabel}
        saveButtonLabel={saveButtonLabel}
        customMenuSx={customMenuSx}
        maxHeight={maxHeight}
        showTextInsteadOfDropdown={showTextInsteadOfDropdown}
      />
    </VidMobBox>
  );
};
