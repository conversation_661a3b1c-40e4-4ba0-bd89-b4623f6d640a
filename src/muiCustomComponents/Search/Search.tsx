import React, {
  useState,
  useRef,
  useEffect,
  ChangeEvent,
  CSSProperties,
  FC,
} from 'react';
import {
  Box,
  IconButton,
  InputAdornment,
  TextField,
  Button,
} from '@mui/material';
import CancelRounded from '@mui/icons-material/CancelRounded';
import { useIntl } from 'react-intl';
import useDebounce from '../../hooks/useDebounce';
import { SearchFilledIcon } from '../../assets/vidmob-mui-icons/general';
import { Theme } from '@mui/material/styles';

const defaultButtonSx = {
  position: 'absolute',
  left: 0,
  maxWidth: '32px !important',
  minWidth: '32px !important',
  height: '32px !important',
  borderRadius: '6px',
  color: 'black',
  padding: 0,
  margin: 0,
  justifyContent: 'center',
  backgroundColor: '',
  '&:hover': {
    backgroundColor: 'transparent',
  },
};

const defaultCollapsedButtonSx = {
  backgroundColor: (theme: any) => theme.palette.action.enabled,
  '&:hover': {
    backgroundColor: (theme: Theme) => theme.palette.action.hover,
  },
};

interface SearchProps {
  placeholder?: string;
  searchTerm?: string;
  onSearchChange?: (value: string) => void;
  isCollapsible?: boolean;
  isDisabled?: boolean;
  debounceTime?: number;
  onCollapse?: () => void;
  customStyles?: CSSProperties | null;
  customCollapsedButtonStyles?: CSSProperties | null;
  customIconStyles?: CSSProperties | null;
  isExpandedOverride?: boolean;
  setIsExpandedOverride?: (isExpandedOverride: boolean) => void;
  textFieldSx?: CSSProperties | null;
  canCollapseAndClear?: boolean;
}

/**
 * PROPS:
 * @param {string} placeholder - Optional: placeholder text for the search input
 * @param {string} searchTerm - Optional: initial search term
 * @param {function} onSearchChange - Required: function to be called upon changing the search input value
 * @param {boolean} isCollapsible - Optional: whether the search should be collapsible or always open (defaults to always open)
 * @param {boolean} isDisabled - Optional: whether the button input is disabled altogether (only relevant if isCollapsible is true)
 * @param {number} debounceTime - Optional: time in milliseconds to debounce the search input (defaults to 0)
 * @param {function} onCollapse - Optional: function to be called upon collapsing the search input (only relevant if isCollapsible is true)
 * @param {CSSProperties} customStyles - Optional: custom styles for the search input
 * @param {CSSProperties} customCollapsedButtonStyles - Optional: custom styles for the search button
 * @param {CSSProperties} customIconStyles - Optional: custom styles for the search icon
 * @param {boolean} isExpandedOverride - Optional: override for the expanded state of the search input (only relevant if isCollapsible is true)
 * @param {function} setIsExpandedOverride - Optional: function to set the expanded state of the search input (only relevant if isCollapsible is true)
 */

const Search: FC<SearchProps> = ({
  placeholder,
  searchTerm = '',
  onSearchChange,
  isCollapsible = false,
  isDisabled = false,
  debounceTime = 0,
  onCollapse,
  customStyles = null,
  customCollapsedButtonStyles = null,
  customIconStyles = null,
  isExpandedOverride,
  setIsExpandedOverride,
  textFieldSx,
  canCollapseAndClear,
}) => {
  const intl = useIntl();
  const [isExpandedLocalState, setIsExpandedLocalState] =
    useState<boolean>(!isCollapsible);
  const [inputValue, setInputValue] = useState<string>(searchTerm);
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  const inputWrapperRef = useRef<HTMLInputElement | null>(null);
  const textFieldInputRef = useRef<HTMLInputElement | null>(null);
  const isSearchTermEmpty = useRef<boolean>(true);

  const debouncedSearchTerm = useDebounce(inputValue, debounceTime);
  const isExpanded = isExpandedOverride ?? isExpandedLocalState;

  const showCollapseAndClearButton =
    canCollapseAndClear ||
    (!isCollapsible && inputValue) ||
    (isCollapsible && isExpanded);

  useEffect(() => {
    setInputValue(searchTerm);
  }, [searchTerm]);

  useEffect(() => {
    if (inputValue !== '') {
      isSearchTermEmpty.current = false;
    }

    if (inputValue === '') {
      isSearchTermEmpty.current = true;
    }
  }, [inputValue]);

  useEffect(() => {
    onSearchChange?.(debouncedSearchTerm);
  }, [debouncedSearchTerm]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleExpand = () => {
    if (isCollapsible) {
      if (
        isExpandedOverride !== undefined &&
        setIsExpandedOverride !== undefined
      ) {
        setIsExpandedOverride(!isExpandedOverride);
      } else {
        setIsExpandedLocalState(!isExpandedLocalState);
      }
    }
  };

  const handleCollapse = () => {
    if (isCollapsible) {
      if (
        isExpandedOverride !== undefined &&
        setIsExpandedOverride !== undefined
      ) {
        setIsExpandedOverride(false);
      } else {
        setIsExpandedLocalState(false);
      }
    }

    setInputValue('');
    handleInputChange({
      target: {
        value: '',
      },
    } as ChangeEvent<HTMLInputElement>);

    if (onCollapse) {
      onCollapse();
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    const shouldCollapse =
      textFieldInputRef.current &&
      !textFieldInputRef.current.contains(event.target as Node) &&
      isCollapsible &&
      isSearchTermEmpty.current;
    if (shouldCollapse) {
      handleCollapse();
    }
  };

  useEffect(() => {
    if (isExpanded && textFieldInputRef.current && isCollapsible) {
      textFieldInputRef.current.focus();
    }
  }, [isExpanded, textFieldInputRef]);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const searchHeight = customStyles?.height
    ? customStyles.height
    : '32px !important';

  return (
    <Box
      ref={inputWrapperRef}
      sx={{
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        width: customStyles?.width
          ? customStyles.width
          : isExpanded
            ? '100%'
            : 36,
        height: searchHeight,
        transition: 'all 0.3s ease-in-out',
        ...customStyles,
      }}
    >
      <TextField
        inputRef={textFieldInputRef}
        value={inputValue}
        onChange={handleInputChange}
        placeholder={
          placeholder ||
          (intl.messages['ui.mui.search.defaults.placeholder'] as string)
        }
        variant="outlined"
        fullWidth
        size="small"
        InputProps={
          showCollapseAndClearButton
            ? {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      edge="end"
                      onClick={handleCollapse}
                      sx={{
                        '&:hover': {
                          backgroundColor: 'transparent',
                        },
                      }}
                    >
                      <CancelRounded
                        sx={{ color: (theme) => theme.palette.text.secondary }}
                      />
                    </IconButton>
                  </InputAdornment>
                ),
              }
            : {}
        }
        sx={{
          height: searchHeight,
          opacity: isExpanded ? 1 : 0,
          visibility: isExpanded ? 'visible' : 'hidden',
          transition: 'opacity 0.3s ease-in-out',
          '& .MuiInputBase-root': {
            height: searchHeight,
            borderRadius: '6px',
          },
          '& .MuiInputBase-input': {
            padding: '6px 0 6px 40px',
            fontSize: '14px',
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderWidth: '1px',
          },
          '& .MuiInputBase-input::placeholder': {
            fontSize: '14px',
            fontWeight: 400,
            lineHeight: '20px',
          },
          '&:hover .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline': {
            borderColor: (theme) => theme.palette.primary.main,
          },
          ...(textFieldSx || {}),
        }}
      />

      <Button
        sx={{
          ...defaultButtonSx,
          ...(!customCollapsedButtonStyles &&
            !isExpanded &&
            defaultCollapsedButtonSx),
          ...(customCollapsedButtonStyles &&
            !isExpanded &&
            customCollapsedButtonStyles),
        }}
        onMouseEnter={() => setIsButtonHovered(true)}
        onMouseLeave={() => setIsButtonHovered(false)}
        onClick={handleExpand}
        disabled={isDisabled}
      >
        <SearchFilledIcon
          sx={{
            className: 'search-icon',
            color: isExpanded
              ? (theme: Theme) => theme.palette.text.secondary
              : isButtonHovered
                ? (theme: Theme) => theme.palette.primary.main
                : 'inherit',
            ...(customIconStyles && customIconStyles),
          }}
        />
      </Button>
    </Box>
  );
};

export default Search;
