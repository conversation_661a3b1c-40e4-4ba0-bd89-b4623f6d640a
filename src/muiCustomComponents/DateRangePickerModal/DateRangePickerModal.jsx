import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
import { useIntl } from 'react-intl';
import { isSameDateRange } from '../../utils/dateRangePickerMUIUtils';
import { Box, Button, Paper, Typography, Stack } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { StaticDateRangePicker } from '@mui/x-date-pickers-pro/StaticDateRangePicker';
import { SingleInputDateRangeField } from '@mui/x-date-pickers-pro/SingleInputDateRangeField';
import useIsDateRangeValid from './useIsDateRangeValid';
import { getIntl } from '../../utils/getIntl';
import { ALL_TIME_PRESET_ID } from '../../components/ReportFilters/components/ReportDateRangePicker';

// To utilize the current browser locale with the MUI LocalizationProvider,
// the initial step involves identifying the browser's locale.
// Subsequently, the appropriate locale file from dayjs should be imported to align with this locale setting.
let LOCALE =
  navigator.languages && navigator.languages.length
    ? navigator.languages[0]
    : navigator.language;
LOCALE = LOCALE.toLowerCase();

import(`dayjs/locale/${LOCALE}.js`).catch(() => {
  // match the dayjs locale file name with the locale + region string
  LOCALE = LOCALE.slice(0, 2);
  import(`dayjs/locale/${LOCALE}.js`).catch(() => {
    // else match the dayjs locale file name with the parent locale string
    LOCALE = 'en';
    import(`dayjs/locale/${LOCALE}.js`); // else default to English
  });
});

const CUSTOM_DATE_RANGE_INTL_KEY = 'ui.mui.dateRangePicker.presets.custom';

const dateRangeSx = {
  display: 'flex',
  '& .MuiListItem-root': { pl: '0' },
  '& .MuiPickersArrowSwitcher-spacer': { width: '24px' },
  '& .MuiPickersArrowSwitcher-button': { borderRadius: '100%' },
  '& .MuiPickersCalendarHeader-labelContainer': { cursor: 'default' },
};

const defaultPaperSx = {
  borderRadius: '6px',
  p: '20px',
  mt: '5px',
  position: 'absolute',
  zIndex: '1000',
  maxHeight: '70vh',
  overflowY: 'auto',
};

const titleSx = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
};

const labelSx = {
  color: 'text.secondary',
  mb: '4px',
};

const inputSx = {
  width: 300,
  mb: '20px',
};

const buttonContainerSx = {
  mt: '20px',
  gap: '4px',
};

const getParsedDateRanges = (presetDateRanges) => {
  const intl = getIntl();
  return presetDateRanges.map((item, index) => ({
    label: intl.formatMessage({ id: item.label }),
    getValue: item.getValue,
    id: item.id || `date-preset-${index}`,
  }));
};

const getCustomIndex = (presetDateRanges) =>
  presetDateRanges.findIndex((dateRange) => {
    return dateRange.label === CUSTOM_DATE_RANGE_INTL_KEY;
  });

const getSlotElement = (element) => {
  let parent = element.parentElement;
  let counter = 0;
  while (
    counter < 5 &&
    parent &&
    !parent.classList.contains('MuiListItem-root')
  ) {
    parent = parent.parentElement;
    counter++;
  }

  return parent;
};

const DateRangePickerModal = ({ ...dateRangePickerProps }) => {
  const {
    selectedDateRange,
    setSelectedDateRange,
    disableFutureDates = false,
    setIsDropdownOpen,
    presetDateRanges,
    calendars = 1,
    customPaperSx = {},
    minDate,
    maxNumberOfDays,
    hasActionButtons = true,
    customFooterComponent,
    allTimePresetProps,
  } = dateRangePickerProps;

  const { includeAllTimeOption, isAllTimeSelected, setIsAllTimeSelected } =
    allTimePresetProps || {};

  const intl = useIntl();
  const [startDate, endDate] = selectedDateRange;
  const [dateValues, updateDateValues] = useState([
    dayjs(startDate),
    dayjs(endDate),
  ]);

  const maxDate = disableFutureDates ? dayjs() : undefined;
  const [selectedPresetIndex, setSelectedPresetIndex] = useState(-1);
  const [parsedPresetDateRanges, setParsedPresetDateRanges] = useState(
    getParsedDateRanges(presetDateRanges),
  );
  const [customIndex] = useState(getCustomIndex(presetDateRanges));

  const selectedShortcutButtonSelector = `li:nth-of-type(${selectedPresetIndex}) .MuiButtonBase-root`;

  const onSlotClick = (event) => {
    try {
      const slotElement = getSlotElement(event.target);
      if (slotElement) {
        const { parentElement } = slotElement;
        const index = Array.from(parentElement?.children || []).indexOf(
          slotElement,
        );
        if (index > -1) {
          setSelectedPresetIndex(index + 1);
        } else {
          setSelectedPresetIndex(customIndex + 1);
        }
      }
    } catch (err) {
      setSelectedPresetIndex(customIndex + 1);
      console.error(err);
    }
  };

  const dateRangePickerSlotProps = {
    shortcuts: {
      items: parsedPresetDateRanges,
      onClick: onSlotClick,
      sx: {
        [selectedShortcutButtonSelector]: {
          color: 'primary.main',
          backgroundColor: 'primary.light',
        },
      },
    },
  };

  const setSelectedIndex = (dateRange) => {
    if (includeAllTimeOption && isAllTimeSelected) {
      const indexOfAllTimePreset = parsedPresetDateRanges.findIndex(
        (item) => item.id === ALL_TIME_PRESET_ID,
      );
      setSelectedPresetIndex(indexOfAllTimePreset + 1);
      return;
    }

    let foundMatch = false;
    parsedPresetDateRanges.forEach((parsedPresetDateRange, index) => {
      const isRangeSameAsSelected = isSameDateRange(
        parsedPresetDateRange.getValue(),
        dateRange,
      );

      if (isRangeSameAsSelected && !foundMatch) {
        setSelectedPresetIndex(index + 1);
        foundMatch = true;
      }
    });
    if (!foundMatch) {
      setSelectedPresetIndex(customIndex + 1);
    }
  };

  const handleDateChange = (dateRange) => {
    const [startDate, endDate, id] = dateRange;
    updateDateValues(dateRange);

    if (!hasActionButtons) {
      setIsAllTimeSelected?.(id === ALL_TIME_PRESET_ID);
      setSelectedDateRange([startDate, endDate]);
      updateDateValues(dateRange);
    }
    setSelectedPresetIndex(customIndex + 1);
  };

  const handleClose = () => {
    setIsDropdownOpen(false);
  };

  const handleSave = () => {
    const presetId = dateValues[2];
    setIsAllTimeSelected?.(presetId === ALL_TIME_PRESET_ID);
    setSelectedDateRange(dateValues);
    setIsDropdownOpen(false);
  };

  useEffect(() => {
    setSelectedIndex(dateValues);
  }, []);

  useEffect(() => {
    setParsedPresetDateRanges(getParsedDateRanges(presetDateRanges));
  }, [presetDateRanges]);

  const isApplyButtonDisabled = !useIsDateRangeValid(
    dateValues,
    minDate,
    maxDate,
    maxNumberOfDays,
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={LOCALE}>
      <Paper
        elevation={2}
        sx={{
          ...defaultPaperSx,
          ...customPaperSx,
        }}
      >
        <Box sx={titleSx}>
          <Typography variant="subtitle2" sx={labelSx}>
            {intl.formatMessage({
              id: 'ui.mui.dateRangePicker.dateRange.label',
              defaultMessage: 'Select Date Range',
            })}
          </Typography>
          <SingleInputDateRangeField
            disableFuture
            value={dateValues}
            onChange={handleDateChange}
            minDate={minDate}
            maxDate={maxDate}
            hiddenLabel
            variant="standard"
            InputProps={{
              disableUnderline: true,
            }}
            sx={inputSx}
          />
        </Box>
        <StaticDateRangePicker
          onChange={handleDateChange}
          sx={dateRangeSx}
          value={dateValues}
          disableAutoMonthSwitching
          slotProps={dateRangePickerSlotProps}
          minDate={minDate}
          maxDate={maxDate}
          displayStaticWrapperAs="desktop"
          calendars={calendars}
        />
        <Stack
          flexDirection="row"
          justifyContent={customFooterComponent ? 'space-between' : 'flex-end'}
          alignItems="center"
        >
          {customFooterComponent}
          {hasActionButtons ? (
            <Stack direction="row" sx={buttonContainerSx}>
              <Button onClick={handleClose}>
                {intl.formatMessage({
                  id: 'ui.mui.dateRangePicker.cancel.label',
                  defaultMessage: 'Cancel',
                })}
              </Button>
              <Button disabled={isApplyButtonDisabled} onClick={handleSave}>
                {intl.formatMessage({
                  id: 'ui.mui.dateRangePicker.confirm.label',
                  defaultMessage: 'OK',
                })}
              </Button>
            </Stack>
          ) : null}
        </Stack>
      </Paper>
    </LocalizationProvider>
  );
};

export default DateRangePickerModal;
