import React, { useEffect, useState, useRef, useCallback } from 'react';
import dayjs from 'dayjs';
import { FilterDateValueType } from '../../ControlBarFilters/controlBarFilters.types';
import { VidMobBox } from '../../../vidMobComponentWrappers';
import DateRangePickerModal from '../../DateRangePickerModal/DateRangePickerModal';
import { presetDateRangesForLandingPage } from '../../../utils/dateRangePickerMUIUtils';

interface Props {
  setIsDropdownOpen: (isDropdownOpen: boolean) => void;
  setSelectedDateRange: (selectedItems: FilterDateValueType) => void;
  selectedDateRange: FilterDateValueType;
  hidePresets?: boolean;
}

export const FilterDropdownDateFilter = (props: Props) => {
  const {
    setIsDropdownOpen,
    setSelectedDateRange,
    selectedDateRange,
    hidePresets,
  } = props;

  const dropdownRef = useRef<HTMLDivElement>(null);
  const [leftPosition, setLeftPosition] = useState('0px');

  const [tempDateRange, setTempDateRange] =
    useState<FilterDateValueType>(selectedDateRange);

  useEffect(() => {
    let [startDate, endDate] = tempDateRange;

    const isStartDateValid = dayjs(startDate).isValid();
    const isEndDateValid = dayjs(endDate).isValid();

    if (!isStartDateValid) {
      startDate = null;
    }

    if (!isEndDateValid) {
      endDate = null;
    }

    setSelectedDateRange([startDate, endDate]);
  }, [tempDateRange]);

  const handleResize = useCallback(() => {
    const dropdownElement = dropdownRef?.current;
    if (!dropdownElement) return;

    const PAGE_WIDTH = window.innerWidth;
    const DROPDOWN_WIDTH = 786;
    const PAGE_MARGIN = 24;

    if (!(dropdownElement instanceof HTMLElement)) return;

    const dropdownRect = dropdownElement?.getBoundingClientRect();
    const spaceToLeft = dropdownRect?.left;
    const spaceToRight = PAGE_WIDTH - spaceToLeft - DROPDOWN_WIDTH;

    // calculate 'left', but want max of 0
    const calculatedPosition = spaceToRight - PAGE_MARGIN;
    const position = calculatedPosition > 0 ? 0 : calculatedPosition;

    setLeftPosition(`${position}px`);
  }, [dropdownRef]);

  useEffect(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div ref={dropdownRef}>
      <VidMobBox
        sx={{
          position: 'absolute',
          top: '100%',
          left: leftPosition,
          zIndex: 3,
        }}
      >
        <DateRangePickerModal
          selectedDateRange={tempDateRange}
          setSelectedDateRange={setTempDateRange}
          setIsDropdownOpen={setIsDropdownOpen}
          presetDateRanges={hidePresets ? [] : presetDateRangesForLandingPage}
          disableFutureDates
          calendars={2}
          hasActionButtons={false}
        />
      </VidMobBox>
    </div>
  );
};
