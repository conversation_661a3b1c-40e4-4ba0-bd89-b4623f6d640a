import React from 'react';
import {
  VidMobBox,
  VidMobTooltip,
  VidMobTypography,
} from '../../vidMobComponentWrappers';
import { ScoringReportType } from '../../creativeScoring/types/rollUpReports.types';
import { useScoringReportContext } from '../../creativeScoring/components/__providers/ScoringReportContext';
import DownloadColumnButton from './DownloadColumnButton';
import { useAdherenceMetadataDownloadV2 } from '../../creativeScoring/components/reports/rollUpReport/queries/useAdherenceMetadataDownloadV2';
import { ColumnIdentifiersType } from '../../creativeScoring/components/ReportsDataGrid/adherenceReportDataGrid/types';

type Props = {
  displayName: string;
  identifiers?: ColumnIdentifiersType;
};

const ColumnHeader = ({ displayName, identifiers }: Props) => {
  const { reportType } = useScoringReportContext();
  const { download } = useAdherenceMetadataDownloadV2();
  const isAdherenceReport = reportType === ScoringReportType.ADHERENCE;

  return (
    <VidMobBox
      sx={{
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        height: '44px !important',
        padding: '10px 6px !important',
        '&:hover .download-button': {
          opacity: 1,
          marginLeft: '8px',
          minWidth: '28px !important',
          minHeight: '28px !important',
          maxWidth: '28px !important',
          maxHeight: '28px !important',
          padding: '4px !important',
        },
        '&:hover .column-inner-text': {
          maxWidth: isAdherenceReport ? '81px' : '125px',
        },
      }}
    >
      <VidMobTooltip title={displayName} placement="top">
        <div style={{ overflow: 'hidden' }}>
          <VidMobTypography
            variant="subtitle2"
            className="column-inner-text"
            sx={{
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: isAdherenceReport ? '118px' : '162px',
            }}
          >
            {displayName}
          </VidMobTypography>
        </div>
      </VidMobTooltip>

      <DownloadColumnButton
        onClick={() => {
          download(identifiers);
        }}
      />
    </VidMobBox>
  );
};

export default ColumnHeader;
