import { styled } from '@mui/material';
import { DataGridPro, DataGridProProps } from '@mui/x-data-grid-pro';
import { buildGroupedColumnSelector } from './utils';

interface StyledDataGridProps extends DataGridProProps {
  groupColors?: string[];
  dynamicParentRowColorStyles: Record<string, string>;
  isDeepNestedColumnTable: boolean;
  isImpressionsAdherenceReport: boolean;
  isLoading?: boolean;
}

export const NestedDataGridV2 = styled(DataGridPro, {
  shouldForwardProp: (prop) =>
    prop !== 'dynamicParentRowColorStyles' &&
    prop !== 'isDeepNestedColumnTable' &&
    prop !== 'isImpressionsAdherenceReport',
})<StyledDataGridProps>(({
  theme,
  columnGroupingModel,
  dynamicParentRowColorStyles,
  isDeepNestedColumnTable,
  isImpressionsAdherenceReport,
  isLoading,
}) => {
  const dataField = buildGroupedColumnSelector(columnGroupingModel);

  const standardCellStyle = {
    '&:nth-of-type(odd)': {
      borderRight: `1px solid ${theme.palette.divider}`,
    },
  };
  const impressionsAdherenceSpecificCellStyle = {
    '&:nth-of-type(odd):not(.MuiDataGrid-columnHeader--filledGroup)': {
      borderRight: 'none !important',
    },
  };
  const specificReportCellStyle =
    isImpressionsAdherenceReport && !isLoading
      ? impressionsAdherenceSpecificCellStyle
      : standardCellStyle;

  return {
    borderLeft: 'none',
    borderRight: 'none',
    borderRadius: 0,
    width: 'fit-content',
    maxWidth: '100%',

    '& .MuiDataGrid-columnHeaderTitleContainer': {
      alignItems: 'flex-start',
    },
    '& .MuiDataGrid-columnHeaders': {
      borderRadius: 0,
      minHeight: 'unset !important',
      maxHeight: 'unset !important',
      lineHeight: 'unset !important',
      contain: 'content',
      flexShrink: 0,
      zIndex: 1,
      boxShadow: '0 2px 6px 0 rgba(15, 15, 15, 0.08)',
      backgroundColor: theme.palette.background.default,
    },
    '& .MuiDataGrid-pinnedColumns': {
      '[data-colindex="0"]': {
        borderRight: `1px solid ${theme.palette.divider}`,
      },
      backgroundColor: theme.palette.secondary.main,
      minHeight: '0 !important', // in order to remove right border in excess virtual scroller height
    },

    '& .MuiDataGrid-pinnedColumnHeaders': {
      boxShadow: '2px 0px 4px -2px rgba(0, 0, 0, 0.21)',
      '[aria-colindex="1"]': {
        borderRight: `1px solid ${theme.palette.divider}`,
      },
      height: '100% !important',
      '[aria-rowindex="1"]': {
        height: '100% !important',
      },
      '& .MuiDataGrid-columnHeader': {
        height: '100% !important',
        padding: '0 !important',
        '& .MuiDataGrid-columnHeaderTitleContainer': {
          padding: '0 10px',
        },
      },
    },
    '& .MuiDataGrid-columnHeadersInner': {
      '[aria-rowindex="1"]': {
        height: 'auto !important',
        contain: 'content',
        flexShrink: 0,
        '& .MuiDataGrid-columnHeader': {
          height: 'auto !important',
          contain: 'content',
          flexShrink: 0,
          borderRight: `1px solid ${theme.palette.divider}`,
          borderBottom: `1px solid ${theme.palette.divider}`,
          padding: 0,
          '& .MuiDataGrid-columnHeaderTitleContainerContent': {
            padding: '0 10px',
          },
          '&:last-child': {
            borderRight: 'none !important',
          },
        },
      },
      '[aria-rowindex="2"]': {
        height: 'auto !important',
        contain: 'content',
        flexShrink: 0,

        '& .MuiDataGrid-columnHeader': {
          height: isDeepNestedColumnTable
            ? '40px !important'
            : '58px !important',
          contain: 'content',
          flexShrink: 0,
          padding: '5px 16px !important',
          alignItems: 'center',
          justifyContent: 'center',
          borderRight: `1px solid ${theme.palette.divider}`,
          ...specificReportCellStyle,
          borderBottom: isDeepNestedColumnTable
            ? `1px solid ${theme.palette.divider}`
            : 0,
          '& .MuiDataGrid-columnHeaderTitleContainer': {
            overflow: 'visible',
          },
          '&:last-child': {
            borderRight: 'none !important',
          },
        },
        [dataField]: {
          borderRight: `1px solid ${theme.palette.divider}`,
        },
      },
      '[aria-rowindex="3"]': {
        '& .MuiDataGrid-columnHeader': {
          height: '58px !important',
          padding: '10px 16px !important',
          borderRight: `1px solid ${theme.palette.divider}`,
          ...specificReportCellStyle,
          '&:last-child': {
            borderRight: 'none !important',
          },
        },
        [dataField]: {
          borderRight: `1px solid ${theme.palette.divider}`,
        },
      },
    },
    '& .MuiDataGrid-virtualScrollerRenderZone': {
      backgroundColor: theme.palette.background.paper,
      [dataField]: {
        borderRight: `1px solid ${theme.palette.divider}`,
      },
    },
    '& .MuiDataGrid-row': {
      backgroundColor: theme.palette.background.paper,
    },
    '& .MuiDataGrid-cell': {
      borderRight: `1px solid ${theme.palette.divider}`,
      ...specificReportCellStyle,
      '&:last-child': {
        borderRight: 'none !important',
      },
    },
    '& .MuiDataGrid-columnHeader--filledGroup .MuiDataGrid-columnHeaderTitleContainer':
      {
        borderBottom: '0px',
      },
    '.MuiDataGrid-columnHeader:focus, .MuiDataGrid-columnHeader:focus-within, .MuiDataGrid-cell:focus-within':
      {
        outline: 'none',
      },

    ...dynamicParentRowColorStyles,
  };
});
