import React from 'react';
import { useIntl } from 'react-intl';
import { GridColumnGroupingModel } from '@mui/x-data-grid-pro';
import {
  gridColumnVisibilityModelSelector,
  useGridApiContext,
} from '@mui/x-data-grid';
import {
  VidMobIconButton,
  VidMobTypography,
  VidMobTooltip,
  VidMobBox,
} from '../../vidMobComponentWrappers';
import {
  ChevronUpIcon,
  ChevronDownIcon,
} from '../../assets/vidmob-mui-icons/general';
import { useScoringReportContext } from '../../creativeScoring/components/__providers/ScoringReportContext';
import { getFieldsToToggleInColumnGroup } from './utils';

interface Props {
  groupId: string;
  displayName?: string;
  columnGroupingModel: GridColumnGroupingModel;
  isOverall?: boolean;
  hasImpressions: boolean;
}

export const ExpandableColumnHeader = ({
  groupId,
  displayName,
  columnGroupingModel,
  hasImpressions,
  isOverall = false,
}: Props) => {
  const intl = useIntl();
  const apiRef = useGridApiContext();
  const { rowColumnState, setRowColumnState } = useScoringReportContext();

  const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);
  const fieldsToToggle = getFieldsToToggleInColumnGroup(
    groupId,
    columnGroupingModel,
    hasImpressions,
  );

  const isGroupCollapsed = fieldsToToggle.every(
    (field) => columnVisibilityModel[field] === false,
  );

  const hasChildren = fieldsToToggle.length > 0;
  const currentCol = rowColumnState?.columns.find(
    (column) => column.id === groupId,
  );

  const onClickArrow = () => {
    if (
      isOverall ||
      fieldsToToggle.length === 0 ||
      typeof currentCol?.isExpanded !== 'boolean'
    ) {
      return;
    }

    setRowColumnState((prevState) => {
      const newColumns = prevState.columns.map((column) => {
        if (column.id === groupId) {
          return {
            id: column.id,
            isExpanded: !column.isExpanded,
          };
        }
        return column;
      });

      return {
        rows: prevState.rows,
        columns: newColumns,
      };
    });

    const newModel = { ...columnVisibilityModel };
    fieldsToToggle.forEach((field) => {
      newModel[field] = !currentCol?.isExpanded;
    });

    apiRef.current?.setColumnVisibilityModel(newModel);
  };

  const getArrowIcon = () => {
    const iconStyle = {
      width: '16px',
      height: '16px',
    };

    if (isGroupCollapsed) {
      return <ChevronUpIcon sx={iconStyle} />;
    }

    return <ChevronDownIcon sx={iconStyle} />;
  };

  const getTooltipText = () => {
    if (isGroupCollapsed) {
      return intl.formatMessage({
        id: 'ui.compliance.rollUpReports.adherence.column.button.tooltip.expand',
        defaultMessage: 'Expand columns',
      });
    }
    return intl.formatMessage({
      id: 'ui.compliance.rollUpReports.adherence.column.button.tooltip.collapse',
      defaultMessage: 'Collapse columns',
    });
  };

  return (
    <VidMobBox
      sx={{
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        padding: '10px 6px',
        maxHeight: '44px !important',
        height: '44px !important',
        '&:hover .download-button': {
          opacity: 1,
          marginLeft: '8px',
          minWidth: '28px !important',
          minHeight: '28px !important',
          maxWidth: '28px !important',
          maxHeight: '28px !important',
          padding: '4px !important',
        },
      }}
    >
      {hasChildren && !isOverall && (
        <VidMobTooltip
          title={getTooltipText()}
          position="above"
          disableInteractive
        >
          <VidMobIconButton
            sx={{
              mr: '6px',
              maxHeight: '32px !important',
              maxWidth: '32px !important',
            }}
            onClick={onClickArrow}
          >
            {getArrowIcon()}
          </VidMobIconButton>
        </VidMobTooltip>
      )}
      <VidMobTooltip title={displayName ?? groupId} position="above">
        <VidMobTypography
          // @ts-ignore
          component="span"
          variant="subtitle2"
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {displayName ?? groupId}
        </VidMobTypography>
      </VidMobTooltip>
    </VidMobBox>
  );
};
