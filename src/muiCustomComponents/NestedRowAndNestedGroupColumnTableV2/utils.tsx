import React from 'react';
import { IntlShape } from 'react-intl';
import {
  GridColumnGroupingModel,
  GridRowParams,
  GridRowsProp,
} from '@mui/x-data-grid-pro';
import NestedColumnHeader from './NestedColumnHeader';
import ColumnHeader from './ColumnHeader';
import { ExpandableColumnHeader } from './ExpandableColumnHeader';
import {
  CollapseColumnsIcon,
  CollapseRowsIcon,
  ExpandColumnsIcon,
  ExpandRowsIcon,
} from '../../assets/vidmob-mui-icons/general';
import {
  AdherenceServerDataResponseColumnModelV2,
  AdherenceServerDataResponseRowModelV2,
} from '../../creativeScoring/components/ReportsDataGrid/adherenceReportDataGrid/types';
import { NestedRowType, FlattenRowType } from './types';
import { GLOBALS } from '../../constants';
import {
  CRITERIA_GROUPS_OVERALL,
  CRITERIA_GROUPS_OVERALL_SHORT,
  DEFAULT_PARENT_ROW_BACKGROUND_COLOR,
  IMPRESSIONS_ID_SUFFIX,
} from '../../creativeScoring/components/ReportsDataGrid/adherenceReportDataGrid/constants/adherenceReportConstants';
import { BREAKDOWN_TYPES } from '../../creativeScoring/components/reports/rollUpReport/rollUpReport.constants';
import { formatPercentage } from '../../utils/formatPercentage';
import fuzzyNumber from '../../utils/fuzzyNumber';
import { formatNumberToStringWithCommas } from '../../utils/formatNumberToStringWithCommas';

const { EM_DASH_UNICODE } = GLOBALS;

export const getNestedRows = (
  rows: FlattenRowType[],
): GridRowsProp<NestedRowType> => {
  const nestedRows = rows.map((row) => {
    const { id, parentId, color, ...others } = row;
    const hierarchy = [];
    if (parentId) {
      hierarchy.push(parentId);
    }

    hierarchy.push(id);
    return {
      hierarchy,
      id,
      color,
      ...others,
    };
  });

  return nestedRows;
};

const extractFields = (children: any[]): string[] =>
  children
    .flatMap((child) =>
      child.field ? [child.field] : extractFields(child.children || []),
    )
    .filter((field) => !checkIfStartsWithOverallColumnId(field));

// hide all except first 1-2 columns depending on report type
const getColumnGroupFieldsToToggle = (
  groupFields: string[],
  hasImpressions: boolean,
): string[] => groupFields.slice(hasImpressions ? 2 : 1);

const getFieldsInColumnGroup = (
  groupId: string,
  columnGroupingModel: GridColumnGroupingModel,
): string[] => {
  const group = columnGroupingModel.find((g) => g.groupId === groupId);
  if (!group) {
    return [];
  }

  return extractFields(group.children);
};

export const getFieldsToToggleInColumnGroup = (
  groupId: string,
  columnGroupingModel: GridColumnGroupingModel,
  hasImpressions: boolean,
) => {
  const groupFields = getFieldsInColumnGroup(groupId, columnGroupingModel);
  return getColumnGroupFieldsToToggle(groupFields, hasImpressions);
};

export const getFieldsToToggleAcrossAllColumnGroups = (
  isDeepNestedColumnTable: boolean,
  columnGroupingModel?: GridColumnGroupingModel,
): string[] => {
  if (!isDeepNestedColumnTable || !columnGroupingModel?.length) {
    return [];
  }

  const fieldsToToggleAcrossAllColumnGroups: string[] = [];

  columnGroupingModel.forEach((group) => {
    const hasImpressions = doesAnyGroupChildHaveImpressions(group.children);
    const groupFields = extractFields(group.children);
    const fieldsToToggle = getColumnGroupFieldsToToggle(
      groupFields,
      hasImpressions,
    );
    fieldsToToggleAcrossAllColumnGroups.push(...fieldsToToggle);
  });

  return fieldsToToggleAcrossAllColumnGroups;
};

const getColumnAverageGroupByCopy = (
  groupByValue: string[] | undefined,
  intl: IntlShape,
): string | undefined => {
  if (!groupByValue || groupByValue.length !== 2) return;
  const { MARKET, WORKSPACE, BRAND } = BREAKDOWN_TYPES;
  const first = groupByValue[0];

  const marketCopy = intl.formatMessage({
    id: 'ui.compliance.rollUpReports.adherence.columnHeader.average.market',
    defaultMessage: 'Market',
  });

  const brandCopy = intl.formatMessage({
    id: 'ui.compliance.rollUpReports.adherence.columnHeader.average.brand',
    defaultMessage: 'Brand',
  });
  const workspaceCopy = intl.formatMessage({
    id: 'ui.compliance.rollUpReports.adherence.columnHeader.average.workspace',
    defaultMessage: 'Workspace',
  });

  if (groupByValue.includes(WORKSPACE) && groupByValue.includes(MARKET)) {
    return first === WORKSPACE ? workspaceCopy : marketCopy;
  }

  if (groupByValue.includes(WORKSPACE) && groupByValue.includes(BRAND)) {
    return first === WORKSPACE ? workspaceCopy : brandCopy;
  }

  if (groupByValue.includes(BRAND) && groupByValue.includes(MARKET)) {
    return first === BRAND ? brandCopy : marketCopy;
  }

  return;
};

export const checkIfIsOverallColumnId = (id: string) =>
  id === CRITERIA_GROUPS_OVERALL || id === CRITERIA_GROUPS_OVERALL_SHORT;

export const checkIfStartsWithOverallColumnId = (id: string) =>
  id.startsWith(CRITERIA_GROUPS_OVERALL_SHORT);

export const findOverallAverageColumn = (
  columns: AdherenceServerDataResponseColumnModelV2[],
) => columns.find((col) => checkIfIsOverallColumnId(col.id));

export const doesGroupHaveImpressions = (impressions?: number) =>
  Boolean(impressions || impressions === 0);

export const doesAnyGroupChildHaveImpressions = (children: any[]): boolean =>
  children.some((child) => doesGroupHaveImpressions(child.impressions));

export const getColumnChildren = (id: string, impressions: boolean) => {
  const children = [{ field: id }];

  if (impressions) {
    children.push({
      field: id + IMPRESSIONS_ID_SUFFIX,
    });
  }
  // TODO MF ADD NORMS HERE WHEN IMPLEMENTED

  return children;
};

export function getNestedColumnGroupsModel(
  columns: AdherenceServerDataResponseColumnModelV2[],
  isDeepNestedColumnTable: boolean,
  intl: IntlShape,
  groupByValue: string[] | undefined,
): GridColumnGroupingModel {
  if (isDeepNestedColumnTable) {
    const topLevelGroups = columns.filter((col) => !col.parentId);

    const groupingModel = topLevelGroups.map((group) => {
      // Find children that have this group as their parent
      const children = columns.filter((col) => col.parentId === group.id);
      const isGroupOverall = checkIfIsOverallColumnId(group.id);
      const hasImpressions = doesGroupHaveImpressions(group.impressions);

      return {
        groupId: group.id,
        headerName: group.displayName,
        renderHeaderGroup: () => (
          <ExpandableColumnHeader
            groupId={group.id}
            displayName={group.displayName}
            columnGroupingModel={groupingModel}
            isOverall={isGroupOverall}
            hasImpressions={hasImpressions}
          />
        ),
        children: [
          isGroupOverall
            ? {
                groupId: 'overall',
                children: getColumnChildren(group.id, hasImpressions),
                renderHeaderGroup: () => (
                  <NestedColumnHeader
                    displayName={''}
                    identifiers={group.identifiers}
                  />
                ),
              }
            : {
                id: 'average' + group.id,
                groupId: 'average' + group.id,
                children: getColumnChildren(group.id, hasImpressions),
                renderHeaderGroup: () => (
                  <NestedColumnHeader
                    identifiers={group.identifiers}
                    displayName={intl.formatMessage(
                      {
                        id: 'ui.compliance.rollUpReports.adherence.columnHeader.average',
                        defaultMessage: 'Workspace Average',
                      },
                      {
                        averageType: getColumnAverageGroupByCopy(
                          groupByValue,
                          intl,
                        ),
                      },
                    )}
                  />
                ),
              },

          ...children.map((child) => ({
            ...child,
            groupId: child.id,
            children: getColumnChildren(child.id, hasImpressions),
            renderHeaderGroup: () => (
              <NestedColumnHeader
                identifiers={child.identifiers}
                displayName={child.displayName || ''}
              />
            ),
          })),
        ],
      };
    }) as GridColumnGroupingModel;

    return groupingModel;
  } else {
    const groupingModel = columns.map((group) => {
      const hasImpressions = doesGroupHaveImpressions(group.impressions);

      const columnGroup = {
        groupId: group.id,
        children: getColumnChildren(group.id, hasImpressions),
        renderHeaderGroup: () => (
          <ColumnHeader
            displayName={group.displayName || ''}
            identifiers={group.identifiers}
          />
        ),
      };

      return columnGroup;
    }) as GridColumnGroupingModel;
    return groupingModel;
  }
}

/**
 * Finds the deepest field in a column group structure
 * @param group The column group to search
 * @returns The deepest field name or null if not found
 */
export const findDeepestField = (group: any): string | null => {
  if ('field' in group) {
    return group.field;
  }
  if ('children' in group && Array.isArray(group.children)) {
    return findDeepestField(group.children[group.children.length - 1]);
  }
  return null;
};

/**
 * Builds a CSS selector string for targeting data grid columns based on their field names
 * @param columnGroupingModel The column grouping model array
 * @returns CSS selector string for grouped columns
 */
export const buildGroupedColumnSelector = (
  columnGroupingModel?: GridColumnGroupingModel,
): string => {
  if (!columnGroupingModel || columnGroupingModel.length === 0) {
    return '& ';
  }

  let dataField = '& ';

  columnGroupingModel.forEach((group, index) => {
    const lastField = findDeepestField(group);

    if (!lastField) {
      console.warn(
        'Error in NestedDataGrid: No valid field found in group:',
        group,
      );
      return;
    }

    if (index > 0) {
      dataField += ', ';
    }

    dataField += `[data-field="${lastField.replace(/"/g, '\\"')}"]`;
  });

  return dataField;
};

export const getParentRowClassName = (params: GridRowParams) => {
  if (
    Array.isArray(params.row?.hierarchy) &&
    params.row.hierarchy.length === 1
  ) {
    const color = params.row.color || DEFAULT_PARENT_ROW_BACKGROUND_COLOR;
    return `group-row-color-${color.replace(/[^a-zA-Z0-9]/g, '')}`;
  }

  return '';
};

export const getDynamicParentRowColorStyles = (
  rows: GridRowsProp<NestedRowType>,
) => {
  // To specify the css for parent rows (or other rows), feel free to add on to what's below
  // Currently we are changing background colors for parent rows
  const styles: Record<string, any> = {};
  rows.forEach((row) => {
    if (Array.isArray(row.hierarchy) && row.hierarchy.length === 1) {
      const color = row.color || DEFAULT_PARENT_ROW_BACKGROUND_COLOR;
      const className = `.group-row-color-${color.replace(/[^a-zA-Z0-9]/g, '')}`;
      styles[className] = {
        backgroundColor: `${color} !important`,
      };
    }
  });
  return styles;
};

export const getTotalCriteriaCountInRows = (
  rows: AdherenceServerDataResponseRowModelV2[],
): number => rows.filter((row) => row.parentId).length || 0;

export const getFirstColumnCellDropdownOptions = (
  intl: IntlShape,
  onClick: (isCollapseClicked: boolean, isColumns: boolean) => void,
) => {
  const menuItemIconSx = { mr: '8px', mt: '3px' };

  return {
    rows: [
      {
        id: 'rows',
        name: intl.messages[
          'ui.compliance.rollUpReports.column.header.dropdown.rows'
        ] as string,
        onClick: () => {},
        isHeader: true,
      },
      {
        id: 'collapseRows',
        name: intl.messages[
          'ui.compliance.rollUpReports.column.header.dropdown.action.collapse.rows'
        ] as string,
        disabled: false,
        onClick: () => onClick(true, false),
        icon: <CollapseRowsIcon sx={menuItemIconSx} fontSize="small" />,
      },
      {
        id: 'expandRows',
        name: intl.messages[
          'ui.compliance.rollUpReports.column.header.dropdown.action.expand.rows'
        ] as string,
        disabled: false,
        onClick: () => onClick(false, false),
        icon: <ExpandRowsIcon sx={menuItemIconSx} fontSize="small" />,
      },
    ],
    columns: [
      {
        id: 'columns',
        name: intl.messages[
          'ui.compliance.rollUpReports.column.header.dropdown.columns'
        ] as string,
        onClick: () => {},
        isHeader: true,
      },
      {
        id: 'collapseColumns',
        name: intl.messages[
          'ui.compliance.rollUpReports.column.header.dropdown.action.collapse.columns'
        ] as string,
        disabled: false,
        onClick: () => onClick(true, true),
        icon: <CollapseColumnsIcon sx={menuItemIconSx} fontSize="small" />,
      },
      {
        id: 'expandColumns',
        name: intl.messages[
          'ui.compliance.rollUpReports.column.header.dropdown.action.expand.columns'
        ] as string,
        disabled: false,
        onClick: () => onClick(false, true),
        icon: <ExpandColumnsIcon sx={menuItemIconSx} fontSize="small" />,
      },
    ],
    divider: [
      {
        id: 'divider',
        name: 'divider',
        onClick: () => {},
        isDivider: true,
      },
    ],
  };
};

export const formatNestedGridCellValue = (
  value: string | number | undefined | null,
  isImpressionsColumn: boolean,
  abbreviateNumber: boolean,
) => {
  const noValue = value === null || value === undefined;

  if (noValue) {
    return EM_DASH_UNICODE;
  } else if (isImpressionsColumn) {
    return abbreviateNumber
      ? fuzzyNumber(value)
      : formatNumberToStringWithCommas(value);
  }
  return formatPercentage(typeof value === 'string' ? Number(value) : value);
};
