import React, { ReactNode, MutableRefObject } from 'react';
import { useIntl } from 'react-intl';
import { useTheme } from '@mui/material';
import { GridApiPro } from '@mui/x-data-grid-pro/models/gridApiPro';
import {
  VidMobSkeleton,
  VidMobStack,
  VidMobTypography,
} from '../../vidMobComponentWrappers';
import { MoreVerticalIcon } from '../../assets/vidmob-mui-icons/general';
import MoreHorizontalDropdown from '../MoreHorizontalDropdown';
import {
  getFieldsToToggleAcrossAllColumnGroups,
  getFirstColumnCellDropdownOptions,
} from './utils';
import { useScoringReportContext } from '../../creativeScoring/components/__providers/ScoringReportContext';
import { RowOrColumnState } from '../../creativeScoring/types/rollUpReports.types';
import { GridColumnGroupingModel } from '@mui/x-data-grid-pro';

interface Props {
  isLoading: boolean;
  columnGroupingModel?: GridColumnGroupingModel;
  nestedColumnWidth: number;
  nestedColumnHeaderTitle: ReactNode;
  apiRef: MutableRefObject<GridApiPro>;
  totalCriteriaCount: number;
  isDeepNestedColumnTable: boolean;
}

const FirstColumnCellWithButtons = ({
  isLoading,
  columnGroupingModel,
  nestedColumnWidth,
  nestedColumnHeaderTitle,
  apiRef,
  totalCriteriaCount,
  isDeepNestedColumnTable,
}: Props) => {
  const intl = useIntl();
  const theme = useTheme();

  const { rowColumnState, setRowColumnState } = useScoringReportContext();

  const onClickCollapseOrExpand = (
    isCollapseClicked: boolean,
    isColumns: boolean,
  ) => {
    if (
      !apiRef.current ||
      !rowColumnState ||
      !rowColumnState.columns ||
      !rowColumnState.rows
    ) {
      return;
    }

    if (isColumns) {
      const columns = [...rowColumnState.columns];
      const newColumns = columns.map((columnObject: RowOrColumnState) => ({
        ...columnObject,
        isExpanded: !isCollapseClicked,
      }));
      setRowColumnState({
        rows: rowColumnState.rows,
        columns: newColumns,
      });

      const fieldsToToggle = getFieldsToToggleAcrossAllColumnGroups(
        isDeepNestedColumnTable,
        columnGroupingModel,
      );
      const newModel: Record<string, boolean> = {};
      fieldsToToggle.forEach((field) => {
        newModel[field] = !isCollapseClicked;
      });

      apiRef.current?.setColumnVisibilityModel(newModel);
    } else {
      const rows = [...rowColumnState.rows];
      const newRows = rows.map((rowObject: RowOrColumnState) => ({
        ...rowObject,
        isExpanded: !isCollapseClicked,
      }));
      setRowColumnState({
        rows: newRows,
        columns: rowColumnState.columns,
      });
    }
  };

  const getDropdownActions = () => {
    const { rows, columns, divider } = getFirstColumnCellDropdownOptions(
      intl,
      onClickCollapseOrExpand,
    );

    if (isDeepNestedColumnTable) {
      return [...columns, ...divider, ...rows];
    } else {
      return [...rows];
    }
  };

  const getContentState = () => {
    if (isLoading) {
      return (
        <VidMobStack
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="46px"
          width="100%"
        >
          <VidMobSkeleton variant="text" width="90%" />
        </VidMobStack>
      );
    }

    return (
      <>
        <VidMobStack
          justifyContent="space-between"
          alignItems="center"
          flexDirection="row"
          sx={{
            width: `${nestedColumnWidth}px`,
            minHeight: isDeepNestedColumnTable ? '86px' : '46px',
            maxHeight: isDeepNestedColumnTable ? '86px' : '46px',
            padding: '12px 16px 12px 20px',
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <VidMobTypography variant="subtitle2">
            {nestedColumnHeaderTitle}
          </VidMobTypography>
          <MoreHorizontalDropdown
            actions={getDropdownActions()}
            isBlueOnActive
            moreIcon={<MoreVerticalIcon />}
            btnSx={{
              border: '1px solid #BDBDBD',
              backgroundColor: '#FFF !important',
              ':hover': {
                backgroundColor: '#DEE6FF !important',
                border: '1px solid #DEE6FF',
              },
            }}
            activeButtonSx={{
              backgroundColor: '#B3C9FF !important',
              border: '1px solid #B3C9FF',
            }}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          />
        </VidMobStack>

        <VidMobStack
          justifyContent="space-between"
          alignItems="center"
          flexDirection="row"
          sx={{
            width: `${nestedColumnWidth}px`,
            minHeight: '58px',
            padding: '12px 16px 12px 20px',
          }}
        >
          <VidMobTypography variant="subtitle2">
            {intl.formatMessage({
              id: 'ui.compliance.rollUpReports.adherence.first.column.cell.subtitle',
              defaultMessage: 'Criteria Average',
            })}
          </VidMobTypography>
          <VidMobTypography variant="caption" color="text.secondary">
            {intl.formatMessage(
              {
                id: 'ui.compliance.rollUpReports.adherence.pinnedColumn.criteria.count',
                defaultMessage: 'criteria',
              },
              {
                count: totalCriteriaCount,
              },
            )}
          </VidMobTypography>
        </VidMobStack>
      </>
    );
  };

  return (
    <VidMobStack
      justifyContent="space-between"
      alignItems="center"
      sx={{
        position: 'absolute',
        top: 0,
        zIndex: 2,
        left: 0,
        width: `${nestedColumnWidth}px`,
        minHeight: '46px',
      }}
    >
      {getContentState()}
    </VidMobStack>
  );
};

export default FirstColumnCellWithButtons;
