import SearchBar from './SearchBar';
import { useIntl } from 'react-intl';
import React, { useState, useMemo } from 'react';
import classnames from 'classnames';
import './SearchableMultiSelectDropdownList.scss';
import {
  VidMobBox,
  VidMobStack,
  VidMobTypography,
  VidMobListItemButton,
  VidMobList,
  VidMobListItem,
  VidMobDivider,
  VidMobListItemText,
  VidMobCheckbox,
} from '../../../vidMobComponentWrappers';

const SearchableMultiSelectDropdownList = ({
  listItems,
  setSelectedItems,
  selectedItems,
  showSelectAll = true,
  isSelectAllBold = true,
  includeSearch,
  maxListHeight = '132px',
  focusSearch = false,
  afterDividerText = '',
  actionButtons,
  isAdvancedSelectionCase = false,
}) => {
  const intl = useIntl();
  const [searchText, setSearchText] = useState('');

  const groupedData = useMemo(() => {
    const groups = [];
    const ungrouped = [];
    let currentGroup = null;
    listItems.forEach((item) => {
      if (item.isGroup) {
        currentGroup = { ...item, children: [] };
        groups.push(currentGroup);
      } else if (currentGroup) {
        currentGroup.children.push(item);
      } else {
        ungrouped.push(item);
      }
    });
    return { groups, ungrouped };
  }, [listItems]);

  const listItemsAfterSearch = useMemo(() => {
    if (!searchText) {
      const flatUngrouped = groupedData.ungrouped;
      const flatGroups = groupedData.groups.reduce((acc, group) => {
        acc.push(group);
        return acc.concat(group.children);
      }, []);
      return [...flatUngrouped, ...flatGroups];
    }
    const lowerQuery = searchText.toLowerCase();
    const filteredUngrouped = groupedData.ungrouped.filter((item) =>
      item.label.toLowerCase().includes(lowerQuery),
    );
    const filteredGroups = groupedData.groups
      .map((group) => {
        const groupMatches = group.label.toLowerCase().includes(lowerQuery);
        const filteredChildren = group.children.filter((child) =>
          child.label.toLowerCase().includes(lowerQuery),
        );
        if (groupMatches || filteredChildren.length > 0) {
          return {
            ...group,
            children: groupMatches ? group.children : filteredChildren,
          };
        }
        return null;
      })
      .filter((g) => g !== null);

    const flatGroups = filteredGroups.reduce((acc, group) => {
      acc.push(group);
      acc.push(...group.children);
      return acc;
    }, []);

    return [...filteredUngrouped, ...flatGroups];
  }, [groupedData, searchText]);

  const visibleItems = listItemsAfterSearch.filter((item) => !item.isGroup);
  const visibleItemsCount = visibleItems.length;

  const isAllSelected = isAdvancedSelectionCase
    ? visibleItemsCount > 0 &&
      visibleItems.every((item) => {
        const selectedItem = selectedItems.find((sI) => sI.id === item.id);
        return selectedItem
          ? selectedItem.checked && !selectedItem.indeterminate
          : false;
      })
    : visibleItemsCount > 0 &&
      visibleItems.every((item) => selectedItems.includes(item.id));

  const isSomeSelected = isAdvancedSelectionCase
    ? visibleItems.some((item) => {
        const selectedItem = selectedItems.find((sI) => sI.id === item.id);
        return selectedItem
          ? selectedItem.checked || selectedItem.indeterminate
          : false;
      })
    : visibleItemsCount > 0 &&
      selectedItems.length > 0 &&
      selectedItems.length < visibleItemsCount;

  const handleListItemClick = (item) => () => {
    let newItems;
    if (isAdvancedSelectionCase) {
      newItems = selectedItems.map((selectedItem) => {
        if (selectedItem.id === item.id) {
          return {
            ...selectedItem,
            checked: !selectedItem.checked,
            indeterminate: false,
          };
        }
        return selectedItem;
      });
      if (!newItems.some((selectedItem) => selectedItem.id === item.id)) {
        newItems = [
          ...newItems,
          {
            id: item.id,
            label: item.label,
            checked: true,
            indeterminate: false,
          },
        ];
      }
    } else {
      if (selectedItems.includes(item.id)) {
        newItems = selectedItems.filter(
          (selectedItem) => selectedItem !== item.id,
        );
      } else {
        newItems = [...selectedItems, item.id];
      }
    }
    setSelectedItems(newItems);
  };

  const handleSelectAllClick = () => {
    const visibleIds = visibleItems.map((item) => item.id);
    if (isAdvancedSelectionCase) {
      if (isAllSelected) {
        const updatedItems = selectedItems.filter(
          (item) => !visibleIds.includes(item.id),
        );
        setSelectedItems(updatedItems);
      } else {
        const updatedVisibleItems = visibleItems.map((item) => {
          const existing = selectedItems.find((si) => si.id === item.id);
          if (existing) {
            return { ...existing, checked: true, indeterminate: false };
          } else {
            return { ...item, checked: true, indeterminate: false };
          }
        });
        const nonVisibleItems = selectedItems.filter(
          (item) => !visibleIds.includes(item.id),
        );
        const updatedItems = [...nonVisibleItems, ...updatedVisibleItems];
        setSelectedItems(updatedItems);
      }
    } else {
      if (isAllSelected) {
        const updatedItems = selectedItems.filter(
          (id) => !visibleIds.includes(id),
        );
        setSelectedItems(updatedItems);
      } else {
        const updatedItems = Array.from(
          new Set([...selectedItems, ...visibleIds]),
        );
        setSelectedItems(updatedItems);
      }
    }
  };

  const selectAllItemClass = classnames({
    'select-all-list-item-bold': isSelectAllBold,
    'select-all-list-item': !isSelectAllBold,
  });

  const renderItemsAfterSearch = () => {
    return listItemsAfterSearch.map((item) => {
      const { label, id, isGroup } = item;
      const itemLabel = intl.messages[label] ? intl.messages[label] : label;
      if (item.isGroup) {
        return (
          <VidMobTypography
            key={id}
            variant="caption"
            sx={{
              fontWeight: 500,
              color: '#757575',
              fontSize: '12px',
            }}
          >
            {itemLabel}
          </VidMobTypography>
        );
      }
      return (
        <VidMobListItemButton
          className="list-item"
          key={id}
          onClick={handleListItemClick(item)}
          sx={{
            '&:hover': {
              backgroundColor: '#E0E0E0',
            },
            borderRadius: '4px',
            height: '36px',
          }}
        >
          <VidMobCheckbox
            checked={
              isAdvancedSelectionCase
                ? selectedItems.find((sI) => sI.id === id)
                  ? selectedItems.find((sI) => sI.id === id).checked
                  : false
                : selectedItems.includes(id)
            }
            indeterminate={
              isAdvancedSelectionCase
                ? selectedItems.find((sI) => sI.id === id)
                  ? selectedItems.find((sI) => sI.id === id).indeterminate
                  : false
                : false
            }
          />
          {item.iconUrl && (
            <VidMobBox
              component="img"
              sx={{
                width: '20px',
                height: '20px',
                marginRight: '4px',
              }}
              src={item.iconUrl}
              alt={item.label}
            />
          )}
          {item.icon && item.icon}
          <VidMobListItemText
            primary={itemLabel}
            primaryTypographyProps={{
              sx: {
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
              },
            }}
            sx={{
              flex: 1,
              width: '100%',
              ml: item.icon ? '8px' : 0,
            }}
          />
        </VidMobListItemButton>
      );
    });
  };

  return (
    <div className="searchable-multi-select-dropdown-list">
      <VidMobList
        sx={{
          '&.MuiList-root': {
            paddingBottom: actionButtons ? '0' : 'initial',
          },
        }}
      >
        <VidMobStack
          flex
          flexDirection="column"
          gap={4}
          sx={{ position: 'sticky', top: 0 }}
        >
          {includeSearch && (
            <SearchBar
              setSearchText={setSearchText}
              searchBarStyles={{ height: '36px' }}
              focus={focusSearch}
            />
          )}
          {showSelectAll && (
            <VidMobListItemButton
              className={selectAllItemClass}
              onClick={handleSelectAllClick}
              sx={{
                height: '36px',
                '&:hover': {
                  backgroundColor: '#E0E0E0',
                },
              }}
            >
              <VidMobCheckbox
                checked={isAllSelected}
                indeterminate={
                  isAdvancedSelectionCase
                    ? isSomeSelected && !isAllSelected
                    : isSomeSelected
                }
              />
              <VidMobTypography variant="body2">
                {
                  intl.messages[
                    'ui.creativeScoring.rollUpReports.multiselectDropdown.label.selectAll'
                  ]
                }
              </VidMobTypography>
            </VidMobListItemButton>
          )}
        </VidMobStack>
        <VidMobDivider sx={{ margin: '8px 8px' }} />
        {afterDividerText && (
          <VidMobTypography
            variant="caption"
            color="#757575"
            sx={{ marginLeft: 9, marginTop: 4, marginBottom: 4 }}
          >
            {afterDividerText}
          </VidMobTypography>
        )}
        <div
          className="multi-select-modal-list-items"
          style={{ maxHeight: maxListHeight }}
        >
          {renderItemsAfterSearch().map((item, index) => (
            <VidMobListItem
              key={index}
              sx={{ padding: '0 8px', fontSize: '14px', lineHeight: '20px' }}
            >
              {item}
            </VidMobListItem>
          ))}
        </div>
      </VidMobList>
      {actionButtons && (
        <>
          <VidMobDivider sx={{ margin: '2px 0' }} />
          <VidMobStack sx={{ padding: '12px 0 12px 16px' }}>
            {actionButtons.map((actionButton, index) => (
              <VidMobBox key={index}>{actionButton}</VidMobBox>
            ))}
          </VidMobStack>
        </>
      )}
    </div>
  );
};

export default SearchableMultiSelectDropdownList;
