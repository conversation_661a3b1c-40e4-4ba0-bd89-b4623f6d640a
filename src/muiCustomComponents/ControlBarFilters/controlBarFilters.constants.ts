import { CONTROLBAR_FILTERS_WORKSPACES_INTL } from '../../creativeAnalytics/__pages/SavedReports/SavedReportsConstants';
import {
  LandingPageFilter,
  LandingPageFilterId,
  ValueType,
} from './controlBarFilters.types';

const PLATFORM_FILTER = {
  id: LandingPageFilterId.PLATFORMS,
  name: 'ui.creativeScoring.criteriaManagementV2.columnHeader.channel',
  type: ValueType.MULTI,
  showSearch: true,
};

const CHANNEL_FILTER = {
  ...PLATFORM_FILTER,
  id: LandingPageFilterId.CHANNELS,
};

const KPI_FILTER = {
  id: LandingPageFilterId.KPI,
  name: 'ui.creativeScoring.criteriaManagementV2.columnHeader.kpi',
  type: ValueType.MULTI,
  showSearch: true,
};

const OBJECTIVE_FILTER = {
  id: LandingPageFilterId.OBJECTIVE,
  name: 'ui.creativeScoring.criteriaManagementV2.columnHeader.objective',
  type: ValueType.MULTI,
  showSearch: true,
};

const CREATED_BY_FILTER_OPTION = {
  id: LandingPageFilterId.CREATED_BY,
  name: 'ui.creativeScoring.rollUpReports.column.owner',
  type: ValueType.MULTI,
  showSearch: true,
};

const BRANDS_FILTER = {
  id: LandingPageFilterId.BRANDS,
  name: 'ui.creativeScoring.reportManagement.filter.v2.brands.dropdown.label',
  type: ValueType.MULTI,
  showSearch: true,
};

const MARKETS_FILTER = {
  id: LandingPageFilterId.MARKETS,
  name: 'ui.creativeScoring.reportManagement.filter.v2.locations.dropdown.label',
  type: ValueType.MULTI,
  showSearch: true,
};

const DATE_CREATED = {
  id: LandingPageFilterId.DATE_CREATED,
  name: 'ui.creativeScoring.rollUpReports.column.dateCreated',
  type: ValueType.DATE_RANGE,
  showSearch: false,
};

const WORKSPACES_FILTER = {
  id: LandingPageFilterId.WORKSPACES,
  name: CONTROLBAR_FILTERS_WORKSPACES_INTL,
  type: ValueType.MULTI,
  showSearch: true,
};

const STATUS_FILTER = {
  id: LandingPageFilterId.STATUS,
  name: 'ui.user.insightsLibraryFilterPanel.grid.status',
  type: ValueType.MULTI,
  showSearch: false,
};

const INSIGHT_TYPE_FILTER = {
  id: LandingPageFilterId.INSIGHT_TYPE,
  name: 'ui.user.insightsLibraryFilterPanel.grid.type',
  type: ValueType.MULTI,
  showSearch: false,
};

export const INSIGHT_CONTROL_BAR_FILTERS: LandingPageFilter[] = [
  BRANDS_FILTER,
  CHANNEL_FILTER,
  KPI_FILTER,
  OBJECTIVE_FILTER,
  CREATED_BY_FILTER_OPTION,
  DATE_CREATED,
  MARKETS_FILTER,
  WORKSPACES_FILTER,
  STATUS_FILTER,
  INSIGHT_TYPE_FILTER,
];
