import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { arrayOf, objectOf, any, string, shape } from 'prop-types';
import './ProjectCreateV2.scss';
import ProjectCreateDeprecation from './ProjectCreateDeprecation';
import ProjectCreateHeader from './Sections/ProjectCreateHeader/ProjectCreateHeader';
import ProjectCreateFooter from './Sections/ProjectCreateFooter/ProjectCreateFooter';
import routesRenderer from '../../../routing/routesRenderer';
import projectCreateSlice from '../../../redux/slices/projectCreate.slice';
import {
  getProjectBeingCreatedId,
  getProjectBeingCreated,
  getProjectCreatedFromInsightBannerVisible,
  getProjectLoadingAPIRequestStatus,
} from '../../../redux/selectors/projectCreate.selectors';
import { getCurrentPartner } from '../../../redux/selectors/partner.selectors';
import InfoIcon from '../../../assets/icons/ic-info-black.svg';
import { PROJECT_CREATE, GLOBALS } from '../../../constants';
import GenericBanner from '../../../components/GenericBanner';
import { useIntl } from 'react-intl';
import { useHistory } from 'react-router-dom';
import { generatePath } from 'react-router';
import siteMap from '../../../routing/siteMap';
import { getProjectCreateFlowSteps } from '../../utils/getProjectCreateFlowSteps';
import AdminProjectCreateError from './AdminProjectCreateError';
import { IntercomAPI as intercomAPI } from 'react-intercom';
import { SuccessIcon } from '../../../assets/vidmob-mui-icons/general';
import { VidMobCircularProgress } from '../../../vidMobComponentWrappers/VidMobCircularProgress';
import { GENERIC_BANNER_TYPES } from '../../../constants/analytics.api.constants';

const { WARNING_WITH_CLOSE, SUCCESS_WITH_CLOSE } = GENERIC_BANNER_TYPES;
const { REDUX_LOADING_STATUS } = GLOBALS;

export const ProjectCreateV2 = (props) => {
  const intl = useIntl();
  const history = useHistory();
  const dispatch = useDispatch();
  const { match, routes } = props;
  let step = match.params?.step;
  const projectBeingCreatedId = useSelector(getProjectBeingCreatedId);
  const projectBeingCreated = useSelector(getProjectBeingCreated);
  const projectCreatedFromInsightBannerVisible = useSelector(
    getProjectCreatedFromInsightBannerVisible,
  );
  const projectLoadingApiStatus = useSelector(
    getProjectLoadingAPIRequestStatus,
  );
  const [isProjectEditBannerOpen, setIsProjectEditBannerOpen] = useState(false);
  const [shouldRenderStep, setShouldRenderStep] = useState(false);
  const [showPermissionError, setShowPermissionError] = useState(false);
  const currentPartner = useSelector(getCurrentPartner);

  useEffect(() => {
    const currentProjectIdFromParams = match.params?.projectId
      ? parseInt(match.params.projectId, 10)
      : null;
    if (
      currentProjectIdFromParams &&
      currentProjectIdFromParams !== projectBeingCreatedId
    ) {
      dispatch(
        projectCreateSlice.actions.loadProjectIntoState({
          projectId: currentProjectIdFromParams,
        }),
      );
    }

    intercomAPI('update', {
      // eslint-disable-next-line camelcase
      hide_default_launcher: true,
    });

    return () => {
      intercomAPI('update', {
        // eslint-disable-next-line camelcase
        hide_default_launcher: false,
      });
    };
  }, []);

  useEffect(() => {
    if (!step && !projectBeingCreated) {
      dispatch(projectCreateSlice.actions.reset());
    }

    const shouldRedirectToActiveProjects =
      (projectBeingCreated?.status === 2 &&
        !projectBeingCreated?.permissions.canProjectAdminUpdateProjects()) ||
      projectBeingCreated?.status > 2;

    if (shouldRedirectToActiveProjects) {
      return history.push(generatePath(siteMap.activeProjects));
    }

    if (step) {
      setShouldRenderStep(true);
    } else {
      setShouldRenderStep(false);

      const steps = getProjectCreateFlowSteps(
        currentPartner,
        projectBeingCreated,
      );
      if (steps) {
        step = steps[0];
        if (
          match?.path?.includes('edit') &&
          (currentPartner.permissions.canUpdatePartnerProjects() ||
            currentPartner.permissions.canPartnerAdminUpdateProjects() ||
            projectBeingCreated?.permissions?.canProjectAdminUpdateProjects())
        ) {
          setShouldRenderStep(true);
          history.push(
            generatePath(siteMap.projectEdit, {
              projectId: projectBeingCreated?.id,
              step,
            }),
          );
        } else if (
          currentPartner.permissions.canCreatePartnerProjects() ||
          currentPartner.permissions.canPartnerAdminCreateProjects()
        ) {
          setShouldRenderStep(true);
          const noStepUrl = generatePath(siteMap.projectCreateV2);
          const newUrl = generatePath(siteMap.projectCreateV2, { step });
          const currentUrl = history.location.pathname;
          // If the user is on the same step, replace the URL
          // We want to replace so we want add one more redundant history entry
          if (currentUrl === noStepUrl || newUrl === currentUrl) {
            history.replace(newUrl);
          } else {
            history.push(newUrl);
          }
        } else {
          setShowPermissionError(true);
        }
      }
    }

    dispatch(
      projectCreateSlice.actions.setProjectCreateStep({ currentStep: step }),
    );

    const shouldSeeProjectEditBanner = () =>
      (step === PROJECT_CREATE.PROJECT_CREATE_STEPS.PROJECT_DETAILS &&
        projectBeingCreated?.status >= 0) ||
      (step === PROJECT_CREATE.PROJECT_CREATE_STEPS.ADMIN_PROJECT_DETAILS &&
        projectBeingCreated?.status === 2);

    if (shouldSeeProjectEditBanner()) {
      setIsProjectEditBannerOpen(true);
    } else {
      setIsProjectEditBannerOpen(false);
    }
  }, [step, projectBeingCreated]);

  const closeProjectEditBanner = () => {
    setIsProjectEditBannerOpen(false);
  };

  const closeInsightsBanner = () => {
    dispatch(projectCreateSlice.actions.hideProjectCreatedFromInsightBanner());
  };

  if (projectLoadingApiStatus === REDUX_LOADING_STATUS.PENDING) {
    return (
      <div className="center-absolute">
        <VidMobCircularProgress />
      </div>
    );
  }

  if (
    !PROJECT_CREATE.PARTNER_TYPES_FOR_PROJECT_CREATE.includes(
      currentPartner?.accountTypeIdentifier,
    )
  ) {
    return <ProjectCreateDeprecation />;
  }

  if (showPermissionError) {
    return <AdminProjectCreateError />;
  }

  return (
    <div className="project-create-wrapper">
      <ProjectCreateHeader />
      <div className="project-create-step">
        <div className="warning-container">
          {projectCreatedFromInsightBannerVisible &&
            step === PROJECT_CREATE.PROJECT_CREATE_STEPS.PROJECT_DETAILS && (
              <GenericBanner
                type={SUCCESS_WITH_CLOSE}
                iconComponent={
                  <SuccessIcon color="success" className="large" />
                }
                message={
                  intl.messages['ui.projectCreate.successCloseBanner.label']
                }
                closeBanner={closeInsightsBanner}
                messageStyle="insights-notification-bar"
              />
            )}
          {isProjectEditBannerOpen && (
            <GenericBanner
              type={WARNING_WITH_CLOSE}
              icon={InfoIcon}
              message={
                intl.messages['ui.projectCreate.warningBanner.disabledFields']
              }
              closeBanner={closeProjectEditBanner}
            />
          )}
        </div>
        {shouldRenderStep && routesRenderer(routes)}
      </div>
      <ProjectCreateFooter />
    </div>
  );
};

ProjectCreateV2.propTypes = {
  match: shape({
    path: string,
  }).isRequired,
  routes: arrayOf(objectOf(any)).isRequired,
};

export default ProjectCreateV2;
