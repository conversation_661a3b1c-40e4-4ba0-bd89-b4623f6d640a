/**
 * This class is specific for CI netting/grouping tags support
 */
import { Tree, Node } from './tree';
import vmErrorLog from './vmErrorLog';
import uniqueBy from './uniqueBy';
import {
  TAGS_COLORS_MAP,
  TAG_TITLES_TO_FILTER,
} from '../constants/ci.constants';
import APP_CONFIG from '../constants/appConfig.constants';
import _ from 'lodash';

export class TagTree extends Tree {
  static ROOT = 'ROOT';

  /**
   * @param {Array} tags each object needs to be {title, parents}
   */
  constructor(tags) {
    super(TagTree.ROOT, {}, Tree.TRAVERSE_METHOD.BreadthFirst);
    this.tags = {};
    this.allParents = [];
    this.parentsMap = {};

    if (tags && tags.length) {
      const filteredTags = tags.filter(
        (tag) => !TAG_TITLES_TO_FILTER.includes(tag.title?.toUpperCase()),
      );
      const processedTags = filteredTags.map((tag) => {
        if (tag.title === 'Architecture') {
          return {
            ...tag,
            parents: tag.parents.filter((parent) => parent !== 'Building'),
          };
        }

        return tag;
      });
      this.buildTree(processedTags);
    }
  }

  /**
   * Build the tree
   *
   * @param {Array} tags all tags {title, parents}
   * @returns {Tree} this object so next call can be nested
   */
  buildTree = (tags) => {
    this._preprocessTags(tags);
    try {
      Object.values(this.tags).forEach((tag) => {
        this._buildRecursively(tag);
      });
    } catch (e) {
      vmErrorLog(
        e,
        'TagTree.buildTree()',
        'cant built tag netting grouping',
        APP_CONFIG.ERROR_TYPES.ERROR,
        { tags: this.tags },
      );
      this._root = new Node(TagTree.ROOT, {});
      this._failsafeTagsBuilding();
    }

    return this;
  };

  /**
   * Return Full Tree
   *
   * @returns
   */

  getFullTree = () => {
    return this.getHighestLevelParentTagsWithData().map((parentTag) => {
      return this._getChildrenRecursively(parentTag);
    });
  };

  getThreeLevelTree = () => {
    return this.getHighestLevelParentTagsWithData().map((parentTag) => {
      if (parentTag.hasChildren) {
        const tagChildren = this.getImmediateChildrenWithData(parentTag).map(
          (middleTag) => {
            if (middleTag.hasChildren) {
              const middleTagChildren =
                this.getChildrenByTagWithData(middleTag);

              return { ...middleTag, children: middleTagChildren };
            }

            return middleTag;
          },
        );

        return { ...parentTag, children: tagChildren };
      }

      return parentTag;
    });
  };

  // I created this function to try to get around the recursion issue
  // I didn't quite figure it out but needed to get the rest of my fixes out
  // so this is here for me to work on later
  getThreeLevelTreeAsObjectForSelection = () => {
    const objectTree = {};
    this.getHighestLevelParentTagsWithData().forEach(
      (highestLevelParentTag) => {
        const highestLevelParentTagForObj = {
          ...highestLevelParentTag,
          id: highestLevelParentTag.title,
          name: highestLevelParentTag.title,
          isOpen: false,
          isSelected: false,
          elementColor:
            TAGS_COLORS_MAP[highestLevelParentTag.genericType?.toLowerCase()],
        };
        if (highestLevelParentTag.hasChildren) {
          const highestLevelChildrenAsObj = {};
          this.getImmediateChildrenWithData(highestLevelParentTag).forEach(
            (middleLevelTag) => {
              const middleLevelTagForObj = {
                ...middleLevelTag,
                id: middleLevelTag.title,
                name: middleLevelTag.title,
                isOpen: false,
                isSelected: false,
                elementColor:
                  TAGS_COLORS_MAP[middleLevelTag.genericType?.toLowerCase()],
              };
              if (middleLevelTag.hasChildren) {
                const middleLevelTagChildrenAsObj = {};
                this.getChildrenByTagWithData(middleLevelTag).forEach(
                  (lowestLevelTag) => {
                    middleLevelTagChildrenAsObj[lowestLevelTag.title] = {
                      ...lowestLevelTag,
                      id: lowestLevelTag.title,
                      name: lowestLevelTag.title,
                      isOpen: false,
                      isSelected: false,
                      elementColor:
                        TAGS_COLORS_MAP[
                          lowestLevelTag.genericType?.toLowerCase()
                        ],
                    };
                  },
                );
                middleLevelTagForObj.children = {
                  ...middleLevelTagChildrenAsObj,
                };
              }

              highestLevelChildrenAsObj[middleLevelTag.title] = {
                ...middleLevelTagForObj,
              };
            },
          );
          highestLevelParentTagForObj.children = {
            ...highestLevelChildrenAsObj,
          };
        }

        objectTree[highestLevelParentTag.title] = {
          ...highestLevelParentTagForObj,
        };
      },
    );

    return objectTree;
  };

  /**
   * Return top level tags
   *
   * @returns {{hasChildren: boolean, title: string}[]} array of tag title and hasChildren flag
   */
  getHighestLevelParentTags = () => {
    return Object.values(this._root.children).map((node) => {
      return {
        title: node.key,
        hasChildren: Boolean(Object.keys(node.children).length),
      };
    });
  };

  /**
   * Return top level tags with
   *
   * @returns {{hasChildren: boolean, title: string}[]} array of tag title and hasChildren flag
   */
  getHighestLevelParentTagsWithData = () => {
    return Object.values(this._root.children).map((node) => {
      return {
        ...node.data,
        hasChildren: Boolean(Object.keys(node.children).length),
      };
    });
  };

  /**
   * Return all immediate children for a tag
   *
   * @param {string} tagName tag name
   * @returns {{hasChildren: boolean, title: string}[]} array of tag title and hasChildren flag
   */
  getImmediateChildrenByTagName = (tagName) => {
    const found = this.find(tagName);
    if (found) {
      const children = Object.values(found.children).map((childTagNode) => ({
        title: childTagNode.key,
        hasChildren: Boolean(Object.keys(childTagNode.children).length),
      }));
      return uniqueBy(children, (child) => child.title);
    }

    return [];
  };

  getImmediateChildrenByTagNameWithData = (tagName) => {
    const found = this.find(tagName);
    if (found) {
      const children = Object.values(found.children).map((childTagNode) => ({
        ...childTagNode.data,
        hasChildren: Boolean(Object.keys(childTagNode.children).length),
      }));
      return uniqueBy(children, (child) => child.title);
    }

    return [];
  };

  /**
   * Return all immediate children for a tag
   *
   * @param {{title}} tag tag object
   * @returns {{hasChildren: boolean, title: string}[]} array of tag title and hasChildren flag
   */
  getImmediateChildren = (tag) => this.getImmediateChildrenByTagName(tag.title);

  /**
   * Return all immediate children for a tag with data
   *
   * @param {{title}} tag tag object
   * @returns {{hasChildren: boolean, title: string}[]} array of tag title and hasChildren flag
   */
  getImmediateChildrenWithData = (tag) =>
    this.getImmediateChildrenByTagNameWithData(tag.title);

  /**
   * Return all children of all level for a tag
   *
   * @param {string} tagName tag name
   * @returns {{hasChildren: boolean, title: string}[]} array of tag title and hasChildren flag
   */
  getChildrenByTagName = (tagName) => {
    const found = this.find(tagName);
    if (found) {
      const children = found.getAllChildrenAsList().map((childTagNode) => ({
        title: childTagNode.key,
        hasChildren: Boolean(Object.keys(childTagNode.children).length),
      }));
      return uniqueBy(children, (child) => child.title);
    }

    return [];
  };

  getChildrenByTagNameWithData = (tagName) => {
    const found = this.find(tagName);
    if (found) {
      const children = found.getAllChildrenAsList().map((childTagNode) => ({
        ...childTagNode.data,
        hasChildren: Boolean(Object.keys(childTagNode.children).length),
      }));
      return uniqueBy(children, (child) => child.title);
    }

    return [];
  };

  /**
   * Return all children of all level for a tag
   *
   * @param {{title}} tag {title, ...}
   * @returns {{hasChildren: boolean, title: string}[]} array of tag title and hasChildren flag
   */
  getChildrenByTag = (tag) => {
    return this.getChildrenByTagName(tag.title);
  };

  getChildrenByTagWithData = (tag) => {
    return this.getChildrenByTagNameWithData(tag.title);
  };

  /**
   * Traverse the tree and find all the lowest level children
   *
   * @returns {{hasParents: boolean, title: *}[]} array of no child nodes
   */
  getLowestLevelChildren = () => {
    const noChild = [];
    const find = (node) => {
      if (!Object.keys(node.children).length) {
        noChild.push(node);
      }
    };

    this.invokes(find);

    return noChild.map((childTagNode) => ({
      title: childTagNode.key,
      hasParents: Boolean(Object.keys(childTagNode.parents).length),
    }));
  };

  /**
   * Get parents of a node by its name
   *
   * @param {string} tagName tag name
   * @returns {Array<Array<{hasParents: boolean, title: string}>>} array of array of parents of node
   */
  getAllParentsByTagName = (tagName) => {
    const found = this.find(tagName);
    if (found) {
      const parents = found.getAllParentsAsListOfList().map((list) =>
        list.map((node) => ({
          title: node.key,
          hasParents: Boolean(Object.keys(node.parents).length),
        })),
      );
      return parents.map((list) => uniqueBy(list, (tag) => tag.title));
    }

    return [];
  };

  getAllParents = (tag) => {
    return this.getAllParentsByTagName(tag.title);
  };

  // *************
  // **** PRIVATE*
  // *************
  _getTagsFromNames = (tagNames) =>
    tagNames.map((parentTagName) => this.tags[parentTagName]).filter(Boolean);

  // given a tag, get all of its parents and find the immediate parent by excluding all parents of parents
  _getImmediateParents = (tag) => {
    const parentsParent = tag.allParents
      .map((parent) => this.tags[parent])
      .filter(Boolean)
      .map(this._getAllParents)
      .flat()
      .reduce((acc, curr) => {
        acc[curr] = true;
        return acc;
      }, {});
    const immediateParentNames = tag.allParents.filter(
      (item) => !parentsParent[item],
    );
    return this._getTagsFromNames(immediateParentNames);
  };

  _getAllParents = (tag) => {
    const parents = tag.allParents
      .map((parent) => this.tags[parent])
      .filter(Boolean);
    return [
      ...tag.allParents,
      ...parents.map((parent) => this._getAllParents(parent)),
    ].flat();
  };

  _getChildrenRecursively = (tag) => {
    if (tag.hasChildren) {
      const tagChildren = this.getImmediateChildrenWithData(tag).map(
        (tagChild) => {
          return this._getChildrenRecursively(tagChild);
        },
      );
      return { ...tag, children: tagChildren };
    }

    return tag;
  };

  /**
   * Add a node into tree, a node can have multiple parents so the add function will have to build the tree as goes, build recursively
   *
   * @param {object} tag data
   * @returns {Node} node object
   */
  _buildRecursively = (tag) => {
    const found = this.find(tag.title);
    if (found) {
      return found;
    }

    const node = new Node(tag.title, tag);
    const immediateParents = this._getImmediateParents(tag);
    if (immediateParents.length) {
      immediateParents
        .map((parent) => this._buildRecursively(parent))
        .forEach((parent) => {
          node.parents[parent.key] = parent;
          parent.children[node.key] = node;
        });
    } else {
      // when node doesnt have parent - aka root
      this.add(node, TagTree.ROOT);
    }

    return node;
  };

  /**
   * Pre-process tags, build a map of all tags that are keyed by its title
   *
   * @param {Array} tags array of tag objects
   * @returns {object} return processed tags
   * @private
   */
  _preprocessTags = (tags) => {
    tags = tags.map((tag) => {
      tag = {
        ...tag,
        allParents: tag.parents
          ? tag.parents.filter((parentTitle) => parentTitle !== tag.title)
          : [],
      };

      if (tag.allParents) {
        tag.parentsMap = tag.allParents.reduce((acc, curr) => {
          acc[curr] = true;
          return acc;
        }, {});
      } else {
        tag.parentsMap = {};
      }

      return tag;
    });
    this.tags = tags.reduce((acc, curr) => {
      acc[curr.title] = curr;
      return acc;
    }, {});
    return this._preprocessForCustomTags();
  };

  _preprocessForCustomTags = () => {
    Object.values(this.tags).forEach((tag) => {
      if (tag.customTagGroupTags) {
        tag.customTagGroupTags.forEach((customTagGroupTag) => {
          if (this.tags[customTagGroupTag.title]) {
            tag.children = tag.children || [];
            tag.hasChildren = true;
            this.tags[customTagGroupTag.title] = {
              ...this.tags[customTagGroupTag.title],
              parents: this.tags[customTagGroupTag.title].parents
                ? [...this.tags[customTagGroupTag.title].parents]
                : [],
              allParents: this.tags[customTagGroupTag.title].allParents
                ? [...this.tags[customTagGroupTag.title].allParents]
                : [],
            };
            const childTag = this.tags[customTagGroupTag.title];
            if (!_.isEqual(childTag.parentsMap, tag.parentsMap)) {
              tag.children.push(childTag);
            }
          }
        });
      }
    });

    return this.tags;
  };

  // add everything to root
  _failsafeTagsBuilding = () => {
    Object.values(this.tags).forEach((tag) => {
      const tagNode = new Node(tag.title, tag);
      this.add(tagNode, TagTree.ROOT);
    });
  };
}
