import {
  AVERAGE_ID_MAP,
  COMPARE_REPORT_ACCOUNT_AVERAGE_COLUMN_NAME,
} from '../constants/creativeAnalytics.constants';
import {
  formatCompareReportKpiAndPercentLift,
  formatNormativeInsightMetadataKpiAndPercentLift,
} from './formatNumberToStringWithCommas';
import { REPORT_TABLE_VIEW } from '../constants/ci.constants';
import { getElementPresenceReportColumnTitle } from '../creativeAnalytics/__pages/ImpactReport/ImpactReportDataGrid/utils/getElementPresenceReportColumnTitle';
import { makeTitleCase } from '../creativeAnalytics/__pages/ImpactReport/ImpactReportDataGrid/utils/adjustStringCase';

const getColumnName = (group, sharedFilterObject) => {
  const groupName = group?.group?.name;
  if (groupName) {
    if (sharedFilterObject.elementFilterSelections?.[groupName]) {
      return getElementPresenceReportColumnTitle(
        sharedFilterObject.elementFilterSelections[group.group.name],
      );
    }

    return groupName;
  }

  return group.title;
};

export const getBrandInsightHeaderData = ({
  keyData,
  sharedFilterObject,
  intl,
}) => {
  const { reportTableViewId, statSettings } = sharedFilterObject;
  const statSettingDirection = statSettings?.direction;
  const isKPIReportTableView = reportTableViewId === REPORT_TABLE_VIEW.KPI.id;

  return keyData?.groups?.map((group, index) => {
    const isAccountAverageHeader =
      group.title === COMPARE_REPORT_ACCOUNT_AVERAGE_COLUMN_NAME;
    const {
      kpiValue,
      percentLift,
      isBeneficialLift,
      isNegativeLift,
      shouldShowLiftIcon,
    } = formatCompareReportKpiAndPercentLift(
      group,
      keyData?.selectedKpi,
      statSettingDirection,
      reportTableViewId,
      false,
      true,
      isAccountAverageHeader,
    );

    return {
      title: group.title,
      id:
        group.id ||
        AVERAGE_ID_MAP[group.title] ||
        group.group?.id ||
        group.title,
      className: index === 0 && 'sticky-column superimposed',
      columnName: getColumnName(group, sharedFilterObject),
      filterSelectionId: group.id,
      isAccountAverageHeader,
      columnData: [
        {
          name: intl.messages['ui.user.compareReports.columnHeaderCell.kpiAvg'],
          value: kpiValue,
          hideValue: isKPIReportTableView,
        },
        {
          name: intl.messages[
            'ui.user.compareReports.columnHeaderCell.percentLift'
          ],
          value: percentLift,
          isNegativeLift,
          isBeneficialLift,
          shouldShowLiftIcon,
        },
      ],
    };
  });
};

export const getBrandInsightElementsData = ({
  headerData,
  keyData,
  statSettingDirection,
  reportTableView,
}) => {
  const isKPIReportTableView = reportTableView?.id === REPORT_TABLE_VIEW.KPI.id;

  return keyData?.elements?.map((element) => {
    return {
      title: element.title,
      genericType: element.genericType,
      cellData: headerData?.map((header, index) => {
        const groupData =
          element.groups[header.id] || element.groups[header.title] || {};
        const kpi = isKPIReportTableView ? element : keyData?.selectedKpi;
        const {
          kpiValue,
          percentLift,
          isBeneficialLift,
          isNegativeLift,
          shouldShowLiftIcon,
        } = formatCompareReportKpiAndPercentLift(
          groupData,
          kpi,
          statSettingDirection,
          reportTableView,
          header.isAccountAverageHeader,
        );

        return {
          className: index === 0 && 'sticky-column superimposed',
          formattedValue: kpiValue,
          percentLift: {
            value: percentLift,
            isBeneficialLift,
            isNegativeLift,
            shouldShowLiftIcon,
          },
        };
      }),
    };
  });
};

export const getNormativeInsightHeaderData = ({
  type,
  normativeMetaData,
  intl,
}) => {
  const colName =
    intl.messages[
      `ui.creative.intelligence.reportTableView.${type.toLowerCase()}`
    ] +
    ' ' +
    intl.messages['ui.creative.intelligence.reportTableView.custom'];
  const colId = AVERAGE_ID_MAP[colName];
  const { levelKpiValue } = normativeMetaData;
  const {
    kpiValue,
    percentLift,
    isBeneficialLift,
    isNegativeLift,
    shouldShowLiftIcon,
  } = formatNormativeInsightMetadataKpiAndPercentLift(levelKpiValue);

  return [
    {
      title: colName,
      id: colId,
      className: 'sticky-column superimposed',
      columnName: colName,
      filterSelectionId: colId,
      isAccountAverageHeader: true,
      columnData: [
        {
          name: intl.messages['ui.user.compareReports.columnHeaderCell.kpiAvg'],
          value: kpiValue,
          hideValue: false,
        },
        {
          name: intl.messages[
            'ui.user.compareReports.columnHeaderCell.percentLift'
          ],
          value: percentLift,
          isNegativeLift,
          isBeneficialLift,
          shouldShowLiftIcon,
        },
      ],
    },
  ];
};

const getNormativeInsightsFromElementList = (elements) => {
  return elements.map((element) => {
    const {
      elementKpiValue,
      elementPercentLift,
      elementTagType,
      elementValue,
    } = element;

    const {
      kpiValue,
      percentLift,
      isBeneficialLift,
      isNegativeLift,
      shouldShowLiftIcon,
    } = formatNormativeInsightMetadataKpiAndPercentLift(
      elementKpiValue,
      elementPercentLift,
    );
    const title = makeTitleCase(elementValue);
    return {
      title: title,
      genericType: elementTagType,
      cellData: [
        {
          className: 'sticky-column superimposed',
          formattedValue: kpiValue,
          percentLift: {
            value: percentLift,
            isBeneficialLift,
            isNegativeLift,
            shouldShowLiftIcon,
          },
        },
      ],
    };
  });
};

export const getNormativeInsightElementsData = (normativeMetaData) => {
  const {
    elementKpiValue,
    elementPercentLift,
    elementTagType,
    elementValue,
    elements,
  } = normativeMetaData;

  const elementList = elements
    ? elements
    : [{ elementKpiValue, elementPercentLift, elementTagType, elementValue }];

  return getNormativeInsightsFromElementList(elementList);
};
