import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import { MINIMUM_DATE } from '../components/ReportFilters/constants';
import { ALL_TIME_PRESET_ID } from '../components/ReportFilters/components/ReportDateRangePicker';

dayjs.extend(quarterOfYear);
const today = dayjs();

const getDateRange = ({ startDate, endDate }) => {
  return [startDate, endDate];
};

const getDateRangeKey = ([startDate, endDate]) => {
  return `${startDate.format('YYYY-MM-DD')}-${endDate.format('YYYY-MM-DD')}`;
};

/**
 * Adjusts the provided date to midday time (12:00:00).
 *
 * @param {dayjs.Dayjs} date - A dayjs date object.
 * @returns {dayjs.Dayjs} - The adjusted dayjs date object with time set to 12:00:00.
 */
const getMiddayTime = (date) => {
  return date.hour(12).minute(0).second(0);
};

/**
 * Returns the UTC date range as a tuple, set to midday (12:00:00) for the given start and end dates.
 *
 * @param {object} params - The parameters.
 * @param {Date|string} params.startDate - The start date.
 * @param {Date|string} params.endDate - The end date.
 * @returns {Array} A tuple containing the adjusted `startDate` and `endDate` set to midday in UTC.
 */
export const getUtcDateRangeTuple = ({ startDate, endDate }) => {
  return [
    getMiddayTime(dayjs.utc(startDate)),
    getMiddayTime(dayjs.utc(endDate)),
  ];
};

const getPreviousQuarter = () => {
  const startDate = today.startOf('quarter').subtract(3, 'months');
  const endDate = today.startOf('quarter').subtract(1, 'day');
  return getDateRange({ startDate, endDate });
};

const getPreviousYear = () => {
  const startDate = today.startOf('year').subtract(1, 'year');
  const endDate = today.startOf('year').subtract(1, 'day');
  return getDateRange({ startDate, endDate });
};

export const getLastThreeClosedMonths = () => {
  const endDate = today.subtract(1, 'month').endOf('month');
  const startDate = today.subtract(3, 'month').startOf('month');
  return getDateRange({ startDate, endDate });
};

const getCurrentMonth = () => {
  const endDate = today.subtract(1, 'day');
  const startDate = endDate.startOf('month');
  return getDateRange({ startDate, endDate });
};

// utils where end date is yesterday
const yesterday = today.subtract(1, 'day');

const getLastThirtyDaysFromYesterday = () => {
  const endDate = yesterday;
  const startDate = endDate.subtract(30, 'day');
  return getDateRange({ startDate, endDate });
};

export const getLastThreeMonthsFromYesterday = () => {
  const endDate = yesterday;
  const startDate = endDate.subtract(3, 'month');
  return getDateRange({ startDate, endDate });
};

const getQuarterToYesterday = () => {
  const startDate = today.startOf('quarter');
  const endDate = yesterday;
  return getDateRange({ startDate, endDate });
};

const getYearToYesterday = () => {
  const endDate = yesterday;
  const startDate = endDate.startOf('year');
  return getDateRange({ startDate, endDate });
};

// utils where end date is today
export const getLastThirtyDaysFromToday = () => {
  const endDate = today;
  const startDate = endDate.subtract(30, 'day');
  return getDateRange({ startDate, endDate });
};

const getLastThreeMonthsFromToday = () => {
  const endDate = today;
  const startDate = endDate.subtract(3, 'month');
  return getDateRange({ startDate, endDate });
};

const getQuarterToDate = () => {
  const startDate = today.startOf('quarter');
  const endDate = today;
  return getDateRange({ startDate, endDate });
};

const getYearToDate = () => {
  const endDate = today;
  const startDate = endDate.startOf('year');
  return getDateRange({ startDate, endDate });
};

const getAllTime = () => {
  const startDate = dayjs(MINIMUM_DATE);
  const endDate = today;
  return getDateRange({ startDate, endDate });
};

// for reports (analytics and scoring)
export const presetDateRangesForReportPage = [
  {
    label: 'ui.mui.dateRangePicker.presets.lastThirtyDays',
    getValue: () => getLastThirtyDaysFromYesterday(),
  },
  {
    label: 'ui.mui.dateRangePicker.presets.lastThreeMonths',
    getValue: () => getLastThreeMonthsFromYesterday(),
  },
  {
    label: 'ui.mui.dateRangePicker.presets.quarterToDate',
    getValue: () => getQuarterToYesterday(),
  },
  {
    label: 'ui.mui.dateRangePicker.presets.previousQuarter',
    getValue: () => getPreviousQuarter(),
  },
  {
    label: 'ui.mui.dateRangePicker.presets.yearToDate',
    getValue: () => getYearToYesterday(),
  },
  {
    label: 'ui.mui.dateRangePicker.presets.previousYear',
    getValue: () => getPreviousYear(),
  },
  {
    label: 'ui.mui.dateRangePicker.presets.custom',
    getValue: () => getCurrentMonth(),
  },
];

// for reports landing page (analytics and scoring)
export const presetDateRangesForLandingPage = [
  {
    label: 'ui.mui.dateRangePicker.presets.lastThirtyDays',
    getValue: getLastThirtyDaysFromToday,
  },
  {
    label: 'ui.mui.dateRangePicker.presets.lastThreeMonths',
    getValue: getLastThreeMonthsFromToday,
  },
  {
    label: 'ui.mui.dateRangePicker.presets.quarterToDate',
    getValue: getQuarterToDate,
  },
  {
    label: 'ui.mui.dateRangePicker.presets.previousQuarter',
    getValue: getPreviousQuarter,
  },
  {
    label: 'ui.mui.dateRangePicker.presets.yearToDate',
    getValue: getYearToDate,
  },
  {
    label: 'ui.mui.dateRangePicker.presets.previousYear',
    getValue: getPreviousYear,
  },
  {
    label: 'ui.mui.dateRangePicker.presets.custom',
    getValue: getCurrentMonth,
  },
];

export const allTimePreset = {
  label: 'ui.mui.dateRangePicker.presets.allTime',
  getValue: getAllTime,
  id: ALL_TIME_PRESET_ID,
};

export const getPresetDateRangeLabelFromDateRange = (
  presetDateRanges,
  [startDate, endDate],
) => {
  const dateRangeKey = getDateRangeKey([startDate, endDate]);
  const presetValue = presetDateRanges.find(
    (preset) => getDateRangeKey(preset.getValue()) === dateRangeKey,
  );
  return presetValue ? presetValue.label : null;
};

export const convertDateToISODateString = (date) => {
  return dayjs(date).toISOString();
};

export const convertDateRangeToISODateStringObject = (dateRange) => {
  return {
    startDate: convertDateToISODateString(dateRange[0]),
    endDate: convertDateToISODateString(dateRange[1]),
  };
};

export const convertDateToMMMDYYYYString = (date, useComma) => {
  const format = useComma ? 'MMM D, YYYY' : 'MMM D YYYY';
  if (typeof date === 'object') {
    return dayjs(date).format(format);
  }

  if (typeof date === 'string') {
    return dayjs(date.substring(0, 19), 'YYYY-MM-DDTHH:mm:ss').format(format);
  }
};

export const convertDateToYYYYMMDDString = (date) => {
  return dayjs(date).format('YYYY-MM-DD');
};

export const isSameDateRange = (dateRange1, dateRange2) => {
  return (
    dateRange1[0]?.isSame(dateRange2[0], 'day') &&
    dateRange1[1]?.isSame(dateRange2[1], 'day')
  );
};

export const convertDateToDayJSObject = (date) => {
  return dayjs(date);
};
