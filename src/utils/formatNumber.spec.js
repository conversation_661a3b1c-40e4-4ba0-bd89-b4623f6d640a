import {
  formatCompareReportKpiAndPercentLift,
  formatNormativeInsightMetadataKpiAndPercentLift,
  formatNumberToStringWithCommas,
} from './formatNumberToStringWithCommas';
import { ORIENTATIONS, REPORT_TABLE_VIEW } from '../constants/ci.constants';
import { GLOBALS } from '../constants';

const dummyInverseHealthKpi = { inverseHealth: true, id: 1 };
const dummyNonInverseHealthKpi = { inverseHealth: false, id: 1 };
const dummyAccountAverageColumnHeaderData = {
  formattedValue: '9.7%',
  statLiftAgainst: { 1: { element: 0, group: 0 } },
};
const dummyAccountAverageKPIViewCellData = {
  formattedValue: '13.5%',
  statLiftAgainst: { 1: { element: 0, group: 0 } },
};
const dummyColumnHeaderKPIViewCellData = {
  formattedValue: null,
  statLiftAgainst: {
    1: { element: -22.***************, group: -22.*************** },
  },
};
const dummyColumnHeaderCellData = {
  formattedValue: '10.7%',
  statLiftAgainst: {
    1: { element: -22.***************, group: -22.*************** },
  },
};
const dummyZeroPercentLiftCellData = {
  formattedValue: '9.7%',
  statLiftAgainst: { 1: { element: 0, group: 0 } },
};
const dummyColumnCellDataNegative = {
  formattedValue: '10.7%',
  statLiftAgainst: {
    1: { element: -22.***************, group: -22.*************** },
  },
};
const dummyColumnCellDataPositive = {
  formattedValue: '10.7%',
  statLiftAgainst: {
    1: { element: 22.***************, group: 22.*************** },
  },
};
const dummyNormativeAverageColumnHeaderKPIValue = 0.********;
const dummyNormativeAverageColumnHeaderKPISmallValue = 0.********;
const dummyNormativeElementKPIValue = 1.0122392;
const dummyNormativeElementPositivePercentLift = 47.86692;
const dummyNormativeElementKPISmallValue = 0.********;
const dummyNormativeElementPositivePercentLiftSmallValues = 450;
const dummyNormativeElementZeroPercentLift = 0;
const dummyNormativeElementNegativePercentLift = -33.3393333;

const expectedAccountAverageFormattedData = {
  kpiValue: '9.7%',
  percentLift: GLOBALS.EM_DASH_UNICODE,
  shouldShowLiftIcon: false,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedAccountAverageKPIViewFormattedData = {
  kpiValue: '13.5%',
  percentLift: GLOBALS.EM_DASH_UNICODE,
  shouldShowLiftIcon: false,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedColumnHeaderKPIViewFormattedData = {
  kpiValue: GLOBALS.EM_DASH_UNICODE,
  percentLift: GLOBALS.EM_DASH_UNICODE,
  shouldShowLiftIcon: false,
  isNegativeLift: true,
  isBeneficialLift: true,
};
const expectedColumnHeaderFormattedData = {
  kpiValue: '10.7%',
  percentLift: GLOBALS.EM_DASH_UNICODE,
  shouldShowLiftIcon: false,
  isNegativeLift: true,
  isBeneficialLift: true,
};
const expectedDefaultFormattedData = {
  kpiValue: GLOBALS.EM_DASH_UNICODE,
  percentLift: '0%',
  shouldShowLiftIcon: false,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedZeroPercentLiftFormattedData = {
  kpiValue: '9.7%',
  percentLift: '0%',
  shouldShowLiftIcon: false,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedColumnInversePositiveLiftFormattedData = {
  kpiValue: '10.7%',
  percentLift: '22.35%',
  shouldShowLiftIcon: true,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedColumnInverseNegativeLiftFormattedData = {
  kpiValue: '10.7%',
  percentLift: '22.35%',
  shouldShowLiftIcon: true,
  isNegativeLift: true,
  isBeneficialLift: true,
};
const expectedColumnNonInversePositiveLiftFormattedData = {
  kpiValue: '10.7%',
  percentLift: '22.35%',
  shouldShowLiftIcon: true,
  isNegativeLift: false,
  isBeneficialLift: true,
};
const expectedColumnNonInverseNegativeLiftFormattedData = {
  kpiValue: '10.7%',
  percentLift: '22.35%',
  shouldShowLiftIcon: true,
  isNegativeLift: true,
  isBeneficialLift: false,
};
const expectedNormativeAverageKPIFormattedData = {
  kpiValue: '0.68%',
  percentLift: GLOBALS.EM_DASH_UNICODE,
  shouldShowLiftIcon: false,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedNormativeAverageKPIFormattedDataSmallValue = {
  kpiValue: '< 0.01%',
  percentLift: GLOBALS.EM_DASH_UNICODE,
  shouldShowLiftIcon: false,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedNormativeElementKPIFormattedDataPositiveLift = {
  kpiValue: '1.01%',
  percentLift: '47.87%',
  shouldShowLiftIcon: true,
  isNegativeLift: false,
  isBeneficialLift: true,
};
const expectedNormativeElementKPIFormattedDataPositiveLiftSmallValues = {
  kpiValue: '< 0.01%',
  percentLift: '450%',
  shouldShowLiftIcon: true,
  isNegativeLift: false,
  isBeneficialLift: true,
};
const expectedNormativeElementKPIFormattedDataZeroPercentLift = {
  kpiValue: '1.01%',
  percentLift: '0%',
  shouldShowLiftIcon: false,
  isNegativeLift: false,
  isBeneficialLift: false,
};
const expectedNormativeElementKPIFormattedDataNegativeLift = {
  kpiValue: '1.01%',
  percentLift: '33.34%',
  shouldShowLiftIcon: true,
  isNegativeLift: true,
  isBeneficialLift: false,
};

describe('formatNumberToStringWithCommas', () => {
  test('Should return a formatted string representation of a number', () => {
    expect.assertions(1);
    const formattedNumber = formatNumberToStringWithCommas(95039);
    expect(formattedNumber).toBe('95,039');
  });
});

describe('formatCompareReportKpiAndPercentLift should hide percent lift for', () => {
  test('account average column header', () => {
    expect.assertions(1);
    const formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyAccountAverageColumnHeaderData,
      dummyInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.ELEMENT.id,
      true,
      true,
      true,
    );
    expect(formattedCellData).toStrictEqual(
      expectedAccountAverageFormattedData,
    );
  });

  test('cells in account average column and kpi report table view', () => {
    expect.assertions(1);
    const formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyAccountAverageKPIViewCellData,
      dummyInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.KPI.id,
      true,
      false,
      false,
    );
    expect(formattedCellData).toStrictEqual(
      expectedAccountAverageKPIViewFormattedData,
    );
  });

  test('column headers in kpi report table view', () => {
    expect.assertions(1);
    const formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyColumnHeaderKPIViewCellData,
      dummyInverseHealthKpi,
      ORIENTATIONS.HORIZONTAL,
      REPORT_TABLE_VIEW.KPI.id,
      false,
      true,
      false,
    );
    expect(formattedCellData).toStrictEqual(
      expectedColumnHeaderKPIViewFormattedData,
    );
  });

  test('column headers with vertical percent lift', () => {
    expect.assertions(1);
    const formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyColumnHeaderCellData,
      dummyInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.DURATION.id,
      false,
      true,
      false,
    );
    expect(formattedCellData).toStrictEqual(expectedColumnHeaderFormattedData);
  });
});

describe('formatCompareReportKpiAndPercentLift should', () => {
  test('return blank kpi value when no cell data passed', () => {
    expect.assertions(1);
    const formattedCellData = formatCompareReportKpiAndPercentLift(
      null,
      dummyInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.DURATION.id,
      false,
    );
    expect(formattedCellData).toStrictEqual(expectedDefaultFormattedData);
  });

  test('hide lift icon when 0% lift', () => {
    expect.assertions(1);
    const formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyZeroPercentLiftCellData,
      dummyInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.DURATION.id,
    );
    expect(formattedCellData).toStrictEqual(
      expectedZeroPercentLiftFormattedData,
    );
  });

  test('correctly format cell data for inverse health kpi', () => {
    expect.assertions(2);
    let formattedCellData;
    formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyColumnCellDataPositive,
      dummyInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.DURATION.id,
    );
    expect(formattedCellData).toStrictEqual(
      expectedColumnInversePositiveLiftFormattedData,
    );

    formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyColumnCellDataNegative,
      dummyInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.DURATION.id,
    );
    expect(formattedCellData).toStrictEqual(
      expectedColumnInverseNegativeLiftFormattedData,
    );
  });

  test('correctly format cell data for non inverse health kpi', () => {
    expect.assertions(2);
    let formattedCellData;
    formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyColumnCellDataPositive,
      dummyNonInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.DURATION.id,
    );
    expect(formattedCellData).toStrictEqual(
      expectedColumnNonInversePositiveLiftFormattedData,
    );

    formattedCellData = formatCompareReportKpiAndPercentLift(
      dummyColumnCellDataNegative,
      dummyNonInverseHealthKpi,
      ORIENTATIONS.VERTICAL,
      REPORT_TABLE_VIEW.DURATION.id,
    );
    expect(formattedCellData).toStrictEqual(
      expectedColumnNonInverseNegativeLiftFormattedData,
    );
  });
});

describe('formatNormativeInsightMetadataKpiAndPercentLift should correctly format', () => {
  test('normative average column header KPI value', () => {
    expect.assertions(1);
    const formattedCellData = formatNormativeInsightMetadataKpiAndPercentLift(
      dummyNormativeAverageColumnHeaderKPIValue,
    );
    expect(formattedCellData).toStrictEqual(
      expectedNormativeAverageKPIFormattedData,
    );
  });

  test('normative average column header KPI with a value smaller than 0.01%', () => {
    expect.assertions(1);
    const formattedCellData = formatNormativeInsightMetadataKpiAndPercentLift(
      dummyNormativeAverageColumnHeaderKPISmallValue,
    );
    expect(formattedCellData).toStrictEqual(
      expectedNormativeAverageKPIFormattedDataSmallValue,
    );
  });

  test('normative element KPI value with positive lift', () => {
    expect.assertions(1);
    const formattedCellData = formatNormativeInsightMetadataKpiAndPercentLift(
      dummyNormativeElementKPIValue,
      dummyNormativeElementPositivePercentLift,
    );
    expect(formattedCellData).toStrictEqual(
      expectedNormativeElementKPIFormattedDataPositiveLift,
    );
  });

  test('normative element KPI value with positive lift with small values', () => {
    expect.assertions(1);
    const formattedCellData = formatNormativeInsightMetadataKpiAndPercentLift(
      dummyNormativeElementKPISmallValue,
      dummyNormativeElementPositivePercentLiftSmallValues,
    );
    expect(formattedCellData).toStrictEqual(
      expectedNormativeElementKPIFormattedDataPositiveLiftSmallValues,
    );
  });

  test('normative element KPI value with 0% lift', () => {
    expect.assertions(1);
    const formattedCellData = formatNormativeInsightMetadataKpiAndPercentLift(
      dummyNormativeElementKPIValue,
      dummyNormativeElementZeroPercentLift,
    );
    expect(formattedCellData).toStrictEqual(
      expectedNormativeElementKPIFormattedDataZeroPercentLift,
    );
  });

  test('normative element KPI value with negative lift', () => {
    expect.assertions(1);
    const formattedCellData = formatNormativeInsightMetadataKpiAndPercentLift(
      dummyNormativeElementKPIValue,
      dummyNormativeElementNegativePercentLift,
    );
    expect(formattedCellData).toStrictEqual(
      expectedNormativeElementKPIFormattedDataNegativeLift,
    );
  });
});
