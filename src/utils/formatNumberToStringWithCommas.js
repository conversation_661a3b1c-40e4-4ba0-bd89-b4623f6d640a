import { STAT_SETTING_DIRECTIONS } from '../constants/analytics.api.constants';
import { ORIENTATIONS, REPORT_TABLE_VIEW } from '../constants/ci.constants';
import { GLOBALS } from '../constants';

export const formatNumberToStringWithCommas = (number) =>
  number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

/**
 * @param {{formattedValue: string | null, statLiftAgainst: object}} cellData   rowItem/columnItem data
 * @param {{inverseHealth: boolean, id: string | number} | undefined} kpi   selected kpi in report (or row item in kpi report table view)
 * @param {"group" | "element"} statDirection direction for stat lift
 * @param {number} reportTableViewId  report table view id
 * @param {boolean} isCellInAccountAverageColumn    is cell for account average column
 * @param {boolean} isColumnHeader    is cell for a column header
 * @param {boolean} isAccountAverageColumnHeader    is account average column header cell
 * @returns {{shouldShowLiftIcon: boolean, kpiValue: string, isNegativeLift: boolean, isBeneficialLift: boolean, percentLift: string}}    kpi and percentLift data
 */
export const formatCompareReportKpiAndPercentLift = (
  cellData,
  kpi,
  statDirection,
  reportTableViewId,
  isCellInAccountAverageColumn = false,
  isColumnHeader = false,
  isAccountAverageColumnHeader = false,
  // eslint-disable-next-line max-params
) => {
  if (cellData && kpi && statDirection && reportTableViewId) {
    const isKPIReportTableView = reportTableViewId === REPORT_TABLE_VIEW.KPI.id;
    const validStatDirection = isKPIReportTableView
      ? ORIENTATIONS.HORIZONTAL
      : statDirection;
    const { formattedValue, statLiftAgainst } = cellData;
    const { inverseHealth: isInverse, id } = kpi;
    const statLift =
      statLiftAgainst?.[id]?.[STAT_SETTING_DIRECTIONS[validStatDirection]];

    // round to 100 decimal place
    const rounded = statLift ? Math.round(statLift * 100) / 100 : 0;
    const isNegativeLift = rounded < 0;
    const absoluteValue = Math.abs(rounded);

    const shouldShowPercentLift =
      formattedValue &&
      !isAccountAverageColumnHeader &&
      !(isColumnHeader && isKPIReportTableView) &&
      !(isCellInAccountAverageColumn && isKPIReportTableView) &&
      !(isColumnHeader && validStatDirection === 'vertical');

    return {
      kpiValue: formattedValue || GLOBALS.EM_DASH_UNICODE,
      percentLift: shouldShowPercentLift
        ? absoluteValue.toString() + '%'
        : GLOBALS.EM_DASH_UNICODE,
      shouldShowLiftIcon: Boolean(shouldShowPercentLift && absoluteValue !== 0),
      isNegativeLift,
      isBeneficialLift: isInverse ? isNegativeLift : !isNegativeLift,
    };
  }

  return {
    kpiValue: cellData?.formattedValue || GLOBALS.EM_DASH_UNICODE,
    percentLift: '0%',
    shouldShowLiftIcon: false,
    isNegativeLift: false,
    isBeneficialLift: false,
  };
};

export const formatNormativeInsightMetadataKpiAndPercentLift = (
  kpi,
  percentLift,
) => {
  const rounded = percentLift ? Math.round(percentLift * 100) / 100 : 0;
  const isNegativeLift = rounded < 0;
  const isBeneficialLift = rounded > 0;
  const absolutePercentLiftValue = Math.abs(rounded);
  const formattedKpiValue = kpi ? Math.round(kpi * 100) / 100 : 0;
  const stringFormattedKpi =
    formattedKpiValue < 0.01 ? '< 0.01%' : formattedKpiValue.toString() + '%';
  const shouldShowPercentLift = percentLift || percentLift === 0;

  return {
    kpiValue: kpi ? stringFormattedKpi : GLOBALS.EM_DASH_UNICODE,
    percentLift: shouldShowPercentLift
      ? absolutePercentLiftValue.toString() + '%'
      : GLOBALS.EM_DASH_UNICODE,
    shouldShowLiftIcon: Boolean(percentLift),
    isNegativeLift,
    isBeneficialLift,
  };
};
