import store from '../redux/store';
import { extractSelectedOptionsFromMultiSelectOptionsState } from '../components/MultiSelectNestedOptionsWithAccordion/MultiSelectNestedOptionsWithAccordion';
import StoredDataService from '../apiServices/StoredDataService';
import {
  getAnalyticsDates,
  getAnalyticsFiltersCreatedWithVidmob,
  getAnalyticsFilterShowAppAds,
  getAnalyticsFiltersSparkAds,
  getAnalyticsFiltersSelectedCampaigns,
  getAnalyticsFiltersSelectedAdSets,
  getAnalyticsFiltersSelectedMediaTypes,
  getAnalyticsFiltersSelectedObjectives,
  getAnalyticsFiltersSelectedPlacements,
  getIsPercentLiftViewEnabled,
  getReportTableView,
  getSelectedKpi,
  getSelectedOpCompareOption,
  getCurrentCreativeIntelligenceView,
  getSelectedPlacementNestingOption,
  getSelectedRows,
  getImpressionFilterValues,
} from '../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';
import { getCurrentPlatformAccount } from '../redux/selectors/platformAccounts.selectors';
import { getAudienceFilterSelections } from '../redux/selectors/creativeAnalytics/audienceReport.selectors';
import { getCampaignReportFilterSelections } from '../redux/selectors/creativeAnalytics/campaignReport.selectors';
import { getCompareGroups } from '../redux/selectors/creativeAnalytics/creativeGroups.selectors';
import {
  getElementPresenceReportFilterSelections,
  getElementPresenceReportOrderedColumnTitles,
} from '../redux/selectors/creativeAnalytics/elementPresenceReport.selectors';
import { CUSTOM_COMPARE_REPORT } from '../featureServices/AnalyticsFiltering/AnalyticsFilteringConstants';
import { showLocalizedErrorBar } from './showConfirmationBar';
import vmErrorLog from './vmErrorLog';
import {
  AUDIENCE_REPORT,
  CAMPAIGN_REPORT,
  COMPARE_REPORT_ACCOUNT_AVERAGE_COLUMN_NAME,
  CREATIVE_MANAGER,
  DURATION_REPORT,
  ELEMENT_PRESENCE_REPORT,
  OBJECTIVE_REPORT,
  PLACEMENT_REPORT,
} from '../constants/creativeAnalytics.constants';
import { getDurationReportFilterSelections } from '../redux/selectors/creativeAnalytics/durationReport.selectors';
import { AdvancedSectionChildrenType } from '../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersTypes';
import {
  INSIGHT_ATTRIBUTES_KEYS,
  INSIGHT_SOURCES,
  INSIGHT_TYPES,
  INSIGHTS_STATUSES,
} from '../constants/insights.constants';
import { getAdvancedFilterOptions } from '../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersApiServices/getAdvancedFilterOptions';
import { getFilterParams } from '../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersApiServices/getFilterParams';
import { getReportConstant } from '../creativeAnalytics/components/AnalyticsFilters/utilsV2/getters';
import { getCurrencyFromLocalStorage } from '../creativeAnalytics/components/AnalyticsFilters/utils/localStorageFunctions';
import { KPI_FORMATS } from '../types/kpi.types';

const getCampaignIds = (filterCampaigns) => {
  let campaignIds = '';
  if (filterCampaigns.length > 0) {
    campaignIds = filterCampaigns.map((campaign) => campaign.id);
  }

  return campaignIds;
};

const extractIds = (data) => {
  const result = {};
  if (!data) return result;

  if (data.placements?.length) {
    result.placementIds = data.placements.map((item) => item.id);
  }
  if (data.objectives?.length) {
    result.objectiveIds = data.objectives.map((item) => item.id);
  }
  if (data.mediaTypes?.length) {
    result.mediaTypes = data.mediaTypes;
  }

  return result;
};

export const getSelectedIdsFromObject = (obj) => {
  const values = Object.values(obj);
  const res = [];
  values.forEach((val) => {
    if (val.isSelected) {
      res.push(val.id);
    }

    if (val.children) {
      res.push(...getSelectedIdsFromObject(Object.values(val.children)));
    }
  });

  return res;
};

export const getItemsFromAvailableFilters = (
  sharedItemIds,
  availableItemsList,
) => {
  if (!availableItemsList || !availableItemsList?.length) {
    return;
  }

  const itemsForRedux = {};
  availableItemsList.forEach((availableItem) => {
    const childItemForRedux = {};
    let allChildrenSelected = true;
    if (availableItem.children.length > 0) {
      availableItem.children.forEach((childItem) => {
        const childSelected =
          sharedItemIds.includes(childItem.id) ||
          sharedItemIds.includes(childItem?.value);
        if (!childSelected) {
          allChildrenSelected = false;
        }

        childItemForRedux[childItem.id] = {
          ...childItem,
          isSelected: childSelected,
        };
      });
    } else {
      allChildrenSelected = false;
    }

    itemsForRedux[availableItem.id] = {
      ...availableItem,
      isSelected: allChildrenSelected,
      isOpen: false,
      children: childItemForRedux,
    };
  });

  return itemsForRedux;
};

const getAvailableReportColumnHeaders = (state, sliceName) =>
  state[sliceName].columnItems
    ?.map((columnHeader) => columnHeader.title)
    ?.filter((title) => title !== COMPARE_REPORT_ACCOUNT_AVERAGE_COLUMN_NAME);

export const buildSelectedFilters = (optionalSelectedRows) => {
  const reduxStoreState = store.getState();
  const selectedKpi = getSelectedKpi(reduxStoreState);
  const reportTableView = getReportTableView(reduxStoreState);
  const { analyticsStartDate, analyticsEndDate } =
    getAnalyticsDates(reduxStoreState);
  const selectedOpCompareOption = getSelectedOpCompareOption(reduxStoreState);
  const audienceFilterSelections = getAudienceFilterSelections(reduxStoreState);
  const campaignFilterSelections =
    getCampaignReportFilterSelections(reduxStoreState);
  const filterObjectives =
    getAnalyticsFiltersSelectedObjectives(reduxStoreState);
  const filterPlacements =
    getAnalyticsFiltersSelectedPlacements(reduxStoreState);
  const selectedPlacementNestingOption =
    getSelectedPlacementNestingOption(reduxStoreState);
  const filterCampaigns = getAnalyticsFiltersSelectedCampaigns(reduxStoreState);
  const filterMediaTypes =
    getAnalyticsFiltersSelectedMediaTypes(reduxStoreState);
  const impressionFilters = getImpressionFilterValues(reduxStoreState);
  const createdWithVidMob =
    getAnalyticsFiltersCreatedWithVidmob(reduxStoreState);
  const sparkAds = getAnalyticsFiltersSparkAds(reduxStoreState);
  const showAppAds = getAnalyticsFilterShowAppAds(reduxStoreState);
  const currentPlatformAccount = getCurrentPlatformAccount(reduxStoreState);
  const percentLiftEnabled = getIsPercentLiftViewEnabled(reduxStoreState);
  const selectedRows = optionalSelectedRows
    ? optionalSelectedRows
    : Array.from(getSelectedRows(reduxStoreState));
  const adsets = getAnalyticsFiltersSelectedAdSets(reduxStoreState);

  const availableObjectiveFilters = getAvailableReportColumnHeaders(
    reduxStoreState,
    'objectiveReport',
  );
  const availablePlacementFilters = getAvailableReportColumnHeaders(
    reduxStoreState,
    'placementReport',
  );

  const selectedObjectives =
    extractSelectedOptionsFromMultiSelectOptionsState(filterObjectives);
  const mergedObjectives = [
    ...selectedObjectives.parent,
    ...selectedObjectives.children,
  ];

  const selectedPlacements =
    extractSelectedOptionsFromMultiSelectOptionsState(filterPlacements);
  const mergedPlacements = [
    ...selectedPlacements.parent,
    ...selectedPlacements.children,
  ];

  const currentCreativeIntelligenceView =
    getCurrentCreativeIntelligenceView(reduxStoreState);
  const shouldSaveCompareGroups =
    currentCreativeIntelligenceView === CUSTOM_COMPARE_REPORT ||
    currentCreativeIntelligenceView === CREATIVE_MANAGER;

  const elementFilterSelections =
    getElementPresenceReportFilterSelections(reduxStoreState);
  const isElementPresenceReport =
    currentCreativeIntelligenceView === ELEMENT_PRESENCE_REPORT;

  const durationFilterSelections =
    getDurationReportFilterSelections(reduxStoreState);

  const { impressionFilterMinValue, impressionFilterMaxValue } =
    impressionFilters;

  // exclude audience, campaign and element filter selections unless on respective report
  return {
    isAdvancedElementPresenceReport: true,
    platformAccountUniqueKey: currentPlatformAccount?.uniqueKey,
    kpiId: selectedKpi?.id,
    reportTableViewId: reportTableView.id,
    filterPanel: {
      dateRange: { analyticsStartDate, analyticsEndDate },
      campaigns: getCampaignIds(filterCampaigns),
      media: filterMediaTypes,
      ...(currentCreativeIntelligenceView === OBJECTIVE_REPORT && {
        availableObjectiveFilters,
      }),
      objectives: mergedObjectives,
      ...(currentCreativeIntelligenceView === PLACEMENT_REPORT && {
        availablePlacementFilters,
      }),
      placements: mergedPlacements,
      selectedPlacementNestingOption,
      createdWithVidMob,
      sparkAds,
      showAppAds,
      ...(Boolean(impressionFilterMinValue) && { impressionFilterMinValue }),
      ...(Boolean(impressionFilterMaxValue) && { impressionFilterMaxValue }),
      adset: adsets,
    },
    selectedRows,
    percentLiftEnabled,
    selectedOpCompareOption,
    ...(currentCreativeIntelligenceView === AUDIENCE_REPORT && {
      audienceFilterSelections,
    }),
    ...(currentCreativeIntelligenceView === CAMPAIGN_REPORT && {
      campaignFilterSelections,
    }),
    ...(isElementPresenceReport && { elementFilterSelections }),
    ...(isElementPresenceReport && {
      orderedColumnTitles:
        getElementPresenceReportOrderedColumnTitles(reduxStoreState),
    }),
    ...(currentCreativeIntelligenceView === DURATION_REPORT && {
      durationFilterSelections,
    }),
    compareGroups: shouldSaveCompareGroups
      ? getPublicSavedCompareGroups()
      : null,
    dateCreated: Date.now(),
  };
};

export const buildAndSaveCIUrlFilters = async (optionalSelectedRows) => {
  const filterJSON = buildSelectedFilters(optionalSelectedRows);

  try {
    const jsonString = JSON.stringify(filterJSON);
    const storedDataResponse =
      await StoredDataService.createStoredData(jsonString);
    const savedFiltersId = storedDataResponse.data.identifier;
    return (
      window.location.href.split('?')[0] + '?sharedFilter=' + savedFiltersId
    );
  } catch (error) {
    showLocalizedErrorBar('ui.creative.intelligence.urlShare.save.error');
    vmErrorLog(
      error,
      'Share Report URL',
      'Error saving creativeAnalytics shared filter data',
    );
    return '';
  }
};

const getCurrency = (globalFilters) => {
  if (!globalFilters) return null;
  const { kpi } = globalFilters;
  const isSpendBasedKpi =
    kpi && kpi.value?.format && kpi.value?.format === KPI_FORMATS.SPEND;
  if (isSpendBasedKpi) {
    const currency = getCurrencyFromLocalStorage();
    if (currency) {
      return JSON.parse(currency);
    }
  }
  return null;
};

export const getInsightReportURL = () => {
  const urlString = window.location.href.split('?')[0];
  const reportAsURLInstance = new URL(urlString);
  // the insight create API complains if the insightReportUrl has local or a port
  if (reportAsURLInstance.host.includes('local')) {
    reportAsURLInstance.host = 'acs-dev.vidmob.com';
    reportAsURLInstance.port = '';
    reportAsURLInstance.protocol = 'https';
  }

  return reportAsURLInstance.href;
};

export const getCreateUpdateInsightRequest = async ({
  activeInsight,
  analyticsFilters,
  organizationId,
  activeWorkspaceId,
  // rowView, // TODO: VID-830
  // insightCreativeExamples, // TODO: VID-830
}) => {
  const {
    isPublished,
    finding,
    title,
    detail,
    selectedKpi,
    additionalKpis,
    recommendation,
    detailedInsightBuckets,
    savedAnalyticsReportId,
    audiences,
    audienceIds,
    campaignsInfo,
    copilotBrandMetadata,
  } = activeInsight;

  const { startDate, endDate, channel, adAccounts, advancedAdAccounts } =
    analyticsFilters.globalFilters;

  const { brands, markets } = advancedAdAccounts.value;
  // TODO: VID-830, use or remove
  // const impactReportParams = matchPath(location.pathname, {path: siteMap.creativeIntelligenceImpactReport})?.params;
  // const isInMediaImpactReport = impactReportParams?.reportType === IMPACT_REPORT_TYPES.ELEMENT_IMPACT;
  // const isKPIReportTableView = isInMediaImpactReport || rowView?.id === REPORT_TABLE_VIEW.KPI.id;

  const formattedAdditionalKpis = additionalKpis.map((kpi) => ({
    id: kpi.id,
    name: kpi.name,
  }));
  // TODO: VID-830 also read from selected rows in custom kpi view & impact report
  const kpisInfo = selectedKpi
    ? [
        { id: selectedKpi.id, name: selectedKpi.name },
        ...formattedAdditionalKpis,
      ]
    : formattedAdditionalKpis;
  const attributesFromAdvancedFilters = await getAvailableFilterOptionsByType(
    analyticsFilters,
    organizationId,
  );

  const currency = getCurrency(analyticsFilters?.globalFilters);
  const formattedAdvancedFilters = extractIds(attributesFromAdvancedFilters);
  return {
    title: title,
    finding: finding,
    recommendation: recommendation?.length ? recommendation : undefined,
    ...(detailedInsightBuckets?.length && {
      categoryIds: detailedInsightBuckets.map((category) => category.id),
    }),
    audienceIds,
    ...formattedAdvancedFilters,
    insightReportUrl: getInsightReportURL(),
    startDate: Date.parse(startDate),
    endDate: Date.parse(endDate),
    platform: channel.value,
    adAccountIds: adAccounts.value.map(
      (adAccount) => adAccount.platformAccountId,
    ),
    kpiIds: kpisInfo.map((kpi) => parseInt(kpi.id)),
    publishDate: isPublished ? Date.now() : undefined,
    savedAnalyticsReportId,
    source: INSIGHT_SOURCES.MANUAL,
    status: isPublished ? INSIGHTS_STATUSES.PUBLISHED : INSIGHTS_STATUSES.DRAFT,
    type: INSIGHT_TYPES.BRAND,
    ...(markets?.length && { marketIds: markets.map((market) => market.id) }),
    ...(brands?.length && { brandIds: brands.map((brand) => brand.id) }),
    activeWorkspaceId,
    reportType: getReportConstant(),
    copilotBrandMetadata,
    brandFilters: { currency },
  };
};

export const getAvailableFilterOptionsByType = async (
  analyticsFilters,
  organizationId,
) => {
  const insightAdvancedFilterTypeToInsightAttributeMap = {
    [AdvancedSectionChildrenType.CAMPAIGN_IDENTIFIER]:
      INSIGHT_ATTRIBUTES_KEYS.campaignsKey,
    [AdvancedSectionChildrenType.CREATIVE_MEDIA_TYPE]:
      INSIGHT_ATTRIBUTES_KEYS.mediaTypesKey,
    [AdvancedSectionChildrenType.CAMPAIGN_OBJECTIVE]:
      INSIGHT_ATTRIBUTES_KEYS.objectivesKey,
    [AdvancedSectionChildrenType.AD_PLACEMENT]:
      INSIGHT_ATTRIBUTES_KEYS.placementsKey,
  };

  const promises = analyticsFilters.advancedFilters
    .filter((advancedFilter) =>
      Object.keys(insightAdvancedFilterTypeToInsightAttributeMap).includes(
        advancedFilter.type,
      ),
    )
    .map((advancedFilter) => {
      const params = getFilterParams(
        advancedFilter.type,
        organizationId,
        analyticsFilters,
      );

      return getAdvancedFilterOptions({ organizationId, params }).then(
        (response) => {
          const insightAttributeKey =
            insightAdvancedFilterTypeToInsightAttributeMap[advancedFilter.type];
          if (
            advancedFilter.type ===
            AdvancedSectionChildrenType.CREATIVE_MEDIA_TYPE
          ) {
            return {
              [insightAttributeKey]: advancedFilter.value?.map(
                (mediaTypeObject) => mediaTypeObject?.id,
              ),
            };
          }

          const result = response.options
            .filter((item) =>
              advancedFilter.value.some(
                (filterItem) =>
                  (filterItem.id === item.id ||
                    filterItem.originalName === item.originalName ||
                    filterItem === item.id ||
                    filterItem === item.originalName) &&
                  !item.isDivider,
              ),
            )
            .map((item) => ({ id: item.id, name: item.originalName }));
          return {
            [insightAttributeKey]: result,
          };
        },
      );
    });

  const filtersObjects = await Promise.all(promises);
  return filtersObjects.reduce(
    (acc, filterObject) => ({ ...acc, ...filterObject }),
    {},
  );
};

export const getPublicSavedCompareGroups = () => {
  const reduxStoreState = store.getState();
  const compareGroups = getCompareGroups(reduxStoreState);

  const compareGroupsNoBlanks = compareGroups.filter(
    (group) => group.media?.length > 0,
  );

  if (compareGroupsNoBlanks.length === 0) {
    return;
  }

  const publicSavedGroups = compareGroupsNoBlanks.filter(
    (group) => group.privacyLevel === 'PUBLIC' && group.isSavedGroup === true,
  );

  // this is currently saving just the ids into state
  if (compareGroupsNoBlanks.length === publicSavedGroups.length) {
    return publicSavedGroups.map((group) => group.id);
  }
};

export const getSharedCampaignsFromAvailableFilters = (
  sharedCampaignIds,
  availableCampaigns,
) => {
  if (sharedCampaignIds?.length > 0 && availableCampaigns?.length > 0) {
    return availableCampaigns.filter((availableCampaign) =>
      sharedCampaignIds.includes(availableCampaign.id),
    );
  }

  return false;
};

export const getSharedObjectivesFromAvailableFilters = (
  sharedObjectiveIds,
  availableObjectives,
) => {
  if (sharedObjectiveIds?.length > 0 && availableObjectives?.length > 0) {
    return getItemsFromAvailableFilters(
      sharedObjectiveIds,
      availableObjectives,
    );
  }

  return false;
};

export const getSharedPlacementsFromAvailableFilters = (
  sharedPlacementIds,
  sharedSelectedPlacementNestingOption,
  availablePlacements,
) => {
  if (
    sharedPlacementIds?.length > 0 &&
    Object.keys(availablePlacements?.length > 0)
  ) {
    if (sharedSelectedPlacementNestingOption) {
      for (const key in availablePlacements) {
        if (Object.prototype.hasOwnProperty.call(availablePlacements, key)) {
          // eslint-disable-next-line max-depth
          if (key === sharedSelectedPlacementNestingOption) {
            return getItemsFromAvailableFilters(
              sharedPlacementIds,
              availablePlacements[key],
            );
          }
        }
      }
    } else {
      return getItemsFromAvailableFilters(
        sharedPlacementIds,
        availablePlacements,
      );
    }
  }

  return false;
};

export const getSharedMediaFromAvailableFilters = (
  sharedMediaIds,
  availableMediaIds,
) => {
  if (sharedMediaIds?.length > 0 && availableMediaIds?.length > 0) {
    return availableMediaIds.filter((availableMediaId) =>
      sharedMediaIds.includes(availableMediaId),
    );
  }

  return false;
};

export const getSharedKpiFromAvailableFilters = (
  sharedKpiId,
  availableKpis,
) => {
  if (sharedKpiId && availableKpis?.length > 0) {
    return availableKpis.find(
      (availableKpi) => availableKpi.id === sharedKpiId,
    );
  }

  return false;
};

export const getSharedReportTableViewFromAvailableFilters = (
  sharedReportTableViewId,
  availableReportTableViews,
) => {
  if (sharedReportTableViewId) {
    const reportTableViewArray = Object.values(availableReportTableViews);
    let reportTableViewForRedux = reportTableViewArray.find(
      (availableReportTableView) =>
        availableReportTableView.id === sharedReportTableViewId,
    );
    reportTableViewForRedux = { ...reportTableViewForRedux, selected: true };
    return reportTableViewForRedux;
  }

  return false;
};
