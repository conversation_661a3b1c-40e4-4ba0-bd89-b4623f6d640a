import SCORING_CONSTANTS from './creativeScoring.constants';
import { COMPLIANCE } from '../../constants';
import { LandingPageFilterId } from '../components/ScoringLandingFilters/scoringLandingFilters.types';

const { PARTNER_CRITERIA_PLATFORMS } = COMPLIANCE;
const { FILE_TYPES_FOR_FILTERING } = SCORING_CONSTANTS;

export const DATA_GRID_FIELDS = {
  CHECK: '__check__',
  NAME: 'name',
  RULE: 'rule',
  PLATFORM: 'platform',
  MEDIA_TYPES: 'mediaTypes',
  IS_OPTIONAL: 'isOptional',
  IS_GLOBAL: 'isGlobal',
  CATEGORY: 'category',
  DATE_CREATED: 'dateCreated',
  OWNER: 'owner',
  CRITERIA_GROUPS: 'criteriaGroups',
  ACTIONS: 'actions',
} as const;

export const SORT_ORDER = {
  ASC: 'ASC',
  DESC: 'DESC',
};

export const CRITERIA_MANAGEMENT_FILTER_FIELDS = {
  PLATFORMS: 'platforms',
  MEDIA_TYPES: 'mediaTypes',
  IS_OPTIONAL: 'isOptional',
  OWNER_IDS: 'ownerIds',
  GLOBAL_STATUSES: 'globalStatuses',
  CATEGORY: 'category',
} as const;

export const DEFAULT_SORT_ORDER = SORT_ORDER.DESC;
export const DEFAULT_SORT_BY = DATA_GRID_FIELDS.DATE_CREATED;

export const IS_OPTIONAL_TYPES = {
  OPTIONAL: 'OPTIONAL',
  MANDATORY: 'MANDATORY',
};

export const GLOBAL_STATUSES_TYPES = {
  GLOBAL: 'GLOBAL',
  DEFAULT: 'DEFAULT',
};

export const CRITERIA_MANAGEMENT_USERS = {
  ADMIN: 'admin',
  STANDARD: 'standard',
};

export const CRITERIA_MANAGEMENT_MODAL_TYPES = {
  EDIT: 'edit',
  DELETE: 'delete',
  BEST_PRACTICES: 'bestPractices',
  ADD_CRITERIA: 'addCriteria',
  SET_BRAND_IDENTIFIER: 'setBrandIdentifier',
  CRITERIA_DETAILS: 'criteriaDetails',
  CRITERIA_GROUP_DETAILS: 'criteriaGroupDetails',
  EXPORT_CSV_MODAL: 'exportCSVModal',
};

export const INITIAL_FILTERS = {
  [CRITERIA_MANAGEMENT_FILTER_FIELDS.PLATFORMS]: [
    ...PARTNER_CRITERIA_PLATFORMS.map((platform) => platform.id),
  ],
  [CRITERIA_MANAGEMENT_FILTER_FIELDS.MEDIA_TYPES]: [
    FILE_TYPES_FOR_FILTERING.IMAGE,
    FILE_TYPES_FOR_FILTERING.VIDEO,
    FILE_TYPES_FOR_FILTERING.ANIMATED_IMAGE,
    FILE_TYPES_FOR_FILTERING.HTML,
  ],
  [CRITERIA_MANAGEMENT_FILTER_FIELDS.IS_OPTIONAL]: [
    IS_OPTIONAL_TYPES.OPTIONAL,
    IS_OPTIONAL_TYPES.MANDATORY,
  ],
};

export const CRITERIA_MANAGEMENT_INITIAL_FILTERS = {
  [LandingPageFilterId.PLATFORMS]: [],
  [LandingPageFilterId.MEDIA_TYPES]: [],
  [LandingPageFilterId.IS_OPTIONAL]: [],
  [LandingPageFilterId.OWNER_IDS]: [],
  [LandingPageFilterId.GLOBAL_STATUSES]: [],
  [LandingPageFilterId.CATEGORY]: [],
  [LandingPageFilterId.DATE_ADDED]: [],
  [LandingPageFilterId.CRITERIA_GROUP]: [],
};

export const CRITERIA_CATEGORIES: Record<string, string> = {
  ALL: 'All',
  BRAND_VISIBILITY: 'Brand Visibility',
  FORMATTING: 'Formatting',
  KEY_VISUALS: 'Key Visuals',
  MESSAGING_EFFECTIVENESS: 'Messaging Effectiveness',
  PRODUCT_VISIBILITY: 'Product Visibility',
  REGULATORY_COMPLIANCE: 'Regulatory & Compliance',
  OTHER: 'Other',
  CUSTOM: 'Custom',
};

export const CRITERIA_CATEGORIES_INTL_MAP = {
  [CRITERIA_CATEGORIES.ALL]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.viewAll',
  [CRITERIA_CATEGORIES.BRAND_VISIBILITY]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.brandVisibility',
  [CRITERIA_CATEGORIES.FORMATTING]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.formatting',
  [CRITERIA_CATEGORIES.KEY_VISUALS]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.keyVisuals',
  [CRITERIA_CATEGORIES.MESSAGING_EFFECTIVENESS]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.messagingEffectiveness',
  [CRITERIA_CATEGORIES.PRODUCT_VISIBILITY]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.productVisibility',
  [CRITERIA_CATEGORIES.REGULATORY_COMPLIANCE]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.regulatoryCompliance',
  [CRITERIA_CATEGORIES.OTHER]:
    'ui.compliance.criteriaManagement.create.modal.category.dropdown.other',
};
