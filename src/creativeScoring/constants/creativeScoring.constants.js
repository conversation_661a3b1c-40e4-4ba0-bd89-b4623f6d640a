import MEDI<PERSON> from '../../constants/media.constants';
import PLATFORM from '../../constants/platform.constants';
import vidmobBlackIconUrl from '../../assets/icons/ic-vidmob-black.svg';
import { CriteriaResultType } from '../types/criteria.types';

const CUSTOM_AUDIO_IDENTIFIER = 'CUSTOM_AUDIO';
const CUSTOM_TEXT_IDENTIFIER = 'CUSTOM_TEXT';
const CUSTOM_COLOR_IDENTIFIER = 'CUSTOM_COLOR';
const DISCLAIMER_PRESENT_TEXT = 'DISCLAIMER_PRESENT_TEXT';
const DISCLAIMER_PRESENT_AUDIO = 'DISCLAIMER_PRESENT_AUDIO';
const TERMS_CONDITIONS_PRESENT_TEXT = 'TERMS_CONDITIONS_PRESENT_TEXT';
const TERMS_CONDITIONS_PRESENT_AUDIO = 'TERMS_CONDITIONS_PRESENT_AUDIO';
const PRODUCT_BENEFIT_PRESENT_TEXT = 'PRODUCT_BENEFIT_PRESENT_TEXT';
const PRODUCT_BENEFIT_PRESENT_AUDIO = 'PRODUCT_BENEFIT_PRESENT_AUDIO';
const PRODUCT_CLAIMS_PRESENT_TEXT = 'PRODUCT_CLAIMS_PRESENT_TEXT';
const PRODUCT_CLAIMS_PRESENT_AUDIO = 'PRODUCT_CLAIMS_PRESENT_AUDIO';
const PRODUCT_FEATURES_PRESENT_TEXT = 'PRODUCT_FEATURES_PRESENT_TEXT';
const PRODUCT_FEATURES_PRESENT_AUDIO = 'PRODUCT_FEATURES_PRESENT_AUDIO';
const PACKAGE_DESCRIBES_PRODUCT = 'PACKAGE_DESCRIBES_PRODUCT';
const PACKAGE_HAS_COLOR = 'PACKAGE_HAS_COLOR';
const PACKAGE_HAS_LOGO = 'PACKAGE_HAS_LOGO';
const PACKAGE_SHOWS_PRODUCT = 'PACKAGE_SHOWS_PRODUCT';
const FILE_SIZE_LIMIT = 'FILE_SIZE_LIMIT';
const USER_GENERATED_CONTENT_ANYTIME = 'USER_GENERATED_CONTENT_ANYTIME';
const USER_GENERATED_CONTENT_NEAR_END = 'USER_GENERATED_CONTENT_NEAR_END';
const USER_GENERATED_CONTENT_EARLY = 'USER_GENERATED_CONTENT_EARLY';
const FITS_SIZE = 'FITS_SIZE';
const TEXT_AND_LOGO_PRESENT_EARLY = 'TEXT_AND_LOGO_PRESENT_EARLY';
const LOGO_PLACEMENT = 'LOGO_PLACEMENT';
export const CRITERIA_GROUP_NO_GROUP = 'No group';

const CUSTOM_CRITERIA_IDENTIFIERS = [
  CUSTOM_AUDIO_IDENTIFIER,
  CUSTOM_TEXT_IDENTIFIER,
  CUSTOM_COLOR_IDENTIFIER,
  DISCLAIMER_PRESENT_TEXT,
  DISCLAIMER_PRESENT_AUDIO,
  TERMS_CONDITIONS_PRESENT_TEXT,
  TERMS_CONDITIONS_PRESENT_AUDIO,
  PRODUCT_BENEFIT_PRESENT_TEXT,
  PRODUCT_BENEFIT_PRESENT_AUDIO,
  PRODUCT_CLAIMS_PRESENT_TEXT,
  PRODUCT_CLAIMS_PRESENT_AUDIO,
  PRODUCT_FEATURES_PRESENT_TEXT,
  PRODUCT_FEATURES_PRESENT_AUDIO,
];

const ALL_PLATFORMS_TEMPLATE_IDENTIFIER = 'ALL_PLATFORMS';
const STANDARD_CRITERIA_PLATFORM_IDENTIFIER = 'Standard';

const ALL_CHANNELS_IDENTIFIER = 'ALL_CHANNELS';
const ALL_LOCATIONS_IDENTIFIER = 'ALL_LOCATIONS';
const NOT_SPECIFIED_IDENTIFIER = 'NOT_SPECIFIED';

// Template identifiers. They match identifiers in DB
export const CRITERIA_TEMPLATE_IDENTIFIERS = {
  VIDEO_DURATION: 'VIDEO_DURATION',
  HUMAN_PRESENCE: 'HUMAN_PRESENCE',
  HUMAN_PRESENCE_ANYTIME: 'HUMAN_PRESENCE_ANYTIME',
  POSITIVE_EMOTION: 'POSITIVE_EMOTION',
  POSITIVE_EMOTION_ANYTIME: 'POSITIVE_EMOTION_ANYTIME',
  MAX_WORDS_PER_FRAME: 'MAX_WORDS_PER_FRAME',
  WORDS_PER_FRAME_IN_RANGE: 'WORDS_PER_FRAME_IN_RANGE',
  BRAND_NAME_LOGO: 'BRAND_NAME_OR_LOGO',
  BRAND_NAME_LOGO_ANYTIME: 'BRAND_NAME_OR_LOGO_ANYTIME',
  BRAND_NAME_IN_AUDIO_ANYTIME: 'BRAND_NAME_IN_AUDIO_ANYTIME',
  BRAND_NAME_OR_LOGO_NEAR_END: 'BRAND_NAME_OR_LOGO_NEAR_END',
  BRAND_NAME_IN_AUDIO_NEAR_END: 'BRAND_NAME_IN_AUDIO_NEAR_END',
  FRAMED_FOR_MOBILE: 'FRAMED_FOR_MOBILE',
  LOREAL_PRODUCT: 'LOREAL_PRODUCT',
  SCENE_CHANGE: 'SCENE_CHANGE',
  CTA_DETECTION: 'CTA_DETECTION',
  SOUND_OFF: 'SOUND_OFF',
  SOUND_ON: 'SOUND_ON',
  JNJ_PRODUCT: 'JNJ_PRODUCT',
  SNAP_PRODUCT: 'SNAP_PRODUCT',
  PERSON_WITH_PRODUCT: 'PERSON_WITH_PRODUCT',
  BRAND_NAME_OR_LOGO_PERSISTS: 'BRAND_NAME_OR_LOGO_PERSISTS',
  KEY_MESSAGING_PERSISTS: 'KEY_MESSAGING_PERSISTS',
  ABI_BRAND_LINK: 'ABI_BRAND_LINK',
  SOUND_OFF_NO_TRANSCRIPTION: 'SOUND_OFF_NO_TRANSCRIPTION',
  ABI_PRODUCT_EARLY: 'ABI_PRODUCT_EARLY',
  ABI_RESPONSIBLE_DRINK_MESSAGE: 'ABI_RESPONSIBLE_DRINK_MESSAGE',
  ABI_SUPERS_PRESENCE: 'ABI_SUPERS_PRESENCE',
  LOREAL_VOICE_OVER_ANYTIME: 'LOREAL_VOICE_OVER_ANYTIME',
  ABI_FRAME_TIGHTLY: 'ABI_FRAME_TIGHTLY',
  ABI_EARLY_CTA: 'ABI_EARLY_CTA',
  ABI_BRAND_COLOR: 'ABI_BRAND_COLOR',
  JNJ_VISUAL_CTA_PRESENCE: 'JNJ_VISUAL_CTA_PRESENCE',
  SNAP_CELEBRITY_PRESENCE: 'SNAP_CELEBRITY_PRESENCE',
  JNJ_BRAND_NAME_IN_AUDIO: 'JNJ_BRAND_NAME_IN_AUDIO',
  JNJ_BRAND_NAME_IN_AUDIO_ANYTIME: 'JNJ_BRAND_NAME_IN_AUDIO_ANYTIME',
  LOREAL_BRAND_NAME_IN_AUDIO: 'LOREAL_BRAND_NAME_IN_AUDIO',
  LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME: 'LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME',
  SNAP_FOOD_BEVERAGE_PRODUCT: 'SNAP_FOOD_BEVERAGE_PRODUCT',
  SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME: 'SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME',
  SNAP_TECHNOLOGY_PRODUCT: 'SNAP_TECHNOLOGY_PRODUCT',
  OFFER_MESSAGE: 'OFFER_MESSAGE',
  ABI_RETAILER_LOGO_ANYTIME: 'ABI_RETAILER_LOGO_ANYTIME',
  ABI_PRODUCT_ANYTIME: 'ABI_PRODUCT_ANYTIME',
  ABI_PACE_QUICKLY: 'ABI_PACE_QUICKLY',
  ABI_DYNAMIC_START: 'ABI_DYNAMIC_START',
  CUSTOM_PARAMS: 'CUSTOM_PARAMS',
  CUSTOM_AUDIO: CUSTOM_AUDIO_IDENTIFIER,
  CUSTOM_TEXT: CUSTOM_TEXT_IDENTIFIER,
  CUSTOM_COLOR: CUSTOM_COLOR_IDENTIFIER,
  PACKAGE_DESCRIBES_PRODUCT: PACKAGE_DESCRIBES_PRODUCT,
  PACKAGE_HAS_COLOR: PACKAGE_HAS_COLOR,
  PACKAGE_HAS_LOGO: PACKAGE_HAS_LOGO,
  PACKAGE_SHOWS_PRODUCT: PACKAGE_SHOWS_PRODUCT,
  FILE_SIZE_LIMIT: FILE_SIZE_LIMIT,
  USER_GENERATED_CONTENT_ANYTIME: USER_GENERATED_CONTENT_ANYTIME,
  USER_GENERATED_CONTENT_NEAR_END: USER_GENERATED_CONTENT_NEAR_END,
  USER_GENERATED_CONTENT_EARLY: USER_GENERATED_CONTENT_EARLY,
  FITS_SIZE: FITS_SIZE,
  TEXT_AND_LOGO_PRESENT_EARLY: TEXT_AND_LOGO_PRESENT_EARLY,
  LOGO_PLACEMENT: LOGO_PLACEMENT,
};

// Blank parameter used when we don't want to plug in value(s) to template description
const BLANK_TEMPLATE_DESCRIPTION_PARAMETER = '__';

// Note that these do not correspond to the actual API call - these are internal constants.
const INDIVIDUAL_CRITERIA_RESULTS = {
  CRITERIA_MET: 'CRITERIA_MET_RESULT',
  CRITERIA_NOT_MET: 'CRITERIA_NOT_MET_RESULT',
  RESULT_NOT_AVAILABLE: 'RESULT_NOT_AVAILABLE_RESULT',
  NOT_RUN: 'NOT_RUN',
  NO_MEDIA: 'NO_MEDIA',
  NO_DATA: 'NO_DATA',
  PROCESSING: 'PROCESSING',
  ERROR: 'ERROR',
};

// The values here match the error codes for this API:
// https://vidmob.atlassian.net/wiki/spaces/ATOM/pages/1572831233/AS+-1675+Define+and+Document+API+for+content+audit#Media-Detail
const PLATFORM_MEDIA_ERROR_CODES = {
  SERVER_ERROR: 'SERVER_ERROR',
  BAD_REQUEST: 'BAD_REQUEST',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NOT_FOUND: 'NOT_FOUND',
};

/**
 * @deprecated This should be removed and replaced with the CriteriaResultType enum
 */
export const INDIVIDUAL_CRITERIA_RESULTS_API_VALUES = CriteriaResultType;

export const INDIVIDUAL_CRITERIA_RESULTS_PILL_MESSAGES_KEYS = {
  [CriteriaResultType.CRITERIA_PASS]:
    'ui.compliance.individualCriteriaResult.pass',
  [CriteriaResultType.CRITERIA_FAIL]:
    'ui.compliance.individualCriteriaResult.fail',
  [CriteriaResultType.CRITERIA_RESULT_PENDING]:
    'ui.compliance.individualCriteriaResult.processing',
  [CriteriaResultType.CRITERIA_NOT_RUN]:
    'ui.compliance.contentAuditResult.filter.option.notRun',
  [CriteriaResultType.CRITERIA_NO_DATA]:
    'ui.compliance.individualCriteriaResult.notAvailable',
  [CriteriaResultType.CRITERIA_NO_MEDIA]:
    'ui.compliance.individualCriteriaResult.notAvailable',
  [CriteriaResultType.CRITERIA_ERROR]:
    'ui.compliance.contentAuditResult.result.error',
  [CriteriaResultType.NEW_CRITERIA]:
    'ui.compliance.contentAuditResult.result.newCriteria',
  [CriteriaResultType.NOT_APPLICABLE]:
    'ui.compliance.contentAuditResult.result.notApplicable',
  [CriteriaResultType.MEDIA_PREDATES_MODEL]:
    'ui.compliance.contentAuditResult.result.mediaPredatesModel',
  [CriteriaResultType.MEDIA_PREDATES_TAG_API]:
    'ui.compliance.contentAuditResult.result.mediaPredatesTagApi',
  [CriteriaResultType.MISSING_BRAND_IDENTIFIERS]:
    'ui.compliance.contentAuditResult.result.missingBrandIdentifiers',
  [CriteriaResultType.MISSING_REQUIRED_TAGS]:
    'ui.compliance.contentAuditResult.result.missingRequiredTags',
  [CriteriaResultType.SCORING_BACKLOG]:
    'ui.compliance.contentAuditResult.result.scoringBacklog',
  [CriteriaResultType.AD_ACCOUNT_DISCONNECTED]:
    'ui.compliance.contentAuditResult.result.adAccountDisconnected',
  [CriteriaResultType.MEDIA_DOWNLOAD_FAILURE]:
    'ui.compliance.contentAuditResult.result.mediaDownloadFailure',
  [CriteriaResultType.MEDIA_NOT_TAGGED]:
    'ui.compliance.contentAuditResult.result.mediaNotTagged',
  [CriteriaResultType.MEDIA_TAGGING_FAILURE]:
    'ui.compliance.contentAuditResult.result.missingRequiredTags',

  // TODO: Not implemented yet
  [CriteriaResultType.RACE_CONDITION]:
    'ui.compliance.contentAuditResult.result.raceCondition',
  [CriteriaResultType.MEDIA_RECOGNITION_FAIL]:
    'ui.compliance.contentAuditResult.result.mediaRecognitionFail',
  [CriteriaResultType.SCORING_TIMEOUT]:
    'ui.compliance.contentAuditResult.result.scoringTimeout',
  [CriteriaResultType.MEDIA_ANNOTATION_API_OVERLOAD]:
    'ui.compliance.contentAuditResult.result.mediaAnnotationApiOverload',
};

const FILTER_CRITERIA_SCORE_RESULTS_API_VALUES = {
  CRITERIA_PASS: 'PASS',
  CRITERIA_FAIL: 'FAIL',
  CRITERIA_EMPTY: 'EMPTY',
};

// This maps the values from the API to the internal constants.
// Keys are API values, values are internal ACS values
const INDIVIDUAL_CRITERIA_RESULTS_API_VALUES_TO_INTERNAL_VALUES = {
  [CriteriaResultType.CRITERIA_PASS]: INDIVIDUAL_CRITERIA_RESULTS.CRITERIA_MET,
  [CriteriaResultType.CRITERIA_FAIL]:
    INDIVIDUAL_CRITERIA_RESULTS.CRITERIA_NOT_MET,
  // The API returns several values that are shown to the user as "not available".
  [CriteriaResultType.CRITERIA_NO_DATA]: INDIVIDUAL_CRITERIA_RESULTS.NO_DATA,
  [CriteriaResultType.CRITERIA_NO_MEDIA]: INDIVIDUAL_CRITERIA_RESULTS.NO_MEDIA,
  [CriteriaResultType.CRITERIA_RESULT_PENDING]:
    INDIVIDUAL_CRITERIA_RESULTS.PROCESSING,
  [CriteriaResultType.CRITERIA_NOT_RUN]: INDIVIDUAL_CRITERIA_RESULTS.NOT_RUN,
  [CriteriaResultType.CRITERIA_ERROR]: INDIVIDUAL_CRITERIA_RESULTS.ERROR,
};

const VIDMOB_CRITERIA_LABEL = 'ui.compliance.contentAudit.vidmobCriteria.label';

// These values match results from API:
// https://vidmob.atlassian.net/wiki/spaces/ATOM/pages/1572831233/AS+-1675+Define+and+Document+API+for+content+audit#Aggregate-content-audit-scores
const CONTENT_AUDIT_CRITERIA = {
  DURATION: 'DURATION',
  TALENT: 'TALENT',
  POSITIVE_EMOTION: 'POSITIVE_EMOTION',
  BRAND_APPEARS: 'BRAND_APPEARS',
  BRAND_APPEARS_ENOUGH: 'BRAND_APPEARS_ENOUGH',
  WORDS_PER_SECOND: 'WORDS_PER_SECOND',
  FORMAT: 'FORMAT',
};

// The displayed descriptions of content audit criteria
const CONTENT_AUDIT_CRITERIA_DESCRIPTION = {
  DURATION: 'ui.compliance.contentAudit.criterion.duration.description',
  TALENT: 'ui.compliance.contentAudit.criterion.talent.description',
  POSITIVE_EMOTION:
    'ui.compliance.contentAudit.criterion.positive.emotion.description',
  BRAND_APPEARS:
    'ui.compliance.contentAudit.criterion.brand.appears.description',
  BRAND_APPEARS_ENOUGH:
    'ui.compliance.contentAudit.criterion.brand.appears.enough.description',
  WORDS_PER_SECOND: 'ui.compliance.contentAudit.criterion.wps.description',
  FORMAT: 'ui.compliance.contentAudit.criterion.format.description',
};

const CONTENT_AUDIT_FILTER_STATES = {
  ALL_EVALUATED: 'ALL_EVALUATED',
  CRITERIA_MET: 'CRITERIA_MET',
  CRITERIA_NOT_MET: 'CRITERIA_NOT_MET',
  NOT_AVAILABLE: 'NOT_AVAILABLE',
};

const MOBILE_FITNESS_SCORE = {
  BRAND_SCORE_PERCENTILE: 'BRAND_SCORE_PERCENTILE',
  MESSAGING_SCORE_PERCENTILE: 'MESSAGING_SCORE_PERCENTILE',
  CLUTTER_SCORE_PERCENTILE: 'CLUTTER_SCORE_PERCENTILE',
  DURATION_SCORE: 'DURATION_SCORE',
  MOBILE_FITNESS_SCORE: 'MOBILE_FITNESS_SCORE',
};

const COMPLIANCE_MOBILE_FITNESS_SCORE_WIDGET_INSIGHT_TEXT = {
  overall: {
    title: 'widget.mobileFitnessScore.legend.mobileFitnessScore',
    complianceText:
      'widget.mobileFitnessScore.insight.mobileFitnessScore.compliance.text',
    complianceQuestion:
      'widget.mobileFitnessScore.insight.mobileFitnessScore.compliance.question',
  },
  keyVisuals: {
    title: 'widget.mobileFitnessScore.legend.keyVisuals',
    complianceText:
      'widget.mobileFitnessScore.insight.keyVisuals.compliance.text',
    complianceQuestion:
      'widget.mobileFitnessScore.insight.keyVisuals.compliance.question',
  },
  branding: {
    title: 'widget.mobileFitnessScore.legend.branding',
    complianceText:
      'widget.mobileFitnessScore.insight.branding.compliance.text',
    complianceQuestion:
      'widget.mobileFitnessScore.insight.branding.compliance.question',
  },
  messaging: {
    title: 'widget.mobileFitnessScore.legend.messaging',
    complianceText:
      'widget.mobileFitnessScore.insight.messaging.compliance.text',
    complianceQuestion:
      'widget.mobileFitnessScore.insight.messaging.compliance.question',
  },
  duration: {
    title: 'widget.mobileFitnessScore.legend.duration',
    complianceText:
      'widget.mobileFitnessScore.insight.duration.compliance.text',
    complianceQuestion:
      'widget.mobileFitnessScore.insight.duration.compliance.question',
  },
};

const COMPLIANCE_MOBILE_FITNESS_SCORE_TEST_DATA = {
  INITIAL_DATA: 'DATA_LOADED',
  data: {
    overall: { value: 82 },
    rings: {
      keyVisuals: { value: 45, position: 4, foregroundColor: '#1abbc1' },
      messaging: { value: 67, position: 3, foregroundColor: '#ffc841' },
      branding: { value: 91, position: 2, foregroundColor: '#ff6946' },
      duration: { value: 94, position: 1, foregroundColor: '#5564ff' },
    },
  },
};

const J_AND_J_DAM_SEGMENT_IDENTIFIERS = {
  JOHNSONS_BABY_INDIA_IDENTIFIER: 'JOHNSONS_BABY_INDIA',
  JOHNSONS_BABY_PHILIPPINES_IDENTIFIER: 'JOHNSONS_BABY_PHILIPPINES',
  JOHNSONS_BABY_UK_IDENTIFIER: 'JOHNSONS_BABY_UK',
  LISTERINE_INDIA_IDENTIFIER: 'LISTERINE_INDIA',
  LISTERINE_JAPAN_IDENTIFIER: 'LISTERINE_JAPAN',
  NEUTROGENA_GERMANY_IDENTIFIER: 'NEUTROGENA_GERMANY',
  NEUTROGENA_USA_IDENTIFIER: 'NEUTROGENA_USA',
  NICORETTE_UK_IDENTIFIER: 'NICORETTE_UK',
  TYLENOL_USA_IDENTIFIER: 'TYLENOL_USA',
};

const J_AND_J_DAM_SEGMENTS = {
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.JOHNSONS_BABY_PHILIPPINES_IDENTIFIER]: {
    localizationKey:
      'ui.compliance.contentAudit.jAndJ.damSegment.johnsonsBabyPhilippines',
    brand: "Johnson's baby",
    market: 'Philippines',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.JOHNSONS_BABY_INDIA_IDENTIFIER]: {
    localizationKey:
      'ui.compliance.contentAudit.jAndJ.damSegment.johnsonsBabyIndia',
    brand: "Johnson's baby",
    market: 'India',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.LISTERINE_INDIA_IDENTIFIER]: {
    localizationKey:
      'ui.compliance.contentAudit.jAndJ.damSegment.listerineIndia',
    brand: 'Listerine',
    market: 'India',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.LISTERINE_JAPAN_IDENTIFIER]: {
    localizationKey:
      'ui.compliance.contentAudit.jAndJ.damSegment.listerineJapan',
    brand: 'Listerine',
    market: 'Japan',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.TYLENOL_USA_IDENTIFIER]: {
    localizationKey: 'ui.compliance.contentAudit.jAndJ.damSegment.tylenolUSA',
    brand: 'Tylenol',
    market: 'United States',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.NEUTROGENA_USA_IDENTIFIER]: {
    localizationKey:
      'ui.compliance.contentAudit.jAndJ.damSegment.neutrogenaUSA',
    brand: 'Neutrogena',
    market: 'United States',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.NEUTROGENA_GERMANY_IDENTIFIER]: {
    localizationKey:
      'ui.compliance.contentAudit.jAndJ.damSegment.neutrogenaGermany',
    brand: 'Neutrogena',
    market: 'Germany',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.JOHNSONS_BABY_UK_IDENTIFIER]: {
    localizationKey:
      'ui.compliance.contentAudit.jAndJ.damSegment.johnsonsBabyUK',
    brand: "Johnson's baby",
    market: 'United Kingdom',
  },
  [J_AND_J_DAM_SEGMENT_IDENTIFIERS.NICORETTE_UK_IDENTIFIER]: {
    localizationKey: 'ui.compliance.contentAudit.jAndJ.damSegment.nicoretteUK',
    brand: 'Nicorette',
    market: 'United Kingdom',
  },
};

const BATCH_STATUS = {
  COMPLETE: 'COMPLETE',
  PROCESSING: 'PROCESSING',
  SETUP: 'SETUP',
  SUBMITTED: 'SUBMITTED',
  RESUBMITTED: 'RESUBMITTED',
  ERROR: 'ERROR',
  DELETE: 'DELETE',
  VERSIONED: 'VERSIONED',
};

const EXECUTIVE_DASHBOARD_CHANNELS = [
  PLATFORM.DV360.IDENTIFIER,
  PLATFORM.FACEBOOK.IDENTIFIER,
  PLATFORM.GOOGLE_ADWORDS.IDENTIFIER,
  PLATFORM.LINKEDIN.IDENTIFIER,
  PLATFORM.PINTEREST.IDENTIFIER,
  PLATFORM.REDDIT.IDENTIFIER,
  PLATFORM.SNAPCHAT.IDENTIFIER,
  PLATFORM.TIKTOK.IDENTIFIER,
  PLATFORM.TWITTER.IDENTIFIER,
];

const EXECUTIVE_DASHBOARD_PLATFORMS = [
  PLATFORM.DV360,
  PLATFORM.FACEBOOK,
  PLATFORM.GOOGLE_ADWORDS,
  PLATFORM.LINKEDIN,
  PLATFORM.PINTEREST,
  PLATFORM.REDDIT,
  PLATFORM.SNAPCHAT,
  PLATFORM.TIKTOK,
  PLATFORM.TWITTER,
];

const PARTNER_CRITERIA_PLATFORMS = [
  {
    i18nName: 'ui.compliance.criteriaManagement.all.platforms.label',
    iconUrl: vidmobBlackIconUrl,
    id: ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.DV360.IDENTIFIER],
    iconUrl: PLATFORM.platformColorIconUrls[PLATFORM.DV360.IDENTIFIER],
    id: PLATFORM.DV360.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.META.IDENTIFIER],
    id: PLATFORM.FACEBOOK.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[
        PLATFORM.GOOGLE_ADWORDS.IDENTIFIER
      ],
    iconUrl: PLATFORM.platformColorIconUrls[PLATFORM.GOOGLE_ADWORDS.IDENTIFIER],
    id: PLATFORM.GOOGLE_ADWORDS.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.LINKEDIN.IDENTIFIER],
    iconUrl: PLATFORM.platformColorIconUrls[PLATFORM.LINKEDIN.IDENTIFIER],
    id: PLATFORM.LINKEDIN.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.PINTEREST.IDENTIFIER],
    iconUrl: PLATFORM.platformColorIconUrls[PLATFORM.PINTEREST.IDENTIFIER],
    id: PLATFORM.PINTEREST.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.REDDIT.IDENTIFIER],
    iconUrl:
      PLATFORM.platformSmallColorIconUrls.alt[PLATFORM.REDDIT.IDENTIFIER],
    id: PLATFORM.REDDIT.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.SNAPCHAT.IDENTIFIER],
    iconUrl: PLATFORM.platformColorIconUrls[PLATFORM.SNAPCHAT.IDENTIFIER],
    id: PLATFORM.SNAPCHAT.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.TIKTOK.IDENTIFIER],
    iconUrl: PLATFORM.platformColorIconUrls[PLATFORM.TIKTOK.IDENTIFIER],
    id: PLATFORM.TIKTOK.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[PLATFORM.TWITTER.IDENTIFIER],
    iconUrl: PLATFORM.platformColorIconUrls[PLATFORM.TWITTER.IDENTIFIER],
    id: PLATFORM.TWITTER.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[
        PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER
      ],
    iconUrl:
      PLATFORM.platformColorIconUrls[PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER],
    id: PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER,
  },
  {
    i18nName:
      PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[
        PLATFORM.AMAZON_ADVERTISING_DSP.IDENTIFIER
      ],
    iconUrl:
      PLATFORM.platformColorIconUrls[
        PLATFORM.AMAZON_ADVERTISING_DSP.IDENTIFIER
      ],
    id: PLATFORM.AMAZON_ADVERTISING_DSP.IDENTIFIER,
  },
].sort((a, b) => {
  const uppercaseNameA = a.i18nName.toUpperCase();
  const uppercaseNameB = b.i18nName.toUpperCase();
  return uppercaseNameA.localeCompare(uppercaseNameB);
});

// Dictionary to maintain recommendations for multi-select criteria templates
const MULTI_SELECT_TEMPLATE_RECOMMENDATIONS = {
  [CRITERIA_TEMPLATE_IDENTIFIERS.FRAMED_FOR_MOBILE]: {
    [ALL_PLATFORMS_TEMPLATE_IDENTIFIER]: {}, // No recommendations
    [PLATFORM.DV360.IDENTIFIER]: {
      // 16:9
      [MEDIA.VIDEO_ASPECT_RATIO.LANDSCAPE]: true,
    },
    [PLATFORM.FACEBOOK.IDENTIFIER]: {
      // 1:1, 4:5, 9:16
      [MEDIA.VIDEO_ASPECT_RATIO.SQUARE]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.ASPECT4x5]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.PORTRAIT]: true,
    },
    [PLATFORM.GOOGLE_ADWORDS.IDENTIFIER]: {
      // 16:9
      [MEDIA.VIDEO_ASPECT_RATIO.LANDSCAPE]: true,
    },
    [PLATFORM.LINKEDIN.IDENTIFIER]: {
      // 16:9
      [MEDIA.VIDEO_ASPECT_RATIO.LANDSCAPE]: true,
    },
    [PLATFORM.PINTEREST.IDENTIFIER]: {
      // 2:3, 1:1, 9:16, or 16:9
      [MEDIA.VIDEO_ASPECT_RATIO.ASPECT2x3]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.SQUARE]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.PORTRAIT]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.LANDSCAPE]: true,
    },
    [PLATFORM.SNAPCHAT.IDENTIFIER]: {
      // 9:16
      [MEDIA.VIDEO_ASPECT_RATIO.PORTRAIT]: true,
    },
    [PLATFORM.TIKTOK.IDENTIFIER]: {
      // 9:16
      [MEDIA.VIDEO_ASPECT_RATIO.PORTRAIT]: true,
    },
    [PLATFORM.TWITTER.IDENTIFIER]: {
      // 1:1, 2:3, 4:5, 16:9
      [MEDIA.VIDEO_ASPECT_RATIO.SQUARE]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.ASPECT2x3]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.ASPECT4x5]: true,
      [MEDIA.VIDEO_ASPECT_RATIO.LANDSCAPE]: true,
    },
  },
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_COLOR]: {
    [ALL_PLATFORMS_TEMPLATE_IDENTIFIER]: {}, // no recommendations
    [PLATFORM.DV360.IDENTIFIER]: {}, // no recommendations,
    [PLATFORM.FACEBOOK.IDENTIFIER]: {}, // no recommendations,
    [PLATFORM.GOOGLE_ADWORDS.IDENTIFIER]: {}, // no recommendations,
    [PLATFORM.LINKEDIN.IDENTIFIER]: {}, // no recommendations,
    [PLATFORM.PINTEREST.IDENTIFIER]: {}, // no recommendations,
    [PLATFORM.SNAPCHAT.IDENTIFIER]: {}, // no recommendations,
    [PLATFORM.TIKTOK.IDENTIFIER]: {}, // no recommendations,
    [PLATFORM.TWITTER.IDENTIFIER]: {}, // no recommendations
  },
};

// Dictionary to maintain info pills in multiselect
const MULTI_SELECT_TEMPLATE_INFO = {
  [CRITERIA_TEMPLATE_IDENTIFIERS.FRAMED_FOR_MOBILE]: {
    [PLATFORM.DV360.IDENTIFIER]: {
      [MEDIA.VIDEO_ASPECT_RATIO.RECTANGLE]:
        'ui.creativeScoring.reportDetails.filter.v2.criteria.HTML.only',
      [MEDIA.VIDEO_ASPECT_RATIO.LEADER_BOARD]:
        'ui.creativeScoring.reportDetails.filter.v2.criteria.HTML.only',
      [MEDIA.VIDEO_ASPECT_RATIO.WIDE_SKYSCRAPER]:
        'ui.creativeScoring.reportDetails.filter.v2.criteria.HTML.only',
      [MEDIA.VIDEO_ASPECT_RATIO.HALF_PAGE_AD]:
        'ui.creativeScoring.reportDetails.filter.v2.criteria.HTML.only',
      [MEDIA.VIDEO_ASPECT_RATIO.MOBILE_LEADER_BOARD]:
        'ui.creativeScoring.reportDetails.filter.v2.criteria.HTML.only',
      [MEDIA.VIDEO_ASPECT_RATIO.BILLBOARD]:
        'ui.creativeScoring.reportDetails.filter.v2.criteria.HTML.only',
      [MEDIA.VIDEO_ASPECT_RATIO.FULL_BANNER]:
        'ui.creativeScoring.reportDetails.filter.v2.criteria.HTML.only',
    },
  },
};

// for use on multiselects with chips
const CHIP_TYPE = {
  BEST_PRACTICE: 'BEST_PRACTICE',
  INFO: 'INFO',
};

// to designate aspect ratios that can only apply to DV360 ads (used in create criteria)
const DV360_ONLY_ASPECT_RATIOS = [
  MEDIA.VIDEO_ASPECT_RATIO.RECTANGLE,
  MEDIA.VIDEO_ASPECT_RATIO.LEADER_BOARD,
  MEDIA.VIDEO_ASPECT_RATIO.WIDE_SKYSCRAPER,
  MEDIA.VIDEO_ASPECT_RATIO.HALF_PAGE_AD,
  MEDIA.VIDEO_ASPECT_RATIO.MOBILE_LEADER_BOARD,
  MEDIA.VIDEO_ASPECT_RATIO.BILLBOARD,
  MEDIA.VIDEO_ASPECT_RATIO.FULL_BANNER,
];

// I18n template descriptions
const CRITERIA_TEMPLATE_DESCRIPTION = {
  [CRITERIA_TEMPLATE_IDENTIFIERS.VIDEO_DURATION]:
    'ui.compliance.criteriaManagement.template.video.duration.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE]:
    'ui.compliance.criteriaManagement.template.human.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE_ANYTIME]:
    'ui.compliance.criteriaManagement.template.human.presence.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION]:
    'ui.compliance.criteriaManagement.template.positive.emotion.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION_ANYTIME]:
    'ui.compliance.criteriaManagement.template.positive.emotion.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.MAX_WORDS_PER_FRAME]:
    'ui.compliance.criteriaManagement.template.max.frame.words.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.WORDS_PER_FRAME_IN_RANGE]:
    'ui.compliance.criteriaManagement.template.words.per.frame.range.in.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.FRAMED_FOR_MOBILE]:
    'ui.compliance.criteriaManagement.template.framed.for.mobile.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SCENE_CHANGE]:
    'ui.compliance.criteriaManagement.template.scene.change.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_PRODUCT]:
    'ui.compliance.criteriaManagement.template.product.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CTA_DETECTION]:
    'ui.compliance.criteriaManagement.template.call.to.action.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF]:
    'ui.compliance.criteriaManagement.template.jnj.sound.off.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_ON]:
    'ui.compliance.criteriaManagement.template.jnj.sound.on.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.product.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.beauty.pc.product.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PERSON_WITH_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.highlight.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_OR_LOGO_PERSISTS]:
    'ui.compliance.criteriaManagement.template.lusa.brand.persist.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.KEY_MESSAGING_PERSISTS]:
    'ui.compliance.criteriaManagement.template.key.messaging.persists.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_LINK]:
    'ui.compliance.criteriaManagement.template.abi.brandLink.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF_NO_TRANSCRIPTION]:
    'ui.compliance.criteriaManagement.template.sound.off.no.transcription.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_EARLY]:
    'ui.compliance.criteriaManagement.template.abi.product.early.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RESPONSIBLE_DRINK_MESSAGE]:
    'ui.compliance.criteriaManagement.template.abi.responsibleDrink.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_SUPERS_PRESENCE]:
    'ui.compliance.criteriaManagement.template.abi.supers.early.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_VOICE_OVER_ANYTIME]:
    'ui.compliance.criteriaManagement.template.loreal.vo.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_FRAME_TIGHTLY]:
    'ui.compliance.criteriaManagement.template.abi.frame.tightly.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_EARLY_CTA]:
    'ui.compliance.criteriaManagement.template.earlyCta.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_COLOR]:
    'ui.compliance.criteriaManagement.template.abi.brandColor.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_VISUAL_CTA_PRESENCE]:
    'ui.compliance.criteriaManagement.template.jnj.visual.cta.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_CELEBRITY_PRESENCE]:
    'ui.compliance.criteriaManagement.template.snap.celebrity.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.brand.audio.mention.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_IN_AUDIO_NEAR_END]:
    'ui.compliance.criteriaManagement.template.brand.audio.last.mention.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_OR_LOGO_NEAR_END]:
    'ui.compliance.criteriaManagement.template.brand.logo.last.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_TECHNOLOGY_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.product.technology.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.OFFER_MESSAGE]:
    'ui.compliance.criteriaManagement.template.offer.message.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RETAILER_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.retailer.logo.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.product.anytime.logo.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PACE_QUICKLY]:
    'ui.compliance.criteriaManagement.template.abi.pace.quickly.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_DYNAMIC_START]:
    'ui.compliance.criteriaManagement.template.abi.dynamic.start.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_AUDIO]:
    'ui.compliance.criteriaManagement.template.custom.audio.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_TEXT]:
    'ui.compliance.criteriaManagement.template.custom.text.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_COLOR]:
    'ui.compliance.criteriaManagement.template.custom.color.description',
};

const CUSTOM_AUDIO_NAMES = {
  AUDIO_TAGLINES: 'AUDIO_TAGLINES',
  AUDIO_PRODUCT_BENEFITS: 'AUDIO_SPECIFIC_BENEFITS',
  AUDIO_CALLS_TO_ACTION: 'AUDIO_CALLS_TO_ACTION',
  AUDIO_DISCLAIMERS: 'AUDIO_DISCLAIMERS',
  AUDIO_LEGAL_INFORMATION: 'AUDIO_LEGAL_INFORMATION',
  AUDIO_TERMS_CONDITIONS: 'AUDIO_TERMS_CONDITIONS',
  AUDIO_PRODUCT_CLAIMS: 'AUDIO_PRODUCT_CLAIMS',
  AUDIO_PRODUCT_FEATURES: 'AUDIO_PRODUCT_FEATURES',
  AUDIO_PROMOTIONAL_OFFERS: 'AUDIO_PROMOTIONAL_OFFERS',
  AUDIO_FLAGGED_WORDS: 'AUDIO_FLAGGED_WORDS',
  AUDIO_OTHER: 'AUDIO_OTHER',
};
const CUSTOM_TEXT_NAMES = {
  WRITTEN_PRODUCT_BENEFITS: 'WRITTEN_SPECIFIC_BENEFITS',
  WRITTEN_CALLS_TO_ACTION: 'WRITTEN_CALLS_TO_ACTION',
  WRITTEN_DISCLAIMERS: 'WRITTEN_DISCLAIMERS',
  WRITTEN_LEGAL_INFORMATION: 'WRITTEN_LEGAL_INFORMATION',
  WRITTEN_TERMS_CONDITIONS: 'WRITTEN_TERMS_CONDITIONS',
  WRITTEN_PRODUCT_CLAIMS: 'WRITTEN_PRODUCT_CLAIMS',
  WRITTEN_PRODUCT_FEATURES: 'WRITTEN_PRODUCT_FEATURES',
  WRITTEN_PROMOTIONAL_OFFERS: 'WRITTEN_PROMOTIONAL_OFFERS',
  WRITTEN_TAGLINES: 'WRITTEN_TAGLINES',
  WRITTEN_FLAGGED_WORDS: 'WRITTEN_FLAGGED_WORDS',
  WRITTEN_OTHER: 'WRITTEN_OTHER',
};
const CUSTOM_COLOR_NAMES = {
  BRAND_COLORS: 'BRAND_COLORS',
};

const CUSTOM_CRITERIA_DISPLAY_NAMES = {
  [CUSTOM_TEXT_NAMES.WRITTEN_PRODUCT_BENEFITS]:
    'ui.compliance.criteriaManagement.names.custom.text.specific.benefits',
  [CUSTOM_TEXT_NAMES.WRITTEN_CALLS_TO_ACTION]:
    'ui.compliance.criteriaManagement.names.custom.text.calls.to.action',
  [CUSTOM_TEXT_NAMES.WRITTEN_DISCLAIMERS]:
    'ui.compliance.criteriaManagement.names.custom.text.written.disclaimers',
  [CUSTOM_TEXT_NAMES.WRITTEN_LEGAL_INFORMATION]:
    'ui.compliance.criteriaManagement.names.custom.text.legal.information',
  [CUSTOM_TEXT_NAMES.WRITTEN_TERMS_CONDITIONS]:
    'ui.compliance.criteriaManagement.names.custom.text.terms.conditions',
  [CUSTOM_TEXT_NAMES.WRITTEN_PRODUCT_CLAIMS]:
    'ui.compliance.criteriaManagement.names.custom.text.product.claims',
  [CUSTOM_TEXT_NAMES.WRITTEN_PRODUCT_FEATURES]:
    'ui.compliance.criteriaManagement.names.custom.text.product.features',
  [CUSTOM_TEXT_NAMES.WRITTEN_PROMOTIONAL_OFFERS]:
    'ui.compliance.criteriaManagement.names.custom.text.promotional.offers',
  [CUSTOM_TEXT_NAMES.WRITTEN_TAGLINES]:
    'ui.compliance.criteriaManagement.names.custom.text.written.taglines',
  [CUSTOM_TEXT_NAMES.WRITTEN_FLAGGED_WORDS]:
    'ui.compliance.criteriaManagement.names.custom.text.flagged.words',
  [CUSTOM_TEXT_NAMES.WRITTEN_OTHER]:
    'ui.compliance.criteriaManagement.names.custom.text.other',

  [CUSTOM_AUDIO_NAMES.AUDIO_TAGLINES]:
    'ui.compliance.criteriaManagement.names.custom.audio.taglines',
  [CUSTOM_AUDIO_NAMES.AUDIO_PRODUCT_BENEFITS]:
    'ui.compliance.criteriaManagement.names.custom.audio.specific.benefits',
  [CUSTOM_AUDIO_NAMES.AUDIO_CALLS_TO_ACTION]:
    'ui.compliance.criteriaManagement.names.custom.audio.calls.to.action',
  [CUSTOM_AUDIO_NAMES.AUDIO_DISCLAIMERS]:
    'ui.compliance.criteriaManagement.names.custom.audio.disclaimers',
  [CUSTOM_AUDIO_NAMES.AUDIO_LEGAL_INFORMATION]:
    'ui.compliance.criteriaManagement.names.custom.audio.legal.information',
  [CUSTOM_AUDIO_NAMES.AUDIO_TERMS_CONDITIONS]:
    'ui.compliance.criteriaManagement.names.custom.audio.terms.conditions',
  [CUSTOM_AUDIO_NAMES.AUDIO_PRODUCT_CLAIMS]:
    'ui.compliance.criteriaManagement.names.custom.audio.product.claims',
  [CUSTOM_AUDIO_NAMES.AUDIO_PRODUCT_FEATURES]:
    'ui.compliance.criteriaManagement.names.custom.audio.product.features',
  [CUSTOM_AUDIO_NAMES.AUDIO_PROMOTIONAL_OFFERS]:
    'ui.compliance.criteriaManagement.names.custom.audio.promotional.offers',
  [CUSTOM_AUDIO_NAMES.AUDIO_FLAGGED_WORDS]:
    'ui.compliance.criteriaManagement.names.custom.audio.flagged.words',
  [CUSTOM_AUDIO_NAMES.AUDIO_OTHER]:
    'ui.compliance.criteriaManagement.names.custom.audio.other',

  [CUSTOM_COLOR_NAMES.BRAND_COLORS]:
    'ui.compliance.criteriaManagement.names.custom.color.brand.colors',
};

const CRITERIA_DEFINITIONS = {
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO_NEAR_END]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.near.end.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_IN_AUDIO_NEAR_END]:
    'ui.compliance.criteriaManagement.template.brand.name.audio.near.end.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.brand.name.audio.anytime',
  [CRITERIA_TEMPLATE_IDENTIFIERS.FRAMED_FOR_MOBILE]:
    'ui.compliance.criteriaManagement.template.framed.for.mobile.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.beauty.pc.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PERSON_WITH_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.highlight.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF]:
    'ui.compliance.criteriaManagement.template.jnj.sound.off.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE]:
    'ui.compliance.criteriaManagement.template.human.presence.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE_ANYTIME]:
    'ui.compliance.criteriaManagement.template.human.presence.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION]:
    'ui.compliance.criteriaManagement.template.positive.emotion.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION_ANYTIME]:
    'ui.compliance.criteriaManagement.template.positive.emotion.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.VIDEO_DURATION]:
    'ui.compliance.criteriaManagement.template.video.duration.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.MAX_WORDS_PER_FRAME]:
    'ui.compliance.criteriaManagement.template.max.frame.words.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.WORDS_PER_FRAME_IN_RANGE]:
    'ui.compliance.criteriaManagement.template.words.per.frame.in.range.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_VISUAL_CTA_PRESENCE]:
    'ui.compliance.criteriaManagement.template.jnj.visual.cta.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_ON]:
    'ui.compliance.criteriaManagement.template.jnj.sound.on.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CTA_DETECTION]:
    'ui.compliance.criteriaManagement.template.call.to.action.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_EARLY_CTA]:
    'ui.compliance.criteriaManagement.template.early.cta.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_EARLY]:
    'ui.compliance.criteriaManagement.template.abi.product.early.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RESPONSIBLE_DRINK_MESSAGE]:
    'ui.compliance.criteriaManagement.template.abi.responsibleDrink.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_SUPERS_PRESENCE]:
    'ui.compliance.criteriaManagement.template.abi.supers.early.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_OR_LOGO_PERSISTS]:
    'ui.compliance.criteriaManagement.template.lusa.brand.persist.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.KEY_MESSAGING_PERSISTS]:
    'ui.compliance.criteriaManagement.template.key.messaging.persists.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_LINK]:
    'ui.compliance.criteriaManagement.template.abi.brandLink.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF_NO_TRANSCRIPTION]:
    'ui.compliance.criteriaManagement.template.sound.off.no.transcription.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_CELEBRITY_PRESENCE]:
    'ui.compliance.criteriaManagement.template.snap.celebrity.presence.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_COLOR]:
    'ui.compliance.criteriaManagement.template.abi.brandColor.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_TECHNOLOGY_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.product.technology.presence.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.OFFER_MESSAGE]:
    'ui.compliance.criteriaManagement.template.offer.message.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RETAILER_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.retailer.logo.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.product.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PACE_QUICKLY]:
    'ui.compliance.criteriaManagement.template.abi.pace.quickly.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_DYNAMIC_START]:
    'ui.compliance.criteriaManagement.template.abi.dynamic.start.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_PARAMS]:
    'ui.compliance.criteriaManagement.template.custom_params.dynamic.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PACKAGE_DESCRIBES_PRODUCT]:
    'ui.compliance.criteriaManagement.template.package.describes.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PACKAGE_HAS_COLOR]:
    'ui.compliance.criteriaManagement.template.package.has.color.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PACKAGE_HAS_LOGO]:
    'ui.compliance.criteriaManagement.template.package.has.logo.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PACKAGE_SHOWS_PRODUCT]:
    'ui.compliance.criteriaManagement.template.package.shows.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.FILE_SIZE_LIMIT]:
    'ui.compliance.criteriaManagement.template.file.size.limit.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.USER_GENERATED_CONTENT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.user.generated.content.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.USER_GENERATED_CONTENT_EARLY]:
    'ui.compliance.criteriaManagement.template.user.generated.content.early.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.USER_GENERATED_CONTENT_NEAR_END]:
    'ui.compliance.criteriaManagement.template.user.generated.content.near.end.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.FITS_SIZE]:
    'ui.compliance.criteriaManagement.template.fits.size.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOGO_PLACEMENT]:
    'ui.compliance.criteriaManagement.template.logo.placement.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.TEXT_AND_LOGO_PRESENT_EARLY]:
    'ui.compliance.criteriaManagement.template.text.and.logo.present.early.definition',
};

const BATCH_TYPE = {
  IN_FLIGHT: 'IN_FLIGHT',
  PRE_FLIGHT: 'PRE_FLIGHT',
  PLUGIN_MEDIA: 'PLUGIN_MEDIA',
};

const REPORT_TYPE = {
  IN_FLIGHT: BATCH_TYPE.IN_FLIGHT,
  PRE_FLIGHT: BATCH_TYPE.PRE_FLIGHT,
  AD_ACCOUNT: 'AD_ACCOUNT',
  PLUGIN_MEDIA: 'PLUGIN_MEDIA',
};

const REPORT_TYPE_DISPLAY = {
  ALL_REPORTS: 'All reports',
  [REPORT_TYPE.AD_ACCOUNT]: 'Ad account',
  [REPORT_TYPE.PRE_FLIGHT]: 'Pre-flight',
  [REPORT_TYPE.IN_FLIGHT]: 'In-flight',
};

const BRAND_SCORE_PAGE_VIEW = {
  CRITERIA: 'Criteria',
  ASSETS: 'Assets',
};

const REPORT_TABLE_HEADER_KEYS = {
  CREATED_BY: 'created by',
  DATE_CREATED: 'date created',
  LAST_MODIFIED: 'last modified',
  LOCATION: 'location',
  NAME: 'name',
  TYPE: 'report type',
  SCORE: 'score',
  CREATIVES_OPTIMIZED: 'creatives optimized',
};

const SUBMISSION_REPORT_STATUS = {
  NOT_UPLOADING: 'NOT_UPLOADING',
  UPLOADING: 'UPLOADING',
  PROCESSING_UPLOADS: 'PROCESSING_UPLOADS',
  READY_FOR_SUBMISSION: 'READY',
};

const COMPLIANCE_TABS_NAME_IDS = {
  dashboard: 'ui.compliance.navTabs.executiveDashboard',
  reports: 'ui.compliance.navTabs.reports',
  'rollup-reports': 'ui.compliance.navTabs.rollUpReports',
  'brand-score': 'ui.compliance.navTabs.brandScore',
  'criteria-management': 'ui.compliance.navTabs.criteriaManagement',
  'platform-fitness-score': 'ui.compliance.navTabs.platformFitnessScore',
  'scorecards-in-flight':
    'ui.site.pageTitle.ci.compliance.scorecardsLanding.inFlight',
  'scorecards-pre-flight':
    'ui.site.pageTitle.ci.compliance.scorecardsLanding.preFlight',
  'scorecards-landing': 'ui.compliance.navTabs.reports',
};

const CRITERIA_PARAMETER_UNIT_NAME = {
  SECONDS: 'SECONDS',
  DIMENSIONS: 'DIMENSIONS',
  WORDS_PER_FRAME: 'WORDS_PER_FRAME',
  RATIO: 'RATIO',
  PERCENTAGE: 'PERCENTAGE',
  MIN_DURATION: 'minDuration',
  MAX_DURATION: 'maxDuration',
  CHANGES: 'CHANGES',
  COLOR: 'COLOR',
  NONE: 'NONE',
  MIN_WORDS: 'minWords',
  MAX_WORDS: 'maxWords',
};

const CRITERIA_PARAMETER_UNIT_LABEL = {
  [CRITERIA_PARAMETER_UNIT_NAME.SECONDS]:
    'ui.compliance.criteriaManagement.template.parameter.label.seconds',
  [CRITERIA_PARAMETER_UNIT_NAME.WORDS_PER_FRAME]:
    'ui.compliance.criteriaManagement.template.parameter.label.words',
  [CRITERIA_PARAMETER_UNIT_NAME.RATIO]:
    'ui.compliance.criteriaManagement.template.parameter.label.ratio',
  [CRITERIA_PARAMETER_UNIT_NAME.PERCENTAGE]:
    'ui.compliance.criteriaManagement.template.parameter.label.percent',
  [CRITERIA_PARAMETER_UNIT_NAME.MIN_DURATION]:
    'ui.compliance.criteriaManagement.template.parameter.label.min.duration',
  [CRITERIA_PARAMETER_UNIT_NAME.MAX_DURATION]:
    'ui.compliance.criteriaManagement.template.parameter.label.max.duration',
  [CRITERIA_PARAMETER_UNIT_NAME.CHANGES]:
    'ui.compliance.criteriaManagement.template.parameter.label.scene.changes',
  [CRITERIA_PARAMETER_UNIT_NAME.COLOR]:
    'ui.compliance.criteriaManagement.template.parameter.label.color',
  [CRITERIA_PARAMETER_UNIT_NAME.NONE]:
    'ui.compliance.criteriaManagement.template.parameter.label.required',
  [CRITERIA_PARAMETER_UNIT_NAME.DIMENSIONS]:
    'ui.compliance.criteriaManagement.template.parameter.label.dimensions',
  [CRITERIA_PARAMETER_UNIT_NAME.MIN_WORDS]:
    'ui.compliance.criteriaManagement.template.parameter.label.min.words',
  [CRITERIA_PARAMETER_UNIT_NAME.MAX_WORDS]:
    'ui.compliance.criteriaManagement.template.parameter.label.max.words',
};

const ASPECT_RATIOS = 'aspectRatios';
const BRAND_COLORS = 'brandColors';
const ALLOWED_SIZES = 'allowedSizes';

const MULTI_SELECT_PARAMETERS = [ASPECT_RATIOS, BRAND_COLORS, ALLOWED_SIZES];

const MULTI_VALUE_TEMPLATES = [
  CRITERIA_TEMPLATE_IDENTIFIERS.VIDEO_DURATION,
  CRITERIA_TEMPLATE_IDENTIFIERS.WORDS_PER_FRAME_IN_RANGE,
];

const NO_UNIT_TEMPLATES = [CRITERIA_TEMPLATE_IDENTIFIERS.CTA_DETECTION];

const BOOLEAN_TEMPLATES = [CRITERIA_TEMPLATE_IDENTIFIERS.CTA_DETECTION];

const OUTDATED_BATCH_REASONS = {
  BRAND_CHANGE: 'BRAND_CHANGE',
  CRITERIA_CHANGE: 'CRITERIA_CHANGE',
};

const LEARN_MORE_LINKS = {
  BANNER_LINK:
    'https://help.vidmob.com/en/collections/2983248-understanding-brand-governance',
  ANALYSIS_LINK:
    'https://help.vidmob.com/en/articles/6314561-what-types-of-files-can-i-upload-to-creative-scoring',
};

const BOOLEAN_VALUE_STRINGS = {
  0: 'ui.compliance.criteriaManagement.template.parameter.label.boolean.false',
  1: 'ui.compliance.criteriaManagement.template.parameter.label.boolean.true',
};

const BEST_PRACTICE_BADGE =
  'ui.compliance.criteriaManagement.bestPractice.badge';

const AUDIT_STATUS = {
  CLOSED: 'CLOSED',
  OPEN: 'OPEN',
  RESOLVED: 'RESOLVED',
};

const IN_FLIGHT_BATCH_DATE_FORMAT = 'YYYY-MM-DD';

const VDS_PLATFORM_IDENTIFIERS = {
  [PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER]: 'amazonadvertising',
  [PLATFORM.DROPBOX.IDENTIFIER]: 'dropbox',
  [PLATFORM.DV360.IDENTIFIER]: 'dv360',
  [PLATFORM.FACEBOOK.IDENTIFIER]: 'facebook',
  [PLATFORM.FACEBOOKPAGE.IDENTIFIER]: 'facebook',
  [PLATFORM.FACEBOOK_PAGES.IDENTIFIER]: 'facebook',
  [PLATFORM.FACEBOOK_AD_ACCOUNT.IDENTIFIER]: 'facebook',
  [PLATFORM.GOOGLE_ADWORDS.IDENTIFIER]: 'google-ads',
  [PLATFORM.GOOGLEDRIVE.IDENTIFIER]: 'google-drive',
  [PLATFORM.LINKEDIN.IDENTIFIER]: 'linkedin',
  [PLATFORM.PINTEREST.IDENTIFIER]: 'pinterest',
  [PLATFORM.REDDIT.IDENTIFIER]: 'reddit',
  [PLATFORM.SLACK.IDENTIFIER]: 'slack',
  [PLATFORM.SNAPCHAT.IDENTIFIER]: 'snap',
  [PLATFORM.SNAPCHAT.SHORT_IDENTIFIER]: 'snap',
  [PLATFORM.TIKTOK.IDENTIFIER]: 'tiktok',
  [PLATFORM.TWITTER.IDENTIFIER]: 'twitter',
  [STANDARD_CRITERIA_PLATFORM_IDENTIFIER]: 'vidmob',
  [ALL_PLATFORMS_TEMPLATE_IDENTIFIER]: 'vidmob',
};

const REPORT_TABLE_SORT_IDENTIFIER_OBJECT_MAP = {
  [REPORT_TABLE_HEADER_KEYS.CREATED_BY]: 'createdBy',
  [REPORT_TABLE_HEADER_KEYS.DATE_CREATED]: 'dateCreated',
  [REPORT_TABLE_HEADER_KEYS.LAST_MODIFIED]: 'lastUpdated',
  [REPORT_TABLE_HEADER_KEYS.LOCATION]: 'countries',
  [REPORT_TABLE_HEADER_KEYS.NAME]: 'name',
  [REPORT_TABLE_HEADER_KEYS.TYPE]: 'type',
  [REPORT_TABLE_HEADER_KEYS.SCORE]: 'score',
  [REPORT_TABLE_HEADER_KEYS.CREATIVES_OPTIMIZED]: 'creativesOptimized',
};

const CRITERIA_ABSENCE_IDENTIFIER = 'NO_CRITERIA';
const BLANK_STATE_AGGREGATE_CRITERIA_ITEM = [
  {
    id: CRITERIA_ABSENCE_IDENTIFIER,
  },
];

const SUPPORTED_UPLOAD_FILE_TYPES =
  'image/jpg,image/jpeg,image/gif,video/mp4,image/png,video/quicktime';
const MAX_MEDIA_SUPPORTED_FOR_INFLIGHT = 500;
const WARNING_MEDIA_COUNT_SUPPORTED_FOR_INFLIGHT = 300;

const FILE_TYPES_FOR_FILTERING = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  ANIMATED_IMAGE: 'ANIMATED_IMAGE',
  HTML: 'HTML',
};

const LOCATIONS_FILTER_OPTIONS_NOT_SPECIFIED = {
  name: 'Not Specified',
  isoCode: NOT_SPECIFIED_IDENTIFIER,
};

const APPLICABILITY_MEDIA_TYPES = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  ANIMATED_IMAGE: 'ANIMATED_IMAGE',
  HTML: 'HTML',
};

const CRITERIA_TEMPLATE_APPLICABILITY = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  VIDEO_ONLY: 'VIDEO_ONLY',
  ALL: 'ALL',
};

const SCORE_OVERRIDE = {
  STATUS: {
    OPEN: 'OPEN',
    RESOLVED: 'RESOLVED',
  },
  RESOLUTION: {
    SCORE_CHANGED: 'SCORE_CHANGED',
    SCORE_NOT_CHANGED: 'SCORE_NOT_CHANGED',
  },
  ACTION: {
    APPROVE: 'APPROVE',
    REJECT: 'REJECT',
  },
  RESOLUTION_LABEL: {
    SCORE_CHANGED: 'Approved',
    SCORE_NOT_CHANGED: 'Rejected',
  },
};

const SCORECARDS_NAV_IDENTIFIERS = {
  scorecardsLandingInflight: 'scorecards-in-flight',
  scorecardsLandingPreFlight: 'scorecards-pre-flight',
};

const SCORECARDS_LANDING_SCORECARD_NAV_TYPES = {
  PRE_FLIGHT: 'pre-flight',
  IN_FLIGHT: 'in-flight',
};

const SCORECARD_TYPES = {
  IN_FLIGHT: BATCH_TYPE.IN_FLIGHT,
  PRE_FLIGHT: BATCH_TYPE.PRE_FLIGHT,
  AD_ACCOUNT: 'AD_ACCOUNT',
  PLUGIN_MEDIA: 'PLUGIN_MEDIA',
};

const SCORECARDS_LANDING_COLUMN_KEYS = {
  CREATED_BY: 'createdBy',
  DATE_CREATED: 'dateCreated',
  LAST_MODIFIED: 'lastModified',
  MARKET: 'markets',
  BRAND: 'brands',
  NAME: 'name',
  SCORE: 'score',
  BLANK_SPACE: 'blank',
  SCORECARD_TYPE: 'scorecardType',
  AD_ACCOUNT: 'adAccount',
  CREATIVES_OPTIMIZED: 'creativesOptimized',
  ACTIONS: 'actions',
  NUMBER_OF_CREATIVES: 'totalMediaCount',
  CHANNEL: 'platforms',
  PROJECT: 'projectName',
  OUTPUT: 'outputVideoName',
  DRAFT: 'draft',
  SOURCE: 'deviceIdentifier',
};

const INDIVIDUAL_MEDIA_COLUMN_KEYS = {
  CRITERIA: 'criteria',
  CHANNEL: 'platform',
  SCORE: 'score',
};

const CUSTOM_CRITERIA_DEFINITION_SNIPPET = {
  AUDIO: 'audio',
  TEXT: 'text',
};

const SCORECARD_DETAILS_COLUMN_KEYS = {
  NAME: 'name',
  FILE_TYPE: 'fileType',
  SCORE: 'score',
  OPTIMIZATIONS: 'optimizations',
  LINK: 'link',
};

const CONSIDERATION_TYPES = {
  MANDATORY: 'MANDATORY',
  OPTIONAL: 'OPTIONAL',
};

const CRITERIA_IS_OPTIONAL_OPTIONS = {
  MANDATORY: {
    id: 0,
    label:
      'ui.compliance.criteriaManagement.add.criteria.criteriaIsOptional.consideration.content.mandatory',
  },
  OPTIONAL: {
    id: 1,
    label:
      'ui.compliance.criteriaManagement.add.criteria.criteriaIsOptional.consideration.content.optional',
  },
};

export const CRITERIA_IS_GLOBAL_OPTIONS = {
  WORKSPACE: {
    id: 0,
    label:
      'ui.compliance.criteriaManagement.add.criteria.organizationCriteria.option.workspace',
  },
  ORGANIZATION: {
    id: 1,
    label:
      'ui.compliance.criteriaManagement.add.criteria.organizationCriteria.option.organization',
  },
};

export const IN_FLIGHT_SCORECARDS_HAVE_MOVED_NOTIFICATION_COUNT_LOCAL_STORAGE_KEY =
  'inFlightScorecardsHaveMovedNotificationCount';
export const NUMBER_OF_REMINDERS_THAT_IN_FLIGHT_SCORECARDS_HAVE_MOVED = 5;

export default {
  ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
  ALL_CHANNELS_IDENTIFIER,
  ALL_LOCATIONS_IDENTIFIER,
  NOT_SPECIFIED_IDENTIFIER,
  STANDARD_CRITERIA_PLATFORM_IDENTIFIER,
  LEARN_MORE_LINKS,
  BLANK_TEMPLATE_DESCRIPTION_PARAMETER,
  J_AND_J_DAM_SEGMENTS,
  J_AND_J_DAM_SEGMENT_IDENTIFIERS,
  COMPLIANCE_TABS_NAME_IDS,
  VIDMOB_CRITERIA_LABEL,
  CONTENT_AUDIT_CRITERIA,
  CONTENT_AUDIT_CRITERIA_DESCRIPTION,
  INDIVIDUAL_CRITERIA_RESULTS_API_VALUES_TO_INTERNAL_VALUES,
  INDIVIDUAL_CRITERIA_RESULTS_API_VALUES,
  INDIVIDUAL_CRITERIA_RESULTS,
  CONTENT_AUDIT_FILTER_STATES,
  PLATFORM_MEDIA_ERROR_CODES,
  MOBILE_FITNESS_SCORE,
  COMPLIANCE_MOBILE_FITNESS_SCORE_WIDGET_INSIGHT_TEXT,
  COMPLIANCE_MOBILE_FITNESS_SCORE_TEST_DATA,
  BATCH_STATUS,
  PARTNER_CRITERIA_PLATFORMS,
  CRITERIA_TEMPLATE_DESCRIPTION,
  CRITERIA_DEFINITIONS,
  CRITERIA_PARAMETER_UNIT_LABEL,
  CRITERIA_TEMPLATE_IDENTIFIERS,
  BATCH_TYPE,
  SUBMISSION_REPORT_STATUS,
  MULTI_SELECT_PARAMETERS,
  ALLOWED_SIZES,
  MULTI_VALUE_TEMPLATES,
  MULTI_SELECT_TEMPLATE_RECOMMENDATIONS,
  OUTDATED_BATCH_REASONS,
  NO_UNIT_TEMPLATES,
  BOOLEAN_VALUE_STRINGS,
  BOOLEAN_TEMPLATES,
  CRITERIA_PARAMETER_UNIT_NAME,
  BEST_PRACTICE_BADGE,
  EXECUTIVE_DASHBOARD_CHANNELS,
  REPORT_TYPE,
  REPORT_TYPE_DISPLAY,
  AUDIT_STATUS,
  EXECUTIVE_DASHBOARD_PLATFORMS,
  IN_FLIGHT_BATCH_DATE_FORMAT,
  REPORT_TABLE_HEADER_KEYS,
  BRAND_SCORE_PAGE_VIEW,
  VDS_PLATFORM_IDENTIFIERS,
  REPORT_TABLE_SORT_IDENTIFIER_OBJECT_MAP,
  BLANK_STATE_AGGREGATE_CRITERIA_ITEM,
  CRITERIA_ABSENCE_IDENTIFIER,
  SUPPORTED_UPLOAD_FILE_TYPES,
  MAX_MEDIA_SUPPORTED_FOR_INFLIGHT,
  WARNING_MEDIA_COUNT_SUPPORTED_FOR_INFLIGHT,
  CUSTOM_CRITERIA_IDENTIFIERS,
  LOCATIONS_FILTER_OPTIONS_NOT_SPECIFIED,
  FILE_TYPES_FOR_FILTERING,
  CUSTOM_CRITERIA_DISPLAY_NAMES,
  CUSTOM_COLOR_NAMES,
  CUSTOM_TEXT_NAMES,
  CUSTOM_AUDIO_NAMES,
  CUSTOM_AUDIO_IDENTIFIER,
  CUSTOM_COLOR_IDENTIFIER,
  CUSTOM_TEXT_IDENTIFIER,
  APPLICABILITY_MEDIA_TYPES,
  SCORE_OVERRIDE,
  SCORECARDS_NAV_IDENTIFIERS,
  SCORECARDS_LANDING_COLUMN_KEYS,
  SCORECARDS_LANDING_SCORECARD_NAV_TYPES,
  SCORECARD_DETAILS_COLUMN_KEYS,
  SCORECARD_TYPES,
  INDIVIDUAL_MEDIA_COLUMN_KEYS,
  CRITERIA_IS_OPTIONAL_OPTIONS,
  FILTER_CRITERIA_SCORE_RESULTS_API_VALUES,
  CUSTOM_CRITERIA_DEFINITION_SNIPPET,
  CONSIDERATION_TYPES,
  MULTI_SELECT_TEMPLATE_INFO,
  CHIP_TYPE,
  DV360_ONLY_ASPECT_RATIOS,
  CRITERIA_IS_GLOBAL_OPTIONS,
  CRITERIA_TEMPLATE_APPLICABILITY,
  INDIVIDUAL_CRITERIA_RESULTS_PILL_MESSAGES_KEYS,
};

export const SCORECARD_AND_REPORTS_HELP_CENTER_URL =
  'https://help.vidmob.com/en/articles/10041396-understanding-how-reports-and-scorecards-are-calculated';

export const IN_FLIGHT_REPORT_HELP_CENTER_URL =
  'https://help.vidmob.com/en/articles/10041396-understanding-how-reports-and-checks-are-calculated';

export const DIVERSITY_REPORT_HELP_CENTER_URL =
  'https://help.vidmob.com/en/articles/8672223-what-are-diversity-reports-beta';

export const INDIVIDUAL_CREATIVE_VIEW_MAX_DURATION_INTERVALS = 25;

export const SCORING_ICV_DURATION_SCRUBBER_PADDING = 4;
