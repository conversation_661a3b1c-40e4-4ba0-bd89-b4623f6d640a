import { COMPLIANCE } from '../../constants';
import { CreativeExamples } from '../../creativeAnalytics/__pages/CriteriaPerformanceReport/CriteriaPerformanceReportTypes';
import { Constraint } from '../components/__subpages/CreativeScoringCriteriaCreate/queries/customRules/types';
import {
  CRITERIA_MANAGEMENT_FILTER_FIELDS,
  CRITERIA_MANAGEMENT_MODAL_TYPES,
} from '../constants/criteriaManagement.constants';

const { VIDEO, IMAGE, ANIMATED_IMAGE } = COMPLIANCE.APPLICABILITY_MEDIA_TYPES;
const { MANDATORY, OPTIONAL } = COMPLIANCE.CONSIDERATION_TYPES;
const { PARTNER_CRITERIA_PLATFORMS } = COMPLIANCE;

export interface CriterionServerType {
  id: number;
  identifier: string;
  isOptional: typeof MANDATORY | typeof OPTIONAL;
  isBestPractice: boolean;
  parameters: Parameters;
  custom: CustomCriteria;
  name: string;
  defaultDisplayName: string;
  rule: string;
  description: string;
  platform: Platform;
  mediaTypes: (typeof VIDEO | typeof IMAGE | typeof ANIMATED_IMAGE)[];
  dateCreated: string;
  owner: Person;
  criteriaSet: CriteriaSet;
  category: string;
  customIconId?: string;
  customIconUrl?: string;
  criteriaGroups: { id: string; name: string }[];
}

export interface CriterionTableType
  extends Omit<
    CriterionServerType,
    'criteriaSet' | 'parameters' | 'custom' | 'mediaTypes'
  > {
  isGlobal: boolean;
  workspaces: string;
}

export interface Criterion {
  id: number | string;
  name: string;
  defaultDisplayName: string;
  rule: string;
  description: string;
  platform: Platform;
  mediaTypes: string;
  isBestPractice: boolean;
  isOptional: string;
  dateCreated: string;
  owner: Person;
  workspaces: string;
  isGlobal: boolean;
  category?: string | string[];
  creativeExamples?: CreativeExamples;
  customIconUrl?: string;
  customIconId?: string;
  criteriaGroups: { id: string; name: string; color: string }[];
}

export interface CustomCriteria {
  customName: string;
  customDescription: string;
  customValues: string[];
  isCustom: boolean;
}

export type ConstraintIdentifierType = {
  constraints: Constraint[];
  identifier: string;
};

export type ParametersConstraintType = {
  [key in 'any' | 'all']: {
    [key in 'any' | 'all']: (
      | ConstraintIdentifierType
      | {
          not: ConstraintIdentifierType;
        }
    )[];
  }[];
};

export interface Parameters extends ParametersConstraintType {
  rule: string;
  platform: string;
}

export type Platform = (typeof PARTNER_CRITERIA_PLATFORMS)[number]['id'];

export interface Person {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  photo: string | null;
}

interface CriteriaSet {
  id: number;
  isGlobal: number;
}

export type Field =
  (typeof CRITERIA_MANAGEMENT_FILTER_FIELDS)[keyof typeof CRITERIA_MANAGEMENT_FILTER_FIELDS];
export type SelectedItems = (string | number | boolean)[];

export interface CurrentBestPracticesData {
  [
    key: (typeof PARTNER_CRITERIA_PLATFORMS)[number]['id']
  ]: CurrentChannelBestPractices;
}

export interface OutdatedBestPracticesData {
  [
    key: (typeof PARTNER_CRITERIA_PLATFORMS)[number]['id']
  ]: OutdatedChannelBestPractices;
}

export interface CurrentChannelBestPractices {
  addedBestPractices: number;
  hasAllBestPractices: boolean;
  remainingBestPractices: number;
  totalBestPractices: number;
  bestPracticeDetails: BestPracticeDetailsV2[];
}

export type OutdatedChannelBestPractices = BestPracticeDetailsV2[];

export interface BestPracticeDetailsV2 {
  name: string;
  rule: string;
  isAdded: number;
  identifier: string;
  parameters: string;
  applicabilityTypes: string;
  bestPracticeDefinitionId: number;
  validFromQuarter: string;
  validToQuarter: string;
}

interface BestPracticesDataGridRowBase {
  id: string;
  hierarchy: string[];
  displayName: string;
  availableChildIds: string[];
  isAdded?: boolean;
  validFromQuarter?: string;
  validToQuarter?: string;
}

export interface BestPracticesDataGridParentRow
  extends BestPracticesDataGridRowBase {
  isParent: boolean;
  addedBestPractices?: number;
  totalBestPractices?: number;
  isPartiallyAdded?: boolean;
}

export interface BestPracticesDataGridChildRow
  extends BestPracticesDataGridRowBase {
  rule: string;
  isChild: boolean;
  parentId: string;
}

export type BestPracticesDataGridRow =
  | BestPracticesDataGridParentRow
  | BestPracticesDataGridChildRow;

export interface BestPracticesDataV1 {
  [
    key: (typeof PARTNER_CRITERIA_PLATFORMS)[number]['id']
  ]: ChannelBestPracticesV1;
}

interface ChannelBestPracticesV1 {
  bestPracticesTotal: number;
  bestPracticesIncluded: number;
  hasAllBestPractices: boolean;
  bestPracticeDetails: BestPracticeDetailsV1[];
}

interface BestPracticeDetailsV1 {
  criteriaIds: string;
  identifier: string;
  templateId: number;
  name: string;
}

export type DataGridActionType =
  (typeof CRITERIA_MANAGEMENT_MODAL_TYPES)[keyof typeof CRITERIA_MANAGEMENT_MODAL_TYPES];

export interface AvailableOwner {
  label: string;
  id: string;
  photo?: string;
}

export interface CriteriaTemplate {
  defaultDisplayName: string;
  rule: string;
  description: string;
  identifier: string;
  id: number;
  platformIdentifier: string;
  category: string;
  custom: boolean;
}

export interface CriteriaModalState {
  selectedCriterion: Criterion | null;
  selectedModal: DataGridActionType | null;
  rowId?: string | null;
}
