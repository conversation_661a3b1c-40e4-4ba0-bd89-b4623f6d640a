import { useIntl } from 'react-intl';
import { IS_OPTIONAL_TYPES } from '../../../../constants/criteriaManagement.constants';

export const useIsOptional = () => {
  const intl = useIntl();

  return [
    {
      label:
        intl.messages[
          'ui.creativeScoring.reportDetails.filter.v2.criteria.criteriaIsOptional.optional.label'
        ],
      id: IS_OPTIONAL_TYPES.OPTIONAL,
    },
    {
      label:
        intl.messages[
          'ui.creativeScoring.reportDetails.filter.v2.criteria.criteriaIsOptional.mandatory.label'
        ],
      id: IS_OPTIONAL_TYPES.MANDATORY,
    },
  ];
};
