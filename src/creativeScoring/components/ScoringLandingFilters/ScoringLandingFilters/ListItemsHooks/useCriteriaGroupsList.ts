import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { fetchCriteriaGroupsList } from '../../../criteriaManagement/CriteriaGroupingManagement/helpers';
import {
  CriteriaGroupList,
  useCriteriaGrouping,
} from '../../../criteriaManagement/CriteriaGroupingManagement/context/CriteriaGroupingContext';
import {
  getCurrentPartnerId,
  getOrganizationId,
} from '../../../../../redux/selectors/partner.selectors';

export const useCriteriaGroupsList = (
  isRefreshNeededForAddingToCriteriaGroups?: boolean,
) => {
  const { setIsRefreshNeededForAddingToCriteriaGroups } = useCriteriaGrouping();
  const organizationId = useSelector(getOrganizationId);
  const workspaceId = useSelector(getCurrentPartnerId);
  const isRefreshKeyDefined =
    isRefreshNeededForAddingToCriteriaGroups !== undefined;

  const [criteriaGroupsList, setCriteriaGroupsList] =
    useState<CriteriaGroupList>();

  const { isLoading, refetch } = useQuery({
    queryKey: ['criteriaGroups'],
    queryFn: async () => {
      return fetchCriteriaGroupsList({
        organizationId,
        workspaceId,
        offset: 0,
        perPage: 1000,
      });
    },
    retry: false,
    enabled: false,
  });

  useEffect(() => {
    if (isRefreshKeyDefined && isRefreshNeededForAddingToCriteriaGroups) {
      refetch().then((result) => {
        if (result.data) {
          setCriteriaGroupsList(result.data);
          setIsRefreshNeededForAddingToCriteriaGroups(false);
        }
      });
    }

    if (!isRefreshKeyDefined) {
      // If the flag is undefined, fetch once on mount
      refetch().then((result) => {
        if (result.data) {
          setCriteriaGroupsList(result.data);
        }
      });
    }
  }, [isRefreshKeyDefined, isRefreshNeededForAddingToCriteriaGroups, refetch]);

  if (isLoading) {
    return [];
  }

  return criteriaGroupsList?.data.map((criteriaGroup) => ({
    id: criteriaGroup.id,
    label: criteriaGroup.name,
    color: criteriaGroup.color,
  }));
};
