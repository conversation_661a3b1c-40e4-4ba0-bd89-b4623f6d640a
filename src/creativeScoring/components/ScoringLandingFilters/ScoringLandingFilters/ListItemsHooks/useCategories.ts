import { useIntl } from 'react-intl';
import {
  CRITERIA_CATEGORIES,
  CRITERIA_CATEGORIES_INTL_MAP,
} from '../../../../constants/criteriaManagement.constants';

export const useCategories = () => {
  const intl = useIntl();

  return Object.keys(CRITERIA_CATEGORIES)
    .filter((key) => key !== 'ALL')
    .map((categoryKey) => {
      // @ts-ignore
      const category = CRITERIA_CATEGORIES[categoryKey];
      return {
        id: categoryKey,
        label: intl.messages[CRITERIA_CATEGORIES_INTL_MAP[category]],
      };
    });
};
