import { useSelector } from 'react-redux';
import {
  CriteriaLandingPageFiltersListItems,
  LandingPageFilterId,
} from '../../scoringLandingFilters.types';
import { useChannels } from './useChannels';
import { useMediaTypes } from './useMediaTypes';
import { useIsOptional } from './useIsOptional';
import { useOwners } from './useOwners';
import { useCategories } from './useCategories';
import { useGlobalStatuses } from './useGlobalStatuses';
import { getAvailableFilterOptions } from '../../../../redux/selectors/criteriaManagement.selectors';

const {
  PLATFORMS,
  MEDIA_TYPES,
  IS_OPTIONAL,
  OWNER_IDS,
  CATEGORY,
  GLOBAL_STATUSES,
  CRITERIA_GROUP,
} = LandingPageFilterId;

export const useCriteriaListItems = (
  criteriaGroupsList?:
    | { color: string; id: string; label: string }[]
    | undefined,
) => {
  const { owners } = useSelector(getAvailableFilterOptions);

  const lists: CriteriaLandingPageFiltersListItems = {
    [PLATFORMS]: useChannels({ includeStandardCriteria: true }),
    [MEDIA_TYPES]: useMediaTypes(),
    [IS_OPTIONAL]: useIsOptional(),
    [OWNER_IDS]: useOwners({ owners }),
    [CATEGORY]: useCategories(),
    [GLOBAL_STATUSES]: useGlobalStatuses(),
    [CRITERIA_GROUP]: criteriaGroupsList,
  };

  return lists;
};
