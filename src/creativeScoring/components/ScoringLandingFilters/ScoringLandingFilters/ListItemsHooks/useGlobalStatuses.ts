import { useIntl } from 'react-intl';
import { GLOBAL_STATUSES_TYPES } from '../../../../constants/criteriaManagement.constants';

export const useGlobalStatuses = () => {
  const intl = useIntl();

  return [
    {
      id: GLOBAL_STATUSES_TYPES.GLOBAL,
      label:
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.cellValue.isGlobal.true'
        ],
    },
    {
      id: GLOBAL_STATUSES_TYPES.DEFAULT,
      label:
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.cellValue.isGlobal.false'
        ],
    },
  ];
};
