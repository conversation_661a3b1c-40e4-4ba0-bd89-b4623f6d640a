import React, { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { ClickAwayListener } from '@mui/material';

import {
  VidMobBox,
  VidMobButton,
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import SearchableMultiSelectDropdownList from '../../../../muiCustomComponents/MultiSelectDropdown/SearchableMultiSelectDropdownList';
import {
  CaretDownIcon,
  CaretUpIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import { MappedCriteria } from '../../__subpages/CreativeScoringCriteriaManagement/CreativeScoringCriteriaManagement';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';

export interface ListItem {
  id: string;
  label: string;
}

export const getButtonStyles = (isDropdownOpen: boolean) => {
  return {
    height: '32px',
    color: isDropdownOpen ? '#1842EF' : '#000000',
    backgroundColor: isDropdownOpen ? '#DEE6FF' : '#FFFFFF',
    border: isDropdownOpen ? '1px solid transparent' : '1px solid #BDBDBD',
    '&:hover': {
      color: '#1842EF',
      background: '#DEE6FF',
      borderColor: 'transparent',
    },
  };
};

type Props = {
  listItems: ListItem[];
  onApply: (items: { id: string; type: string }[]) => void;
  selectedCriteria: GridRowSelectionModel;
  criteriaList: MappedCriteria[];
};

interface EnrichedListItem {
  id: string;
  label: string;
  indeterminate: boolean;
  checked: boolean;
}

const calculateSelectedListItems = (
  listItems: ListItem[],
  selectedCriteria: Props['selectedCriteria'],
  criteriaList: MappedCriteria[],
): EnrichedListItem[] => {
  const selectedCriteriaList = criteriaList.filter((criterion) =>
    selectedCriteria.includes(criterion.id),
  );

  return listItems
    ?.map((group) => {
      const groupId = group.id;

      const selectedInGroup = selectedCriteriaList.filter((criterion) =>
        criterion.criteriaGroups.includes(groupId),
      ).length;

      const totalSelectedCriteria = selectedCriteriaList.length;

      return {
        id: groupId,
        label: group.label,
        checked:
          selectedInGroup === totalSelectedCriteria &&
          totalSelectedCriteria > 0,
        indeterminate:
          selectedInGroup > 0 && selectedInGroup < totalSelectedCriteria,
      };
    })
    .filter((item) => item.checked || item.indeterminate);
};

const iconStyles = {
  height: '18px',
  width: '18px',
  marginLeft: '6px',
};
const dropDownStyles = {
  borderRadius: '6px',
  gap: '12px',
  border: '1px solid #BDBDBD',
  backgroundColor: '#FFFFFF',
  boxShadow: '0px 4px 8px 0px #0F0F0F29',
  position: 'absolute',
  top: '100%',
  left: 0,
  zIndex: 3,
  height: 'auto',
  width: '250px',
};

export const AddToGroupButton: React.FC<Props> = ({
  onApply,
  listItems,
  selectedCriteria,
  criteriaList,
}) => {
  const intl = useIntl();
  const [selectedValue, setSelectedValue] = useState<EnrichedListItem[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [touched, setTouched] = useState<boolean>(false);
  const [initialSelectedItems, setInitialSelectedItems] = useState<
    EnrichedListItem[]
  >([]);

  useEffect(() => {
    const defaultSelectedState = calculateSelectedListItems(
      listItems,
      selectedCriteria,
      criteriaList,
    );
    setSelectedValue(defaultSelectedState);
    if (isDropdownOpen) {
      setInitialSelectedItems(defaultSelectedState);
    }
  }, [isDropdownOpen, selectedCriteria]);

  const handleClickAway = () => {
    setIsDropdownOpen(false);
  };

  const handleSelectItems = (newSelectedValue: EnrichedListItem[]) => {
    setSelectedValue(newSelectedValue);
    setTouched(true);
  };

  const onApplyClick = () => {
    const changes = selectedValue.reduce<
      { id: string; type: 'add' | 'remove' }[]
    >((acc, item) => {
      const initialState = initialSelectedItems.find(
        (initialItem) => initialItem.id === item.id,
      );

      if (
        initialState &&
        initialState.indeterminate &&
        !item.checked &&
        !item.indeterminate
      ) {
        acc.push({ id: item.id, type: 'remove' });
        return acc;
      }

      if (
        (!initialState && item.checked) ||
        (initialState && initialState.checked !== item.checked)
      ) {
        acc.push({ id: item.id, type: item.checked ? 'add' : 'remove' });
      }

      return acc;
    }, []);

    onApply(changes);
    setTouched(false);
    setIsDropdownOpen(false);
  };

  const buttonStyles = getButtonStyles(isDropdownOpen);

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <VidMobStack sx={{ position: 'relative' }}>
        <VidMobButton
          variant="outlined"
          sx={buttonStyles}
          onClick={() => setIsDropdownOpen(true)}
        >
          <VidMobBox
            display="flex"
            flexDirection="row"
            alignItems="center"
            sx={{ gap: '4px' }}
          >
            <VidMobTypography variant="subtitle2">
              {
                intl.messages[
                  'ui.compliance.criteriaManagement.criteriaGroup.dropDown.add'
                ] as string
              }
            </VidMobTypography>
          </VidMobBox>
          {isDropdownOpen ? (
            <CaretUpIcon sx={iconStyles} />
          ) : (
            <CaretDownIcon sx={iconStyles} />
          )}
        </VidMobButton>
        {isDropdownOpen && (
          <VidMobBox sx={dropDownStyles}>
            <SearchableMultiSelectDropdownList
              listItems={listItems}
              selectedItems={selectedValue}
              setSelectedItems={handleSelectItems}
              maxListHeight="310px"
              includeSearch
              isAdvancedSelectionCase
              actionButtons={[
                <VidMobButton
                  key="apply"
                  variant="contained"
                  title="apply"
                  color="primary"
                  size="small"
                  disabled={!touched}
                  onClick={onApplyClick}
                >
                  {intl.messages['ui.dropdown.actionButton.apply'] as string}
                </VidMobButton>,
              ]}
            />
          </VidMobBox>
        )}
      </VidMobStack>
    </ClickAwayListener>
  );
};
