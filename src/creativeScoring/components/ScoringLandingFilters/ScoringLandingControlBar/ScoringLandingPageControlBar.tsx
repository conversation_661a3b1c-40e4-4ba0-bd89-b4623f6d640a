import React, { useEffect, useState } from 'react';
import { VidMobButton, VidMobStack } from '../../../../vidMobComponentWrappers';
import {
  AddToGroupButton,
  getButtonStyles,
  ListItem,
} from './AddToGroupButton';
import AddFilterButton from '../AddFilterButton';
import ScoringLandingFilters from '../ScoringLandingFilters';
import {
  FilterSelectedValueType,
  LandingPageContext,
  LandingPageFilter,
  LandingPageFilterListItems,
  LandingPageFilters,
  SavedFilterParams,
} from '../scoringLandingFilters.types';
import { getIsSavedFilter } from '../ScoringLandingFilters/FilterUtils/scoringLandingFilters.utils';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { IconSelectionModal } from '../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModal';
import { useAttachIconsToCriteria } from '../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModalHooks';
import { CustomIcon } from '../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModal.types';
import { useIntl } from 'react-intl';
import { useSelector, useDispatch } from 'react-redux';
import { getCriteriaData } from '../../../redux/selectors/criteriaManagement.selectors';
import { Criteria } from '../../../../types/criteria.types';
import CriteriaManagementSlice from '../../../redux/slices/criteriaManagement.slice';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { useCriteriaGroupsList } from '../ScoringLandingFilters/ListItemsHooks/useCriteriaGroupsList';
import {
  AssignDeassignPayload,
  useCriteriaGroupsApi,
} from '../../criteriaManagement/CriteriaGroupingManagement/hooks/useCriteriaGroupsApi';
import { displayToastAlert } from '../../../../muiCustomComponents/ToastAlert/displayToastAlert';
import { translationKeys } from '../../criteriaManagement/CriteriaGroupingManagement/constants';
import { MappedCriteria } from '../../__subpages/CreativeScoringCriteriaManagement/CreativeScoringCriteriaManagement';
import ListCount, {
  Divider,
} from '../../../../muiCustomComponents/ListCount/ListCount';

const {
  FORM: {
    TOAST: { EDIT_ERROR },
  },
} = translationKeys;

type Props = {
  filters: LandingPageFilters;
  isLoading: boolean;
  totalSize: number;
  itemsIntlKey: string;
  selectedItemsIntlKey?: string;
  filtersListItems: LandingPageFilterListItems;
  handleDispatchFilterChange: (arg: {
    filterId: string;
    value: FilterSelectedValueType;
  }) => void;
  savedFilterParams: SavedFilterParams;
  searchComponent: React.ReactNode;
  selectedCriteria?: GridRowSelectionModel;
  setSelectedCriteria?: (selectedCriteria: GridRowSelectionModel) => void;
  context: LandingPageContext;
  criteriaList: MappedCriteria[];
};

const ScoringLandingPageControlBar = ({
  filters,
  isLoading,
  totalSize,
  itemsIntlKey,
  selectedItemsIntlKey,
  filtersListItems,
  handleDispatchFilterChange,
  savedFilterParams,
  searchComponent: SearchComponent,
  selectedCriteria,
  setSelectedCriteria,
  context,
  criteriaList,
}: Props) => {
  const intl = useIntl();

  const [activeFilters, setActiveFilters] = useState<LandingPageFilter[]>([]);
  const [isIconModalOpen, setIsIconModalOpen] = useState(false);
  const [showAddToGroupDropDown, setShowAddToGroupDropDown] =
    useState<boolean>(false);

  const criteriaGroupsList = useCriteriaGroupsList();
  const { assignDeassignCriteriaGroup } = useCriteriaGroupsApi();
  const { mutate: attachIconsToCriteria } = useAttachIconsToCriteria();
  const dispatch = useDispatch();

  const { loadCriteria } = CriteriaManagementSlice.actions;

  const criteria = useSelector(getCriteriaData);

  useEffect(() => {
    if (
      context === LandingPageContext.CRITERIA &&
      getFeatureFlag('isCriteriaGroupsEnabled')
    ) {
      const shouldShowDropdown = Boolean(
        selectedCriteria && selectedCriteria.length > 0,
      );

      if (showAddToGroupDropDown !== shouldShowDropdown) {
        setShowAddToGroupDropDown(shouldShowDropdown);
      }
    }
  }, [selectedCriteria]);

  useEffect(() => {
    // need to look at saved filters in instances where we are persisting filters between pages
    const initialActiveFilters = filters.filter((filter) =>
      getIsSavedFilter(filter, savedFilterParams),
    );

    setActiveFilters(initialActiveFilters);
  }, [filters]);

  useEffect(() => {
    setActiveFilters([]);
  }, [context]); // to reset filters on tab change for scorecards landing

  const handleAddToGroupApply = async (
    items: { id: string; type: string }[],
  ) => {
    setSelectedCriteria && setSelectedCriteria([]);
    const { selectedCriteriaGroups, unselectedCriteriaGroups } = items.reduce<{
      selectedCriteriaGroups: AssignDeassignPayload['selectedCriteriaGroups'];
      unselectedCriteriaGroups: AssignDeassignPayload['unselectedCriteriaGroups'];
    }>(
      (acc, { id, type }) => {
        if (type === 'add') acc.selectedCriteriaGroups.push(id);
        else if (type === 'remove') acc.unselectedCriteriaGroups.push(id);
        return acc;
      },
      { selectedCriteriaGroups: [], unselectedCriteriaGroups: [] },
    );

    try {
      await assignDeassignCriteriaGroup.mutateAsync({
        criteriaIds: selectedCriteria!,
        unselectedCriteriaGroups,
        selectedCriteriaGroups,
      });
      dispatch(loadCriteria({}));
    } catch (error) {
      displayToastAlert({
        message: intl.messages[EDIT_ERROR],
        type: 'error',
      });
    }
  };

  const handleAddFilter = (filter: LandingPageFilter) => {
    setActiveFilters([...activeFilters, filter]);
  };

  const handleIconModalSubmit = (icon: CustomIcon | null) => {
    if (!icon || !selectedCriteria || selectedCriteria.length === 0) {
      return;
    }
    const iconCriteriaMappings = selectedCriteria.map((criteriaId) => ({
      criteriaId: Number(criteriaId),
      key: icon.key,
    }));
    attachIconsToCriteria({ iconCriteriaMappings });
    dispatch(loadCriteria({}));

    setIsIconModalOpen(false);
  };

  return (
    <VidMobStack
      className="creative-scoring-landing-page-control-bar"
      direction="row"
      alignItems="center"
      flexWrap="wrap"
      sx={{ gap: '8px', mb: '8px' }}
    >
      <ListCount
        count={totalSize}
        isLoading={isLoading}
        selectedItemsCount={selectedCriteria?.length || 0}
        itemsIntlKey={itemsIntlKey}
        selectedItemsIntlKey={selectedItemsIntlKey}
      />
      {SearchComponent}
      <ScoringLandingFilters
        activeFilters={activeFilters}
        filtersListItems={filtersListItems}
        setActiveFilters={setActiveFilters}
        handleDispatchFilterChange={handleDispatchFilterChange}
        savedFilterParams={savedFilterParams}
      />
      {filters.length !== activeFilters.length && ( // hide button once everything is selected
        <AddFilterButton
          filters={filters}
          activeFilters={activeFilters}
          onAddFilter={handleAddFilter}
        />
      )}
      {showAddToGroupDropDown && selectedCriteria && (
        <>
          <Divider />
          <AddToGroupButton
            listItems={criteriaGroupsList as ListItem[]}
            onApply={handleAddToGroupApply}
            selectedCriteria={selectedCriteria}
            criteriaList={criteriaList}
          />
        </>
      )}

      {selectedCriteria && selectedCriteria.length > 0 && (
        <>
          <Divider />
          <VidMobButton
            variant="outlined"
            color="primary"
            onClick={() => setIsIconModalOpen(true)}
            size="small"
            sx={{
              ...getButtonStyles(false),
              fontWeight: 600,
              fontSize: '14px',
            }}
          >
            {intl.formatMessage({
              id: 'ui.compliance.criteriaManagement.iconSelection.button.text',
              defaultMessage: 'Add icon',
            })}
          </VidMobButton>
          <IconSelectionModal
            isOpen={isIconModalOpen}
            handleClose={() => setIsIconModalOpen(false)}
            handleSubmit={(icon) => handleIconModalSubmit(icon)}
            criteriaCount={selectedCriteria.length}
            selectedIconsIds={criteria
              .filter((criteria: Criteria) =>
                selectedCriteria.includes(criteria.id),
              )
              .map((criteria: Criteria) => criteria.customIconId)}
          />
        </>
      )}
    </VidMobStack>
  );
};

export default ScoringLandingPageControlBar;
