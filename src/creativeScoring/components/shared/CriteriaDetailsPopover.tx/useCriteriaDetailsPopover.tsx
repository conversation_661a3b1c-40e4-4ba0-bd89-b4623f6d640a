import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobChip,
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { CircularProgress } from '@mui/material';
import { BestPracticeIcon } from '../../../../assets/vidmob-mui-icons/general';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { usePopover } from '../../../../muiCustomComponents/Popover/usePopover';
import { getPlatformIdentifierForLogo } from '../../../../utils/feConstantsUtils';
import { convertCriterionDataToSectionKey } from './criteriaDetailsPopover.utils';
import {
  CRITERIA_DETAILS_SECTION_KEY_TO_INTL_MAP,
  CRITERIA_DETAILS_SECTIONS,
} from './criteriaDetailPopover.constants';
import {
  CriteriaDetailsCriterionData,
  CriteriaDetailsSections,
} from './criteriaDetailsPopover.types';
import OverflowMoreTextWithTooltip from '../../../../muiCustomComponents/OverflowMoreTextWithTooltip';

type CriteriaDetailsPopoverProps = {
  criterion: CriteriaDetailsCriterionData;
  isLoading?: boolean;
  customAnchor?: {
    anchorReference: string;
    anchorPosition?: { top: number; left: number };
  };
  customIconUrl?: string;
};

const useCriteriaDetailsPopover = (props: CriteriaDetailsPopoverProps) => {
  const { criterion, customAnchor, isLoading = false, customIconUrl } = props;
  const intl = useIntl();
  const criterionSections = convertCriterionDataToSectionKey(criterion, intl);

  const sectionHeader = (key: keyof CriteriaDetailsSections) => {
    return (
      <VidMobTypography variant="caption" sx={{ color: '#757575' }}>
        {intl.messages[CRITERIA_DETAILS_SECTION_KEY_TO_INTL_MAP[key]] as string}
      </VidMobTypography>
    );
  };

  const sectionData = (key: keyof CriteriaDetailsSections, value: string) => {
    return (
      <VidMobStack direction="row" sx={{ gap: '8px' }}>
        {key === CRITERIA_DETAILS_SECTIONS.rule && criterion.isBestPractice && (
          <BestPracticeIcon
            sx={{
              height: '14px',
              width: '14px',
              mt: '3px',
              alignSelf: 'start',
            }}
          />
        )}
        <VidMobTypography variant="body2" width="100%">
          {(() => {
            if (key === CRITERIA_DETAILS_SECTIONS.category) {
              return <VidMobChip label={value} />;
            }
            if (key === CRITERIA_DETAILS_SECTIONS.description) {
              return <OverflowMoreTextWithTooltip stringsList={[value]} />;
            }
            if (key === CRITERIA_DETAILS_SECTIONS.workspaces) {
              return (
                <OverflowMoreTextWithTooltip stringsList={value.split(',')} />
              );
            }
            return value;
          })()}
        </VidMobTypography>
      </VidMobStack>
    );
  };

  const popoverContent = isLoading ? (
    <VidMobBox
      sx={{
        height: '200px',
        justifyContent: 'center',
        alignItems: 'center',
        display: 'flex',
      }}
    >
      <CircularProgress size={24} />
    </VidMobBox>
  ) : (
    <VidMobStack sx={{ gap: '12px' }}>
      <VidMobStack
        className="criteria-name"
        direction="row"
        sx={{ gap: '6px' }}
        alignItems="center"
      >
        {getMUIIconForChannel(
          getPlatformIdentifierForLogo(criterion.platform),
          {
            width: '20px',
            height: '20px',
          },
        )}
        <VidMobTypography variant="subtitle2">
          {criterion.name}
        </VidMobTypography>
        {customIconUrl && (
          <VidMobAvatar
            src={customIconUrl}
            sx={{ width: '24px', height: '24px', marginLeft: '4px' }}
          />
        )}
      </VidMobStack>
      {Object.keys(criterionSections).map((key) => {
        const value = criterionSections[key as keyof CriteriaDetailsSections];

        return (
          value && (
            <VidMobStack key={key} sx={{ gap: '6px' }}>
              {sectionHeader(key as keyof CriteriaDetailsSections)}
              {sectionData(
                key as keyof CriteriaDetailsSections,
                value as string,
              )}
            </VidMobStack>
          )
        );
      })}
    </VidMobStack>
  );

  return usePopover({
    popoverContent,
    customAnchor,
    customPopoverSx: {
      customBorder: 'none',
      customBoxShadow: '0px 4px 12px 0px #0F0F0F52',
    },
  });
};

export default useCriteriaDetailsPopover;
