import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { CircularProgress } from '@mui/material';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { usePopover } from '../../../../muiCustomComponents/Popover/usePopover';
import { getPlatformIdentifierForLogo } from '../../../../utils/feConstantsUtils';
import { convertCriterionDataToSectionKeyV2 } from './criteriaDetailsPopover.utils';
import { BestPracticeIcon } from '../../../../assets/vidmob-mui-icons/general';
import {
  CriteriaDetailsCriterionData,
  CriteriaDetailsSections,
} from './criteriaDetailsPopover.types';
import CriteriaDetailsPopoverSections from './CriteriaDetailsPopoverSections';

type CriteriaDetailsPopoverProps = {
  criterion: CriteriaDetailsCriterionData;
  isLoading?: boolean;
  customAnchor?: {
    anchorReference: string;
    anchorPosition?: { top: number; left: number };
  };
  customIconUrl?: string;
};

const useCriteriaDetailsPopoverV2 = (props: CriteriaDetailsPopoverProps) => {
  const { criterion, customAnchor, isLoading = false, customIconUrl } = props;
  const intl = useIntl();
  const criterionSections = convertCriterionDataToSectionKeyV2(criterion, intl);

  const popoverContent = isLoading ? (
    <VidMobBox
      sx={{
        height: '200px',
        justifyContent: 'center',
        alignItems: 'center',
        display: 'flex',
      }}
    >
      <CircularProgress size={24} />
    </VidMobBox>
  ) : (
    <VidMobStack sx={{ gap: '12px' }}>
      <VidMobStack
        className="criteria-name"
        direction="row"
        sx={{ gap: '6px' }}
        alignItems="center"
      >
        {getMUIIconForChannel(
          getPlatformIdentifierForLogo(criterion.platform),
          {
            width: '20px',
            height: '20px',
          },
        )}
        <VidMobTypography variant="subtitle2">
          {criterion.name}
        </VidMobTypography>
        {customIconUrl && (
          <VidMobAvatar
            src={customIconUrl}
            sx={{ width: '24px', height: '24px', marginLeft: '4px' }}
          />
        )}
        {criterion.isBestPractice && (
          <BestPracticeIcon
            sx={{
              height: '14px',
              width: '14px',
              mt: '3px',
              alignSelf: 'start',
            }}
          />
        )}
      </VidMobStack>
      {Object.keys(criterionSections).map((key) => {
        const value = criterionSections[key as keyof CriteriaDetailsSections];

        if (value === true || (value && value.length > 0)) {
          return (
            value && (
              <CriteriaDetailsPopoverSections
                key={key}
                currentKey={key}
                value={value}
              />
            )
          );
        }
        return null;
      })}
    </VidMobStack>
  );

  return usePopover({
    popoverContent,
    customAnchor,
    customPopoverSx: {
      customBorder: 'none',
      customBoxShadow: '0px 4px 12px 0px #0F0F0F52',
      maxHeight: '350px',
    },
  });
};

export default useCriteriaDetailsPopoverV2;
