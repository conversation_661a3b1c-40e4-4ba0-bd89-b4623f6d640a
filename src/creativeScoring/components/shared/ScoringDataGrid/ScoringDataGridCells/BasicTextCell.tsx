import React from 'react';
// components
import { Typography, Tooltip } from '@mui/material';
// types
import { GridCellParams } from '@mui/x-data-grid-pro';
import { DATA_GRID_FIELDS } from '../../../../constants/criteriaManagement.constants';
import { useIntl } from 'react-intl';
// constants

const BasicTextCell = ({ params }: { params: GridCellParams }) => {
  const intl = useIntl();
  const { value, field } = params;

  let displayText = value as string;

  if (field === DATA_GRID_FIELDS.IS_GLOBAL) {
    displayText = intl.messages[
      `ui.creativeScoring.criteriaManagementV2.cellValue.isGlobal.${value}`
    ] as string;
  }

  if (value === null || value === undefined || value === '') {
    return <div>-</div>;
  }

  return (
    <Tooltip placement="bottom" title={displayText} disableInteractive>
      <Typography
        variant="body2"
        sx={{
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
        }}
      >
        {displayText}
      </Typography>
    </Tooltip>
  );
};

export default BasicTextCell;
