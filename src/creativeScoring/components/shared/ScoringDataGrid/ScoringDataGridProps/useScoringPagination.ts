import { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { getCriteriaPagination } from '../../../../redux/selectors/criteriaManagement.selectors';
import { SERVER } from '../../../criteriaManagement/CriteriaManagementDataGrid/DataGridProps/useDataGridProps';
import { useDidUpdateEffect } from '../../../../../hooks/useDidUpdateEffect';
import CriteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';

const { setCriteriaPagination } = CriteriaManagementSlice.actions;

export const useScoringPagination = () => {
  const dispatch = useDispatch();

  const paginationInitialState = {
    page: 0,
    pageSize: 25,
  };

  const [paginationModel, setPaginationModel] = useState(
    paginationInitialState,
  );
  const pagination = useSelector(getCriteriaPagination);
  const { totalSize } = pagination;

  useDidUpdateEffect(() => {
    dispatch(
      setCriteriaPagination({
        pagination: {
          ...pagination,
          offset: paginationModel.page * paginationModel.pageSize,
          perPage: paginationModel.pageSize,
        },
      }),
    );
  }, [paginationModel]);

  const paginationProps = {
    pagination: true,
    paginationMode: SERVER,
    paginationModel,
    rowCount: totalSize,
    onPaginationModelChange: setPaginationModel,
    hideFooter: totalSize <= paginationModel.pageSize,
  };

  return {
    paginationModel,
    paginationProps,
  };
};
