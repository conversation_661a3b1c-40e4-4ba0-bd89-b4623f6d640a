import BffRollUpReportsService from '../../../../../apiServices/BffRollUpReportsService';
import { ScoringReportType } from '../../../../types/rollUpReports.types';
import { SCORING_SAVED_REPORTS_LIST_QUERY_KEY } from './constants';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';

const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

const getReportsListApiCallV2 = BffRollUpReportsService.getReportsListV2.bind(
  BffRollUpReportsService,
);
const getReportsListApiCallV3 = BffRollUpReportsService.getReportsListV3.bind(
  BffRollUpReportsService,
);

export const savedReportsListQuery = (requestParams: {
  workspaceId: string;
  types: ScoringReportType[];
  searchTerm?: string;
  offset: number;
  perPage: number;
  sortOrder: string;
  sortBy: string;
  createdBy?: string;
  dateUpdated?: string;
  dateCreated?: string;
  adAccounts?: string[];
  channels?: string[];
}) => {
  const {
    workspaceId,
    types,
    searchTerm,
    offset,
    perPage,
    sortOrder,
    sortBy,
    createdBy,
    dateUpdated,
    dateCreated,
    adAccounts,
    channels,
  } = requestParams;

  return {
    queryKey: [SCORING_SAVED_REPORTS_LIST_QUERY_KEY, requestParams],
    queryFn: async () => {
      if (isCriteriaGroupsInReportsEnabled) {
        return await getReportsListApiCallV3(
          workspaceId,
          {
            types,
            searchTerm,
            sortOrder,
            sortBy,
            createdBy,
            lastUpdated: dateUpdated,
            dateCreated,
            adAccounts,
            channels,
          },
          {
            offset: offset ?? null,
            perPage: perPage ?? 20,
          },
          {
            sortBy,
            sortOrder,
          },
        );
      } else {
        return await getReportsListApiCallV2(
          workspaceId,
          {
            types,
            searchTerm,
            sortOrder,
            sortBy,
            createdBy,
            lastUpdated: dateUpdated,
            dateCreated,
            adAccounts,
            channels,
          },
          {
            offset: offset ?? null,
            perPage: perPage ?? 20,
          },
          {
            sortBy,
            sortOrder,
          },
        );
      }
    },
    refetchOnWindowFocus: false,
    keepPreviousData: false,
  };
};
