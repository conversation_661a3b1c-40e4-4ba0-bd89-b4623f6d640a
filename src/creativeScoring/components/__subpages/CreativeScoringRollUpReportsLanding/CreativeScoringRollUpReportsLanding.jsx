import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import RollUpReportsTable from '../../rollUpReportsLandingPage/RollUpReportsTable';
import { RollUpReportBlankStates } from '../../reports/rollUpReport/RollUpReportStates';
import PageHeaderV2 from '../../../../components/PageHeaderV2';
import { RollUpReportsCreateReport } from '../../rollUpReportsLandingPage/Modals';
import EmptyState from '../../../../components/EmptyState';
import { SearchFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import rollupReportsManagementSlice from '../../../redux/slices/rollupReportsManagement.slice';
import {
  getHasRollupReportsFailed,
  getHasNoReports,
  getReportsFilters,
  getRollupReportsPagination,
} from '../../../redux/selectors/rollupReportsManagement.selectors';
import './CreativeScoringRollUpReportsLanding.scss';
import { SavedReportControlBar } from './SavedReportsControlBar/SavedReportControlBar';
import { LandingPageFilterId } from '../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import { SCORING_REPORTS_CREATE_OPTIONS } from '../../reports/rollUpReport/rollUpReport.constants';
import { ROLLUP_REPORTS_LANDING_COLUMNS } from '../../rollUpReportsLandingPage/RollUpReportsTable/useRollUpReportsColumns';
import { useLocalStorage } from '../../../../hooks/useLocalStorageState';
import { useQuery } from '@tanstack/react-query';
import {
  getCurrentPartner,
  getOrganizationId,
} from '../../../../redux/selectors/partner.selectors';
import { savedReportsListQuery } from './queries/scoringSavedReports';
import { trackFeatureUsageGainsight } from '../../../../utils/gainsight';
import { convertDateToDayJSObject } from '../../../../utils/dateRangePickerMUIUtils';

const { loadReports, setReportTypesWorkspaceHasAccessTo } =
  rollupReportsManagementSlice.actions;

const CreativeScoringRollUpReportsLanding = () => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const searchQuery = useSelector(getReportsFilters)?.searchTerm;
  const organizationId = useSelector(getOrganizationId);
  const workspace = useSelector(getCurrentPartner);
  const { sortBy, sortOrder } = useSelector(getReportsFilters);

  useEffect(() => {
    trackFeatureUsageGainsight('Scoring Landing Filters', {
      Context: 'Creative Scoring Reports Landing with Filters',
    });
  }, []);

  // triggers saga to get list of reports
  useEffect(() => {
    dispatch(loadReports({ skipReportFetching: true }));

    return () => {
      dispatch(setReportTypesWorkspaceHasAccessTo({ reportTypes: null })); // reset when leave page
    };
  }, [searchQuery]);

  const hasFailed = useSelector(getHasRollupReportsFailed);
  const hasNoReports = useSelector(getHasNoReports);
  const showSearchBlankState = hasNoReports && searchQuery;

  const renderGenericBlankState = () => {
    return <RollUpReportBlankStates isError={hasFailed} isReportsHome />;
  };

  const [columns, setColumns] = useLocalStorage(
    'savedScoringReportsColumns',
    ROLLUP_REPORTS_LANDING_COLUMNS,
    {
      isSessionStorage: true,
    },
  );
  const [searchTerm, setSearchTerm] = useState('');

  let reportTypes = Object.keys(SCORING_REPORTS_CREATE_OPTIONS);

  const [filters, setFilters] = useLocalStorage(
    'savedScoringReportsFilters',
    {
      [LandingPageFilterId.REPORT_TYPE]: reportTypes,
      [LandingPageFilterId.CREATED_BY]: [],
      [LandingPageFilterId.DATE_CREATED]: [],
      [LandingPageFilterId.DATE_UPDATED]: [],
      [LandingPageFilterId.CHANNELS]: [],
      [LandingPageFilterId.AD_ACCOUNTS]: [],
    },
    {
      isSessionStorage: true,
      parseValue: {
        [LandingPageFilterId.REPORT_TYPE]: (value) =>
          value.length > 0 ? value : reportTypes,
        [LandingPageFilterId.DATE_CREATED]: (value) =>
          value.length > 0
            ? [
                convertDateToDayJSObject(value[0]),
                convertDateToDayJSObject(value[1]),
              ]
            : [],

        [LandingPageFilterId.DATE_UPDATED]: (value) =>
          value.length > 0
            ? [
                convertDateToDayJSObject(value[0]),
                convertDateToDayJSObject(value[1]),
              ]
            : [],
      },
    },
  );

  const { totalSize, offset } = useSelector(getRollupReportsPagination);

  const paginationInitialState = {
    page: 0,
    pageSize: 20,
  };

  const [paginationModel, setPaginationModel] = useState(
    paginationInitialState,
  );

  useEffect(() => {
    if (offset === 0) {
      setPaginationModel(paginationInitialState); // to reset back to first page
    }
  }, [offset]);

  useEffect(() => {
    setPaginationModel(paginationInitialState);
  }, [searchTerm]);

  const columnVisibilityModel = columns.reduce((acc, column) => {
    acc[column.field] = !column.hide;
    return acc;
  }, {});

  const queryOptions = useMemo(
    () => ({
      offset: paginationModel.page * paginationModel.pageSize,
      perPage: paginationModel.pageSize,
      ...(sortBy && sortOrder ? { sortBy, sortOrder } : {}),
    }),
    [paginationModel, sortBy, sortOrder],
  );

  const formattedFiltersV2 = useMemo(() => {
    const newFilters = {
      ...filters,
      enabled: Boolean(organizationId),
      searchTerm,
      channels: filters[LandingPageFilterId.CHANNELS].map((channel) =>
        channel?.toString().toLowerCase(),
      ),
      createdBy: filters[LandingPageFilterId.CREATED_BY]
        .map((createdBy) => createdBy && Number(createdBy))
        .filter(Boolean),
      dateCreated: filters[LandingPageFilterId.DATE_CREATED][0]
        ? {
            startDate: filters[LandingPageFilterId.DATE_CREATED][0],
            endDate: filters[LandingPageFilterId.DATE_CREATED][1],
          }
        : undefined,
      dateUpdated: filters[LandingPageFilterId.DATE_UPDATED][0]
        ? {
            startDate: filters[LandingPageFilterId.DATE_UPDATED][0],
            endDate: filters[LandingPageFilterId.DATE_UPDATED][1],
          }
        : undefined,
    };

    return Object.fromEntries(
      Object.entries(newFilters).filter(
        ([_, value]) =>
          value !== undefined && (!Array.isArray(value) || value.length > 0),
      ),
    );
  }, [filters, organizationId, searchTerm]);

  const { data: savedReportsData, isFetching: isFetchingSavedReportsList } =
    useQuery(
      savedReportsListQuery({
        workspaceId: workspace.id,
        searchTerm,
        ...formattedFiltersV2,
        ...queryOptions,
      }),
    );

  const pagination = savedReportsData?.pagination
    ? savedReportsData?.pagination
    : { totalSize, offset };

  useEffect(() => {
    setPaginationModel(paginationInitialState);
  }, [JSON.stringify(formattedFiltersV2)]);

  const renderContent = () => {
    return (
      <>
        {showSearchBlankState ? (
          <EmptyState
            icon={() => <SearchFilledIcon />}
            title={intl.messages['ui.reports.search.blankState.title']}
            description={intl.formatMessage(
              { id: 'ui.reports.search.blankState.description' },
              { searchQuery },
            )}
          />
        ) : (
          <RollUpReportsTable
            isLoading={isFetchingSavedReportsList}
            reports={savedReportsData?.data || []}
            columnVisibilityModel={columnVisibilityModel}
            paginationModel={paginationModel}
          />
        )}
      </>
    );
  };

  const showGenericBlankState =
    !isFetchingSavedReportsList && savedReportsData?.data?.length === 0;

  return (
    <>
      <PageHeaderV2
        title={intl.messages['sideNav.reports']}
        additionalBoxStyles={{
          pl: 0,
          pr: 0,
          maxHeight: '36px',
          fontWeight: 600,
          fontSize: '14px',
          lineHeight: '20px',
        }}
      >
        {!showGenericBlankState && (
          <RollUpReportsCreateReport
            isDisabled={isFetchingSavedReportsList || hasFailed}
          />
        )}
      </PageHeaderV2>
      <SavedReportControlBar
        columns={columns}
        setColumns={setColumns}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        filters={filters}
        onFilterChange={setFilters}
        pageSize={paginationModel.pageSize}
        page={paginationModel.page}
        totalCount={pagination.totalSize}
        onPageChange={(page) => {
          setPaginationModel({ ...paginationModel, page: page });
        }}
        isLoading={isFetchingSavedReportsList}
      />
      {showGenericBlankState ? renderGenericBlankState() : renderContent()}
    </>
  );
};

export default CreativeScoringRollUpReportsLanding;
