import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ScorecardsLandingBlankStateWrapper from '../../scorecardsLanding/ScorecardsLandingBlankState/ScorecardsLandingBlankStateWrapper';
import ScorecardsLandingTableDataGridWrapper from '../../scorecardsLanding/ScorecardsLandingTableDataGrid';
import ScorecardsLandingHeader from '../../scorecardsLanding/ScorecardsLandingHeader';
import ScorecardsLandingToolbar from '../../scorecardsLanding/ScorecardsLandingToolbar';
import { getFormattedScorecards } from '../../../redux/selectors/complianceShared.selectors';
import { loadAvailableFilterOptionsAction } from '../../../redux/actions/filterPanelV2.actions';
import {
  areFilterOptionsLoaded,
  getScorecardsLandingChannels,
} from '../../../redux/selectors/filterPanelV2.selectors';
import { fetchScorecards } from '../../../redux/actions/batches.actions';
import {
  fetchBrands,
  fetchCountries,
} from '../../../../userManagement/redux/thunk/workspaces.thunk';
import {
  getAvailableFiltersStatus,
  getSelectedFiltersForScorecardsLandingControlBar,
  getScorecardsLandingSortingParams,
} from '../../../redux/selectors/filterPanelV2.selectors';
import {
  getCurrentScorecardLandingViewType,
  getScorecardsLoadingStatus,
} from '../../../redux/selectors/complianceBatches.selectors';
import complianceBatchesSlice from '../../../redux/slices/complianceBatches.slice';
import { GLOBALS } from '../../../../constants';
import { VidMobBox } from '../../../../vidMobComponentWrappers';
import {
  IN_FLIGHT_SCORECARDS_HAVE_MOVED_NOTIFICATION_COUNT_LOCAL_STORAGE_KEY,
  NUMBER_OF_REMINDERS_THAT_IN_FLIGHT_SCORECARDS_HAVE_MOVED,
} from '../../../constants/creativeScoring.constants';

const { SUCCESS, PENDING, NOT_LOADED, FAILED } = GLOBALS.REDUX_LOADING_STATUS;

const { resetScorecardsHasData } = complianceBatchesSlice.actions;

export const scorecardsDataGridWrapperSx = {
  whiteSpace: 'nowrap',
  width: '99%',
  position: 'absolute',
  height: 'calc(100% - 175px)',

  '.MuiDataGrid-virtualScrollerContent': {
    minHeight: 0,
  },
};

const scorecardsDataGridWrapperSxWithNotificationBanner = {
  ...scorecardsDataGridWrapperSx,
  height: 'calc(100% - 270px)',
};

const CreativeScoringScorecardsLanding = () => {
  const dispatch = useDispatch();

  const areScorecardsFilterOptionsLoaded = useSelector(areFilterOptionsLoaded);
  const availableFiltersStatus = useSelector(getAvailableFiltersStatus);
  const scorecardType = useSelector(getCurrentScorecardLandingViewType);
  const formattedScorecards = useSelector((state) =>
    getFormattedScorecards(state),
  );
  const scorecardsLoadingStatus = useSelector((state) =>
    getScorecardsLoadingStatus(state),
  );

  const isScorecardsLoading =
    [PENDING, NOT_LOADED].includes(scorecardsLoadingStatus) &&
    availableFiltersStatus !== FAILED;
  const isScorecardsLoadingErrorState =
    scorecardsLoadingStatus === FAILED || availableFiltersStatus === FAILED;

  const [showFilterBar, setShowFilterBar] = useState(true);
  const [
    isMovedScorecardsNotificationBannerVisible,
    setIsMovedScorecardsNotificationBannerVisible,
  ] = useState(false);

  const notificationCountFromLocalStorage = localStorage.getItem(
    IN_FLIGHT_SCORECARDS_HAVE_MOVED_NOTIFICATION_COUNT_LOCAL_STORAGE_KEY,
  );
  const notificationCount = notificationCountFromLocalStorage
    ? JSON.parse(notificationCountFromLocalStorage)
    : 0;

  // these are the control bar filter values that trigger API calls on change.
  // all other filters reside in the control panel and only update on submit.
  const selectedFiltersForControlBar = useSelector(
    getSelectedFiltersForScorecardsLandingControlBar,
  );

  const selectedChannels = useSelector(getScorecardsLandingChannels);

  const { startDate, endDate, platforms, searchText } =
    selectedFiltersForControlBar;

  const sortingParams = useSelector(getScorecardsLandingSortingParams);
  const { sortBy, sortOrder } = sortingParams;

  useEffect(() => {
    dispatch(fetchCountries());
    dispatch(fetchBrands());

    // reset status of if workspace has data on unmount
    // (prevents incorrect data if workspace is changed on another page)
    return () => {
      dispatch(resetScorecardsHasData());
    };
  }, []);

  useEffect(() => {
    if (!areScorecardsFilterOptionsLoaded) {
      dispatch(
        loadAvailableFilterOptionsAction({ platforms: selectedChannels }),
      );
    }
  }, [areScorecardsFilterOptionsLoaded]);

  useEffect(() => {
    dispatch(loadAvailableFilterOptionsAction({ platforms: selectedChannels }));
  }, [selectedChannels]);

  useEffect(() => {
    if (scorecardType && availableFiltersStatus === SUCCESS) {
      dispatch(fetchScorecards());
    }
  }, [
    scorecardType,
    availableFiltersStatus,
    startDate,
    endDate,
    platforms,
    sortBy,
    sortOrder,
    searchText,
  ]);

  useEffect(() => {
    if (
      notificationCount <
      NUMBER_OF_REMINDERS_THAT_IN_FLIGHT_SCORECARDS_HAVE_MOVED
    ) {
      setIsMovedScorecardsNotificationBannerVisible(true);
    }
  }, []);

  const onCloseNotificationBanner = () => {
    const updatedNotificationCount = notificationCount + 1;
    localStorage.setItem(
      IN_FLIGHT_SCORECARDS_HAVE_MOVED_NOTIFICATION_COUNT_LOCAL_STORAGE_KEY,
      JSON.stringify(updatedNotificationCount),
    );
    setIsMovedScorecardsNotificationBannerVisible(false);
  };

  return (
    <>
      <ScorecardsLandingHeader
        scorecardType={scorecardType}
        shouldShowNotificationBanner={
          isMovedScorecardsNotificationBannerVisible
        }
        onCloseNotificationBanner={onCloseNotificationBanner}
      />
      {showFilterBar && (
        <ScorecardsLandingToolbar scorecardType={scorecardType} />
      )}
      {formattedScorecards.length === 0 && !isScorecardsLoading ? (
        <ScorecardsLandingBlankStateWrapper
          isScorecardsLoadingErrorState={isScorecardsLoadingErrorState}
          showFilterBar={setShowFilterBar}
        />
      ) : (
        <VidMobBox
          sx={
            isMovedScorecardsNotificationBannerVisible
              ? scorecardsDataGridWrapperSxWithNotificationBanner
              : scorecardsDataGridWrapperSx
          }
        >
          <ScorecardsLandingTableDataGridWrapper
            rows={formattedScorecards}
            isLoading={isScorecardsLoading}
            scorecardsLandingPageType={scorecardType}
          />
        </VidMobBox>
      )}
    </>
  );
};

export default CreativeScoringScorecardsLanding;
