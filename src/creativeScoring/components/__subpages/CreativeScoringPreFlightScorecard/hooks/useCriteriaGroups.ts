import { useSelector } from 'react-redux';
import {
  getCurrentPartnerId,
  getOrganizationId,
} from '../../../../../redux/selectors/partner.selectors';
import { useQuery } from '@tanstack/react-query';
import { fetchCriteriaGroupsList } from '../../../criteriaManagement/CriteriaGroupingManagement/helpers';
import vmErrorLog from '../../../../../utils/vmErrorLog';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';

export const useCriteriaGroups = () => {
  const showToastAlert = useToastAlert();
  const organizationId = useSelector(getOrganizationId);
  const workspaceId = useSelector(getCurrentPartnerId);

  return useQuery({
    queryKey: ['criteriaGroups'],
    enabled: getFeatureFlag('isCriteriaGroupsInPreFlightCreationEnabled'),
    queryFn: async () =>
      fetchCriteriaGroupsList({
        organizationId,
        workspaceId,
        offset: 0,
        perPage: 1000,
      }),
    onError: (error: Error) => {
      showToastAlert(
        'ui.creativeScoring.preFlightCheck.toast.criteriaGroups.error',
        'error',
      );
      vmErrorLog(error as Error, 'useCriteriaGroups');
    },
    retry: false,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });
};
