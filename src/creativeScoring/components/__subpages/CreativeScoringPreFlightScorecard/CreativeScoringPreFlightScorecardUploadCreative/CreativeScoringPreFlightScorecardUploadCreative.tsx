import React, { useRef, MouseEvent, useState } from 'react';
import {
  VidMobBox,
  VidMobTypography,
  VidMobLink,
} from '../../../../../vidMobComponentWrappers';
import { useIntl, FormattedMessage } from 'react-intl';
import { COMPLIANCE } from '../../../../../constants';
import EmptyState from '../../../../../components/EmptyState';
import { CreateIcon } from '../../../../../assets/vidmob-mui-icons/general';
import { uploadFiles as uploadFilesActionCreator } from '../../../../redux/actions/uploads.actions';
import { useDispatch, useSelector } from 'react-redux';
import { getBatchId } from '../../../../redux/selectors/complianceUploads.selectors';
import vmErrorLog from '../../../../../utils/vmErrorLog';
import { checkAndFilterSupportedFiles } from '../../../../featureServices/checkAndFilterSupportedFiles';
import getFilesFromDataTransferItems from '../../../../../utils/getFilesFromDataTransferItems';
import { wasFolderDraggedAndDropped } from '../../../../../featureServices/assets/transforms';
import { getBatchFolderContents } from '../../../../redux/selectors/complianceUploads.selectors';
import CreativeScoringPreFlightScorecardUploadedCreativeTable from './CreativeScoringPreFlightScorecardUploadedCreativeTable';
import creativeScoringPreFlightScorecardUploadCreativeStyles from './CreativeScoringPreFlightScorecardUploadCreativeStyles';
import { getOrganizationId } from '../../../../../redux/selectors/partner.selectors';
import { useQuery } from '@tanstack/react-query';
import BffOrganizationService from '../../../../../apiServices/BffOrganizationService';
import {
  DEFAULT_MAX_VIDEO_DURATION,
  EXTENDED_MAX_VIDEO_DURATION,
} from '../../../../featureServices/files/utils/validateFile';

const { getIsMediaConversionTrimDurationEnabled } = BffOrganizationService;

const CreativeScoringPreFlightScorecardUploadCreative: React.FC = () => {
  const intl = useIntl();
  const { LEARN_MORE_LINKS, SUPPORTED_UPLOAD_FILE_TYPES } = COMPLIANCE;
  const { ANALYSIS_LINK } = LEARN_MORE_LINKS;
  const { boxStyle, emptyStateStyle } =
    creativeScoringPreFlightScorecardUploadCreativeStyles;
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const dispatch = useDispatch();
  const uploadedAssets = useSelector((state) => getBatchFolderContents(state));
  const hasUploadedAssets = Boolean(uploadedAssets?.length);

  const batchId = useSelector(getBatchId);

  const organizationId = useSelector(getOrganizationId);
  const { data: organizationDetails } = useQuery(
    ['IsMediaConversionTrimDurationEnabled', organizationId],
    () => getIsMediaConversionTrimDurationEnabled(organizationId),
    {
      enabled: Boolean(organizationId),
    },
  );
  const maxVideoLengthSec = organizationDetails?.featureRecord
    ?.MEDIA_CONVERSION_TRIM_DURATION
    ? EXTENDED_MAX_VIDEO_DURATION
    : DEFAULT_MAX_VIDEO_DURATION;

  const inputFieldRef = useRef<HTMLInputElement | null>(null);

  const handleUploadFilesDispatch = (supportedFiles: File[]) => {
    if (supportedFiles.length > 0) {
      dispatch(uploadFilesActionCreator(supportedFiles));
    }
  };

  const handleChooseClick = (event: MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    inputFieldRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);
  };
  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDraggingOver(false);

    const { items, files } = event.dataTransfer;

    if (wasFolderDraggedAndDropped(event)) {
      getFilesFromDataTransferItems(items)
        .then(async (files) => {
          const supportedFiles = await checkAndFilterSupportedFiles(
            files,
            uploadedAssets,
            maxVideoLengthSec,
          );
          handleUploadFilesDispatch(supportedFiles);
        })
        .catch((error) => {
          vmErrorLog(
            error,
            'CreativeScoringPreFlightScorecardUploadCreative.tsx handleFolderDrop',
            `Failed to upload files in dropped folder for batch: ${batchId}`,
          );
        });
    } else {
      const supportedFiles: File[] = await checkAndFilterSupportedFiles(
        files,
        uploadedAssets,
        maxVideoLengthSec,
      );
      handleUploadFilesDispatch(supportedFiles);
    }
  };

  const onLocalFileSelected = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { files } = event.target;
    const supportedFiles = await checkAndFilterSupportedFiles(
      files,
      uploadedAssets,
      maxVideoLengthSec,
    );
    handleUploadFilesDispatch(supportedFiles);
  };

  const renderHelpCenterLink = () => (
    <VidMobLink href={ANALYSIS_LINK} target="_blank" underline="hover">
      <VidMobTypography variant="body2" sx={{ maxWidth: '555px' }}>
        {
          intl.messages[
            'ui.compliance.contentAuditResult.selectStatus.helpCenter'
          ] as string
        }
      </VidMobTypography>
    </VidMobLink>
  );

  const renderUploadDescription = () => (
    <VidMobBox sx={{ display: 'flex' }}>
      <VidMobTypography variant="body2" sx={{ paddingRight: '2px' }}>
        {
          intl.messages[
            'ui.compliance.contentAuditResult.submissionReport.upload.text-line-2-frag-1'
          ] as string
        }
      </VidMobTypography>
      <VidMobTypography
        onClick={handleChooseClick}
        variant="body2"
        color="primary"
        sx={{ paddingRight: '2px', cursor: 'pointer' }}
      >
        {
          intl.messages[
            'ui.compliance.contentAuditResult.submissionReport.uploadV2.text-line-2-frag-2'
          ] as string
        }
      </VidMobTypography>
      <VidMobTypography variant="body2">
        {
          intl.messages[
            'ui.compliance.contentAuditResult.submissionReport.uploadV2.text-line-2-frag-3'
          ] as string
        }
      </VidMobTypography>
    </VidMobBox>
  );

  const renderDropZone = () => (
    <VidMobBox
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      sx={{
        ...boxStyle,
        border: `1px solid ${isDraggingOver ? '#1842EF' : '#BDBDBD'}`,
        backgroundColor: isDraggingOver ? '#EDF5FF' : '#FAFAFA',
      }}
    >
      <EmptyState
        icon={() => <CreateIcon />}
        extraClassDescription={emptyStateStyle}
        title={
          intl.messages[
            'ui.compliance.contentAuditResult.submissionReport.upload.text-line-1'
          ] as string
        }
        description={renderUploadDescription()}
      />
      <input
        ref={inputFieldRef}
        multiple
        accept={SUPPORTED_UPLOAD_FILE_TYPES}
        type="file"
        className="hidden-file-input"
        onChange={onLocalFileSelected}
      />
    </VidMobBox>
  );

  return (
    <VidMobBox
      sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      <VidMobTypography
        variant="body2"
        sx={{ maxWidth: '555px', paddingBottom: '24px' }}
      >
        <FormattedMessage
          id="ui.compliance.contentAuditResult.submissionReport.upload.subHeadingV2"
          values={{ helpCenter: renderHelpCenterLink() }}
        />
      </VidMobTypography>
      {renderDropZone()}
      {hasUploadedAssets && (
        <CreativeScoringPreFlightScorecardUploadedCreativeTable
          uploadedAssets={uploadedAssets}
        />
      )}
    </VidMobBox>
  );
};

export default CreativeScoringPreFlightScorecardUploadCreative;
