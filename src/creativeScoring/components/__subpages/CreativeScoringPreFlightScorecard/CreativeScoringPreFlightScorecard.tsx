import React, { useState, useEffect, useMemo } from 'react';
import history from '../../../../routing/history';
import { generatePath } from 'react-router';
import siteMap, { routeParams } from '../../../../routing/siteMap';
import { useDispatch, useSelector } from 'react-redux';
import {
  createScorecard,
  updateScorecard,
  cleanUpOnCancel as cleanUpOnCancelActionCreator,
  removeAllAssets as removeAllAssetsActionCreator,
} from '../../../redux/actions/uploads.actions';
import { toggleUploader } from '../../../../redux/actions/upload.actions';
import {
  getBatchId,
  getSubmissionReportStatus,
  getBatchFolderContentStatus,
  getUploadMediaIds,
} from '../../../redux/selectors/complianceUploads.selectors';
import { getCriteriaSetId } from '../../../redux/selectors/complianceCriteriaManagement.selectors';
import {
  submitPreFlightBatch as submitPreFlightBatchActionCreator,
  retrieveCountriesList as retrieveCountriesListActionCreator,
} from '../../../redux/actions/batches.actions';
import { fetchBrands } from '../../../../userManagement/redux/thunk/workspaces.thunk';
import {
  getCountriesList,
  getUpdateBatchStatusLoadingStatus,
  getSelectedScorecardStatus,
  getCountriesListLoadingStatus,
} from '../../../redux/selectors/complianceBatches.selectors';
import complianceUploadsSlice from '../../../redux/slices/complianceUploads.slice';
import CreativeScoringPreFlightScorecardDetails from './CreativeScoringPreFlightScorecardDetails';
import CreativeScoringPreFlightScorecardUploadCreative from './CreativeScoringPreFlightScorecardUploadCreative';
import CreativeScoringPreFlightScorecardHeader from './CreativeScoringPreFlightScorecardHeader';
import creativeScoringPreFlightScorecardStyles from './CreativeScoringPreFlightScorecardStyles';
import {
  VidMobBox,
  VidMobTypography,
  VidMobStepper,
  VidMobStep,
  VidMobStepLabel,
  VidMobCircularProgress,
} from '../../../../vidMobComponentWrappers';
import {
  Market,
  WarningMessageOutput,
  PartnerCriteriaMap,
  FlattenedObjectivesObject,
  ChannelObjectiveMap,
  Brand,
  Channel,
} from './types';
import { COMPLIANCE, GLOBALS } from '../../../../constants';
import { useIntl } from 'react-intl';
import CriteriaManagementSlice from '../../../redux/slices/criteriaManagement.slice';
import { getPlatformCriteriaCounts } from '../../../redux/selectors/criteriaManagement.selectors';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { getPlatformDisplayIntlText } from '../../../../utils/feConstantsUtils';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { CreateOrEditPreFlightCheck } from './CreateOrEditPreFlightCheck/CreateOrEditPreFlightCheck';
import { ListItem } from '../../../../components/ReportFilters/types';
import { useCriteriaGroups } from './hooks/useCriteriaGroups';
import { brandsSelector } from '../../../../userManagement/redux/selectors/workspaces.selectors';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';
import { CriterionServerType } from '../../../types/criteriaManagement.types';
import { useObjectivesDropdown } from './hooks/useObjectivesDropdown';

const isCriteriaGroupsInPreFlightCreationEnabled = getFeatureFlag(
  'isCriteriaGroupsInPreFlightCreationEnabled',
);

const {
  loadCriteria,
  setCriteriaPagination,
  reset: resetCriteriaManagement,
} = CriteriaManagementSlice.actions;
const { resetUploadsSlice } = complianceUploadsSlice.actions;

const stepsKeys = [
  'ui.compliance.contentAuditResult.submissionReport.headerV2.step.addDetails',
  'ui.compliance.contentAuditResult.submissionReport.headerV2.step.uploadCreative',
];

const chipChannelIconSx = {
  width: '18px',
  height: '18px',
  mr: '6px',
};

const { boxStyle, stepperStyle, boxCircularProgressStyle } =
  creativeScoringPreFlightScorecardStyles;

const { BATCH_TYPE, SUBMISSION_REPORT_STATUS } = COMPLIANCE;
const { READY_FOR_SUBMISSION } = SUBMISSION_REPORT_STATUS;
const { PRE_FLIGHT } = BATCH_TYPE;
const { REDUX_LOADING_STATUS } = GLOBALS;
const { SUCCESS, PENDING, NOT_LOADED } = REDUX_LOADING_STATUS;

const CreativeScoringPreFlightScorecard: React.FC = () => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const checkId = useSelector(getBatchId);
  const submissionReportStatus = useSelector(getSubmissionReportStatus);
  const criteriaSetId = useSelector(getCriteriaSetId);
  const partnerCriteriaMap = useSelector(
    getPlatformCriteriaCounts,
  ) as PartnerCriteriaMap;
  const updateBatchStatusLoadingStatus = useSelector(
    getUpdateBatchStatusLoadingStatus,
  );
  const selectedScorecardStatus = useSelector(getSelectedScorecardStatus);
  const updateCheckLoadingStatus = useSelector(getBatchFolderContentStatus);
  const selectedBatchStatus = checkId
    ? updateCheckLoadingStatus
    : submissionReportStatus;
  const uploadMediaIds = useSelector(getUploadMediaIds);
  const brandsApi = useSelector(brandsSelector);
  const marketsList = useSelector(getCountriesList);
  const marketOptionsLoadingStatus = useSelector(getCountriesListLoadingStatus);

  const [activeStep, setActiveStep] = useState<number>(0);
  const [isCreateButtonClicked, setIsCreateButtonClicked] =
    useState<boolean>(false);
  const [isLeaveButtonClicked, setIsLeaveButtonClicked] =
    useState<boolean>(false);
  const [isScoreCardLoading, setIsScoreCardLoading] = useState<boolean>(false);
  const [nextLocation, setNextLocation] = useState(null);
  const [isLeaveModalOpen, setIsLeaveModalOpen] = useState<boolean>(false);
  const [warningInfo, setWarningInfo] = useState<WarningMessageOutput>({
    warningMessageId: '',
    channelsToDisplay: [],
  });

  const [checkName, setCheckName] = useState<string>('');
  const [selectedChannels, setSelectedChannels] = useState<ListItem[]>([]);
  const [selectedCriteriaGroups, setSelectedCriteriaGroups] = useState<
    ListItem[]
  >([]);
  const [selectedBrands, setSelectedBrands] = useState<ListItem[]>([]);
  const [selectedMarkets, setSelectedMarkets] = useState<ListItem[]>([]);
  const [selectedObjectivesPerChannel, setSelectedObjectivesPerChannel] =
    useState<Record<string, FlattenedObjectivesObject[]>>({});
  const [channelsToObjectives, setChannelsToObjectives] =
    useState<ChannelObjectiveMap>({});
  const [includedCriteria, setIncludedCriteria] = useState<
    CriterionServerType[]
  >([]);

  const trimmedCheckName = useMemo(() => checkName.trim(), [checkName]);

  useObjectivesDropdown(
    selectedChannels as Channel[],
    channelsToObjectives,
    setChannelsToObjectives,
    setSelectedObjectivesPerChannel,
    selectedObjectivesPerChannel,
  );

  const channelOptions = useMemo(
    () =>
      COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.map((platformObject) => ({
        id: platformObject.id,
        name:
          getPlatformDisplayIntlText(platformObject.id, intl) ||
          platformObject.id,
        icon: getMUIIconForChannel(platformObject.id, chipChannelIconSx, true),
      })),
    [],
  );

  const {
    data: criteriaGroupsData,
    isLoading: areCriteriaGroupOptionsLoading,
  } = useCriteriaGroups();

  const criteriaGroupOptions = useMemo(
    () => criteriaGroupsData?.data,
    [criteriaGroupsData],
  ) as ListItem[] | undefined;

  const brandOptions = brandsApi?.data;
  const areBrandOptionsLoading = brandsApi?.isFetching;
  const marketOptions = marketsList?.map((country: Market) => ({
    id: country.isoCode,
    name: country.name,
  }));
  const areMarketOptionsLoading = [PENDING, NOT_LOADED].includes(
    marketOptionsLoadingStatus,
  );

  const stepLabels = stepsKeys.map((key) => intl.formatMessage({ id: key }));

  useEffect(() => {
    if (
      updateBatchStatusLoadingStatus === PENDING ||
      selectedScorecardStatus === PENDING
    ) {
      setIsScoreCardLoading(true);
    } else {
      setIsScoreCardLoading(false);
    }
  }, [updateBatchStatusLoadingStatus, selectedScorecardStatus]);

  useEffect(() => {
    dispatch(retrieveCountriesListActionCreator());
    // @ts-expect-error: thunk is not typed
    dispatch(fetchBrands());
    dispatch(
      setCriteriaPagination({
        pagination: {
          offset: 0,
          perPage: 1000,
        },
      }),
    );

    if (!isCriteriaGroupsInPreFlightCreationEnabled) {
      dispatch(loadCriteria({}));
    }
    return () => {
      dispatch(resetCriteriaManagement({}));
    };
  }, []);

  useEffect(() => {
    if (selectedBatchStatus === SUCCESS && isCreateButtonClicked) {
      history.push(
        generatePath(siteMap.creativeIntelligenceCompliance, {
          tab: routeParams.tabs.creativeIntelligenceCompliance
            .scorecardsLanding,
        }),
      );
    }
  }, [selectedBatchStatus, isCreateButtonClicked]);

  useEffect(() => {
    if (!isLeaveButtonClicked && !isCreateButtonClicked) {
      // @ts-ignore
      const unblock = history.block((location) => {
        setNextLocation(location);
        setIsLeaveModalOpen(true);
        return false;
      });

      return unblock;
    } else {
      if (nextLocation) {
        // @ts-ignore
        return history.push(nextLocation.pathname);
      }
      return history.push(
        generatePath(siteMap.creativeIntelligenceCompliance, {
          tab: routeParams.tabs.creativeIntelligenceCompliance
            .scorecardsLanding,
        }),
      );
    }
  }, [isLeaveButtonClicked, isCreateButtonClicked]);

  useEffect(
    () => () => {
      if (checkId) {
        cancelUploadsAndDeleteMedia();
      }
    },
    [checkId],
  );

  const cancelUploadsAndDeleteMedia = () => {
    dispatch(removeAllAssetsActionCreator());
    dispatch(cleanUpOnCancelActionCreator());
    dispatch(resetUploadsSlice());
    dispatch(toggleUploader(false));
  };

  const formatObjectivesForApi = () =>
    Object.values(selectedObjectivesPerChannel).flatMap((objectives) =>
      objectives.map((objective) => Number(objective.id)),
    );

  const handleSaveReportClick = () => {
    if (checkId) {
      const payload = {
        scorecardId: checkId,
        name: trimmedCheckName,
        platforms: selectedChannels,
        markets: selectedMarkets,
        brands: selectedBrands.map((brand) => brand.id),
        isCreateScorecardPage: true,
        objectives: formatObjectivesForApi(),
      };
      dispatch(updateScorecard(payload));
    } else {
      const actionParams = {
        name: trimmedCheckName,
        batchType: PRE_FLIGHT,
        criteriaSetId,
        platform: selectedChannels.map((channel) => channel.id),
        countries: selectedMarkets.map((market) => market.id),
        brands: selectedBrands.map((brand) => brand.id),
        objectives: formatObjectivesForApi(),
        ...(isCriteriaGroupsInPreFlightCreationEnabled &&
          selectedCriteriaGroups.length && {
            criteriaGroupIds: selectedCriteriaGroups.map((group) => group.id),
          }),
      };
      dispatch(createScorecard(actionParams));
    }
  };

  const handleCreateReportClick = () => {
    setIsCreateButtonClicked(true);
    dispatch(submitPreFlightBatchActionCreator(checkId, uploadMediaIds));
  };

  const handleCancelButton = () => {
    setIsLeaveModalOpen(true);
  };

  const handleOnLeaveButtonClick = () => {
    cancelUploadsAndDeleteMedia();
    setIsLeaveButtonClicked(true);
  };

  const renderStepperSteps = () =>
    stepLabels.map((stepLabel) => (
      <VidMobStep key={stepLabel}>
        <VidMobStepLabel style={{ cursor: 'pointer' }}>
          <VidMobTypography variant="subtitle2">{stepLabel}</VidMobTypography>
        </VidMobStepLabel>
      </VidMobStep>
    ));

  const checkCriteria = (
    selectedChannels: ListItem[],
    channelsWithoutCriteria: ListItem[],
  ) => ({
    singleChannelSelectedHasNoCriteria:
      channelsWithoutCriteria.length === 1 && selectedChannels.length === 1,
    someChannelsSelectedHaveNoCriteria:
      channelsWithoutCriteria.length > 0 &&
      selectedChannels.length > 1 &&
      channelsWithoutCriteria.length < selectedChannels.length,
    allChannelsSelectedHaveNoCriteria:
      channelsWithoutCriteria.length === selectedChannels.length &&
      channelsWithoutCriteria.length > 1,
  });

  const channelsWithoutCriteria = selectedChannels.filter(
    (channel: ListItem) => !partnerCriteriaMap[channel.id],
  );

  const {
    singleChannelSelectedHasNoCriteria,
    someChannelsSelectedHaveNoCriteria,
    allChannelsSelectedHaveNoCriteria,
  } = checkCriteria(selectedChannels, channelsWithoutCriteria);

  const isNextButtonDisabled =
    !trimmedCheckName ||
    !selectedChannels.length ||
    (isCriteriaGroupsInPreFlightCreationEnabled
      ? !includedCriteria.length
      : singleChannelSelectedHasNoCriteria ||
        someChannelsSelectedHaveNoCriteria ||
        allChannelsSelectedHaveNoCriteria);

  const getWarningMessage = (
    selectedChannels: ListItem[],
    channelsWithoutCriteria: ListItem[],
  ) => {
    let warningMessageId = '';
    let channelsToDisplay: ListItem[] = [];

    const {
      singleChannelSelectedHasNoCriteria,
      someChannelsSelectedHaveNoCriteria,
      allChannelsSelectedHaveNoCriteria,
    } = checkCriteria(selectedChannels, channelsWithoutCriteria);

    if (singleChannelSelectedHasNoCriteria) {
      warningMessageId =
        'ui.compliance.contentAuditResult.submissionReport.channelSelectionWarning.singleV2';
    } else if (
      someChannelsSelectedHaveNoCriteria ||
      allChannelsSelectedHaveNoCriteria
    ) {
      warningMessageId =
        'ui.compliance.contentAuditResult.submissionReport.channelSelectionWarning.someOrAll';
      channelsToDisplay = channelsWithoutCriteria;
    }

    return {
      warningMessageId,
      channelsToDisplay,
    };
  };

  useEffect(() => {
    if (isCriteriaGroupsInPreFlightCreationEnabled) {
      return;
    }

    if (selectedChannels.length) {
      const channelsWithoutCriteria = selectedChannels.filter(
        (channel: ListItem) => !partnerCriteriaMap[channel.id],
      );

      const { warningMessageId, channelsToDisplay } = getWarningMessage(
        selectedChannels,
        channelsWithoutCriteria,
      );
      setWarningInfo({ warningMessageId, channelsToDisplay });
    } else {
      setWarningInfo({ warningMessageId: '', channelsToDisplay: [] });
    }
  }, [selectedChannels]);

  return (
    <VidMobBox sx={boxStyle}>
      <CreativeScoringPreFlightScorecardHeader
        setActiveStep={setActiveStep}
        activeStep={activeStep}
        onClickNextButton={handleSaveReportClick}
        nextButtonDisabled={isNextButtonDisabled}
        createButtonDisabled={submissionReportStatus !== READY_FOR_SUBMISSION}
        onClickCreateButton={handleCreateReportClick}
        onClickCancelButton={handleCancelButton}
      />
      {isScoreCardLoading ? (
        <VidMobBox sx={boxCircularProgressStyle}>
          <VidMobCircularProgress />
        </VidMobBox>
      ) : (
        <VidMobBox
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
          }}
        >
          <VidMobStepper activeStep={activeStep} sx={stepperStyle}>
            {renderStepperSteps()}
          </VidMobStepper>
          {activeStep === 0 ? (
            isCriteriaGroupsInPreFlightCreationEnabled ? (
              <CreateOrEditPreFlightCheck
                checkName={checkName}
                setCheckName={setCheckName}
                channelOptions={channelOptions}
                selectedChannels={selectedChannels}
                setSelectedChannels={setSelectedChannels}
                criteriaGroupOptions={criteriaGroupOptions}
                areCriteriaGroupOptionsLoading={areCriteriaGroupOptionsLoading}
                selectedCriteriaGroups={selectedCriteriaGroups}
                setSelectedCriteriaGroups={setSelectedCriteriaGroups}
                brandOptions={brandOptions}
                areBrandOptionsLoading={areBrandOptionsLoading}
                selectedBrands={selectedBrands}
                setSelectedBrands={setSelectedBrands}
                marketOptions={marketOptions}
                areMarketOptionsLoading={areMarketOptionsLoading}
                selectedMarkets={selectedMarkets}
                setSelectedMarkets={setSelectedMarkets}
                channelsToObjectives={channelsToObjectives}
                selectedObjectivesPerChannel={selectedObjectivesPerChannel}
                setSelectedObjectivesPerChannel={
                  setSelectedObjectivesPerChannel
                }
                includedCriteria={includedCriteria}
                setIncludedCriteria={setIncludedCriteria}
              />
            ) : (
              <CreativeScoringPreFlightScorecardDetails
                scorecardName={checkName}
                setScorecardName={setCheckName}
                partnerChannels={channelOptions}
                selectedChannels={selectedChannels as Channel[]}
                setSelectedChannels={setSelectedChannels}
                brandOptions={brandOptions}
                areBrandOptionsLoading={areBrandOptionsLoading}
                selectedBrands={selectedBrands as Brand[]}
                setSelectedBrands={setSelectedBrands}
                countriesList={marketOptions}
                selectedMarkets={selectedMarkets as Market[]}
                setSelectedMarkets={setSelectedMarkets}
                warningInfo={warningInfo}
                setChannelsToObjectives={setChannelsToObjectives}
                channelsToObjectives={channelsToObjectives}
                selectedObjectivesPerChannel={selectedObjectivesPerChannel}
                setSelectedObjectivesPerChannel={
                  setSelectedObjectivesPerChannel
                }
              />
            )
          ) : (
            <CreativeScoringPreFlightScorecardUploadCreative />
          )}
        </VidMobBox>
      )}
      {isLeaveModalOpen && (
        <CustomDialog
          isOpen={isLeaveModalOpen}
          onSubmit={handleOnLeaveButtonClick}
          onClose={() => setIsLeaveModalOpen(false)}
          headerText={intl.formatMessage({
            id: 'ui.compliance.contentAuditResult.submissionReport.headerV2.leaveModal.header',
            defaultMessage: 'Leave this page?',
          })}
          headerSubText={intl.formatMessage({
            id: 'ui.compliance.contentAuditResult.submissionReport.headerV2.leaveModal.description',
            defaultMessage:
              'If you leave this page now, you’ll lose any unsaved changes.',
          })}
          submitButtonLabel={intl.formatMessage({
            id: 'ui.compliance.contentAuditResult.submissionReport.headerV2.leaveModal.leaveButton',
            defaultMessage: 'Leave',
          })}
        />
      )}
    </VidMobBox>
  );
};

export default CreativeScoringPreFlightScorecard;
