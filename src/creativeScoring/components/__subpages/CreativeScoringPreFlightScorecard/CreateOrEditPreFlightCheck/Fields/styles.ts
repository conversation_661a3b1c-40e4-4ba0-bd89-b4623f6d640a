export const sectionSx = {
  flexDirection: 'column',
  gap: '8px',
};

export const loadingSectionSx = {
  flexDirection: 'row',
  width: '100%',
  alignItems: 'center',
  justifyContent: 'center',
  height: '48px',
};

export const sectionLabelSx = {
  flexDirection: 'column',
  gap: '4px',
  maxWidth: '460px',
};

export const objectiveSectionLabelSx = {
  ...sectionLabelSx,
  flexDirection: 'row',
  alignItems: 'center',
};

export const objectiveSectionLabelTextSx = {
  flexDirection: 'column',
};

export const textFieldSx = {
  width: '100%',
  '& .MuiOutlinedInput-root': {
    fontSize: '14px',
    height: '44px',
  },
};

export const dropdownContainerSx = {
  width: '100%',
};

export const customDropdownButtonSxProps = {
  customButtonSx: {
    minHeight: '48px',
    height: 'fit-content',
  },
  labelTextSx: {
    fontWeight: '400',
  },
  slotSx: {
    width: '100%',
    maxWidth: '100%',
  },
};

export const customDropdownMenuSx = {
  minWidth: '490px',
  maxWidth: '616px',
};
