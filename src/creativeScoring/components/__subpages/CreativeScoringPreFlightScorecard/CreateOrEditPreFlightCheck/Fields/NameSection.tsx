import React, { ChangeEvent } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobStack,
  VidMobTextField,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import { sectionSx, textFieldSx } from './styles';

interface Props {
  checkName: string;
  setCheckName: (name: string) => void;
}

export const NameSection = ({ checkName, setCheckName }: Props) => {
  const intl = useIntl();

  const handleChangeCheckName = (event: ChangeEvent<HTMLInputElement>) => {
    setCheckName(event.target.value);
  };

  return (
    <VidMobStack direction="column" sx={sectionSx}>
      <VidMobTypography variant="subtitle2">
        {intl.formatMessage({
          id: 'ui.creativeScoring.preFlightCheck.create.fields.name.title',
          defaultMessage: 'Name*',
        })}
      </VidMobTypography>
      <VidMobTextField
        sx={textFieldSx}
        onChange={handleChangeCheckName}
        value={checkName}
      />
    </VidMobStack>
  );
};
