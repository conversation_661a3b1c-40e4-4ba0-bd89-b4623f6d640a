import React, { useRef } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobCircularProgress,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import {
  customDropdownButtonSxProps,
  customDropdownMenuSx,
  dropdownContainerSx,
  loadingSectionSx,
  sectionLabelSx,
  sectionSx,
} from '../styles';
import MultiValueInput from '../../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';
import { ListItem } from '../../../../../../../components/ReportFilters/types';

interface Props {
  brandOptions: ListItem[];
  areBrandOptionsLoading: boolean;
  selectedBrands: ListItem[];
  setSelectedBrands: (selectedBrands: ListItem[]) => void;
}

export const BrandsSection = ({
  brandOptions,
  areBrandOptionsLoading,
  selectedBrands,
  setSelectedBrands,
}: Props) => {
  const intl = useIntl();

  const dropdownContainerRef = useRef<HTMLDivElement>(null);

  const customMenuSx = {
    ...customDropdownMenuSx,
    width: dropdownContainerRef.current?.clientWidth,
  };

  return (
    <VidMobStack direction="column" sx={sectionSx}>
      <VidMobStack direction="column" sx={sectionLabelSx}>
        <VidMobTypography variant="subtitle2">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.brand.title',
            defaultMessage: 'Brand',
          })}
        </VidMobTypography>
        <VidMobTypography variant="caption" color="text.secondary">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.brand.subtitle',
            defaultMessage:
              'Select the brand(s) associated with this creative.',
          })}
        </VidMobTypography>
      </VidMobStack>
      {areBrandOptionsLoading ? (
        <VidMobStack sx={loadingSectionSx}>
          <VidMobCircularProgress />
        </VidMobStack>
      ) : (
        <VidMobBox ref={dropdownContainerRef} sx={dropdownContainerSx}>
          <MultiValueInput
            displaySearch
            shouldShowSelectAll
            valueOptions={brandOptions}
            value={selectedBrands}
            onChange={setSelectedBrands}
            customMenuSx={customMenuSx}
            {...customDropdownButtonSxProps}
          />
        </VidMobBox>
      )}
    </VidMobStack>
  );
};
