import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobAccordion,
  VidMobAccordionSummary,
  VidMobAccordionDetails,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import { sectionLabelSx } from '../styles';
import { ChevronDownIcon } from '../../../../../../../assets/vidmob-mui-icons/general';
import { BrandsSection } from './BrandsSection';
import { ListItem } from '../../../../../../../components/ReportFilters/types';
import { MarketsSection } from './MarketsSection';
import { ObjectivesSections } from './ObjectivesSections';
import { ChannelObjectiveMap, FlattenedObjectivesObject } from '../../../types';

const accordionSx = {
  height: 'fit-content',
  borderRadius: '6px !important',
  border: '1px solid',
  borderColor: 'action.focus',
  boxShadow: 0,
  '::before': {
    top: 0,
    backgroundColor: 'transparent',
  },
  '.MuiButtonBase-root-MuiAccordionSummary-root': {
    borderRadius: 0,
  },
};

const accordionSummarySx = {
  width: '100%',
  height: '86px',
  m: 0,
  p: '24px',
  borderRadius: '6px 6px 0 0',
};

const chevronSx = {
  width: '20px',
  height: '20px',
};

const accordionDetailsSx = {
  display: 'flex',
  flexDirection: 'column',
  borderTop: '1px solid',
  borderTopColor: 'action.focus',
  p: '24px',
  gap: '24px',
};

interface Props {
  brandOptions: ListItem[];
  areBrandOptionsLoading: boolean;
  selectedBrands: ListItem[];
  setSelectedBrands: (selectedBrands: ListItem[]) => void;
  marketOptions: ListItem[];
  areMarketOptionsLoading: boolean;
  selectedMarkets: ListItem[];
  setSelectedMarkets: (selectedMarkets: ListItem[]) => void;
  selectedChannels: ListItem[];
  channelsToObjectives: ChannelObjectiveMap;
  selectedObjectivesPerChannel: Record<string, FlattenedObjectivesObject[]>;
  setSelectedObjectivesPerChannel: (
    objectives: Record<string, FlattenedObjectivesObject[]>,
  ) => void;
}

export const AdvancedOptions = ({
  brandOptions,
  areBrandOptionsLoading,
  selectedBrands,
  setSelectedBrands,
  marketOptions,
  areMarketOptionsLoading,
  selectedMarkets,
  setSelectedMarkets,
  selectedChannels,
  channelsToObjectives,
  selectedObjectivesPerChannel,
  setSelectedObjectivesPerChannel,
}: Props) => {
  const intl = useIntl();

  const renderSummary = () => (
    <VidMobAccordionSummary
      expandIcon={<ChevronDownIcon sx={chevronSx} />}
      sx={accordionSummarySx}
    >
      <VidMobStack direction="column" sx={sectionLabelSx}>
        <VidMobTypography variant="subtitle2">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.title',
            defaultMessage: 'Advanced options',
          })}
        </VidMobTypography>
        <VidMobTypography variant="caption" color="text.secondary">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.subtitle',
            defaultMessage:
              'Add brand, market, or objective to this Pre-flight Check to make it easier to track within the Pre-flight landing page.',
          })}
        </VidMobTypography>
      </VidMobStack>
    </VidMobAccordionSummary>
  );

  const renderDetails = () => (
    <VidMobAccordionDetails sx={accordionDetailsSx}>
      <BrandsSection
        brandOptions={brandOptions}
        areBrandOptionsLoading={areBrandOptionsLoading}
        selectedBrands={selectedBrands}
        setSelectedBrands={setSelectedBrands}
      />
      <MarketsSection
        marketOptions={marketOptions}
        areMarketOptionsLoading={areMarketOptionsLoading}
        selectedMarkets={selectedMarkets}
        setSelectedMarkets={setSelectedMarkets}
      />
      <ObjectivesSections
        selectedChannels={selectedChannels}
        channelsToObjectives={channelsToObjectives}
        selectedObjectivesPerChannel={selectedObjectivesPerChannel}
        setSelectedObjectivesPerChannel={setSelectedObjectivesPerChannel}
      />
    </VidMobAccordionDetails>
  );

  return (
    <VidMobAccordion disableGutters sx={accordionSx}>
      {renderSummary()}
      {renderDetails()}
    </VidMobAccordion>
  );
};
