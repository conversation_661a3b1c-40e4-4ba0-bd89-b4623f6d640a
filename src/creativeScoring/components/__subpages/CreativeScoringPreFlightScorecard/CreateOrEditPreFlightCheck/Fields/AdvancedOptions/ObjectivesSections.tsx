import React, { useRef } from 'react';
import { useIntl } from 'react-intl';
import { ChannelObjectiveMap, FlattenedObjectivesObject } from '../../../types';
import { ListItem } from '../../../../../../../components/ReportFilters/types';
import { DropdownOption } from '../../../../../../../muiCustomComponents/Dropdowns/types';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import {
  customDropdownButtonSxProps,
  customDropdownMenuSx,
  dropdownContainerSx,
  objectiveSectionLabelSx,
  objectiveSectionLabelTextSx,
  sectionSx,
} from '../styles';
import MultiValueInput from '../../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';

const containerSx = {
  ...dropdownContainerSx,
  gap: '24px',
};

interface Props {
  selectedChannels: ListItem[];
  channelsToObjectives: ChannelObjectiveMap;
  selectedObjectivesPerChannel: Record<string, FlattenedObjectivesObject[]>;
  setSelectedObjectivesPerChannel: (
    objectives: Record<string, FlattenedObjectivesObject[]>,
  ) => void;
}

export const ObjectivesSections = ({
  selectedChannels,
  channelsToObjectives,
  selectedObjectivesPerChannel,
  setSelectedObjectivesPerChannel,
}: Props) => {
  const intl = useIntl();

  const containerRef = useRef<HTMLDivElement>(null);

  const updateSelectedObjectives = (
    channelName: string,
    options: DropdownOption[],
  ) => {
    const updatedSelectedOptions = { ...selectedObjectivesPerChannel };
    updatedSelectedOptions[channelName] =
      options as FlattenedObjectivesObject[];
    setSelectedObjectivesPerChannel(updatedSelectedOptions);
  };

  const customMenuSx = {
    ...customDropdownMenuSx,
    width: containerRef.current?.clientWidth,
  };

  return (
    <VidMobStack ref={containerRef} sx={containerSx}>
      {Object.entries(channelsToObjectives).map(
        ([channelName, objectiveOptions]) => {
          const icon = selectedChannels.find(
            (channel) => channel.name === channelName,
          )?.icon;
          return (
            <VidMobStack key={channelName} direction="column" sx={sectionSx}>
              <VidMobStack sx={objectiveSectionLabelSx}>
                {icon && icon}
                <VidMobStack sx={objectiveSectionLabelTextSx}>
                  <VidMobTypography variant="subtitle2">
                    {intl.formatMessage(
                      {
                        id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.objective.title',
                        defaultMessage: '{channel} objective',
                      },
                      {
                        channel: channelName,
                      },
                    )}
                  </VidMobTypography>
                  <VidMobTypography variant="caption" color="text.secondary">
                    {intl.formatMessage({
                      id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.objective.subtitle',
                      defaultMessage: 'Select objective(s) for this creative.',
                    })}
                  </VidMobTypography>
                </VidMobStack>
              </VidMobStack>
              <MultiValueInput
                hasGroupSelect
                displaySearch
                shouldShowSelectAll
                valueOptions={objectiveOptions}
                value={selectedObjectivesPerChannel[channelName]}
                onChange={(options) =>
                  updateSelectedObjectives(channelName, options)
                }
                customMenuSx={customMenuSx}
                {...customDropdownButtonSxProps}
              />
            </VidMobStack>
          );
        },
      )}
    </VidMobStack>
  );
};
