import React, { useRef } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobCircularProgress,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import {
  customDropdownButtonSxProps,
  customDropdownMenuSx,
  dropdownContainerSx,
  loadingSectionSx,
  sectionLabelSx,
  sectionSx,
} from '../styles';
import MultiValueInput from '../../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';
import { ListItem } from '../../../../../../../components/ReportFilters/types';

interface Props {
  marketOptions: ListItem[];
  areMarketOptionsLoading: boolean;
  selectedMarkets: ListItem[];
  setSelectedMarkets: (selectedBrands: ListItem[]) => void;
}

export const MarketsSection = ({
  marketOptions,
  areMarketOptionsLoading,
  selectedMarkets,
  setSelectedMarkets,
}: Props) => {
  const intl = useIntl();

  const dropdownContainerRef = useRef<HTMLDivElement>(null);

  const customMenuSx = {
    ...customDropdownMenuSx,
    width: dropdownContainerRef.current?.clientWidth,
  };

  return (
    <VidMobStack direction="column" sx={sectionSx}>
      <VidMobStack direction="column" sx={sectionLabelSx}>
        <VidMobTypography variant="subtitle2">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.market.title',
            defaultMessage: 'Market',
          })}
        </VidMobTypography>
        <VidMobTypography variant="caption" color="text.secondary">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.advanced.market.subtitle',
            defaultMessage: 'Select location(s) where this creative will run.',
          })}
        </VidMobTypography>
      </VidMobStack>
      {areMarketOptionsLoading ? (
        <VidMobStack sx={loadingSectionSx}>
          <VidMobCircularProgress />
        </VidMobStack>
      ) : (
        <VidMobBox ref={dropdownContainerRef} sx={dropdownContainerSx}>
          <MultiValueInput
            displaySearch
            shouldShowSelectAll
            valueOptions={marketOptions}
            value={selectedMarkets}
            onChange={setSelectedMarkets}
            customMenuSx={customMenuSx}
            {...customDropdownButtonSxProps}
          />
        </VidMobBox>
      )}
    </VidMobStack>
  );
};
