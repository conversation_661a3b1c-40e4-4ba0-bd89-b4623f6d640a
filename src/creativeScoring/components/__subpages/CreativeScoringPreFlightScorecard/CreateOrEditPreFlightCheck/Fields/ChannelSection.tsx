import React, { useRef } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import MultiValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';
import { RemovableChipsDropdownLabel } from '../../../../../../muiCustomComponents/Dropdowns/RemovableChipsDrowpdownLabel';
import { ListItem } from '../../../../../../components/ReportFilters/types';
import {
  customDropdownButtonSxProps,
  customDropdownMenuSx,
  dropdownContainerSx,
  sectionLabelSx,
  sectionSx,
} from './styles';

interface Props {
  channelOptions: ListItem[];
  selectedChannels: ListItem[];
  setSelectedChannels: (channels: ListItem[]) => void;
}

export const ChannelSection = ({
  channelOptions,
  selectedChannels,
  setSelectedChannels,
}: Props) => {
  const intl = useIntl();

  const dropdownContainerRef = useRef<HTMLDivElement>(null);
  const labelElementParentRef = useRef<HTMLDivElement>(null);

  const customMenuSx = {
    ...customDropdownMenuSx,
    width: dropdownContainerRef.current?.clientWidth,
  };

  return (
    <VidMobStack direction="column" sx={sectionSx}>
      <VidMobStack direction="column" sx={sectionLabelSx}>
        <VidMobTypography variant="subtitle2">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.channel.title',
            defaultMessage: 'Channel*',
          })}
        </VidMobTypography>
        <VidMobTypography variant="caption" color="text.secondary">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.name.subtitle',
            defaultMessage: 'On which channel(s) will the creative run?',
          })}
        </VidMobTypography>
      </VidMobStack>
      <VidMobBox ref={dropdownContainerRef} sx={dropdownContainerSx}>
        <MultiValueInput
          labelHasPills
          labelElementParentRef={labelElementParentRef}
          isChannelDropdown
          displaySearch
          shouldShowSelectAll
          valueOptions={channelOptions}
          value={selectedChannels}
          onChange={setSelectedChannels}
          LabelElement={() => (
            <RemovableChipsDropdownLabel
              parentRef={labelElementParentRef}
              selectedItems={selectedChannels}
              setSelectedItems={setSelectedChannels}
            />
          )}
          disableButtonRipple={selectedChannels.length > 0}
          customMenuSx={customMenuSx}
          {...customDropdownButtonSxProps}
        />
      </VidMobBox>
    </VidMobStack>
  );
};
