import React from 'react';
import { VidMobStack } from '../../../../../../vidMobComponentWrappers';
import { ListItem } from '../../../../../../components/ReportFilters/types';
import { NameSection } from './NameSection';
import { ChannelSection } from './ChannelSection';
import { CriteriaGroupsSection } from './CriteriaGroupsSection';
import { AdvancedOptions } from './AdvancedOptions/AdvancedOptions';
import { ChannelObjectiveMap, FlattenedObjectivesObject } from '../../types';

const containerSx = {
  flexDirection: 'column',
  width: 'calc(100% - 16px)',
  minWidth: '490px',
  maxWidth: '616px',
  pr: '16px',
  overflowY: 'auto',
  gap: '24px',
};

const preFlightCreatePageContainerSx = {
  ...containerSx,
  height: '72vh',
};

interface Props {
  checkName: string;
  setCheckName: (name: string) => void;
  channelOptions: ListItem[];
  selectedChannels: ListItem[];
  setSelectedChannels: (channels: ListItem[]) => void;
  criteriaGroupOptions?: ListItem[];
  areCriteriaGroupOptionsLoading: boolean;
  selectedCriteriaGroups: ListItem[];
  setSelectedCriteriaGroups: (criteriaGroups: ListItem[]) => void;
  brandOptions: ListItem[];
  areBrandOptionsLoading: boolean;
  selectedBrands: ListItem[];
  setSelectedBrands: (selectedBrands: ListItem[]) => void;
  marketOptions: ListItem[];
  areMarketOptionsLoading: boolean;
  selectedMarkets: ListItem[];
  setSelectedMarkets: (selectedMarkets: ListItem[]) => void;
  channelsToObjectives: ChannelObjectiveMap;
  selectedObjectivesPerChannel: Record<string, FlattenedObjectivesObject[]>;
  setSelectedObjectivesPerChannel: (
    objectives: Record<string, FlattenedObjectivesObject[]>,
  ) => void;
  isPreFlightEditModal?: boolean;
}

export const Fields = ({
  checkName,
  setCheckName,
  channelOptions,
  selectedChannels,
  setSelectedChannels,
  criteriaGroupOptions,
  areCriteriaGroupOptionsLoading,
  selectedCriteriaGroups,
  setSelectedCriteriaGroups,
  brandOptions,
  areBrandOptionsLoading,
  selectedBrands,
  setSelectedBrands,
  marketOptions,
  areMarketOptionsLoading,
  selectedMarkets,
  setSelectedMarkets,
  channelsToObjectives,
  selectedObjectivesPerChannel,
  setSelectedObjectivesPerChannel,
  isPreFlightEditModal,
}: Props) => (
  <VidMobStack
    sx={isPreFlightEditModal ? containerSx : preFlightCreatePageContainerSx}
  >
    <NameSection checkName={checkName} setCheckName={setCheckName} />
    <ChannelSection
      channelOptions={channelOptions}
      selectedChannels={selectedChannels}
      setSelectedChannels={setSelectedChannels}
    />
    <CriteriaGroupsSection
      criteriaGroupOptions={criteriaGroupOptions || []}
      areCriteriaGroupOptionsLoading={areCriteriaGroupOptionsLoading}
      selectedCriteriaGroups={selectedCriteriaGroups}
      setSelectedCriteriaGroups={setSelectedCriteriaGroups}
    />
    <AdvancedOptions
      brandOptions={brandOptions}
      areBrandOptionsLoading={areBrandOptionsLoading}
      selectedBrands={selectedBrands}
      setSelectedBrands={setSelectedBrands}
      marketOptions={marketOptions}
      areMarketOptionsLoading={areMarketOptionsLoading}
      selectedMarkets={selectedMarkets}
      setSelectedMarkets={setSelectedMarkets}
      selectedChannels={selectedChannels}
      channelsToObjectives={channelsToObjectives}
      selectedObjectivesPerChannel={selectedObjectivesPerChannel}
      setSelectedObjectivesPerChannel={setSelectedObjectivesPerChannel}
    />
  </VidMobStack>
);
