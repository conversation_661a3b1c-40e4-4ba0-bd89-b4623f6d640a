import React, { useRef } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobCircularProgress,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import MultiValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';
import { RemovableChipsDropdownLabel } from '../../../../../../muiCustomComponents/Dropdowns/RemovableChipsDrowpdownLabel';
import { ListItem } from '../../../../../../components/ReportFilters/types';
import {
  customDropdownButtonSxProps,
  customDropdownMenuSx,
  dropdownContainerSx,
  loadingSectionSx,
  sectionLabelSx,
  sectionSx,
} from './styles';

interface Props {
  criteriaGroupOptions: ListItem[];
  areCriteriaGroupOptionsLoading: boolean;
  selectedCriteriaGroups: ListItem[];
  setSelectedCriteriaGroups: (criteriaGroups: ListItem[]) => void;
}

export const CriteriaGroupsSection = ({
  criteriaGroupOptions,
  areCriteriaGroupOptionsLoading,
  selectedCriteriaGroups,
  setSelectedCriteriaGroups,
}: Props) => {
  const intl = useIntl();

  const dropdownContainerRef = useRef<HTMLDivElement>(null);
  const labelElementParentRef = useRef<HTMLDivElement>(null);

  const customMenuSx = {
    ...customDropdownMenuSx,
    width: dropdownContainerRef.current?.clientWidth,
  };

  return (
    <VidMobStack direction="column" sx={sectionSx}>
      <VidMobStack direction="column" sx={sectionLabelSx}>
        <VidMobTypography variant="subtitle2">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.criteriaGroup.title',
            defaultMessage: 'Criteria group',
          })}
        </VidMobTypography>
        <VidMobTypography variant="caption" color="text.secondary">
          {intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.fields.criteriaGroup.subtitle',
            defaultMessage:
              'Against which criteria group(s) do you want the creative to be scored?',
          })}
        </VidMobTypography>
      </VidMobStack>
      {areCriteriaGroupOptionsLoading ? (
        <VidMobStack sx={loadingSectionSx}>
          <VidMobCircularProgress />
        </VidMobStack>
      ) : (
        <VidMobBox ref={dropdownContainerRef} sx={dropdownContainerSx}>
          <MultiValueInput
            labelHasPills
            labelElementParentRef={labelElementParentRef}
            displaySearch
            shouldShowSelectAll
            valueOptions={criteriaGroupOptions}
            value={selectedCriteriaGroups}
            onChange={setSelectedCriteriaGroups}
            LabelElement={() => (
              <RemovableChipsDropdownLabel
                parentRef={labelElementParentRef}
                selectedItems={selectedCriteriaGroups}
                setSelectedItems={setSelectedCriteriaGroups}
              />
            )}
            disableButtonRipple={selectedCriteriaGroups.length > 0}
            customMenuSx={customMenuSx}
            noChips
            {...customDropdownButtonSxProps}
          />
        </VidMobBox>
      )}
    </VidMobStack>
  );
};
