import { FIELD_CHANNEL, FIELD_CRITERIA_GROUP, FIELD_NAME } from './constants';

import { CriterionServerType } from '../../../../../../types/criteriaManagement.types';

export const createRows = (criteria: CriterionServerType[]) =>
  criteria.map((criterion) => ({
    id: criterion.id,
    [FIELD_NAME]: criterion.name || criterion.rule,
    isBestPractice: criterion.isBestPractice,
    isOptional: criterion.isOptional,
    customIconUrl: criterion.customIconUrl,
    [FIELD_CHANNEL]: criterion.platform,
    [FIELD_CRITERIA_GROUP]: criterion.criteriaGroups,
  }));
