import React from 'react';
import { useIntl } from 'react-intl';
import { GridCellParams } from '@mui/x-data-grid-pro';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../../../../vidMobComponentWrappers';
import { cellContentSx } from '../../styles';
import { BestPracticeIcon } from '../../../../../../../../assets/vidmob-mui-icons/general';
import CriteriaIsOptionalPill from '../../../../../../shared/CriteriaIsOptionalPill';
import { Tooltip } from '@mui/material';

const iconsContainerSx = {
  ...cellContentSx,
  gap: '4px',
};

const iconSx = {
  width: '14px',
  height: '14px',
};

const optionalCriteriaPillSx = {
  m: 0,
};

export const NameCell = ({ row }: GridCellParams) => {
  const intl = useIntl();

  const { name, isBestPractice, isOptional, customIconUrl } = row;

  const BestPracticeIconAndTooltip = () => (
    <VidMobTooltip
      placement="top"
      title={intl.formatMessage({
        id: 'ui.creativeScoring.inFlightReport.tab.criteria.accordion.tooltip.bestPractice', // TODO: new key
        defaultMessage: 'Channel best practice',
      })}
      disableInteractive
    >
      <VidMobBox sx={iconSx}>
        <BestPracticeIcon sx={iconSx} />
      </VidMobBox>
    </VidMobTooltip>
  );

  return (
    <VidMobStack sx={cellContentSx}>
      <Tooltip placement="top-start" title={name}>
        <VidMobTypography variant="body2">{name}</VidMobTypography>
      </Tooltip>
      <VidMobStack sx={iconsContainerSx}>
        {customIconUrl && <VidMobAvatar src={customIconUrl} sx={iconSx} />}
        {isBestPractice && <BestPracticeIconAndTooltip />}
        {isOptional && (
          <CriteriaIsOptionalPill additionalStyles={optionalCriteriaPillSx} />
        )}
      </VidMobStack>
    </VidMobStack>
  );
};
