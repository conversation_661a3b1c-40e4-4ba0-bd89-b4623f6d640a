import React from 'react';
import { GridCellParams } from '@mui/x-data-grid-pro';
import { VidMobStack } from '../../../../../../../../vidMobComponentWrappers';
import { cellContentSx } from '../../styles';
import { CriteriaGroupCell as CriteriaManagementTableCriteriaGroupCell } from '../../../../../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells';

export const CriteriaGroupCell = (params: GridCellParams) => (
  <VidMobStack sx={cellContentSx}>
    <CriteriaManagementTableCriteriaGroupCell params={params} />
  </VidMobStack>
);
