import React from 'react';
import { useIntl } from 'react-intl';
import { GridCellParams } from '@mui/x-data-grid-pro';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../../vidMobComponentWrappers';
import { getPlatformDisplayIntlText } from '../../../../../../../../utils/feConstantsUtils';
import getMUIIconForChannel from '../../../../../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { cellContentSx } from '../../styles';
import { FIELD_CHANNEL } from '../constants';

const iconSx = {
  width: '20px',
  height: '20px',
};

export const ChannelCell = ({ row }: GridCellParams) => {
  const intl = useIntl();
  const channelId = row[FIELD_CHANNEL];
  const channelName = getPlatformDisplayIntlText(channelId, intl);
  const icon = getMUIIconForChannel(channelId, iconSx, true);

  return (
    <VidMobStack sx={cellContentSx}>
      {icon}
      <VidMobTypography variant="body2">{channelName}</VidMobTypography>
    </VidMobStack>
  );
};
