import React from 'react';
import { VidMobBox } from '../../../../../../../vidMobComponentWrappers';
import { DataGridPro, GridSortModel } from '@mui/x-data-grid-pro';
import { dataGridContainerSx, dataGridSx } from '../styles';
import {
  INCLUDED_CRITERIA_TABLE_COLUMN_HEADER_HEIGHT,
  INCLUDED_CRITERIA_TABLE_COLUMNS,
  INCLUDED_CRITERIA_TABLE_ROW_HEIGHT,
} from './constants';
import { createRows } from './dataGridUtils';
import LazyLoadingFooter from '../../../../../../../muiCustomComponents/LazyLoadingFooter';
import { CriterionServerType } from '../../../../../../types/criteriaManagement.types';

const loadingFooterSx = {
  height: `${INCLUDED_CRITERIA_TABLE_ROW_HEIGHT}px`,
  mt: '12px',
  '.MuiCircularProgress-root': {
    width: '32px !important',
    height: '32px !important',
  },
};

interface Props {
  criteria: CriterionServerType[];
  getNextPage: () => void;
  isNextPageLoading: boolean;
  sortModel: GridSortModel;
  onSortModelChange: (sortModel: GridSortModel) => void;
}

export const IncludedCriteriaTable = ({
  criteria,
  getNextPage,
  isNextPageLoading,
  sortModel,
  onSortModelChange,
}: Props) => (
  <VidMobBox sx={dataGridContainerSx}>
    <DataGridPro
      columns={INCLUDED_CRITERIA_TABLE_COLUMNS}
      rows={createRows(criteria)}
      disableColumnMenu
      hideFooter={!isNextPageLoading}
      slots={{
        footer: () => <LazyLoadingFooter customSx={loadingFooterSx} />,
      }}
      onRowsScrollEnd={getNextPage}
      sortingMode="server"
      sortModel={sortModel}
      onSortModelChange={onSortModelChange}
      columnHeaderHeight={INCLUDED_CRITERIA_TABLE_COLUMN_HEADER_HEIGHT}
      rowHeight={INCLUDED_CRITERIA_TABLE_ROW_HEIGHT}
      sx={dataGridSx}
    />
  </VidMobBox>
);
