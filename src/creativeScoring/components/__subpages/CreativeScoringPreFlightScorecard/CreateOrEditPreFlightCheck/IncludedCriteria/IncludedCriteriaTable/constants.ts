import { GridColDef } from '@mui/x-data-grid-pro';
import { getIntl } from '../../../../../../../utils/getIntl';
import { ChannelCell } from './cellComponents/ChannelCell';
import { NameCell } from './cellComponents/NameCell';
import { CriteriaGroupCell } from './cellComponents/CriteriaGroupCell';
import { PaginationParams } from '../../../../../../../types/pagination.types';
import {
  SortDirection,
  SortParams,
} from '../../../../../../../types/sort.types';

const intl = getIntl();

export const FIELD_NAME = 'name';
export const FIELD_CHANNEL = 'platform';
export const FIELD_CRITERIA_GROUP = 'criteriaGroup';

const INCLUDED_CRITERIA_TABLE_PAGE_SIZE = 100;

export const INCLUDED_CRITERIA_TABLE_DEFAULT_PAGINATION: PaginationParams = {
  offset: 0,
  perPage: INCLUDED_CRITERIA_TABLE_PAGE_SIZE,
};

export const INCLUDED_CRITERIA_TABLE_DEFAULT_SORT_PARAMS: SortParams = {
  sortBy: FIELD_CHANNEL,
  sortDirection: SortDirection.ASC,
};

export const INCLUDED_CRITERIA_TABLE_COLUMN_HEADER_HEIGHT = 42;
export const INCLUDED_CRITERIA_TABLE_ROW_HEIGHT = 48;

export const INCLUDED_CRITERIA_TABLE_COLUMNS: GridColDef[] = [
  {
    field: FIELD_NAME,
    headerName: intl.formatMessage({
      id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.table.header.name',
      defaultMessage: 'Name',
    }),
    flex: 3,
    renderCell: NameCell,
  },
  {
    field: FIELD_CHANNEL,
    headerName: intl.formatMessage({
      id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.table.header.channel',
      defaultMessage: 'Channel',
    }),
    width: 168,
    renderCell: ChannelCell,
  },
  {
    field: FIELD_CRITERIA_GROUP,
    headerName: intl.formatMessage({
      id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.table.header.criteriaGroup',
      defaultMessage: 'Criteria group',
    }),
    flex: 2,
    renderCell: CriteriaGroupCell,
    sortable: false,
  },
];
