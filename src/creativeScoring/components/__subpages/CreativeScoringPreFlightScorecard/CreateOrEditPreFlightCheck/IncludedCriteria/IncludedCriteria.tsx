import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import { ListItem } from '../../../../../../components/ReportFilters/types';
import { LoadingState } from './states/LoadingState';
import { ErrorState } from './states/ErrorState';
import { NoChannelEmptyState } from './states/NoChannelEmptyState';
import { NoCriteriaState } from './states/NoCriteriaEmptyState';
import useCriteria from '../../../../ScoringFilters/ScoringFiltersDrawerContent/TabsContent/CriteriaTabContent/useCriteria';
import { getCurrentPartnerId } from '../../../../../../redux/selectors/partner.selectors';
import { IncludedCriteriaTable } from './IncludedCriteriaTable/IncludedCriteriaTable';
import {
  INCLUDED_CRITERIA_TABLE_DEFAULT_PAGINATION,
  INCLUDED_CRITERIA_TABLE_DEFAULT_SORT_PARAMS,
} from './IncludedCriteriaTable/constants';
import { CriterionServerType } from '../../../../../types/criteriaManagement.types';
import { PaginationParams } from '../../../../../../types/pagination.types';
import { SortDirection, SortParams } from '../../../../../../types/sort.types';
import { GridSortDirection } from '@mui/x-data-grid/models/gridSortModel';
import { GridSortModel } from '@mui/x-data-grid-pro';
import Search from '../../../../../../muiCustomComponents/Search';
import { isNil } from '../../../../../../utils/typeCheckUtils';
import { NoSearchResultsEmptyState } from './states/NoSearchResultsEmptyState';

const containerSx = {
  flexDirection: 'column',
  width: '100%',
  minWidth: '600px',
  backgroundColor: 'background.deEmphasized',
  borderRadius: '12px',
  p: '24px',
  gap: '24px',
};

const preFlightCreatePageContainerSx = {
  ...containerSx,
  height: '72vh',
};

const preFlightEditModalContainerSx = {
  ...containerSx,
  height: '542px',
};

const headerSx = {
  flexDirection: 'column',
  gap: '4px',
};

const subtitleSx = {
  fontSize: '14px',
  fontWeight: 500,
  color: 'text.secondary',
};

const searchContainerSx = {
  flexDirection: 'row',
  alignItems: 'center',
  gap: '12px',
};

const searchSx = {
  height: '32px',
  width: '230px',
  backgroundColor: 'background.default',
};

const hideSearchSx = {
  display: 'none',
};

const searchIconSx = {
  height: '20px',
  width: '20px',
};

interface Props {
  selectedChannels: ListItem[];
  selectedCriteriaGroups: ListItem[];
  includedCriteria: CriterionServerType[];
  setIncludedCriteria: (criteria: CriterionServerType[]) => void;
  isPreFlightEditModal?: boolean;
}

export const IncludedCriteria = ({
  selectedChannels,
  selectedCriteriaGroups,
  includedCriteria,
  setIncludedCriteria,
  isPreFlightEditModal,
}: Props) => {
  const intl = useIntl();
  const workspaceId = useSelector(getCurrentPartnerId);

  const [searchText, setSearchText] = useState<string>('');
  const [totalSize, setTotalSize] = useState<number | null>(null);
  const [totalSizeFilteredBySearch, setTotalSizeFilteredBySearch] = useState<
    number | null
  >(null);
  const [pagination, setPagination] = useState<PaginationParams>(
    INCLUDED_CRITERIA_TABLE_DEFAULT_PAGINATION,
  );
  const [sortParams, setSortParams] = useState<SortParams>(
    INCLUDED_CRITERIA_TABLE_DEFAULT_SORT_PARAMS,
  );

  const isFirstPage = pagination.offset === 0;

  const onSearchChange = (searchTerm: string) => {
    if (searchTerm === '') {
      setTotalSizeFilteredBySearch(null);
    }
    setPagination(INCLUDED_CRITERIA_TABLE_DEFAULT_PAGINATION);
    setSearchText(searchTerm);
  };

  const filters = {
    platforms: selectedChannels.map(
      (selectedChannel) => selectedChannel.id as string,
    ),
    criteriaGroupIds: selectedCriteriaGroups.map(
      (selectedCriteriaGroup) => selectedCriteriaGroup.id as string,
    ),
  };

  const {
    data: apiResponse,
    isFetching,
    isError,
  } = useCriteria({
    enabled: Boolean(selectedChannels.length),
    disableCache: true,
    workspaceId,
    filters,
    searchText,
    page: pagination.offset,
    pageSize: pagination.perPage,
    sort: {
      sortBy: sortParams.sortBy,
      sortOrder: sortParams.sortDirection,
    },
  });

  const isFetchingFirstPage = isFetching && isFirstPage;
  const isFetchingNextPage = isFetching && !isFirstPage;

  useEffect(() => {
    if (!apiResponse?.data) {
      return;
    }

    const newCriteria = apiResponse.data;
    const updatedCriteria = isFirstPage
      ? newCriteria
      : [...includedCriteria, ...newCriteria];
    setIncludedCriteria(updatedCriteria);

    const newTotalSize = apiResponse.pagination?.totalSize;

    if (isNil(newTotalSize)) {
      return;
    }

    if (!searchText && newTotalSize !== totalSize) {
      setTotalSize(newTotalSize);
      setTotalSizeFilteredBySearch(null);
    } else if (searchText && newTotalSize !== totalSizeFilteredBySearch) {
      setTotalSizeFilteredBySearch(newTotalSize);
    }
  }, [apiResponse]);

  useEffect(() => {
    setSearchText('');
    setTotalSize(null);
    setTotalSizeFilteredBySearch(null);
    setPagination(INCLUDED_CRITERIA_TABLE_DEFAULT_PAGINATION);
  }, [selectedChannels, selectedCriteriaGroups]);

  const getNextPage = () => {
    if (
      (searchText &&
        totalSizeFilteredBySearch &&
        includedCriteria.length < totalSizeFilteredBySearch) ||
      (!searchText && totalSize && includedCriteria.length < totalSize)
    ) {
      setPagination({
        ...pagination,
        offset: pagination.offset + 1,
      });
    }
  };

  const sortModel: GridSortModel = [
    {
      field: sortParams.sortBy,
      sort: sortParams.sortDirection.toLowerCase() as GridSortDirection,
    },
  ];

  const onSortModelChange = (newSortModel: GridSortModel) => {
    setPagination(INCLUDED_CRITERIA_TABLE_DEFAULT_PAGINATION);
    const tmpSortModel = {
      field: sortParams.sortBy,
      sort:
        sortParams.sortDirection === SortDirection.ASC
          ? SortDirection.DESC
          : SortDirection.ASC,
    };

    const newSort = newSortModel[0] ? newSortModel[0] : tmpSortModel;
    const newSortParams = {
      sortBy: newSort.field,
      sortDirection: newSort.sort?.toUpperCase() as SortDirection,
    };

    setSortParams(newSortParams);
  };

  const getTitle = () => {
    const titlePrefix = intl.formatMessage({
      id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.title',
      defaultMessage: 'Included criteria',
    });

    if (isError) {
      return titlePrefix;
    }

    if (!selectedChannels?.length) {
      return `${titlePrefix} (0)`;
    }

    if (!isNil(totalSize)) {
      return `${titlePrefix} (${totalSize})`;
    }

    return titlePrefix;
  };

  const subtitle = intl.formatMessage({
    id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.subtitle',
    defaultMessage:
      'The following are the criteria your creative will be scored against. This list is based on the channel and criteria group selections you make.',
  });

  const renderContent = () => {
    if (isError) {
      return <ErrorState />;
    }

    if (isFetchingFirstPage) {
      return <LoadingState />;
    }

    if (!selectedChannels?.length) {
      return <NoChannelEmptyState />;
    }

    if (!totalSizeFilteredBySearch && searchText) {
      return <NoSearchResultsEmptyState searchText={searchText} />;
    }

    if (!includedCriteria?.length) {
      return <NoCriteriaState />;
    }

    return (
      <IncludedCriteriaTable
        criteria={includedCriteria}
        getNextPage={getNextPage}
        isNextPageLoading={isFetchingNextPage}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
      />
    );
  };

  return (
    <VidMobStack
      sx={
        isPreFlightEditModal
          ? preFlightEditModalContainerSx
          : preFlightCreatePageContainerSx
      }
    >
      <VidMobStack sx={headerSx}>
        <VidMobTypography variant="subtitle2">{getTitle()}</VidMobTypography>
        <VidMobTypography variant="caption" sx={subtitleSx}>
          {subtitle}
        </VidMobTypography>
      </VidMobStack>
      <VidMobStack sx={searchContainerSx}>
        <Search
          searchTerm={searchText}
          onSearchChange={onSearchChange}
          isDisabled
          customStyles={{
            ...searchSx,
            ...(!totalSize && hideSearchSx),
          }}
          customIconStyles={searchIconSx}
          debounceTime={500}
          placeholder={intl.formatMessage({
            id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.search.placeholder',
            defaultMessage: 'Search criteria',
          })}
        />
        {searchText && !isNil(totalSizeFilteredBySearch) && (
          <VidMobTypography variant="subtitle3">
            {`${totalSizeFilteredBySearch} ${intl.formatMessage(
              {
                id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.match',
                defaultMessage: 'matches',
              },
              {
                count: totalSizeFilteredBySearch ?? 0,
              },
            )}`}
          </VidMobTypography>
        )}
      </VidMobStack>
      {renderContent()}
    </VidMobStack>
  );
};
