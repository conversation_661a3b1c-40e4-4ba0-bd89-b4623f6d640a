import React from 'react';
import { useIntl } from 'react-intl';
import { VidMobStack } from '../../../../../../../vidMobComponentWrappers';
import {
  emptyStateContainerSx,
  emptyStateMessageSx,
  emptyStateStackSx,
  emptyStateTitleSx,
} from '../styles';
import { BlankOrErrorState } from '../../../../../../../muiCustomComponents/BlankState/BlankState';

export const NoCriteriaState = () => {
  const intl = useIntl();

  return (
    <VidMobStack sx={emptyStateContainerSx}>
      <BlankOrErrorState
        stateType="error"
        title={intl.formatMessage({
          id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.noCriteria.title',
          defaultMessage: 'No criteria available',
        })}
        message={intl.formatMessage({
          id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.noCriteria.description',
          defaultMessage:
            'No criteria available for the selected Channel(s) and Criteria group(s). Please update your selection.',
        })}
        additionalStackStyles={emptyStateStackSx}
        titleStyle={emptyStateTitleSx}
        messageStyle={emptyStateMessageSx}
      />
    </VidMobStack>
  );
};
