import React from 'react';
import { useIntl } from 'react-intl';
import { VidMobStack } from '../../../../../../../vidMobComponentWrappers';
import {
  emptyStateContainerSx,
  emptyStateMessageSx,
  emptyStateStackSx,
  emptyStateTitleSx,
} from '../styles';
import { BlankOrErrorState } from '../../../../../../../muiCustomComponents/BlankState/BlankState';

export const ErrorState = () => {
  const intl = useIntl();

  return (
    <VidMobStack sx={emptyStateContainerSx}>
      <BlankOrErrorState
        stateType="error"
        message={intl.formatMessage({
          id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.error.description',
          defaultMessage:
            'We encountered an unexpected error while loading your criteria. Please refresh the page to try again.',
        })}
        additionalStackStyles={emptyStateStackSx}
        titleStyle={emptyStateTitleSx}
        messageStyle={emptyStateMessageSx}
      />
    </VidMobStack>
  );
};
