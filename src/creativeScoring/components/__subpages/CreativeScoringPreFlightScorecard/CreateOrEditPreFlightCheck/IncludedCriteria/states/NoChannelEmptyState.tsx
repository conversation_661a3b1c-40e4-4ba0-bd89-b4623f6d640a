import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import { CriteriaIcon } from '../../../../../../../assets/vidmob-mui-icons/general';
import { emptyStateContainerSx } from '../styles';

const labelSx = {
  color: 'text.primary',
  textAlign: 'center',
  width: '256px',
};

export const NoChannelEmptyState = () => {
  const intl = useIntl();

  return (
    <VidMobStack sx={emptyStateContainerSx}>
      <CriteriaIcon />
      <VidMobTypography variant="body2" sx={labelSx}>
        {intl.formatMessage({
          id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.noChannel.description',
          defaultMessage:
            'Select a channel and optional criteria group to view included criteria',
        })}
      </VidMobTypography>
    </VidMobStack>
  );
};
