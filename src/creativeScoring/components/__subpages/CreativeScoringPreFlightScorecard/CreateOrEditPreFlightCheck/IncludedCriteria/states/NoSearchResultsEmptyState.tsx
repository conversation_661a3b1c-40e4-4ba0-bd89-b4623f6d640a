import React from 'react';
import { useIntl } from 'react-intl';
import { VidMobStack } from '../../../../../../../vidMobComponentWrappers';
import {
  emptyStateContainerSx,
  emptyStateMessageSx,
  emptyStateStackSx,
  emptyStateTitleSx,
} from '../styles';
import { BlankOrErrorState } from '../../../../../../../muiCustomComponents/BlankState/BlankState';

interface Props {
  searchText: string;
}

export const NoSearchResultsEmptyState = ({ searchText }: Props) => {
  const intl = useIntl();

  return (
    <VidMobStack sx={emptyStateContainerSx}>
      <BlankOrErrorState
        message={intl.formatMessage(
          {
            id: 'ui.creativeScoring.preFlightCheck.create.includedCriteria.noMatchingResults',
            defaultMessage: `No matching results for ${searchText}.`,
          },
          { searchText },
        )}
        additionalStackStyles={emptyStateStackSx}
        titleStyle={emptyStateTitleSx}
        messageStyle={emptyStateMessageSx}
      />
    </VidMobStack>
  );
};
