export const emptyStateContainerSx = {
  width: '100%',
  height: '100%',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '12px',
};

export const emptyStateStackSx = {
  width: '256px',
  gap: '4px',
};

export const emptyStateTitleSx = {
  mt: '8px',
};

export const emptyStateMessageSx = {
  width: '100%',
  color: 'text.primary',
  m: 0,
};

export const dataGridContainerSx = {
  height: '100%',
  overflowY: 'hidden',
};

export const dataGridSx = {
  border: 'none',
  '.MuiDataGrid-columnHeaderTitleContainer': {
    gap: '4px',
  },
  '.MuiDataGrid-cell:focus-within, .MuiDataGrid-columnHeader:focus-within': {
    outline: 'none',
  },
  '& .MuiDataGrid-row:hover': {
    backgroundColor: 'inherit',
  },
  '& .MuiDataGrid-row.Mui-hovered': {
    backgroundColor: 'inherit',
  },
  '& .MuiDataGrid-row.Mui-selected': {
    backgroundColor: 'inherit',
  },
  '& .MuiDataGrid-row.Mui-selected:hover': {
    backgroundColor: 'inherit',
  },
  '& .MuiDataGrid-row:focus, & .MuiDataGrid-row:focus-within': {
    outline: 'none',
    backgroundColor: 'inherit',
  },
};

export const cellContentSx = {
  flexDirection: 'row',
  alignItems: 'center',
  gap: '12px',
};
