import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { Fields } from './Fields/Fields';
import { IncludedCriteria } from './IncludedCriteria/IncludedCriteria';
import { ListItem } from '../../../../../components/ReportFilters/types';
import { ChannelObjectiveMap, FlattenedObjectivesObject } from '../types';
import { CriterionServerType } from '../../../../types/criteriaManagement.types';

const containerSx = {
  flexDirection: 'column',
  gap: '24px',
};

const preFlightEditModalContainerSx = {
  ...containerSx,
  height: '569px',
};

const sectionsSx = {
  height: '100%',
  flexDirection: 'row',
  gap: '32px',
};

const captionTextSx = {
  color: 'text.secondary',
};

interface Props {
  checkName: string;
  setCheckName: (name: string) => void;
  channelOptions: ListItem[];
  selectedChannels: ListItem[];
  setSelectedChannels: (channels: ListItem[]) => void;
  criteriaGroupOptions?: ListItem[];
  areCriteriaGroupOptionsLoading: boolean;
  selectedCriteriaGroups: ListItem[];
  setSelectedCriteriaGroups: (criteriaGroups: ListItem[]) => void;
  brandOptions: ListItem[];
  areBrandOptionsLoading: boolean;
  selectedBrands: ListItem[];
  setSelectedBrands: (selectedBrands: ListItem[]) => void;
  marketOptions: ListItem[];
  areMarketOptionsLoading: boolean;
  selectedMarkets: ListItem[];
  setSelectedMarkets: (selectedMarkets: ListItem[]) => void;
  channelsToObjectives: ChannelObjectiveMap;
  selectedObjectivesPerChannel: Record<string, FlattenedObjectivesObject[]>;
  setSelectedObjectivesPerChannel: (
    objectives: Record<string, FlattenedObjectivesObject[]>,
  ) => void;
  includedCriteria: CriterionServerType[];
  setIncludedCriteria: (criteria: CriterionServerType[]) => void;
  isPreFlightEditModal?: boolean;
}

export const CreateOrEditPreFlightCheck = (props: Props) => {
  const intl = useIntl();
  const { isPreFlightEditModal } = props;

  return (
    <VidMobStack
      sx={isPreFlightEditModal ? preFlightEditModalContainerSx : containerSx}
    >
      <VidMobTypography variant="caption" sx={captionTextSx}>
        {intl.formatMessage({
          id: 'ui.creativeScoring.preFlightCheck.create.caption.requiredFields',
          defaultMessage: '*Indicates a required field',
        })}
      </VidMobTypography>
      <VidMobStack sx={sectionsSx}>
        <Fields {...props} />
        <IncludedCriteria {...props} />
      </VidMobStack>
    </VidMobStack>
  );
};
