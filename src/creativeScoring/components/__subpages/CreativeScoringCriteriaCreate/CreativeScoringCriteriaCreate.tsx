import React from 'react';
import CriteriaForm from './CriteriaForm/CriteriaForm';
import CreateCriteriaHeader from './components/CreateCriteriaHeader';
import { VidMobBox } from '../../../../vidMobComponentWrappers';
import { useLoadCriteriaData } from './useLoadCriteriaData';
import { ConfigureCriteriaRuleProvider } from './ConfigureCriteriaRuleContext';
import { CriteriaGroupingProvider } from '../../criteriaManagement/CriteriaGroupingManagement/context/CriteriaGroupingProvider';

const CreativeScoringCriteriaCreate = () => {
  useLoadCriteriaData();

  return (
    <VidMobBox
      sx={{
        maxHeight: '100%',
        paddingLeft: '10px',
        paddingBottom: '20px',
        height: '100%',
      }}
    >
      <CreateCriteriaHeader />
      <VidMobBox
        sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          flexDirection: 'column',
          width: '100%',
          height: 'calc(100vh - 180px)',
        }}
      >
        <VidMobBox>
          <CriteriaForm />
        </VidMobBox>
      </VidMobBox>
    </VidMobBox>
  );
};

const CreateCriteria = () => (
  <CriteriaGroupingProvider>
    <ConfigureCriteriaRuleProvider>
      <CreativeScoringCriteriaCreate />
    </ConfigureCriteriaRuleProvider>
  </CriteriaGroupingProvider>
);

export default CreateCriteria;
