import { Template } from '../../../../../types/template.type';
import { ListItem } from '../../../../../muiCustomComponents/MultiSelectDropdown/MultiSelectDropdown';
import {
  CriteriaIsOptionalOption,
  CreateCriteriaPayload,
  MediaType,
} from '../types';
import { CustomIcon } from '../../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModal.types';

const prepareRequestPayload = ({
  applicability,
  selectedTemplate,
  selectedPlatforms,
  customParams,
  apiParameters,
  currentPartnerId,
  criteriaIsOptional,
  name,
  icon,
  isGlobalCriteria,
}: {
  applicability: string[];
  selectedTemplate: Template | null;
  selectedPlatforms: ListItem[];
  customParams: any;
  apiParameters: Record<string, any>;
  currentPartnerId: string;
  criteriaIsOptional: CriteriaIsOptionalOption;
  name: string;
  icon: CustomIcon | null;
  isGlobalCriteria: CriteriaIsOptionalOption;
}) => {
  const platformIds = selectedPlatforms.map((platform) => platform.id);

  const params: CreateCriteriaPayload = {
    partnerId: currentPartnerId,
    templateIdentifier: selectedTemplate?.identifier,
    parameters: apiParameters,
    platforms: platformIds as string[],
    applicabilityMediaTypes:
      applicability[0] === MediaType.VideoOnly
        ? [MediaType.Video]
        : applicability,
    isOptional: Boolean(criteriaIsOptional.id),
    name: name.length > 0 ? name.trim() : selectedTemplate?.defaultDisplayName,
    icon: icon,
    isGlobal: Boolean(isGlobalCriteria.id),
    ...customParams,
  };

  return params;
};

export default prepareRequestPayload;
