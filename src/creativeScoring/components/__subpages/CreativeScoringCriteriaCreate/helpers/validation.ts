import { useState, useEffect } from 'react';
import { Rule, Group } from '../types';
import {
  Dimension,
  Operator,
  PossibleValueType,
} from '../queries/customRules/types';
import { isNumber } from '../components/customRules/BetweenInputs';
import { INTL_KEYS } from '../intlKeys';
import { MOTION_IDENTIFIER } from '../components/customRules/CriteriaGroup/constants';
import validateHexCode from './constraints/color/colorValidation';
import isTimeConstraint from './constraints/color/isColorConstraint';

export type ConstraintError = {
  operator?: ValidationMessage;
  value?: ValidationMessage | ValidationMessage[];
  index?: number;
};

export type RuleValidationResult = {
  isValid: boolean;
  errors: {
    constraints: ConstraintError[];
    name?: ValidationMessage;
  };
};

export type GroupValidationResult = {
  isValid: boolean;
  rules: Record<number, RuleValidationResult>;
};

export type GroupsValidationResult = Record<number, GroupValidationResult>;

type UseValidationReturn = {
  validationResults: GroupsValidationResult;
  isGroupsSetValid: boolean;
};

export type ValidationMessage = {
  message: string;
  messageIntl: string;
};

export type Errors = Array<{ index: number } & ConstraintError>;

export const RequiredError: ValidationMessage = {
  message: 'This field cannot be empty',
  messageIntl: INTL_KEYS.validation.empty,
};

export const InvalidFormatError: ValidationMessage = {
  message: 'This field must be a number',
  messageIntl: INTL_KEYS.validation.invalidNumberValue,
};

export const GreaterThanError: ValidationMessage = {
  message: 'The first value must not be larger than the second',
  messageIntl: INTL_KEYS.validation.moreThan,
};

export const Max90SecondsError: ValidationMessage = {
  message: 'Value must be 90 seconds or less.',
  messageIntl: INTL_KEYS.validation.max90Seconds,
};

export const HexError: ValidationMessage = {
  message: 'Invalid HEX code format. Please enter 6 characters (0-9, A-F).',
  messageIntl: INTL_KEYS.validation.invalidHex,
};

export const SingleHexAllowed: ValidationMessage = {
  message: 'Only one HEX code is allowed per rule.',
  messageIntl: INTL_KEYS.validation.singleHex,
};

export const validateNumberAndRequired = (
  value: string | number | null | undefined,
  valueType: PossibleValueType,
): ValidationMessage[] => {
  const messages: ValidationMessage[] = [];

  if (!value) {
    messages.push({ ...RequiredError });
  } else if (!isNumber(value) && valueType === PossibleValueType.Number) {
    messages.push({ ...InvalidFormatError });
  }

  return messages;
};

export const validateRange = (
  valueArray: string[] | undefined,
  valueType: PossibleValueType,
): ValidationMessage[] => {
  const messages: ValidationMessage[] = [];

  if (valueArray) {
    const from = valueArray[0];
    const to = valueArray[1];

    messages.push(...validateNumberAndRequired(from, valueType));
    messages.push(...validateNumberAndRequired(to, valueType));

    if (
      from &&
      to &&
      Number(from) > Number(to) &&
      valueType === PossibleValueType.Number
    ) {
      messages.push({ ...GreaterThanError });
    }
  } else {
    messages.push({ ...RequiredError });
  }

  return messages;
};

export const validateRule = (rule: Rule): RuleValidationResult => {
  const errors: ConstraintError[] = [];

  if (!rule.constraints?.length) {
    errors.push({
      index: 0,
      operator: { ...RequiredError },
      value: { ...RequiredError },
    });
  }

  rule.constraints?.forEach((constraint, index) => {
    const constraintErrors: ConstraintError = {} as ConstraintError;

    if (!constraint.operator) {
      constraintErrors.operator = { ...RequiredError };
    }

    if (!constraint.value) {
      if (constraint.operator !== Operator.Anytime) {
        constraintErrors.value = { ...RequiredError };
      }
    } else if (constraint.value) {
      if (constraint.valueType === PossibleValueType.Number) {
        if (
          // probably we should rely on Operator rather than operator and dimension
          // it's expected if dimension is range then only between is awailable
          constraint.operator !== Operator.Between &&
          constraint.dimension !== Dimension.Range &&
          constraint.dimension !== Dimension.Element &&
          typeof constraint.value !== 'number'
        ) {
          constraintErrors.value = { ...InvalidFormatError };
        }
      }

      if (constraint.dimension === Dimension.Element) {
        if (Array.isArray(constraint.value) && constraint.value.length === 0) {
          constraintErrors.value = { ...RequiredError };
        }
      }
    }

    if (constraint.value && constraint.operator === Operator.Between) {
      const valueArray = constraint.value as string[];
      const messages = validateRange(valueArray, constraint.valueType);
      if (messages.length > 0) {
        constraintErrors.value = messages;
      }
    }

    if (constraint.value && rule.identifier === MOTION_IDENTIFIER) {
      if (Number(constraint.value) > 90) {
        constraintErrors.value = { ...Max90SecondsError };
      }
    }

    if (isTimeConstraint({ rule, constraint })) {
      const { isValid, isMultiple } = validateHexCode(
        constraint.value as string,
      );
      if (!isValid) {
        if (isMultiple) {
          constraintErrors.value = { ...SingleHexAllowed };
        } else {
          constraintErrors.value = { ...HexError };
        }
      }
    }

    if (Object.keys(constraintErrors).length > 0) {
      errors.push({ index, ...constraintErrors });
    }
  });

  return {
    isValid: errors.length === 0,
    errors: {
      constraints: errors,
      name: !rule.name ? { ...RequiredError } : undefined,
    },
  };
};

export const validateGroups = (groups: Group[]): GroupsValidationResult => {
  const results: GroupsValidationResult = {};

  groups.forEach((group) => {
    const ruleResults: Record<number, RuleValidationResult> = {};

    group.rules.forEach((rule) => {
      const ruleValidation = validateRule(rule);
      ruleResults[rule.id] = ruleValidation;
    });

    const isGroupValid = Object.values(ruleResults).every(
      (result) => result.isValid,
    );

    results[group.id] = {
      isValid: isGroupValid,
      rules: ruleResults,
    };
  });

  return results;
};

export const useValidation = (groups: Group[]): UseValidationReturn => {
  const [validationResults, setValidationResults] =
    useState<GroupsValidationResult>({});
  const [isGroupsSetValid, setIsGroupsSetValid] = useState<boolean>(true);

  useEffect(() => {
    const results: GroupsValidationResult = validateGroups(groups);
    setValidationResults(results);
    setIsGroupsSetValid(Object.values(results).every((group) => group.isValid));
  }, [groups, setValidationResults, setIsGroupsSetValid]);

  return { validationResults, isGroupsSetValid };
};
