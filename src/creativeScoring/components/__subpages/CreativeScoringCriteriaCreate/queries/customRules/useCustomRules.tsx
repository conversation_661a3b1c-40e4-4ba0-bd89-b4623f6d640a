import BffService from '../../../../../../apiServices/BffService';

import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { getCurrentPartnerId } from '../../../../../../redux/selectors/partner.selectors';
import { Operator, OperatorNameMap, RuleDto } from './types';
import { ListItem } from '../../../../../../components/ReportFilters/types';
import { getFeatureFlag } from '../../../../../../utils/featureFlagUtils';

const isMessagingApertureEnabled = getFeatureFlag('isMessagingApertureEnabled');

const endpoint = 'v2/custom-criteria/rules';

const stageMap: Record<string, boolean> = {
  stage1: true,
  stage2: true,
  aperture: isMessagingApertureEnabled,
};

const getAvailableStages = () => {
  return String(Object.keys(stageMap).filter((stage) => stageMap[stage]));
};

const getCustomRules = ({ workspaceId }: { workspaceId: string }) => {
  let url = `${endpoint}?workspaceId=${workspaceId}`;

  const stages = getAvailableStages();
  if (stages) url += `&stages=${stages}`;

  return BffService.handleBffApiGet(url);
};

export const useCustomRulesQuery = (
  workspaceId: string,
): UseQueryResult<RuleDto[], Error> => {
  return useQuery(
    ['customRules', workspaceId],
    () => getCustomRules({ workspaceId }),
    {
      enabled: !!workspaceId,
    },
  );
};

export const useCustomRules = () => {
  const currentWorkspaceId: string = useSelector(getCurrentPartnerId) as string;
  return useCustomRulesQuery(currentWorkspaceId);
};

export function getOperatorDetails(operators: Operator[]): ListItem[] {
  return operators.map((operator) => getOperatorValue(operator));
}

export const getOperatorValue = (operator: Operator): ListItem => ({
  name: OperatorNameMap[operator] || operator,
  id: operator,
});
