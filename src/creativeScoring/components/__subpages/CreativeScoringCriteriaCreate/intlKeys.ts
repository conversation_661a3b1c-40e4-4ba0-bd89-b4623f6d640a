export const INTL_KEYS = {
  APPLY_SETTINGS: {
    ADD_TO_CRITERIA_GROUP: {
      LABEL:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.addToCriteriaGroup.label',
      TOOLTIP:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.addToCriteriaGroup.tooltip',
    },
  },
  CRITERIA_GROUPS: {
    ADD_RULE_GROUP:
      'ui.creativeScoring.criteriaManagementV2.addCriteriaRuleGroup',
    ELEMENTS:
      'ui.creativeScoring.criteriaManagementV2.addCriteriaRuleGroup.elements',
  },
  CRITERIA_MODE_TOGGLER: {
    CONFIGURE_RULE:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.configureCriteriaRule',
  },
  HOVER_POPUP: {
    NEW_RULE_BASED_CRITERIA:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.newRuleBasedCriteria',
    TITLE:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.hoverPopup.title',
    DESCRIPTION:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.hoverPopup.description',
    LEARN_MORE:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.hoverPopup.learnMore',
  },
  CRITERIA_RULE_GROUP: {
    ENTER_TEXT:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholder.enterText',
    ENTER_HEX:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholder.enterHex',
    WHERE_CREATIVE:
      'ui.creativeScoring.criteriaManagementV2.configureCriteriaRule',
    HAS: 'ui.creativeScoring.criteriaManagementV2.has',
    DOES_NOT_HAVE: 'ui.creativeScoring.criteriaManagementV2.doesNotHave',
    IS: 'ui.creativeScoring.criteriaManagementV2.is',
    IS_NOT: 'ui.creativeScoring.criteriaManagementV2.isNot',
    IN: 'ui.creativeScoring.criteriaManagementV2.in',
    CUSTOM_COLOR_OPTION:
      'ui.creativeScoring.criteriaManagementV2.criteriaRule.options.customColor',
    ADD_RULE: 'ui.creativeScoring.criteriaManagementV2.createCriteria.addRule',
    HINTS: {
      COPY: 'ui.creativeScoring.criteriaManagementV2.createCriteria.hints.copy',
      DELETE:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.hints.delete',
    },
    placeholders: {
      tags: 'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholders.tags',
      category: 'ui.dropdown.placeholder.v2',
      parameter:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholders.parameter',
      operator:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholders.operator',
      specificTiming:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholders.specificTiming',
      rangeMin:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholders.rangeMin',
      rangeMax:
        'ui.creativeScoring.criteriaManagementV2.createCriteria.placeholders.rangeMax',
    },
    ruleDescriptionPrefix: 'ui.creativeScoring.criteriaManagementV2.rules.',
  },
  CRITERIA_STEPPER: {
    SELECT_CRITERIA:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.pageHeader.selectCriteria',
    APPLY_SETTINGS:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.pageHeader.applySettings',
  },
  CRITERIA_HEADER: {
    TITLE:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.pageHeader.title',
    CRITERIA_LINK:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.pageHeader.criteriaLink',
  },
  ACTION_BUTTONS: {
    CANCEL: 'ui.mui.customDialog.defaults.cancel.button.label',
    SAVE: 'button.global.save.label',
    NEXT: 'ui.creative.intelligence.customReport.modal.actionButton.next',
    BACK: 'ui.creative.intelligence.customReport.modal.actionButton.back',
  },
  CRITERIA_FORM: {
    CHECKBOX_LABEL:
      'ui.compliance.criteriaManagement.modal.footer.checkbox.label',
    TYPE_LABEL:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.typeLabel',
  },
  LOGIC_OPTIONS: {
    AND: 'ui.creativeScoring.criteriaManagementV2.logicOptions.and',
    OR: 'ui.creativeScoring.criteriaManagementV2.logicOptions.or',
  },
  PARAMETERS: {
    AND: 'ui.creativeScoring.criteriaManagementV2.logicOptions.and',
  },
  validation: {
    empty:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.validation.empty',
    moreThan:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.validation.moreThan',
    invalidNumber:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.validation.invalidNumber',
    invalidNumberValue:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.validation.invalidNumberValue',
    max90Seconds:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.validation.max90Seconds',
    invalidHex:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.validation.invalidhex',
    singleHex:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.validation.singleHex',
  },
  DIMENSIONS: {
    seconds:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.dimensions.seconds',
    aspectRatio:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.dimensions.aspectRatio',
    color:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.dimensions.color',
    visualThreshold:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.dimensions.visualThreshold',
    fileSize:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.dimensions.fileSize',
  },
  INFO_HEADER: {
    SELECT_CRITERIA:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.selectCriteria',
    APPLY_SETTINGS:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.applySettings',
    LEARN_MORE:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.learnMore',
    REQUIRED_FIELDS:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.requiredFields',
    APPLY_TO:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.applyTo',
    RULE_START:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.start',
    HAS: 'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.has',
    DOES_NOT_HAVE:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.doesNotHave',
    IS_NOT: 'ui.creativeScoring.criteriaManagementV2.isNot',
    OR: 'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.or',
    AND: 'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.and',
    BETWEEN:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.between',
    IS: 'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.is',
    AFTER:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.after',
    BEFORE:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.before',
    INCLUDING:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.including',
    SECONDS:
      'ui.creativeScoring.criteriaManagementV2.createCriteria.infoHeader.rule.seconds',
  },
  SELECT_PLACEHOLDER: 'ui.dropdown.placeholder.v2',
};
