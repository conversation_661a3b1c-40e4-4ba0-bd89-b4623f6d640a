import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getCanUserUpdateCriteria } from '../../../redux/selectors/criteriaManagement.selectors';
import criteriaManagementSlice from '../../../redux/slices/criteriaManagement.slice';

const {
  loadCriteria,
  loadBestPractices,
  loadBrandIdentifiers,
  loadAvailableFilterOptions,
  loadCriteriaTemplates,
  reset,
} = criteriaManagementSlice.actions;

export const useLoadCriteriaData = () => {
  const dispatch = useDispatch();

  const canUserUpdateCriteria = useSelector(getCanUserUpdateCriteria);

  useEffect(() => {
    dispatch(loadAvailableFilterOptions({}));
    if (canUserUpdateCriteria) {
      dispatch(loadBestPractices({}));
      dispatch(loadBrandIdentifiers({}));
      dispatch(loadCriteriaTemplates({}));
      dispatch(loadCriteria({}));
    }

    return () => {
      dispatch(reset({}));
    };
  }, []);
};
