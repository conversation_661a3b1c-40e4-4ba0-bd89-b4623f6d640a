import React from 'react';
import { useSelector } from 'react-redux';
import { VidMobBox } from '../../../../../../vidMobComponentWrappers';
import CriteriaIsOptionalSingleSelectDropdown from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/CriteriaIsOptionalSingleSelectDropdown';
import { useConfigureCriteriaRule } from '../../ConfigureCriteriaRuleContext';
import { TypeSelection } from '../../components/customRules/TypeSelection/TypeSelection';
import { getOrganizationPermissions } from '../../../../../../userManagement/redux/selectors/organization.selectors';
import GlobalCriteriaSingleSelectDropdown from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/GlobalCriteriaSingleSelectDropdown';
import DropDownCriteriaGroup from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/DropDownCriteriaGroup';
import { getFeatureFlag } from '../../../../../../utils/featureFlagUtils';
import { useCriteriaGroupsList } from '../../../../ScoringLandingFilters/ScoringLandingFilters/ListItemsHooks/useCriteriaGroupsList';

const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');

const CriteriaBuilderApplySettings = () => {
  const organizationPermissions = useSelector((state) =>
    getOrganizationPermissions(state),
  );
  const canCreateGlobalCriteria =
    organizationPermissions?.canCreateGlobalCriteria();

  const {
    formState: {
      applicability,
      criteriaIsOptional,
      setCriteriaIsOptional,
      handleSelectApplicability,
      setIsGlobalCriteria,
      isGlobalCriteria,
      selectedCriteriaGroups,
      setSelectedCriteriaGroups,
    },
  } = useConfigureCriteriaRule();

  const criteriaGroupsList = useCriteriaGroupsList();

  return (
    <VidMobBox width="500px">
      <TypeSelection
        applicability={applicability}
        handleSelectApplicability={handleSelectApplicability}
      />
      <CriteriaIsOptionalSingleSelectDropdown
        criteriaIsOptional={criteriaIsOptional}
        setCriteriaIsOptional={setCriteriaIsOptional}
        required
      />
      {canCreateGlobalCriteria && (
        <GlobalCriteriaSingleSelectDropdown
          setIsGlobalCriteria={setIsGlobalCriteria}
          isGlobalCriteria={isGlobalCriteria}
        />
      )}
      {isCriteriaGroupsEnabled && (
        <DropDownCriteriaGroup
          listItems={criteriaGroupsList}
          selectedCriteriaGroups={selectedCriteriaGroups}
          setSelectedCriteriaGroups={setSelectedCriteriaGroups}
        />
      )}
    </VidMobBox>
  );
};

export default CriteriaBuilderApplySettings;
