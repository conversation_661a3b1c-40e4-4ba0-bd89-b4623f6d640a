import React, { useMemo } from 'react';
import { useIntl } from 'react-intl';
import NameField from '../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/NameField';
import DropDownChannel from '../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/DropDownChannel';
import { Step } from '../types';
import { useConfigureCriteriaRule } from '../ConfigureCriteriaRuleContext';
import useChannels from '../helpers/useChannels';
import { INTL_KEYS } from '../intlKeys';
import { IconSelection } from '../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/IconSelection/IconSelection';
import CriteriaModeToggler from '../components/CriteriaModeToggler';
import { VidMobStack } from '../../../../../vidMobComponentWrappers';
import { VidMobBox } from '../../../../../vidMobComponentWrappers';
import { trackFeatureUsageGainsight } from '../../../../../utils/gainsight';

const CommonFields = () => {
  const intl = useIntl();
  const {
    isNewCriteriaView,
    toggleCriteriaView,
    formState: {
      name,
      setName,
      icon,
      setIcon,
      selectedPlatforms,
      setSelectedPlatforms,
      setApplicability,
      resetCustomValues,
    },
    submitted,
  } = useConfigureCriteriaRule();

  const handleToggle = (checked: boolean) => {
    if (checked) {
      trackFeatureUsageGainsight('Dynamic Criteria Rules Engine', {
        Context: 'Dynamic Criteria Rules Engine Enabled',
      });
    }
    toggleCriteriaView(checked);
  };

  const channels = useChannels();

  const emptyFieldHelperText = intl.formatMessage({
    id: INTL_KEYS.validation.empty,
  });

  const nameError =
    !name.trim() && submitted[Step.SelectCriteria] && emptyFieldHelperText;

  const selectedPlatformsError =
    !selectedPlatforms?.length &&
    submitted[Step.SelectCriteria] &&
    emptyFieldHelperText;

  const memoizedChannelDropdown = useMemo(
    () => (
      <DropDownChannel
        required
        listItems={channels}
        selectedPlatforms={selectedPlatforms}
        setSelectedPlatforms={setSelectedPlatforms}
        resetCustomValues={resetCustomValues}
        setApplicability={setApplicability}
        error={!!selectedPlatformsError}
        helperText={selectedPlatformsError || ''}
        iconSize="small"
      />
    ),
    [selectedPlatforms, selectedPlatformsError],
  );

  return (
    <VidMobBox>
      <VidMobStack
        direction="row"
        alignItems="flex-start"
        justifyContent="space-between"
      >
        <VidMobBox width="500px">
          <NameField
            name={name}
            setName={setName}
            error={!!nameError}
            helperText={nameError || ''}
            required
            fieldWidth="500px"
            iconSize="small"
          />
        </VidMobBox>
        <IconSelection
          icon={icon}
          setIcon={setIcon}
          formControlSx={{ marginTop: 0, marginLeft: 10 }}
          infoIconSize="small"
        />
      </VidMobStack>
      {memoizedChannelDropdown}
      <CriteriaModeToggler
        onToggle={handleToggle}
        isChecked={isNewCriteriaView}
      />
    </VidMobBox>
  );
};

export default CommonFields;
