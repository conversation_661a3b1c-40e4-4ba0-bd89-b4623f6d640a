import React, { useEffect } from 'react';
import { Step } from '../types';
import { useConfigureCriteriaRule } from '../ConfigureCriteriaRuleContext';
import CriteriaBuilderApplySettings from './CriteriaBuilderForm/CriteriaBuilderApplySettings';
import BasicFormSelectCriteria from './BasicForm/BasicFormSelectCriteria';
import BasicFormApplySettings from './BasicForm/BasicFormApplySettings';
import CriteriaBuilderSelectCriteria from './CriteriaBuilderForm/CriteriaBuilderSelectCriteria';
import { trackFeatureUsageGainsight } from '../../../../../utils/gainsight';

const CriteriaForm = () => {
  const {
    isNewCriteriaView,
    formState: { setApplicability, selectedTemplate },
    steps: { currentStep },
  } = useConfigureCriteriaRule();

  useEffect(() => {
    trackFeatureUsageGainsight('Dynamic Criteria Rules Engine', {
      Context: 'Criteria Builder with Dynamic Criteria Toggle',
    });
  }, []);

  useEffect(() => {
    const defaultTemplateApplicability = selectedTemplate?.applicability;
    if (
      defaultTemplateApplicability &&
      defaultTemplateApplicability !== 'ALL'
    ) {
      return setApplicability([defaultTemplateApplicability]);
    }

    return setApplicability([]);
  }, [selectedTemplate?.applicability, setApplicability]);

  return (
    <>
      {isNewCriteriaView ? (
        <>
          {currentStep === Step.SelectCriteria ? (
            <CriteriaBuilderSelectCriteria />
          ) : null}
          {currentStep === Step.ApplySettings ? (
            <CriteriaBuilderApplySettings />
          ) : null}
        </>
      ) : (
        <>
          {currentStep === Step.SelectCriteria ? (
            <BasicFormSelectCriteria />
          ) : null}
          {currentStep === Step.ApplySettings ? (
            <BasicFormApplySettings />
          ) : null}
        </>
      )}
    </>
  );
};

CriteriaForm.defaultProps = {};

export default CriteriaForm;
