import React from 'react';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import {
  VidMobCheckbox,
  VidMobFormControl,
  VidMobFormControlLabel,
  VidMobStack,
  VidMobFormHelperText,
  VidMobBox,
} from '../../../../../../vidMobComponentWrappers';
import ApplicabilityTypesSelection from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/ApplicabilityTypesSelection';
import CriteriaIsOptionalSingleSelectDropdown from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/CriteriaIsOptionalSingleSelectDropdown';
import GlobalCriteriaSingleSelectDropdown from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/GlobalCriteriaSingleSelectDropdown';
import CustomComponentTextArea from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/CustomComponentTextArea';
import { getOrganizationPermissions } from '../../../../../../userManagement/redux/selectors/organization.selectors';
import { Step } from '../../types';
import { useConfigureCriteriaRule } from '../../ConfigureCriteriaRuleContext';
import { INTL_KEYS } from '../../intlKeys';
import DropDownCriteriaGroup from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/DropDownCriteriaGroup';
import { useCriteriaGroupsList } from '../../../../ScoringLandingFilters/ScoringLandingFilters/ListItemsHooks/useCriteriaGroupsList';
import { getFeatureFlag } from '../../../../../../utils/featureFlagUtils';

const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');

const BasicFormApplySettings = () => {
  const intl = useIntl();

  const organizationPermissions = useSelector((state) =>
    getOrganizationPermissions(state),
  );

  const {
    isNewCriteriaView,
    formState: {
      selectedTemplate,
      selectedPlatforms,
      parameterValues,
      applicability,
      handleSelectApplicability,
      isCustomAudio,
      customValues,
      setCustomValues,
      criteriaIsOptional,
      setCriteriaIsOptional,
      isGlobalCriteria,
      setIsGlobalCriteria,
      isAddAnotherSelected,
      setAddAnotherSelected,
      selectedCriteriaGroups,
      setSelectedCriteriaGroups,
    },
    validation: { isTemplateCustom },
    submitted,
  } = useConfigureCriteriaRule();

  const criteriaGroupsList = useCriteriaGroupsList();
  const canCreateGlobalCriteria =
    organizationPermissions?.canCreateGlobalCriteria();

  const requiredError = intl.messages[INTL_KEYS.validation.empty];

  const applySettingsSubmitted = submitted[Step.ApplySettings];

  const customValuesNotSelected = !customValues?.isValid;
  const applicabilityNotSelected = !applicability.length;

  const customValueError =
    applySettingsSubmitted && customValuesNotSelected && requiredError;
  const applicabilityError =
    applySettingsSubmitted && applicabilityNotSelected && requiredError;

  // hide it for now
  const addAnotherDisabled = true;

  return (
    <VidMobBox width="500px">
      {selectedTemplate && isTemplateCustom() && (
        <VidMobBox mb={1}>
          <VidMobFormControl error={!!customValueError} variant="standard">
            <CustomComponentTextArea
              type={selectedTemplate?.identifier}
              textValue={customValues.values}
              onChange={({
                value,
                isValid,
              }: {
                value: string;
                isValid: boolean;
              }) => {
                setCustomValues((prevState) => ({
                  ...prevState,
                  values: value,
                  isValid,
                }));
              }}
            />
            <VidMobFormHelperText>
              {customValueError as string}
            </VidMobFormHelperText>
          </VidMobFormControl>
        </VidMobBox>
      )}
      {selectedTemplate && (
        <VidMobBox mb={8}>
          <VidMobFormControl error={!!applicabilityError} variant="standard">
            <ApplicabilityTypesSelection
              template={selectedTemplate}
              selectedPlatforms={selectedPlatforms}
              applicability={applicability}
              handleSelectApplicability={handleSelectApplicability}
              isCustomAudio={isCustomAudio}
              parameterValues={parameterValues}
              required
            />

            <VidMobFormHelperText>
              {applicabilityError as string}
            </VidMobFormHelperText>
          </VidMobFormControl>
        </VidMobBox>
      )}
      <CriteriaIsOptionalSingleSelectDropdown
        criteriaIsOptional={criteriaIsOptional}
        setCriteriaIsOptional={setCriteriaIsOptional}
        required
      />
      {canCreateGlobalCriteria && (
        <GlobalCriteriaSingleSelectDropdown
          setIsGlobalCriteria={setIsGlobalCriteria}
          isGlobalCriteria={isGlobalCriteria}
        />
      )}

      <VidMobStack
        direction="row"
        justifyContent="space-between"
        sx={{
          width: '100%',
        }}
      >
        {!isNewCriteriaView && !addAnotherDisabled ? (
          <div className="add-another-checkbox">
            <VidMobFormControl>
              <VidMobFormControlLabel
                label={
                  intl.messages[
                    'ui.compliance.criteriaManagement.modal.footer.checkbox.label'
                  ] as string
                }
                control={
                  <VidMobCheckbox
                    checked={isAddAnotherSelected}
                    onChange={() => {
                      setAddAnotherSelected(!isAddAnotherSelected);
                    }}
                  />
                }
              />
            </VidMobFormControl>
          </div>
        ) : null}
      </VidMobStack>
      {isCriteriaGroupsEnabled && (
        <DropDownCriteriaGroup
          listItems={criteriaGroupsList}
          selectedCriteriaGroups={selectedCriteriaGroups}
          setSelectedCriteriaGroups={setSelectedCriteriaGroups}
        />
      )}
    </VidMobBox>
  );
};

export default BasicFormApplySettings;
