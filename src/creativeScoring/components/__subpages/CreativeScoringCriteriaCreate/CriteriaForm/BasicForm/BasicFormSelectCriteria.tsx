import React from 'react';
import { useIntl } from 'react-intl';

import DropDownCategory from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/DropDownCategory';
import DropDownRuleWrapper from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/DropDownRule';

import { Step } from '../../types';

import { useConfigureCriteriaRule } from '../../ConfigureCriteriaRuleContext';

import { INTL_KEYS } from '../../intlKeys';
import CommonFields from '../CommonFields';
import Parameters from './Parameters';
import { VidMobBox } from '../../../../../../vidMobComponentWrappers';
import { CRITERIA_CATEGORIES } from '../../../../../constants/criteriaManagement.constants';

const BasicFormSelectCriteria = () => {
  const intl = useIntl();

  const {
    formState: {
      selectedTemplate,
      setSelectedTemplate,
      selectedPlatforms,
      setParameters,
      selectedCategory,
      setSelectedCategory,
      resetCustomValues,
    },
    submitted,
  } = useConfigureCriteriaRule();

  const requiredError = intl.messages[INTL_KEYS.validation.empty];

  const selectCriteriaSubmitted = submitted[Step.SelectCriteria];

  const ruleError =
    selectCriteriaSubmitted && !selectedTemplate && requiredError;

  return (
    <VidMobBox>
      <CommonFields />
      <VidMobBox width="500px">
        <DropDownCategory
          selectedCategory={selectedCategory || CRITERIA_CATEGORIES.ALL}
          setSelectedCategory={setSelectedCategory}
          setSelectedTemplate={setSelectedTemplate}
        />

        <DropDownRuleWrapper
          selectedPlatforms={
            selectedPlatforms.map((platform) => platform.id) as string[]
          }
          selectedCategory={selectedCategory || ''}
          selectedTemplate={selectedTemplate}
          setSelectedTemplate={setSelectedTemplate}
          setParameters={setParameters}
          resetCustomValues={resetCustomValues}
          dropdownWrapperSx={{ marginBottom: '8px' }}
          error={!!ruleError}
          helperText={(ruleError as string) || ''}
        />
        <Parameters />
      </VidMobBox>
    </VidMobBox>
  );
};

export default BasicFormSelectCriteria;
