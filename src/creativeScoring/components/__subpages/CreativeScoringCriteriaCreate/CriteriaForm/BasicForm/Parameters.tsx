import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobFormControl,
  VidMobFormHelperText,
} from '../../../../../../vidMobComponentWrappers';
import ParameterComponentSelector from '../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/ParameterComponentSelector';
import { Step } from '../../types';
import { useConfigureCriteriaRule } from '../../ConfigureCriteriaRuleContext';
import getParameters from '../../helpers/getParameters';
import { INTL_KEYS } from '../../intlKeys';
import { Template } from '../../../../../../types/template.type';

const Parameters = () => {
  const intl = useIntl();

  const {
    formState: { selectedTemplate, parameterValues, setParameters },
    validation: { isTemplateCustom, areAllRequiredParametersSet },
    submitted,
  } = useConfigureCriteriaRule();

  const requiredError = intl.messages[INTL_KEYS.validation.empty];

  const selectCriteriaSubmitted = submitted[Step.SelectCriteria];

  const parameters = getParameters(selectedTemplate as Template);

  const allParametersSet = areAllRequiredParametersSet();
  const parametersError =
    selectCriteriaSubmitted && !allParametersSet && requiredError;

  return selectedTemplate && !isTemplateCustom() ? (
    <VidMobFormControl fullWidth error={!!parametersError} variant="standard">
      <ParameterComponentSelector
        intl={intl}
        parameters={parameters}
        parameterValues={parameterValues}
        setParameters={setParameters}
        selectedTemplate={selectedTemplate}
        required
      />
      {parametersError ? (
        <VidMobFormHelperText>{parametersError as string}</VidMobFormHelperText>
      ) : null}
    </VidMobFormControl>
  ) : null;
};

export default Parameters;
