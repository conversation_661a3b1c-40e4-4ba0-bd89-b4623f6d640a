import React, { useState } from 'react';
import { useConfigureCriteriaRule } from '../ConfigureCriteriaRuleContext';
import {
  VidMobBox,
  VidMobButton,
  VidMobIconButton,
} from '../../../../../vidMobComponentWrappers';
import AddIcon from '@mui/icons-material/Add';
import CriteriaRuleGroupCard from './customRules/CriteriaGroup/CriteriaRuleGroupCard';
import { TAG_TYPE } from './customRules/CriteriaGroup/constants';
import { useIntl } from 'react-intl';
import { Group, Rule, Logic } from '../types';
import DeleteIcon from '@mui/icons-material/Delete';
import { INTL_KEYS } from '../intlKeys';
import { VidMobCircularProgress } from '../../../../../vidMobComponentWrappers';
import { useCustomRules } from '../queries/customRules/useCustomRules';
import { copyRule, defaultRule, getNewRule } from '../helpers/rules';
import { useTags } from '../useTags';
import GroupLogic from './GroupLogic';
import { textSx } from './customRules/ConstraintsSelection/components/styles';
import { Elements, useElements } from '../queries/customRules/useElements';
import { ElementTagOption } from './customRules/RuleSelection';
import extractRuleOptions from './extractRuleOptions';

const CriteriaGroups = () => {
  const intl = useIntl();
  const { groups, setGroups, setAperture } = useConfigureCriteriaRule();

  const [activeRuleId, setActiveRuleId] = useState<number | null>(null);
  const [activeGroupId, setActiveGroupId] = useState<number | null>(null);

  const onRuleInputBlur = () => {
    setActiveGroupId(null);
    setActiveRuleId(null);
  };

  const onRuleInputFocus = ({
    ruleId,
    groupId,
  }: {
    ruleId: number;
    groupId: number;
  }) => {
    setActiveGroupId(groupId);
    setActiveRuleId(ruleId);
  };

  const insertRuleAfterCurrent = () => {
    if (activeGroupId === null || activeRuleId === null) {
      return;
    }

    const groupIndex = groups.findIndex((group) => group.id === activeGroupId);
    if (groupIndex === -1) {
      return;
    }

    const group = groups[groupIndex];
    const ruleIndex = group.rules.findIndex((rule) => rule.id === activeRuleId);

    if (ruleIndex === -1) {
      return;
    }

    const newRule = getNewRule(group.rules);

    const updatedRules = [
      ...group.rules.slice(0, ruleIndex + 1),
      newRule,
      ...group.rules.slice(ruleIndex + 1),
    ];

    setGroups(
      groups.map((g) =>
        g.id === activeGroupId ? { ...g, rules: updatedRules } : g,
      ),
    );
  };

  const handleKeydownOnGroup: React.KeyboardEventHandler<HTMLInputElement> = (
    event,
  ) => {
    if (
      event.key === 'Tab' &&
      activeGroupId !== null &&
      activeRuleId !== null
    ) {
      event.preventDefault();
      insertRuleAfterCurrent();
    }
  };

  const handleAddGroup = () => {
    const newGroup = {
      id: groups.length + 1,
      rules: [defaultRule],
      logic: Logic.Or,
    };
    setGroups([...groups, newGroup]);
  };

  const handleDeleteGroup = (groupId: number) => {
    const updatedGroups = groups.filter((group) => group.id !== groupId);
    setGroups(updatedGroups);
  };

  const handleAddRule = (groupId: number) => {
    const updatedGroups = groups.map((group) =>
      group.id === groupId
        ? {
            ...group,
            rules: [...group.rules, getNewRule(group.rules)],
          }
        : group,
    );
    setGroups(updatedGroups);
  };

  const handleCopyRule = (groupId: number, ruleId: number) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const updatedRules = copyRule(group.rules, ruleId);
        return {
          ...group,
          rules: updatedRules,
        };
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const handleDeleteRule = (groupId: number, ruleId: number) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const filteredRules = group.rules.filter((rule) => rule.id !== ruleId);

        return {
          ...group,
          rules: filteredRules.length > 0 ? filteredRules : [defaultRule],
        };
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const handleChangeRule = (groupId: number, ruleId: number, data: Rule) => {
    const updatedGroups = groups.map((group) =>
      group.id === groupId
        ? {
            ...group,
            rules: group.rules.map((rule) =>
              rule.id === ruleId ? { ...rule, ...data } : rule,
            ),
          }
        : group,
    );

    setGroups(updatedGroups);
  };

  const onRuleLogicChange = (groupId: number, logic: Logic) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const updatedRules = group.rules.map((rule) => ({
          ...rule,
          logic,
        }));

        return { ...group, rules: updatedRules };
      }

      return group;
    });

    setGroups(updatedGroups);
  };

  const onGroupLogicChange = ({ value }: { value: Logic }) => {
    const updatedGroups = groups.map((group) => ({
      ...group,
      logic: value as unknown as Logic,
    }));
    setGroups(updatedGroups);
  };

  const { data, isLoading } = useCustomRules();
  const customRules = data;

  const { tags, isLoading: tagsLoading } = useTags();

  const { data: elements, isLoading: elementsLoading } = useElements({
    onSuccess: (data: Elements) => {
      if (data?.aperture) {
        setAperture(data.aperture);
      }
    },
  });

  const [ruleSearchText, setRuleSearchText] = useState('');

  const onRuleSearchChange = (value: string) => {
    setRuleSearchText(value);
  };

  const convertDataToOptions = (
    data: Elements | undefined,
  ): ElementTagOption[] => {
    return (
      data?.category?.flatMap((category) => {
        const options =
          category.values?.map((value) => ({
            name: intl.formatMessage(
              {
                id: INTL_KEYS.CRITERIA_GROUPS.ELEMENTS,
                defaultMessage: 'Elements > {category} > {value}',
              },
              { category: category.name, value },
            ),
            id: value,
            parentId: category.name,
            type: TAG_TYPE,
          })) || [];

        if (!category.values || category.values.length === 0) {
          options.push({
            name: intl.formatMessage(
              {
                id: INTL_KEYS.CRITERIA_GROUPS.ELEMENTS,
                defaultMessage: 'Elements > {category}',
              },
              { category: category.name },
            ),
            id: '',
            parentId: category.name,
            type: TAG_TYPE,
          });
        }

        return options;
      }) || []
    );
  };

  const elementTagOptions =
    ruleSearchText?.length > 0 ? convertDataToOptions(elements) : [];

  const ruleOptions = extractRuleOptions(customRules || []);
  const ruleOptionsToPreselect = ruleSearchText?.length > 0 ? ruleOptions : [];

  if (isLoading) {
    return (
      <VidMobBox
        display={'flex'}
        alignItems={'center'}
        justifyContent={'center'}
      >
        <VidMobCircularProgress size={40} color="primary" />
      </VidMobBox>
    );
  }

  return (
    <>
      {groups.map((group: Group, index: number) => (
        <React.Fragment key={group.id}>
          <GroupLogic
            value={group.logic}
            index={index}
            onChange={onGroupLogicChange}
          />
          <VidMobBox
            display="flex"
            alignItems="center"
            justifyContent="flex-start"
            gap={2}
            onKeyDown={handleKeydownOnGroup}
          >
            {Array.isArray(customRules) && customRules?.length ? (
              <CriteriaRuleGroupCard
                onRuleInputBlur={onRuleInputBlur}
                onRuleInputFocus={onRuleInputFocus}
                group={group}
                onRuleAdd={handleAddRule}
                onRuleDelete={handleDeleteRule}
                onRuleChange={handleChangeRule}
                onRuleCopy={handleCopyRule}
                onRuleSearchChange={onRuleSearchChange}
                customRules={customRules || []}
                onRuleLogicChange={onRuleLogicChange}
                tags={tags}
                tagsLoading={tagsLoading}
                elements={elements?.category || []}
                elementTagOptions={elementTagOptions}
                ruleOptionsToPreselect={ruleOptionsToPreselect}
                elementsLoading={elementsLoading}
              />
            ) : null}
            {index !== 0 ? (
              <VidMobIconButton onClick={() => handleDeleteGroup(group.id)}>
                <DeleteIcon />
              </VidMobIconButton>
            ) : null}
          </VidMobBox>
        </React.Fragment>
      ))}

      <VidMobBox>
        <VidMobButton
          variant="text"
          startIcon={<AddIcon />}
          onClick={handleAddGroup}
          sx={textSx}
        >
          {intl.formatMessage({
            id: INTL_KEYS.CRITERIA_GROUPS.ADD_RULE_GROUP,
            defaultMessage: 'Add criteria rule group',
          })}
        </VidMobButton>
      </VidMobBox>
    </>
  );
};
export default CriteriaGroups;
