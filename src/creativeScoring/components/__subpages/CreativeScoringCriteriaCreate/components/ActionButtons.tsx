import React from 'react';
import { useIntl } from 'react-intl';
import { VidMobStack } from '../../../../../vidMobComponentWrappers';
import { CreateCriteriaPayload, MediaType, Step, BaseError } from '../types';
import { VidMobButton } from '../../../../../vidMobComponentWrappers';

import { recordComplianceCriteriaCreation } from '../../../../featureServices/recordUserActions';
import processApiParameters from '../helpers/processApiParameters';
import generateCustomParams from '../helpers/generateCustomParams';
import prepareRequestPayload from '../helpers/prepareRequestPayload';
import { useDispatch, useSelector } from 'react-redux';

import { getSelectedPlatform } from '../../../../redux/selectors/complianceCriteriaManagement.selectors';
import { getCurrentPartnerId } from '../../../../../redux/selectors/partner.selectors';
import { useConfigureCriteriaRule } from '../ConfigureCriteriaRuleContext';
import { INTL_KEYS } from '../intlKeys';

import { clearCriteriaMgmtPlatform } from '../../../../redux/actions/criteriaManagement.actions';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import useCreateCriteria from '../useCreateCriteria';

import history from '../../../../../routing/history';
import { useAttachIconsToCriteria } from '../../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModalHooks';
import { generateToastMessage } from '../helpers/generateCriteriaErrorMessage';
import prepareConfigurableRulesApiPayload from '../helpers/prepareConfigurableRulesApiPayload';
import { trackCustomEventGainsight } from '../../../../../utils/gainsight';

function extractIdentifiers(obj: unknown): string[] {
  if (obj === null || typeof obj !== 'object') {
    return [];
  }

  // If it's an array, recurse into each element
  if (Array.isArray(obj)) {
    return obj.flatMap(extractIdentifiers);
  }

  // It's a plain object: scan its entries
  const ids: string[] = [];
  for (const [key, value] of Object.entries(obj)) {
    if (key === 'identifier' && typeof value === 'string') {
      ids.push(value);
    } else {
      ids.push(...extractIdentifiers(value));
    }
  }
  return ids;
}

const ActionButtons = () => {
  const intl = useIntl();

  const handleCancel = () => {
    history.push('/creativeIntelligence/creative-scoring/criteria-management');
  };
  const showToastAlert = useToastAlert();
  const { mutate: attachIconsToCriteria } = useAttachIconsToCriteria();

  const selectedPlatform = useSelector(getSelectedPlatform);
  const { mutate, isLoading: isUpdateCriteriaLoading } = useCreateCriteria();

  const trackEvent = (params: CreateCriteriaPayload) => {
    try {
      const categorySelections = extractIdentifiers(params.parameters);
      trackCustomEventGainsight('Custom guideline builder', {
        templateIdentifier: params.templateIdentifier,
        name: params.name,
        categorySelections: categorySelections.join(', '),
      });
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  };

  const handleCreateCriteria = (params: CreateCriteriaPayload) => {
    const { icon } = params;

    trackEvent(params);

    return mutate(params, {
      onSuccess: (criteriaResponse) => {
        const criteriaId = criteriaResponse?.[0]?.id;
        if (icon?.key) {
          attachIconsToCriteria({
            iconCriteriaMappings: [{ criteriaId: criteriaId, key: icon.key }],
          });
        }

        if (isAddAnotherSelected) {
          clearCriteriaStates();
        }

        recordComplianceCriteriaCreation(selectedPlatform, {
          selectedTemplate,
        });

        history.push(
          '/creativeIntelligence/creative-scoring/criteria-management',
        );

        showToastAlert(
          'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.create.success',
          'success',
        );
      },
      onError(error: unknown) {
        const typedError = error as BaseError;
        const errorText = generateToastMessage(
          typedError.baseError?.response?.responseErrorMessage ||
            typedError.message,
          intl,
        ) as string;

        showToastAlert(errorText, 'error');
      },
    });
  };

  const {
    formState: {
      name,
      icon,
      selectedTemplate,
      selectedPlatforms,
      setSelectedPlatforms,
      parameterValues,
      applicability,
      isCustomAudio,
      customValues,
      criteriaIsOptional,
      isGlobalCriteria,
      isAddAnotherSelected,
      setAddAnotherSelected,
      clearCriteriaStates,
      selectedCriteriaGroups,
    },
    markAsSubmitted,
    isNewCriteriaView,
    groups,
    validation: { isTemplateCustom, canSubmitCriteria },
    steps: { currentStep, goToApplySettings, goToSelectCriteria },
    aperture,
  } = useConfigureCriteriaRule();

  const dispatch = useDispatch();

  const currentPartnerId = useSelector(getCurrentPartnerId);

  const handleCancelClick = () => {
    setAddAnotherSelected(false);
    setSelectedPlatforms([]);
    clearCriteriaStates();
    dispatch(clearCriteriaMgmtPlatform());
    handleCancel();
  };

  const oldFormValid =
    !isNewCriteriaView && canSubmitCriteria() && !isUpdateCriteriaLoading;

  const handleCreatePartnerCriteria = async () => {
    markAsSubmitted(Step.ApplySettings);
    if (!oldFormValid) {
      return;
    }
    const apiParameters = processApiParameters(parameterValues);
    const customParams = generateCustomParams(
      applicability,
      customValues,
      isCustomAudio,
      isTemplateCustom,
    );

    const params = prepareRequestPayload({
      applicability,
      selectedTemplate,
      selectedPlatforms,
      customParams: {
        ...customParams,
        criteriaGroupIds: selectedCriteriaGroups,
      },
      apiParameters,
      currentPartnerId,
      criteriaIsOptional,
      name,
      icon,
      isGlobalCriteria,
    });

    await handleCreateCriteria(params);
  };

  const handleCreateConfigurableCriteria = async () => {
    markAsSubmitted(Step.ApplySettings);
    if (!applicability.length) {
      return;
    }

    const params: CreateCriteriaPayload = {
      partnerId: currentPartnerId,
      parameters: prepareConfigurableRulesApiPayload(groups, aperture),
      templateIdentifier: 'CUSTOM_CRITERIA',
      platforms: selectedPlatforms.map(
        (platform) => platform.id,
      ) as unknown as string[],

      applicabilityMediaTypes:
        applicability[0] === MediaType.VideoOnly
          ? [MediaType.Video]
          : applicability,
      isOptional: Boolean(criteriaIsOptional.id),

      name,
      icon,
      isGlobal: Boolean(isGlobalCriteria.id),
      ...(selectedCriteriaGroups.length && {
        criteriaGroupIds: selectedCriteriaGroups,
      }),
    };

    await handleCreateCriteria(params);
  };

  const handleNextButtonClick = () => {
    if (currentStep === Step.SelectCriteria) {
      goToApplySettings();
    } else {
      if (isNewCriteriaView) {
        handleCreateConfigurableCriteria();
      } else {
        handleCreatePartnerCriteria();
      }
    }
  };

  const nextButtonLabel =
    currentStep === Step.SelectCriteria
      ? intl.messages[INTL_KEYS.ACTION_BUTTONS.NEXT]
      : intl.messages[INTL_KEYS.ACTION_BUTTONS.SAVE];

  // also will need groups selected for criteria builder once we start saving it
  const applySettingsDisabled =
    currentStep === Step.ApplySettings && !applicability?.length;

  return (
    <VidMobStack direction="row" gap={3}>
      <VidMobButton
        onClick={handleCancelClick}
        variant="text"
        color="primary"
        disabled={isUpdateCriteriaLoading}
      >
        {
          intl.messages[
            'ui.mui.customDialog.defaults.cancel.button.label'
          ] as string
        }
      </VidMobButton>
      {currentStep !== Step.SelectCriteria && (
        <VidMobButton
          onClick={goToSelectCriteria}
          variant="contained"
          color="secondary"
          sx={{ fontWeight: 600 }}
          disabled={isUpdateCriteriaLoading}
        >
          {intl.messages[INTL_KEYS.ACTION_BUTTONS.BACK] as string}
        </VidMobButton>
      )}

      <VidMobButton
        disabled={isUpdateCriteriaLoading || applySettingsDisabled}
        onClick={handleNextButtonClick}
        variant="contained"
        color="primary"
      >
        {nextButtonLabel as string}
      </VidMobButton>
    </VidMobStack>
  );
};

export default ActionButtons;
