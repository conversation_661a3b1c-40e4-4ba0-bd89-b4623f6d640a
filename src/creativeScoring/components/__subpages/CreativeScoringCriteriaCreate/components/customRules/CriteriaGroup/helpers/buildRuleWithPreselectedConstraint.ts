import {
  Constraint,
  Dimension,
  ExtendedConstraint,
  RuleDto,
} from '../../../../queries/customRules/types';
import { Rule } from '../../../../types';
import getConstaintsFromSelectedRuleOption from '../../RuleSelector/getConstaintsFromSelectedRuleOption';
import getDefaultConstraints from './getDefaultConstraints';

const buildRuleWithPreselectedConstraint = ({
  parentId,
  id,
  type,
  rule,
  customRules,
}: {
  parentId?: string | number;
  id: string | number;
  type: string;
  rule: Rule;
  customRules: RuleDto[];
}): Rule | null => {
  const selectedRuleOption = customRules.find(
    (customRule) => customRule.identifier === parentId,
  );
  if (!selectedRuleOption) {
    return null;
  }

  const identifier = selectedRuleOption?.identifier;

  const constraints: Constraint[] =
    getConstaintsFromSelectedRuleOption(selectedRuleOption);

  const defaultConstraints = getDefaultConstraints(constraints);

  const selectedConstraint = defaultConstraints.find(
    (c) => c.dimension !== Dimension.Time,
  );

  const preselectedElementPresentConstraint = {
    ...selectedConstraint,
    elementGroup: selectedRuleOption.name,
    value: id || undefined,
    tagType: type,
  } as ExtendedConstraint;

  return {
    ...rule,
    name: selectedRuleOption?.name?.toString(),
    identifier,
    constraints: defaultConstraints.map((c) =>
      c.dimension !== Dimension.Time
        ? { ...preselectedElementPresentConstraint }
        : c,
    ),
  };
};

export default buildRuleWithPreselectedConstraint;
