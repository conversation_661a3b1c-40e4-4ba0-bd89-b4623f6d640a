import React from 'react';
import AddIcon from '@mui/icons-material/Add';
import { useIntl } from 'react-intl';
import {
  VidMobCard,
  VidMobBox,
  VidMobTypography,
  VidMobButton,
  VidMobTextField,
} from '../../../../../../../vidMobComponentWrappers';
import { Rule, Group, Logic, Tag } from '../../../types';
import { INTL_KEYS } from '../../../intlKeys';
import {
  Constraint,
  Dimension,
  ExtendedConstraint,
  RuleDto,
} from '../../../queries/customRules/types';
import LogicSelect, { ReadOnlyLogic } from '../LogicSelect';
import { textSx } from '../ConstraintsSelection/components/styles';
import HasHasNotSelection from './HasHasNotSelection';
import CopyDeleteButtons from './CopyDeleteButtons';
import { getConstaintsFromSelectedRuleOption } from '../RuleSelector/getConstaintsFromSelectedRuleOption';
import RuleSelectorComponent from '../RuleSelector/RuleSelectorComponent';
import { ElementCategory } from '../../../queries/customRules/useElements';
import { ElementTagOption } from '../RuleSelection';
import { ListItem } from '../../../../../../../components/ReportFilters/types';
import { OnRuleInputBlur, OnRuleInputFocus } from './ruleInputHandlers.types';
import getElementGroupOptions from './helpers/getElementGroupOptions';

import {
  TAG_TYPE,
  ELEMENT_PRESENT_IDENTIFIER,
  ELEMENT_GROUP,
  RULE_OPTION,
} from './constants';
import getElementPresentRuleOption from './helpers/getElementPresentRuleOption';
import getOptionsForRuleSelection from './helpers/getOptionsForRuleSelection';
import getDefaultConstraints from './helpers/getDefaultConstraints';
import RuleWrapper from './RuleWrapper';
import buildRuleWithPreselectedConstraint from './helpers/buildRuleWithPreselectedConstraint';
import extractIdFromRuleOption from './helpers/extractIdFromRuleOption';

const CriteriaRuleGroupCard = ({
  group,
  onRuleAdd,
  onRuleChange,
  onRuleDelete,
  onRuleCopy,
  onRuleLogicChange,
  onRuleSearchChange,
  onRuleInputBlur,
  onRuleInputFocus,
  customRules,
  tags,
  tagsLoading,
  elements,
  elementTagOptions,
  elementsLoading,
  ruleOptionsToPreselect,
}: {
  group: Group;
  onRuleAdd: (groupId: number) => void;
  onRuleCopy: (groupId: number, ruleId: number) => void;
  onRuleChange: (groupId: number, ruleId: number, data: Rule) => void;
  onRuleDelete: (groupId: number, ruleId: number) => void;
  onRuleLogicChange: (groupId: number, logic: Logic) => void;
  customRules: RuleDto[];
  tags: Tag[];
  tagsLoading: boolean;
  elements: ElementCategory[];
  elementTagOptions?: ElementTagOption[];
  elementsLoading: boolean;
  onRuleSearchChange: (value: string) => void;
  onRuleInputFocus?: OnRuleInputFocus;
  onRuleInputBlur?: OnRuleInputBlur;
  ruleOptionsToPreselect: ElementTagOption[];
}) => {
  const intl = useIntl();

  const elementGroupOptions = getElementGroupOptions(elements);

  const handleAddRule = () => {
    onRuleAdd(group.id);
  };

  const handleCopyRule = (ruleId: number) => {
    onRuleCopy(group.id, ruleId);
  };

  const handleDeleteRule = (ruleId: number) => {
    onRuleDelete(group.id, ruleId);
  };

  const onRulePropsChange = (rule: Rule) => {
    onRuleChange(group.id, rule.id, rule);
  };

  const onRuleConstraintChange = ({
    rule,
    constraintIndex,
    constraint,
  }: {
    rule: Rule;
    constraint: ExtendedConstraint;
    constraintIndex: number;
  }) => {
    const updatedConstraints =
      rule.constraints?.map((currrentConstraint, index) =>
        index === constraintIndex ? constraint : currrentConstraint,
      ) || [];

    onRulePropsChange({
      ...rule,
      constraints: updatedConstraints,
    });
  };

  const onRuleNameChange = ({ name, rule }: { name: string; rule: Rule }) => {
    const selectedRuleOption = customRules.find((opt) => opt.name === name);
    const constraints: Constraint[] =
      getConstaintsFromSelectedRuleOption(selectedRuleOption);
    const identifier = selectedRuleOption?.identifier;
    // need to set default constraints to rule
    const defaultConstraints = getDefaultConstraints(constraints);

    onRulePropsChange({
      ...rule,
      name,
      identifier,
      constraints: defaultConstraints,
    });
  };

  const onRuleOptionSelectedFromCategoryDropdown = ({
    id,
    type,
    parentId,
    rule,
  }: {
    id: string | number;
    type: string;
    parentId?: string | number;
    rule: Rule;
  }) => {
    const ruleObjectToSave = buildRuleWithPreselectedConstraint({
      customRules,
      parentId,
      id,
      type,
      rule,
    });
    if (ruleObjectToSave) {
      onRulePropsChange(ruleObjectToSave);
      onRuleSearchChange('');
    }
  };

  const onElementTagSelectedFromCategoryDropdown = ({
    nameOption: { id, parentId },
    rule,
  }: {
    rule: Rule;
    nameOption: ListItem;
  }) => {
    const selectedRuleOption = getElementPresentRuleOption(customRules);
    const constraints: Constraint[] =
      getConstaintsFromSelectedRuleOption(selectedRuleOption);
    const identifier = selectedRuleOption?.identifier;

    const defaultConstraints = getDefaultConstraints(constraints);

    const elementPresentConstraint = defaultConstraints.find(
      (constraint: ExtendedConstraint) =>
        constraint.dimension === Dimension.Element,
    );

    const tagType = elements.find(
      (category) =>
        typeof parentId === 'string' &&
        category.name.toLowerCase() === parentId?.toLocaleLowerCase(),
    )?.tagType;

    const preselectedElementPresentConstraint = {
      ...elementPresentConstraint,
      elementGroup: parentId,
      value: id ? [id] : undefined,
      tagType,
    } as ExtendedConstraint;

    onRulePropsChange({
      ...rule,
      name: parentId?.toString(),
      identifier,
      constraints: defaultConstraints.map((c) =>
        c.dimension === Dimension.Element
          ? { ...preselectedElementPresentConstraint }
          : c,
      ),
    });
    onRuleSearchChange('');
  };

  const onElementGroupSelectedFromCategoryDropdown = ({
    rule,
    name,
  }: {
    rule: Rule;
    name: string;
  }) => {
    const selectedRuleOption = getElementPresentRuleOption(customRules);
    const constraints: Constraint[] =
      getConstaintsFromSelectedRuleOption(selectedRuleOption);

    const defaultConstraints = getDefaultConstraints(constraints);

    const elementPresentConstraint = defaultConstraints.find(
      (constraint: ExtendedConstraint) =>
        constraint.dimension === Dimension.Element,
    );

    const tagType = elements.find(
      (category: any) =>
        category.name.toLowerCase() === name?.toLocaleLowerCase(),
    )?.tagType;

    const preselectedElementPresentConstraint = {
      ...elementPresentConstraint,
      elementGroup: name,
      value: undefined,
      tagType,
    } as ExtendedConstraint;

    onRulePropsChange({
      ...rule,
      name: name,
      identifier: ELEMENT_PRESENT_IDENTIFIER,
      constraints: defaultConstraints.map((c) =>
        c.dimension === Dimension.Element
          ? { ...preselectedElementPresentConstraint }
          : c,
      ),
    });
    onRuleSearchChange('');
  };

  const onRuleCategoryOptionChange = ({
    nameOption: { name, id, type, parentId },
    rule,
  }: {
    nameOption: ListItem;
    rule: Rule;
  }) => {
    // Extracts the ID part from a rule option string in the format "identifier:id"
    // to avoid duplication of ids
    if (type === RULE_OPTION) {
      onRuleOptionSelectedFromCategoryDropdown({
        id: extractIdFromRuleOption(id as string),
        type,
        parentId,
        rule,
      });
    } else if (type === TAG_TYPE) {
      const option = {
        name,
        id,
        type,
        parentId,
      };
      onElementTagSelectedFromCategoryDropdown({ nameOption: option, rule });
    } else if (type === ELEMENT_GROUP) {
      onElementGroupSelectedFromCategoryDropdown({ rule, name });
    } else {
      onRuleNameChange({ name, rule });
    }
  };

  const getConstraintsOutOfSelectedRuleOption = (
    selectedRuleOption: RuleDto,
  ) => {
    const constraints: Constraint[] =
      getConstaintsFromSelectedRuleOption(selectedRuleOption);

    return constraints;
  };

  const getConstraints = (name?: string) => {
    const selectedRuleOption = customRules.find((opt) => opt.name === name);
    if (selectedRuleOption) {
      return getConstraintsOutOfSelectedRuleOption(selectedRuleOption);
    }

    const selectedElementGroupOption = elementGroupOptions.find(
      (opt) => opt.name === name,
    );

    if (selectedElementGroupOption) {
      return getElementPresentRuleOption(customRules)?.constraints || [];
    }
    return [];
  };

  const getSelectedRuleOption = (name?: string): boolean => {
    return !!(
      customRules.find((opt) => opt.name === name) ||
      elementGroupOptions.find((opt) => opt.name === name)
    );
  };

  const nameOptions = customRules
    .filter(
      (customRule) => customRule.identifier !== ELEMENT_PRESENT_IDENTIFIER,
    )
    .reduce<{ group: string; name: string }[]>((acc, rule) => {
      acc.push({ group: rule.category, name: rule.name });
      return acc;
    }, []);

  const onInputFocus = (ruleId: number) => {
    onRuleInputFocus?.({ ruleId, groupId: group.id });
  };

  const onInputBlur = () => {
    onRuleInputBlur?.();
  };

  const ruleSelectionOptions = getOptionsForRuleSelection({
    nameOptions,
    elementGroupOptions,
    elementTagOptions,
    ruleOptions: ruleOptionsToPreselect,
  });

  return (
    <VidMobCard
      variant="outlined"
      sx={{
        padding: '16px',
        margin: '16px 0',
        minWidth: 'fit-content',
        display: 'inline-block',
        borderRadius: '6px',
        border: '1px solid var(--divider, #BDBDBD)',
        background: '#FFF',
      }}
    >
      <VidMobBox>
        {group.rules.map((rule: Rule, index: number) => (
          <React.Fragment key={rule.id}>
            <RuleWrapper rule={rule} groupId={group.id}>
              <VidMobBox
                sx={{
                  display: 'flex',
                  gap: '8px',
                  alignItems: 'flex-start',
                  boxSizing: 'border-box',
                  flexWrap: 'nowrap',
                  maxHeight: '32px',
                }}
              >
                {index === 0 ? (
                  <VidMobBox
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      height: '30px',
                    }}
                  >
                    <VidMobTypography variant="subtitle1" sx={textSx}>
                      {intl.formatMessage({
                        id: INTL_KEYS.CRITERIA_RULE_GROUP.WHERE_CREATIVE,
                        defaultMessage: 'Where the creative',
                      })}
                    </VidMobTypography>
                  </VidMobBox>
                ) : index === 1 ? (
                  <LogicSelect
                    onChange={(value) => {
                      onRuleLogicChange(group.id, value.id as Logic);
                    }}
                    value={rule.logic}
                  />
                ) : (
                  <VidMobBox
                    sx={{
                      height: '32px',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <VidMobTypography variant="subtitle1">
                      <ReadOnlyLogic logic={rule?.logic} />
                    </VidMobTypography>
                  </VidMobBox>
                )}
                {Array.isArray(customRules) && customRules?.length ? (
                  <RuleSelectorComponent
                    customRules={customRules}
                    onRuleInputFocus={() => onInputFocus(rule.id)}
                    onRuleInputBlur={() => onInputBlur()}
                    onRuleSearchChange={onRuleSearchChange}
                    onRuleConstraintChange={onRuleConstraintChange}
                    onRuleCategoryOptionChange={onRuleCategoryOptionChange}
                    rule={rule}
                    onRulePropsChange={onRulePropsChange}
                    groupId={group.id}
                    tags={tags}
                    tagsLoading={tagsLoading}
                    getConstraints={getConstraints}
                    getSelectedRuleOption={getSelectedRuleOption}
                    options={ruleSelectionOptions}
                    elements={elements}
                    elementsLoading={elementsLoading}
                    operatorElement={(isAttributeBasedCriteria) =>
                      isAttributeBasedCriteria ? (
                        <VidMobBox
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: isAttributeBasedCriteria ? '0px' : '8px',
                          }}
                        >
                          <HasHasNotSelection
                            has={rule.has}
                            rule={rule}
                            onChange={(has) => {
                              onRuleChange(group.id, rule.id, {
                                ...rule,
                                has,
                              });
                            }}
                            onRuleInputFocus={() => onInputFocus(rule.id)}
                            onRuleInputBlur={() => onInputBlur()}
                          />
                        </VidMobBox>
                      ) : null
                    }
                  />
                ) : null}
                {rule.name === 'Custom' ? <VidMobTextField /> : null}
                <CopyDeleteButtons
                  onCopy={() => handleCopyRule(rule.id)}
                  onDelete={() => handleDeleteRule(rule.id)}
                />
              </VidMobBox>
            </RuleWrapper>
          </React.Fragment>
        ))}
      </VidMobBox>

      <VidMobBox>
        <VidMobButton
          variant="text"
          startIcon={<AddIcon />}
          onClick={handleAddRule}
          sx={textSx}
        >
          {intl.formatMessage({
            id: INTL_KEYS.CRITERIA_RULE_GROUP.ADD_RULE,
            defaultMessage: 'Add criteria rule',
          })}
        </VidMobButton>
      </VidMobBox>
    </VidMobCard>
  );
};

export default CriteriaRuleGroupCard;
