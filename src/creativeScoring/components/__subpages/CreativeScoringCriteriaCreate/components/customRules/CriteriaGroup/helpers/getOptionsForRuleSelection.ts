import { ListItem } from '../../../../../../../../components/ReportFilters/types';
import { ElementTagOption } from '../../RuleSelection';
import combineCategoryNamesAndElementTagGroups from './combineCategoryNamesAndElementTagGroups';
import transformNameOptionsToNameAndDivider from './transformNameOptionsToNameAndDivider';

const getOptionsForRuleSelection = ({
  nameOptions,
  elementTagOptions,
  elementGroupOptions,
  ruleOptions,
}: {
  nameOptions: Array<{ group: string; name: string }>;
  elementTagOptions?: ElementTagOption[];
  elementGroupOptions?: ListItem[];
  ruleOptions: ListItem[];
}): ListItem[] => {
  const valueOptions = combineCategoryNamesAndElementTagGroups(
    transformNameOptionsToNameAndDivider(nameOptions),
    elementGroupOptions?.length ? elementGroupOptions : [],
  );

  let optionsToPass = elementTagOptions?.length
    ? valueOptions.concat(elementTagOptions)
    : valueOptions;

  if (ruleOptions?.length) {
    optionsToPass = optionsToPass.concat(ruleOptions);
  }

  return optionsToPass;
};

export default getOptionsForRuleSelection;
