export const TAG_TYPE = 'tag';
export const RULE_OPTION = 'RuleOption';
export const ELEMENT_GROUP = 'elementGroup';
export const MOTION = 'ContainsMotion';
export const ELEMENT_PRESENT_IDENTIFIER = 'ElementPresent';
export const MOTION_IDENTIFIER = 'ContainsMotion';
export const CUSTOM_COLOR_IDENTIFIER = 'CustomColorPresent';

export const ATTRIBUTE_BASED_CRITERIA = [
  'FitsDimensions',
  'AspectRatio',
  'VideoDuration',
  'FileSize',
  'MaxWordLimitPerFame',
  'BenefitMessaging',
  'CtaTypeMessaging',
  'ProductionType',
  'ProductionLevel',
  'SoundType',
  'StrategicTone',
  'StoryTypeMessaging',
];

export const RULE_WITH_INDEFINITE_ARTICLE = [
  'BrandAudioPresent',
  'BrandTextPresent',
  'BrandLogoPresent',
  'HumanPresence',
  'CallToActionPresent',
  'PromotionalOfferPresent',
];
