import React from 'react';
import {
  VidMobCheckbox,
  VidMobFormControlLabel,
  VidMobBox,
} from '../../../../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import CreativeScoringConstants from '../../../../../../constants/creativeScoring.constants';
import SectionLabel from '../../../../../criteriaManagement/CriteriaManagementModals/ModalCreateCriteria/SectionLabel';
import { INTL_KEYS } from '../../../intlKeys';
import { Group, Step } from '../../../types';
import { Operator } from '../../../queries/customRules/types';
import { useConfigureCriteriaRule } from '../../../ConfigureCriteriaRuleContext';
import {
  VidMobFormControl,
  VidMobFormHelperText,
} from '../../../../../../../vidMobComponentWrappers';
import useApplicabilityItems from './useApplicabilityItems';
import { MOTION_IDENTIFIER } from '../CriteriaGroup/constants';

const { APPLICABILITY_MEDIA_TYPES } = CreativeScoringConstants;

type ApplicabilityMediaType =
  (typeof APPLICABILITY_MEDIA_TYPES)[keyof typeof APPLICABILITY_MEDIA_TYPES];

const hasTimeDimension = (groups: Group[]): boolean => {
  return groups.some((group) =>
    group.rules.some((rule) =>
      rule.constraints?.some(
        (constraint) =>
          constraint?.operator &&
          [
            Operator.Between,
            Operator.After,
            Operator.Before,
            Operator.BeforeEnd,
          ].includes(constraint.operator),
      ),
    ),
  );
};

const containsTimeOffPercentage = (groups: Group[]) =>
  groups.some((group) =>
    group.rules.some((rule) =>
      rule?.constraints?.some(
        (constraint) =>
          constraint.operator === Operator.MinTotalDurationPercent,
      ),
    ),
  );

const containsMotion = (groups: Group[]) =>
  groups.some((group) =>
    group.rules.some((rule) => rule.identifier === MOTION_IDENTIFIER),
  );

const getTypesToSelectFrom = () => {
  const applicabilityMediaTypes: ApplicabilityMediaType[] = Object.values(
    APPLICABILITY_MEDIA_TYPES,
  ) as ApplicabilityMediaType[];
  return applicabilityMediaTypes;
};

type MediaTypeObject = {
  isChecked: boolean;
  isDisabled: boolean;
  value: string;
  type: string;
};

const getMediaTypeItems = ({
  typesToSelectFrom,
  applicability,
  applicableApplicabilityItems,
  groups,
}: {
  typesToSelectFrom: string[];
  applicability: string[];
  applicableApplicabilityItems: string[];
  groups: Group[];
}): MediaTypeObject[] => {
  const isThereTimeOffPercentage = containsTimeOffPercentage(groups);
  const hasMotion = containsMotion(groups);
  const mediaTypesToDisplay = typesToSelectFrom.map((type) => {
    const value =
      APPLICABILITY_MEDIA_TYPES[type as keyof typeof APPLICABILITY_MEDIA_TYPES];

    const isChecked = applicability?.includes(value);

    const hasTime = hasTimeDimension(groups);
    const isImage = type === APPLICABILITY_MEDIA_TYPES.IMAGE;

    const isDisabled =
      !applicableApplicabilityItems?.includes(value) ||
      (hasTime && isImage) ||
      (isImage && isThereTimeOffPercentage) ||
      (isImage && hasMotion);

    return {
      type,
      value,
      isChecked,
      isDisabled,
    };
  });
  return mediaTypesToDisplay;
};

export const TypeSelection = ({
  applicability,
  handleSelectApplicability,
}: {
  applicability: string[];
  handleSelectApplicability: (mediaType: string) => void;
}) => {
  const intl = useIntl();

  const applicabilityTypeDisplayText = {
    [APPLICABILITY_MEDIA_TYPES.IMAGE]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.images.label'
      ],
    [APPLICABILITY_MEDIA_TYPES.VIDEO]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.videos.label'
      ],
    [APPLICABILITY_MEDIA_TYPES.ANIMATED_IMAGE]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.animated.images.label'
      ],
    [APPLICABILITY_MEDIA_TYPES.HTML]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.html.label'
      ],
  };

  const { groups, submitted } = useConfigureCriteriaRule();

  const applicableApplicabilityItems = useApplicabilityItems();

  const typesToSelectFrom = getTypesToSelectFrom();

  const mediaTypesToDisplay = getMediaTypeItems({
    typesToSelectFrom,
    applicability,
    applicableApplicabilityItems,
    groups,
  });

  const typeRequiredError =
    submitted[Step.ApplySettings] &&
    !applicability.length &&
    intl.messages[INTL_KEYS.validation.empty];

  return (
    <VidMobBox mb={16}>
      <SectionLabel
        sectionTitle={intl.formatMessage({
          id: INTL_KEYS.CRITERIA_FORM.TYPE_LABEL,
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.modal.criteria.custom.criteriaType.helperText',
        })}
        labelCustomSx={{ marginTop: 0 }}
        required
      />
      <VidMobFormControl error={!!typeRequiredError} variant="standard">
        <VidMobBox>
          {mediaTypesToDisplay.map(
            ({ value, type, isChecked, isDisabled }: MediaTypeObject) => (
              <VidMobFormControlLabel
                key={type}
                label={applicabilityTypeDisplayText[type] as string}
                control={
                  <VidMobCheckbox
                    checked={isChecked}
                    disabled={isDisabled}
                    onChange={() => handleSelectApplicability(value)}
                  />
                }
              />
            ),
          )}
        </VidMobBox>
        {typeRequiredError ? (
          <VidMobFormHelperText>
            {typeRequiredError as string}
          </VidMobFormHelperText>
        ) : null}
      </VidMobFormControl>
    </VidMobBox>
  );
};
