import React from 'react';
import SingleValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/SingleValueInput';
import {
  ListItem,
  SingleValue,
} from '../../../../../../components/ReportFilters/types';
import {
  activeLabelSx,
  labelSx,
} from './ConstraintsSelection/components/styles';
import { Stack } from '@mui/material';
import DefaultSlotElement from '../../../../../../components/ReportFilters/components/FilterValueInput/DefaultSlotElement';
import { useIntl } from 'react-intl';
import { CRITERIA_BUILDER_HELP_CENTER_URL } from '../../V2/constants';
import { INTL_KEYS } from '../../intlKeys';
import { useCustomTooltip } from '../../../../../../muiCustomComponents/CustomTooltip/CustomTooltip';
import { RuleDto } from '../../queries/customRules/types';
import { RULE_OPTION, TAG_TYPE } from './CriteriaGroup/constants';
import extractIdFromRuleOption from './CriteriaGroup/helpers/extractIdFromRuleOption';

const getRuleOptionIdentifierByName = (
  customRules: RuleDto[],
  name: string,
) => {
  return customRules.find((opt) => opt.name === name)?.identifier;
};

const getDescriptionKeyByRuleOptionIdentifier = (
  name: string,
  ruleOptionIdentifier?: string,
) => {
  const prefix = INTL_KEYS.CRITERIA_RULE_GROUP.ruleDescriptionPrefix;
  return ruleOptionIdentifier
    ? `${prefix}${ruleOptionIdentifier}`
    : `${prefix}${name}`;
};

const getTranslationKeyFromOption = ({
  option,
  customRules,
}: {
  option: ListItem;
  customRules: RuleDto[];
}) => {
  let identifier;
  if (option.type === TAG_TYPE) {
    identifier = option.parentId || '';
  } else if (option.type === RULE_OPTION) {
    const ruleOptionId = extractIdFromRuleOption(option.id as string);
    identifier = `${option.parentId}.${ruleOptionId}`;
  } else {
    identifier = getRuleOptionIdentifierByName(customRules, option.name);
  }

  const descriptionKey = getDescriptionKeyByRuleOptionIdentifier(
    option.name,
    identifier as string,
  );
  return descriptionKey;
};

const DropdownOptionSlot = (props: {
  customRules: RuleDto[];
  options: ListItem[];
  optionProps: any;
}) => {
  const { customRules, optionProps, options } = props;
  const { name, isDivider } = optionProps;
  const intl = useIntl();

  const descriptionKey = getTranslationKeyFromOption({
    option: optionProps,
    customRules,
  });

  const description = intl.messages[descriptionKey]
    ? `${intl.messages[descriptionKey]}`
    : '';

  const infoPopover = useCustomTooltip(
    name,
    description,
    CRITERIA_BUILDER_HELP_CENTER_URL,
    { vertical: 'center', horizontal: 'left' },
    { vertical: 'center', horizontal: 'right' },
    { marginLeft: '20px' },
  );

  return (
    <Stack
      width="100%"
      onMouseEnter={infoPopover.handlePopoverOpen}
      onMouseLeave={infoPopover.handlePopoverClose}
    >
      <DefaultSlotElement
        {...optionProps}
        slotSx={{ textTransform: 'capitalize' }}
        valueOptions={options}
      />
      {!isDivider && infoPopover.renderPopover()}
    </Stack>
  );
};

export type ElementTagOption = {
  name: string;
  id: string;
  parentId: string;
  type: string;
};

interface RuleSelectionProps {
  value: SingleValue | null;
  onChange: (value: SingleValue) => void;
  error?: any;
  placeholder?: string;
  onSearchChange: (value: string) => void;
  onRuleInputFocus?: () => void;
  onRuleInputBlur?: () => void;
  options: ListItem[];
  customRules: RuleDto[];
}

const RuleSelection = ({
  value,
  onChange,
  onSearchChange,
  error,
  placeholder,
  onRuleInputFocus,
  onRuleInputBlur,
  options,
  customRules,
}: RuleSelectionProps) => {
  return (
    <SingleValueInput
      onFocus={onRuleInputFocus}
      onBlur={onRuleInputBlur}
      onSearchChange={onSearchChange}
      displaySearch
      value={value}
      valueOptions={options}
      onChange={onChange}
      buttonWidth={!value ? '160px' : 'unset'}
      error={!!error}
      hasGroupSelect={false}
      customPlaceholder={placeholder}
      activeLabelSx={activeLabelSx}
      labelSx={labelSx}
      slotSx={{ textTransform: 'capitalize' }}
      DropdownOptionSlot={(props) => (
        <DropdownOptionSlot
          optionProps={props}
          customRules={customRules}
          options={options}
        />
      )}
    />
  );
};

export default RuleSelection;
