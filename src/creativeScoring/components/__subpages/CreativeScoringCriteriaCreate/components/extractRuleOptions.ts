import { RuleDto } from '../queries/customRules/types';
import { RULE_OPTION } from './customRules/CriteriaGroup/constants';
import { ElementTagOption } from './customRules/RuleSelection';

const extractRuleOptions = (rules: RuleDto[]): ElementTagOption[] => {
  const result: ElementTagOption[] = [];

  rules.forEach((rule: RuleDto) => {
    rule?.constraints?.forEach((constraint) => {
      if (constraint.options && constraint.options.length > 0) {
        constraint.options.forEach((option) => {
          result.push({
            name: `${rule.identifier} > ${rule.name} > ${option.label}`,
            id: `${rule.identifier}:${option.value}`,
            parentId: rule.identifier,
            type: RULE_OPTION,
          });
        });
      } else if (
        constraint.allowedValues &&
        constraint.allowedValues.length > 0
      ) {
        constraint.allowedValues.forEach((value) => {
          result.push({
            name: `${rule.identifier} > ${rule.name} > ${value}`,
            id: `${rule.identifier}:${value}`,
            parentId: rule.identifier,
            type: RULE_OPTION,
          });
        });
      }
    });
  });

  return result;
};

export default extractRuleOptions;
