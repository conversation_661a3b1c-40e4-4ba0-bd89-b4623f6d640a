import React, { createContext, ReactNode, useContext, useState } from 'react';
import { ListItem } from '../../../../muiCustomComponents/MultiSelectDropdown/MultiSelectDropdown';
import { Template } from '../../../../types/template.type';
import { isNil } from '../../../../utils/typeCheckUtils';
import CreativeScoringConstants from '../../../constants/creativeScoring.constants';
import { CRITERIA_CATEGORIES } from '../../../constants/criteriaManagement.constants';
import { CustomIcon } from '../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModal.types';
import { defaultGroups } from './helpers/rules';
import { GroupsValidationResult, useValidation } from './helpers/validation';
import { CriteriaIsOptionalOption, Group, Step } from './types';

const { CRITERIA_IS_OPTIONAL_OPTIONS, CRITERIA_IS_GLOBAL_OPTIONS } =
  CreativeScoringConstants;

type StepSubmittedState = Record<Step, boolean>;
interface ConfigureCriteriaRuleContextType {
  formState: {
    name: string;
    icon: CustomIcon | null;
    setIcon: React.Dispatch<React.SetStateAction<CustomIcon | null>>;
    setName: React.Dispatch<React.SetStateAction<string>>;
    selectedTemplate: Template | null;
    setSelectedTemplate: React.Dispatch<React.SetStateAction<Template | null>>;
    selectedPlatforms: ListItem[];
    setSelectedPlatforms: React.Dispatch<React.SetStateAction<ListItem[]>>;
    selectedCriteriaGroups: string[];
    setSelectedCriteriaGroups: React.Dispatch<React.SetStateAction<string[]>>;
    parameterValues: Record<string, any>;
    setParameters: React.Dispatch<React.SetStateAction<Record<string, any>>>;
    applicability: string[];
    setApplicability: React.Dispatch<React.SetStateAction<string[]>>;
    handleSelectApplicability: (mediaType: string) => void;
    selectedCategory: string | null;
    setSelectedCategory: React.Dispatch<React.SetStateAction<string | null>>;
    isCustomAudio: boolean;
    customValues: {
      selected: any;
      values: string;
      isValid: boolean;
    };
    setCustomValues: React.Dispatch<
      React.SetStateAction<{
        selected: any;
        values: string;
        isValid: boolean;
      }>
    >;
    criteriaIsOptional: CriteriaIsOptionalOption;
    setCriteriaIsOptional: (isGlobalOption: CriteriaIsOptionalOption) => void;
    isGlobalCriteria: CriteriaIsOptionalOption;
    setIsGlobalCriteria: (isGlobalOption: CriteriaIsOptionalOption) => void;
    isAddAnotherSelected: boolean;
    setAddAnotherSelected: React.Dispatch<React.SetStateAction<boolean>>;
    clearCriteriaStates: () => void;
    resetCustomValues: () => void;
  };
  validation: {
    isTemplateCustom: () => boolean;
    areAllParameterSet: () => boolean;
    areAllRequiredParametersSet: () => boolean;
    isValidCustomValue: () => boolean;
    isApplicabilitySelected: () => boolean;
    canSubmitCriteria: () => boolean;
  };
  submitted: StepSubmittedState;
  markAsSubmitted: (step: Step) => void;
  toggleCriteriaView: (active: boolean) => void;
  isNewCriteriaView: boolean;
  groups: Group[];
  setGroups: (groups: Group[]) => void;
  groupValidationResult: GroupsValidationResult;
  isGroupsSetValid: boolean;
  steps: {
    currentStep: Step;
    goToApplySettings: () => void;
    goToSelectCriteria: () => void;
  };
  aperture?: number;
  setAperture: (aperture?: number) => void;
}

const useFormValues = () => {
  const [name, setName] = useState('');
  const [icon, setIcon] = useState<CustomIcon | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null,
  );
  const [selectedPlatforms, setSelectedPlatforms] = useState<ListItem[]>([]);
  const [selectedCriteriaGroups, setSelectedCriteriaGroups] = useState<
    string[]
  >([]);
  const [parameterValues, setParameters] = useState({});
  const [applicability, setApplicability] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(
    CRITERIA_CATEGORIES.ALL,
  );
  const [customValues, setCustomValues] = useState({
    selected: null,
    values: '',
    isValid: false,
  });
  const [criteriaIsOptional, setCriteriaIsOptional] = useState(
    CRITERIA_IS_OPTIONAL_OPTIONS.MANDATORY,
  );
  const [isGlobalCriteria, setIsGlobalCriteria] = useState(
    CRITERIA_IS_GLOBAL_OPTIONS.WORKSPACE,
  );
  const [isAddAnotherSelected, setAddAnotherSelected] = useState(false);

  return {
    name,
    setName,
    icon,
    setIcon,
    selectedTemplate,
    setSelectedTemplate,
    selectedPlatforms,
    setSelectedPlatforms,
    parameterValues,
    setParameters,
    applicability,
    setApplicability,
    selectedCategory,
    setSelectedCategory,
    customValues,
    setCustomValues,
    criteriaIsOptional,
    setCriteriaIsOptional,
    isGlobalCriteria,
    setIsGlobalCriteria,
    isAddAnotherSelected,
    setAddAnotherSelected,
    selectedCriteriaGroups,
    setSelectedCriteriaGroups,
  };
};

const ConfigureCriteriaRuleContext = createContext<
  ConfigureCriteriaRuleContextType | undefined
>(undefined);

export const ConfigureCriteriaRuleProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const {
    name,
    setName,
    icon,
    setIcon,
    selectedTemplate,
    setSelectedTemplate,
    selectedPlatforms,
    setSelectedPlatforms,
    parameterValues,
    setParameters,
    applicability,
    setApplicability,
    selectedCategory,
    setSelectedCategory,
    customValues,
    setCustomValues,
    criteriaIsOptional,
    setCriteriaIsOptional,
    isGlobalCriteria,
    setIsGlobalCriteria,
    isAddAnotherSelected,
    setAddAnotherSelected,
    selectedCriteriaGroups,
    setSelectedCriteriaGroups,
  } = useFormValues();

  const [groups, setGroups] = useState<Group[]>(defaultGroups);
  const { validationResults: groupValidationResult, isGroupsSetValid } =
    useValidation(groups);

  const [isNewCriteriaView, setIsNewCriteriaView] = useState(false);
  const [currentStep, setCurrentStep] = useState<Step>(Step.SelectCriteria);

  const [aperture, setAperture] = useState<number | undefined>(undefined);

  const isTemplateCustom = () => {
    return Boolean(selectedTemplate?.custom);
  };

  const areAllRequiredParametersSet = () => {
    if (isTemplateCustom()) {
      return true;
    }

    let allSelected = true;
    selectedTemplate?.parameters?.forEach((parameter) => {
      const key = parameter.identifier as keyof typeof parameterValues;
      const values = parameterValues[key];
      const valuesUndefined = values === undefined;
      const emptyObject =
        typeof values === 'object' &&
        !Array.isArray(values) &&
        !Object.values(values)?.length;
      if (valuesUndefined || emptyObject) {
        allSelected = false;
      }
    });
    return allSelected;
  };

  const areAllParameterSet = () => {
    if (isTemplateCustom()) {
      return true;
    }

    if (selectedTemplate) {
      return selectedTemplate?.parameters
        ? selectedTemplate?.parameters?.length ===
            Object.keys(parameterValues).length
        : true;
    }

    return false;
  };

  const isOldViewSelectCriteriaValid =
    Boolean(name.trim().length) &&
    selectedPlatforms.length &&
    selectedTemplate &&
    selectedCategory &&
    areAllRequiredParametersSet();

  const defaultSubmittedState = {
    [Step.SelectCriteria]: false,
    [Step.ApplySettings]: false,
  };

  const [submitted, setSubmitted] = useState<StepSubmittedState>(
    defaultSubmittedState,
  );

  const markAsSubmitted = (step: Step) => {
    setSubmitted((prev) => ({ ...prev, [step]: true }));
  };

  const resetSubmitted = () => {
    setSubmitted(defaultSubmittedState);
  };

  const goToApplySettings = () => {
    markAsSubmitted(Step.SelectCriteria);

    if (isGroupsSetValid && selectedPlatforms?.length > 0 && name.trim()) {
      setCurrentStep(Step.ApplySettings);
      return;
    }

    if (!isNewCriteriaView && isOldViewSelectCriteriaValid) {
      setCurrentStep(Step.ApplySettings);
    }
  };

  const goToSelectCriteria = () => setCurrentStep(Step.SelectCriteria);

  const clearCriteriaGroups = () => {
    setGroups(defaultGroups);
  };

  const toggleCriteriaView = (checked: boolean) => {
    setIsNewCriteriaView(checked);
    handleClearStateAfterModeChange();
    clearCriteriaGroups();
    resetSubmitted();
  };

  const isCustomAudio =
    selectedTemplate?.identifier === 'CUSTOM_AUDIO_IDENTIFIER';

  const resetCustomValues = () => {
    setCustomValues({
      selected: null,
      values: '',
      isValid: false,
    });
  };

  const clearCriteriaStates = () => {
    setName('');
    setSelectedCategory(CRITERIA_CATEGORIES.ALL);
    setParameters({});
    setSelectedTemplate(null);
    setApplicability([]);
    setCriteriaIsOptional(CRITERIA_IS_OPTIONAL_OPTIONS.MANDATORY);
    setIsGlobalCriteria(CRITERIA_IS_GLOBAL_OPTIONS.WORKSPACE);
    resetCustomValues();
  };

  const handleClearStateAfterModeChange = () => {
    setParameters({});
    setSelectedTemplate(null);
    setSelectedCategory(CRITERIA_CATEGORIES.ALL);
    setApplicability([]);
    setCriteriaIsOptional(CRITERIA_IS_OPTIONAL_OPTIONS.MANDATORY);
    setIsGlobalCriteria(CRITERIA_IS_GLOBAL_OPTIONS.WORKSPACE);
    resetCustomValues();
  };

  const handleSelectApplicability = (mediaType: string) => {
    if (isCustomAudio) {
      return;
    }

    setApplicability((applicability) => {
      if (applicability?.includes(mediaType)) {
        return applicability?.filter((item) => item !== mediaType);
      }

      return [mediaType].concat(applicability || []);
    });
  };

  const isValidCustomValue = () => {
    if (isTemplateCustom()) {
      return customValues.isValid;
    }
    return true;
  };

  const isApplicabilitySelected = () => {
    return applicability?.length > 0;
  };

  const canSubmitCriteria = () => {
    return (
      Boolean(name.trim().length) &&
      !isNil(selectedPlatforms) &&
      !isNil(selectedTemplate) &&
      areAllRequiredParametersSet() &&
      isValidCustomValue() &&
      isApplicabilitySelected()
    );
  };

  return (
    <ConfigureCriteriaRuleContext.Provider
      value={{
        formState: {
          name,
          setName,
          icon,
          setIcon,
          selectedTemplate,
          setSelectedTemplate,
          selectedPlatforms,
          setSelectedPlatforms,
          parameterValues,
          setParameters,
          applicability,
          setApplicability,
          handleSelectApplicability,
          selectedCategory,
          setSelectedCategory,
          isCustomAudio,
          customValues,
          setCustomValues,
          criteriaIsOptional,
          setCriteriaIsOptional,
          isGlobalCriteria,
          setIsGlobalCriteria,
          isAddAnotherSelected,
          setAddAnotherSelected,
          clearCriteriaStates,
          resetCustomValues,
          selectedCriteriaGroups,
          setSelectedCriteriaGroups,
        },
        validation: {
          isTemplateCustom,
          areAllParameterSet,
          areAllRequiredParametersSet,
          isValidCustomValue,
          isApplicabilitySelected,
          canSubmitCriteria,
        },
        submitted,
        markAsSubmitted,
        toggleCriteriaView,
        isNewCriteriaView,
        groupValidationResult,
        isGroupsSetValid,
        groups,
        setGroups,
        steps: {
          currentStep,
          goToApplySettings,
          goToSelectCriteria,
        },
        aperture,
        setAperture,
      }}
    >
      {children}
    </ConfigureCriteriaRuleContext.Provider>
  );
};

export const useConfigureCriteriaRule = () => {
  const context = useContext(ConfigureCriteriaRuleContext);
  if (!context) {
    throw new Error(
      'useConfigureCriteriaRule must be used within a ConfigureCriteriaRuleProvider',
    );
  }
  return context;
};
