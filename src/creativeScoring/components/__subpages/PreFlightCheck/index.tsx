import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { COMPLIANCE, GLOBALS } from '../../../../constants';
import { getSelectedScorecard } from '../../../redux/selectors/complianceBatches.selectors';
import {
  getHasUpdatedCriteriaOrBrand,
  getScorecardsLandingViewLink,
  getShowOutdatedBanner,
} from '../../../redux/selectors/complianceShared.selectors';
import complianceShared from '../../../redux/slices/complianceShared.slice';
import PreFlightOrInFlight from '../../reports/preFlightOrInFlight';
import { getLoadScorecardFromURLStatus } from '../../../redux/selectors/complianceContentAudit.selectors';
import { setAndLoadScorecardFromURL } from '../../../redux/actions/contentAudit.actions';
import { getCurrentPartnerId } from '../../../../redux/selectors/partner.selectors';
import Header from './Header/Header';
import { useToastAlert } from '../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { PreFlightFiltersProvider } from './Filters/context/PreFlightFiltersProvider';
import { FiltersPanel } from './Filters/FiltersPanel';
import {
  VidMobBox,
  VidMobCircularProgress,
  VidMobStack,
} from '../../../../vidMobComponentWrappers';
import { getReportDetailsScorecardId } from '../../../redux/selectors/filterPanelV2.selectors';
import useGetIsMediaProcessing from '../../reports/preFlightOrInFlight/hooks/common/useGetIsMediaProcessing';
import { submitUpdateScorePreFlightBatch } from '../../../redux/actions/batches.actions';
import scoringFilterPanelV2Slice from '../../../redux/slices/filterPanelV2.slice';
import { UpdateScoreConfirmationModal } from './UpdateScoreConfirmationModal';
import { usePreFlightFilters } from '../../reports/preFlightOrInFlight/hooks/preFlight/usePreFlightFilters';

const { INDIVIDUAL_CRITERIA_RESULTS } = COMPLIANCE;
const { ERROR } = INDIVIDUAL_CRITERIA_RESULTS;
const { setShowOutdatedBanner } = complianceShared.actions;
const { REDUX_LOADING_STATUS } = GLOBALS;
const { NOT_LOADED } = REDUX_LOADING_STATUS;
const { resetReportDetailsPanelFilters } = scoringFilterPanelV2Slice.actions;

const loadingContainerSx = {
  flexDirection: 'column',
  width: '100%',
  height: '100%',
  alignItems: 'center',
  justifyContent: 'center',
};

const containerSx = {
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  height: '94vh',
  pl: '24px',
};

const PreFlightScorecardDetails = () => {
  const { scorecardId: scorecardIdFromUrl } = useParams<{
    scorecardId: string;
  }>();

  const dispatch = useDispatch();
  const showToastAlert = useToastAlert();
  const { filters } = usePreFlightFilters();
  const isMediaProcessing = useGetIsMediaProcessing();

  const selectedBatch = useSelector(getSelectedScorecard);
  const partnerId = useSelector(getCurrentPartnerId);
  const loadScorecardFromURLStatus = useSelector(getLoadScorecardFromURLStatus);
  const hasUserUpdatedCriteriaThisSession = useSelector(
    getHasUpdatedCriteriaOrBrand,
  );
  const showOutdatedScoreBanner = useSelector(getShowOutdatedBanner);
  const scorecardsLandingViewLink = useSelector(getScorecardsLandingViewLink);
  const reportDetailsScorecardId = useSelector(getReportDetailsScorecardId);

  const [isOutdatedScoresBannerOpen, setIsOutdatedScoresBannerOpen] =
    useState(false);
  const [isMediaProcessingBannerOpen, setIsMediaProcessingBannerOpen] =
    useState(false);
  const [
    isUpdateScorecardScoreConfirmationModalOpen,
    setIsUpdateScorecardScoreConfirmationModalOpen,
  ] = useState(false);

  const selectedScorecardId = selectedBatch?.id;
  const scorecardName = selectedBatch?.name;
  const bothIdsAreDefined = selectedScorecardId && scorecardIdFromUrl;
  const idsAreNotEqual =
    bothIdsAreDefined && selectedScorecardId !== scorecardIdFromUrl;

  const onClickUpdateScore = () => {
    setIsUpdateScorecardScoreConfirmationModalOpen(true);
  };

  const handleUpdateScorecardScore = () => {
    dispatch(submitUpdateScorePreFlightBatch(selectedBatch.id));
    setIsUpdateScorecardScoreConfirmationModalOpen(false);
    setIsOutdatedScoresBannerOpen(false);
  };

  const handleCloseOutdatedScoresBanner = () => {
    setIsOutdatedScoresBannerOpen(false);
  };

  const handleCloseMediaProcessingBanner = () => {
    setIsMediaProcessingBannerOpen(false);
  };

  useEffect(() => {
    if (!scorecardIdFromUrl) {
      return;
    }

    if (reportDetailsScorecardId !== String(selectedScorecardId)) {
      dispatch(resetReportDetailsPanelFilters());
    }

    const shouldLoad =
      idsAreNotEqual || loadScorecardFromURLStatus === NOT_LOADED;

    if (shouldLoad) {
      dispatch(
        setAndLoadScorecardFromURL(
          idsAreNotEqual ? selectedScorecardId : scorecardIdFromUrl,
          partnerId,
        ),
      );
    }
  }, [
    selectedScorecardId,
    scorecardIdFromUrl,
    partnerId,
    idsAreNotEqual,
    loadScorecardFromURLStatus,
  ]);

  useEffect(() => {
    if (isMediaProcessing) {
      setIsOutdatedScoresBannerOpen(false);
      setIsMediaProcessingBannerOpen(true);
    }
  }, [isMediaProcessing]);

  useEffect(() => {
    if (Boolean(showOutdatedScoreBanner) && !isMediaProcessing) {
      setIsOutdatedScoresBannerOpen(true);
    }
  }, [showOutdatedScoreBanner]);

  useEffect(() => {
    if (selectedBatch?.status === ERROR) {
      showToastAlert('blank.compliance.batchNoData.error.message', 'error');
    } else if (selectedBatch?.isOutdated || hasUserUpdatedCriteriaThisSession) {
      dispatch(setShowOutdatedBanner({ showOutdatedBanner: true } as any));
    }
  }, [selectedBatch]);

  if (!selectedBatch) {
    return (
      <VidMobStack sx={loadingContainerSx}>
        <VidMobCircularProgress />
      </VidMobStack>
    );
  }

  return (
    <VidMobBox sx={containerSx}>
      <Header
        scorecardName={scorecardName}
        scorecardsLandingViewLink={scorecardsLandingViewLink}
        onClickUpdateScore={onClickUpdateScore}
        isOutdatedScoresBannerOpen={isOutdatedScoresBannerOpen}
        handleCloseOutdatedScoresBanner={handleCloseOutdatedScoresBanner}
        isMediaProcessingBannerOpen={isMediaProcessingBannerOpen}
        handleCloseMediaProcessingBanner={handleCloseMediaProcessingBanner}
      />
      <PreFlightOrInFlight
        workspaceId={partnerId}
        channelId={selectedBatch?.platforms?.[0] || null}
        filters={filters}
        scorecard={selectedBatch}
        isAnyBannerOpen={
          isOutdatedScoresBannerOpen || isMediaProcessingBannerOpen
        }
      />
      {isUpdateScorecardScoreConfirmationModalOpen && (
        <UpdateScoreConfirmationModal
          isOpen={isUpdateScorecardScoreConfirmationModalOpen}
          onClose={() => setIsUpdateScorecardScoreConfirmationModalOpen(false)}
          onSubmit={handleUpdateScorecardScore}
        />
      )}
    </VidMobBox>
  );
};

const PreFlightScorecardDetailsWrapper = () => (
  <PreFlightFiltersProvider>
    <FiltersPanel>
      <PreFlightScorecardDetails />
    </FiltersPanel>
  </PreFlightFiltersProvider>
);

export default PreFlightScorecardDetailsWrapper;
