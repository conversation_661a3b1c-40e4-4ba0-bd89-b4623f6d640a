import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobAlert,
  VidMobBox,
  VidMobButton,
  VidMobIconButton,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import {
  CloseIcon,
  InfoFilledIcon,
} from '../../../../../assets/vidmob-mui-icons/general';
import {
  alertSx,
  containerSx,
  infoIconSx,
  actionButtonsSx,
  actionButtonTextSx,
} from './bannerStyles';

interface Props {
  onBannerButtonClick: () => void;
  onClose: () => void;
}

export const OutdatedCriteriaSetBanner = ({
  onBannerButtonClick,
  onClose,
}: Props) => {
  const intl = useIntl();

  return (
    <VidMobBox sx={containerSx}>
      <VidMobAlert
        sx={alertSx}
        onClose={() => {}}
        icon={<InfoFilledIcon sx={infoIconSx} />}
        action={
          <VidMobBox sx={actionButtonsSx}>
            <VidMobButton
              onClick={onBannerButtonClick}
              size="small"
              sx={actionButtonTextSx}
            >
              {intl.formatMessage({
                id: 'ui.compliance.contentAudit.banner.outdated.batch.button',
                defaultMessage: 'Update Score',
              })}
            </VidMobButton>
            <VidMobIconButton aria-label="delete" onClick={onClose}>
              <CloseIcon />
            </VidMobIconButton>
          </VidMobBox>
        }
      >
        <VidMobTypography variant="body2">
          {intl.formatMessage({
            id: 'ui.compliance.contentAudit.banner.outdated.batch.text',
            defaultMessage:
              'The criteria for this analysis has changed and is no longer up to date.',
          })}
        </VidMobTypography>
      </VidMobAlert>
    </VidMobBox>
  );
};
