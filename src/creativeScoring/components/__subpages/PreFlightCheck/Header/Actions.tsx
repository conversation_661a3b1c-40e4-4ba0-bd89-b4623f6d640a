import React, { useState, JSX } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getCurrentUserId } from '../../../../../redux/selectors/user.selectors';
import { canUserUpdateScorecardDetail } from '../../../../featureServices/CreativeScoring';
import { getCurrentPartnerPermissions } from '../../../../../redux/selectors/partner.selectors';
import { getSelectedScorecard } from '../../../../redux/selectors/complianceBatches.selectors';
import ContentAuditResultsCSVDownload from '../../../brandScore/ContentAuditResultsCSVDownload';
import PreFlightCheckEditModal, {
  Props as EditModalProps,
} from '../../../scorecardsLanding/Modals/PreFlightCheckEditModal';
import ScorecardsLandingDeleteModal, {
  Props as DeleteModalProps,
} from '../../../scorecardsLanding/Modals/ScorecardsLandingDeleteModal';
import ReportActionButtons from '../../../reports/reportsSharedComponents/ReportPageHeader/ReportActionButtons';
import {
  fetchBrands,
  fetchCountries,
} from '../../../../../userManagement/redux/thunk/workspaces.thunk';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import { ScorecardsLandingEditModal } from '../../../scorecardsLanding/Modals';

const isCriteriaGroupsInPreFlightCreationEnabled = getFeatureFlag(
  'isCriteriaGroupsInPreFlightCreationEnabled',
);

interface Props {
  onClickUpdateScore: () => void;
}

export const Actions = ({ onClickUpdateScore }: Props) => {
  const dispatch = useDispatch();
  const [currentModal, setCurrentModal] = useState<JSX.Element | null>(null);
  const currentUserId = useSelector(getCurrentUserId);
  const partnerPermissions = useSelector(getCurrentPartnerPermissions);
  const selectedBatch = useSelector(getSelectedScorecard);
  const isBatchOutdated = selectedBatch?.isOutdated;

  const { personId } = selectedBatch;

  const handleClearModal = () => {
    setCurrentModal(null);
  };

  const handleCsvDownload = (args: any) => {
    setCurrentModal(<ContentAuditResultsCSVDownload {...args} />);
  };

  const handleEditScorecard = (args: EditModalProps) => {
    // @ts-ignore
    dispatch(fetchCountries());
    // @ts-ignore
    dispatch(fetchBrands());
    setCurrentModal(
      isCriteriaGroupsInPreFlightCreationEnabled ? (
        <PreFlightCheckEditModal {...args} />
      ) : (
        <ScorecardsLandingEditModal {...args} />
      ),
    );
  };

  const handleDeleteScorecard = (args: DeleteModalProps) => {
    setCurrentModal(<ScorecardsLandingDeleteModal {...args} />);
  };

  const handleUpdateScore = () => {
    onClickUpdateScore();
  };

  const canUpdateDetailOrDelete = canUserUpdateScorecardDetail(
    partnerPermissions,
    personId,
    currentUserId,
  );

  const editOrDeleteCallbackArgs = {
    isOpen: true,
    onClose: handleClearModal,
    scorecard: selectedBatch,
    isDetailsPage: true,
  };

  return (
    <>
      <ReportActionButtons
        isSavedReport
        loadingOrError={false}
        noData={false}
        onClickExportAsCSV={() =>
          handleCsvDownload({ selectedBatch, setCsvDownload: handleClearModal })
        }
        onClickEdit={() => handleEditScorecard(editOrDeleteCallbackArgs)}
        onClickDelete={() => handleDeleteScorecard(editOrDeleteCallbackArgs)}
        canEditOrDelete={canUpdateDetailOrDelete}
        editPermissionTooltipKey="ui.compliance.scorecardsLanding.action.editReportDetails.noPermission"
        defaultEditPermissionTooltip="You do not have permission to edit this scorecard's details"
        deletePermissionTooltipKey="ui.compliance.scorecardsLanding.action.deleteReport.noPermission"
        defaultDeletePermissionTooltip="You do not have permission to delete this scorecard"
        {...(isBatchOutdated && {
          onClickUpdateScore: handleUpdateScore,
        })}
      />
      {currentModal}
    </>
  );
};
