import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobAlert,
  VidMobBox,
  VidMobIconButton,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import {
  CloseIcon,
  InfoFilledIcon,
} from '../../../../../assets/vidmob-mui-icons/general';
import {
  alertSx,
  containerSx,
  infoIconSx,
  actionButtonsSx,
} from './bannerStyles';

interface Props {
  onClose: () => void;
}

export const MediaProcessingBanner = ({ onClose }: Props) => {
  const intl = useIntl();

  return (
    <VidMobBox sx={containerSx}>
      <VidMobAlert
        sx={alertSx}
        onClose={() => {}}
        icon={<InfoFilledIcon sx={infoIconSx} />}
        action={
          <VidMobBox sx={actionButtonsSx}>
            <VidMobIconButton aria-label="delete" onClick={onClose}>
              <CloseIcon />
            </VidMobIconButton>
          </VidMobBox>
        }
      >
        <VidMobTypography variant="body2">
          {intl.formatMessage({
            id: 'ui.compliance.contentAudit.banner.mediaProcessing.text',
            defaultMessage:
              'This analysis is being updated. Your results will be ready within the hour. In some instances, this may take up to 24 hours.',
          })}
        </VidMobTypography>
      </VidMobAlert>
    </VidMobBox>
  );
};
