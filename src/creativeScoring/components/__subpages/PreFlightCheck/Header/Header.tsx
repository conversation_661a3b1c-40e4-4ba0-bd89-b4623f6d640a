import React from 'react';
import { useIntl } from 'react-intl';
import PageHeaderV2 from '../../../../../components/PageHeaderV2';
import { ScoreIcon } from '../../../../../assets/vidmob-mui-icons/general';
import { SCORECARD_AND_REPORTS_HELP_CENTER_URL } from '../../../../constants/creativeScoring.constants';
import { VidMobBox } from '../../../../../vidMobComponentWrappers';
import ReportTypeSection from '../../../../../components/ReportPageHeaderReportDetails/ReportTypeSection';
import { OutdatedCriteriaSetBanner } from './OutdatedCriteriaSetBanner';
import { MediaProcessingBanner } from './MediaProcessingBanner';
import { Actions } from './Actions';

const containerSx = {
  display: 'flex',
  flexDirection: 'column',
};

const headerSx = {
  pl: 0,
};

const headerButtonsSx = {
  display: 'flex',
  justifyContent: 'flex-end',
  alignItems: 'center',
  gap: '8px',
  flexWrap: 'wrap',
};

const headerDetailsSx = {
  pb: '16px',
};

interface Props {
  scorecardName: string;
  scorecardsLandingViewLink: string;
  onClickUpdateScore: () => void;
  isOutdatedScoresBannerOpen: boolean;
  handleCloseOutdatedScoresBanner: () => void;
  isMediaProcessingBannerOpen: boolean;
  handleCloseMediaProcessingBanner: () => void;
}

const Header = ({
  scorecardName,
  scorecardsLandingViewLink,
  onClickUpdateScore,
  isOutdatedScoresBannerOpen,
  handleCloseOutdatedScoresBanner,
  isMediaProcessingBannerOpen,
  handleCloseMediaProcessingBanner,
}: Props) => {
  const intl = useIntl();

  const scorecardsLandingBreadcrumbLabel = intl.formatMessage({
    id: 'ui.site.pageTitle.ci.compliance.preFlight.breadcrumb',
    defaultMessage: 'Pre-flight',
  });

  const getScoreCardDetails = () => ({
    suffix: '',
    reportTypeCopy: intl.formatMessage({
      id: 'ui.scorecards.header.details.preflight.scorecard',
      defaultMessage: 'Pre-flight scorecard',
    }),
    reportDescription: intl.formatMessage({
      id: 'ui.scorecards.header.details.description.preflight.scorecard',
      defaultMessage:
        'This scorecard considers creative-level data to help you understand the percentage of creatives that met your criteria.',
    }),
    reportIcon: <ScoreIcon />,
    learnMoreLink: SCORECARD_AND_REPORTS_HELP_CENTER_URL,
    learnMoreTextOverride: intl.formatMessage({
      id: 'ui.scorecards.header.details.learnMore',
      defaultMessage: 'How is this view calculated?',
    }),
  });

  const getBanner = () => {
    if (isMediaProcessingBannerOpen) {
      return (
        <MediaProcessingBanner onClose={handleCloseMediaProcessingBanner} />
      );
    }

    if (isOutdatedScoresBannerOpen) {
      return (
        <OutdatedCriteriaSetBanner
          onBannerButtonClick={onClickUpdateScore}
          onClose={handleCloseOutdatedScoresBanner}
        />
      );
    }

    return null;
  };

  return (
    <VidMobBox sx={containerSx}>
      <PageHeaderV2
        title={scorecardName}
        additionalBoxStyles={headerSx}
        breadcrumbs={[
          {
            label: scorecardsLandingBreadcrumbLabel,
            url: scorecardsLandingViewLink,
          },
        ]}
      >
        <VidMobBox sx={headerButtonsSx}>
          <Actions onClickUpdateScore={onClickUpdateScore} />
        </VidMobBox>
      </PageHeaderV2>
      <VidMobBox sx={headerDetailsSx}>
        <ReportTypeSection {...getScoreCardDetails()} />
      </VidMobBox>
      {getBanner()}
    </VidMobBox>
  );
};

export default Header;
