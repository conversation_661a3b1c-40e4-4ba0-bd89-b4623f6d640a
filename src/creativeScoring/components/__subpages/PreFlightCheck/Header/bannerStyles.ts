export const containerSx = {
  display: 'flex',
  justifyContent: 'center',
  pb: '10px',
  mr: '24px',
};

export const alertSx = {
  width: '100%',
  backgroundColor: 'info.light',
  alignItems: 'center',
  '& .MuiAlert-message': {
    color: 'text.primary',
    fontWeight: '600',
  },
  '& .MuiAlert-action': {
    p: 0,
  },
};

export const infoIconSx = {
  color: 'icon.primary',
  width: '20px',
  height: '20px',
};

export const actionButtonsSx = {
  display: 'flex',
  justifyContent: 'center',
  alignContent: 'center',
};

export const actionButtonTextSx = {
  color: 'text.primary',
  fontWeight: '600',
};
