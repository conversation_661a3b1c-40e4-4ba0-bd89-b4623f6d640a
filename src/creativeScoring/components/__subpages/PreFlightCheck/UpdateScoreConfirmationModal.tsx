import React from 'react';
import { useIntl } from 'react-intl';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';

interface Props {
  isOpen: boolean;
  onSubmit: () => void;
  onClose: () => void;
}

export const UpdateScoreConfirmationModal = ({
  isOpen,
  onSubmit,
  onClose,
}: Props) => {
  const intl = useIntl();

  return (
    <CustomDialog
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onSubmit}
      headerText={intl.formatMessage({
        id: 'ui.compliance.contentAudit.updateAnalysis.confirmation.header',
        defaultMessage: 'Are you sure you’d like to update this analysis?',
      })}
      headerSubText={intl.formatMessage({
        id: 'ui.compliance.contentAudit.updateAnalysis.confirmation.description',
        defaultMessage:
          'If you proceed, current results will be updated using the latest criteria set.',
      })}
      submitButtonLabel={intl.formatMessage({
        id: 'ui.compliance.contentAudit.customAnalysis.updateAnalysis',
        defaultMessage: 'Update score',
      })}
      explicitDialogWidth={420}
    />
  );
};
