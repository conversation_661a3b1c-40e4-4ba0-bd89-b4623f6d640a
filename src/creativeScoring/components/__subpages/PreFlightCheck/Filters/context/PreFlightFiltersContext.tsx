import { createContext } from 'react';
import { ReportPillarSpecificAdvancedFilterKey } from '../../../../../../components/ReportFilters/types';
import { SCORECARD_STATUS } from '../../../../../constants/scorecard.constants';
import {
  PreFlightCreativeAdherence,
  PreFlightFilters,
} from '../../../../reports/preFlightOrInFlight/types';
import { DEFAULT_PRE_FLIGHT_CREATIVE_ADHERENCE } from './PreFlightFiltersProvider';

type PreFlightStagingFilters = Partial<
  Record<
    ReportPillarSpecificAdvancedFilterKey,
    Set<string> | PreFlightCreativeAdherence
  >
>;

export interface PreFlightFiltersContextType {
  filters: PreFlightFilters;
  creativeTypes: Set<string>;
  criteriaResults: Set<string>;
  creativeAdherence: PreFlightCreativeAdherence;
  scorecardStatus: SCORECARD_STATUS | null;
  stagingFilters: PreFlightStagingFilters;
  hasPendingFilters: boolean;
  isFilterDrawerOpen: boolean;
  disabledFilters: Set<ReportPillarSpecificAdvancedFilterKey>;
  setCreativeTypes: (types: Set<string>) => void;
  setCriteriaResults: (results: Set<string>) => void;
  setStagingFilters: (filters: PreFlightStagingFilters) => void;
  setHasPendingFilters: (pending: boolean) => void;
  setIsFilterDrawerOpen: (isOpen: boolean) => void;
  setScorecardStatus: (status: SCORECARD_STATUS | null) => void;
  applyFilters: () => void;
  resetStagingFilters: () => void;
}

export const PreFlightFiltersContext =
  createContext<PreFlightFiltersContextType>({
    filters: {},
    creativeTypes: new Set(),
    criteriaResults: new Set(),
    creativeAdherence: DEFAULT_PRE_FLIGHT_CREATIVE_ADHERENCE,
    stagingFilters: {},
    scorecardStatus: null,
    hasPendingFilters: false,
    isFilterDrawerOpen: false,
    disabledFilters: new Set(),
    setCreativeTypes: () => {},
    setCriteriaResults: () => {},
    setStagingFilters: () => {},
    setHasPendingFilters: () => {},
    setIsFilterDrawerOpen: () => {},
    applyFilters: () => {},
    resetStagingFilters: () => {},
    setScorecardStatus: () => {},
  });
