import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { PreFlightFiltersContext } from './PreFlightFiltersContext';
import {
  Operator,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  ReportPillarSpecificAdvancedFilterKey,
} from '../../../../../../components/ReportFilters/types';
import { SCORING_MEDIA_TYPE_FILTER_OPTIONS } from '../../../../ScoringFilters/constants';
import { SCORECARD_STATUS } from '../../../../../constants/scorecard.constants';
import { getSelectedScorecard } from '../../../../../redux/selectors/complianceBatches.selectors';
import { CriteriaResult } from '../../../../../types/rollUpReports.types';
import {
  PreFlightCreativeAdherence,
  PreFlightFilters,
} from '../../../../reports/preFlightOrInFlight/types';
import { DEFAULT_EMPTY_CRITERIA_RESULTS } from '../../../../reports/preFlightOrInFlight/constants';

const CREATIVE_TYPE_OPTIONS = new Set(
  SCORING_MEDIA_TYPE_FILTER_OPTIONS.map((item) => item.id),
);

const DEFAULT_CRITERIA_RESULT_OPTIONS = new Set([
  CriteriaResult.PASS,
  CriteriaResult.FAIL,
  CriteriaResult.NO_DATA,
]);

export const DEFAULT_PRE_FLIGHT_CREATIVE_ADHERENCE: PreFlightCreativeAdherence =
  {
    [Operator.LESS_THAN]: '',
  };

export const PreFlightFiltersProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const selectedBatch = useSelector(getSelectedScorecard);

  const [creativeTypes, setCreativeTypes] = useState(
    new Set(CREATIVE_TYPE_OPTIONS),
  );
  const [criteriaResults, setCriteriaResults] = useState<Set<string>>(
    DEFAULT_CRITERIA_RESULT_OPTIONS,
  );
  const [creativeAdherence, setCreativeAdherence] =
    useState<PreFlightCreativeAdherence>(DEFAULT_PRE_FLIGHT_CREATIVE_ADHERENCE);
  const [scorecardStatus, setScorecardStatus] =
    useState<SCORECARD_STATUS | null>(null);
  const [stagingFilters, setStagingFilters] = useState<
    Partial<
      Record<
        ReportPillarSpecificAdvancedFilterKey,
        Set<string> | PreFlightCreativeAdherence
      >
    >
  >({
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]: new Set(
      creativeTypes,
    ),
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS]: new Set(
      criteriaResults,
    ),
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]:
      creativeAdherence,
  });

  const [hasPendingFilters, setHasPendingFilters] = useState(false);
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  // Creative Adherence filter is disabled by default since we have selected Criteria Results by default
  const [disabledFilters, setDisabledFilters] = useState<
    Set<REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS>
  >(new Set([REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]));

  useEffect(() => {
    if (selectedBatch?.status) {
      setScorecardStatus(selectedBatch.status);
    }
  }, [selectedBatch?.status]);

  // disable Creative Adherence filter if no criteria results are different from default
  useEffect(() => {
    const stagingCriteriaResults = stagingFilters[
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS
    ] as Set<CriteriaResult>;

    const newDisabledFilters = new Set(disabledFilters);

    if (stagingCriteriaResults.size > 0 && stagingCriteriaResults.size < 3) {
      newDisabledFilters.add(
        REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
      );
    } else {
      newDisabledFilters.delete(
        REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
      );
    }

    setDisabledFilters(newDisabledFilters);
  }, [
    stagingFilters[REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS],
  ]);

  // disable Criteria Results filter if creative adherence is selected
  useEffect(() => {
    const stagingCreativeAdherence = stagingFilters[
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE
    ] as PreFlightCreativeAdherence;

    const newDisabledFilters = new Set(disabledFilters);
    if (Object.values(stagingCreativeAdherence).some(Boolean)) {
      newDisabledFilters.add(
        REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
      );
    } else {
      newDisabledFilters.delete(
        REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
      );
    }
    setDisabledFilters(newDisabledFilters);
  }, [
    stagingFilters[REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE],
  ]);

  const applyFilters = () => {
    const newCreativeTypes = stagingFilters[
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE
    ] as Set<string>;
    const newCriteriaResults = stagingFilters[
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS
    ] as Set<string>;
    const newCreativeAdherence = stagingFilters[
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE
    ] as PreFlightCreativeAdherence;

    setCreativeTypes(
      newCreativeTypes.size === 0
        ? CREATIVE_TYPE_OPTIONS
        : new Set(newCreativeTypes),
    );

    setCriteriaResults(
      newCriteriaResults.size === 0
        ? new Set(DEFAULT_EMPTY_CRITERIA_RESULTS)
        : new Set(newCriteriaResults),
    );

    setCreativeAdherence(newCreativeAdherence);

    setHasPendingFilters(false);
    setIsFilterDrawerOpen(false);
  };

  const resetStagingFilters = () => {
    setStagingFilters({
      [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]: new Set(
        creativeTypes,
      ),
      [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS]: new Set(
        criteriaResults,
      ),
      [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]:
        creativeAdherence,
    });
    setHasPendingFilters(false);
  };

  const filters: PreFlightFilters = {
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]:
      Array.from(creativeTypes),
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS]:
      Array.from(criteriaResults),
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]:
      creativeAdherence,
  };

  return (
    <PreFlightFiltersContext.Provider
      value={{
        filters,
        creativeTypes,
        criteriaResults,
        creativeAdherence,
        stagingFilters,
        scorecardStatus,
        hasPendingFilters,
        isFilterDrawerOpen,
        disabledFilters,
        setCreativeTypes,
        setCriteriaResults,
        setStagingFilters,
        setHasPendingFilters,
        setIsFilterDrawerOpen,
        setScorecardStatus,
        applyFilters,
        resetStagingFilters,
      }}
    >
      {children}
    </PreFlightFiltersContext.Provider>
  );
};
