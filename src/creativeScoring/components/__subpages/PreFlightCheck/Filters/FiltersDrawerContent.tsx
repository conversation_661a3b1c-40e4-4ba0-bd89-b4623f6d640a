import React, { <PERSON> } from 'react';
import { VidMobBox } from '../../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import CustomTabs from '../../../../../muiCustomComponents/CustomTabs/CustomTabs';
import { FILTERS_TAB_KEY } from '../../../ScoringFilters/constants';
import { FiltersTabContent } from './FiltersTabContent';

export const FiltersDrawerContent: FC = () => {
  const intl = useIntl();

  const tabs = [
    {
      label: intl.formatMessage({ id: FILTERS_TAB_KEY }),
      content: <FiltersTabContent />,
    },
  ];

  return (
    <VidMobBox
      sx={{
        display: 'flex',
        justifyContent: 'center',
      }}
    >
      <CustomTabs tabs={tabs} initialSelectedTab={0} />
    </VidMobBox>
  );
};
