import React, { useContext, useState, useEffect } from 'react';
import ReportFiltersDrawer from '../../../../../components/ReportFilters/components/ReportFiltersDrawer';
import {
  VidMobBox,
  VidMobDrawer,
} from '../../../../../vidMobComponentWrappers';
import { PreFlightFiltersContext } from './context/PreFlightFiltersContext';
import { FiltersDrawerContent } from './FiltersDrawerContent';
import FilterPanelContentWrapper from '../../../../../components/ReportFilters/components/FilterPanelContentWrapper';

const DRAWER_WIDTH = 320;

const wrapperSx = {
  width: '100%',
  height: '100%',
  display: 'flex',
  margin: 0,
  padding: 0,
};

const drawerSx = {
  width: DRAWER_WIDTH,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: DRAWER_WIDTH,
    marginTop: '52px',
    height: 'calc(100% - 52px)',
    border: 'unset',
    borderLeft: '1px solid var(--divider, #BDBDBD)',
    zIndex: 7,
  },
};

export const FiltersPanel: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const {
    isFilterDrawerOpen,
    setIsFilterDrawerOpen,
    hasPendingFilters,
    applyFilters,
  } = useContext(PreFlightFiltersContext);

  const [isFilterDrawerLoading, _setIsFilterDrawerLoading] = useState(false);
  const [isApplyButtonEnabled, setIsApplyButtonEnabled] = useState(false);
  const [areFiltersValid, _setAreFiltersValid] = useState(true);

  useEffect(() => {
    setIsApplyButtonEnabled(hasPendingFilters);
  }, [hasPendingFilters]);

  const handleApplyFilters = () => {
    applyFilters();
    setIsFilterDrawerOpen(false);
  };

  return (
    <VidMobBox sx={wrapperSx}>
      <FilterPanelContentWrapper open={isFilterDrawerOpen}>
        {children}
      </FilterPanelContentWrapper>
      <VidMobDrawer
        sx={drawerSx}
        variant="persistent"
        anchor="right"
        open={isFilterDrawerOpen}
      >
        <ReportFiltersDrawer
          isLoading={isFilterDrawerLoading}
          onApply={handleApplyFilters}
          canApply={isApplyButtonEnabled}
          areFiltersValid={areFiltersValid}
          content={<FiltersDrawerContent />}
          onClose={() => setIsFilterDrawerOpen(false)}
        />
      </VidMobDrawer>
    </VidMobBox>
  );
};
