import React from 'react';
import { VidMobStack } from '../../../../../vidMobComponentWrappers';
import FilterEntry from '../../../../../components/ReportFilters/components/FilterEntry';
import {
  FilterValue,
  Operator,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  ReportAdvancedFilterDefinition,
  ReportPillarSpecificAdvancedFilterKey,
} from '../../../../../components/ReportFilters/types';
import { ShareRounded } from '@mui/icons-material';
import { usePreFlightFilters } from '../../../reports/preFlightOrInFlight/hooks/preFlight/usePreFlightFilters';
import usePreFlightFilterOptions from '../../../reports/preFlightOrInFlight/hooks/preFlight/usePreFlightFilterOptions';
import { IdAndName } from '../../../../../types/common.types';
import { PreFlightCreativeAdherence } from '../../../reports/preFlightOrInFlight/types';

const filterEntriesSx = {
  gap: 4,
  width: '100%',
  mb: '8px',
};

export const FiltersTabContent: React.FC = () => {
  const { LOCAL_FILTER_DEFINITIONS, FILTER_OPTIONS_MAP } =
    usePreFlightFilterOptions();

  const filterKeys = Object.keys(
    LOCAL_FILTER_DEFINITIONS,
  ) as ReportPillarSpecificAdvancedFilterKey[];

  const { stagingFilters, setStagingFilters, setHasPendingFilters } =
    usePreFlightFilters();

  const handleFilterChange = (
    filterKey: ReportPillarSpecificAdvancedFilterKey,
    updatedValue: FilterValue,
  ) => {
    if (
      filterKey === REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE
    ) {
      handleCreativeAdherenceFilterChange(updatedValue);
    } else {
      handleFilterChangeDefault(filterKey, updatedValue);
    }

    setHasPendingFilters(true);
  };

  const handleFilterChangeDefault = (
    filterKey: ReportPillarSpecificAdvancedFilterKey,
    updatedValue: FilterValue,
  ) => {
    const newValueIds = new Set<string>(
      (updatedValue.value as IdAndName[]).map((item: IdAndName) => item.id),
    );

    setStagingFilters({
      ...stagingFilters,
      [filterKey]: new Set(newValueIds),
    });
  };

  const handleCreativeAdherenceFilterChange = (updatedValue: FilterValue) => {
    const newCreativeAdherence = {
      [updatedValue.operator as Operator.LESS_THAN | Operator.GREATER_THAN]:
        updatedValue.value,
    };

    setStagingFilters({
      ...stagingFilters,
      [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]:
        newCreativeAdherence,
    });
  };

  const getFilterOperator = (
    filterKey: ReportPillarSpecificAdvancedFilterKey,
    filter: Set<string> | PreFlightCreativeAdherence,
    filterDefinition: ReportAdvancedFilterDefinition,
  ): Operator => {
    if (
      filterKey === REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE
    ) {
      return Object.keys(filter)[0] as
        | Operator.LESS_THAN
        | Operator.GREATER_THAN;
    }

    return filterDefinition.defaultOperator as Operator.EQUALS;
  };

  const getFilterValues = (
    filterKey: ReportPillarSpecificAdvancedFilterKey,
    filter: Set<string> | PreFlightCreativeAdherence,
    options: IdAndName[] | string,
  ) => {
    if (
      filterKey === REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE
    ) {
      return getCreativeAdherenceFilterValues(
        filter as PreFlightCreativeAdherence,
      );
    }

    return getFilterValuesDefault(
      filter as Set<string>,
      options as IdAndName[],
    );
  };

  const getCreativeAdherenceFilterValues = (
    filter: PreFlightCreativeAdherence,
  ) => Object.values(filter)[0];

  const getFilterValuesDefault = (filter: Set<string>, options: IdAndName[]) =>
    Array.from(filter)
      .map((id) => options.find((opt) => opt.id === id))
      .filter(Boolean);

  const renderFilters = () =>
    filterKeys.map((filterKey: ReportPillarSpecificAdvancedFilterKey) => {
      const filterDefinition = LOCAL_FILTER_DEFINITIONS[filterKey];
      const filter = stagingFilters[filterKey];
      const filterOptions = FILTER_OPTIONS_MAP[filterKey];

      if (!filterDefinition || !filter || filterOptions === undefined) {
        return null;
      }

      const filterOperator = getFilterOperator(
        filterKey,
        filter,
        filterDefinition,
      );
      const filterValue = getFilterValues(filterKey, filter, filterOptions);

      return (
        <FilterEntry
          key={filterKey}
          filterKey={filterKey}
          icon={
            filterDefinition.icon || (
              <ShareRounded fontSize="small" sx={{ color: 'icon.secondary' }} />
            )
          }
          onChange={(updatedValue) =>
            handleFilterChange(filterKey, { ...updatedValue, key: filterKey })
          }
          value={filterValue}
          operator={filterOperator!}
          operators={filterDefinition.operators!}
          valueOptions={filterDefinition.valueOptions}
          valueType={filterDefinition.valueType}
          textFieldInputType={filterDefinition.textFieldInputType}
          numberInputRule={filterDefinition.numberInputRule}
          hideOperator={
            !filterDefinition.operators ||
            filterDefinition.operators.length <= 1
          }
          labelKey={filterDefinition.labelKey}
          suffix={filterDefinition.suffix}
          initialBlur={filterDefinition.initialBlur}
          infoTooltipKey={filterDefinition.infoTooltipKey}
          hideError
          disabled={Boolean(filterDefinition.isDisabled)}
          disabledTooltipKey={filterDefinition.disabledTooltipKey}
        />
      );
    });

  return (
    <VidMobStack direction="column" sx={filterEntriesSx}>
      {renderFilters()}
    </VidMobStack>
  );
};
