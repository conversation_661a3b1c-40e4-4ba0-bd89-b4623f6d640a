import React, { useEffect, useState } from 'react';
import { Prompt } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import './CreativeScoringBrandAuditReport.scss';
import PageHeaderV2 from '../../../../components/PageHeaderV2';
import siteMap, { routeParams } from '../../../../routing/siteMap';
import { FormattedMessage } from 'react-intl';
import CommonButton from '../../../../components/CommonButton';
import { Button, Skeleton } from '@mui/material';
import FormInputBoxField from '../../../../components/FormInputBoxField';
import DropDownUploadAssets from '../../../../components/DropDownUploadAssets';
import PreFlightDragDropUploadBox from '../../brandScore/PreFlightDragDropUploadBox';
import ChannelSelectionWarning from '../../shared/ChannelSelectionWarning';
import WarnBeforeUnload from '../../../../components/WarnBeforeUnload';
import CriteriaMetIcon from '../../../../assets/icons/ic-ca-met-checkmark-green.svg';
import IconClearRed from '../../../../assets/icons/ic-clear-red.svg';
import {
  createScorecard,
  updateScorecard,
} from '../../../redux/actions/uploads.actions';
import { COMPLIANCE, PLATFORM, GLOBALS } from '../../../../constants';
import {
  uploadFiles as uploadFilesActionCreator,
  cleanUpOnCancel as cleanUpOnCancelActionCreator,
  removeAllAssets as removeAllAssetsActionCreator,
} from '../../../redux/actions/uploads.actions';
import {
  getBatchFolderId,
  getSubmissionReportStatus,
  getBatchId,
  getBatchFolderContents,
  getBatchFolderContentStatus,
} from '../../../redux/selectors/complianceUploads.selectors';
import {
  submitPreFlightBatch as submitPreFlightBatchActionCreator,
  retrieveCountriesList as retrieveCountriesListActionCreator,
} from '../../../redux/actions/batches.actions';
import { getCriteriaSetId } from '../../../redux/selectors/complianceCriteriaManagement.selectors';
import ModalReportSubmissionDone from '../../../../components/ModalReportSubmissionDone';
import { generatePath } from 'react-router';
import history from '../../../../routing/history';
import {
  getSelectedBatchSubmissionStatus,
  getCountriesList,
  getUpdateBatchStatusLoadingStatus,
} from '../../../redux/selectors/complianceBatches.selectors';
import complianceUploadsSlice from '../../../redux/slices/complianceUploads.slice';
import { wasFolderDraggedAndDropped } from '../../../../featureServices/assets/transforms';
import getFilesFromDataTransferItems from '../../../../utils/getFilesFromDataTransferItems';
import vmErrorLog from '../../../../utils/vmErrorLog';
import { toggleUploader } from '../../../../redux/actions/upload.actions';
import Loading from '../../../../vcl/ui/Loading';
import SearchableMultiSelectDropDown from '../../../../components/SearchableMultiSelectDropDown';
import { useIntl } from 'react-intl';
import checkAndFilterSupportedFiles from '../../../featureServices/checkAndFilterSupportedFiles';
import { getCurrentPartner } from '../../../../redux/selectors/partner.selectors';
import { uniqueId } from '../../../../utils/uniqueIdGenerator';
import { fetchBrands } from '../../../../userManagement/redux/thunk/workspaces.thunk';
import { brandsSelector } from '../../../../userManagement/redux/selectors/workspaces.selectors';
import { VidMobTooltip } from '../../../../vidMobComponentWrappers';
import {
  getLogoByPlatformIdentifier,
  getPlatformIdentifierForLogo,
  isFacebook,
} from '../../../../utils/feConstantsUtils';
import CriteriaManagementSlice from '../../../redux/slices/criteriaManagement.slice';
import { getPlatformCriteriaCounts } from '../../../redux/selectors/criteriaManagement.selectors';

const { resetUploadsSlice } = complianceUploadsSlice.actions;
const {
  loadCriteria,
  setCriteriaPagination,
  reset: resetCriteriaManagement,
} = CriteriaManagementSlice.actions;
const { REDUX_LOADING_STATUS } = GLOBALS;
const { SUCCESS, FAILED, PENDING } = REDUX_LOADING_STATUS;
const { LEARN_MORE_LINKS, SUBMISSION_REPORT_STATUS, BATCH_TYPE } = COMPLIANCE;

const { PLATFORM_IDENTIFIERS_TO_LOCALES } = PLATFORM;

const { ANALYSIS_LINK } = LEARN_MORE_LINKS;
const { UPLOADING, READY_FOR_SUBMISSION } = SUBMISSION_REPORT_STATUS;
const { PRE_FLIGHT } = BATCH_TYPE;

let dragged = 0; // Used to track drag events for external files/folders.

const CreativeScoringBrandAuditReport = () => {
  const dispatch = useDispatch();
  const intl = useIntl();

  const submissionReportStatus = useSelector((state) =>
    getSubmissionReportStatus(state),
  );
  const batchFolderId = useSelector((state) => getBatchFolderId(state));
  const batchId = useSelector((state) => getBatchId(state));
  const criteriaSetId = useSelector((state) => getCriteriaSetId(state));
  const selectedBatchStatus = useSelector((state) =>
    getSelectedBatchSubmissionStatus(state),
  );
  const batchFolderContentStatus = useSelector((state) =>
    getBatchFolderContentStatus(state),
  );
  const batchFolderContents = useSelector((state) =>
    getBatchFolderContents(state),
  );
  const countriesList = useSelector((state) => getCountriesList(state));
  const partnerCriteriaMap = useSelector((state) =>
    getPlatformCriteriaCounts(state),
  );
  const currentPartner = useSelector(getCurrentPartner);
  const updateBatchLoadingStatus = useSelector((state) =>
    getUpdateBatchStatusLoadingStatus(state),
  );
  const brandsApi = useSelector(brandsSelector);
  const isBrandsFetching = brandsApi?.isFetching;
  const brands = brandsApi?.data;

  const [reportName, setReportName] = useState('');
  /* savedReportName, isReportNameSaved, and isSaveButtonVisible are all tied into the mechanics
  of rendering the save button or the green checkmark by the analysis name input. The requirements are in
  AGL-7257 */
  const [isLoading, setIsLoading] = useState(false);
  const [savedReportName, setSavedReportName] = useState('');
  const [isReportNameSaved, setIsReportNameSaved] = useState(false);
  const [isReportNameSaveFailed, setIsReportNameSaveFailed] = useState(false);
  const [isSaveButtonVisible, setIsSaveButtonVisible] = useState(false);
  const [isSubmissionDoneModalOpen, setIsSubmissionDoneModalOpen] =
    useState(false);
  const [isNewPreFlightBatchSubmitted, setIsNewPreFlightBatchSubmitted] =
    useState(false);
  const [selectedCountries, setSelectedCountries] = useState([]);
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [selectedChannels, setSelectedChannels] = useState([]);
  const [partnerChannels, setPartnerChannels] = useState([]);
  const [platformLocalesToIdentifiers, setPlatformLocalesToIdentifiers] =
    useState(null);
  const [channelErrorId, setChannelErrorId] = useState('');
  // Used only for displaying in our warning component.
  const [
    channelsWithoutCriteriaToDisplay,
    setChannelsWithoutCriteriaToDisplay,
  ] = useState([]);

  // State tracks the dragging over of external file or folder
  const [itemDraggedOver, setItemDraggedOver] = useState(false);

  const isUploadActive = submissionReportStatus === UPLOADING;

  const multiselectDropdownClass = classNames({
    'multiselect-dropdown': true,
    'dragged-over': itemDraggedOver,
  });
  const submissionReportNameClass = classNames({
    'submission-report-name': true,
    'dragged-over': itemDraggedOver,
    'save-button-visible': isSaveButtonVisible,
  });

  const mediaUploadHeaderClass = classNames({
    'media-upload-header': true,
    'dragged-over': itemDraggedOver,
  });

  const complianceSubmissionReportClass = classNames({
    'overlay-no-scroll': !batchFolderId,
  });

  useEffect(() => {
    dispatch(
      setCriteriaPagination({
        pagination: {
          offset: 0,
          perPage: 1000,
        },
      }),
    );

    dispatch(loadCriteria());
    return () => {
      dispatch(resetCriteriaManagement());
    };
  }, []);

  const handleUploadFilesDispatch = (supportedFiles) => {
    if (supportedFiles.length > 0) {
      dispatch(uploadFilesActionCreator(supportedFiles));
    }
  };

  const cancelUploadsAndDeleteMedia = () => {
    // Cancel polling files and delete them. Clean up the redux state and hide the uploads bar.
    dispatch(removeAllAssetsActionCreator());
    dispatch(cleanUpOnCancelActionCreator());
    dispatch(resetUploadsSlice());
    dispatch(toggleUploader(false));
  };

  const handleChannelWarningDetails = (
    channelWarningTextId,
    channelsWithoutCriteriaDisplayList = [],
  ) => {
    setChannelErrorId(channelWarningTextId);
    setChannelsWithoutCriteriaToDisplay(channelsWithoutCriteriaDisplayList);
  };

  const getWarningMessage = (selectedChannels, channelsWithoutCriteria) => {
    let warningMessageId = '';
    let channelsToDisplay = [];

    const singleChannelSelectedHasNoCriteria =
      channelsWithoutCriteria.length === 1 && selectedChannels.length === 1;
    const someChannelsSelectedHaveNoCriteria =
      channelsWithoutCriteria.length > 0 &&
      selectedChannels.length > 1 &&
      channelsWithoutCriteria.length < selectedChannels.length;
    const allChannelsSelectedHaveNoCriteria =
      channelsWithoutCriteria.length > 1 &&
      channelsWithoutCriteria.length === selectedChannels.length;

    if (singleChannelSelectedHasNoCriteria) {
      warningMessageId =
        'ui.compliance.contentAuditResult.submissionReport.channelSelectionWarning.single';
    }

    if (someChannelsSelectedHaveNoCriteria) {
      warningMessageId =
        'ui.compliance.contentAuditResult.submissionReport.channelSelectionWarning.some';
      channelsToDisplay = channelsWithoutCriteria;
    }

    if (allChannelsSelectedHaveNoCriteria) {
      warningMessageId =
        'ui.compliance.contentAuditResult.submissionReport.channelSelectionWarning.all';
    }

    return {
      warningMessageId,
      channelsToDisplay,
    };
  };

  useEffect(() => {
    if (selectedChannels.length) {
      const channelsWithoutCriteria = [];

      selectedChannels.forEach((channel) => {
        const channelId = platformLocalesToIdentifiers[channel];
        const channelHasNoCriteria = !partnerCriteriaMap[channelId];

        if (channelHasNoCriteria) {
          channelsWithoutCriteria.push(channel);
        }
      });

      const { warningMessageId, channelsToDisplay } = getWarningMessage(
        selectedChannels,
        channelsWithoutCriteria,
      );
      return handleChannelWarningDetails(warningMessageId, channelsToDisplay);
    }

    return handleChannelWarningDetails('');
  }, [
    platformLocalesToIdentifiers,
    selectedChannels,
    partnerCriteriaMap,
    partnerChannels,
  ]);

  useEffect(() => {
    dispatch(retrieveCountriesListActionCreator());
    /* Brands are fetched on scorecards landing.
       This is a failsafe for direct navigation */
    if (!isBrandsFetching && !brandsApi.isFulfilled) {
      dispatch(fetchBrands());
    }

    const updatedPlatforms = COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.map(
      (platform) => {
        const iconIdentifier = getPlatformIdentifierForLogo(platform.id);
        const iconUrl =
          getLogoByPlatformIdentifier(iconIdentifier) || platform.iconUrl;

        return {
          ...platform,
          i18nName: isFacebook(platform.id)
            ? PLATFORM_IDENTIFIERS_TO_LOCALES.META
            : platform.i18nName,
          name: intl.messages[
            isFacebook(platform.id)
              ? PLATFORM_IDENTIFIERS_TO_LOCALES.META
              : platform.i18nName
          ],
          iconUrl: iconUrl,
        };
      },
    );

    const platformLocalesMap = updatedPlatforms.reduce((map, platform) => {
      map[platform.name] = platform.id;
      return map;
    }, {});

    setPlatformLocalesToIdentifiers(platformLocalesMap);
    setPartnerChannels(updatedPlatforms);
  }, []);

  useEffect(
    () => () => {
      // When a useEffect hook returns a callback, it acts as a componentWillUnmount
      // We want to clear uploads state when this component unmounts without submitting a batch.
      if (batchId) {
        cancelUploadsAndDeleteMedia();
      }
    },
    [batchId],
  );

  useEffect(() => {
    const isBatchActionComplete =
      selectedBatchStatus === SUCCESS || selectedBatchStatus === FAILED;

    if (isBatchActionComplete) {
      setIsLoading(false);

      if (isNewPreFlightBatchSubmitted) {
        setIsNewPreFlightBatchSubmitted(false);
      } else if (selectedBatchStatus === SUCCESS) {
        setIsReportNameSaved(true);
        setSavedReportName(reportName.trim());
      } else if (selectedBatchStatus === FAILED) {
        setIsReportNameSaved(false);
        setIsReportNameSaveFailed(true);
      }
    } else {
      setIsReportNameSaved(false);
      setIsReportNameSaveFailed(false);
    }
  }, [selectedBatchStatus, updateBatchLoadingStatus]);

  const handleInputChange = (name) => {
    setReportName(name);
    setIsReportNameSaved(false);
    setIsSaveButtonVisible(true);
  };

  const handleSaveReportClick = () => {
    setIsSaveButtonVisible(false);
    setIsLoading(true);
    if (batchId) {
      const payload = {
        scorecardId: batchId,
        name: reportName,
        markets: selectedCountries.length
          ? selectedCountries.map((isoCode) => ({ id: isoCode }))
          : null,
        platforms: selectedChannels.length
          ? selectedChannels
              .map((channel) => platformLocalesToIdentifiers[channel])
              .map((channel) => ({ id: channel }))
          : null,
        brands: selectedBrands,
      };
      dispatch(updateScorecard(payload));
    } else {
      const actionParams = {
        name: reportName,
        batchType: PRE_FLIGHT,
        criteriaSetId,
        platform: selectedChannels.length
          ? selectedChannels.map(
              (channel) => platformLocalesToIdentifiers[channel],
            )
          : null,
        countries: selectedCountries,
        brands: selectedBrands,
      };
      dispatch(createScorecard(actionParams));
    }
  };

  const uploadOverlayClass = classNames({
    'upload-full-overlay': !batchFolderId,
    'upload-partial-overlay': isUploadActive,
  });

  const handleExternalDragEnter = (event) => {
    event.preventDefault();
    event.stopPropagation();
    dragged++;

    setItemDraggedOver(true);
  };

  const handleExternalDragLeave = (event) => {
    event.preventDefault();
    event.stopPropagation();
    dragged--;

    if (dragged === 0) {
      setItemDraggedOver(false);
    }
  };

  const handleFileOrFolderDrop = async (event) => {
    event.preventDefault();
    event.stopPropagation();
    setItemDraggedOver(false); // reset styling after items have been dropped

    if (wasFolderDraggedAndDropped(event)) {
      // handle folder flattening and file upload
      getFilesFromDataTransferItems(event.dataTransfer.items)
        .then(async (files) => {
          const supportedFiles = await checkAndFilterSupportedFiles(files);
          handleUploadFilesDispatch(supportedFiles);
        })
        .catch((error) => {
          vmErrorLog(
            error,
            'CreativeScoringBrandAuditReport.jsx handleFolderDrop',
            `Failed to upload files in dropped folder for batch: ${batchId}`,
          );
        });
    } else {
      // upload files dragged and dropped
      const { files } = event.dataTransfer;
      const supportedFiles = await checkAndFilterSupportedFiles(files);
      handleUploadFilesDispatch(supportedFiles);
    }
  };

  const headerText =
    intl.messages[
      'ui.compliance.contentAuditResult.modal.warnBeforeExit.header'
    ];
  const descriptionText =
    intl.messages[
      'ui.compliance.contentAuditResult.modal.warnBeforeExit.description'
    ];
  const warnBeforeExistMessage = headerText + '\n' + descriptionText;

  let headerWithPartnerName =
    intl.messages['ui.compliance.contentAuditResult.submissionReport.header'];

  if (currentPartner && currentPartner.name) {
    headerWithPartnerName =
      intl.messages[
        'ui.compliance.contentAuditResult.submissionReport.header.withPartnerName'
      ] + currentPartner.name;
  }

  const handleOnBlur = () => {
    setIsReportNameSaved(reportName === savedReportName);
    setIsSaveButtonVisible(reportName && reportName !== savedReportName);
  };

  const handleOnFocus = () => {
    setIsSaveButtonVisible(!isReportNameSaved);
  };

  const renderScoreAction = () => {
    if (isLoading) {
      return (
        <div className="saved-name-loading-dots">
          <Loading dots />
        </div>
      );
    }

    if (isSaveButtonVisible) {
      return (
        <CommonButton
          disabled={!reportName}
          buttonColoring="primary"
          buttonSize="medium"
          onClick={handleSaveReportClick}
        >
          {
            intl.messages[
              'ui.compliance.contentAuditResult.submissionReport.save'
            ]
          }
        </CommonButton>
      );
    }

    if (reportName && isReportNameSaved) {
      return <img src={CriteriaMetIcon} className="saved-name-icon" alt="" />;
    }

    if (isReportNameSaveFailed) {
      return (
        <img src={IconClearRed} className="saved-name-icon failed" alt="" />
      );
    }

    return null;
  };

  const onReportSubmission = () => {
    dispatch(submitPreFlightBatchActionCreator(batchId));
    setIsSubmissionDoneModalOpen(true);
    setIsNewPreFlightBatchSubmitted(true);
  };

  return (
    <div id="compliance-submission-report-scroll-container">
      <div
        id="compliance-submission-report"
        className={complianceSubmissionReportClass}
      >
        <Prompt
          when={isReportNameSaved && !isSubmissionDoneModalOpen}
          message={warnBeforeExistMessage}
        />
        {isReportNameSaved && <WarnBeforeUnload />}
        <PageHeaderV2
          title={headerWithPartnerName}
          breadcrumbs={[
            {
              label: intl.messages['sideNav.preFlight'],
              url: generatePath(siteMap.creativeIntelligenceReports),
            },
          ]}
        >
          <Button
            variant="contained"
            disabled={
              isSaveButtonVisible ||
              submissionReportStatus !== READY_FOR_SUBMISSION
            }
            onClick={onReportSubmission}
          >
            {
              intl.messages[
                'ui.compliance.contentAuditResult.submissionReport.button.create.label'
              ]
            }
          </Button>
        </PageHeaderV2>
        {isUploadActive && <div className="name-overlay" />}
        <section id="report-container">
          <div className="report-inputs-wrapper">
            <div>
              <div className={submissionReportNameClass}>
                <span className="sub-heading-1-left-aligned-black report-header">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.name.heading'
                    ]
                  }
                </span>
                <span className="body-left-aligned-black report-subHeader">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.name.subHeading'
                    ]
                  }
                </span>
                <div className="name-input-wrapper">
                  <FormInputBoxField
                    className="name-input"
                    value={reportName}
                    inputProps={{
                      onFocus: handleOnFocus,
                    }}
                    onChange={(e) => {
                      handleInputChange(e.target.value);
                    }}
                    onBlur={handleOnBlur}
                  />
                  {renderScoreAction()}
                </div>
              </div>
              <div className={multiselectDropdownClass}>
                <span className="sub-heading-1-left-aligned-black multiselect-header">
                  <VidMobTooltip
                    text={
                      intl.messages[
                        'ui.compliance.contentAuditResult.submissionReport.channel.tooltip'
                      ]
                    }
                    position="right"
                  >
                    <span>
                      {
                        intl.messages[
                          'ui.compliance.contentAuditResult.submissionReport.channel.heading'
                        ]
                      }
                    </span>
                  </VidMobTooltip>
                </span>
                <span className="body-left-aligned-black multiselect-subHeader">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.channel.subHeading'
                    ]
                  }
                </span>
                <div className="channel-dropdown-wrapper">
                  <SearchableMultiSelectDropDown
                    hideSearch
                    idKey="id"
                    listItems={partnerChannels}
                    disabled={false}
                    handleUpdateList={setSelectedChannels}
                    exclusionClass={`channel-outside-click-ignore-${uniqueId()}`}
                    onChange={setIsSaveButtonVisible}
                  />
                </div>
                {channelErrorId && (
                  <ChannelSelectionWarning
                    detailBullets={channelsWithoutCriteriaToDisplay}
                    warningTextId={channelErrorId}
                  />
                )}
              </div>
            </div>
            <div>
              <div className={multiselectDropdownClass}>
                <span className="sub-heading-1-left-aligned-black multiselect-header">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.market.heading'
                    ]
                  }
                </span>
                <span className="body-left-aligned-black multiselect-subHeader">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.market.subHeading'
                    ]
                  }
                </span>
                <div className="location-dropdown-wrapper">
                  <SearchableMultiSelectDropDown
                    idKey="isoCode"
                    keyOfInterest="isoCode"
                    listItems={countriesList}
                    itemCollectionName={
                      intl.messages[
                        'ui.compliance.contentAuditResult.submissionReport.dropdown.market.name'
                      ]
                    }
                    disabled={false}
                    handleUpdateList={setSelectedCountries}
                    exclusionClass={`location-outside-click-ignore-${uniqueId()}`}
                    onChange={setIsSaveButtonVisible}
                  />
                </div>
              </div>
              <div className={multiselectDropdownClass}>
                <span className="sub-heading-1-left-aligned-black multiselect-header">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.brands.heading'
                    ]
                  }
                </span>
                <span className="body-left-aligned-black multiselect-subHeader">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.brands.subHeading'
                    ]
                  }
                </span>
                <div className="location-dropdown-wrapper">
                  {isBrandsFetching ? (
                    <Skeleton width="340px" height="45px" variant="rectangle" />
                  ) : (
                    <SearchableMultiSelectDropDown
                      idKey="id"
                      keyOfInterest="id"
                      listItems={brands}
                      itemCollectionName={
                        intl.messages[
                          'ui.compliance.contentAuditResult.submissionReport.dropdown.brand.name'
                        ]
                      }
                      disabled={false}
                      handleUpdateList={setSelectedBrands}
                      exclusionClass={`location-outside-click-ignore-${uniqueId()}`}
                      onChange={setIsSaveButtonVisible}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="report-media-upload-container">
            {(!batchFolderId || isUploadActive) && (
              <div className={uploadOverlayClass} />
            )}
            <div
              className="submission-media-upload"
              onDragEnter={handleExternalDragEnter}
              onDragLeave={handleExternalDragLeave}
              onDragOver={(event) => {
                /*
                 This empty handler is purposely used to allow the drop event to register.
                */
                event.preventDefault();
              }}
              onDrop={(event) => {
                handleFileOrFolderDrop(event);
              }}
            >
              <div className={mediaUploadHeaderClass}>
                <span className="sub-heading-1-left-aligned-black media-upload-header">
                  {
                    intl.messages[
                      'ui.compliance.contentAuditResult.submissionReport.upload.heading'
                    ]
                  }
                </span>
                <div className="body-left-aligned-black media-upload-subheading">
                  <div>
                    <FormattedMessage
                      id="ui.compliance.contentAuditResult.submissionReport.upload.subHeading"
                      values={{
                        learnMoreLink: (
                          <a
                            className="body-left-aligned-blue learn-more-link"
                            target="_blank"
                            rel="noopener noreferrer"
                            href={ANALYSIS_LINK}
                          >
                            {
                              intl.messages[
                                'ui.compliance.contentAuditResult.selectStatus.helpCenter'
                              ]
                            }
                          </a>
                        ),
                      }}
                    />
                  </div>
                  <DropDownUploadAssets
                    positionDropDownToTheRight
                    disableAssetLockerUpload
                    disablePastProjectsUpload
                    disableStockContentUpload
                    disableIntegrationsUpload
                    isPreFlightUpload
                    buttonLabel={
                      intl.messages[
                        'ui.compliance.contentAuditResult.submissionReport.upload.button.label'
                      ]
                    }
                    onLocalFileSelected={(event) => {
                      const { files } = event.target;
                      const supportedFiles =
                        checkAndFilterSupportedFiles(files);
                      dispatch(uploadFilesActionCreator(supportedFiles));
                    }}
                  />
                </div>
              </div>
              <PreFlightDragDropUploadBox
                intl={intl}
                itemsPending={batchFolderContentStatus === PENDING}
                itemDraggedOver={itemDraggedOver}
                uploadedAssets={batchFolderContents}
              />
            </div>
          </div>
          {isSubmissionDoneModalOpen && (
            <ModalReportSubmissionDone
              onDone={() => {
                history.push(
                  generatePath(siteMap.creativeIntelligenceCompliance, {
                    tab: routeParams.tabs.creativeIntelligenceCompliance
                      .scorecardsLanding,
                  }),
                );
              }}
            />
          )}
        </section>
      </div>
    </div>
  );
};

export default CreativeScoringBrandAuditReport;
