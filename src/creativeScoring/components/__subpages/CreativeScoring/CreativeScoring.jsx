import React, { useEffect, useRef } from 'react';
import './CreativeScoring.scss';
import siteMap, { routeParams } from '../../../../routing/siteMap';
import { array } from 'prop-types';
import routesRenderer from '../../../../routing/routesRenderer';
import { COMPLIANCE } from '../../../../constants';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  getIsJAndJSegmentSelected,
  hasBlankState,
  getCurrentAdAccountId,
  getCurrentAdAccountPlatform,
  getMediaIds,
  getHasUpdatedCriteriaOrBrand,
} from '../../../redux/selectors/complianceShared.selectors';
import CreativeScoringPreFlightScorecard from '../CreativeScoringPreFlightScorecard';
import { J_AND_J_DAM_SPLIT } from '../../../../components/__pages/FeatureFlags/featureFlagConstants';
import {
  retrieveCountriesList,
  setFilteredBatches,
} from '../../../redux/actions/batches.actions';
import {
  getPartnerBatches,
  getIsBatchSelected,
  getSelectedScorecard,
  getCountriesList,
} from '../../../redux/selectors/complianceBatches.selectors';
import {
  getCurrentPartnerId,
  getCurrentPartnerPermissions,
} from '../../../../redux/selectors/partner.selectors';
import complianceSharedSlice from '../../../redux/slices/complianceShared.slice';
import complianceCriteriaManagementSlice from '../../../redux/slices/complianceCriteriaManagement.slice';
import { GLOBALS } from '../../../../constants';
import { loadPartnerCriteriaSets } from '../../../redux/actions/criteriaManagement.actions';
import getIsSplitEnabled from '../../../../utils/getIsSplitEnabled';
import { getCurrentPartnerFeatureList } from '../../../../redux/selectors/partner.selectors.js';
import history from '../../../../routing/history';
import pathToRegexp from 'path-to-regexp';
import {
  getCriteriaSets,
  getCriteriaSetsLoadingStatus,
} from '../../../redux/selectors/complianceCriteriaManagement.selectors';
import { showLocalizedErrorBar } from '../../../../utils/showConfirmationBar';
import { getCurrentUser } from '../../../../redux/selectors/user.selectors';
import { generatePath } from 'react-router';
const { REDUX_LOADING_STATUS, PARTNER_SPECIFIC_FEATURES } = GLOBALS;
const { SUCCESS, NOT_LOADED, PENDING } = REDUX_LOADING_STATUS;
const { BATCH_STATUS, INDIVIDUAL_CRITERIA_RESULTS } = COMPLIANCE;
const { ERROR } = INDIVIDUAL_CRITERIA_RESULTS;
const { COMPLETE, SUBMITTED } = BATCH_STATUS;
const { BRAND_GOVERNANCE } = PARTNER_SPECIFIC_FEATURES;
const { setHasUpdatedCriteriaOrBrand } = complianceSharedSlice.actions;
const { clearCriteriaSets } = complianceCriteriaManagementSlice.actions;

const CreativeScoring = ({ routes }) => {
  const location = useLocation();
  const dispatch = useDispatch();

  /*
  Variables to store feature flag and split values
   */
  const isJAndJOptionVisible = getIsSplitEnabled(J_AND_J_DAM_SPLIT);
  /*
  Variables to store selector data
   */
  const partnerFeatureList = useSelector((state) =>
    getCurrentPartnerFeatureList(state),
  );
  const partnerBatches = useSelector((state) => getPartnerBatches(state));
  const partnerId = useSelector((state) => getCurrentPartnerId(state));
  const isBatchSelected = useSelector((state) => getIsBatchSelected(state));
  const selectedBatch = useSelector((state) => getSelectedScorecard(state));
  const isBatchProcessing =
    isBatchSelected && ![COMPLETE].includes(selectedBatch.status);
  const currentAdAccountId = useSelector((state) =>
    getCurrentAdAccountId(state),
  );
  const currentAdAccountPlatform = useSelector((state) =>
    getCurrentAdAccountPlatform(state),
  );
  const criteriaSetsLoadingStatus = useSelector((state) =>
    getCriteriaSetsLoadingStatus(state),
  );
  const isJnJSegmentSelected = useSelector((state) =>
    getIsJAndJSegmentSelected(state),
  );
  const mediaIds = useSelector((state) => getMediaIds(state));
  const blankState = useSelector((state) => hasBlankState(state));
  const criteriaSets = useSelector((state) => getCriteriaSets(state));
  const currentUser = useSelector((state) => getCurrentUser(state));
  const partnerPermissions = useSelector((state) =>
    getCurrentPartnerPermissions(state),
  );
  const countryList = useSelector(getCountriesList);

  const hasUserUpdatedCriteriaThisSession = useSelector((state) =>
    getHasUpdatedCriteriaOrBrand(state),
  );
  // const [isReminderOpen, setIsReminderOpen] = useState(false);
  const complianceRegexp = pathToRegexp(siteMap.creativeIntelligenceCompliance);
  const isPartnerCriteriaSetsLoading = [PENDING].includes(
    criteriaSetsLoadingStatus,
  );
  const tabRoutes = routeParams.tabs.creativeIntelligenceCompliance;

  const currentPartner = currentUser.partners;

  // location observers
  const isBatchSubmitted =
    isBatchSelected && [SUBMITTED].includes(selectedBatch.status);
  const isScoreCardsLandingTabSelected = location.pathname.endsWith(
    tabRoutes.scorecardsLanding,
  );
  const isCreatingNewBrandAuditReport = location.pathname.endsWith(
    tabRoutes.submissionReport,
  );
  const isScorecardsLandingPreflightTabSelected = location.pathname.includes(
    'scorecards/pre-flight',
  );
  const isScorecardsLandingInflightTabSelected = location.pathname.includes(
    'scorecards/in-flight',
  );
  const isReportsOrScorecardsLandingTabSelected =
    isScoreCardsLandingTabSelected ||
    isScorecardsLandingPreflightTabSelected ||
    isScorecardsLandingInflightTabSelected;

  const hasDashboardAccess = useRef(false);

  if (
    location.pathname.match(complianceRegexp) &&
    !partnerFeatureList[BRAND_GOVERNANCE]
  ) {
    // If user is not allowed to access the page, redirect to the try page
    history.push(generatePath(siteMap.try, { featureName: 'scoring' }));
  }

  useEffect(() => {
    for (const prop in currentPartner) {
      if (
        partnerPermissions.canUpdatePartnerConfiguration() &&
        currentPartner[prop].id === partnerId
      ) {
        hasDashboardAccess.current = true;
      }
    }

    // clear any stale criteria sets when component mounts
    if (criteriaSetsLoadingStatus === SUCCESS) {
      dispatch(clearCriteriaSets());
    }
  }, [partnerId]);

  // sets the countries
  useEffect(() => {
    if (!countryList || countryList.length === 0) {
      dispatch(retrieveCountriesList());
    }
  }, [partnerId]);

  // Clear accordion state when switching batches
  useEffect(() => {
    if (selectedBatch?.status === ERROR) {
      showLocalizedErrorBar('blank.compliance.batchNoData.error.message');
    }
  }, [selectedBatch]);

  useEffect(() => {
    // If user has made changes to criteria or brand name this session
    // And wants to view reports, reload them
    if (
      hasUserUpdatedCriteriaThisSession &&
      isReportsOrScorecardsLandingTabSelected
    ) {
      dispatch(setFilteredBatches());
      dispatch(
        setHasUpdatedCriteriaOrBrand({ hasUpdatedCriteriaOrBrand: false }),
      );
    }
  }, [
    hasUserUpdatedCriteriaThisSession,
    isReportsOrScorecardsLandingTabSelected,
  ]);

  useEffect(() => {
    // Load criteria if they have never been loaded
    if (criteriaSetsLoadingStatus === NOT_LOADED) {
      dispatch(loadPartnerCriteriaSets(partnerId, true));
    }
  }, [partnerId, criteriaSetsLoadingStatus]);

  return (
    <>
      {
        <div id="compliance">
          {isCreatingNewBrandAuditReport ? (
            <CreativeScoringPreFlightScorecard />
          ) : (
            <>
              <section id="compliance-content-area">
                {routesRenderer(routes, {
                  partnerId,
                  selectedBatch,
                  isBatchSelected,
                  currentAdAccountId,
                  currentAdAccountPlatform,
                  mediaIds,
                  hasBlankState: blankState,
                  isJnJSegmentSelected,
                  criteriaSets,
                  partnerBatches,
                  isBatchProcessing,
                  isBatchSubmitted,
                  isPartnerCriteriaSetsLoading,
                  isJAndJOptionVisible,
                })}
              </section>
            </>
          )}
        </div>
      }
    </>
  );
};

CreativeScoring.propTypes = {
  routes: array.isRequired,
};

export default CreativeScoring;
