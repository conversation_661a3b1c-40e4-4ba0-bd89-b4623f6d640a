import { useScoringPagination } from '../../shared/ScoringDataGrid/ScoringDataGridProps/useScoringPagination';

export interface PaginationResult {
  page: number;
  pageSize: number;
  rowCount: number;
  handleChangePage: (page: number) => void;
  resetPage: () => void;
}

const useCriteriaPagination = (): PaginationResult => {
  const { paginationModel, paginationProps } = useScoringPagination();

  const rowCount = paginationProps?.rowCount ?? 0;
  const onPaginationModelChange =
    paginationProps?.onPaginationModelChange ?? (() => {});

  const page = paginationModel?.page ?? 0;
  const pageSize = paginationModel?.pageSize ?? 25;

  const handleChangePage = (newPage: number) => {
    onPaginationModelChange({ ...paginationModel, page: newPage });
  };

  const resetPage = () => {
    onPaginationModelChange({ ...paginationModel, page: 0 });
  };

  return {
    rowCount,
    page,
    pageSize,
    handleChangePage,
    resetPage,
  };
};

export default useCriteriaPagination;
