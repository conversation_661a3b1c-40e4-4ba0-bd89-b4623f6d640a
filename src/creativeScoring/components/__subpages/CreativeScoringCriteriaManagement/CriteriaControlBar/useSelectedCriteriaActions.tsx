import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import {
  AssignDeassignPayload,
  useCriteriaGroupsApi,
} from '../../../criteriaManagement/CriteriaGroupingManagement/hooks/useCriteriaGroupsApi';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import { displayToastAlert } from '../../../../../muiCustomComponents/ToastAlert/displayToastAlert';
import { translationKeys } from '../../../criteriaManagement/CriteriaGroupingManagement/constants';
import criteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';
import {
  AddToGroupButton,
  ListItem,
  getButtonStyles,
} from '../../../ScoringLandingFilters/ScoringLandingControlBar/AddToGroupButton';
import { useCriteriaGroupsList } from '../../../ScoringLandingFilters/ScoringLandingFilters/ListItemsHooks/useCriteriaGroupsList';
import { Divider } from '../../../../../muiCustomComponents/ListCount/ListCount';
import { VidMobButton } from '../../../../../vidMobComponentWrappers';
import { IconSelectionModal } from '../../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModal';
import { Criteria } from '../../../../../types/criteria.types';
import { useAttachIconsToCriteria } from '../../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModalHooks';
import { CustomIcon } from '../../../criteriaManagement/CriteriaManagementModals/IconSelectionModal/IconSelectionModal.types';
import { getOrganizationPermissions } from '../../../../../userManagement/redux/selectors/organization.selectors';
import { getCurrentPartnerPermissions } from '../../../../../redux/selectors/partner.selectors';
import { useCriteriaGrouping } from '../../../criteriaManagement/CriteriaGroupingManagement/context/CriteriaGroupingContext';

type MappedCriteria = { criteriaGroups: string[]; id: number };

const { loadCriteria } = criteriaManagementSlice.actions;
const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');
const {
  FORM: {
    TOAST: { EDIT_ERROR },
  },
} = translationKeys;

const useSelectedCriteriaActions = (
  selectedCriteria: GridRowSelectionModel,
  setSelectedCriteria: (selectedCriteria: GridRowSelectionModel) => void,
  criteria: Criteria[],
) => {
  const dispatch = useDispatch();
  const intl = useIntl();
  const { isRefreshNeededForAddingToCriteriaGroups } = useCriteriaGrouping();
  const criteriaGroupsList = useCriteriaGroupsList(
    isRefreshNeededForAddingToCriteriaGroups,
  );
  const { mutate: attachIconsToCriteria } = useAttachIconsToCriteria();
  const { assignDeassignCriteriaGroup } = useCriteriaGroupsApi();

  const [showAddToGroupDropDown, setShowAddToGroupDropDown] =
    useState<boolean>(false);
  const [isIconModalOpen, setIsIconModalOpen] = useState(false);

  const hasSelectedCriteria = Boolean(
    selectedCriteria && selectedCriteria.length > 0,
  );
  const orgPermissions = useSelector(getOrganizationPermissions);
  const partnerPermissions = useSelector(getCurrentPartnerPermissions);

  const canAddCriteriaToCriteriaGroupForOrg =
    orgPermissions?.canAddCriteriaToCriteriaGroup();
  const canAddCriteriaToCriteriaGroupForWorkspace =
    partnerPermissions?.canAddCriteriaToCriteriaGroup();

  const hasPermissionsToAddCriteriaToGroup =
    canAddCriteriaToCriteriaGroupForOrg ||
    canAddCriteriaToCriteriaGroupForWorkspace;

  const criteriaList: MappedCriteria[] = useMemo(() => {
    return criteria.map((criteria: Criteria) => ({
      id: criteria.id,
      criteriaGroups: criteria?.criteriaGroups
        ? criteria.criteriaGroups.map((crtGr: { id: string }) => crtGr.id)
        : [],
    }));
  }, [criteria]);

  useEffect(() => {
    if (isCriteriaGroupsEnabled && hasPermissionsToAddCriteriaToGroup) {
      const shouldShowDropdown = hasSelectedCriteria;

      if (showAddToGroupDropDown !== shouldShowDropdown) {
        setShowAddToGroupDropDown(shouldShowDropdown);
      }
    }
  }, [selectedCriteria]);

  const handleAddToGroupApply = async (
    items: { id: string; type: string }[],
  ) => {
    setSelectedCriteria && setSelectedCriteria([]);
    const { selectedCriteriaGroups, unselectedCriteriaGroups } = items.reduce<{
      selectedCriteriaGroups: AssignDeassignPayload['selectedCriteriaGroups'];
      unselectedCriteriaGroups: AssignDeassignPayload['unselectedCriteriaGroups'];
    }>(
      (acc, { id, type }) => {
        if (type === 'add') acc.selectedCriteriaGroups.push(id);
        else if (type === 'remove') acc.unselectedCriteriaGroups.push(id);
        return acc;
      },
      { selectedCriteriaGroups: [], unselectedCriteriaGroups: [] },
    );

    try {
      await assignDeassignCriteriaGroup.mutateAsync({
        criteriaIds: selectedCriteria!,
        unselectedCriteriaGroups,
        selectedCriteriaGroups,
      });
      dispatch(loadCriteria({}));
    } catch (error) {
      displayToastAlert({
        message: intl.messages[EDIT_ERROR],
        type: 'error',
      });
    }
  };

  const handleIconModalSubmit = (icon: CustomIcon | null) => {
    if (!icon || !selectedCriteria || selectedCriteria.length === 0) {
      return;
    }
    const iconCriteriaMappings = selectedCriteria.map((criteriaId) => ({
      criteriaId: Number(criteriaId),
      key: icon.key,
    }));
    attachIconsToCriteria({ iconCriteriaMappings });
    dispatch(loadCriteria({}));

    setIsIconModalOpen(false);
  };

  const getAdditionalActionButtons = () => {
    if (!hasSelectedCriteria) return null;

    const addCriteriaToGroupButton = showAddToGroupDropDown ? (
      <>
        <Divider />
        <AddToGroupButton
          listItems={criteriaGroupsList as ListItem[]}
          onApply={handleAddToGroupApply}
          selectedCriteria={selectedCriteria}
          criteriaList={criteriaList}
        />
      </>
    ) : null;

    const addIconToCriteriaButton = (
      <>
        <Divider />
        <VidMobButton
          variant="outlined"
          color="primary"
          onClick={() => setIsIconModalOpen(true)}
          size="small"
          sx={{
            ...getButtonStyles(false),
            fontWeight: 600,
            fontSize: '14px',
          }}
        >
          {intl.formatMessage({
            id: 'ui.compliance.criteriaManagement.iconSelection.button.text',
            defaultMessage: 'Add icon',
          })}
        </VidMobButton>
        <IconSelectionModal
          isOpen={isIconModalOpen}
          handleClose={() => setIsIconModalOpen(false)}
          handleSubmit={(icon) => handleIconModalSubmit(icon)}
          criteriaCount={selectedCriteria.length}
          selectedIconsIds={
            criteria
              .filter((criteria: Criteria) =>
                selectedCriteria.includes(criteria.id),
              )
              .map((criteria: Criteria) => criteria.customIconId)
              .filter(Boolean) as string[]
          }
        />
      </>
    );

    return (
      <>
        {addCriteriaToGroupButton}
        {addIconToCriteriaButton}
      </>
    );
  };

  return {
    additionalActionButtons: getAdditionalActionButtons(),
    criteriaGroupsList,
  };
};

export default useSelectedCriteriaActions;
