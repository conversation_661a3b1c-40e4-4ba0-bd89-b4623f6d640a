import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import { VidMobBox, VidMobStack } from '../../../../../vidMobComponentWrappers';
import FiltersControlBar from '../../../../../muiCustomComponents/ControlBarFilters/ControlBar';
import SavedReportsSearch from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsSearch';
import ListCount from '../../../../../muiCustomComponents/ListCount/ListCount';
import { useCriteriaSearch } from './useCriteriaSearch';
import { useCriteriaListItems } from '../../../ScoringLandingFilters/ScoringLandingFilters/ListItemsHooks/useCriteriaListItems';
import useSelectedCriteriaActions from './useSelectedCriteriaActions';
import {
  CRITERIA_GROUP_FILTER,
  CRITERIA_LANDING_PAGE_FILTERS,
} from '../../../ScoringLandingFilters/scoringLandingFilters.constants';
import criteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';
import {
  getCriteriaFilters,
  getCriteriaPagination,
  getIsLoading,
} from '../../../../redux/selectors/criteriaManagement.selectors';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import { FilterSelectedValueType } from '../../../ScoringLandingFilters/scoringLandingFilters.types';
import {
  LandingPageContext,
  LandingPageFilters,
} from '../../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import { Criteria } from '../../../../../types/criteria.types';

const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');
const { setCriteriaFilter } = criteriaManagementSlice.actions;

const CriteriaControlBar = ({
  page,
  pageSize,
  handleChangePage,
  rowCount,
  resetPage,
  selectedCriteria,
  setSelectedCriteria,
  criteria,
}: {
  page: number;
  pageSize: number;
  handleChangePage: (page: number) => void;
  rowCount: number;
  resetPage: () => void;
  selectedCriteria: GridRowSelectionModel;
  setSelectedCriteria: (selectedCriteria: GridRowSelectionModel) => void;
  criteria: Criteria[];
}) => {
  const dispatch = useDispatch();

  const { searchTerm, setSearchTerm } = useCriteriaSearch();
  const { additionalActionButtons, criteriaGroupsList } =
    useSelectedCriteriaActions(selectedCriteria, setSelectedCriteria, criteria);
  const filtersListItems = useCriteriaListItems(criteriaGroupsList);

  const isLoading = useSelector(getIsLoading);
  const filters = useSelector(getCriteriaFilters);
  const { totalSize } = useSelector(getCriteriaPagination) || {};

  const handleSearchChange = (search: string) => {
    resetPage();
    setSearchTerm(search);
  };

  const handleDispatchFilterChange = ({
    filterId,
    value,
  }: {
    filterId: string;
    value: FilterSelectedValueType;
  }) => {
    dispatch(
      setCriteriaFilter({
        filter: { field: filterId, value },
      }),
    );
    resetPage();
  };

  const criteriaFilters =
    isCriteriaGroupsEnabled &&
    !CRITERIA_LANDING_PAGE_FILTERS.includes(CRITERIA_GROUP_FILTER)
      ? [...CRITERIA_LANDING_PAGE_FILTERS, CRITERIA_GROUP_FILTER]
      : CRITERIA_LANDING_PAGE_FILTERS;

  return (
    <VidMobBox
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-start',
        gap: '8px',
      }}
    >
      <ListCount
        count={totalSize}
        selectedItemsCount={selectedCriteria?.length}
        itemsIntlKey="ui.creativeScoring.landingPages.numberAndTypeOfListItems.criteria"
        selectedItemsIntlKey="ui.creativeScoring.landingPages.numberAndTypeOfListItems.criteria.selected"
        isLoading={isLoading}
      />
      <VidMobBox sx={{ flex: 1 }}>
        <FiltersControlBar
          enablePagination
          pageSize={pageSize}
          page={page}
          totalCount={rowCount}
          onPageChange={handleChangePage}
          searchComponent={
            <VidMobStack direction="row" spacing={4} alignItems="center">
              <SavedReportsSearch
                searchTerm={searchTerm}
                setSearchTerm={handleSearchChange}
                isDisabled={isLoading}
                isClearable
              />
            </VidMobStack>
          }
          // @ts-ignore
          filters={criteriaFilters as LandingPageFilters[]}
          filtersListItems={filtersListItems}
          handleDispatchFilterChange={handleDispatchFilterChange}
          savedFilterParams={filters}
          context={LandingPageContext.CRITERIA}
          additionalActionButtons={additionalActionButtons}
        />
      </VidMobBox>
    </VidMobBox>
  );
};

export default CriteriaControlBar;
