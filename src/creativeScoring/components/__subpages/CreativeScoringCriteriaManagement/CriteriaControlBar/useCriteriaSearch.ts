import { useState } from 'react';
import { useDispatch } from 'react-redux';
import useDebounce from '../../../../../hooks/useDebounce';
import { useDidUpdateEffect } from '../../../../../hooks/useDidUpdateEffect';
import CriteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';

const { setCriteriaSearchText } = CriteriaManagementSlice.actions;

interface UseCriteriaSearchResult {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}

export const useCriteriaSearch = (): UseCriteriaSearchResult => {
  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  useDidUpdateEffect(() => {
    dispatch(setCriteriaSearchText({ searchText: debouncedSearchTerm }));
  }, [debouncedSearchTerm]);

  return { searchTerm, setSearchTerm };
};
