import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CriteriaManagementDataGrid from '../../criteriaManagement/CriteriaManagementDataGrid';
import CriteriaManagementStates from '../../criteriaManagement/CriteriaManagementStates';
import CriteriaManagementHeader from '../../criteriaManagement/CriteriaManagementHeader';
import criteriaManagementSlice from '../../../redux/slices/criteriaManagement.slice';
import {
  getCriteriaData,
  getBrandIdentifiersData,
  getHasNoData,
  getHasFailed,
  getHasNoFilterResults,
  getHasNoSearchResults,
  getCriteriaSearchText,
  getCriteriaPagination,
  getCriteriaFilters,
  getCriteriaSorting,
  getHaveBrandIdentifiersLoaded,
  getCanUserUpdateCriteria,
  getIsLoading,
} from '../../../redux/selectors/criteriaManagement.selectors';
import BrandIdentifierBanner from '../../criteriaManagement/CriteriaManagementModals/SetBrandIdentifiersModal/BrandIdentifierBanner';
import { useDidUpdateEffect } from '../../../../hooks/useDidUpdateEffect';
import { CriteriaGroupingProvider } from '../../criteriaManagement/CriteriaGroupingManagement/context/CriteriaGroupingProvider';
import { GridRowSelectionModel } from '@mui/x-data-grid-pro';
import useCriteriaPagination from './useCriteriaPagination';
import CriteriaControlBar from './CriteriaControlBar/CriteriaControlBar';

export type MappedCriteria = { criteriaGroups: string[]; id: number };

const {
  loadCriteria,
  loadBestPractices,
  loadBrandIdentifiers,
  loadAvailableFilterOptions,
  loadCriteriaTemplates,
  reset,
} = criteriaManagementSlice.actions;

const CreativeScoringCriteriaManagement = () => {
  const dispatch = useDispatch();

  const criteria = useSelector(getCriteriaData);
  const searchText = useSelector(getCriteriaSearchText);
  const pagination = useSelector(getCriteriaPagination);
  const filters = useSelector(getCriteriaFilters);
  const { sortOrder, sortBy } = useSelector(getCriteriaSorting);

  const [currentModal, setCurrentModal] = useState<string | null>(null);
  const [isBrandIdentifierBannerOpen, setIsBrandIdentifierBannerOpen] =
    useState<boolean>(false);
  const [criteriaRowSelectionModel, setCriteriaRowSelectionModel] =
    useState<GridRowSelectionModel>([]);

  const isLoading = useSelector(getIsLoading);
  const hasFailed = useSelector(getHasFailed);
  const hasNoData = useSelector(getHasNoData);
  const hasNoFilterResults = useSelector(getHasNoFilterResults);
  const hasNoSearchResults = useSelector(getHasNoSearchResults);

  const isBlankState =
    hasNoData || hasFailed || hasNoFilterResults || hasNoSearchResults;
  const hideControlBar = hasNoData || hasFailed;
  const brandIdentifiers = useSelector(getBrandIdentifiersData) || '';
  const haveBrandIdentifiersLoaded = useSelector(getHaveBrandIdentifiersLoaded);

  const canUserUpdateCriteria = useSelector(getCanUserUpdateCriteria);

  const paginationState = useCriteriaPagination();
  const { rowCount, page, pageSize, handleChangePage, resetPage } =
    paginationState;

  const criteriaManagementContent = useMemo(
    () => (
      <section id="criteria-management">
        <div className="header">
          <CriteriaManagementHeader
            brandIdentifiers={brandIdentifiers}
            hasNoData={hasNoData}
            currentModal={currentModal}
            setCurrentModal={setCurrentModal}
          />
          {isBrandIdentifierBannerOpen && haveBrandIdentifiersLoaded && (
            <BrandIdentifierBanner
              setIsOpen={setIsBrandIdentifierBannerOpen}
              setCurrentModal={setCurrentModal}
            />
          )}
        </div>
        <CriteriaControlBar
          page={page}
          pageSize={pageSize}
          handleChangePage={handleChangePage}
          rowCount={rowCount}
          resetPage={resetPage}
          selectedCriteria={criteriaRowSelectionModel}
          setSelectedCriteria={setCriteriaRowSelectionModel}
          criteria={criteria}
        />
        {isBlankState ? (
          <CriteriaManagementStates
            hasNoData={hasNoData}
            hasFailed={hasFailed}
            hasNoFilterResults={hasNoFilterResults}
            hasNoSearchResults={hasNoSearchResults}
            setCurrentModal={setCurrentModal}
          />
        ) : (
          <CriteriaManagementDataGrid
            criteria={criteria}
            isHeaderTaller={isBrandIdentifierBannerOpen}
            isLoading={isLoading}
            criteriaRowSelectionModel={criteriaRowSelectionModel}
            setCriteriaRowSelectionModel={setCriteriaRowSelectionModel}
          />
        )}
      </section>
    ),
    [
      brandIdentifiers,
      hasNoData,
      currentModal,
      isBrandIdentifierBannerOpen,
      haveBrandIdentifiersLoaded,
      hideControlBar,
      isBlankState,
      hasFailed,
      hasNoFilterResults,
      hasNoSearchResults,
      criteria,
      isLoading,
      criteriaRowSelectionModel,
    ],
  );

  useEffect(() => {
    if (
      haveBrandIdentifiersLoaded &&
      brandIdentifiers?.length === 0 &&
      canUserUpdateCriteria
    ) {
      setIsBrandIdentifierBannerOpen(true);
    }
  }, [brandIdentifiers, haveBrandIdentifiersLoaded]);

  useEffect(() => {
    dispatch(loadAvailableFilterOptions({}));
    dispatch(loadCriteria({}));
    if (canUserUpdateCriteria) {
      dispatch(loadBestPractices({}));
      dispatch(loadBrandIdentifiers({}));
      dispatch(loadCriteriaTemplates({}));
    }

    return () => {
      dispatch(reset({}));
    };
  }, []);

  useDidUpdateEffect(() => {
    dispatch(loadCriteria({}));
  }, [
    pagination.offset,
    pagination.perPage,
    searchText,
    filters,
    sortOrder,
    sortBy,
  ]);

  return (
    <CriteriaGroupingProvider>
      {criteriaManagementContent}
    </CriteriaGroupingProvider>
  );
};

export default CreativeScoringCriteriaManagement;
