import React, { useMemo } from 'react';
import CriteriaDetailsModal from './CriteriaDetailsModal';
import { Criterion } from '../../types/criteriaManagement.types';
import { CRITERIA_CATEGORIES } from '../../constants/criteriaManagement.constants';

interface CriteriaDetailsModalWrapperProps {
  isOpen: boolean;
  isLoading?: boolean;
  isError?: boolean;
  onClose: () => void;
  criterion: Criterion | null;
  modalType?: string;
}

const CriteriaDetailsModalWrapper = (
  props: CriteriaDetailsModalWrapperProps,
) => {
  const { isOpen, onClose, criterion, isLoading, isError, modalType } = props;
  const {
    name = '',
    defaultDisplayName = '',
    isBestPractice = false,
    rule = '',
    description = '',
    platform = '',
    mediaTypes = '',
    workspaces = '',
    category = null,
    creativeExamples = null,
    customIconUrl = '',
    criteriaGroups = [],
  } = criterion || {};

  const criteriaDetails = useMemo(() => {
    return {
      name: name || defaultDisplayName,
      type: defaultDisplayName,
      rule: rule
        ? {
            rule,
            isBestPractice,
          }
        : null,
      ...(category !== CRITERIA_CATEGORIES.CUSTOM && { description }),
      channel: platform,
      criteriaGroups,
      creativeTypes: mediaTypes,
      workspaces,
      category,
      creativeExamples: creativeExamples ? creativeExamples : null,
      customIconUrl,
    };
  }, [criterion]);

  if (!criteriaDetails) {
    return null;
  }

  return (
    <CriteriaDetailsModal
      isLoading={isLoading}
      isError={isError}
      isOpen={isOpen}
      onClose={onClose}
      criteriaDetails={criteriaDetails}
      modalType={modalType}
    />
  );
};

export default CriteriaDetailsModalWrapper;
