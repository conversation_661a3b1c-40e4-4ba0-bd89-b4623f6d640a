import React from 'react';
import { CriteriaDetailsType } from './criteriaDetailsTypes';
import {
  CriteriaDetailsErrorState,
  getValidCriteriaDetails,
} from './criteriaDetailsUtils';
import { CloseIcon } from '../../../assets/vidmob-mui-icons/general';
import CenterCircularProgress from '../../../userManagement/components/CenterCircularProgress';
import {
  VidMobAvatar,
  VidMobDialog,
  VidMobDialogContent,
  VidMobDivider,
  VidMobIconButton,
  VidMobStack,
  VidMobTypography,
} from '../../../vidMobComponentWrappers';

const closeIconStyle = {
  color: '#212121',
  cursor: 'pointer',
  width: '36px',
  height: '36px',
  padding: '6px',
  background: '#E0E0E0',
  borderRadius: '6px',
};

interface CriteriaDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  criteriaDetails: CriteriaDetailsType;
  isLoading?: boolean;
  isError?: boolean;
  modalType?: string;
}

const CriteriaDetailsModal = ({
  isOpen,
  onClose,
  criteriaDetails,
  isLoading,
  isError,
  modalType,
}: CriteriaDetailsModalProps) => {
  const { name, customIconUrl, ...otherDetails } = criteriaDetails;

  const renderModalContent = () => {
    if (isLoading) {
      return <CenterCircularProgress />;
    }

    if (isError) {
      return <CriteriaDetailsErrorState />;
    }

    return (
      <>
        <VidMobStack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          sx={{ padding: '24px' }}
        >
          <VidMobStack direction="row" alignItems="center">
            {customIconUrl && (
              <VidMobAvatar
                src={customIconUrl}
                sx={{ height: '24px', width: '24px', marginRight: '8px' }}
              />
            )}
            <VidMobTypography variant="h6">{name}</VidMobTypography>
          </VidMobStack>
          <VidMobIconButton onClick={onClose} sx={closeIconStyle}>
            <CloseIcon />
          </VidMobIconButton>
        </VidMobStack>
        <VidMobDivider />
        <VidMobDialogContent>
          {getValidCriteriaDetails(otherDetails, modalType, name)}
        </VidMobDialogContent>
      </>
    );
  };

  return (
    <VidMobDialog
      fullWidth
      maxWidth="sm"
      open={isOpen}
      PaperProps={{
        sx: {
          minHeight: '500px',
          display: 'flex',
          justifyContent: 'center',
        },
      }}
      onClose={onClose}
      className="criteria-details-modal"
    >
      {renderModalContent()}
    </VidMobDialog>
  );
};

export default CriteriaDetailsModal;
