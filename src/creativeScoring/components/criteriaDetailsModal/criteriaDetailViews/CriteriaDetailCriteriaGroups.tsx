import React, { useMemo } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import {
  chipBaseStyles,
  getTextColor,
} from '../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells/CriteriaGroupCell';
import { CriteriaGroup } from '../../criteriaManagement/CriteriaGroupingManagement/context/CriteriaGroupingContext';
import { TITLES_PREFIX } from '../criteriaDetailsConstants';

interface Props {
  detailKey: string;
  detailValue: CriteriaGroup[];
}

const MAX_ROWS = 2; // Maximum number of rows
const ROW_HEIGHT = 34; // Height of each row
const ROW_GAP = 4; // Gap between rows
const MAX_HEIGHT = MAX_ROWS * ROW_HEIGHT + (MAX_ROWS - 1) * ROW_GAP; // Maximum height of the container
const MAX_CONTAINER_WIDTH = 552; // Maximum width of the container

const CriteriaDetailCriteriaGroups = ({ detailKey, detailValue }: Props) => {
  const intl = useIntl();

  // Calculate which chips will be visible and which will be hidden
  const { visibleChips, remainingChips } = useMemo(() => {
    let currentRowWidth = 0; // Tracks the current row width
    let currentRowCount = 1; // Tracks the current row count
    const visible: CriteriaGroup[] = []; // Chips that will be visible
    const remaining: CriteriaGroup[] = []; // Chips that will be collapsed

    for (const group of detailValue) {
      const chipWidth = group.name.length * 8; // Estimate chip width based on text length

      if (
        currentRowWidth + chipWidth > MAX_CONTAINER_WIDTH && // If chip doesn't fit in the current row
        currentRowCount < MAX_ROWS // And there are still rows available
      ) {
        currentRowWidth = chipWidth; // Start a new row
        currentRowCount++;
        visible.push(group);
      } else if (currentRowWidth + chipWidth <= MAX_CONTAINER_WIDTH) {
        // If the chip fits in the current row
        currentRowWidth += chipWidth + ROW_GAP;
        visible.push(group);
      } else {
        // Chips that don't fit in the allowed rows go into the remaining group
        remaining.push(group);
      }
    }

    return { visibleChips: visible, remainingChips: remaining };
  }, [detailValue]);

  const renderContent = () => {
    if (!detailValue?.length) {
      return (
        <VidMobTypography variant="body2">
          {intl.formatMessage({
            id: 'ui.criteria.details.modal.titles.criteriaGroups.none',
            defaultMessage: 'None',
          })}
        </VidMobTypography>
      );
    }

    return (
      <VidMobBox
        sx={{
          display: 'flex',
          flexWrap: 'wrap', // Allow wrapping to multiple rows
          gap: `${ROW_GAP}px`,
          maxHeight: `${MAX_HEIGHT}px`,
          overflow: 'hidden', // Hide content that exceeds the allowed height
          alignContent: 'flex-start', // Align rows to the top
        }}
      >
        {/* Render visible chips */}
        {visibleChips.map((criteriaGroup) => (
          <VidMobBox
            key={criteriaGroup.id}
            sx={{
              ...chipBaseStyles,
              backgroundColor: criteriaGroup.color,
              color: getTextColor(criteriaGroup.color),
              padding: '6px 8px',
            }}
          >
            {criteriaGroup.name}
          </VidMobBox>
        ))}
        {/* Render collapsed chips with a tooltip */}
        {remainingChips.length > 0 && (
          <VidMobTooltip
            title={remainingChips.map((group) => group.name).join(', ')}
            arrow
          >
            <VidMobBox
              sx={{
                ...chipBaseStyles,
                backgroundColor: '#E0E0E0',
                color: '#212121',
                cursor: 'pointer',
                padding: '6px 8px',
              }}
            >
              +{remainingChips.length}
            </VidMobBox>
          </VidMobTooltip>
        )}
      </VidMobBox>
    );
  };

  return (
    <VidMobBox sx={{ marginBottom: '24px' }}>
      <VidMobTypography variant="subtitle2" sx={{ marginBottom: '8px' }}>
        {intl.messages[`${TITLES_PREFIX}.${detailKey}`] as string}
      </VidMobTypography>
      {renderContent()}
    </VidMobBox>
  );
};

export default CriteriaDetailCriteriaGroups;
