import React, { useState, useRef } from 'react';
import { Box, Stack, Typography, CardMedia } from '@mui/material';
import { PlatformMediaObjectValues } from '../../../../creativeAnalytics/__pages/CriteriaPerformanceReport/CriteriaPerformanceReportTypes';
import MediaPreview from '../../../../creativeAnalytics/components/MediaPreview/MediaPreview';
import {
  getPassOrFailHeader,
  noCriteriaExamples,
  getZipFileName,
} from './CreativeExampleDetailUtils';
import { useIntl } from 'react-intl';
import { downloadAndZip } from '../../../../creativeAnalytics/__pages/CreativeManager/helpers/CreativeManagerUtils';

interface Props {
  mediaExamples: PlatformMediaObjectValues[];
  pass: boolean;
  criteriaName?: string;
}

const thumbnailSx = {
  height: '90px',
  width: '122px',
  borderRadius: '4px',
};

const CreativeExampleDetail = (props: Props) => {
  const { mediaExamples, pass, criteriaName } = props;
  const [activeMedia, setActiveMedia] =
    useState<PlatformMediaObjectValues | null>(null);
  const anchorRef = useRef<HTMLDivElement | null>(null);
  const intl = useIntl();

  const handleMouseEnter = (mediaExample: PlatformMediaObjectValues) => {
    setActiveMedia(mediaExample);
  };

  const handleMouseLeave = () => {
    setActiveMedia(null);
  };

  const hasMediaExamples = mediaExamples.length > 0;

  return (
    <Stack sx={{ justifyContent: 'flex-start' }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          paddingBottom: '8px',
          width: '537px',
        }}
      >
        {getPassOrFailHeader(pass, intl)}
        {hasMediaExamples && (
          <Box
            onClick={() =>
              downloadAndZip(
                mediaExamples.map((media) => ({
                  url: media.media.downloadUrl,
                  fileName: media.media.name,
                })),
                getZipFileName(pass, criteriaName),
              )
            }
            sx={{ cursor: 'pointer' }}
          >
            <Typography
              variant="subtitle2"
              color="primary"
              sx={{ '&:hover': { textDecoration: 'underline' } }}
            >
              {
                intl.messages[
                  'ui.criteria.details.modal.criteria.download'
                ] as string
              }
            </Typography>
          </Box>
        )}
      </Box>
      <Stack direction="row" gap={8}>
        {hasMediaExamples
          ? mediaExamples.map((mediaExample) => (
              <Box
                key={mediaExample.media.id}
                onMouseEnter={() => handleMouseEnter(mediaExample)}
                onMouseLeave={handleMouseLeave}
                ref={anchorRef}
              >
                <CardMedia
                  image={mediaExample.media.thumbnails[0]?.url}
                  sx={thumbnailSx}
                />
                {activeMedia === mediaExample && (
                  <MediaPreview
                    mediaPreview={mediaExample.media}
                    rightPosition={20}
                  />
                )}
              </Box>
            ))
          : noCriteriaExamples(intl)}
      </Stack>
    </Stack>
  );
};

export default CreativeExampleDetail;
