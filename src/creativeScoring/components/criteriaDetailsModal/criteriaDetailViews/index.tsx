import React from 'react';
import { Box, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { CriteriaDetailsType, Rule } from '../criteriaDetailsTypes';
import {
  TITLES_PREFIX,
  RULE,
  CHANNEL,
  CATEGORY,
  CREATIVE_EXAMPLES,
  CRITERIA_GROUPS,
} from '../criteriaDetailsConstants';
import CriteriaDetailRule from './CriteriaDetailRule';
import CriteriaDetailChannel from './CriteriaDetailChannel';
import CriteriaDetailCategory from './CriteriaDetailCategory';
import CriteriaCreativeExamples from './CriteriaCreativeExamples';
import { CreativeExamples } from '../../../../creativeAnalytics/__pages/CriteriaPerformanceReport/CriteriaPerformanceReportTypes';
import CriteriaDetailCriteriaGroups from './CriteriaDetailCriteriaGroups';
import { CriteriaGroup } from '../../criteriaManagement/CriteriaGroupingManagement/context/CriteriaGroupingContext';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';

interface CriteriaDetailProps {
  detailKey: string;
  detailValue:
    | string
    | string[]
    | CriteriaDetailsType
    | Rule
    | CreativeExamples
    | CriteriaGroup[];
  modalType?: string;
  criteriaName?: string;
}

const CriteriaDetailView = (props: CriteriaDetailProps) => {
  const { detailKey, detailValue, criteriaName } = props;
  const intl = useIntl();

  switch (detailKey) {
    case RULE:
      return (
        <CriteriaDetailRule
          detailKey={detailKey}
          detailValue={detailValue as Rule}
        />
      );
    case CHANNEL:
      return (
        <CriteriaDetailChannel
          detailKey={detailKey}
          detailValue={detailValue as string}
        />
      );

    case CRITERIA_GROUPS:
      return getFeatureFlag('isCriteriaGroupsEnabled') ? (
        <CriteriaDetailCriteriaGroups
          detailKey={detailKey}
          detailValue={detailValue as CriteriaGroup[]}
        />
      ) : null;

    case CATEGORY:
      return (
        <CriteriaDetailCategory
          detailKey={detailKey}
          detailValue={detailValue as string[]}
        />
      );

    case CREATIVE_EXAMPLES:
      return (
        <CriteriaCreativeExamples
          detailKey={detailKey}
          detailValue={detailValue as CreativeExamples}
          criteriaName={criteriaName}
        />
      );

    default:
      return (
        <Box sx={{ marginBottom: '24px' }}>
          <Typography variant="subtitle2" sx={{ marginBottom: '8px' }}>
            {intl.messages[`${TITLES_PREFIX}.${detailKey}`] as string}
          </Typography>
          <Typography variant="body2">{detailValue as string}</Typography>
        </Box>
      );
  }
};

export default CriteriaDetailView;
