import React from 'react';
import { useIntl } from 'react-intl';
// components
import getMUIIconForChannel from '../../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import IconAndTextCell from '../../../shared/ScoringDataGrid/ScoringDataGridCells/IconAndTextCell';
import { GridCellParams } from '@mui/x-data-grid-pro';
// constants
import { COMPLIANCE } from '../../../../../constants';
import {
  getPlatformDisplayIntlText,
  getPlatformIdentifierForLogo,
} from '../../../../../utils/feConstantsUtils';

const ChannelCell = ({ params }: { params: GridCellParams }) => {
  const { value } = params;
  const platform = value as string;

  const intl = useIntl();

  const i18nName = COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.find(
    (p) => p.id === platform,
  )?.i18nName;

  const displayText =
    getPlatformDisplayIntlText(
      platform,
      intl,
      i18nName ? (intl.messages[i18nName] as string) : undefined,
    ) || null;

  const iconSxStyling = {
    height: '22px',
    width: '22px',
  };

  const platformIdentifierForLogo = getPlatformIdentifierForLogo(platform);
  const icon = getMUIIconForChannel(platformIdentifierForLogo, iconSxStyling);

  return (
    <IconAndTextCell
      displayText={displayText}
      icon={icon}
      sx={{ gap: '10px' }}
    />
  );
};

export default ChannelCell;
