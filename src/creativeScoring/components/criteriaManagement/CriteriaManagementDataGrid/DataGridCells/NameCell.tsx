import React from 'react';
import { useIntl } from 'react-intl';
import { BestPracticeIcon } from '../../../../../assets/vidmob-mui-icons/general';
import { GridCellParams } from '@mui/x-data-grid-pro';
import IconAndTextCell from '../../../shared/ScoringDataGrid/ScoringDataGridCells/IconAndTextCell';
import {
  VidMobBox,
  VidMobStack,
  VidMobTooltip,
} from '../../../../../vidMobComponentWrappers';

const NameCell = ({ params }: { params: GridCellParams }) => {
  const intl = useIntl();
  const {
    row: { isBestPractice, customIconUrl },
  } = params;
  const displayText = params.value as string;

  if (!displayText) {
    return <div>-</div>;
  }

  const icon = () => {
    if (customIconUrl) {
      // Render customIconUrl first
      return (
        <VidMobStack direction="row" alignItems="center">
          <VidMobBox
            sx={{
              height: '13px',
              width: '13px',
              mt: '-2px',
              borderRadius: '24px',
              backgroundImage: `url(${customIconUrl})`,
              backgroundSize: 'contain',
              backgroundPosition: '50% 50%',
            }}
          />
          {isBestPractice && (
            <VidMobTooltip
              title={
                intl.messages[
                  'ui.compliance.criteriaManagement.bestPractice.tooltip'
                ] as string
              }
            >
              <div>
                <BestPracticeIcon
                  sx={{
                    height: '16px',
                    width: '16px',
                    mt: '4px',
                  }}
                />
              </div>
            </VidMobTooltip>
          )}
        </VidMobStack>
      );
    } else if (isBestPractice) {
      // Only BestPractice icon
      return (
        <VidMobStack direction="row" alignItems="center">
          <VidMobTooltip
            title={
              intl.messages[
                'ui.compliance.criteriaManagement.bestPractice.tooltip'
              ] as string
            }
          >
            <div>
              <BestPracticeIcon
                sx={{
                  height: '16px',
                  width: '16px',
                  mt: '4px',
                }}
              />
            </div>
          </VidMobTooltip>
        </VidMobStack>
      );
    } else {
      // No icons
      return <></>;
    }
  };

  return (
    <IconAndTextCell
      displayText={displayText}
      conditional={true}
      icon={icon()}
      sx={{ gap: '12px' }}
    />
  );
};

export default NameCell;
