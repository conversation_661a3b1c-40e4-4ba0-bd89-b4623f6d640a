import React, { useMemo } from 'react';
import { GridCellParams } from '@mui/x-data-grid-pro';
import {
  VidMobBox,
  VidMobStack,
  VidMobTooltip,
} from '../../../../../vidMobComponentWrappers';
import { COLORS } from '../../CriteriaGroupingManagement/components/CriteriaGroupingModals/CriteriaGroupingForm/ColorPickerField';
import { SxProps } from '@mui/material';
import { GLOBALS } from '../../../../../constants';

const { EM_DASH_UNICODE } = GLOBALS;

interface CriteriaItem {
  id: number;
  name: string;
  color: string; // Background color
}

export function getTextColor(backgroundColor?: string): string {
  const defaultColor = 'text.primary';

  if (!backgroundColor) {
    return defaultColor;
  }

  const foundColor = COLORS.find(
    (colorItem) =>
      colorItem.color.toUpperCase() === backgroundColor.toUpperCase(),
  );

  return foundColor ? foundColor.borderColor : defaultColor;
}

const CHIP_MARGIN = 4;
const TEXT_WIDTH_MULTIPLIER = 8; // Approximate width per character

export const chipBaseStyles = {
  borderRadius: '100px',
  padding: '9px 10px',
  fontSize: '14px',
  fontWeight: 500,
  display: 'inline-flex',
  alignItems: 'center',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  maxHeight: '26px',
};

const grayChipStyles = {
  ...chipBaseStyles,
  color: '#212121',
  backgroundColor: '#E0E0E0',
};

const CriteriaGroupCell = ({
  params,
  chipSx,
  chipPadding = 20,
  maxWidth = 215,
}: {
  params: GridCellParams;
  chipSx?: SxProps;
  chipPadding?: number;
  maxWidth?: number;
}) => {
  const value = params.value as CriteriaItem[];

  const sortedChips = useMemo(
    () =>
      [...value].sort((a: CriteriaItem, b: CriteriaItem) =>
        a.name.localeCompare(b.name),
      ),
    [value],
  );

  const { visibleChips, remainingChips } = useMemo(() => {
    let totalWidth = 0;
    const visible: CriteriaItem[] = [];
    const remaining: CriteriaItem[] = [];

    for (const chip of sortedChips) {
      const chipWidth =
        chip.name.length * TEXT_WIDTH_MULTIPLIER + chipPadding + CHIP_MARGIN;

      if (totalWidth + chipWidth > maxWidth) {
        remaining.push(chip);
      } else {
        visible.push(chip);
        totalWidth += chipWidth;
      }
    }

    return { visibleChips: visible, remainingChips: remaining };
  }, [sortedChips]);

  if (value.length === 0) {
    return <VidMobBox>{EM_DASH_UNICODE}</VidMobBox>;
  }

  return (
    <VidMobStack direction="row" gap={2} alignItems="center">
      {value.length === 1 && remainingChips.length > 0 ? (
        <VidMobTooltip title={value[0].name}>
          <VidMobBox
            sx={{
              ...chipBaseStyles,
              ...chipSx,
              maxWidth: `${maxWidth}px`,
              backgroundColor: value[0].color,
              color: getTextColor(value[0].color),
            }}
          >
            {`${value[0].name.slice(0, maxWidth / TEXT_WIDTH_MULTIPLIER)}...`}
          </VidMobBox>
        </VidMobTooltip>
      ) : (
        <>
          {visibleChips.map((item) => (
            <VidMobBox
              key={item.id}
              sx={{
                ...chipBaseStyles,
                ...chipSx,
                backgroundColor: item.color,
                color: getTextColor(item.color),
              }}
            >
              {item.name}
            </VidMobBox>
          ))}
          {remainingChips.length > 0 && (
            <VidMobTooltip
              title={remainingChips.map((item) => item.name).join(', ')}
            >
              <VidMobBox sx={{ ...grayChipStyles, ...chipSx }}>
                +{remainingChips.length}
              </VidMobBox>
            </VidMobTooltip>
          )}
        </>
      )}
    </VidMobStack>
  );
};

export default CriteriaGroupCell;
