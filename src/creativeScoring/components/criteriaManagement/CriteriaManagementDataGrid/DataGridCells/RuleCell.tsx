import React from 'react';
import { useIntl } from 'react-intl';
import { BestPracticeIcon } from '../../../../../assets/vidmob-mui-icons/general';
import { GridCellParams } from '@mui/x-data-grid-pro';
import IconAndTextCell from '../../../shared/ScoringDataGrid/ScoringDataGridCells/IconAndTextCell';
import { VidMobTooltip } from '../../../../../vidMobComponentWrappers';

const RuleCell = ({ params }: { params: GridCellParams }) => {
  const intl = useIntl();
  const displayText = params.value as string;

  if (!displayText) {
    return <div>-</div>;
  }

  const icon = () => {
    return (
      <VidMobTooltip
        title={
          intl.messages[
            'ui.compliance.criteriaManagement.bestPractice.tooltip'
          ] as string
        }
      >
        <div>
          <BestPracticeIcon
            sx={{
              height: '16px',
              width: '16px',
              mt: '4px',
            }}
          />
        </div>
      </VidMobTooltip>
    );
  };

  return (
    <IconAndTextCell
      displayText={displayText}
      conditional={false}
      icon={icon()}
      sx={{ gap: '4px' }}
    />
  );
};

export default RuleCell;
