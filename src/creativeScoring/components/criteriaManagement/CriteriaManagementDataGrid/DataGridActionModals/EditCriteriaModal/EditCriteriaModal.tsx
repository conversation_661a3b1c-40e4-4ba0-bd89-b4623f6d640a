import React, { useEffect, useState } from 'react';
// components
import EditCriteriaModalBody from './EditCriteriaModalBody';
import EditCriteriaConfirmationBody from './EditCriteriaConfirmationBody';
// types
import { Criterion } from '../../../../../types/criteriaManagement.types';
import { GlobalCriteriaOption } from '../../../CriteriaManagementModals/ModalCreateCriteria/GlobalCriteriaSingleSelectDropdown/GlobalCriteriaSingleSelectDropdown';
// constants
import { COMPLIANCE } from '../../../../../../constants';
import { CustomIcon } from '../../../CriteriaManagementModals/IconSelectionModal/IconSelectionModal.types';

const { CRITERIA_IS_GLOBAL_OPTIONS } = COMPLIANCE;

interface EditCriteriaModalProps {
  isOpen: boolean;
  onClose: () => void;
  criterion: Criterion | null;
}

export interface EditParams {
  name: string;
  isGlobal?: boolean;
}

const EditCriteriaModal: React.FC<EditCriteriaModalProps> = ({
  isOpen,
  onClose,
  criterion,
}) => {
  const { name: savedName, isGlobal: savedIsGlobal } = criterion || {};

  const [isSubmitButtonEnabled, setIsSubmitButtonEnabled] =
    React.useState<boolean>(false);
  const [showGlobalCriteriaWarning, setShowGlobalCriteriaWarning] =
    React.useState<boolean>(false);
  const [showConfirmationModal, setShowConfirmationModal] =
    React.useState<boolean>(false);

  const [name, setName] = React.useState<string>(savedName || '');
  const [icon, setIcon] = useState<CustomIcon | null>(null);

  const [isGlobalCriteria, setIsGlobalCriteria] = React.useState<
    GlobalCriteriaOption | null | undefined
  >(null);

  useEffect(() => {
    const savedIsGlobalCriteriaOption = Object.values(
      CRITERIA_IS_GLOBAL_OPTIONS,
    ).find((option) => Boolean(option.id) === savedIsGlobal);

    setName(savedName || '');
    setIsGlobalCriteria(savedIsGlobalCriteriaOption);
  }, [savedName, savedIsGlobal, isOpen]);

  useEffect(() => {
    const nameUnchanged = savedName === name;
    const isGlobalCriteriaUnchanged =
      savedIsGlobal === Boolean(isGlobalCriteria?.id);

    if (name?.length === 0 || (nameUnchanged && isGlobalCriteriaUnchanged)) {
      setIsSubmitButtonEnabled(false);
    } else {
      setIsSubmitButtonEnabled(true);
    }

    setShowGlobalCriteriaWarning(!isGlobalCriteriaUnchanged);
  }, [name, isGlobalCriteria]);

  const handleClickCancel = () => {
    onClose();
    setIcon(null);
    setShowGlobalCriteriaWarning(false);
    setShowConfirmationModal(false);
  };

  if (showConfirmationModal) {
    return (
      <EditCriteriaConfirmationBody
        criterion={criterion}
        name={name}
        icon={icon}
        isGlobalCriteria={isGlobalCriteria}
        handleClickCancel={handleClickCancel}
        isOpen={isOpen}
      />
    );
  }

  return (
    <EditCriteriaModalBody
      criterion={criterion}
      name={name}
      setName={setName}
      icon={icon}
      setIcon={setIcon}
      isGlobalCriteria={isGlobalCriteria}
      setIsGlobalCriteria={setIsGlobalCriteria}
      showGlobalCriteriaWarning={showGlobalCriteriaWarning}
      handleClickCancel={handleClickCancel}
      setShowConfirmationModal={setShowConfirmationModal}
      isOpen={isOpen}
      isSubmitButtonEnabled={isSubmitButtonEnabled}
    />
  );
};

export default EditCriteriaModal;
