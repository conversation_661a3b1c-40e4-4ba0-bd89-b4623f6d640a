// @ts-nocheck
import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobButton,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';

interface EditCriteriaModalFooterProps {
  onClose: () => void;
  handleClickUpdate: () => void;
  isSubmitButtonEnabled: boolean;
}

const EditCriteriaModalFooter: React.FC<EditCriteriaModalFooterProps> = ({
  onClose,
  handleClickUpdate,
  isSubmitButtonEnabled,
}) => {
  const intl = useIntl();
  return (
    <VidMobStack direction="row" sx={{ gap: '8px' }}>
      <VidMobButton variant="contained" color="inherit" onClick={onClose}>
        <VidMobTypography variant="subtitle2">
          {
            intl.messages[
              'ui.creativeScoring.criteriaManagementV2.action.edit.modal.button.cancel'
            ]
          }
        </VidMobTypography>
      </VidMobButton>
      <VidMobButton
        variant="contained"
        color="primary"
        onClick={handleClickUpdate}
        disabled={!isSubmitButtonEnabled}
      >
        <VidMobTypography variant="subtitle2">
          {
            intl.messages[
              'ui.creativeScoring.criteriaManagementV2.action.edit.modal.button.save'
            ]
          }
        </VidMobTypography>
      </VidMobButton>
    </VidMobStack>
  );
};

export default EditCriteriaModalFooter;
