import React from 'react';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { VidMobFormControl } from '../../../../../../vidMobComponentWrappers';
import NameField from '../../../CriteriaManagementModals/ModalCreateCriteria/NameField';
import GlobalCriteriaSingleSelectDropdown from '../../../CriteriaManagementModals/ModalCreateCriteria/GlobalCriteriaSingleSelectDropdown';
import CustomDialog from '../../../../../../muiCustomComponents/CustomDialog';
import { GlobalCriteriaOption } from '../../../CriteriaManagementModals/ModalCreateCriteria/GlobalCriteriaSingleSelectDropdown/GlobalCriteriaSingleSelectDropdown';
import EditCriteriaModalFooter from './EditCriteriaModalFooter';
import { Criterion } from '../../../../../types/criteriaManagement.types';
import {
  getIsOrganizationAdmin,
  getOrganizationPermissions,
} from '../../../../../../userManagement/redux/selectors/organization.selectors';
import { IconSelection } from '../../../CriteriaManagementModals/ModalCreateCriteria/IconSelection/IconSelection';
import { CustomIcon } from '../../../CriteriaManagementModals/IconSelectionModal/IconSelectionModal.types';

interface EditCriteriaModalBodyProps {
  criterion: Criterion | null;
  name: string;
  setName: (name: string) => void;
  icon: CustomIcon | null;
  setIcon: React.Dispatch<React.SetStateAction<CustomIcon | null>>;
  isGlobalCriteria: GlobalCriteriaOption | null | undefined;
  setIsGlobalCriteria: (
    isGlobalCriteria: GlobalCriteriaOption | null | undefined,
  ) => void;
  showGlobalCriteriaWarning: boolean;
  setShowConfirmationModal: (show: boolean) => void;
  handleClickCancel: () => void;
  isOpen: boolean;
  isSubmitButtonEnabled: boolean;
}

const EditCriteriaModalBody: React.FC<EditCriteriaModalBodyProps> = ({
  criterion,
  name,
  setName,
  icon,
  setIcon,
  isGlobalCriteria,
  setIsGlobalCriteria,
  showGlobalCriteriaWarning,
  setShowConfirmationModal,
  handleClickCancel,
  isOpen,
  isSubmitButtonEnabled,
}) => {
  const intl = useIntl();

  const organizationPermissions = useSelector((state) =>
    getOrganizationPermissions(state),
  );

  const canCreateGlobalCriteria =
    organizationPermissions?.canCreateGlobalCriteria();

  const isOrgAdmin = useSelector(getIsOrganizationAdmin);

  const shouldShowIconSection = isOrgAdmin || canCreateGlobalCriteria;

  const handleClickUpdate = () => {
    setShowConfirmationModal(true); // to switch to confirmation modal
  };

  const modalBody = () => {
    return (
      <VidMobFormControl sx={{ width: '100%', gap: '4px' }}>
        <NameField name={name} setName={setName} />
        {canCreateGlobalCriteria && (
          <GlobalCriteriaSingleSelectDropdown
            isGlobalCriteria={isGlobalCriteria as GlobalCriteriaOption}
            setIsGlobalCriteria={setIsGlobalCriteria}
            showWarning={showGlobalCriteriaWarning}
          />
        )}
        {shouldShowIconSection && (
          <IconSelection
            icon={icon}
            setIcon={setIcon}
            customIconId={criterion?.customIconId}
          />
        )}
      </VidMobFormControl>
    );
  };

  return (
    <CustomDialog
      headerText={
        intl.messages['ui.creativeScoring.criteriaManagementV2.action.edit']
      }
      bodyChildren={modalBody()}
      customFooterChildren={
        <EditCriteriaModalFooter
          onClose={handleClickCancel}
          handleClickUpdate={handleClickUpdate}
          isSubmitButtonEnabled={isSubmitButtonEnabled || Boolean(icon)}
        />
      }
      isOpen={isOpen}
      explicitDialogWidth="500px"
    />
  );
};

export default EditCriteriaModalBody;
