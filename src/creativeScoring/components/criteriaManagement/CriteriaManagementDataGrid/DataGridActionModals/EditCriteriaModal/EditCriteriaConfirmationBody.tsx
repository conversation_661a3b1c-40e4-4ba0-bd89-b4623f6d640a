import React from 'react';
import { useDispatch } from 'react-redux';
import { useIntl } from 'react-intl';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import CustomDialog from '../../../../../../muiCustomComponents/CustomDialog';
import EditCriteriaModalFooter from './EditCriteriaModalFooter';
import { ArrowRightIcon } from '../../../../../../assets/vidmob-mui-icons/general';
import { Criterion } from '../../../../../types/criteriaManagement.types';
import { GlobalCriteriaOption } from '../../../CriteriaManagementModals/ModalCreateCriteria/GlobalCriteriaSingleSelectDropdown/GlobalCriteriaSingleSelectDropdown';
import { EditParams } from './EditCriteriaModal';
import CriteriaManagementSlice from '../../../../../redux/slices/criteriaManagement.slice';
import { CustomIcon } from '../../../CriteriaManagementModals/IconSelectionModal/IconSelectionModal.types';
import { useAttachIconsToCriteria } from '../../../CriteriaManagementModals/IconSelectionModal/IconSelectionModalHooks';

const { editCriteria } = CriteriaManagementSlice.actions;

interface EditCriteriaConfirmationBodyProps {
  criterion: Criterion | null;
  name: string;
  icon: CustomIcon | null;
  isGlobalCriteria: GlobalCriteriaOption | null | undefined;
  handleClickCancel: () => void;
  isOpen: boolean;
}

const EditCriteriaConfirmationBody: React.FC<
  EditCriteriaConfirmationBodyProps
> = ({
  criterion,
  name,
  icon,
  isGlobalCriteria,
  handleClickCancel,
  isOpen,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const { name: savedName, isGlobal: savedIsGlobal, id } = criterion || {};

  const { mutate: attachIconsToCriteria } = useAttachIconsToCriteria();

  const isGlobalValue = Boolean(isGlobalCriteria?.id);

  const isNameChanged = name !== savedName;
  const isGlobalCriteriaChanged = isGlobalValue !== savedIsGlobal;

  const renderHeaderSubText = () => {
    if (isGlobalCriteriaChanged && isGlobalValue === true) {
      return intl.messages[
        'ui.creativeScoring.criteriaManagementV2.action.edit.modal.confirmation.isGlobalChange.promote.subText'
      ];
    }

    if (isGlobalCriteriaChanged && isGlobalValue === false) {
      return intl.messages[
        'ui.creativeScoring.criteriaManagementV2.action.edit.modal.confirmation.isGlobalChange.demote.subText'
      ];
    }

    return intl.messages[
      'ui.creativeScoring.criteriaManagementV2.action.edit.modal.confirmation.nameChange.subText'
    ];
  };

  const handleClickUpdate = () => {
    const params: EditParams = {
      name,
      isGlobal: Boolean(isGlobalCriteria?.id),
    };

    dispatch(
      editCriteria({
        criteriaId: id,
        params,
      }),
    );
    if (icon) {
      attachIconsToCriteria({
        iconCriteriaMappings: [{ criteriaId: Number(id), key: icon.key }],
      });
    }
    handleClickCancel();
  };

  const modalBody = () => {
    return (
      <VidMobBox>
        {Boolean(icon) && (
          <VidMobStack
            direction="column"
            alignItems="flex-start"
            sx={{ gap: '6px', mb: '24px' }}
          >
            <VidMobTypography variant="subtitle2">
              {intl.formatMessage({
                id: 'ui.compliance.criteriaManagement.create.modal.icon.field.description',
              })}
            </VidMobTypography>
            <VidMobAvatar
              alt="Icon"
              src={icon?.signedUrl}
              sx={{ width: 48, height: 48, marginRight: '8px' }}
            />
          </VidMobStack>
        )}
        {isNameChanged && (
          <VidMobStack
            direction="column"
            alignItems="flex-start"
            sx={{ gap: '6px', mb: '24px' }}
          >
            <VidMobTypography variant="subtitle2">
              {intl.formatMessage({
                id: 'ui.compliance.criteriaManagement.create.modal.name.field.description',
              })}
            </VidMobTypography>
            <VidMobStack
              direction="row"
              alignItems="flex-start"
              sx={{ gap: '6px' }}
            >
              <VidMobTypography variant="body2" sx={{ maxWidth: '280px' }}>
                {savedName}
              </VidMobTypography>
              <ArrowRightIcon sx={{ mt: '-3px' }} />
              <VidMobTypography variant="body2" sx={{ maxWidth: '280px' }}>
                {name}
              </VidMobTypography>
            </VidMobStack>
          </VidMobStack>
        )}
        {isGlobalCriteriaChanged && (
          <VidMobStack
            direction="column"
            alignItems="flex-start"
            sx={{ gap: '6px' }}
          >
            <VidMobTypography variant="subtitle2">
              {intl.formatMessage({
                id: 'ui.compliance.criteriaManagement.add.criteria.organizationCriteria',
              })}
            </VidMobTypography>
            <VidMobStack
              direction="row"
              alignItems="center"
              sx={{ gap: '6px' }}
            >
              <VidMobTypography variant="body2">
                {intl.formatMessage({
                  id: `ui.creativeScoring.criteriaManagementV2.confirmation.isGlobal.${savedIsGlobal}`,
                })}
              </VidMobTypography>
              <ArrowRightIcon />
              <VidMobTypography variant="body2">
                {intl.formatMessage({
                  id: `ui.creativeScoring.criteriaManagementV2.confirmation.isGlobal.${isGlobalValue}`,
                })}
              </VidMobTypography>
            </VidMobStack>
          </VidMobStack>
        )}
      </VidMobBox>
    );
  };

  return (
    <CustomDialog
      headerText={
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.action.edit.modal.confirmation.header'
        ]
      }
      headerSubText={renderHeaderSubText()}
      bodyChildren={modalBody()}
      customFooterChildren={
        <EditCriteriaModalFooter
          onClose={handleClickCancel}
          handleClickUpdate={handleClickUpdate}
          isSubmitButtonEnabled
        />
      }
      isOpen={isOpen}
      explicitDialogWidth="500px"
    />
  );
};

export default EditCriteriaConfirmationBody;
