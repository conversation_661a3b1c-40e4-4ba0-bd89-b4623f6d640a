import React from 'react';
import { screen } from '@testing-library/react';
import * as reduxHooks from 'react-redux';
import '@testing-library/jest-dom';
import { DeleteCriteriaModal, EditCriteriaModal } from './index';
import * as workspacesSelectors from '../../../../../userManagement/redux/selectors/workspaces.selectors';
import { testRender } from '../../../../utils/testUtils';
import store from '../../../../../redux/store';
import { userEvent } from '@storybook/testing-library';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
}));

jest.mock(
  '../../../../../userManagement/redux/selectors/workspaces.selectors',
  () => ({
    organizationSelector: jest.fn(),
    workspacesPaginationTotalSizeSelector: jest.fn(),
  }),
);

// Mock the Redux store instead of importing the real one
jest.mock('../../../../../redux/store', () => ({
  __esModule: true,
  default: {
    getState: jest.fn(),
    dispatch: jest.fn(),
    subscribe: jest.fn(),
  },
}));

const queryClient = new QueryClient();

describe('Delete Criteria Modal', () => {
  const criterion = {
    id: 1,
    isGlobal: false,
  };

  beforeEach(() => {
    jest.spyOn(reduxHooks, 'useSelector').mockImplementation((selector) => {
      if (selector === workspacesSelectors.organizationSelector) {
        return { organizationId: '123', organizationName: 'Test Org' };
      }

      if (
        selector === workspacesSelectors.workspacesPaginationTotalSizeSelector
      ) {
        return 5;
      }

      return null;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly for non-global criteria', () => {
    testRender(
      <DeleteCriteriaModal
        isOpen={true}
        onClose={jest.fn()}
        criterion={criterion}
      />,
      { store },
    );

    expect(screen.getByText('Remove this criteria?')).toBeInTheDocument();
  });

  it('renders correctly for global criteria', () => {
    const globalCriterion = { ...criterion, isGlobal: true };
    testRender(
      <DeleteCriteriaModal
        isOpen={true}
        onClose={jest.fn()}
        criterion={globalCriterion}
      />,
      { store },
    );

    expect(
      screen.getByText('Remove this criteria from organization?'),
    ).toBeInTheDocument();
  });
});

describe('Edit Criteria Modal', () => {
  const criterion = {
    id: 1,
    name: 'Audio present',
    isGlobal: false,
  };

  it('renders the edit criteria modal with the correct initial values', () => {
    testRender(
      <QueryClientProvider client={queryClient}>
        <EditCriteriaModal
          isOpen={true}
          onClose={jest.fn()}
          criterion={criterion}
        />
      </QueryClientProvider>,
      { store },
    );

    expect(screen.getByText('Edit criteria')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
  });

  it('enables the submit button when changes are made', () => {
    testRender(
      <QueryClientProvider client={queryClient}>
        <EditCriteriaModal
          isOpen={true}
          onClose={jest.fn()}
          criterion={criterion}
        />
      </QueryClientProvider>,
      { store },
    );

    // Simulate user input to change the name
    userEvent.type(screen.getByDisplayValue('Audio present'), ' Updated');

    // The submit button should now be enabled
    expect(screen.getByText('Update')).not.toBeDisabled();
  });
});
