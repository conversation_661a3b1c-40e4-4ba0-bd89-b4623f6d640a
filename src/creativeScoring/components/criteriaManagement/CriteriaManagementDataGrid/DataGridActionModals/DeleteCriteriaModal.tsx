import React, { useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { useAppDispatch } from '../../../../../redux/store/appDispatch';
// Components
import CustomDialog from '../../../../../muiCustomComponents/CustomDialog';
// constants, types
import { Criterion } from '../../../../types/criteriaManagement.types';
// redux
import CriteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';
import { fetchWorkspaces } from '../../../../../userManagement/redux/thunk/workspaces.thunk';
import {
  workspacesPaginationTotalSizeSelector,
  organizationSelector,
} from '../../../../../userManagement/redux/selectors/workspaces.selectors';

const { deleteCriteria } = CriteriaManagementSlice.actions;

interface ComponentProps {
  isOpen: boolean;
  onClose: () => void;
  criterion: Criterion | null;
}
const DeleteCriteriaModal: React.FC<ComponentProps> = ({
  isOpen,
  onClose,
  criterion,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const appDispatch = useAppDispatch();

  const { organizationId, organizationName } =
    useSelector(organizationSelector);
  const numberOfWorkspaces = useSelector(workspacesPaginationTotalSizeSelector);

  const { isGlobal } = criterion || {};

  useEffect(() => {
    const paginationParams = {
      perPage: 1,
      offset: 0, // only need to know the total number of workspaces
    };
    appDispatch(fetchWorkspaces({ organizationId, paginationParams }));
  }, []);

  const headerText = isGlobal
    ? intl.messages[
        'ui.creativeScoring.criteriaManagementV2.action.delete.modal.header.globalCriteria'
      ]
    : intl.messages[
        'ui.creativeScoring.criteriaManagementV2.action.delete.modal.header'
      ];

  const headerSubText = isGlobal
    ? intl.formatMessage(
        {
          id: 'ui.creativeScoring.criteriaManagementV2.action.delete.modal.subText.globalCriteria',
        },
        { numberOfWorkspaces, organization: organizationName },
      )
    : intl.messages[
        'ui.creativeScoring.criteriaManagementV2.action.delete.modal.subText'
      ];

  const handleDelete = () => {
    dispatch(deleteCriteria({ criterion }));
    onClose();
  };

  return (
    <CustomDialog
      isDelete
      headerText={headerText}
      headerSubText={headerSubText}
      submitButtonLabel={
        intl.messages['ui.creativeScoring.criteriaManagementV2.action.delete']
      }
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={() => handleDelete()}
      explicitDialogWidth="420px"
    />
  );
};

export default DeleteCriteriaModal;
