import React from 'react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import CriteriaManagementDataGrid from './CriteriaManagementDataGrid';
import { getCriteriaPagination } from '../../../redux/selectors/criteriaManagement.selectors';
import * as reduxHooks from 'react-redux';

const mockCriteria = [
  {
    id: 1,
    name: 'Criterion 1',
    rule: 'Rule 1',
    platform: 'Tiktok',
    mediaTypes: ['IMAGE'],
    isOptional: true,
    dateCreated: '2023-01-01',
    owner: 'Owner 1',
    criteriaSet: {
      id: 1,
      isGlobal: false,
    },
  },
];

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

jest.mock(
  '../../../../userManagement/redux/selectors/organization.selectors',
  () => ({
    getOrganizationPermissions: jest.fn(() => ({
      canCreateGlobalCriteria: jest.fn(() => true),
    })),
  }),
);

describe.skip('CriteriaManagementDataGrid', () => {
  beforeEach(() => {
    jest.spyOn(reduxHooks, 'useSelector').mockImplementation((selector) => {
      if (selector === getCriteriaPagination) {
        return mockPagination;
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  it('renders correct columns', () => {
    render(
      <CriteriaManagementDataGrid
        criteria={mockCriteria}
        isHeaderTaller={false}
      />,
    );
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Rule')).toBeInTheDocument();
    expect(screen.getByText('Channel')).toBeInTheDocument();
    expect(screen.getByText('Creative type')).toBeInTheDocument();
    expect(screen.getByText('Consideration')).toBeInTheDocument();
    expect(screen.getByText('Date added')).toBeInTheDocument();
    expect(screen.getByText('Added by')).toBeInTheDocument();
  });
});

const mockPagination = {
  totalSize: 10,
};
