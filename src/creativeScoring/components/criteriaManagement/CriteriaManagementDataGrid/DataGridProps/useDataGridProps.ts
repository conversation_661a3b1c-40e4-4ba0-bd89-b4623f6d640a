import { useScoringDataGridStyles } from '../../../shared/ScoringDataGrid/ScoringDataGridProps/useScoringDataGridStyles';
import { useDispatch } from 'react-redux';
import {
  GridFeatureMode,
  GridSortDirection,
  GridSortModel,
} from '@mui/x-data-grid-pro';
import { DATA_GRID_FIELDS } from '../../../../constants/criteriaManagement.constants';
import { useScoringPagination } from '../../../shared/ScoringDataGrid/ScoringDataGridProps/useScoringPagination';
import CriteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';
import { RefObject } from 'react';
import CustomColumnMenu from './CustomColumnMenu';
import { useGridScrollAndResize } from '../../../shared/ScoringDataGrid/ScoringDataGridProps/useGridScrollAndResize';

export const SERVER = 'server' as GridFeatureMode;

const { setCriteriaSort } = CriteriaManagementSlice.actions;

const customSx = {
  '&.MuiDataGrid-root': {
    '.MuiDataGrid-row': {
      cursor: 'pointer',
    },
    '.MuiDataGrid-cell:focus-within, .MuiDataGrid-columnHeader:focus-within': {
      outline: 'none',
    },
  },
};

export const useDataGridProps = ({
  gridElement,
  gridRef,
  isHeaderTaller,
  isLoading,
}: {
  gridElement: EventTarget | null;
  gridRef: RefObject<HTMLDivElement>;
  isHeaderTaller: boolean;
  isLoading?: boolean;
}) => {
  const dispatch = useDispatch();
  const { showBoxShadowOnFirstColumn, showBoxShadowOnLastColumn } =
    useGridScrollAndResize(gridElement, gridRef);
  const { dataGridStyles } = useScoringDataGridStyles({
    height: `calc(100vh - 230px - ${isHeaderTaller ? '80px' : '0px'})`,
    showBoxShadowOnFirstColumn,
    showBoxShadowOnLastColumn,
    isLoading,
    customSx,
  });

  const dataGridSx = {
    ...dataGridStyles,
    '& .MuiDataGrid-columnHeader[data-field="__check__"]': { padding: 0 },
  };

  // pagination
  const { paginationModel, paginationProps } = useScoringPagination();

  // sorting
  const handleSortModelChange = (model: GridSortModel) => {
    const sortItem = model[0];

    dispatch(
      setCriteriaSort({
        sortOrder: (sortItem?.sort?.toUpperCase() as GridSortDirection) || null,
        sortBy: sortItem?.field || null,
      }),
    );
  };

  const sortingProps = {
    sortingMode: SERVER,
    onSortModelChange: handleSortModelChange,
  };

  const pinnedColumnsLeft = [DATA_GRID_FIELDS.CHECK, DATA_GRID_FIELDS.NAME];

  return {
    sx: dataGridSx,
    rowHeight: 66,
    initialState: {
      pinnedColumns: {
        left: pinnedColumnsLeft,
        right: [DATA_GRID_FIELDS.ACTIONS],
      },
      pagination: { paginationModel },
    },
    disableVirtualization: true,
    slots: {
      columnMenu: CustomColumnMenu,
    },
    ...paginationProps,
    ...sortingProps,
  };
};
