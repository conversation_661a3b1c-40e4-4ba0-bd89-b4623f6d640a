import dayjs from 'dayjs';
import { useSelector } from 'react-redux';
import { IntlShape, useIntl } from 'react-intl';
import { CriterionServerType } from '../../../types/criteriaManagement.types';
import { getIsLoading } from '../../../redux/selectors/criteriaManagement.selectors';
import { generateBlankRows } from '../../rollUpReportsLandingPage/RollUpReportsTable/useRollUpReportsRows';
import { GridColDef } from '@mui/x-data-grid-pro';
import { getCurrentPartnerName } from '../../../../redux/selectors/partner.selectors';
import SCORING_CONSTANTS from '../../../constants/creativeScoring.constants';

export const useRows = (
  criteria: CriterionServerType[],
  columns: GridColDef[],
) => {
  const intl = useIntl();
  const isLoading = useSelector(getIsLoading);
  const workspaceName = useSelector(getCurrentPartnerName);

  if (isLoading) {
    return generateBlankRows(columns, 8);
  }

  return criteria?.map((criterion: CriterionServerType) => {
    const {
      id,
      name,
      platform,
      isBestPractice,
      mediaTypes,
      isOptional,
      dateCreated,
      rule,
      description,
      defaultDisplayName,
      criteriaSet,
      category,
      customIconId,
      customIconUrl,
      criteriaGroups,
    } = criterion;
    const owner = criterion.owner || {};
    const { isGlobal } = criteriaSet;

    return {
      id,
      name: name || defaultDisplayName,
      defaultDisplayName,
      rule,
      description,
      platform,
      mediaTypes: getMediaTypes(mediaTypes, intl),
      isBestPractice,
      isGlobal: Boolean(isGlobal),
      category,
      isOptional: intl.messages[
        `ui.creativeScoring.criteriaManagementV2.cellValue.isOptional.${isOptional}`
      ] as string,
      dateCreated: dayjs(dateCreated).format('MMM DD, YYYY'),
      owner,
      workspaces: isGlobal
        ? intl.messages['ui.creativeScoring.criteriaManagementV2.all']
        : workspaceName,
      customIconId,
      customIconUrl,
      criteriaGroups,
    };
  });
};

const getMediaTypes = (mediaTypes: string[], intl: IntlShape) => {
  const { APPLICABILITY_MEDIA_TYPES } = SCORING_CONSTANTS;
  if (mediaTypes.length === Object.keys(APPLICABILITY_MEDIA_TYPES).length) {
    return intl.messages['ui.creativeScoring.criteriaManagementV2.all'];
  }

  return mediaTypes
    .map(
      (mediaType: string) =>
        intl.messages[
          `ui.creativeScoring.criteriaManagementV2.cellValue.mediaType.${mediaType}`
        ],
    )
    .join(', ');
};
