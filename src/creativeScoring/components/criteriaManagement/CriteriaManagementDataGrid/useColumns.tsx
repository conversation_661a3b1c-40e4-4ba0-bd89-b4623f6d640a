import React from 'react';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import {
  BasicTextCell,
  ChannelCell,
  OwnerCell,
  LoadingCell,
  RuleCell,
  PillCell,
  CriteriaGroupCell,
} from './DataGridCells';
import {
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridActionsColDef,
  GridCellParams,
  GridColDef,
} from '@mui/x-data-grid-pro';
import {
  CRITERIA_MANAGEMENT_MODAL_TYPES,
  DATA_GRID_FIELDS,
} from '../../../constants/criteriaManagement.constants';
import {
  Criterion,
  CriterionServerType,
  DataGridActionType,
  Person,
} from '../../../types/criteriaManagement.types';
import {
  getCanUserUpdateCriteria,
  getIsLoading,
} from '../../../redux/selectors/criteriaManagement.selectors';
import { useScoringDataGridStyles } from '../../shared/ScoringDataGrid/ScoringDataGridProps/useScoringDataGridStyles';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { getOrganizationPermissions } from '../../../../userManagement/redux/selectors/organization.selectors';
import NameCell from './DataGridCells/NameCell';

const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');

interface OwnerCellParams extends Omit<GridCellParams, 'value'> {
  value: Person;
}

type OnOpenModalFunction = (
  modalType: DataGridActionType,
  criterion: Criterion,
) => void;

export const useColumns = (
  onOpenModal: OnOpenModalFunction,
  criteria: CriterionServerType[],
) => {
  const intl = useIntl();
  const isLoading = useSelector(getIsLoading);
  const canUserUpdateWorkspaceCriteria = useSelector(getCanUserUpdateCriteria);
  const organizationPermissions = useSelector((state) =>
    getOrganizationPermissions(state),
  );
  const canUserCreateGlobalCriteria =
    organizationPermissions?.canCreateGlobalCriteria();

  const isAllCriteriaGlobal = criteria.every(
    (criterion) => Boolean(criterion.criteriaSet.isGlobal) === true,
  );

  const { rowActionStyles } = useScoringDataGridStyles({});

  const columns = [
    {
      ...GRID_CHECKBOX_SELECTION_COL_DEF,
      renderHeader: () => <></>,
    },
    {
      field: DATA_GRID_FIELDS.NAME,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.name'
      ] as string,
      minWidth: 200,
      filterable: false,
      renderCell: (params: GridCellParams) => <NameCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.RULE,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.rule'
      ] as string,
      minWidth: 300,
      sortable: false,
      filterable: false,
      renderCell: (params: GridCellParams) => <RuleCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.PLATFORM,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.channel'
      ] as string,
      width: 220,
      filterable: false,
      renderCell: (params: GridCellParams) => <ChannelCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.MEDIA_TYPES,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.mediaType'
      ] as string,
      width: 180,
      filterable: false,
      renderCell: (params: GridCellParams) => <BasicTextCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.IS_OPTIONAL,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.consideration'
      ] as string,
      width: 180,
      filterable: false,
      renderCell: (params: GridCellParams) => <BasicTextCell params={params} />,
    },
    isCriteriaGroupsEnabled && {
      field: DATA_GRID_FIELDS.CRITERIA_GROUPS,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.criteriaGroup'
      ] as string,
      width: 250,
      sortable: false,
      filterable: false,
      renderCell: (params: GridCellParams) => (
        <CriteriaGroupCell params={params} />
      ),
    },
    {
      field: DATA_GRID_FIELDS.IS_GLOBAL,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.isGlobal'
      ] as string,
      width: 180,
      sortable: false,
      filterable: false,
      renderCell: (params: GridCellParams) => <BasicTextCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.CATEGORY,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.category'
      ] as string,
      width: 250,
      filterable: false,
      renderCell: (params: GridCellParams) => <PillCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.DATE_CREATED,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.dateCreated'
      ] as string,
      width: 180,
      filterable: false,
      renderCell: (params: GridCellParams) => <BasicTextCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.OWNER,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.owner'
      ] as string,
      width: 220,
      filterable: false,
      renderCell: (params: OwnerCellParams) => <OwnerCell {...params.value} />,
    },
  ].filter(Boolean) as GridColDef[];

  const showAction = (criterion: Criterion) =>
    !criterion.isGlobal || (canUserCreateGlobalCriteria && criterion.isGlobal);

  if (
    canUserUpdateWorkspaceCriteria &&
    (!isAllCriteriaGlobal || canUserCreateGlobalCriteria)
  ) {
    // don't show action column if there won't be actions for any row
    const actionsColumn = {
      field: DATA_GRID_FIELDS.ACTIONS,
      type: 'actions',
      width: 40,
      resizable: false,
      getActions: (cell: any) => {
        const { row: criterion } = cell;
        return [
          showAction(criterion) && (
            <GridActionsCellItem
              key="delete"
              sx={rowActionStyles}
              label={intl.formatMessage({
                id: 'ui.creativeScoring.criteriaManagementV2.action.delete',
                defaultMessage: 'Remove criteria',
              })}
              onClick={() =>
                onOpenModal(CRITERIA_MANAGEMENT_MODAL_TYPES.DELETE, criterion)
              }
              showInMenu
            />
          ),
          showAction(criterion) && (
            <GridActionsCellItem
              key="edit"
              sx={rowActionStyles}
              label={intl.formatMessage({
                id: 'ui.creativeScoring.criteriaManagementV2.action.edit.name',
                defaultMessage: 'Edit criteria name',
              })}
              onClick={() =>
                onOpenModal(CRITERIA_MANAGEMENT_MODAL_TYPES.EDIT, criterion)
              }
              showInMenu
            />
          ),
        ].filter(Boolean);
      },
    } as GridActionsColDef;

    columns.push(actionsColumn);
  }

  if (isLoading) {
    return columns.map((column) => {
      if (column.field === DATA_GRID_FIELDS.ACTIONS) {
        return {
          ...column,
          getActions: () => [],
        };
      }
      return {
        ...column,
        renderCell: () => <LoadingCell />,
      };
    });
  }

  return columns;
};
