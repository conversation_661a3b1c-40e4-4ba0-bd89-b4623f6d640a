import React, { useRef, useState } from 'react';
import {
  CriterionServerType,
  DataGridActionType,
  Criterion,
  CriteriaModalState,
} from '../../../types/criteriaManagement.types';
import { useColumns } from './useColumns';
import { useRows } from './useRows';
import { useDataGridProps } from './DataGridProps/useDataGridProps';
import {
  DataGridPro,
  GridColDef,
  GridRowParams,
  GridRowSelectionModel,
} from '@mui/x-data-grid-pro';
import { DeleteCriteriaModal, EditCriteriaModal } from './DataGridActionModals';
import { CriteriaDetailsModalWrapper } from '../../criteriaDetailsModal';
import { CRITERIA_MANAGEMENT_MODAL_TYPES } from '../../../constants/criteriaManagement.constants';

interface ComponentProps {
  criteria: CriterionServerType[];
  isHeaderTaller: boolean;
  isLoading?: boolean;
  criteriaRowSelectionModel: GridRowSelectionModel;
  setCriteriaRowSelectionModel: (
    criteriaRowSelectionModel: GridRowSelectionModel,
  ) => void;
}

const CriteriaManagementDataGrid: React.FC<ComponentProps> = ({
  criteria,
  isHeaderTaller,
  isLoading,
  criteriaRowSelectionModel,
  setCriteriaRowSelectionModel,
}) => {
  const [modalState, setModalState] = useState<CriteriaModalState>({
    selectedCriterion: null,
    selectedModal: null,
  });

  const onOpenModal = (modalType: DataGridActionType, criterion: Criterion) => {
    setModalState({
      selectedCriterion: criterion,
      selectedModal: modalType,
    });
  };

  const onCloseModal = () => {
    setModalState({
      selectedCriterion: null,
      selectedModal: null,
    });
  };

  const handleRowClick = (row: GridRowParams) => {
    const { row: criterion } = row;
    setModalState({
      selectedCriterion: criterion,
      selectedModal: CRITERIA_MANAGEMENT_MODAL_TYPES.CRITERIA_DETAILS,
    });
  };

  const columns = useColumns(onOpenModal, criteria) as GridColDef[];
  const rows = useRows(criteria, columns);

  const gridElement = document.querySelector(
    '.MuiDataGrid-root .MuiDataGrid-virtualScroller',
  );
  const gridRef = useRef(null);
  const dataGridProps = useDataGridProps({
    gridElement,
    gridRef,
    isHeaderTaller,
    isLoading,
  });

  const criteriaDGSelectionProps = {
    rowSelectionModel: criteriaRowSelectionModel,
    checkboxSelection: true,
    onRowSelectionModelChange: setCriteriaRowSelectionModel,
  };

  const combinedProps = {
    columns,
    rows,
    onRowClick: handleRowClick,
    ...dataGridProps,
    ...criteriaDGSelectionProps,
  };

  return (
    <div ref={gridRef}>
      <DataGridPro
        {...combinedProps}
        slotProps={{
          pagination: { sx: { display: 'none' } },
        }}
        keepNonExistentRowsSelected
      />
      <DeleteCriteriaModal
        isOpen={
          modalState.selectedModal === CRITERIA_MANAGEMENT_MODAL_TYPES.DELETE
        }
        onClose={onCloseModal}
        criterion={modalState.selectedCriterion}
      />
      <EditCriteriaModal
        isOpen={
          modalState.selectedModal === CRITERIA_MANAGEMENT_MODAL_TYPES.EDIT
        }
        onClose={onCloseModal}
        criterion={modalState.selectedCriterion}
      />
      <CriteriaDetailsModalWrapper
        isOpen={
          modalState.selectedModal ===
          CRITERIA_MANAGEMENT_MODAL_TYPES.CRITERIA_DETAILS
        }
        onClose={onCloseModal}
        criterion={modalState.selectedCriterion}
      />
    </div>
  );
};

export default CriteriaManagementDataGrid;
