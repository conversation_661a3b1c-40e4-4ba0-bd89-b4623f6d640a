import { useQuery, useMutation } from '@tanstack/react-query';
import BffCriteriaCustomIconsService, {
  AttachIconsToCriteriaRequest,
  CustomIconForUpload,
} from '../../../../../apiServices/BffCriteriaCustomIconsService';
import { getOrganizationId } from '../../../../../redux/selectors/partner.selectors';
import { useSelector } from 'react-redux';
import { CustomIcon } from './IconSelectionModal.types';

const {
  getCustomIconsForOrganization,
  uploadCustomIcon,
  attachIconsToCriteria,
} = BffCriteriaCustomIconsService;

export const CUSTOM_ICONS_QUERY_KEY = 'customIcons';

export const useGetCustomIcons = () => {
  const organizationId = useSelector(getOrganizationId);
  return useQuery<CustomIcon[]>(
    [CUSTOM_ICONS_QUERY_KEY, organizationId],
    () => getCustomIconsForOrganization(organizationId),
    {
      enabled: !!organizationId,
    },
  );
};

export const useUploadCustomIcon = () => {
  const organizationId = useSelector(getOrganizationId);
  return useMutation((uploadDetails: CustomIconForUpload) =>
    uploadCustomIcon(organizationId, uploadDetails),
  );
};

export const useAttachIconsToCriteria = () => {
  return useMutation((iconCriteriaMappings: AttachIconsToCriteriaRequest) =>
    attachIconsToCriteria(iconCriteriaMappings),
  );
};
