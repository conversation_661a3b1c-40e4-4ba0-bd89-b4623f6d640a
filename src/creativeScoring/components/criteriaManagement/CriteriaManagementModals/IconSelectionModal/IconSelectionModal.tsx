import React, { useEffect, useRef, useState } from 'react';
import CustomDialog from '../../../../../muiCustomComponents/CustomDialog';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobButton,
  VidMobFormControl,
  VidMobFormControlLabel,
  VidMobRadio,
  VidMobStack,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import DeleteIcon from '@mui/icons-material/Delete';
import { useIntl } from 'react-intl';
import {
  useGetCustomIcons,
  useUploadCustomIcon,
} from './IconSelectionModalHooks';
import { blue } from '@mui/material/colors';
import {
  CustomIcon,
  ICON_SELECTION_MODAL_RADIO_OPTIONS,
} from './IconSelectionModal.types';
import { CustomIconForUpload } from '../../../../../apiServices/BffCriteriaCustomIconsService';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';

const { EXISTING_ICON, UPLOAD_ICON } = ICON_SELECTION_MODAL_RADIO_OPTIONS;

interface IconSelectionModalProps {
  originalIcon?: CustomIcon | null;
  criteriaCount?: number;
  isOpen: boolean;
  handleClose: () => void;
  handleSubmit: (icon: CustomIcon | null) => void;
  selectedIconsIds?: string[];
}

const MAX_FILE_SIZE_MB = 2;
const ALLOWED_FILE_TYPES = ['image/png', 'image/jpeg', 'image/svg+xml'];

export const IconSelectionModal = ({
  criteriaCount,
  isOpen,
  handleClose,
  handleSubmit,
  selectedIconsIds,
}: IconSelectionModalProps) => {
  const intl = useIntl();
  const showToastAlert = useToastAlert();

  const [selectedExistingIconId, setSelectedExistingIconId] = useState<
    string | null
  >(null);
  const [uploadedIcon, setUploadedIcon] = useState<CustomIconForUpload | null>(
    null,
  );
  const [selectedRadioOption, setSelectedRadioOption] = useState<string | null>(
    EXISTING_ICON,
  );
  const [isExistingIconSectionDisabled, setIsExistingIconSectionDisabled] =
    useState(false);
  const [isUploadButtonDisabled, setIsUploadButtonDisabled] = useState(false);

  const { data: organizationIcons, refetch: refetchOrganizationIcons } =
    useGetCustomIcons();

  const { mutate: uploadCustomIcon } = useUploadCustomIcon();

  useEffect(() => {
    if (!organizationIcons || organizationIcons.length === 0) {
      setIsExistingIconSectionDisabled(true);
      setSelectedRadioOption(UPLOAD_ICON);
      setIsUploadButtonDisabled(false);
    } else if (organizationIcons && organizationIcons.length === 1) {
      setIsExistingIconSectionDisabled(false);
      setSelectedRadioOption(EXISTING_ICON);
      setSelectedExistingIconId(organizationIcons[0].id);
      setIsUploadButtonDisabled(true);
    } else {
      setSelectedRadioOption(EXISTING_ICON);
      setIsExistingIconSectionDisabled(false);
      setIsUploadButtonDisabled(true);
    }
    setUploadedIcon(null);
  }, [organizationIcons, isOpen]);

  const isExistingIconReadyToSubmit =
    selectedRadioOption === EXISTING_ICON && selectedExistingIconId;
  const isUploadedIconReadyToSubmit =
    selectedRadioOption === UPLOAD_ICON && uploadedIcon;

  const onSubmit = () => {
    if (isExistingIconReadyToSubmit) {
      const selectedIcon =
        organizationIcons?.find((icon) => icon.id === selectedExistingIconId) ||
        null;
      handleSubmit(selectedIcon);
    }
    if (isUploadedIconReadyToSubmit) {
      const modifiedIcon = {
        ...uploadedIcon,
        base64: uploadedIcon.base64?.replace(/^data:image\/\w+;base64,/, ''),
      };
      uploadCustomIcon(modifiedIcon, {
        onSuccess: async (uploadedResponse) => {
          const { data: refreshedIcons } = await refetchOrganizationIcons();

          const matchingIcon = refreshedIcons?.find(
            (icon) => icon.key === uploadedResponse,
          );
          setUploadedIcon(null);
          handleSubmit(matchingIcon || null);
        },
        onError: () => {
          showToastAlert(
            'ui.compliance.criteriaManagement.iconSelection.modal.save.error',
            'error',
          );
        },
      });
    }
  };

  const handleRemoveUploadedIcon = () => setUploadedIcon(null);

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedOption = event.target.value;
    if (selectedOption === UPLOAD_ICON) {
      setSelectedExistingIconId(null);
      setIsUploadButtonDisabled(false);
    }

    if (selectedOption === EXISTING_ICON) {
      setIsUploadButtonDisabled(true);
      if (organizationIcons && organizationIcons.length === 1) {
        setIsExistingIconSectionDisabled(false);
        setSelectedExistingIconId(organizationIcons[0].id);
      }
    }

    setSelectedRadioOption(selectedOption);
  };

  const handleExistingSelection = (iconId: string) => {
    setSelectedRadioOption(EXISTING_ICON);
    setSelectedExistingIconId(iconId);
  };

  const handleFileUploadFromLocal = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const fileSizeMB = file.size / (1024 * 1024);

      if (fileSizeMB > MAX_FILE_SIZE_MB) {
        showToastAlert(
          'ui.compliance.criteriaManagement.iconSelection.modal.upload.fileSizeError',
          'error',
          { maxFileSize: MAX_FILE_SIZE_MB },
        );
        event.target.value = '';
        return;
      }

      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        showToastAlert(
          'ui.compliance.criteriaManagement.iconSelection.modal.upload.fileTypeError',
          'error',
        );
        event.target.value = '';
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        if (reader.result) {
          setUploadedIcon({
            base64: reader.result.toString(),
            mimeType: file.type,
            originalFilename: file.name,
          });
        }
      };
      reader.readAsDataURL(file);
    }
    event.target.value = '';
  };

  const uploadInputRef = useRef<HTMLInputElement>(null);

  const selectedExistingIcon =
    selectedExistingIconId === null
      ? selectedIconsIds
      : [selectedExistingIconId];

  const bodyChildren = (
    <VidMobFormControl>
      <VidMobStack direction="column">
        <VidMobFormControlLabel
          value={EXISTING_ICON}
          control={
            <VidMobRadio
              checked={selectedRadioOption === EXISTING_ICON}
              onChange={handleRadioChange}
              disabled={isExistingIconSectionDisabled}
            />
          }
          label={intl.formatMessage({
            id: 'ui.compliance.criteriaManagement.iconSelection.modal.existingSection.title',
            defaultMessage: 'Select from previously used icons',
          })}
        />
        <VidMobBox
          display="flex"
          flexDirection="row"
          flexWrap="wrap"
          gap={2}
          sx={{ marginLeft: '24px', marginRight: '24px' }}
        >
          {organizationIcons?.map((icon) => (
            <VidMobBox key={icon.id} position="relative">
              <VidMobAvatar
                src={icon.signedUrl}
                alt={icon.key}
                sx={{
                  width: 48,
                  height: 48,
                  border: selectedExistingIcon?.find((id) => id === icon.id)
                    ? `2px solid ${blue[500]}`
                    : '2px solid transparent',
                }}
                onClick={() => handleExistingSelection(icon.id)}
              />
            </VidMobBox>
          ))}
        </VidMobBox>
        <VidMobFormControlLabel
          value="uploadIcon"
          control={
            <VidMobRadio
              checked={selectedRadioOption === 'uploadIcon'}
              onChange={handleRadioChange}
            />
          }
          label={intl.formatMessage({
            id: 'ui.compliance.criteriaManagement.iconSelection.modal.uploadSection.title',
            defaultMessage: 'Upload new icon',
          })}
          sx={{ marginTop: '16px' }}
        />
        <VidMobStack
          direction="row"
          alignItems="center"
          sx={{ marginLeft: '24px' }}
        >
          {uploadedIcon && (
            <VidMobAvatar
              alt={uploadedIcon.originalFilename}
              src={uploadedIcon.base64}
              sx={{
                width: 48,
                height: 48,
                marginRight: '8px',
              }}
            />
          )}
          {uploadedIcon && (
            <DeleteIcon
              sx={{
                cursor: 'pointer',
                marginRight: '16px',
                width: '20px',
                height: '20px',
              }}
              onClick={handleRemoveUploadedIcon}
            />
          )}
          <input
            type="file"
            accept="image/*"
            onChange={handleFileUploadFromLocal}
            style={{ display: 'none' }}
            ref={uploadInputRef}
          />
          <VidMobButton
            variant="contained"
            color="secondary"
            onClick={() =>
              uploadInputRef.current && uploadInputRef.current.click()
            }
            disabled={isUploadButtonDisabled}
          >
            {intl.formatMessage({
              id: 'ui.compliance.criteriaManagement.iconSelection.modal.upload',
              defaultMessage: 'Upload',
            })}
          </VidMobButton>
        </VidMobStack>
        <VidMobTypography
          variant="body2"
          color="textSecondary"
          sx={{ marginTop: '4px', marginLeft: '24px', marginRight: '24px' }}
        >
          {intl.formatMessage({
            id: 'ui.compliance.criteriaManagement.iconSelection.modal.uploadSection.description',
            defaultMessage:
              'Icon must be <2MB in size, must be in PNG, JPEG, or SVG format, and would ideally be no larger than 128 x 128 pixels.',
          })}
        </VidMobTypography>
      </VidMobStack>
    </VidMobFormControl>
  );

  const headerText = intl.formatMessage(
    {
      id: 'ui.compliance.criteriaManagement.iconSelection.modal.headerText',
      defaultMessage: 'Add icon to criteria',
    },
    {
      criteriaCount: criteriaCount ?? 0,
    },
  );

  return (
    <CustomDialog
      isOpen={isOpen}
      headerText={headerText}
      onClose={handleClose}
      onSubmit={onSubmit}
      explicitDialogWidth={'444px'}
      bodyChildren={bodyChildren}
      submitButtonLabel={intl.formatMessage({
        id: 'ui.compliance.criteriaManagement.iconSelection.modal.save',
        defaultMessage: 'Save',
      })}
      isSubmitButtonDisabled={
        !isExistingIconReadyToSubmit && !isUploadedIconReadyToSubmit
      }
    />
  );
};
