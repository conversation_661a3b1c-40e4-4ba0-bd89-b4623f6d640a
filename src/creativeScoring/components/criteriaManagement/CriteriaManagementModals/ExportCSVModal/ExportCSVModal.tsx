import React, { useState, useEffect } from 'react';
import CustomDialog from '../../../../../muiCustomComponents/CustomDialog';
import { useExportCriteriaCsv } from '../../CriteriaManagementHeader/useExportCriteriaCsv';
import { useSelector } from 'react-redux';
import {
  getCriteriaFilters,
  getCriteriaSearchText,
  getCriteriaSorting,
} from '../../../../redux/selectors/criteriaManagement.selectors';
import { getCurrentPartner } from '../../../../../redux/selectors/partner.selectors';
import { useIntl } from 'react-intl';
import { DropdownMultiSelect } from '../../../../../muiCustomComponents/Dropdowns';
import { getUserWorkspacesInCurrentOrganization } from '../../../../../redux/selectors/user.selectors';
import { DropdownOption } from '../../../../../muiCustomComponents/Dropdowns/types';
import {
  VidMobFormLabel,
  VidMobStack,
} from '../../../../../vidMobComponentWrappers';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { trackFeatureUsageGainsight } from '../../../../../utils/gainsight';

interface ExportCSVModalProps {
  isOpen: boolean;
  setCurrentModal: (modal: string | null) => void;
}

export const ExportCSVModal = ({
  isOpen,
  setCurrentModal,
}: ExportCSVModalProps) => {
  const intl = useIntl();
  const exportCriteriaMutation = useExportCriteriaCsv();
  const showToastAlert = useToastAlert();

  const workspaces = useSelector(getUserWorkspacesInCurrentOrganization);
  const searchText = useSelector(getCriteriaSearchText);
  const filters = useSelector(getCriteriaFilters);
  const { sortOrder, sortBy } = useSelector(getCriteriaSorting);
  const currentWorkspace = useSelector(getCurrentPartner);

  const [selectedWorkspaces, setSelectedWorkspaces] = useState<
    DropdownOption[]
  >([currentWorkspace]);

  useEffect(() => {
    if (isOpen) {
      trackFeatureUsageGainsight('Export Criteria', {
        Context: 'Export Criteria Modal Opened',
      });
    }
  }, [isOpen]);

  const resetModal = () => {
    setCurrentModal(null);
    setSelectedWorkspaces([currentWorkspace]);
  };

  const handleClose = () => {
    resetModal();
  };

  const handleSubmit = () => {
    const workspaceIds = selectedWorkspaces.map((workspace) =>
      workspace?.id?.toString(),
    );

    const errorMessage = intl.formatMessage({
      id: 'ui.creativeScoring.criteriaManagementV2.modal.exportCSVModal.error',
      defaultMessage:
        "We've encountered an unexpected error exporting these workspace criteria as a CSV.",
    });

    exportCriteriaMutation.mutate(
      {
        workspaceIds,
        sort: { sortBy, sortOrder },
        filters,
        searchText,
      },
      {
        onSuccess: () => {
          resetModal();
        },
        onError: () => {
          showToastAlert(errorMessage, 'error');
        },
      },
    );

    trackFeatureUsageGainsight('Export Criteria', {
      Context: 'Export Criteria Submitted',
    });
  };

  const headerText = intl.formatMessage({
    id: 'ui.creativeScoring.criteriaManagementV2.modal.exportCSVModal.headerText',
    defaultMessage: 'Export as CSV',
  });

  const labelSx = {
    color: 'black !important',
    fontWeight: 600,
    fontSize: '14px',
    lineHeight: '20px',
    marginBottom: '8px',
  };

  const workspacesDropdown = (
    <VidMobStack>
      <VidMobFormLabel sx={labelSx}>Workspaces to include:</VidMobFormLabel>
      <DropdownMultiSelect
        selectedOptions={selectedWorkspaces}
        setSelectedOptions={setSelectedWorkspaces}
        options={workspaces}
        customMenuSx={{ width: '396px' }}
      />
    </VidMobStack>
  );

  return (
    <CustomDialog
      isOpen={isOpen}
      onClose={handleClose}
      onSubmit={handleSubmit}
      headerText={headerText}
      bodyChildren={workspacesDropdown}
      explicitDialogWidth={'444px'}
      submitButtonLabel={intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.modal.exportCSVModal.submit',
        defaultMessage: 'Export',
      })}
      isSubmitButtonDisabled={selectedWorkspaces.length === 0}
    />
  );
};
