import React from 'react';
import { COMPLIANCE } from '../../../../../../constants';
import { useIntl } from 'react-intl';
import {
  VidMobFormControl,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import MultiValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';
import { ListItem } from '../../../../../../components/ReportFilters/types';

type ParametersType = {
  [key: string]: {
    [key: string]: boolean;
  };
};

const DropDownMultiSelectParamV2 = ({
  parameter,
  parameters,
  setParameters,
  titleOverride,
  required,
}: {
  parameter: {
    identifier: string;
    units: string;
    values: string[];
  };
  parameters: ParametersType;
  setParameters: (state: ParametersType) => void;
  titleOverride: boolean;
  required: boolean;
}) => {
  const intl = useIntl();

  const title = titleOverride
    ? `${titleOverride}${required ? '*' : ''}`
    : `${intl.formatMessage({ id: COMPLIANCE.CRITERIA_PARAMETER_UNIT_LABEL[parameter?.units] }, { count: null })} ${required ? '*' : ''}`;

  const identifier = parameter?.identifier;

  const options = parameter.values;

  const valuesObject = identifier ? parameters[identifier] : null;
  const selectedOptions = valuesObject ? Object.keys(valuesObject) : [];

  const handleChange = (values: ListItem[]) => {
    const result: Record<string, any> = {
      ...parameters,
      [identifier]: {},
    };

    values.forEach((value) => {
      result[identifier][value.id] = true;
    });

    setParameters(result);
  };

  return (
    <VidMobFormControl
      key={parameter.identifier}
      sx={{
        width: '500px',
        marginBottom: '8px',
        '& .MuiButtonBase-root': {
          width: '500px',
          height: '44px',
        },
      }}
    >
      <VidMobTypography variant="subtitle2" sx={{ marginBottom: '8px' }}>
        {title}
      </VidMobTypography>

      <MultiValueInput
        customButtonSx={{ height: '48px' }}
        labelTextSx={{
          fontWeight: '400',
        }}
        slotSx={{
          width: '482px',
        }}
        onChange={handleChange}
        value={selectedOptions.map((option) => ({
          id: option,
          name: option,
        }))}
        valueOptions={options.map((option) => ({
          id: option,
          name: option,
        }))}
      />
    </VidMobFormControl>
  );
};

export default DropDownMultiSelectParamV2;
