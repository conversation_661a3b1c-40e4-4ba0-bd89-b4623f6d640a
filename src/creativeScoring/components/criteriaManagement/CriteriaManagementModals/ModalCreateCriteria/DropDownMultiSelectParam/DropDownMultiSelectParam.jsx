import React, { useMemo, useState } from 'react';
import { any, func, object, objectOf, shape, string } from 'prop-types';
// deleted import
import classNames from 'classnames';
import { complianceCriteriaParameterShape } from '../../../../../featureServices/shapes';
import DropDownControl from '../../../../../../components/DropDownControl';
import DropDownItemChipCheckbox from '../../../../../../components/DropDownItemChipCheckbox';
import DropDownStateless from '../../../../../../components/DropDownStateless';
import { COMPLIANCE } from '../../../../../../constants';
import { isNil } from '../../../../../../utils/typeCheckUtils';
import { uniqueId } from '../../../../../../utils/uniqueIdGenerator';
import { useIntl } from 'react-intl';
import './DropDownMultiSelectParam.scss';

const { CHIP_TYPE } = COMPLIANCE;

const DropDownMultiSelectParam = (props) => {
  const {
    parameter,
    parameters,
    setParameters,
    titleOverride,
    recommendations,
    informationChipText,
    required,
  } = props;
  const [isOpen, setIsOpen] = useState(false);
  const intl = useIntl();
  const exclusionClass = 'on-click-outside-ignore-' + uniqueId();

  const labelClass = classNames({
    'body-left-aligned-gray': isNil(parameters[parameter?.identifier]),
    'body-left-aligned-black': !isNil(parameters[parameter?.identifier]),
  });

  const getChipText = (parameterValue) => {
    if (!parameterValue) {
      return null;
    }

    if (recommendations && recommendations[parameterValue]) {
      return intl.messages['global.recommended.label'];
    }

    if (informationChipText && informationChipText[parameterValue]) {
      const intlIdentifier = informationChipText[parameterValue];
      return intl.messages[intlIdentifier];
    }

    return null;
  };

  const shouldShowChip = (parameterValue) => {
    if (recommendations && recommendations[parameterValue]) {
      return true;
    }

    if (informationChipText && informationChipText[parameterValue]) {
      return true;
    }

    return false;
  };

  const getChipType = (parameterValue) => {
    if (recommendations && recommendations[parameterValue]) {
      return CHIP_TYPE.BEST_PRACTICE;
    }

    return CHIP_TYPE.INFO;
  };

  const items = useMemo(() =>
    parameter.values.map(
      (value) => ({
        chipText: getChipText(value),
        id: value,
        name: String(value),
        showChip: shouldShowChip(value),
        chipType: getChipType(value),
      }),
      [parameter],
    ),
  );

  const dropdownLabel = (identifier) => {
    switch (identifier) {
      case 'aspectRatios':
        return 'ui.compliance.criteriaManagement.template.parameter.label.multi.select.ratio';
      case 'brandColors':
        return 'ui.compliance.criteriaManagement.template.parameter.label.multi.select.color';
      default:
        return 'ui.compliance.criteriaManagement.template.parameter.label.multi.select.ratio';
    }
  };

  const title = titleOverride
    ? `${titleOverride}${required ? '*' : ''}`
    : `${intl.formatMessage({ id: COMPLIANCE.CRITERIA_PARAMETER_UNIT_LABEL[parameter?.units] }, { count: null })} ${required ? '*' : ''}`;

  return (
    <div className="create-criteria-modal-multi-select wrapper">
      <span className="description-text body-left-aligned-black">{title}</span>
      <DropDownControl
        exclusionClass={exclusionClass}
        isOpen={isOpen}
        label={
          !isNil(parameters[parameter?.identifier]) &&
          Object.keys(parameters[parameter?.identifier]).length
            ? Object.keys(parameters[parameter?.identifier]).length === 1
              ? Object.keys(parameters[parameter?.identifier])[0]
              : intl.formatMessage(
                  { id: dropdownLabel(parameter?.identifier) },
                  {
                    count: Object.keys(parameters[parameter?.identifier])
                      .length,
                  },
                )
            : intl.messages['global.select.label']
        }
        labelClass={labelClass}
        onClick={() => {
          setIsOpen(!isOpen);
        }}
      >
        <DropDownStateless
          wrapper={DropDownItemChipCheckbox}
          items={items}
          selectedItemIds={parameters[parameter.identifier] || {}}
          onClickOutside={(event) => {
            if (
              !isNil(event) &&
              !event.target.getAttribute('class')?.includes(exclusionClass)
            ) {
              setIsOpen(false);
            }
          }}
          onDone={() => {}}
          onItemSelected={(item, isSelected) => {
            setParameters((prevState) => {
              if (!isSelected) {
                return {
                  ...prevState,
                  [parameter.identifier]: {
                    ...prevState[parameter.identifier],
                    [item.id]: !isSelected,
                  },
                };
              }

              const nextState = { ...prevState };
              delete nextState[parameter.identifier][item.id]; // remove the value from component state
              return nextState;
            });
          }}
        />
      </DropDownControl>
    </div>
  );
};

DropDownMultiSelectParam.propTypes = {
  parameters: objectOf(any).isRequired,
  setParameters: func.isRequired,
  parameter: shape(complianceCriteriaParameterShape),
  titleOverride: string,
  recommendations: object,
};

DropDownMultiSelectParam.defaultProps = {
  parameter: null,
  titleOverride: '',
};

export default DropDownMultiSelectParam;
