import React from 'react';
import { useIntl } from 'react-intl';
import { VidMobFormControl } from '../../../../../../vidMobComponentWrappers';
import {
  CRITERIA_CATEGORIES,
  CRITERIA_CATEGORIES_INTL_MAP,
} from '../../../../../constants/criteriaManagement.constants';
import { Template } from '../../../../../../types/template.type';
import SingleValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/SingleValueInput';
import { SingleValue } from '../../../../../../components/ReportFilters/types';

interface DropDownCategoryProps {
  selectedCategory: string;
  setSelectedCategory: (category: string | null) => void;
  setSelectedTemplate: (template: Template | null) => void;
  disabled?: boolean;
}

const DropDownCategory: React.FC<DropDownCategoryProps> = ({
  selectedCategory,
  setSelectedCategory,
  setSelectedTemplate,
  disabled,
}) => {
  const intl = useIntl();

  const dropdownValue = {
    id: selectedCategory,
    name: intl.formatMessage({
      id: CRITERIA_CATEGORIES_INTL_MAP[selectedCategory],
    }),
  };

  const dropdownOptions = Object.values(CRITERIA_CATEGORIES)
    .filter((category: string) => CRITERIA_CATEGORIES_INTL_MAP[category])
    .map((category: string) => ({
      id: category,
      name: intl.formatMessage({ id: CRITERIA_CATEGORIES_INTL_MAP[category] }),
    }));

  const handleDropdownSelection = (selectedItem: SingleValue) => {
    const selectedOption = Object.values(CRITERIA_CATEGORIES).find(
      (category: string) => category === selectedItem.id,
    );
    setSelectedCategory(selectedOption || null);
    setSelectedTemplate(null);
  };

  return (
    <div style={{ width: '500px', marginBottom: '12px' }}>
      <VidMobFormControl fullWidth>
        <SingleValueInput
          isDisabled={disabled}
          value={dropdownValue}
          valueOptions={dropdownOptions}
          onChange={handleDropdownSelection}
          buttonWidth="500px"
          customButtonSx={{ height: '48px' }}
          labelTextSx={{
            fontWeight: '400',
          }}
          slotSx={{ width: '482px' }}
        />
      </VidMobFormControl>
    </div>
  );
};

export default DropDownCategory;
