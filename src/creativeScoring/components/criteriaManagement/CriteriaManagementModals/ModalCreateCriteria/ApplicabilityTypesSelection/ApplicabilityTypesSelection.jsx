import React, { useMemo } from 'react';
import {
  VidMobCheckbox,
  VidMobFormControlLabel,
} from '../../../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import classnames from 'classnames';
import CreativeScoringConstants from '../../../../../constants/creativeScoring.constants';
import { PLATFORM_DV360 } from '../../../../../../constants/platform.constants';
import SectionLabel from '../SectionLabel';

const {
  APPLICABILITY_MEDIA_TYPES,
  DV360_ONLY_ASPECT_RATIOS,
  CRITERIA_TEMPLATE_APPLICABILITY,
} = CreativeScoringConstants;

const ApplicabilityTypesSelection = ({
  template,
  applicability,
  selectedPlatforms,
  handleSelectApplicability,
  isCustomAudio,
  parameterValues,
  required,
}) => {
  const intl = useIntl();

  const isOnlyDV360Selected = useMemo(() => {
    if (selectedPlatforms.length !== 1) {
      return false;
    }

    return selectedPlatforms[0].id === PLATFORM_DV360;
  }, [selectedPlatforms.length]);

  const isHTMLOnlyAspectRatioSelected = (aspectRatios) => {
    if (!aspectRatios) {
      return false;
    }

    return DV360_ONLY_ASPECT_RATIOS.some((item) =>
      Object.keys(aspectRatios).includes(item),
    );
  };

  const applicabilitySelectionClasses = classnames({
    'applicability-selection': true,
    'applicability-selection-disabled': isCustomAudio,
  });

  const applicabilityTypeDisplayText = {
    [APPLICABILITY_MEDIA_TYPES.IMAGE]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.images.label'
      ],
    [APPLICABILITY_MEDIA_TYPES.VIDEO]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.videos.label'
      ],
    [APPLICABILITY_MEDIA_TYPES.ANIMATED_IMAGE]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.animated.images.label'
      ],
    [APPLICABILITY_MEDIA_TYPES.HTML]:
      intl.messages[
        'ui.creativeScoring.reportDetails.filter.v2.mediaType.html.label'
      ],
  };

  return (
    <div className="create-criteria-modal-single-select no-margin">
      <SectionLabel
        sectionTitle={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.modal.criteria.creative.type.label',
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.modal.criteria.custom.criteriaType.helperText',
        })}
        labelCustomSx={{ marginTop: 0 }}
        required
      />
      <div className={applicabilitySelectionClasses}>
        <div></div>

        {Object.values(APPLICABILITY_MEDIA_TYPES).map((type) => {
          const { ANIMATED_IMAGE, HTML } = APPLICABILITY_MEDIA_TYPES;

          const isDisabled =
            (type !== ANIMATED_IMAGE &&
              type !== HTML &&
              template?.applicability !==
                CRITERIA_TEMPLATE_APPLICABILITY.ALL) ||
            (type !== HTML &&
              isHTMLOnlyAspectRatioSelected(parameterValues?.aspectRatios));

          const isChecked =
            applicability?.includes(APPLICABILITY_MEDIA_TYPES[type]) ||
            template?.applicability ===
              CRITERIA_TEMPLATE_APPLICABILITY.VIDEO_ONLY;

          if (
            template?.applicability ===
              CRITERIA_TEMPLATE_APPLICABILITY.VIDEO_ONLY &&
            type !== APPLICABILITY_MEDIA_TYPES.VIDEO
          ) {
            return null;
          }

          if (
            template?.applicability === CRITERIA_TEMPLATE_APPLICABILITY.IMAGE &&
            type !== APPLICABILITY_MEDIA_TYPES.IMAGE
          ) {
            return null;
          }

          if (!isChecked && isDisabled) {
            return null;
          }

          if (type === HTML && !isOnlyDV360Selected) {
            return null;
          }

          return (
            // eslint-disable-next-line jsx-a11y/label-has-associated-control
            <label className="applicability-option" key={type}>
              <VidMobFormControlLabel
                label={applicabilityTypeDisplayText[type]}
                control={
                  <VidMobCheckbox
                    checked={isChecked}
                    disabled={isDisabled}
                    onChange={() =>
                      handleSelectApplicability(APPLICABILITY_MEDIA_TYPES[type])
                    }
                  />
                }
              />
            </label>
          );
        })}
      </div>
    </div>
  );
};

export { ApplicabilityTypesSelection };
