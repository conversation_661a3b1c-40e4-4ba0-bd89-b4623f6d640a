import React from 'react';
import { arrayOf, func, object, shape } from 'prop-types';
import { complianceCriteriaParameterShape } from '../../../../../featureServices/shapes';
import DropDownSingleSelectParam from '../DropDownSingleSelectParam/DropDownSingleSelectParam';
import ParameterTextBox from '../ParameterTextBox';
import { COMPLIANCE } from '../../../../../../constants';
import { validators } from '../CriteriaParameterValidator';
import DropDownMultiSelectParamV2 from '../DropDownMultiSelectParam/DropDownMultiSelectParamV2';

const {
  MULTI_VALUE_TEMPLATES,
  CRITERIA_PARAMETER_UNIT_LABEL,
  NO_UNIT_TEMPLATES,
  CRITERIA_PARAMETER_UNIT_NAME,
} = COMPLIANCE;

const ParameterComponentSelector = ({
  intl,
  parameters,
  parameterValues,
  selectedTemplate,
  setParameters,
  required,
}) => {
  const getTitleOverride = (parameter) => {
    if (
      selectedTemplate &&
      MULTI_VALUE_TEMPLATES.includes(selectedTemplate.identifier)
    ) {
      return `${intl.messages[CRITERIA_PARAMETER_UNIT_LABEL[parameter.identifier]]} ${required ? '*' : ''}`;
    }

    return '';
  };

  const isUnitShown = () =>
    selectedTemplate &&
    !NO_UNIT_TEMPLATES.includes(selectedTemplate.identifier);

  const getDisplayParameter = (parameter) => {
    const { identifier, values } = parameter;
    let displayParameter = { ...parameter };

    if (
      identifier === CRITERIA_PARAMETER_UNIT_NAME.MAX_DURATION &&
      parameterValues.minDuration >= 0
    ) {
      displayParameter.values = values.filter(
        (value) => value >= parameterValues.minDuration,
      );
    }

    if (
      identifier === CRITERIA_PARAMETER_UNIT_NAME.MAX_WORDS &&
      parameterValues.minWords >= 0
    ) {
      displayParameter.values = values.filter(
        (value) => value >= parameterValues.minWords,
      );
    }

    return displayParameter;
  };

  const renderParameterList = () => {
    if (parameters.length) {
      return parameters.map((parameter) => {
        const displayParameter = getDisplayParameter(parameter);

        if (parameter.multiselect) {
          return (
            <DropDownMultiSelectParamV2
              key={parameter.identifier}
              parameter={parameter}
              parameters={parameterValues}
              setParameters={setParameters}
              required={required}
            />
          );
        }

        if (parameter.values?.length) {
          return (
            <DropDownSingleSelectParam
              key={displayParameter.identifier}
              intl={intl}
              parameter={displayParameter}
              parameters={parameterValues}
              setParameters={setParameters}
              titleOverride={getTitleOverride(displayParameter)}
              showItemsWithUnit={isUnitShown()}
              required={required}
            />
          );
        } else {
          return (
            <ParameterTextBox
              key={displayParameter.identifier}
              intl={intl}
              parameter={displayParameter}
              parameterValues={parameterValues}
              setParameters={setParameters}
              validator={validators.get(selectedTemplate.identifier)}
              required={required}
            />
          );
        }
      });
    }

    return <div />;
  };

  return <div>{renderParameterList()}</div>;
};

ParameterComponentSelector.propTypes = {
  parameterValues: object.isRequired,
  parameters: arrayOf(shape(complianceCriteriaParameterShape)).isRequired,
  setParameters: func.isRequired,
  selectedTemplate: object,
};

ParameterComponentSelector.defaultProps = {
  selectedTemplate: null,
};

export default ParameterComponentSelector;
