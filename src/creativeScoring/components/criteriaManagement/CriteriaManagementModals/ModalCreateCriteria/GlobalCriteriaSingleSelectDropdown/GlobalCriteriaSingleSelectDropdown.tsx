import React, { useMemo } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobFormControl,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import SectionLabel from '../SectionLabel';
import { InfoFilledIcon } from '../../../../../../assets/vidmob-mui-icons/general';
import CREATIVE_SCORING_CONSTANTS from '../../../../../constants/creativeScoring.constants';
import SingleValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/SingleValueInput';
import { SingleValue } from '../../../../../../components/ReportFilters/types';

export interface GlobalCriteriaOption {
  id: number;
  label: string;
}

interface GlobalCriteriaSingleSelectDropdown {
  isGlobalCriteria: GlobalCriteriaOption;
  setIsGlobalCriteria: (value: GlobalCriteriaOption) => void;
  showWarning?: boolean;
}

const GlobalCriteriaSingleSelectDropdown = (
  globalCriteriaSingleSelectDropdown: GlobalCriteriaSingleSelectDropdown,
) => {
  const {
    isGlobalCriteria,
    setIsGlobalCriteria,
    showWarning = false,
  } = globalCriteriaSingleSelectDropdown;

  const intl = useIntl();
  const { CRITERIA_IS_GLOBAL_OPTIONS } = CREATIVE_SCORING_CONSTANTS;

  const isGlobalOptions = useMemo(
    () => [
      CRITERIA_IS_GLOBAL_OPTIONS.WORKSPACE,
      CRITERIA_IS_GLOBAL_OPTIONS.ORGANIZATION,
    ],
    [],
  );

  const renderWarningBanner = () => (
    <VidMobStack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      sx={{
        height: '64px',
        borderRadius: '6px',
        gap: '12px',
        backgroundColor: '#E1F5FE',
        p: '12px',
      }}
    >
      <InfoFilledIcon />
      <VidMobTypography variant="body2">
        <>
          {isGlobalCriteria?.id
            ? intl.messages[
                'ui.creativeScoring.criteriaManagementV2.action.edit.modal.warning.organization'
              ]
            : intl.messages[
                'ui.creativeScoring.criteriaManagementV2.action.edit.modal.warning.workspace'
              ]}
        </>
      </VidMobTypography>
    </VidMobStack>
  );

  const dropdownValue = {
    id: isGlobalCriteria.id,
    name: intl.formatMessage({ id: isGlobalCriteria.label }),
  };

  const dropdownOptions = isGlobalOptions.map((option) => ({
    id: option.id,
    name: intl.formatMessage({ id: option.label }),
  }));

  const handleDropdownSelection = (selectedItem: SingleValue) => {
    const selectedOption = isGlobalOptions.find(
      (option) => option.id === selectedItem.id,
    );

    if (selectedOption) {
      setIsGlobalCriteria(selectedOption);
    }
  };

  return (
    <VidMobBox>
      <SectionLabel
        sectionTitle={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.add.criteria.organizationCriteria',
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.add.criteria.organizationCriteria.tooltip',
        })}
      />
      {showWarning && renderWarningBanner()}
      <VidMobFormControl fullWidth sx={{ mt: '8px' }}>
        <SingleValueInput
          value={dropdownValue}
          valueOptions={dropdownOptions}
          onChange={handleDropdownSelection}
          buttonWidth="500px"
          customButtonSx={{ height: '48px' }}
          labelTextSx={{
            fontWeight: '400',
          }}
          slotSx={{ width: '482px' }}
        />
      </VidMobFormControl>
    </VidMobBox>
  );
};

export default GlobalCriteriaSingleSelectDropdown;
