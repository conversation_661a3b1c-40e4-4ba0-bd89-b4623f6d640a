import React, { useMemo } from 'react';
import { ListItem } from '../../../../../../muiCustomComponents/MultiSelectDropdown/MultiSelectDropdown';
import getMUIIconForChannel from '../../../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { RemovableChipsDropdownLabel } from '../../../../../../muiCustomComponents/Dropdowns/RemovableChipsDrowpdownLabel';
import { DropdownOption } from '../../../../../../muiCustomComponents/Dropdowns/types';

const channelIconSx = {
  width: '18px',
  mr: '6px',
};

interface Props {
  selectedPlatforms: ListItem[];
  setSelectedPlatforms: (selectedItems: ListItem[]) => void;
  explicitParentWidth: number;
}

const DropDownChannelLabel = ({
  selectedPlatforms,
  setSelectedPlatforms,
  explicitParentWidth,
}: Props) => {
  const selectedItems = useMemo(
    () =>
      selectedPlatforms.map((item) => ({
        ...item,
        id: String(item.id),
        name: item.label as string,
        icon: getMUIIconForChannel(item.id as string, channelIconSx, true),
      })),
    [selectedPlatforms],
  );

  const setSelectedItems = (selectedItems: DropdownOption[]) => {
    const updatedListItems = selectedItems.map((item) => ({
      id: item.id,
      label: item.name,
    }));
    setSelectedPlatforms(updatedListItems);
  };

  return (
    <RemovableChipsDropdownLabel
      explicitParentWidth={explicitParentWidth}
      selectedItems={selectedItems}
      setSelectedItems={setSelectedItems}
    />
  );
};

export default DropDownChannelLabel;
