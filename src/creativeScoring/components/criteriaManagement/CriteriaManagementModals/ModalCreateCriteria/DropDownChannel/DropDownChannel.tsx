import React from 'react';
import { useDispatch } from 'react-redux';
import { ListItem as LabelTypeListItem } from '../../../../../../muiCustomComponents/MultiSelectDropdown/MultiSelectDropdown';
import { ListItem as NameTypeListItem } from '../../../../../../components/ReportFilters/types';
import { useIntl } from 'react-intl';
import { COMPLIANCE } from '../../../../../../constants';
import { setCriteriaMgmtPlatform } from '../../../../../redux/actions/criteriaManagement.actions';
import SectionLabel from '../SectionLabel';
import { VidMobFormControl } from '../../../../../../vidMobComponentWrappers';
import { FormHelperText } from '@mui/material';
import DropDownChannelLabel from './DropDownChannelLabel';
import MultiValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';

interface DropDownChannelProps {
  listItems: LabelTypeListItem[];
  selectedPlatforms?: LabelTypeListItem[];
  setSelectedPlatforms: (selectedItems: LabelTypeListItem[]) => void;
  setSelectedTemplate?: (arg0: string | null) => void;
  resetCustomValues: () => void;
  setApplicability: (arg0: any) => void;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  iconSize?: 'small' | 'medium' | 'large';
}

const width = 500;

const DropDownChannel: React.FC<DropDownChannelProps> = ({
  listItems,
  setSelectedPlatforms,
  selectedPlatforms,
  setApplicability,
  resetCustomValues,
  error,
  helperText,
  required,
  iconSize,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const valueOptions = listItems.map((item) => ({
    ...item,
    id: String(item.id),
    name: item.label as string,
  }));

  const value = (selectedPlatforms || []).map((item) => ({
    ...item,
    id: String(item.id),
    name: item.label as string,
  }));

  const handleSelect = (selectedChannels: NameTypeListItem[]) => {
    const selectedChannelsIds = selectedChannels.map(
      (item: NameTypeListItem) => item.id,
    );
    const fullSelections = listItems?.filter((item) =>
      selectedChannelsIds.includes(item.id as string),
    );
    setSelectedPlatforms(fullSelections);
    setApplicability([]);
    resetCustomValues();
    // sets the channel in redux to support showing recommendations if only one channel is selected
    if (selectedChannels.length === 1) {
      dispatch(setCriteriaMgmtPlatform(selectedChannelsIds[0]));
    }

    if (selectedChannels.length > 1) {
      dispatch(
        setCriteriaMgmtPlatform(COMPLIANCE.ALL_PLATFORMS_TEMPLATE_IDENTIFIER),
      );
    }
  };

  return (
    <VidMobFormControl sx={{ width: `${width}px`, gap: '8px' }} error={error}>
      <SectionLabel
        required={required}
        sectionTitle={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.platform.dropdown.description',
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.platform.dropdown.helperText',
        })}
        iconSize={iconSize}
      />
      <MultiValueInput
        labelHasPills
        isChannelDropdown
        displaySearch
        shouldShowSelectAll
        valueOptions={valueOptions}
        value={value}
        onChange={handleSelect}
        LabelElement={() => (
          <DropDownChannelLabel
            selectedPlatforms={selectedPlatforms || []}
            setSelectedPlatforms={setSelectedPlatforms}
            explicitParentWidth={width}
          />
        )}
        disableButtonRipple={(selectedPlatforms || [])?.length > 0}
        error={error}
        customButtonSx={{ minHeight: '48px', height: 'fit-content' }}
        labelTextSx={{
          fontWeight: '400',
        }}
        slotSx={{
          width: '482px',
        }}
      />
      {helperText ? (
        <FormHelperText sx={{ marginLeft: 'unset' }}>
          {helperText}
        </FormHelperText>
      ) : null}
    </VidMobFormControl>
  );
};

export default DropDownChannel;
