import React from 'react';
import { InfoFilledIcon } from '../../../../../assets/vidmob-mui-icons/general';
import {
  VidMobBox,
  VidMobFormLabel,
  VidMobTooltip,
} from '../../../../../vidMobComponentWrappers';
import { IconButton, SxProps } from '@mui/material';

interface SectionHeaderProps {
  sectionTitle: string;
  tooltipText: string;
  required?: boolean;
  iconSize?: 'small' | 'medium' | 'large';
  labelCustomSx?: SxProps;
}

const SectionLabel: React.FC<SectionHeaderProps> = ({
  sectionTitle,
  tooltipText,
  required,
  iconSize = 'small',
  labelCustomSx,
}) => {
  const labelSx = {
    color: 'black !important',
    fontWeight: 600,
    fontSize: '14px',
    lineHeight: '20px',
    ...labelCustomSx,
  };

  const tooltipSx = {
    bgcolor: 'rgba(0, 0, 0, 0.6) !important',
    color: 'white',
    maxWidth: '235px',
    marginLeft: '10px',
  };

  const iconButtonSx = {
    height: '0 !important',
    color: 'rgba(0, 0, 0, 0.6) !important',
    '&:hover': {
      backgroundColor: 'transparent !important',
      color: 'rgba(0, 0, 0, 0.6) !important',
    },
  };

  return (
    <VidMobBox sx={{ display: 'flex', alignItems: 'center' }}>
      <VidMobFormLabel sx={labelSx}>
        {required ? `${sectionTitle}*` : sectionTitle}
      </VidMobFormLabel>
      <VidMobTooltip
        placement="top-start"
        disableInteractive
        title={tooltipText}
        PopperProps={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, -10],
              },
            },
          ],
        }}
        componentsProps={{
          tooltip: {
            sx: tooltipSx,
          },
        }}
      >
        <IconButton sx={iconButtonSx} size={iconSize}>
          <InfoFilledIcon
            sx={{
              color: 'rgba(0, 0, 0, 0.6) !important',
            }}
            fontSize={iconSize}
          />
        </IconButton>
      </VidMobTooltip>
    </VidMobBox>
  );
};

export default SectionLabel;
