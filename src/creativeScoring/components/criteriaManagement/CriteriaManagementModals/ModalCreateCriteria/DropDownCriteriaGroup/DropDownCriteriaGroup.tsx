import React, { useRef } from 'react';
import { VidMobStack } from '../../../../../../vidMobComponentWrappers';
import SectionLabel from '../SectionLabel';
import { useIntl } from 'react-intl';
import MultiValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/MultiValueInput';
import { RemovableChipsDropdownLabel } from '../../../../../../muiCustomComponents/Dropdowns/RemovableChipsDrowpdownLabel';
import { DropdownOption } from '../../../../../../muiCustomComponents/Dropdowns/types';

const containerSx = {
  position: 'relative',
  maxWidth: '500px',
  marginTop: '24px',
  gap: 4,
};

type CriteriaGroup = {
  id: string;
  label: string;
  color?: string;
};

type DropDownCriteriaGroupProps = {
  listItems?: CriteriaGroup[];
  selectedCriteriaGroups: string[];
  setSelectedCriteriaGroups: (selected: string[]) => void;
};

const DropDownCriteriaGroup: React.FC<DropDownCriteriaGroupProps> = ({
  listItems,
  selectedCriteriaGroups,
  setSelectedCriteriaGroups,
}) => {
  const intl = useIntl();
  const labelElementParentRef = useRef<HTMLDivElement>(null);

  const valueOptions = (listItems || []).map((item) => ({
    ...item,
    id: String(item.id),
    name: item.label as string,
  }));

  const selectedItems = (selectedCriteriaGroups || []).map(
    (selectedCriteriaGroupId) => {
      const selectedCriteriaGroup = listItems?.find(
        (item) => item.id === selectedCriteriaGroupId,
      );
      return {
        id: selectedCriteriaGroupId,
        name: selectedCriteriaGroup?.label || '',
        color: selectedCriteriaGroup?.color,
      };
    },
  );

  const handleSelect = (selectedCriteriaGroups: DropdownOption[]) => {
    const selectedCriteriaGroupIds = selectedCriteriaGroups.map(
      (item) => item.id,
    );
    setSelectedCriteriaGroups(selectedCriteriaGroupIds as string[]);
  };

  return (
    <VidMobStack sx={containerSx}>
      <SectionLabel
        sectionTitle={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.criteriaGroup.dropdown.description',
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.criteriaGroup.dropdown.helperText',
        })}
        iconSize="small"
      />
      <MultiValueInput
        labelHasPills
        labelElementParentRef={labelElementParentRef}
        displaySearch
        shouldShowSelectAll
        valueOptions={valueOptions}
        value={selectedItems}
        onChange={handleSelect}
        LabelElement={() => (
          <RemovableChipsDropdownLabel
            parentRef={labelElementParentRef}
            selectedItems={selectedItems}
            setSelectedItems={handleSelect}
          />
        )}
        disableButtonRipple={(selectedCriteriaGroups || [])?.length > 0}
        noChips
        customButtonSx={{
          minHeight: '48px',
          height: 'fit-content',
        }}
        labelTextSx={{
          fontWeight: '400',
        }}
        slotSx={{
          width: '482px',
        }}
      />
    </VidMobStack>
  );
};

export default DropDownCriteriaGroup;
