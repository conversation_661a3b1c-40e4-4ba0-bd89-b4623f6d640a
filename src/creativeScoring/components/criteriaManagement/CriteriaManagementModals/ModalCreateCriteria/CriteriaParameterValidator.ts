import { CRITERIA_TEMPLATE_IDENTIFIERS } from '../../../../constants/creativeScoring.constants';

export type CriteriaParameterValidator = {
  validator: (param: string, value: string | number) => boolean;
  errorIntlMessageKey: (param: string) => string;
};

function validateInput(text: string) {
  // exclude special characters - but allow languages
  const allowedCharsRegex = /^[^!@#$%^&*()_+={}[\]:;"'<>?/~`|\\-]+$/u;
  const containsInvalidChars = !allowedCharsRegex.test(text);
  return text.length > 0 && !containsInvalidChars;
}

class FileSizeLimitValidator {
  MAX_FILE_SIZE: number = 2250; // 2.25GB which currently is the max file size limit for videos
  MAX_FILE_SIZE_PARAM_NAME: string = 'maxFileSize';

  validator(param: string, value: string | number): boolean {
    if (param === this.MAX_FILE_SIZE_PARAM_NAME) {
      const isValidInput = validateInput(String(value));
      const isInteger = Number.isInteger(Number(value));
      const isValidFileSize = this.isFileSizeValid(value);

      return isValidInput && isInteger && isValidFileSize;
    }

    return false;
  }

  errorIntlMessageKey(param: string): string {
    if (param === this.MAX_FILE_SIZE_PARAM_NAME) {
      return 'ui.compliance.criteriaManagement.modal.criteria.file.size.limit.max.file.size.textarea.color.error';
    }

    return 'global.error.status';
  }

  private isFileSizeValid(value: string | number): boolean {
    const fileSize = Number(value);
    return fileSize > 0 && fileSize <= this.MAX_FILE_SIZE;
  }
}

export const validators = new Map<string, CriteriaParameterValidator>([
  [CRITERIA_TEMPLATE_IDENTIFIERS.FILE_SIZE_LIMIT, new FileSizeLimitValidator()],
]);
