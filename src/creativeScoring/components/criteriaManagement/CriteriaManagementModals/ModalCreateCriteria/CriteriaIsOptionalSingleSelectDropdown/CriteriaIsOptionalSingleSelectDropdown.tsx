import React, { useMemo } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobFormControl,
} from '../../../../../../vidMobComponentWrappers';
import SectionLabel from '../SectionLabel';
import CREATIVE_SCORING_CONSTANTS from '../../../../../constants/creativeScoring.constants';
import SingleValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/SingleValueInput';
import { SingleValue } from '../../../../../../components/ReportFilters/types';

const formControlSx = {
  mb: '20px',
  mt: '8px',
};

export interface CriteriaOption {
  id: number;
  label: string;
}

interface CriteriaIsOptionalSingleSelectDropdown {
  criteriaIsOptional: CriteriaOption;
  setCriteriaIsOptional: (value: CriteriaOption) => void;
  required?: boolean;
}

const CriteriaIsOptionalSingleSelectDropdown = (
  criteriaIsOptionalSingleSelectDropdown: CriteriaIsOptionalSingleSelectDropdown,
) => {
  const intl = useIntl();
  const { criteriaIsOptional, setCriteriaIsOptional } =
    criteriaIsOptionalSingleSelectDropdown;
  const { CRITERIA_IS_OPTIONAL_OPTIONS } = CREATIVE_SCORING_CONSTANTS;
  const criteriaIsOptionalOptions = useMemo(() => {
    return [
      CRITERIA_IS_OPTIONAL_OPTIONS.MANDATORY,
      CRITERIA_IS_OPTIONAL_OPTIONS.OPTIONAL,
    ];
  }, []);

  const dropdownValue = {
    id: criteriaIsOptional.id,
    name: intl.formatMessage({
      id: criteriaIsOptional.label,
      defaultMessage: '',
    }),
  };

  const dropdownOptions = criteriaIsOptionalOptions.map((option) => ({
    id: option.id,
    name: intl.formatMessage({ id: option.label, defaultMessage: '' }),
  }));

  const handleDropdownSelection = (selectedItem: SingleValue) => {
    const selectedOption = criteriaIsOptionalOptions.find(
      (option) => option.id === selectedItem.id,
    );

    if (selectedOption) {
      setCriteriaIsOptional(selectedOption);
    }
  };

  return (
    <VidMobBox>
      <SectionLabel
        sectionTitle={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.add.criteria.criteriaIsOptional.consideration',
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.add.criteria.criteriaIsOptional.consideration.content.tooltip',
        })}
        required={criteriaIsOptionalSingleSelectDropdown.required}
      />
      <VidMobFormControl fullWidth sx={formControlSx}>
        <SingleValueInput
          value={dropdownValue}
          valueOptions={dropdownOptions}
          onChange={handleDropdownSelection}
          buttonWidth="500px"
          customButtonSx={{ height: '48px' }}
          labelTextSx={{
            fontWeight: '400',
          }}
          slotSx={{ width: '482px' }}
        />
      </VidMobFormControl>
    </VidMobBox>
  );
};

export default CriteriaIsOptionalSingleSelectDropdown;
