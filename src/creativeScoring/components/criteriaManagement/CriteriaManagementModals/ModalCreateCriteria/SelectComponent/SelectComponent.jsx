import React from 'react';
import { FormControl } from '@mui/material';
import PropTypes from 'prop-types';
import { useIntl } from 'react-intl';
import './SelectComponent.scss';
import { VidMobFormItem } from '../../../../../../vidMobComponentWrappers';
import SingleValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/SingleValueInput';

const truncateText = (text, maxLength) => {
  if (text.length > maxLength) {
    return text.substring(0, maxLength) + '...';
  }

  return text;
};

const SelectComponent = ({
  items,
  onChange,
  selectedOption,
  className,
  title,
  helperText,
  isSearchable,
  getLabel,
}) => {
  const intl = useIntl();

  const renderLabel = (option) => {
    if (getLabel && typeof getLabel === 'function') {
      return getLabel(option);
    }

    if (option.name && typeof option.name === 'string') {
      return truncateText(option.name, 50);
    }

    return option.identifier;
  };

  const value = items.find((item) => item.id === selectedOption) || null;
  const valueOptions = items.map((item) => ({
    id: item.id,
    name: renderLabel(item),
  }));

  return (
    <div className={'select-component ' + className}>
      <VidMobFormItem
        itemLabel={
          title ||
          intl.messages[
            'ui.compliance.criteriaManagement.modal.criteria.custom.selected.label'
          ]
        }
        helperText={helperText}
      >
        <FormControl fullWidth>
          <SingleValueInput
            displaySearch={isSearchable}
            isDisabled={items.length === 0}
            value={value}
            valueOptions={valueOptions}
            onChange={onChange}
            buttonWidth="500px"
            customButtonSx={{ height: '48px' }}
            labelTextSx={{
              fontWeight: '400',
            }}
            slotSx={{ width: '482px' }}
          />
        </FormControl>
      </VidMobFormItem>
    </div>
  );
};

SelectComponent.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.string,
      iconUrl: PropTypes.string,
    }),
  ),
  onChange: PropTypes.func,
  className: PropTypes.string,
  title: PropTypes.string,
  helperText: PropTypes.string,
  isSearchable: PropTypes.bool,
  getLabel: PropTypes.string,
};

export default SelectComponent;
