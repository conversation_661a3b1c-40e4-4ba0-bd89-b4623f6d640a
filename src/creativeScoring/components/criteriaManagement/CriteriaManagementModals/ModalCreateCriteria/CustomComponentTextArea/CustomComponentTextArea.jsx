import React, { useState, useEffect } from 'react';
import { getIntl } from '../../../../../../utils/getIntl';
import TextField from '@mui/material/TextField';
import PropTypes from 'prop-types';
import { VidMobFormItem } from '../../../../../../vidMobComponentWrappers';
import { FormControl } from '@mui/material';
import { COMPLIANCE } from '../../../../../../constants';

const { CUSTOM_CRITERIA_IDENTIFIERS } = COMPLIANCE;

function validateColors(text) {
  const hexColorRegex = /^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  const colors = text.split(',').map((color) => color.trim());
  const invalidColors = colors.filter((color) => !hexColorRegex.test(color));

  return invalidColors.length <= 0;
}

function validateText(text) {
  // exclude special characters - but allow languages
  const allowedCharsRegex = /^[^!@#$%^&*()_+={}[\]:;"'<>?/~`|\\-]+$/u;
  const containsInvalidChars = !allowedCharsRegex.test(text);
  return text.length > 0 && !containsInvalidChars;
}

const CustomComponentTextArea = ({ type, onChange, textValue }) => {
  const intl = getIntl();
  const [errorMessage, setErrorMessage] = useState('');

  const getIntlKey = (type) => {
    return type.toLowerCase().split('_').pop();
  };

  function handleCustomValuesChange(event) {
    const isValid =
      type === CUSTOM_CRITERIA_IDENTIFIERS.CUSTOM_COLOR_IDENTIFIER
        ? validateColors(event.target.value)
        : validateText(event.target.value);

    if (isValid) {
      onChange({ value: event.target.value, isValid });
      setErrorMessage('');
    } else {
      onChange({ isValid: false });
      const intlKey = getIntlKey(type);
      setErrorMessage(
        intl.messages[
          `ui.compliance.criteriaManagement.modal.criteria.custom.textarea.${intlKey}.error`
        ],
      );
    }
  }

  useEffect(() => {
    if (type) {
      setErrorMessage('');
    }
  }, [type]);

  function getPlaceholder() {
    const name = getIntlKey(type);
    return intl.messages[
      `ui.compliance.criteriaManagement.modal.criteria.custom.textarea.${name}.placeholder`
    ];
  }

  function getHelperText() {
    const name = getIntlKey(type);
    return intl.messages[
      `ui.compliance.criteriaManagement.modal.criteria.custom.textarea.${name}.formhelperText`
    ];
  }

  return (
    <FormControl fullWidth>
      <VidMobFormItem
        itemLabel={
          intl.messages[
            'ui.compliance.criteriaManagement.modal.criteria.creative.details.label'
          ]
        }
        helperText={getHelperText()}
        error={errorMessage}
      >
        <TextField
          className="custom-parameter-suggestion-textarea"
          fullWidth
          multiline
          placeholder={getPlaceholder()}
          rows={3}
          onChange={handleCustomValuesChange}
          variant="outlined"
          value={textValue}
        />
      </VidMobFormItem>
    </FormControl>
  );
};

CustomComponentTextArea.propTypes = {
  type: PropTypes.oneOf(CUSTOM_CRITERIA_IDENTIFIERS),
  onChange: PropTypes.func,
};

export default CustomComponentTextArea;
