import React from 'react';
import { VidMobFormControl } from '../../../../../../vidMobComponentWrappers';
import { Template } from '../../../../../../types/template.type';
import { useCriteriaTemplatesByCategory } from './useCriteriaTemplatesByCategory';
import { VidMobFormHelperText } from '../../../../../../vidMobComponentWrappers';
import SingleValueInput from '../../../../../../components/ReportFilters/components/FilterValueInput/SingleValueInput';
import {
  ListItem,
  SingleValue,
} from '../../../../../../components/ReportFilters/types';

interface DropDownRuleProps {
  selectedTemplate: Template | null;
  handleSelectRule: (selectedItem: SingleValue) => void;
  setSearchText: (searchText: string) => void;
  selectedCategory: string;
  selectedPlatforms: string[];
  criteriaTemplates: Template[];
  searchText: string;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
}

const DropDownRule: React.FC<DropDownRuleProps> = ({
  handleSelectRule,
  selectedTemplate,
  setSearchText,
  selectedCategory,
  criteriaTemplates,
  searchText,
  disabled,
  error,
  helperText,
}) => {
  const criteriaTemplatesByCategory = useCriteriaTemplatesByCategory({
    criteriaTemplates,
    selectedCategory,
    searchText,
  });

  const dropdownValue = selectedTemplate
    ? {
        id: selectedTemplate.id,
        name: selectedTemplate.defaultDisplayName,
      }
    : null;

  const dropdownOptions: ListItem[] = [];

  Object.entries(criteriaTemplatesByCategory).forEach(
    ([category, templates]) => {
      dropdownOptions.push({
        id: category,
        name: category,
        isDivider: true,
      });
      templates.forEach((template) => {
        dropdownOptions.push({
          id: template.id,
          name: template.defaultDisplayName,
          description: template.description,
          parent: category,
        });
      });
    },
  );

  return (
    <div>
      <VidMobFormControl fullWidth error={error} variant="outlined">
        <SingleValueInput
          displaySearch
          onSearchChange={setSearchText}
          hasGroupSelect={false}
          isDisabled={disabled}
          value={dropdownValue}
          valueOptions={dropdownOptions}
          onChange={handleSelectRule}
          buttonWidth="500px"
          customButtonSx={{ height: '48px' }}
          labelTextSx={{
            fontWeight: '400',
          }}
          slotSx={{ width: '482px' }}
        />
        {helperText && (
          <VidMobFormHelperText sx={{ marginTop: '8px', marginLeft: '0px' }}>
            {helperText as string}
          </VidMobFormHelperText>
        )}
      </VidMobFormControl>
    </div>
  );
};

export default DropDownRule;
