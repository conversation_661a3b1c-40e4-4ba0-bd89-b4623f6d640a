import React from 'react';
import {
  Vid<PERSON>ob<PERSON><PERSON><PERSON>,
  VidMobList<PERSON><PERSON><PERSON>er,
  VidMobMenuItem,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
// types, constants
import { Template } from '../../../../../../types/template.type';
import { CRITERIA_CATEGORIES } from '../../../../../constants/criteriaManagement.constants';

interface DropDownRuleListProps {
  criteriaTemplatesByCategory: { [category: string]: Template[] };
  selectedCategory: string;
  handleSelectRule: (templateId: string) => void;
  setOpen: (open: boolean) => void;
}

const DropDownRuleList: React.FC<DropDownRuleListProps> = ({
  criteriaTemplatesByCategory,
  selectedCategory,
  handleSelectRule,
  setOpen,
}) => {
  const categories = Object.keys(criteriaTemplatesByCategory);

  const handleOnChange = (template: Template) => {
    handleSelectRule(template.id);
    setOpen(false);
  };

  const menuItemStyles = {
    fontSize: '14px',
    p: '8px',
    mr: '8px',
    borderRadius: '6px',
  };

  const CategoryHeader: React.FC<{ category: string }> = ({ category }) => {
    if (selectedCategory !== CRITERIA_CATEGORIES.ALL) return null;

    return (
      <VidMobListSubheader sx={{ zIndex: 0, p: 0 }}>
        <VidMobDivider sx={{ m: '0 16px 0 4px' }} />
        <VidMobTypography
          sx={{ fontSize: '12px', fontWeight: 500, color: '#757575', m: '8px' }}
        >
          {category}
        </VidMobTypography>
      </VidMobListSubheader>
    );
  };

  return (
    <>
      {categories.map((category: string) => {
        const templates = criteriaTemplatesByCategory[category];
        return (
          <div key={category}>
            <CategoryHeader category={category} />
            {templates.map((template: Template) => (
              <VidMobMenuItem
                key={template.id}
                value={template.id}
                onClick={() => handleOnChange(template)}
                sx={menuItemStyles}
              >
                <VidMobStack direction="column" spacing={1}>
                  <VidMobTypography variant="body2">
                    {template.defaultDisplayName || ''}
                  </VidMobTypography>
                  {template.description && (
                    <VidMobTypography
                      variant="caption"
                      color="#757575"
                      sx={{ whiteSpace: 'break-spaces' }}
                    >
                      {template.description}
                    </VidMobTypography>
                  )}
                </VidMobStack>
              </VidMobMenuItem>
            ))}
          </div>
        );
      })}
    </>
  );
};

export default DropDownRuleList;
