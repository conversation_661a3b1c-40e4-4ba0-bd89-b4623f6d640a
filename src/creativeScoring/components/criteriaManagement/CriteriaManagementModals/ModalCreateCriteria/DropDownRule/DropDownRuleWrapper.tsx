import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import DropDownRule from './DropDownRule';
import {
  VidMobBox,
  VidMobCircularProgress,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../vidMobComponentWrappers';
import { SxProps, Theme } from '@mui/material/styles';
import {
  getCriteriaTemplates,
  getIsCriteriaTemplatesLoading,
} from '../../../../../redux/selectors/criteriaManagement.selectors';
import { Template } from '../../../../../../types/template.type';
import { SingleValue } from '../../../../../../components/ReportFilters/types';

interface DropDownCriteriaV2Props {
  selectedTemplate: Template | null;
  selectedCategory: string;
  setParameters: (parameters: any) => void;
  setSelectedTemplate: (template: Template) => void;
  selectedPlatforms: string[];
  resetCustomValues: () => void;
  disabled?: boolean;
  dropdownWrapperSx?: SxProps<Theme>;
  error?: boolean;
  helperText?: string;
  required?: boolean;
}

const DropDownRuleWrapper: React.FC<DropDownCriteriaV2Props> = ({
  selectedTemplate,
  selectedCategory,
  setParameters,
  setSelectedTemplate,
  selectedPlatforms,
  resetCustomValues,
  disabled,
  dropdownWrapperSx = {},
  error,
  helperText,
}) => {
  const criteriaTemplates = useSelector(getCriteriaTemplates);
  const isCriteriaTemplatesLoading = useSelector(getIsCriteriaTemplatesLoading);
  const [searchText, setSearchText] = useState('');

  if (isCriteriaTemplatesLoading) {
    return (
      <VidMobStack width="100%" alignItems="center">
        <VidMobCircularProgress />
      </VidMobStack>
    );
  }

  const handleSelectRule = (selectedItem: SingleValue) => {
    const currentTemplate = criteriaTemplates.find(
      (t: Template) => t.id === selectedItem.id,
    );

    setParameters({});
    resetCustomValues();
    setSearchText('');
    setSelectedTemplate(currentTemplate);
  };

  return (
    <VidMobBox
      className="drop-down-criteria-wrapper"
      sx={{ mb: '24px', width: '500px', ...dropdownWrapperSx }}
    >
      <DropDownRule
        handleSelectRule={handleSelectRule}
        selectedTemplate={selectedTemplate || null}
        setSearchText={setSearchText}
        criteriaTemplates={criteriaTemplates}
        selectedPlatforms={selectedPlatforms}
        selectedCategory={selectedCategory}
        searchText={searchText}
        disabled={disabled}
        error={error}
        helperText={helperText}
      />
      {selectedTemplate && selectedTemplate.description && (
        <VidMobTypography
          variant="body2"
          color="text.secondary"
          sx={{ mt: '12px' }}
        >
          {selectedTemplate.description}
        </VidMobTypography>
      )}
    </VidMobBox>
  );
};

export default DropDownRuleWrapper;
