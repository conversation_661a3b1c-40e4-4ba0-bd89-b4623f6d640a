import { useCallback } from 'react';
import { COMPLIANCE } from '../../../../../../constants';
import { containsSearchText } from '../../../../reports/rollUpReport/rollUpReport.utils';
import { CRITERIA_CATEGORIES } from '../../../../../constants/criteriaManagement.constants';
import { Template } from '../../../../../../types/template.type';

const { ALL_PLATFORMS_TEMPLATE_IDENTIFIER } = COMPLIANCE;

interface UseCriteriaTemplateListItemsProps {
  criteriaTemplates: Template[];
  selectedCategory: string;
  searchText: string;
}

// We should only surface standard templates in the dropdown.

export const useCriteriaTemplatesByCategory = ({
  criteriaTemplates,
  selectedCategory,
  searchText,
}: UseCriteriaTemplateListItemsProps): { [p: string]: Template[] } => {
  const getGenericTemplates = useCallback((templates: Template[]) => {
    const seenIdentifiers = new Set();

    const standardTemplates = templates?.filter((template: Template) => {
      const isUniqueTemplate = !seenIdentifiers.has(template.identifier);
      const isGenericTemplate =
        template.platformIdentifier === ALL_PLATFORMS_TEMPLATE_IDENTIFIER;

      if (isGenericTemplate) {
        seenIdentifiers.add(template.identifier);
      }

      return isGenericTemplate && isUniqueTemplate;
    });

    return standardTemplates;
  }, []);

  const filterTemplatesBySearchText = (templates: Template[]) =>
    templates.filter((template) =>
      containsSearchText(template.defaultDisplayName, searchText),
    );

  const filteredTemplatesBySearch = filterTemplatesBySearchText(
    getGenericTemplates(criteriaTemplates),
  );

  const filterTemplatesByCategory = () => ({
    [selectedCategory]: filteredTemplatesBySearch.filter(
      (template: Template) => template.category === selectedCategory,
    ),
  });

  const sortAlphabetically = (templates: Template[]) =>
    templates.sort((a, b) =>
      a.defaultDisplayName.localeCompare(b.defaultDisplayName),
    );

  const groupTemplatesByCategory = () => {
    const groupedByCategory: { [key: string]: Template[] } = {};

    // Group templates by category
    filteredTemplatesBySearch.forEach((template) => {
      const categoryKey = template.category;

      if (!groupedByCategory[categoryKey]) {
        groupedByCategory[categoryKey] = [];
      }
      groupedByCategory[categoryKey].push(template);
    });

    // Sort templates within each category
    for (const category in groupedByCategory) {
      groupedByCategory[category] = sortAlphabetically(
        groupedByCategory[category],
      );
    }

    return groupedByCategory;
  };

  if (selectedCategory === CRITERIA_CATEGORIES.ALL) {
    return groupTemplatesByCategory();
  } else {
    return filterTemplatesByCategory();
  }
};
