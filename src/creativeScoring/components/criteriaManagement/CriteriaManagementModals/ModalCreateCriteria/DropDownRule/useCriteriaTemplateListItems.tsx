import { useMemo } from 'react';
import { CriteriaTemplate } from '../../../../../types/criteriaManagement.types';
import { COMPLIANCE } from '../../../../../../constants';

const { ALL_PLATFORMS_TEMPLATE_IDENTIFIER } = COMPLIANCE;

interface UseCriteriaTemplateListItemsProps {
  selectedPlatforms: string[];
  criteriaTemplates: CriteriaTemplate[];
}

export const useCriteriaTemplateListItems = ({
  selectedPlatforms,
  criteriaTemplates,
}: UseCriteriaTemplateListItemsProps): CriteriaTemplate[] => {
  const filteredTemplates = useMemo(() => {
    const isMultiplePlatformsSelected = selectedPlatforms.length > 1;
    const isStandardSelected =
      selectedPlatforms.length === 1 &&
      selectedPlatforms[0] === ALL_PLATFORMS_TEMPLATE_IDENTIFIER;

    const standardTemplates = criteriaTemplates?.filter(
      (template: CriteriaTemplate) =>
        template.platformIdentifier === ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
    );

    // if more than one platform selected, use ALL_PLATFORMS (Standard) templates
    if (isMultiplePlatformsSelected || isStandardSelected) {
      return standardTemplates;
    }

    const selectedPlatform = selectedPlatforms[0];
    const templatesForSelectedPlatform = criteriaTemplates?.filter(
      (template: CriteriaTemplate) =>
        template.platformIdentifier === selectedPlatform,
    );

    // if only one platform is selected and it is not Standard, show all standard plus platform specific templates.
    // Remove standard templates that would be duplicates
    const uniqueStandardPlatforms = standardTemplates?.filter(
      (sTemplate) =>
        !templatesForSelectedPlatform.some(
          (pTemplate) => pTemplate.identifier === sTemplate.identifier,
        ),
    );

    return [...uniqueStandardPlatforms, ...templatesForSelectedPlatform];
  }, [criteriaTemplates, selectedPlatforms]);

  return filteredTemplates.sort(
    (a: { defaultDisplayName: string }, b: { defaultDisplayName: any }) =>
      a.defaultDisplayName.localeCompare(b.defaultDisplayName),
  );
};
