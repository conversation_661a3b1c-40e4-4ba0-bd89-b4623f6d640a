import React, { useMemo, useCallback } from 'react';
import { bool, func, shape, string } from 'prop-types';
import {
  complianceCriteriaParameterShape,
  complianceParameterValuesShape,
} from '../../../../../featureServices/shapes';
import { COMPLIANCE } from '../../../../../../constants';
import { useIntl } from 'react-intl';

import './DropDownSingleSelectParam.scss';
import SelectComponent from '../SelectComponent';
import { isNil } from '../../../../../../utils/typeCheckUtils';

const {
  CRITERIA_PARAMETER_UNIT_LABEL,
  BOOLEAN_VALUE_STRINGS,
  CRITERIA_PARAMETER_UNIT_NAME,
} = COMPLIANCE;

const DropDownSingleSelectParam = ({
  parameter,
  setParameters,
  showItemsWithUnit,
  titleOverride,
  parameters,
  required,
}) => {
  const intl = useIntl();
  const boolDataType = 'BOOLEAN';
  const { identifier } = parameter;

  const handleChange = (value) => {
    setParameters((prevState) => {
      if (parameter?.identifier === CRITERIA_PARAMETER_UNIT_NAME.MIN_DURATION) {
        return { [parameter?.identifier]: value.id };
      }

      return {
        ...prevState,
        [parameter?.identifier]: value.id,
      };
    });
  };

  const items = useMemo(
    () =>
      parameter.values.map((value) => {
        const name = showItemsWithUnit
          ? `${value} ${intl.formatMessage({ id: CRITERIA_PARAMETER_UNIT_LABEL[parameter?.units] }, { count: value })}`
          : value;

        return {
          name:
            parameter?.dataType === boolDataType
              ? intl.messages[BOOLEAN_VALUE_STRINGS[name]]
              : name, // Translate boolean integers to words
          id: value,
        };
      }),
    [parameter],
  );

  const getLabel = useCallback(() => {
    const paramValue = parameters?.[parameter?.identifier];

    if (parameter?.dataType === boolDataType && !isNil(paramValue)) {
      return intl.messages[BOOLEAN_VALUE_STRINGS[paramValue]];
    }

    return showItemsWithUnit
      ? `${paramValue} ${intl.formatMessage({ id: CRITERIA_PARAMETER_UNIT_LABEL[parameter?.units] }, { count: paramValue })}`
      : paramValue;
  }, [parameters, parameter, showItemsWithUnit]);

  const title = titleOverride
    ? titleOverride
    : `${intl.formatMessage({ id: CRITERIA_PARAMETER_UNIT_LABEL[parameter?.units] }, { count: null })}${required ? '*' : ''}`;

  return (
    <SelectComponent
      className={'create-criteria-modal-single-select wrapper'}
      selectedOption={parameters[identifier]}
      title={title}
      items={items}
      getLabel={`${getLabel()}${required ? '*' : ''}`}
      onChange={handleChange}
    />
  );
};

DropDownSingleSelectParam.propTypes = {
  setParameters: func.isRequired,
  parameter: shape(complianceCriteriaParameterShape),
  parameters: shape(complianceParameterValuesShape),
  showItemsWithUnit: bool,
  titleOverride: string,
};

DropDownSingleSelectParam.defaultProps = {
  parameter: null,
  parameters: null,
  showItemsWithUnit: true,
  titleOverride: '',
};

export default DropDownSingleSelectParam;
