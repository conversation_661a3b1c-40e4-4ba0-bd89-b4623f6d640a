import { CRITERIA_TEMPLATE_IDENTIFIERS } from '../../../../constants/creativeScoring.constants';
import { validators } from './CriteriaParameterValidator';

describe('FILE_SIZE_LIMIT validator', () => {
  const validator = validators.get(
    CRITERIA_TEMPLATE_IDENTIFIERS.FILE_SIZE_LIMIT,
  );
  test('should return true for valid integer file size limit', () => {
    expect(validator?.validator('maxFileSize', '1000')).toBe(true);
    expect(validator?.validator('maxFileSize', '2250')).toBe(true);
  });

  test('should fail for invalid integer file size limit', () => {
    expect(validator?.validator('maxFileSize', '-1')).toBe(false);
    expect(validator?.validator('maxFileSize', '0')).toBe(false);
    expect(validator?.validator('maxFileSize', '2251')).toBe(false);
  });

  test('should return false for floating point numbers', () => {
    expect(validator?.validator('maxFileSize', '2.25')).toBe(false);
  });

  test('should return false for restricted characters', () => {
    expect(validator?.validator('maxFileSize', '(${})')).toBe(false);
    expect(validator?.validator('maxFileSize', '&')).toBe(false);
    expect(validator?.validator('maxFileSize', '<div>')).toBe(false);
  });

  test('should return false for unrecognized parameter identifier', () => {
    expect(validator?.validator('randomParamName', '1000')).toBe(false);
  });
});
