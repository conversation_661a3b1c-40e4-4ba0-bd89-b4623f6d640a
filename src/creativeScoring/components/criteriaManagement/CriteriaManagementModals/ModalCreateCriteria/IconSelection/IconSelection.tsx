import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import SectionLabel from '../SectionLabel';
import {
  VidMobAvatar,
  VidMobButton,
  VidMobFormControl,
  VidMobStack,
} from '../../../../../../vidMobComponentWrappers';
import DeleteIcon from '@mui/icons-material/Delete';
import { IconSelectionModal } from '../../IconSelectionModal/IconSelectionModal';
import { CustomIcon } from '../../IconSelectionModal/IconSelectionModal.types';
import { SxProps } from '@mui/material';

interface IconSelectionProps {
  icon: CustomIcon | null;
  setIcon: React.Dispatch<React.SetStateAction<CustomIcon | null>>;
  formControlSx?: SxProps;
  customIconId?: string;
  infoIconSize?: 'small' | 'medium' | 'large';
}

export const IconSelection = ({
  icon,
  setIcon,
  formControlSx = {
    marginBottom: '20px',
    marginTop: '20px',
  },
  customIconId,
  infoIconSize,
}: IconSelectionProps) => {
  const intl = useIntl();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleAddOrChangeClick = () => {
    setIsDialogOpen(true);
  };

  const handleRemoveIcon = () => {
    setIcon(null);
  };

  const handleDialogSubmit = (icon: CustomIcon | null) => {
    setIcon(icon);
    setIsDialogOpen(false);
  };

  return (
    <VidMobFormControl sx={{ width: '100%', gap: '8px', ...formControlSx }}>
      <SectionLabel
        sectionTitle={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.iconSelection.title',
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.iconSelection.tooltip',
        })}
        iconSize={infoIconSize}
      />
      <VidMobStack direction="row" alignItems="center">
        {icon && (
          <VidMobAvatar
            alt="Selected Icon"
            src={icon.signedUrl}
            sx={{ width: 48, height: 48, marginRight: '8px' }}
          />
        )}
        {icon && (
          <DeleteIcon
            sx={{
              cursor: 'pointer',
              marginRight: '16px',
              width: '20px',
              height: '20px',
            }}
            onClick={handleRemoveIcon}
          />
        )}
        <VidMobButton
          variant="contained"
          color="secondary"
          onClick={handleAddOrChangeClick}
        >
          {icon
            ? intl.formatMessage({
                id: 'ui.compliance.criteriaManagement.create.modal.iconSelection.changeIcon',
                defaultMessage: 'Change icon',
              })
            : intl.formatMessage({
                id: 'ui.compliance.criteriaManagement.create.modal.iconSelection.addIcon',
                defaultMessage: 'Add icon',
              })}
        </VidMobButton>
      </VidMobStack>
      <IconSelectionModal
        originalIcon={icon}
        isOpen={isDialogOpen}
        handleClose={() => setIsDialogOpen(false)}
        handleSubmit={handleDialogSubmit}
        selectedIconsIds={customIconId ? [customIconId] : []}
      />
    </VidMobFormControl>
  );
};
