import React from 'react';
import { useIntl } from 'react-intl';
// components
import {
  VidMobTextField,
  VidMobFormControl,
} from '../../../../../../vidMobComponentWrappers';
import SectionLabel from '../SectionLabel';
import { SxProps } from '@mui/material';

interface NameFieldProps {
  name: string;
  setName: (name: string) => void;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  containerSx?: SxProps;
  iconSize?: 'small' | 'medium' | 'large';
  fieldWidth?: string;
}

const NameField: React.FC<NameFieldProps> = ({
  name,
  setName,
  error,
  helperText,
  required,
  iconSize,
  fieldWidth = '452px',
  containerSx = {},
}) => {
  const intl = useIntl();

  const textFieldStyles = {
    '& .MuiOutlinedInput-root': {
      height: '44px !important',
      fontSize: '14px',
    },
    '& .MuiFormHelperText-root': {
      marginLeft: 0,
      marginTop: '8px',
    },
  };

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const name = event.target.value;
    if (name.length === 51) {
      // restrict to 50 characters
      return;
    }

    setName(name);
  };

  return (
    <VidMobFormControl
      sx={{
        marginBottom: '20px',
        width: fieldWidth,
        gap: '8px',
        flexShrink: 0,
        ...containerSx,
      }}
    >
      <SectionLabel
        required={required}
        sectionTitle={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.name.field.description',
        })}
        tooltipText={intl.formatMessage({
          id: 'ui.compliance.criteriaManagement.create.modal.name.field.tooltip',
        })}
        iconSize={iconSize}
      />
      <VidMobTextField
        fullWidth
        variant="outlined"
        sx={textFieldStyles}
        value={name}
        onChange={(event) => handleChange(event)}
        error={error}
        helperText={helperText}
      />
    </VidMobFormControl>
  );
};

export default NameField;
