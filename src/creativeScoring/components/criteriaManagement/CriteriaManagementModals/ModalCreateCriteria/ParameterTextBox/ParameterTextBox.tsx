import React, { ChangeEvent, useState } from 'react';
import { IntlShape } from 'react-intl';
import { MessageFormatElement } from '@formatjs/icu-messageformat-parser';
import { FormControl } from '@mui/material';
import TextField from '@mui/material/TextField';
import { VidMobFormItem } from '../../../../../../vidMobComponentWrappers';
import { CriteriaParameterValidator } from '../CriteriaParameterValidator';

interface ParameterTextBoxProps {
  intl: IntlShape;
  parameter: {
    identifier: string;
    values: any;
    dataType: string;
    multiselect: boolean;
    units: string;
  };
  parameterValues: { [x: string]: string };
  setParameters: (
    arg0: (prevState: { [p: string]: string }) => { [p: string]: string },
  ) => void;
  validator: CriteriaParameterValidator | undefined;
}

const ParameterTextBox: React.FC<ParameterTextBoxProps> = ({
  intl,
  parameter,
  parameterValues,
  setParameters,
  validator,
}) => {
  const [errorMessage, setErrorMessage] = useState<
    string | MessageFormatElement[]
  >('');

  // Return early if validator is not found
  if (!validator) {
    return (
      <FormControl fullWidth>
        <div>{`Could not find validator for criteria param for ${parameter.identifier}`}</div>
      </FormControl>
    );
  }

  const getValue = () => {
    return parameterValues[parameter.identifier] ?? '';
  };

  const handleValueChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const isValid = validator.validator(parameter.identifier, value);

    setParameters((prevState: { [x: string]: string }) => {
      if (!isValid) {
        const newState = { ...prevState };
        delete newState[parameter.identifier];
        return newState;
      } else {
        return {
          ...prevState,
          [parameter?.identifier]: value,
        };
      }
    });

    if (isValid) {
      setErrorMessage('');
    } else {
      setErrorMessage(
        intl.messages[validator.errorIntlMessageKey(parameter.identifier)],
      );
    }
  };

  return (
    <FormControl fullWidth>
      <VidMobFormItem
        itemLabel={
          intl.messages[
            `ui.compliance.criteriaManagement.modal.criteria.parameter.${parameter.identifier}.label`
          ]
        }
        helperText={
          intl.messages[
            `ui.compliance.criteriaManagement.modal.criteria.custom.textarea.${parameter.identifier}.formhelperText`
          ]
        }
        error={errorMessage}
        padding={0}
      >
        <TextField
          className="parameter-value-textarea"
          variant="outlined"
          fullWidth
          value={getValue()}
          onChange={handleValueChange}
        />
      </VidMobFormItem>
    </FormControl>
  );
};

export default ParameterTextBox;
