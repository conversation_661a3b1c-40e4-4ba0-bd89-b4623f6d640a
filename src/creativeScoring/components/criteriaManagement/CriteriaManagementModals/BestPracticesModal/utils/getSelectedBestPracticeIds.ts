import { GridRowId, GridValidRowModel } from '@mui/x-data-grid-pro';
import { MutableRefObject } from 'react';
import { GridApiPro } from '@mui/x-data-grid-pro/models/gridApiPro';

export const getSelectedBestPracticeIds = (
  apiRef: MutableRefObject<GridApiPro>,
): number[] => {
  const getSelectedRowsFunction = apiRef.current?.getSelectedRows;
  if (!getSelectedRowsFunction) {
    return [];
  }

  const selectedRows: Map<GridRowId, GridValidRowModel> =
    getSelectedRowsFunction();
  const selectedBestPracticeIds: number[] = [];
  selectedRows.forEach((row, id) => {
    if (row.isChild) {
      selectedBestPracticeIds.push(Number(id));
    }
  });

  return selectedBestPracticeIds;
};
