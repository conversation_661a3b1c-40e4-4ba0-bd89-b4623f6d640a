import { BestPracticeDetailsV2 } from '../../../../../types/criteriaManagement.types';

export const getAvailableBestPracticeIds = (
  bestPractices: BestPracticeDetailsV2[],
): string[] =>
  bestPractices.reduce(
    (availableBestPracticeIds: string[], bestPractice) =>
      bestPractice.isAdded
        ? [...availableBestPracticeIds]
        : [
            ...availableBestPracticeIds,
            bestPractice.bestPracticeDefinitionId.toString(),
          ],
    [],
  );
