import {
  BestPracticesDataGridRow,
  CurrentBestPracticesData,
  OutdatedBestPracticesData,
} from '../../../../../types/criteriaManagement.types';
import { getAvailableBestPracticeIds } from './getAvailableBestPracticeIds';

export const createCurrentBestPracticesRows = (
  data?: CurrentBestPracticesData,
): BestPracticesDataGridRow[] => {
  if (!data) {
    return [];
  }

  const rows: BestPracticesDataGridRow[] = [];
  Object.entries(data).forEach(([platform, platformData]) => {
    const parentId = `current-${platform}`;
    const {
      bestPracticeDetails,
      addedBestPractices,
      totalBestPractices,
      hasAllBestPractices,
      remainingBestPractices,
    } = platformData;

    const availableChildIds = getAvailableBestPracticeIds(bestPracticeDetails);

    rows.push({
      id: parentId,
      isParent: true,
      hierarchy: [parentId],
      displayName: platform,
      addedBestPractices,
      totalBestPractices,
      availableChildIds,
      isAdded: hasAllBestPractices,
      isPartiallyAdded: addedBestPractices > 0 && remainingBestPractices > 0,
    });

    bestPracticeDetails.forEach((detail) => {
      const {
        bestPracticeDefinitionId,
        name,
        rule,
        isAdded,
        validFromQuarter,
        validToQuarter,
      } = detail;
      const childId = bestPracticeDefinitionId.toString();
      rows.push({
        id: childId,
        isChild: true,
        parentId,
        availableChildIds,
        hierarchy: [parentId, childId],
        displayName: name,
        rule,
        isAdded: Boolean(isAdded),
        validFromQuarter,
        validToQuarter,
      });
    });
  });

  return rows;
};

export const createOutdatedBestPracticesRows = (
  data?: OutdatedBestPracticesData,
): BestPracticesDataGridRow[] => {
  if (!data) {
    return [];
  }

  const rows: BestPracticesDataGridRow[] = [];

  Object.entries(data).forEach(([platform, platformOutdatedBestPractices]) => {
    const childIds = platformOutdatedBestPractices.map((bestPractice) =>
      bestPractice.bestPracticeDefinitionId.toString(),
    );
    const parentId = `outdated-${platform}`;
    rows.push({
      id: parentId,
      isParent: true,
      hierarchy: [parentId],
      displayName: platform,
      availableChildIds: childIds,
    });

    platformOutdatedBestPractices.forEach((bestPractice) => {
      const {
        bestPracticeDefinitionId,
        name,
        rule,
        validFromQuarter,
        validToQuarter,
      } = bestPractice;
      const childId = bestPracticeDefinitionId.toString();
      rows.push({
        id: childId,
        isChild: true,
        parentId,
        availableChildIds: childIds,
        hierarchy: [parentId, childId],
        displayName: name,
        rule,
        validFromQuarter,
        validToQuarter,
      });
    });
  });

  return rows;
};
