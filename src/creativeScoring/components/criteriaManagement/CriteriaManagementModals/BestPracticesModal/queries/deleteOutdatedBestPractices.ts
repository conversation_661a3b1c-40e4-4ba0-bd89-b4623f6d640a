import BffBestPracticesService from '../../../../../../apiServices/BffBestPracticesService';
import vmErrorLog from '../../../../../../utils/vmErrorLog';

const REMOVE_BEST_PRACTICES_SUCCESS_MESSAGE_KEY =
  'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.previous.removeBestPractices.toast.success';
const REMOVE_BEST_PRACTICES_ERROR_MESSAGE_KEY =
  'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.previous.removeBestPractices.toast.error';

interface Params {
  workspaceId: number;
  bestPracticeDefinitionIds: number[];
  showToastAlert: (messageKey: string, type: 'info' | 'error') => void;
  refetch: () => void;
}

export const deleteOutdatedBestPractices = ({
  workspaceId,
  bestPracticeDefinitionIds,
  showToastAlert,
  refetch,
}: Params) =>
  BffBestPracticesService.deleteOutdatedBestPractices(
    workspaceId,
    bestPracticeDefinitionIds,
  )
    .then(() => {
      showToastAlert(REMOVE_BEST_PRACTICES_SUCCESS_MESSAGE_KEY, 'info');
      refetch();
    })
    .catch((error) => {
      showToastAlert(REMOVE_BEST_PRACTICES_ERROR_MESSAGE_KEY, 'error');
      vmErrorLog(
        error as Error,
        'BestPracticesModal',
        `Something went wrong removing outdated Best Practices ${bestPracticeDefinitionIds.join(', ')}.`,
      );
    });
