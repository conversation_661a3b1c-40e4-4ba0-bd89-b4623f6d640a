import vmErrorLog from '../../../../../../utils/vmErrorLog';
import BffBestPracticesService from '../../../../../../apiServices/BffBestPracticesService';

const ADD_BEST_PRACTICES_SUCCESS_MESSAGE_KEY =
  'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.addBestPractices.toast.success';
const ADD_BEST_PRACTICES_ERROR_MESSAGE_KEY =
  'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.addBestPractices.toast.error';

interface Params {
  userId: number;
  workspaceId: number;
  bestPracticeDefinitionIds: number[];
  showToastAlert: (messageKey: string, type: 'info' | 'error') => void;
  refetch: () => void;
}

export const createBestPractices = ({
  userId,
  workspaceId,
  bestPracticeDefinitionIds,
  showToastAlert,
  refetch,
}: Params) =>
  BffBestPracticesService.createBestPractices(
    userId,
    workspaceId,
    bestPracticeDefinitionIds,
  )
    .then(() => {
      showToastAlert(ADD_BEST_PRACTICES_SUCCESS_MESSAGE_KEY, 'info');
      refetch();
    })
    .catch((error) => {
      showToastAlert(ADD_BEST_PRACTICES_ERROR_MESSAGE_KEY, 'error');
      vmErrorLog(
        error as Error,
        'BestPracticesModal',
        `Something went wrong adding current Best Practices ${bestPracticeDefinitionIds.join(', ')}.`,
      );
    });
