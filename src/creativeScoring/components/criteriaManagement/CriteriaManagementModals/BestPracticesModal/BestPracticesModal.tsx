import React, { FC, useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import CustomDialog from '../../../../../muiCustomComponents/CustomDialog';
import CustomTabs from '../../../../../muiCustomComponents/CustomTabs/CustomTabs';
import { useGetOutdatedBestPractices } from '../../../../query-hooks/useGetOutdatedBestPractices';
import { useSelector } from 'react-redux';
import { getCurrentPartnerId } from '../../../../../redux/selectors/partner.selectors';
import { useGetCurrentBestPractices } from '../../../../query-hooks/useGetCurrentBestPractices';
import { CurrentChannelBestPractices } from '../../../../types/criteriaManagement.types';
import { BestPracticesModalDataGrid } from './BestPracticesModalDataGrid/BestPracticesModalDataGrid';
import {
  createCurrentBestPracticesRows,
  createOutdatedBestPracticesRows,
} from './utils/createRows';
import { useGridApiRef } from '@mui/x-data-grid-pro';
import { getSelectedBestPracticeIds } from './utils/getSelectedBestPracticeIds';
import { getCurrentUserId } from '../../../../../redux/selectors/user.selectors';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { createBestPractices } from './queries/createBestPractices';
import { deleteOutdatedBestPractices } from './queries/deleteOutdatedBestPractices';
import { BestPracticesModalFooter } from './BestPracticesModalFooter';
import { trackFeatureUsageGainsight } from '../../../../../utils/gainsight';
import { useDispatch } from 'react-redux';
import criteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';

const { loadCriteria } = criteriaManagementSlice.actions;

const tabsHeaderSx = {
  p: '0 9px',
};

const panelSx = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '551px',
  width: '100%',
  p: '16px 0 0 0',
};

const modalContentSx = {
  p: 0,
};

interface ComponentProps {
  isOpen: boolean;
  setCurrentModal: (modal: string | null) => void;
}

const BestPracticesModal: FC<ComponentProps> = ({
  isOpen,
  setCurrentModal,
}) => {
  const intl = useIntl();
  const currentBestPracticesTableApiRef = useGridApiRef();
  const outdatedBestPracticesTableApiRef = useGridApiRef();

  const showToastAlert = useToastAlert();
  const userId = useSelector(getCurrentUserId);
  const workspaceId = useSelector(getCurrentPartnerId);
  const dispatch = useDispatch();

  const [selectedTab, setSelectedTab] = useState(0);

  const isCurrentPracticesTab = selectedTab === 0;

  useEffect(() => {
    if (isOpen) {
      trackFeatureUsageGainsight('Best Practice Versioning', {
        Context: 'Best Practice Versioning Modal Opened',
      });
    }
  }, [isOpen]);

  const {
    isLoading: areCurrentBestPracticesLoading,
    data: currentBestPractices,
    isError: isCurrentBestPracticesError,
    refetch: refetchCurrentBestPractices,
  } = useGetCurrentBestPractices({
    workspaceId,
  });

  const {
    isLoading: areOutdatedBestPracticesLoading,
    data: outdatedBestPractices,
    isError: isOutdatedBestPracticesError,
    refetch: refetchOutdatedBestPractices,
  } = useGetOutdatedBestPractices({
    workspaceId,
    enabled: isOpen,
  });

  const currentBestPracticesCount = Object.values(
    currentBestPractices || {},
  ).reduce(
    (total, bestPracticesForPlatform: CurrentChannelBestPractices) =>
      total + bestPracticesForPlatform.totalBestPractices,
    0,
  );

  const outdatedBestPracticesCount = Object.values(
    outdatedBestPractices || {},
  ).flat().length;

  const handleClose = () => {
    setCurrentModal(null);
  };

  const handleSubmit = async () => {
    if (isCurrentPracticesTab) {
      const selectedCurrentBestPracticeIds = getSelectedBestPracticeIds(
        currentBestPracticesTableApiRef,
      );

      if (selectedCurrentBestPracticeIds.length === 0) {
        return;
      }

      await createBestPractices({
        userId,
        workspaceId,
        bestPracticeDefinitionIds: selectedCurrentBestPracticeIds,
        showToastAlert,
        refetch: refetchCurrentBestPractices,
      });
      dispatch(loadCriteria({}));
    } else {
      const selectedOutdatedBestPracticeIds = getSelectedBestPracticeIds(
        outdatedBestPracticesTableApiRef,
      );

      if (selectedOutdatedBestPracticeIds.length === 0) {
        return;
      }

      await deleteOutdatedBestPractices({
        workspaceId,
        bestPracticeDefinitionIds: selectedOutdatedBestPracticeIds,
        showToastAlert,
        refetch: refetchOutdatedBestPractices,
      });

      dispatch(loadCriteria({}));
    }
    trackFeatureUsageGainsight('Best Practice Versioning', {
      Context: 'Best Practice Versioning Submitted',
    });
  };

  const currentBestPracticesRows =
    createCurrentBestPracticesRows(currentBestPractices);

  const outdatedBestPracticesRows = createOutdatedBestPracticesRows(
    outdatedBestPractices,
  );

  const tabs = [
    {
      label: intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current',
        defaultMessage: 'Current best practices',
      }),
      itemsCount: currentBestPracticesCount,
      content: (
        <BestPracticesModalDataGrid
          bestPracticeType="current"
          rows={currentBestPracticesRows}
          apiRef={currentBestPracticesTableApiRef}
          isLoading={areCurrentBestPracticesLoading}
          isError={isCurrentBestPracticesError}
          noDataTitle={intl.formatMessage({
            id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.noData.title',
            defaultMessage: 'No current best practices in this workspace',
          })}
          noDataMessage={intl.formatMessage({
            id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.noData.description',
            defaultMessage:
              'There are no current best practices available for this workspace.',
          })}
          errorMessage={intl.formatMessage({
            id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.error.description',
            defaultMessage:
              'There was an error loading current best practices. Please refresh the page and try again.',
          })}
        />
      ),
    },
    {
      label: intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.previous',
        defaultMessage: 'Previous versions',
      }),
      itemsCount: outdatedBestPracticesCount,
      content: (
        <BestPracticesModalDataGrid
          bestPracticeType="previous"
          rows={outdatedBestPracticesRows}
          apiRef={outdatedBestPracticesTableApiRef}
          isLoading={areOutdatedBestPracticesLoading}
          isError={isOutdatedBestPracticesError}
          noDataTitle={intl.formatMessage({
            id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.previous.noData.title',
            defaultMessage: 'No previous versions in this workspace',
          })}
          noDataMessage={intl.formatMessage({
            id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.previous.noData.description',
            defaultMessage:
              'There are no outdated best practices that have been added to this workspace.',
          })}
          errorMessage={intl.formatMessage({
            id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.previous.error.description',
            defaultMessage:
              'There was an error loading previous best practices. Please refresh the page and try again.',
          })}
        />
      ),
    },
  ];

  const closeButtonLabel = intl.formatMessage({
    id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.addByChannel.button.close',
    defaultMessage: 'Close',
  });

  const submitButtonLabel = isCurrentPracticesTab
    ? intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.addByChannel.button.addSelected',
        defaultMessage: 'Add selected',
      })
    : intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.addByChannel.button.removeSelected',
        defaultMessage: 'Remove selected',
      });

  return (
    <CustomDialog
      isOpen={isOpen}
      headerText={
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.header'
        ]
      }
      headerSubText={
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.subtext'
        ]
      }
      bodyChildren={
        <CustomTabs
          tabs={tabs}
          headerSx={tabsHeaderSx}
          panelSx={panelSx}
          initialSelectedTab={selectedTab}
          onTabChange={setSelectedTab}
        />
      }
      customFooterChildren={
        <BestPracticesModalFooter
          onClose={handleClose}
          onSubmit={handleSubmit}
          closeButtonLabel={closeButtonLabel}
          submitButtonLabel={submitButtonLabel}
        />
      }
      explicitDialogWidth="900px"
      explicitDialogHeight="800px"
      customContentStyles={modalContentSx}
      renderHeaderDivider={false}
    />
  );
};

export default BestPracticesModal;
