import React from 'react';
import {
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridCellParams,
  GridColDef,
} from '@mui/x-data-grid-pro';
import { BestPracticesDefaultCell } from './components/BestPracticesDefaultCell';
import { getIntl } from '../../../../../../utils/getIntl';

const intl = getIntl();
export const BEST_PRACTICES_MODAL_DATA_GRID_ROW_HEIGHT = 52;
export const FIELD_RULE = 'rule';
export const FIELD_BEST_PRACTICE_SINCE = 'validFromQuarter';
export const FIELD_OUTDATED_SINCE = 'validToQuarter';
export const CHECKBOX_COLUMN = {
  ...GRID_CHECKBOX_SELECTION_COL_DEF,
  width: 80,
};

export const CRITERIA_GROUPING_COLUMN = {
  headerName: intl.formatMessage({
    id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.columnGroup.header.criteria',
    defaultMessage: 'Criteria',
  }),
  hideDescendantCount: true,
  width: 320,
  minWidth: 100,
  maxWidth: 800,
};

const RULE_COLUMN = {
  field: FIELD_RULE,
  headerName: intl.formatMessage({
    id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.column.header.rule',
    defaultMessage: 'Rule',
  }),
  width: 500,
  minWidth: 100,
  maxWidth: 200,
  resizable: true,
  sortable: false,
  renderCell: BestPracticesDefaultCell,
};

const BEST_PRACTICE_SINCE_HEADER_NAME = intl.formatMessage({
  id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.column.header.bestPracticeSince',
  defaultMessage: 'Best Practice Since',
});

const BEST_PRACTICE_SINCE_COLUMN = {
  field: FIELD_BEST_PRACTICE_SINCE,
  headerName: BEST_PRACTICE_SINCE_HEADER_NAME,
  minWidth: 200,
  maxWidth: 300,
  resizable: true,
  sortable: false,
  renderCell: (params: GridCellParams) => {
    if (!params.value) return null;
    return (
      <BestPracticesDefaultCell
        {...params}
        value={`${BEST_PRACTICE_SINCE_HEADER_NAME} ${params.value}`}
      />
    );
  },
};

const OUTDATED_SINCE_HEADER_NAME = intl.formatMessage({
  id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.column.header.outdatedSince',
  defaultMessage: 'Outdated Since',
});

const OUTDATED_SINCE_COLUMN = {
  field: FIELD_OUTDATED_SINCE,
  headerName: OUTDATED_SINCE_HEADER_NAME,
  minWidth: 200,
  maxWidth: 300,
  resizable: true,
  sortable: false,
  renderCell: (params: GridCellParams) => {
    if (!params.value) return null;
    return (
      <BestPracticesDefaultCell
        {...params}
        value={`${OUTDATED_SINCE_HEADER_NAME} ${params.value}`}
      />
    );
  },
};

export const BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS: GridColDef[] = [
  RULE_COLUMN,
];

export const BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS_CURRENT: GridColDef[] = [
  BEST_PRACTICE_SINCE_COLUMN,
];

export const BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS_PREVIOUS: GridColDef[] = [
  OUTDATED_SINCE_COLUMN,
];
