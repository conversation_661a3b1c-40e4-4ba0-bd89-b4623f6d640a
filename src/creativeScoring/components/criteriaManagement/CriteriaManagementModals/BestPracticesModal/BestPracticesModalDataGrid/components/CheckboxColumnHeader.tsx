import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobCheckbox,
  VidMobTooltip,
} from '../../../../../../../vidMobComponentWrappers';
import { Box } from '@mui/material';
import { BestPracticesDataGridRow } from '../../../../../../types/criteriaManagement.types';
import { useGridApiContext } from '@mui/x-data-grid-pro';
import { useBroadcastChannel } from '../../../../../../../hooks/useBroadcastChannel';
import { BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL } from '../../constants';

interface Props {
  rows: BestPracticesDataGridRow[];
}

export const CheckboxColumnHeader = ({ rows }: Props) => {
  const apiRef = useGridApiContext();
  const intl = useIntl();
  const { postMessage } = useBroadcastChannel<number>( // // only used in order to not interfere with BestPracticeModal state while preserving row expansion state
    BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL,
  );
  const nonAddedRowIds = rows.reduce(
    (allAvailableChildIds: string[], row) =>
      row.isAdded ? allAvailableChildIds : [...allAvailableChildIds, row.id],
    [],
  );

  const allAdded = rows.every((row) => row.isAdded);
  const someAdded = !allAdded && rows.some((row) => row.isAdded);

  const selectedRowCount = apiRef.current.getSelectedRows().size;
  const noneSelected = selectedRowCount === 0;
  const allSelected = selectedRowCount === nonAddedRowIds.length;
  const partiallySelected = !allSelected && selectedRowCount > 0;

  const checked = allAdded || allSelected;
  const indeterminate = !checked && (someAdded || partiallySelected);

  const onChange = () => {
    apiRef.current.selectRows(
      nonAddedRowIds,
      noneSelected || partiallySelected,
    );
    const selectedRows = apiRef.current.getSelectedRows();
    postMessage(selectedRows.size);
  };

  return (
    <VidMobTooltip
      title={
        allAdded
          ? intl.formatMessage({
              id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.tooltip.addedAllBestPractices',
              defaultMessage: 'All Best Practices have been added',
            })
          : ''
      }
      placement="top"
      disableInteractive
    >
      <Box>
        <VidMobCheckbox
          disabled={allAdded}
          checked={checked}
          indeterminate={indeterminate}
          onChange={onChange}
        />
      </Box>
    </VidMobTooltip>
  );
};
