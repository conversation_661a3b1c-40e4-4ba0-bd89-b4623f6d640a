import React, { useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobCheckbox,
  VidMobTooltip,
} from '../../../../../../../vidMobComponentWrappers';
import { Box } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid/models/params/gridCellParams';
import { useGridApiContext } from '@mui/x-data-grid-pro';
import { useBroadcastChannel } from '../../../../../../../hooks/useBroadcastChannel';
import { BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL } from '../../constants';

export const CheckboxColumnParentCell = ({ row }: GridRenderCellParams) => {
  const apiRef = useGridApiContext();
  const intl = useIntl();
  const { postMessage } = useBroadcastChannel<number>( // only used in order to not interfere with BestPracticeModal state while preserving row expansion state
    BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL,
  );
  const { id, isAdded, isPartiallyAdded, availableChildIds } = row;

  const [_, rerender] = useState(false); // we want to force a rerender when the child selection changes

  useEffect(() => {
    const handleSelectionChange = () => {
      rerender((prev) => !prev);
    };

    const unsubscribe = apiRef.current.subscribeEvent(
      'rowSelectionChange',
      handleSelectionChange,
    );

    return () => unsubscribe();
  }, [apiRef]);

  const isSelected =
    apiRef.current.isRowSelected(id) ||
    availableChildIds.every((childId: string) =>
      apiRef.current.isRowSelected(childId),
    );

  const isPartiallySelected =
    !isSelected &&
    availableChildIds.some((childId: string) =>
      apiRef.current.isRowSelected(childId),
    );

  const onChange = () => {
    apiRef.current.selectRows([id, ...availableChildIds], !isSelected);
    const selectedRows = apiRef.current.getSelectedRows();
    postMessage(selectedRows.size);
  };

  const checked = isAdded || isSelected;
  const indeterminate = !checked && (isPartiallyAdded || isPartiallySelected);

  return (
    <VidMobTooltip
      title={
        isAdded
          ? intl.formatMessage({
              id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.tooltip.addedBestPracticesForPlatform',
              defaultMessage: 'All Channel Best Practices have been added',
            })
          : ''
      }
      placement="top"
      disableInteractive
    >
      <Box>
        <VidMobCheckbox
          disabled={isAdded}
          checked={checked}
          indeterminate={indeterminate}
          onChange={onChange}
        />
      </Box>
    </VidMobTooltip>
  );
};
