import React from 'react';
import { VidMobTooltip } from '../../../../../../../vidMobComponentWrappers';
import { Typography } from '@mui/material';
import { GridCellParams } from '@mui/x-data-grid';

const textSx = {
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
};

export const BestPracticesDefaultCell = ({ value }: GridCellParams) => {
  if (typeof value === 'number') {
    return (
      <VidMobTooltip title={value} placement="top">
        <Typography variant="body2" sx={textSx}>
          {value.toLocaleString()}
        </Typography>
      </VidMobTooltip>
    );
  }

  if (typeof value === 'string') {
    return (
      <VidMobTooltip title={value} placement="top">
        <Typography variant="body2" sx={textSx}>
          {value}
        </Typography>
      </VidMobTooltip>
    );
  }

  return null;
};
