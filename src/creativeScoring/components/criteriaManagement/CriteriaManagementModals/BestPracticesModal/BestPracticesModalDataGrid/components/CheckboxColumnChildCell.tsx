import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobCheckbox,
  VidMobTooltip,
} from '../../../../../../../vidMobComponentWrappers';
import { Box } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid/models/params/gridCellParams';
import { useGridApiContext } from '@mui/x-data-grid-pro';
import { useBroadcastChannel } from '../../../../../../../hooks/useBroadcastChannel';
import { BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL } from '../../constants';

const checkboxSx = {
  ml: '42px',
};

export const CheckboxColumnChildCell = ({ row }: GridRenderCellParams) => {
  const apiRef = useGridApiContext();
  const intl = useIntl();
  const { postMessage } = useBroadcastChannel<number>( // only used in order to not interfere with BestPracticeModal state while preserving row expansion state
    BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL,
  );
  const { id, isAdded, parentId, availableChildIds } = row;

  const isSelected = apiRef.current.isRowSelected(id);
  const allSiblingsSelected = availableChildIds.every(
    (childId: string) =>
      childId === id || apiRef.current.isRowSelected(childId),
  );

  const checked = isAdded || isSelected;

  const onChange = () => {
    if (isSelected) {
      apiRef.current.selectRows([id, parentId], false);
    } else {
      apiRef.current.selectRow(id, true);
      if (allSiblingsSelected) {
        apiRef.current.selectRow(parentId, true);
      }
    }

    const selectedRows = apiRef.current.getSelectedRows();
    postMessage(selectedRows.size);
  };

  return (
    <VidMobTooltip
      title={
        isAdded
          ? intl.formatMessage({
              id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.tabs.current.tooltip.addedBestPractice',
              defaultMessage: 'Channel Best Practice has been added',
            })
          : ''
      }
      placement="top"
      disableInteractive
    >
      <Box>
        <VidMobCheckbox
          sx={checkboxSx}
          disabled={isAdded}
          checked={checked}
          onChange={onChange}
        />
      </Box>
    </VidMobTooltip>
  );
};
