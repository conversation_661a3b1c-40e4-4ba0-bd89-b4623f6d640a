import React from 'react';
import { useIntl } from 'react-intl';
import { GridRenderCellParams } from '@mui/x-data-grid-pro';
import getMUIIconForChannel from '../../../../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { isInvalidNumber } from '../../../../../../../utils/isInvalidNumber';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import { PLATFORM } from '../../../../../../../constants';

const channelIconSx = {
  height: '24px',
  width: '24px',
};

export const BestPracticesParentCell = ({ row }: GridRenderCellParams) => {
  const intl = useIntl();
  const { addedBestPractices, totalBestPractices, displayName } = row;
  const channelIcon = getMUIIconForChannel(displayName, channelIconSx, true);
  const channelName = intl.formatMessage({
    id: PLATFORM.PLATFORM_IDENTIFIERS_TO_LOCALES[displayName],
    defaultMessage: displayName,
  });
  const shouldAddCount =
    !isInvalidNumber(addedBestPractices) &&
    !isInvalidNumber(totalBestPractices);
  return (
    <VidMobStack
      direction="row"
      gap="8px"
      justifyContent="center"
      alignItems="center"
    >
      {channelIcon}
      <VidMobStack direction="column">
        <VidMobTypography variant="body2">{channelName}</VidMobTypography>
        {shouldAddCount && (
          <VidMobTypography variant="caption" color="text.secondary">
            {intl.formatMessage(
              {
                id: 'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.viewByChannel.groupCell.subtext',
              },
              { addedBestPractices, totalBestPractices },
            )}
          </VidMobTypography>
        )}
      </VidMobStack>
    </VidMobStack>
  );
};
