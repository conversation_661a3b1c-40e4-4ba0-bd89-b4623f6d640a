import React, { MutableRefObject } from 'react';
import { BestPracticesDataGridRow } from '../../../../../types/criteriaManagement.types';
import { VidMobCircularProgress } from '../../../../../../vidMobComponentWrappers';
import { BlankOrErrorState } from '../../../../../../muiCustomComponents/BlankState/BlankState';
import {
  BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS,
  BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS_CURRENT,
  BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS_PREVIOUS,
  BEST_PRACTICES_MODAL_DATA_GRID_ROW_HEIGHT,
  CHECKBOX_COLUMN,
  CRITERIA_GROUPING_COLUMN,
} from './BestPracticesModalDataGridConstants';
import { BestPracticesParentCell } from './components/BestPracticesParentCell';
import NestedRowsTable from '../../../../../../muiCustomComponents/NestedRowsTable/NestedRowsTable';
import { GridRenderCellParams } from '@mui/x-data-grid-pro';
import { CheckboxColumnParentCell } from './components/CheckboxColumnParentCell';
import { CheckboxColumnHeader } from './components/CheckboxColumnHeader';
import { CheckboxColumnChildCell } from './components/CheckboxColumnChildCell';
import { GridApiPro } from '@mui/x-data-grid-pro/models/gridApiPro';

const customBlankStateSx = {
  width: '330px',
};

const tableSx = {
  border: 'none',
  '&.MuiDataGrid-root': {
    '.MuiDataGrid-cell:focus-within, .MuiDataGrid-columnHeader:focus-within': {
      outline: 'none',
    },
  },
};

interface Props {
  rows?: BestPracticesDataGridRow[];
  apiRef: MutableRefObject<GridApiPro>;
  isLoading: boolean;
  isError: boolean;
  noDataTitle: string;
  noDataMessage: string;
  errorMessage: string;
  bestPracticeType: 'current' | 'previous';
}

export const BestPracticesModalDataGrid = ({
  bestPracticeType,
  rows,
  apiRef,
  isLoading,
  isError,
  noDataTitle,
  noDataMessage,
  errorMessage,
}: Props) => {
  if (isError) {
    return (
      <BlankOrErrorState
        stateType="error"
        message={errorMessage}
        messageStyle={customBlankStateSx}
        additionalStackStyles={customBlankStateSx}
      />
    );
  }

  if (isLoading) {
    return <VidMobCircularProgress size="32px" />;
  }

  if (!rows?.length) {
    return (
      <BlankOrErrorState
        stateType="error"
        title={noDataTitle}
        message={noDataMessage}
        messageStyle={customBlankStateSx}
        additionalStackStyles={customBlankStateSx}
      />
    );
  }

  const columns = [
    {
      ...CHECKBOX_COLUMN,
      renderHeader: () => <CheckboxColumnHeader rows={rows} />,
      renderCell: (params: GridRenderCellParams) =>
        params.row.isParent ? (
          <CheckboxColumnParentCell {...params} />
        ) : (
          <CheckboxColumnChildCell {...params} />
        ),
    },
    ...BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS,
    ...(bestPracticeType === 'previous'
      ? BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS_PREVIOUS
      : BEST_PRACTICES_MODAL_DATA_GRID_COLUMNS_CURRENT),
  ];

  return (
    <NestedRowsTable
      apiRef={apiRef}
      columns={columns}
      groupingColumn={CRITERIA_GROUPING_COLUMN}
      rows={rows}
      rowHeight={BEST_PRACTICES_MODAL_DATA_GRID_ROW_HEIGHT}
      disableColumnMenu
      customSx={tableSx}
      marginX={24}
      marginY={0}
      CustomParentCell={BestPracticesParentCell}
      checkboxSelection
    />
  );
};
