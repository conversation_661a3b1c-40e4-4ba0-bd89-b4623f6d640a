import React from 'react';
import { useBroadcastChannel } from '../../../../../hooks/useBroadcastChannel';
import { BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL } from './constants';
import {
  VidMobBox,
  VidMobButton,
} from '../../../../../vidMobComponentWrappers';
import { defaultFooterStyle } from '../../../../../muiCustomComponents/CustomDialog/CustomDialog';

const buttonSx = {
  fontWeight: '600',
};

interface Props {
  onClose: () => void;
  onSubmit: () => void;
  closeButtonLabel: string;
  submitButtonLabel: string;
}

export const BestPracticesModalFooter = ({
  onClose,
  onSubmit,
  closeButtonLabel,
  submitButtonLabel,
}: Props) => {
  const { message: selectedRowCount, closeChannel } =
    useBroadcastChannel<number>( // only used in order to not interfere with BestPracticeModal state while preserving row expansion state
      BEST_PRACTICES_MODAL_ROW_SELECTION_BROADCAST_CHANNEL,
    );

  const handleClose = () => {
    onClose();
    closeChannel();
  };

  return (
    <VidMobBox sx={defaultFooterStyle}>
      <VidMobButton
        variant="contained"
        color="inherit"
        sx={buttonSx}
        onClick={handleClose}
      >
        {closeButtonLabel}
      </VidMobButton>
      <VidMobButton
        variant="contained"
        color="primary"
        sx={buttonSx}
        disabled={!selectedRowCount}
        onClick={onSubmit}
      >
        {submitButtonLabel}
      </VidMobButton>
    </VidMobBox>
  );
};
