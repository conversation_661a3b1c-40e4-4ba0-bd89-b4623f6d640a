// @ts-nocheck
import React from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
// Components
import { Button, Stack, Typography } from '@mui/material';
import {
  InfoFilledIcon,
  CloseIcon,
} from '../../../../../assets/vidmob-mui-icons/general';
// constants
import {
  CRITERIA_MANAGEMENT_MODAL_TYPES,
  CRITERIA_MANAGEMENT_USERS,
} from '../../../../constants/criteriaManagement.constants';
// redux
import { getCanUserUpdateCriteria } from '../../../../redux/selectors/criteriaManagement.selectors';

const { ADMIN, STANDARD } = CRITERIA_MANAGEMENT_USERS;

interface ComponentProps {
  setIsOpen: (isOpen: boolean) => void;
  setCurrentModal: (modal: string | null) => void;
}
const BrandIdentifierBanner: React.FC<ComponentProps> = ({
  setCurrentModal,
  setIsOpen,
}) => {
  const intl = useIntl();
  const canUserUpdateCriteria = useSelector(getCanUserUpdateCriteria);

  const renderText = () => {
    const permissionsKey = canUserUpdateCriteria ? ADMIN : STANDARD;
    return (
      <Typography variant="subtitle2">
        {
          intl.messages[
            `ui.creativeScoring.criteriaManagementV2.banner.brandIdentifier.text.${permissionsKey}`
          ]
        }
      </Typography>
    );
  };

  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      sx={{
        height: '56px',
        borderRadius: '6px',
        backgroundColor: '#FEEBEE',
        p: '12px',
        mb: '24px',
      }}
    >
      <Stack sx={{ gap: '12px' }} direction="row" alignItems="center">
        <InfoFilledIcon />
        {renderText()}
      </Stack>
      <Stack sx={{ gap: '12px' }} direction="row" alignItems="center">
        {canUserUpdateCriteria && (
          <Button
            variant="contained"
            color="inherit"
            size="small"
            onClick={() =>
              setCurrentModal(
                CRITERIA_MANAGEMENT_MODAL_TYPES.SET_BRAND_IDENTIFIER,
              )
            }
          >
            Set brand identifier
          </Button>
        )}
        <Button
          sx={{ minWidth: 0, width: '36px', height: '36px', p: 0 }}
          onClick={() => setIsOpen(false)}
        >
          <CloseIcon sx={{ color: '#212121', height: '14px', width: '14px' }} />
        </Button>
      </Stack>
    </Stack>
  );
};

export default BrandIdentifierBanner;
