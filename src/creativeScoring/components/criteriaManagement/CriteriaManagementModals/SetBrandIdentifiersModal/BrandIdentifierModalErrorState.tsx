import React from 'react';
import { useIntl } from 'react-intl';
import GenericBlankState from '../../../../../muiCustomComponents/GenericBlankState';
import { InfoFilledIcon } from '../../../../../assets/vidmob-mui-icons/general';

const BrandIdentifierModalErrorState: React.FC = () => {
  const intl = useIntl();
  return (
    <GenericBlankState
      title={
        intl.messages['ui.creativeScoring.criteriaManagementV2.error.title']
      }
      subtitle={
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.brandIdentifiers.error.body'
        ]
      }
      icon={<InfoFilledIcon />}
    />
  );
};

export default BrandIdentifierModalErrorState;
