// @ts-nocheck
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
// components
import CustomDialog from '../../../../../muiCustomComponents/CustomDialog';
import {
  Button,
  Stack,
  FormControl,
  Typography,
  TextField,
} from '@mui/material';
import BrandIdentifierModalErrorState from './BrandIdentifierModalErrorState';
// redux
import CriteriaManagementSlice from '../../../../redux/slices/criteriaManagement.slice';
import { getHasBrandIdentifiersFailed } from '../../../../redux/selectors/criteriaManagement.selectors';

const { createBrandIdentifiers } = CriteriaManagementSlice.actions;

interface ComponentProps {
  brandIdentifiers: string | '';
  isOpen: boolean;
  setCurrentModal: (modal: string | null) => void;
}

const BrandIdentifierModal: React.FC<ComponentProps> = ({
  brandIdentifiers: brandIds,
  isOpen,
  setCurrentModal,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const hasBrandIdentifiersFailed = useSelector(getHasBrandIdentifiersFailed);

  const [brandIdentifiers, setBrandIdentifiers] = React.useState<string | ''>(
    brandIds,
  );
  const [isSubmitButtonEnabled, setIsSubmitButtonEnabled] =
    React.useState<boolean>(false);

  useEffect(() => {
    setBrandIdentifiers(brandIds);
  }, [brandIds, isOpen]);

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const updatedBrandIdentifiers = event.target.value || '';
    setIsSubmitButtonEnabled(true);
    setBrandIdentifiers(updatedBrandIdentifiers);
  };

  const handleClose = () => {
    setCurrentModal(null);
    setIsSubmitButtonEnabled(false);
  };

  const handleSubmit = () => {
    dispatch(createBrandIdentifiers({ brandIdentifiers }));
    handleClose();
  };

  const renderModalBody = () => {
    const textFieldStyles = {
      '& .MuiOutlinedInput-root': {
        height: '44px !important',
        fontSize: '14px',
      },
    };

    return (
      <>
        {hasBrandIdentifiersFailed ? (
          <BrandIdentifierModalErrorState />
        ) : (
          <FormControl sx={{ width: '100%', gap: '8px' }}>
            <Typography variant="subtitle2">
              {
                intl.messages[
                  'ui.creativeScoring.criteriaManagementV2.modal.brandIdentifier.body.label'
                ]
              }
            </Typography>
            <TextField
              fullWidth
              variant="outlined"
              sx={textFieldStyles}
              value={brandIdentifiers}
              onChange={(event) => handleChange(event)}
            />
          </FormControl>
        )}
      </>
    );
  };

  const renderModalFooter = () => {
    return (
      <Stack direction="row" sx={{ gap: '8px' }}>
        <Button variant="contained" color="inherit" onClick={handleClose}>
          <Typography variant="subtitle2">
            {
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.addByChannel.button.close'
              ]
            }
          </Typography>
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={!isSubmitButtonEnabled}
        >
          <Typography variant="subtitle2">
            {
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.modal.bestPractices.addByChannel.button.add'
              ]
            }
          </Typography>
        </Button>
      </Stack>
    );
  };

  return (
    <CustomDialog
      isOpen={isOpen}
      headerText={
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.modal.brandIdentifier.header'
        ]
      }
      headerSubText={
        intl.messages[
          'ui.creativeScoring.criteriaManagementV2.modal.brandIdentifier.subText'
        ]
      }
      customFooterChildren={renderModalFooter()}
      bodyChildren={renderModalBody()}
      explicitDialogHeight={hasBrandIdentifiersFailed ? '424px' : '358px'}
      explicitDialogWidth="600px"
    />
  );
};

export default BrandIdentifierModal;
