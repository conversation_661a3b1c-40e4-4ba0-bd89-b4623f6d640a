import React, { useState, ReactNode } from 'react';
import {
  CriteriaGroup,
  CriteriaGroupingContext,
  CriteriaGroupList,
} from './CriteriaGroupingContext';
import CriteriaGroupingDrawer from '../components/CriteriaGroupingDrawer';
import CriteriaGroupingModalWrapper from '../components/CriteriaGroupingModals/CriteriaGroupingModalWrapper';
import CriteriaGroupingDeleteModal from '../components/CriteriaGroupingModals/CriteriaGroupingDeleteModal';
import { GridPaginationModel } from '@mui/x-data-grid-pro';
import {
  criteriaGroupListInitialState,
  defaultPaginationModel,
} from '../constants';

interface CriteriaGroupingProviderProps {
  children: ReactNode;
}

export const CriteriaGroupingProvider: React.FC<
  CriteriaGroupingProviderProps
> = ({ children }) => {
  const [isDrawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [isCriteriaGroupingModalOpen, setCriteriaGroupingModalOpen] =
    useState<boolean>(false);
  const [
    isCriteriaGroupingDeleteModalOpen,
    setCriteriaGroupingDeleteModalOpen,
  ] = useState<boolean>(false);
  const [selectedGroup, setSelectedGroup] = useState<CriteriaGroup | undefined>(
    undefined,
  );
  const [criteriaGroupsList, setCriteriaGroupsList] =
    useState<CriteriaGroupList>(criteriaGroupListInitialState);
  const [
    isRefreshNeededForAddingToCriteriaGroups,
    setIsRefreshNeededForAddingToCriteriaGroups,
  ] = useState<boolean>(true);

  const [searchText, setSearchText] = useState<string>('');
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>(
    defaultPaginationModel,
  );

  const reset = () => {
    setDrawerOpen(false);
    setCriteriaGroupingModalOpen(false);
    setCriteriaGroupingDeleteModalOpen(false);
    setSelectedGroup(undefined);
    setCriteriaGroupsList(criteriaGroupListInitialState);
    setIsRefreshNeededForAddingToCriteriaGroups(true);
    setSearchText('');
    setPaginationModel(defaultPaginationModel);
  };

  const toggleCriteriaGroupingDrawer = () => {
    if (searchText) {
      reset();
    } else {
      setDrawerOpen(!isDrawerOpen);
    }
  };

  const toggleCriteriaGroupingModal = (selectedGroup?: CriteriaGroup) => {
    setCriteriaGroupingModalOpen((prev) => !prev);
    setSelectedGroup(selectedGroup);
  };
  const toggleCriteriaGroupingDeleteModal = (selectedGroup?: CriteriaGroup) => {
    setCriteriaGroupingDeleteModalOpen((prev) => !prev);
    setSelectedGroup(selectedGroup);
  };

  return (
    <CriteriaGroupingContext.Provider
      value={{
        isCriteriaGroupingDrawerOpen: isDrawerOpen,
        toggleCriteriaGroupingDrawer,
        isCriteriaGroupingModalOpen,
        toggleCriteriaGroupingModal,
        isCriteriaGroupingDeleteModalOpen,
        toggleCriteriaGroupingDeleteModal,
        setSelectedGroup,
        selectedGroup,
        setCriteriaGroupsList,
        criteriaGroupsList,
        isRefreshNeededForAddingToCriteriaGroups,
        setIsRefreshNeededForAddingToCriteriaGroups,
        searchText,
        setSearchText,
        paginationModel,
        setPaginationModel,
      }}
    >
      {children}
      <CriteriaGroupingDrawer />
      {isCriteriaGroupingModalOpen && <CriteriaGroupingModalWrapper />}
      {isCriteriaGroupingDeleteModalOpen && <CriteriaGroupingDeleteModal />}
    </CriteriaGroupingContext.Provider>
  );
};
