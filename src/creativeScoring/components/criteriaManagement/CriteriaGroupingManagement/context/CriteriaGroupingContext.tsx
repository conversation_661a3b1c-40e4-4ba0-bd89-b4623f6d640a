import { createContext, useContext } from 'react';
import { GridPaginationModel } from '@mui/x-data-grid-pro';

type CriteriaDetails = {
  defaultDisplayName: string;
  description: string;
  id: number;
  identifier: string;
  parameters: Record<string, string>;
  rule: string;
  name: string;
  platform: string;
};

type CreatedBy = {
  displayName: string;
  firstName: string;
  id: number;
  lastName: string;
  photoUrl: string;
};

export interface CriteriaGroup {
  id: string;
  name: string;
  criteriaDetails: CriteriaDetails[];
  createdBy: CreatedBy;
  dateCreated: string;
  description: string;
  color: string;
}

export interface CriteriaGroupList {
  data: CriteriaGroup[];
  pagination: { totalSize: number };
}

interface CriteriaGroupingContextProps {
  isCriteriaGroupingDrawerOpen: boolean;
  toggleCriteriaGroupingDrawer: () => void;
  isCriteriaGroupingModalOpen: boolean;
  toggleCriteriaGroupingModal: (selectedGroup?: CriteriaGroup) => void;
  isCriteriaGroupingDeleteModalOpen: boolean;
  toggleCriteriaGroupingDeleteModal: (selectedGroup?: CriteriaGroup) => void;
  setSelectedGroup: (group?: CriteriaGroup) => void;
  selectedGroup?: CriteriaGroup;
  setCriteriaGroupsList: (
    groups:
      | CriteriaGroupList
      | ((prevList: CriteriaGroupList) => CriteriaGroupList),
  ) => void;
  criteriaGroupsList: CriteriaGroupList;
  isRefreshNeededForAddingToCriteriaGroups: boolean;
  setIsRefreshNeededForAddingToCriteriaGroups: (isNeeded: boolean) => void;
  searchText: string;
  setSearchText: (text: string) => void;
  paginationModel: GridPaginationModel;
  setPaginationModel: (model: GridPaginationModel) => void;
}

export const CriteriaGroupingContext = createContext<
  CriteriaGroupingContextProps | undefined
>(undefined);

export const useCriteriaGrouping = (): CriteriaGroupingContextProps => {
  const context = useContext(CriteriaGroupingContext);
  if (!context) {
    throw new Error(
      'useCriteriaGrouping must be used within a CriteriaGroupingProvider',
    );
  }
  return context;
};
