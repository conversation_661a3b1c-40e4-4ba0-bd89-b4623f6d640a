import React from 'react';
import { GridToolbarContainer, GridPagination } from '@mui/x-data-grid-pro';
import {
  VidMobBox,
  VidMobStack,
} from '../../../../../../vidMobComponentWrappers';
import Search from '../../../../../../muiCustomComponents/Search';
import { translationKeys } from '../../constants';
import { useCriteriaGrouping } from '../../context/CriteriaGroupingContext';
import { useCriteriaGroupsApi } from '../../hooks/useCriteriaGroupsApi';
import { tablePaginationSx } from '../../../../../../muiCustomComponents/TablePagination/TablePagination';
import ListCount from '../../../../../../muiCustomComponents/ListCount/ListCount';

const {
  DATA_GRID: {
    TOOLBAR: { GROUPS },
  },
} = translationKeys;

const toolbarContainerSx = {
  minHeight: '52px',
  width: '100%',
  justifyContent: 'space-between',
  padding: '0',
  marginBottom: '16px',
};

const CriteriaGroupingDataGridToolbar = () => {
  const { criteriaGroupsList } = useCriteriaGrouping();
  const { isLoading, refetchWithSearchText } = useCriteriaGroupsApi();

  const totalSize = criteriaGroupsList.pagination.totalSize;

  return (
    <GridToolbarContainer sx={toolbarContainerSx}>
      <VidMobStack direction="row" minWidth={400} alignItems="center" gap={4}>
        <ListCount
          count={totalSize}
          itemsIntlKey={GROUPS}
          isLoading={isLoading}
        />
        <VidMobBox maxWidth="250px" width="100%">
          <Search
            placeholder="Search"
            debounceTime={300}
            onSearchChange={refetchWithSearchText}
          />
        </VidMobBox>
      </VidMobStack>
      {Boolean(totalSize) && <GridPagination sx={tablePaginationSx} />}
    </GridToolbarContainer>
  );
};

export default CriteriaGroupingDataGridToolbar;
