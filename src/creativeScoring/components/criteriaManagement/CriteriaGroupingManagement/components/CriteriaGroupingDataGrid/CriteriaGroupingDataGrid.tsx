import React, { useCallback, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { DataGridPro } from '@mui/x-data-grid-pro';
import CriteriaGroupingDataGridToolbar from './CriteriaGroupingDataGridToolbar';
import { InfoFilledIcon } from '../../../../../../assets/vidmob-mui-icons/general';
import EmptyState from '../../../../../../components/EmptyState';
import { VidMobBox } from '../../../../../../vidMobComponentWrappers';
import { useColumns } from './hooks/useColumns';
import { useCriteriaGrouping } from '../../context/CriteriaGroupingContext';
import { useCriteriaGroupsApi } from '../../hooks/useCriteriaGroupsApi';
import { defaultPaginationModel, translationKeys } from '../../constants';

const {
  DATA_GRID: {
    EMPTY_STATE: { TITLE, DESCRIPTION },
  },
} = translationKeys;

const gridSx = {
  height: 'calc(100vh - 220px)',
  border: 0,
  '.MuiDataGrid-columnSeparator': {
    display: 'none',
  },
  '.MuiDataGrid-columnHeader:focus, .MuiDataGrid-cell:focus-within': {
    outline: 'none',
  },
};

const CriteriaGroupingDataGrid: React.FC = () => {
  const intl = useIntl();
  const columns = useColumns();
  const { criteriaGroupsList, paginationModel } = useCriteriaGrouping();
  const { isLoading, refetchWithPagination } = useCriteriaGroupsApi();

  useEffect(() => {
    refetchWithPagination(defaultPaginationModel);
  }, []);

  const CriteriaGroupingEmptyState = useCallback(
    () => (
      <EmptyState
        title={intl.messages[TITLE] as string}
        description={intl.messages[DESCRIPTION] as string}
        icon={InfoFilledIcon}
        boxSx={{
          maxWidth: 180,
          margin: '0 auto',
        }}
      />
    ),
    [intl],
  );

  return (
    <VidMobBox>
      <DataGridPro
        sx={gridSx}
        rows={criteriaGroupsList.data}
        columns={columns}
        loading={isLoading}
        pagination
        paginationMode="server"
        paginationModel={paginationModel}
        onPaginationModelChange={refetchWithPagination}
        rowCount={criteriaGroupsList.pagination.totalSize}
        slots={{
          toolbar: CriteriaGroupingDataGridToolbar,
          noRowsOverlay: CriteriaGroupingEmptyState,
          noResultsOverlay: CriteriaGroupingEmptyState,
        }}
        disableRowSelectionOnClick
        rowHeight={64}
        columnHeaderHeight={52}
        disableColumnResize
        hideFooterPagination
        hideFooterSelectedRowCount
        hideFooter
      />
    </VidMobBox>
  );
};

export default CriteriaGroupingDataGrid;
