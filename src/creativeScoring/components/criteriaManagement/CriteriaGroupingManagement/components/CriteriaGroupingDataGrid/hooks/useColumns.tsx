import React from 'react';
import { useIntl } from 'react-intl';
import { GridCellParams, GridRenderCellParams } from '@mui/x-data-grid-pro';
import { translationKeys } from '../../../constants';
import {
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import EditAction from '../actions/EditAction';
import DeleteAction from '../actions/DeleteAction';
import GroupCell from '../cells/GroupCell';
import CriteriaInGroupCell from '../cells/CriteriaInGroupCell';
import { DateCell } from '../../../../../../../muiCustomComponents/DataGrid/Cells';
import { useSelector } from 'react-redux';
import { getOrganizationPermissions } from '../../../../../../../userManagement/redux/selectors/organization.selectors';
import { CriteriaGroup } from '../../../context/CriteriaGroupingContext';

const {
  DATA_GRID: {
    COLUMNS: { GROUP, CRITERIA_IN_GROUP, CREATED_BY, CREATED_ON },
  },
} = translationKeys;

export const useColumns = () => {
  const intl = useIntl();
  const orgPermissions = useSelector(getOrganizationPermissions);
  const canEditCriteriaGroup = orgPermissions?.canCreateAndEditCriteriaGroup();
  const canDeleteCriteriaGroup = orgPermissions?.canDeleteCriteriaGroup();
  const userCanPerformAction = canEditCriteriaGroup || canDeleteCriteriaGroup;

  const getUserActions = (criteriaGroup: CriteriaGroup) => {
    const actions = [];

    if (canEditCriteriaGroup) {
      actions.push(<EditAction key="edit" item={criteriaGroup} />);
    }

    if (canDeleteCriteriaGroup) {
      actions.push(<DeleteAction key="delete" item={criteriaGroup} />);
    }
    return actions;
  };

  const columns = [
    {
      field: 'name',
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      minWidth: 300,
      renderHeader: () => (
        <VidMobTypography variant="subtitle2" color="textPrimary">
          {intl.messages[GROUP] as string}
        </VidMobTypography>
      ),
      renderCell: (params: GridCellParams) => <GroupCell row={params.row} />,
    },
    {
      field: 'criteriaInGroup',
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      renderHeader: () => (
        <VidMobTypography variant="subtitle2" color="textPrimary">
          {intl.messages[CRITERIA_IN_GROUP] as string}
        </VidMobTypography>
      ),
      renderCell: (params: GridCellParams) => (
        <CriteriaInGroupCell row={params.row} />
      ),
    },
    {
      field: 'createdBy',
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      renderHeader: () => (
        <VidMobTypography variant="subtitle2" color="textPrimary">
          {intl.messages[CREATED_BY] as string}
        </VidMobTypography>
      ),
      renderCell: (cellData: GridRenderCellParams) => (
        <VidMobTooltip
          title={`${cellData.value.firstName} ${cellData.value.lastName}`}
          placement="top"
        >
          <VidMobTypography
            variant="body2"
            color="textPrimary"
            sx={{
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
            }}
          >
            {cellData.value.firstName} {cellData.value.lastName}
          </VidMobTypography>
        </VidMobTooltip>
      ),
    },
    {
      field: 'dateCreated',
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      renderHeader: () => (
        <VidMobTypography variant="subtitle2" color="textPrimary">
          {intl.messages[CREATED_ON] as string}
        </VidMobTypography>
      ),
      renderCell: (cellData: GridRenderCellParams) => (
        <DateCell cellData={cellData} isDisabled={false} />
      ),
    },
  ];

  if (userCanPerformAction) {
    columns.push({
      field: 'actions',
      // @ts-ignore
      type: 'actions',
      resizable: false,
      getActions: (cell: { row: any }) => {
        const { row: criteriaGroup } = cell;
        return getUserActions(criteriaGroup);
      },
    });
  }

  return columns;
};
