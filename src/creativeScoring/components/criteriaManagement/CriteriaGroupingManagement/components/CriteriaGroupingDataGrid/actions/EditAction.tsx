import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobIconButton,
  VidMobTooltip,
} from '../../../../../../../vidMobComponentWrappers';
import { EditFilledIcon } from '../../../../../../../assets/vidmob-mui-icons/general';
import {
  CriteriaGroup,
  useCriteriaGrouping,
} from '../../../context/CriteriaGroupingContext';
import { actionButtonSx, translationKeys } from '../../../constants';

const {
  DATA_GRID: {
    ACTIONS: { EDIT },
  },
} = translationKeys;

type Props = {
  item: CriteriaGroup;
};

const EditAction: React.FC<Props> = ({ item }) => {
  const intl = useIntl();
  const { toggleCriteriaGroupingModal } = useCriteriaGrouping();

  const onClickHandle = () => {
    toggleCriteriaGroupingModal(item);
  };

  return (
    <VidMobTooltip
      title={intl.messages[EDIT] as string}
      placement="top"
      disableInteractive
    >
      <div>
        <VidMobIconButton
          key="edit-action"
          onClick={onClickHandle}
          sx={actionButtonSx}
        >
          <EditFilledIcon />
        </VidMobIconButton>
      </div>
    </VidMobTooltip>
  );
};

export default EditAction;
