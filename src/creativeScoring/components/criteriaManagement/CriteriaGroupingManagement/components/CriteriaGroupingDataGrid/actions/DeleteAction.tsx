import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobIconButton,
  VidMobTooltip,
} from '../../../../../../../vidMobComponentWrappers';
import { DeleteFilledIcon } from '../../../../../../../assets/vidmob-mui-icons/general';
import {
  CriteriaGroup,
  useCriteriaGrouping,
} from '../../../context/CriteriaGroupingContext';
import { actionButtonSx, translationKeys } from '../../../constants';

const {
  DATA_GRID: {
    ACTIONS: { DELETE },
  },
} = translationKeys;

type Props = {
  item: CriteriaGroup;
};

const DeleteAction: React.FC<Props> = ({ item }) => {
  const intl = useIntl();
  const { toggleCriteriaGroupingDeleteModal } = useCriteriaGrouping();

  const onClickHandle = () => {
    toggleCriteriaGroupingDeleteModal(item);
  };

  return (
    <VidMobTooltip
      title={intl.messages[DELETE] as string}
      placement="top"
      disableInteractive
    >
      <div>
        <VidMobIconButton
          key="delete-action"
          onClick={onClickHandle}
          sx={actionButtonSx}
        >
          <DeleteFilledIcon />
        </VidMobIconButton>
      </div>
    </VidMobTooltip>
  );
};

export default DeleteAction;
