import React from 'react';
import { CriteriaGroup } from '../../../context/CriteriaGroupingContext';
import {
  VidMobBox,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';

type Props = {
  row: CriteriaGroup;
};

const GroupCell: React.FC<Props> = ({ row }) => {
  return (
    <VidMobStack direction="row" alignItems="center" gap={6}>
      <VidMobBox
        sx={{
          backgroundColor: row?.color,
          borderRadius: '50%',
          width: 16,
          height: 16,
        }}
      />
      <VidMobStack direction="column" gap={2}>
        <VidMobTooltip title={row?.name} placement="top">
          <VidMobTypography
            variant="body2"
            color="textPrimary"
            sx={{
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              maxWidth: '255px',
            }}
          >
            {row?.name}
          </VidMobTypography>
        </VidMobTooltip>

        <VidMobTypography
          variant="caption"
          color="textSecondary"
          sx={{
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            maxWidth: '255px',
          }}
        >
          {row.description}
        </VidMobTypography>
      </VidMobStack>
    </VidMobStack>
  );
};

export default GroupCell;
