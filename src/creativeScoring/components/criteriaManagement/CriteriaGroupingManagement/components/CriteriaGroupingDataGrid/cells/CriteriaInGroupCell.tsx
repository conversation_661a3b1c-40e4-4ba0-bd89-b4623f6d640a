import React from 'react';

import { CriteriaGroup } from '../../../context/CriteriaGroupingContext';
import {
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';

type Props = {
  row: CriteriaGroup;
};

const CriteriaInGroupCell: React.FC<Props> = ({ row }) => {
  const tooltipTitle = row.criteriaDetails.map((cd) => cd.name).join(', ');

  return (
    <VidMobTooltip title={tooltipTitle} placement="top" disableInteractive>
      <div>
        <VidMobTypography variant="body2" color="textPrimary">
          {row.criteriaDetails.length}
        </VidMobTypography>
      </div>
    </VidMobTooltip>
  );
};

export default CriteriaInGroupCell;
