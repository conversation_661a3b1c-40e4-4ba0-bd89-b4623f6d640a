import React from 'react';
import {
  VidMobBackdrop,
  VidMobButton,
  VidMobDrawer,
  VidMobStack,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import { translationKeys } from '../constants';
import { useCriteriaGrouping } from '../context/CriteriaGroupingContext';
import CriteriaGroupingDataGrid from './CriteriaGroupingDataGrid/CriteriaGroupingDataGrid';
import { useSelector } from 'react-redux';
import { getOrganizationPermissions } from '../../../../../userManagement/redux/selectors/organization.selectors';

const { DRAWER_TITLE, CREATE_NEW, CLOSE } = translationKeys;

const drawerWrapperSx = {
  width: '950px',
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: '950px',
    marginTop: '52px',
    height: 'calc(100% - 52px)',
    zIndex: 2,
    boxShadow: '0px 4px 12px 0px rgba(15, 15, 15, 0.32)',
  },
};
const drawerSx = {
  width: '100%',
  height: '100%',
  position: 'sticky',
  flexDirection: 'column',
  justifyContent: 'space-between',
  alignItems: 'center',
};
const backdropSx = { backgroundColor: 'transparent', zIndex: 1 };
const headerSx = {
  width: '100%',
  maxHeight: '84px',
  padding: '24px',
  alignItems: 'center',
  justifyContent: 'space-between',
};
const footerSx = {
  ...headerSx,
  borderTop: '1px solid #BDBDBD',
  justifyContent: 'flex-end',
};

const CriteriaGroupingDrawer = () => {
  const intl = useIntl();

  const {
    toggleCriteriaGroupingModal,
    toggleCriteriaGroupingDrawer,
    isCriteriaGroupingDrawerOpen,
  } = useCriteriaGrouping();

  const orgPermissions = useSelector(getOrganizationPermissions);
  const canCreateCriteriaGroup =
    orgPermissions?.canCreateAndEditCriteriaGroup();

  return (
    <>
      <VidMobBackdrop
        sx={backdropSx}
        open={isCriteriaGroupingDrawerOpen}
        onClick={toggleCriteriaGroupingDrawer}
      />
      <VidMobDrawer
        variant="persistent"
        anchor="right"
        open={isCriteriaGroupingDrawerOpen}
        sx={drawerWrapperSx}
      >
        {isCriteriaGroupingDrawerOpen && (
          <VidMobStack sx={drawerSx}>
            <VidMobStack direction="row" sx={headerSx}>
              <VidMobTypography variant="h6">
                {intl.messages[DRAWER_TITLE] as string}
              </VidMobTypography>
              {canCreateCriteriaGroup && (
                <VidMobButton
                  variant="contained"
                  color="primary"
                  onClick={() => toggleCriteriaGroupingModal()}
                >
                  {intl.messages[CREATE_NEW] as string}
                </VidMobButton>
              )}
            </VidMobStack>
            <VidMobStack width="100%" height="100%" pl={12} pr={12}>
              <CriteriaGroupingDataGrid />
            </VidMobStack>
            <VidMobStack direction="row" sx={footerSx}>
              <VidMobButton
                variant="contained"
                color="inherit"
                onClick={toggleCriteriaGroupingDrawer}
              >
                {intl.messages[CLOSE] as string}
              </VidMobButton>
            </VidMobStack>
          </VidMobStack>
        )}
      </VidMobDrawer>
    </>
  );
};

export default CriteriaGroupingDrawer;
