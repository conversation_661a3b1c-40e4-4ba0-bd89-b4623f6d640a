import React from 'react';
import {
  VidMobBox,
  VidMobStack,
} from '../../../../../../../vidMobComponentWrappers';

interface ColorPickerFieldProps {
  selectedColor?: string;
  onColorChange: (color: string) => void;
}

export const COLORS = [
  { color: '#DEE6FF', borderColor: '#1842EF', label: 'Light Blue' },
  { color: '#E1F5FE', borderColor: '#039BE5', label: 'Sky Blue' },
  { color: '#E8F5E9', borderColor: '#43A047', label: 'Light Green' },
  { color: '#FFF3E0', borderColor: '#FB8C00', label: 'Orange' },
  { color: '#FEEBEE', borderColor: '#D32F2F', label: 'Light Red' },
  { color: '#E0E0E0', borderColor: '#616161', label: 'Gray' },
];

const ColorPickerField: React.FC<ColorPickerFieldProps> = ({
  selectedColor = COLORS[0].color,
  onColorChange,
}) => {
  return (
    <VidMobStack direction="row" spacing={2} role="radiogroup">
      {COLORS.map(({ color, borderColor, label }) => (
        <VidMobBox
          key={color}
          onClick={() => onColorChange(color)}
          aria-checked={selectedColor === color}
          role="radio"
          aria-label={label}
          sx={{
            width: '24px',
            height: '24px',
            backgroundColor: color,
            borderRadius: '6px',
            border:
              selectedColor === color
                ? `1px solid ${borderColor}`
                : '1px solid transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            transition: 'border 0.3s',
          }}
        >
          {selectedColor === color && (
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.1381 4.41529C13.3802 4.65742 13.3975 5.03924 13.19 5.30133L13.1381 5.35956L6.91725 11.5804C6.67512 11.8225 6.2933 11.8398 6.03122 11.6323L5.97298 11.5804L2.86256 8.46999C2.6018 8.20923 2.6018 7.78647 2.86256 7.52571C3.10469 7.28358 3.48651 7.26629 3.74859 7.47383L3.80683 7.52571L6.44511 10.1634L12.1938 4.41529C12.436 4.17316 12.8178 4.15587 13.0799 4.36341L13.1381 4.41529Z"
                stroke={borderColor}
                strokeWidth="2"
              />
            </svg>
          )}
        </VidMobBox>
      ))}
    </VidMobStack>
  );
};

export default ColorPickerField;
