import { useState, useCallback, useEffect } from 'react';
import { COLORS } from './ColorPickerField';
import {
  CriteriaGroup,
  useCriteriaGrouping,
} from '../../../context/CriteriaGroupingContext';

export enum FormField {
  NAME = 'name',
  DESCRIPTION = 'description',
  COLOR = 'color',
}

export interface FormState {
  data: {
    [FormField.NAME]: string;
    [FormField.DESCRIPTION]: string;
    [FormField.COLOR]: string;
  };
  touched: {
    [FormField.NAME]: boolean;
    [FormField.DESCRIPTION]: boolean;
  };
  errors: {
    [FormField.NAME]?: string;
  };
}

export const validateForm = (data: FormState['data']) => {
  return data[FormField.NAME]?.trim() !== '';
};

export const hasFormChanged = (
  data: FormState['data'],
  initialData?: CriteriaGroup,
) => {
  return (
    data[FormField.NAME] !== initialData?.name ||
    data[FormField.DESCRIPTION] !== initialData?.description ||
    data[FormField.COLOR] !== initialData?.color
  );
};

export const useCriteriaGroupingForm = () => {
  const { selectedGroup } = useCriteriaGrouping();
  const [formState, setFormState] = useState<FormState>({
    data: {
      [FormField.NAME]: '',
      [FormField.DESCRIPTION]: '',
      [FormField.COLOR]: COLORS[0].color,
    },
    touched: {
      [FormField.NAME]: false,
      [FormField.DESCRIPTION]: false,
    },
    errors: {
      [FormField.NAME]: '',
    },
  });

  useEffect(() => {
    if (selectedGroup) {
      setFormState((prevFormState) => ({
        ...prevFormState,
        data: {
          [FormField.NAME]: selectedGroup.name,
          [FormField.DESCRIPTION]: selectedGroup.description,
          [FormField.COLOR]: selectedGroup.color,
        },
      }));
    } else {
      resetFormData();
    }
  }, [selectedGroup]);

  const handleTextChange =
    (field: FormField) => (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;

      setFormState((prevState) => ({
        ...prevState,
        data: {
          ...prevState.data,
          [field]: value,
        },
        touched: {
          ...prevState.touched,
          [field]: true,
        },
        errors: {
          ...prevState.errors,
          [field]: '',
        },
      }));
    };

  const handleColorChange = useCallback((color: string) => {
    setFormState((prevState) => ({
      ...prevState,
      data: {
        ...prevState.data,
        [FormField.COLOR]: color,
      },
    }));
  }, []);

  const resetFormData = () => {
    setFormState({
      data: {
        [FormField.NAME]: '',
        [FormField.DESCRIPTION]: '',
        [FormField.COLOR]: COLORS[0].color,
      },
      touched: {
        [FormField.NAME]: false,
        [FormField.DESCRIPTION]: false,
      },
      errors: {
        [FormField.NAME]: '',
      },
    });
  };

  const handleServerError = (error: string) => {
    setFormState((prevState) => ({
      ...prevState,
      errors: {
        ...prevState.errors,
        [FormField.NAME]: error,
      },
    }));
  };

  const isFormValid =
    validateForm(formState.data) && !formState.errors[FormField.NAME];
  const isFormChanged = hasFormChanged(formState.data, selectedGroup);

  return {
    formState,
    handleServerError,
    handleTextChange,
    handleColorChange,
    resetFormData,
    isFormValid,
    isFormChanged,
  };
};
