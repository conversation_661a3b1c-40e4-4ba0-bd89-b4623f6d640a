import React from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobStack,
  VidMobTextField,
  VidMobFormItem,
} from '../../../../../../../vidMobComponentWrappers';
import ColorPickerField from './ColorPickerField';
import { translationKeys } from '../../../constants';
import { FormField, FormState } from './useCriteriaGroupingForm';

const {
  FORM: { NAME_FIELD, DESCRIPTION_FIELD, COLOR_FIELD },
} = translationKeys;

export const nameFieldSx = {
  '& .MuiOutlinedInput-root': {
    minWidth: '452px',
    height: '44px',
    fontSize: '14px',
  },
};

export const descriptionFieldSx = {
  '& .MuiOutlinedInput-root': {
    ...nameFieldSx['& .MuiOutlinedInput-root'],
    height: '124px',
    alignItems: 'flex-start',
  },
};

interface CriteriaGroupingFormProps {
  formState: FormState;
  handleTextChange: (
    field: FormField,
  ) => (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleColorChange: (color: string) => void;
}

const CriteriaGroupingForm: React.FC<CriteriaGroupingFormProps> = ({
  formState,
  handleTextChange,
  handleColorChange,
}) => {
  const intl = useIntl();

  return (
    <VidMobStack gap={12}>
      <VidMobFormItem
        itemLabel={intl.messages[NAME_FIELD] as string}
        isRequired
        padding={0}
        error={formState.errors?.name}
      >
        <VidMobTextField
          fullWidth
          variant="outlined"
          sx={nameFieldSx}
          value={formState.data[FormField.NAME]}
          onChange={handleTextChange(FormField.NAME)}
          error={Boolean(formState.errors?.name)}
        />
      </VidMobFormItem>
      <VidMobFormItem
        itemLabel={intl.messages[DESCRIPTION_FIELD] as string}
        padding={0}
      >
        <VidMobTextField
          fullWidth
          variant="outlined"
          sx={descriptionFieldSx}
          value={formState.data[FormField.DESCRIPTION]}
          onChange={handleTextChange(FormField.DESCRIPTION)}
        />
      </VidMobFormItem>
      <VidMobFormItem
        itemLabel={intl.messages[COLOR_FIELD] as string}
        padding={0}
      >
        <ColorPickerField
          selectedColor={formState.data[FormField.COLOR]}
          onColorChange={handleColorChange}
        />
      </VidMobFormItem>
    </VidMobStack>
  );
};

export default CriteriaGroupingForm;
