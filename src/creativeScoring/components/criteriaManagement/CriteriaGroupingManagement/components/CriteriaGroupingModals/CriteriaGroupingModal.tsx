import React from 'react';
import { useIntl } from 'react-intl';
import CustomDialog from '../../../../../../muiCustomComponents/CustomDialog';
import CriteriaGroupingForm from './CriteriaGroupingForm';
import { useCriteriaGroupingForm } from './CriteriaGroupingForm/useCriteriaGroupingForm';
import { CriteriaModalConfig } from './useCriteriaGroupModalConfig';
import { translationKeys } from '../../constants';
import {
  CriteriaGroup,
  useCriteriaGrouping,
} from '../../context/CriteriaGroupingContext';

const {
  MODAL_SUB_TITLE,
  FORM: { NAME_FIELD_ERROR },
} = translationKeys;

interface CriteriaGroupingModalProps {
  config: CriteriaModalConfig;
  isOpen: boolean;
  onClose: () => void;
}

const CriteriaGroupingModal: React.FC<CriteriaGroupingModalProps> = ({
  config,
  isOpen,
  onClose,
}) => {
  const intl = useIntl();
  const { selectedGroup } = useCriteriaGrouping();
  const {
    formState,
    handleServerError,
    handleTextChange,
    handleColorChange,
    resetFormData,
    isFormValid,
    isFormChanged,
  } = useCriteriaGroupingForm();

  const handleSave = async () => {
    const result = await config.onSubmit({
      ...selectedGroup,
      ...formState.data,
    } as CriteriaGroup);
    if (result.success) {
      resetFormData();
    } else {
      if (result.error) {
        handleServerError(intl.messages[NAME_FIELD_ERROR] as string);
      }
    }
  };

  const handleClose = () => {
    onClose();
    resetFormData();
  };

  return (
    <CustomDialog
      isOpen={isOpen}
      onClose={handleClose}
      onSubmit={handleSave}
      headerText={intl.messages[config.headerText] as string}
      headerSubText={`*${intl.messages[MODAL_SUB_TITLE] as string}`}
      isSubmitButtonDisabled={!isFormValid || !isFormChanged}
      submitButtonLabel={intl.messages[config.submitButtonLabel] as string}
      bodyChildren={
        <CriteriaGroupingForm
          formState={formState}
          handleTextChange={handleTextChange}
          handleColorChange={handleColorChange}
        />
      }
    />
  );
};

export default CriteriaGroupingModal;
