import React from 'react';
import { useIntl } from 'react-intl';
import { useCriteriaGrouping } from '../../context/CriteriaGroupingContext';
import CustomDialog from '../../../../../../muiCustomComponents/CustomDialog';
import { translationKeys } from '../../constants';
import { useCriteriaGroupsApi } from '../../hooks/useCriteriaGroupsApi';

const {
  MODAL_DELETE_TITLE,
  MODAL_DELETE_SUB_TITLE,
  MODAL_DELETE_SUBMIT_BUTTON,
} = translationKeys;

const CriteriaGroupingDeleteModal: React.FC = () => {
  const intl = useIntl();
  const {
    isCriteriaGroupingDeleteModalOpen,
    toggleCriteriaGroupingDeleteModal,
    selectedGroup,
  } = useCriteriaGrouping();
  const { deleteCriteriaGroup } = useCriteriaGroupsApi();
  const title = intl.formatMessage(
    { id: MODAL_DELETE_TITLE },
    { name: selectedGroup?.name },
  );

  const handleDelete = () => {
    deleteCriteriaGroup.mutate(selectedGroup?.id as string);
  };

  return (
    <CustomDialog
      isDelete
      headerText={title}
      headerSubText={intl.messages[MODAL_DELETE_SUB_TITLE]}
      submitButtonLabel={intl.messages[MODAL_DELETE_SUBMIT_BUTTON]}
      isOpen={isCriteriaGroupingDeleteModalOpen}
      onClose={toggleCriteriaGroupingDeleteModal}
      onSubmit={handleDelete}
      explicitDialogWidth="420px"
    />
  );
};

export default CriteriaGroupingDeleteModal;
