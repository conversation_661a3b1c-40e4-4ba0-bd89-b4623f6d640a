import {
  CriteriaGroup,
  useCriteriaGrouping,
} from '../../context/CriteriaGroupingContext';
import { translationKeys } from '../../constants';
import { useCriteriaGroupsApi } from '../../hooks/useCriteriaGroupsApi';
import { FormState } from './CriteriaGroupingForm/useCriteriaGroupingForm';

const { MODAL_CREATE_TITLE, CREATE, MODAL_EDIT_TITLE, SAVE } = translationKeys;

export interface CriteriaModalConfig {
  headerText: string;
  submitButtonLabel: string;
  onSubmit: (
    formData: FormState['data'] & CriteriaGroup,
  ) => Promise<{ success: boolean; error?: unknown }>;
}

export const useCriteriaGroupModalConfig = () => {
  const { createCriteriaGroup, editCriteriaGroup } = useCriteriaGroupsApi();
  const { selectedGroup } = useCriteriaGrouping();

  const getCreateCriteriaGroupConfig = (): CriteriaModalConfig => ({
    headerText: MODAL_CREATE_TITLE,
    submitButtonLabel: CREATE,
    onSubmit: async (formData: CriteriaGroup) => {
      try {
        await createCriteriaGroup.mutateAsync(formData);
        return { success: true };
      } catch (error) {
        return {
          success: false,
          error,
        };
      }
    },
  });

  const getEditCriteriaGroupConfig = (): CriteriaModalConfig => ({
    headerText: MODAL_EDIT_TITLE,
    submitButtonLabel: SAVE,
    onSubmit: async (formData) => {
      try {
        await editCriteriaGroup.mutateAsync({ ...selectedGroup, ...formData });
        return { success: true };
      } catch (error) {
        return { success: false, error };
      }
    },
  });

  return { getCreateCriteriaGroupConfig, getEditCriteriaGroupConfig };
};
