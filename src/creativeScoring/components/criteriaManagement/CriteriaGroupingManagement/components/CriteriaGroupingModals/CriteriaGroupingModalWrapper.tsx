import React from 'react';
import { useCriteriaGrouping } from '../../context/CriteriaGroupingContext';
import CriteriaGroupingModal from './CriteriaGroupingModal';
import { useCriteriaGroupModalConfig } from './useCriteriaGroupModalConfig';

const CriteriaGroupingModalWrapper = () => {
  const {
    toggleCriteriaGroupingModal,
    isCriteriaGroupingModalOpen,
    selectedGroup,
  } = useCriteriaGrouping();
  const { getCreateCriteriaGroupConfig, getEditCriteriaGroupConfig } =
    useCriteriaGroupModalConfig();

  const config = selectedGroup
    ? getEditCriteriaGroupConfig()
    : getCreateCriteriaGroupConfig();

  return (
    <CriteriaGroupingModal
      isOpen={isCriteriaGroupingModalOpen}
      onClose={toggleCriteriaGroupingModal}
      config={config}
    />
  );
};

export default CriteriaGroupingModalWrapper;
