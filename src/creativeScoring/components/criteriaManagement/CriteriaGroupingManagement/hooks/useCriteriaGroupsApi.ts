import { useMutation, useQuery } from '@tanstack/react-query';
import {
  CriteriaGroup,
  useCriteriaGrouping,
} from '../context/CriteriaGroupingContext';
import { displayToastAlert } from '../../../../../muiCustomComponents/ToastAlert/displayToastAlert';
import { useIntl } from 'react-intl';
import { translationKeys } from '../constants';
import BffService from '../../../../../apiServices/BffService';
import { useSelector } from 'react-redux';
import {
  GridPaginationModel,
  GridRowSelectionModel,
} from '@mui/x-data-grid-pro';
import {
  getCurrentPartnerId,
  getOrganizationId,
} from '../../../../../redux/selectors/partner.selectors';
import {
  EndpointType,
  fetchCriteriaGroupsList,
  getCriteriaGroupsEndpoint,
} from '../helpers';

export interface AssignDeassignPayload {
  criteriaIds: GridRowSelectionModel;
  selectedCriteriaGroups: string[];
  unselectedCriteriaGroups: string[];
}

const {
  FORM: {
    TOAST: {
      CREATION_SUCCESS,
      CREATION_ERROR,
      EDIT_SUCCESS,
      EDIT_ERROR,
      DELETE_SUCCESS,
      DELETE_ERROR,
    },
  },
} = translationKeys;

export const useCriteriaGroupsApi = () => {
  const intl = useIntl();
  const organizationId = useSelector(getOrganizationId);
  const workspaceId = useSelector(getCurrentPartnerId);

  const {
    toggleCriteriaGroupingModal,
    toggleCriteriaGroupingDeleteModal,
    setCriteriaGroupsList,
    setIsRefreshNeededForAddingToCriteriaGroups,
    searchText,
    setSearchText,
    paginationModel,
    setPaginationModel,
  } = useCriteriaGrouping();

  const createCriteriaGroup = useMutation({
    mutationFn: async (newGroup: CriteriaGroup) => {
      const endpoint = getCriteriaGroupsEndpoint(EndpointType.CREATE, {
        organizationId,
        workspaceId,
      });
      return await BffService.handleBffApiPost(endpoint, newGroup);
    },
    onSuccess: (data) => {
      setCriteriaGroupsList((prevList) => ({
        data: [data, ...prevList.data],
        pagination: { totalSize: prevList.pagination.totalSize + 1 },
      }));
      toggleCriteriaGroupingModal();
      displayToastAlert({
        message: intl.messages[CREATION_SUCCESS],
        type: 'success',
      });
      setIsRefreshNeededForAddingToCriteriaGroups(true);
    },
    onError: () => {
      displayToastAlert({
        message: intl.messages[CREATION_ERROR],
        type: 'error',
      });
    },
  });

  const editCriteriaGroup = useMutation({
    mutationFn: async (updatedGroup: CriteriaGroup) => {
      const endpoint = getCriteriaGroupsEndpoint(EndpointType.EDIT, {
        organizationId,
        workspaceId,
        criteriaGroupId: updatedGroup.id,
      });
      return await BffService.handleBffApiPatch(endpoint, updatedGroup);
    },
    onSuccess: (updatedGroup) => {
      toggleCriteriaGroupingModal();
      setCriteriaGroupsList((prevList) => {
        const updatedData = prevList.data.map((group) =>
          group.id === updatedGroup.id ? updatedGroup : group,
        );

        return {
          data: updatedData,
          pagination: { totalSize: prevList.pagination.totalSize },
        };
      });
      displayToastAlert({
        message: intl.messages[EDIT_SUCCESS],
        type: 'success',
      });
      setIsRefreshNeededForAddingToCriteriaGroups(true);
    },
    onError: () => {
      displayToastAlert({
        message: intl.messages[EDIT_ERROR],
        type: 'error',
      });
    },
  });

  const deleteCriteriaGroup = useMutation({
    mutationFn: async (groupId: string) => {
      const endpoint = getCriteriaGroupsEndpoint(EndpointType.DELETE, {
        organizationId,
        workspaceId,
        criteriaGroupId: groupId,
      });
      return await BffService.handleBffApiDelete(endpoint);
    },
    onSuccess: ({ data }, groupId) => {
      if (data) {
        setCriteriaGroupsList((prevList) => {
          const updatedData = prevList.data.filter(
            (group) => group.id !== groupId,
          );
          return {
            data: updatedData,
            pagination: { totalSize: prevList.pagination.totalSize - 1 },
          };
        });

        displayToastAlert({
          message: intl.messages[DELETE_SUCCESS],
          type: 'success',
        });
        toggleCriteriaGroupingDeleteModal();
        setIsRefreshNeededForAddingToCriteriaGroups(true);
      }
    },
    onError: () => {
      displayToastAlert({
        message: intl.messages[DELETE_ERROR],
        type: 'error',
      });
    },
  });

  const assignDeassignCriteriaGroup = useMutation({
    mutationFn: async (payload: AssignDeassignPayload) => {
      const endpoint = getCriteriaGroupsEndpoint(EndpointType.ASSIGN_DEASSIGN, {
        organizationId,
        workspaceId,
      });
      return await BffService.handleBffApiPost(endpoint, payload);
    },
    onSuccess: () => {
      displayToastAlert({
        message: intl.messages[EDIT_SUCCESS],
        type: 'success',
      });
    },
    onError: () => {
      displayToastAlert({
        message: intl.messages[EDIT_ERROR],
        type: 'error',
      });
    },
  });

  const { isLoading, refetch } = useQuery({
    queryKey: ['criteriaGroups', paginationModel, searchText],
    queryFn: async () =>
      fetchCriteriaGroupsList({
        organizationId,
        workspaceId,
        offset: paginationModel.page * paginationModel.pageSize,
        perPage: paginationModel.pageSize,
        searchText,
        includeCriteriaDetails: true,
      }),
    onSuccess: ({
      data,
      pagination,
    }: {
      data: CriteriaGroup[];
      pagination: { totalSize: number };
    }) => {
      setCriteriaGroupsList(() => ({
        data,
        pagination,
      }));
    },
    enabled: false,
    retry: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });

  const refetchWithPagination = (model: GridPaginationModel) => {
    setPaginationModel(model);
    setTimeout(refetch, 100);
  };

  const refetchWithSearchText = (searchStr: string) => {
    if (searchStr !== searchText) {
      setSearchText(searchStr);
      setTimeout(refetch, 100);
    }
  };

  return {
    isLoading,
    refetchWithSearchText,
    refetchWithPagination,
    assignDeassignCriteriaGroup,
    createCriteriaGroup,
    editCriteriaGroup,
    deleteCriteriaGroup,
  };
};
