const baseTranslationPath =
  'ui.creativeScoring.criteriaManagementV2.criteriaGrouping';

export const translationKeys = {
  DRAWER_TITLE: `${baseTranslationPath}.drawer.title`,
  CREATE_NEW: `${baseTranslationPath}.buttons.createNew`,
  CLOSE: `${baseTranslationPath}.buttons.close`,
  SAVE: `${baseTranslationPath}.buttons.save`,
  CREATE: `${baseTranslationPath}.buttons.create`,
  MODAL_CREATE_TITLE: `${baseTranslationPath}.modal.createTitle`,
  MODAL_EDIT_TITLE: `${baseTranslationPath}.modal.editTitle`,
  MODAL_SUB_TITLE: `${baseTranslationPath}.modal.subTitle`,
  MODAL_DELETE_TITLE: `${baseTranslationPath}.deleteModal.title`,
  MODAL_DELETE_SUB_TITLE: `${baseTranslationPath}.deleteModal.subTitle`,
  MODAL_DELETE_SUBMIT_BUTTON: `${baseTranslationPath}.deleteModal.submitButton`,
  FORM: {
    NAME_FIELD: `${baseTranslationPath}.modal.form.name`,
    DESCRIPTION_FIELD: `${baseTranslationPath}.modal.form.description`,
    COLOR_FIELD: `${baseTranslationPath}.modal.form.color`,
    NAME_FIELD_ERROR: `${baseTranslationPath}.modal.form.error.name`,
    TOAST: {
      CREATION_ERROR: `${baseTranslationPath}.modal.form.create.toast.error`,
      CREATION_SUCCESS: `${baseTranslationPath}.modal.form.create.toast.success`,
      EDIT_ERROR: `${baseTranslationPath}.modal.form.edit.toast.error`,
      EDIT_SUCCESS: `${baseTranslationPath}.modal.form.edit.toast.success`,
      DELETE_ERROR: `${baseTranslationPath}.modal.form.delete.toast.error`,
      DELETE_SUCCESS: `${baseTranslationPath}.modal.form.delete.toast.success`,
    },
  },
  DATA_GRID: {
    EMPTY_STATE: {
      TITLE: `${baseTranslationPath}.dataGrid.emptyState.title`,
      DESCRIPTION: `${baseTranslationPath}.dataGrid.emptyState.description`,
    },
    ACTIONS: {
      EDIT: `${baseTranslationPath}.dataGrid.actions.edit`,
      DELETE: `${baseTranslationPath}.dataGrid.actions.delete`,
    },
    TOOLBAR: {
      GROUPS: `${baseTranslationPath}.dataGrid.toolbar.groups`,
    },
    COLUMNS: {
      GROUP: `${baseTranslationPath}.dataGrid.columns.group`,
      CRITERIA_IN_GROUP: `${baseTranslationPath}.dataGrid.columns.criteriaInGroup`,
      CREATED_BY: `${baseTranslationPath}.dataGrid.columns.createdBy`,
      CREATED_ON: `${baseTranslationPath}.dataGrid.columns.createdOn`,
    },
  },
};

export const actionButtonSx = {
  '&.MuiIconButton-sizeMedium': {
    width: 32,
    height: 32,
    '& svg': {
      width: 16,
      height: 16,
    },
  },
};

export const criteriaGroupListInitialState = {
  data: [],
  pagination: {
    totalSize: 0,
  },
};

export const defaultPaginationModel = {
  page: 0,
  pageSize: 10,
};
