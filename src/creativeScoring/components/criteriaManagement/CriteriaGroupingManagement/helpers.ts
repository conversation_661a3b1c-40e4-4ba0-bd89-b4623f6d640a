import BffService from '../../../../apiServices/BffService';
import { CriteriaGroupList } from './context/CriteriaGroupingContext';

export enum EndpointType {
  CREATE = 'create',
  GET_SINGLE = 'get-single',
  GET_LIST = 'get-list',
  EDIT = 'edit',
  DELETE = 'delete',
  ASSIGN_DEASSIGN = 'assign-deassign',
}

interface BaseOptions {
  organizationId: string;
  workspaceId?: number;
}

interface SingleGetEditDeleteOptions extends BaseOptions {
  criteriaGroupId: string;
}

interface FetchCriteriaGroupsParams {
  organizationId: string;
  workspaceId?: number;
  offset?: number;
  perPage?: number;
  searchText?: string;
  includeCriteriaDetails?: boolean;
}

export function getCriteriaGroupsEndpoint(
  type: EndpointType,
  options: BaseOptions | SingleGetEditDeleteOptions,
): string {
  const { organizationId, workspaceId } = options;

  switch (type) {
    case EndpointType.CREATE:
      return `/v1/criteria-group/organization/${organizationId}/workspace/${workspaceId}`;
    case EndpointType.GET_SINGLE:
      return `/v1/criteria-group/organization/${organizationId}/workspace/${workspaceId}/criteria-group/${(options as SingleGetEditDeleteOptions).criteriaGroupId}`;
    case EndpointType.GET_LIST:
      return `/v1/criteria-group/organization/${organizationId}/workspace/${workspaceId}`;
    case EndpointType.EDIT:
      return `/v1/criteria-group/organization/${organizationId}/workspace/${workspaceId}/criteria-group/${(options as SingleGetEditDeleteOptions).criteriaGroupId}`;
    case EndpointType.DELETE:
      return `/v1/criteria-group/organization/${organizationId}/workspace/${workspaceId}/criteria-group/${(options as SingleGetEditDeleteOptions).criteriaGroupId}`;
    case EndpointType.ASSIGN_DEASSIGN:
      return `/v1/criteria-group/organization/${organizationId}/workspace/${workspaceId}/criteria-group/criteria`;
    default:
      throw new Error(`Unsupported endpoint type: ${type}`);
  }
}

export const fetchCriteriaGroupsList = async ({
  organizationId,
  workspaceId,
  offset = 0,
  perPage = 10,
  searchText = '',
  includeCriteriaDetails = false,
}: FetchCriteriaGroupsParams): Promise<CriteriaGroupList> => {
  const endpoint = getCriteriaGroupsEndpoint(EndpointType.GET_LIST, {
    organizationId,
    workspaceId,
  });
  return await BffService.handleBffApiGetWithPagination(endpoint, {
    includeCriteriaDetails,
    offset,
    perPage,
    searchText,
  });
};
