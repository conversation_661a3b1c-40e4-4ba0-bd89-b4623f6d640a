import { useMutation } from '@tanstack/react-query';
import BffCriteriaManagementService from '../../../../apiServices/BffCriteriaManagementService';
import dayjs from 'dayjs';
import { useIntl } from 'react-intl';

const CRITERIA_INTL =
  'ui.creativeScoring.criteriaManagementV2.csvTitle.criteria';

interface ExportCriteriaParams {
  workspaceIds: string[];
  sort: { sortBy: string; sortOrder: string };
  filters: Record<string, any>;
  searchText: string;
}

export const useExportCriteriaCsv = () => {
  const intl = useIntl();
  const criteriaText = intl.formatMessage({
    id: CRITERIA_INTL,
    defaultMessage: 'Criteria',
  });

  return useMutation(
    async ({
      workspaceIds,
      sort,
      filters,
      searchText,
    }: ExportCriteriaParams) => {
      const response =
        await BffCriteriaManagementService.createCriteriaExportAsCSV({
          workspaceIds,
          sort,
          filters,
          searchText,
        });

      const formattedDate = dayjs().format('YYYY-MM-DD');
      const fileName = `${criteriaText}_${formattedDate}.csv`;

      const blob = new Blob([response], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },
  );
};
