import React, { useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import {
  VidMobButton,
  VidMobBadge,
  VidMobTooltip,
} from '../../../../vidMobComponentWrappers';
import PageHeaderV2 from '../../../../components/PageHeaderV2';
import {
  getIsBrandIdentifiersLoading,
  getIsCriteriaTemplatesLoading,
  getCanUserUpdateCriteria,
  getIsLoading,
} from '../../../redux/selectors/criteriaManagement.selectors';
import { CRITERIA_MANAGEMENT_MODAL_TYPES } from '../../../constants/criteriaManagement.constants';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { useGetCurrentBestPractices } from '../../../query-hooks/useGetCurrentBestPractices';
import { getCurrentPartnerId } from '../../../../redux/selectors/partner.selectors';
import { useNumberOfBestPracticesAvailableToAddV2 } from '../../../utils/criteriaManagementUtils/useNumberOfBestPracticesAvailableToAddV2';
import { ExportCSVModal } from '../CriteriaManagementModals/ExportCSVModal/ExportCSVModal';
import { trackFeatureUsageGainsight } from '../../../../utils/gainsight';
import { controlBarButtonSx } from '../../../../components/ReportFilters/styles';
import { DownloadFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import { useCriteriaGrouping } from '../CriteriaGroupingManagement/context/CriteriaGroupingContext';
import BrandIdentifierModal from '../CriteriaManagementModals/SetBrandIdentifiersModal/BrandIdentifierModal';
import BestPracticesModal from '../CriteriaManagementModals/BestPracticesModal';

const headerSx = {
  px: 0,
};

const buttonSx = {
  ...controlBarButtonSx,
  p: '8px 16px',
  height: '36px',
};

const downloadButtonSx = {
  ...buttonSx,
  p: '8px',
  minWidth: '36px',
  maxWidth: '36px',
};

const downloadIconSx = {
  width: '20px',
  height: '20px',
};

const badgeSx = {
  '& .MuiBadge-badge': {
    marginRight: '8px',
  },
};

interface ComponentProps {
  hasNoData: boolean;
  currentModal: string | null;
  setCurrentModal: (modal: string | null) => void;
  brandIdentifiers: string | '';
}

const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');

const CriteriaManagementHeader: React.FC<ComponentProps> = ({
  hasNoData,
  currentModal,
  setCurrentModal,
  brandIdentifiers,
}) => {
  const intl = useIntl();
  const history = useHistory();
  const workspaceId = useSelector(getCurrentPartnerId);
  const canUserUpdateCriteria = useSelector(getCanUserUpdateCriteria);
  const isBrandIdentifiersLoading = useSelector(getIsBrandIdentifiersLoading);
  const isPageLoading = useSelector(getIsLoading);
  const isCriteriaTemplatesLoading = useSelector(getIsCriteriaTemplatesLoading);

  const { data: bestPracticesDataV2, isLoading: isBestPracticesLoadingV2 } =
    useGetCurrentBestPractices({
      workspaceId,
    });

  const { toggleCriteriaGroupingDrawer } = useCriteriaGrouping();
  const badgeNumberV2 = useNumberOfBestPracticesAvailableToAddV2(
    bestPracticesDataV2 || {},
  );

  useEffect(() => {
    trackFeatureUsageGainsight('Export Criteria', {
      Context: 'Criteria Management with Export Criteria Button',
    });
    trackFeatureUsageGainsight('Best Practice Versioning', {
      Context: 'Criteria Management with Best Practice Versioning Button',
    });
  }, []);

  const DownloadCSVButton = () => (
    <VidMobTooltip
      title={intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.button.download.csv',
        defaultMessage: 'Download as CSV',
      })}
      placement="bottom"
    >
      <VidMobButton
        sx={downloadButtonSx}
        disabled={isPageLoading}
        onClick={() =>
          setCurrentModal(CRITERIA_MANAGEMENT_MODAL_TYPES.EXPORT_CSV_MODAL)
        }
      >
        <DownloadFilledIcon sx={downloadIconSx} />
      </VidMobButton>
    </VidMobTooltip>
  );

  const SetBrandIdentifierButton = () => (
    <VidMobButton
      sx={buttonSx}
      disabled={isBrandIdentifiersLoading}
      onClick={() =>
        setCurrentModal(CRITERIA_MANAGEMENT_MODAL_TYPES.SET_BRAND_IDENTIFIER)
      }
    >
      {intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.button.setBrandIdentifier',
        defaultMessage: 'Set brand identifier',
      })}
    </VidMobButton>
  );

  const BestPracticesButton = () => {
    const buttonLabel = intl.formatMessage({
      id: 'ui.creativeScoring.criteriaManagementV2.pageHeader.button.viewBestPractices',
      defaultMessage: 'View best practices',
    });

    return (
      <VidMobBadge color="primary" badgeContent={badgeNumberV2} sx={badgeSx}>
        <VidMobButton
          sx={buttonSx}
          disabled={isPageLoading || isBestPracticesLoadingV2}
          onClick={() =>
            setCurrentModal(CRITERIA_MANAGEMENT_MODAL_TYPES.BEST_PRACTICES)
          }
        >
          {buttonLabel}
        </VidMobButton>
      </VidMobBadge>
    );
  };

  const CriteriaGroupButton = () => (
    <VidMobButton
      sx={buttonSx}
      disabled={isPageLoading}
      onClick={toggleCriteriaGroupingDrawer}
    >
      {intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.button.viewCriteriaGroups',
        defaultMessage: 'View criteria groups',
      })}
    </VidMobButton>
  );

  const AddCriteriaButton = () => (
    <VidMobButton
      variant="contained"
      color="primary"
      disabled={isPageLoading || isCriteriaTemplatesLoading}
      onClick={() =>
        history.push('/creativeIntelligence/creative-scoring/criteria-create')
      }
    >
      {intl.formatMessage({
        id: 'ui.creativeScoring.criteriaManagementV2.pageHeader.button.addCriteria',
        defaultMessage: 'Add criteria',
      })}
    </VidMobButton>
  );

  return (
    <>
      <PageHeaderV2
        title={intl.formatMessage({
          id: 'sideNav.criteria',
          defaultMessage: 'Criteria',
        })}
        additionalBoxStyles={headerSx}
      >
        <DownloadCSVButton />
        {!hasNoData && canUserUpdateCriteria && (
          <>
            <SetBrandIdentifierButton />
            <BestPracticesButton />
            {isCriteriaGroupsEnabled && <CriteriaGroupButton />}
            <AddCriteriaButton />
          </>
        )}
      </PageHeaderV2>
      <BrandIdentifierModal
        brandIdentifiers={brandIdentifiers}
        isOpen={
          currentModal === CRITERIA_MANAGEMENT_MODAL_TYPES.SET_BRAND_IDENTIFIER
        }
        setCurrentModal={setCurrentModal}
      />
      {canUserUpdateCriteria && (
        <BestPracticesModal
          isOpen={
            currentModal === CRITERIA_MANAGEMENT_MODAL_TYPES.BEST_PRACTICES
          }
          setCurrentModal={setCurrentModal}
        />
      )}
      <ExportCSVModal
        isOpen={
          currentModal === CRITERIA_MANAGEMENT_MODAL_TYPES.EXPORT_CSV_MODAL
        }
        setCurrentModal={setCurrentModal}
      />
    </>
  );
};

export default CriteriaManagementHeader;
