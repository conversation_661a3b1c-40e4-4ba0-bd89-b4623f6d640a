import React from 'react';
import { fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import CriteriaManagementHeader from './CriteriaManagementHeader';
import { testRenderWithQueryClient } from '../../../utils/testUtils';
import store from '../../../../redux/store';
import { CRITERIA_MANAGEMENT_MODAL_TYPES } from '../../../constants/criteriaManagement.constants';
import * as reduxHooks from 'react-redux';
import {
  getCanUserUpdateCriteria,
  getCriteriaSorting,
} from '../../../redux/selectors/criteriaManagement.selectors';
import { navPaths } from '../../../../components/__views/SideNav/navPathsConstants';
import { CriteriaGroupingProvider } from '../CriteriaGroupingManagement/context/CriteriaGroupingProvider';

jest.mock('../../../../utils/gainsight');

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
}));

jest.mock('../../../query-hooks/useGetCurrentBestPractices', () => ({
  useGetCurrentBestPractices: jest.fn(() => ({
    data: {},
    isLoading: false,
    isError: false,
  })),
}));

jest.mock('../../../redux/selectors/criteriaManagement.selectors', () => ({
  getBestPracticesData: jest.fn(() => []),
  getIsBrandIdentifiersLoading: jest.fn(() => false),
  getIsLoading: jest.fn(() => false),
  getIsCriteriaTemplatesLoading: jest.fn(() => false),
  getCanUserUpdateCriteria: jest.fn(() => true),
  getCriteriaFilters: jest.fn(() => []),
  getCriteriaSearchText: jest.fn(() => ''),
  getCriteriaSorting: jest.fn(() => {
    return {
      sortOrder: 'ASC',
      sortBy: 'name',
    };
  }),
}));

jest.mock('../../../../redux/selectors/user.selectors.js', () => ({
  getUserWorkspacesInCurrentOrganization: jest.fn(() => []),
}));

jest.mock;

describe.skip('Criteria Management Header - for Scoring Manager', () => {
  // deprecated
  beforeEach(() => {
    jest.spyOn(reduxHooks, 'useSelector').mockImplementation((selector) => {
      if (selector === getCanUserUpdateCriteria) {
        return true;
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the Set Brand Identifier button which opens modal to edit brands', () => {
    const setCurrentModal = jest.fn();
    testRenderWithQueryClient(
      <CriteriaManagementHeader
        hasNoData={false}
        currentModal={null}
        setCurrentModal={setCurrentModal}
        brandIdentifiers={[]}
      />,
      { store },
    );

    expect(screen.getByText('Set brand identifier')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Set brand identifier'));
    expect(setCurrentModal).toHaveBeenCalledWith(
      CRITERIA_MANAGEMENT_MODAL_TYPES.SET_BRAND_IDENTIFIER,
    );
  });

  it('renders the Add Criteria button which opens modal to add criteria', () => {
    const setCurrentModal = jest.fn();
    const push = jest.fn();

    const useHistoryMock = jest
      .spyOn(require('react-router-dom'), 'useHistory')
      .mockReturnValue({ push });

    testRenderWithQueryClient(
      <CriteriaManagementHeader
        hasNoData={false}
        currentModal={null}
        setCurrentModal={setCurrentModal}
        brandIdentifiers={[]}
      />,
      { store },
    );

    expect(screen.getByText('Add criteria')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Add criteria'));
    expect(push).toHaveBeenCalledWith(navPaths.scoringCriteriaCreate);
    useHistoryMock.mockRestore();
  });
});

describe('Criteria Management Header - for Standard user', () => {
  beforeEach(() => {
    jest.spyOn(reduxHooks, 'useSelector').mockImplementation((selector) => {
      if (selector === getCanUserUpdateCriteria) {
        return false;
      }
      if (selector === getCriteriaSorting) {
        return {
          sortOrder: 'ASC',
        };
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  it('does not render any buttons in the header', () => {
    const { container } = testRenderWithQueryClient(
      <CriteriaGroupingProvider>
        <CriteriaManagementHeader
          hasNoData={false}
          currentModal={null}
          setCurrentModal={jest.fn()}
          brandIdentifiers={[]}
        />
      </CriteriaGroupingProvider>,
      { store },
    );
    expect(container.getElementsByTagName('button').length).toBe(1);
  });
});
