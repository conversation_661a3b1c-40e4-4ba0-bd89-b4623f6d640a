import React from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import GenericBlankState from '../../../../muiCustomComponents/GenericBlankState';
import {
  InfoFilledIcon,
  SearchFilledIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import { Stack, Box, Button } from '@mui/material';
import {
  getCriteriaSearchText,
  getCanUserUpdateCriteria,
} from '../../../redux/selectors/criteriaManagement.selectors';
import {
  CRITERIA_MANAGEMENT_MODAL_TYPES,
  CRITERIA_MANAGEMENT_USERS,
} from '../../../constants/criteriaManagement.constants';

const { ADMIN, STANDARD } = CRITERIA_MANAGEMENT_USERS;

interface ComponentProps {
  hasFailed: boolean;
  hasNoData: boolean;
  hasNoFilterResults: boolean;
  hasNoSearchResults: boolean;
  setCurrentModal: (modal: string | null) => void;
}

const CriteriaManagementStates: React.FC<ComponentProps> = ({
  hasFailed,
  hasNoData,
  hasNoFilterResults,
  hasNoSearchResults,
  setCurrentModal,
}) => {
  const intl = useIntl();

  const searchText = useSelector(getCriteriaSearchText);
  const canUserUpdateCriteria = useSelector(getCanUserUpdateCriteria);
  const permissions = canUserUpdateCriteria ? ADMIN : STANDARD;

  const renderBlankState = () => {
    if (hasNoFilterResults) {
      return (
        <Box sx={{ height: 'calc(100vh - 170px)' }}>
          <GenericBlankState
            title={
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.blank.noFilterResults.title'
              ]
            }
            subtitle={
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.blank.noFilterResults.body'
              ]
            }
            icon={<SearchFilledIcon />}
          />
        </Box>
      );
    }

    if (hasNoSearchResults) {
      return (
        <Box sx={{ height: 'calc(100vh - 170px)' }}>
          <GenericBlankState
            title={
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.blank.noSearchResults.title'
              ]
            }
            subtitle={intl.formatMessage(
              {
                id: 'ui.creativeScoring.criteriaManagementV2.blank.noSearchResults.body',
              },
              { search: searchText },
            )}
            icon={<SearchFilledIcon />}
          />
        </Box>
      );
    }

    if (hasNoData) {
      return (
        <Box sx={{ height: 'calc(100vh - 170px)' }}>
          <GenericBlankState
            title={
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.blank.title'
              ]
            }
            subtitle={
              intl.messages[
                `ui.creativeScoring.criteriaManagementV2.blank.${permissions}.body`
              ]
            }
            icon={<InfoFilledIcon />}
          >
            {canUserUpdateCriteria && (
              <Stack
                flexDirection="row"
                justifyContent="center"
                alignItems="center"
                sx={{ gap: '8px' }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  sx={{
                    height: '36px',
                    width: '105px',
                    fontWeight: 600,
                    fontSize: '14px',
                    lineHeight: '20px',
                  }}
                  onClick={() =>
                    setCurrentModal(
                      CRITERIA_MANAGEMENT_MODAL_TYPES.ADD_CRITERIA,
                    )
                  }
                >
                  {intl.formatMessage({
                    id: 'ui.creativeScoring.criteriaManagementV2.button.addCriteria',
                    defaultMessage: 'Add criteria',
                  })}
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  sx={{
                    height: '36px',
                    width: '151px',
                    fontWeight: 600,
                    fontSize: '14px',
                    lineHeight: '20px',
                  }}
                  onClick={() =>
                    setCurrentModal(
                      CRITERIA_MANAGEMENT_MODAL_TYPES.BEST_PRACTICES,
                    )
                  }
                >
                  {intl.formatMessage({
                    id: 'ui.creativeScoring.criteriaManagementV2.button.viewBestPractices',
                    defaultMessage: 'View best practices',
                  })}
                </Button>
              </Stack>
            )}
          </GenericBlankState>
        </Box>
      );
    }

    if (hasFailed) {
      return (
        <Box sx={{ height: 'calc(100vh - 170px)' }}>
          <GenericBlankState
            title={
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.error.title'
              ]
            }
            subtitle={
              intl.messages[
                'ui.creativeScoring.criteriaManagementV2.error.body'
              ]
            }
            icon={<InfoFilledIcon />}
          />
        </Box>
      );
    }

    return null;
  };

  return <>{renderBlankState()}</>;
};

export default CriteriaManagementStates;
