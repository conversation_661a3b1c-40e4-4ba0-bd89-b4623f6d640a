import React from 'react';
import { render, screen } from '@testing-library/react';
import CriteriaManagementStates from './CriteriaManagementStates'; // Adjust the import based on the actual path
import '@testing-library/jest-dom';
import * as reduxHooks from 'react-redux';
import { getCanUserUpdateCriteria } from '../../../redux/selectors/criteriaManagement.selectors';

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
}));

describe('CriteriaManagementStates', () => {
  const mockSetCurrentModal = jest.fn();

  beforeEach(() => {
    jest.spyOn(reduxHooks, 'useSelector').mockImplementation((selector) => {
      if (selector === getCanUserUpdateCriteria) {
        return true;
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders no filter results state', () => {
    render(
      <CriteriaManagementStates
        hasNoFilterResults={true}
        setCurrentModal={mockSetCurrentModal}
      />,
    );
    expect(
      screen.getByText('Please try adjusting your filter selection'),
    ).toBeInTheDocument();
  });

  it('renders no search results state', () => {
    render(
      <CriteriaManagementStates
        hasNoSearchResults={true}
        setCurrentModal={mockSetCurrentModal}
      />,
    );
    expect(screen.getByText('No matching results')).toBeInTheDocument();
  });

  it.skip('renders no data state for admin', () => {
    const { container } = render(
      <CriteriaManagementStates
        hasNoData={true}
        setCurrentModal={mockSetCurrentModal}
      />,
    );
    expect(screen.getByText('No criteria has been added')).toBeInTheDocument();

    expect(container.getElementsByTagName('button').length).toBe(2);
    expect(screen.getByText('Add criteria')).toBeInTheDocument();
    expect(screen.getByText('Add best practices')).toBeInTheDocument();
  });

  it('renders no data state for standard user', () => {
    // Adjust the mock to return false for canUserUpdateCriteria
    jest.spyOn(reduxHooks, 'useSelector').mockImplementation((selector) => {
      if (selector === getCanUserUpdateCriteria) {
        return false;
      }
    });

    const { container } = render(
      <CriteriaManagementStates
        hasNoData={true}
        setCurrentModal={mockSetCurrentModal}
      />,
    );
    expect(screen.getByText('No criteria has been added')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Once an admin in your organization has added criteria, it will show up here.',
      ),
    ).toBeInTheDocument();

    expect(container.getElementsByTagName('button').length).toBe(0);
  });

  it('renders error state', () => {
    render(
      <CriteriaManagementStates
        hasFailed={true}
        setCurrentModal={mockSetCurrentModal}
      />,
    );
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });
});
