import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ScoringLandingPageControlBar from '../../ScoringLandingFilters/ScoringLandingControlBar';
import {
  getScorecardsLoadingStatus,
  getScorecardsPagination,
} from '../../../redux/selectors/complianceBatches.selectors';
import { getFiltersByScorecardType } from './scorecardsLandingToolbar.utils';
import { useScorecardsListItems } from '../../ScoringLandingFilters/ScoringLandingFilters/ListItemsHooks/useScorecardsListItems';
import {
  FilterSelectedValueType,
  LandingPageContext,
  LandingPageFilter,
} from '../../ScoringLandingFilters/scoringLandingFilters.types';
import filterPanelV2Slice from '../../../redux/slices/filterPanelV2.slice';
import { fetchScorecards } from '../../../redux/actions/batches.actions';
import { getReportsFilterParams } from '../../../redux/selectors/filterPanelV2.selectors';
import { VidMobBox, VidMobStack } from '../../../../vidMobComponentWrappers';
import CreateScorecardButton from './CreateScorecardButton';
import { COMPLIANCE, GLOBALS } from '../../../../constants';
import { ReportsSearchFieldFilter } from '../../reportsManagement/ReportsManagementToolbarV2Filters';
import { CriteriaGroupingProvider } from '../../criteriaManagement/CriteriaGroupingManagement/context/CriteriaGroupingProvider';
const { setScorecardsLandingFilter } = filterPanelV2Slice.actions;
const { SCORECARD_TYPES } = COMPLIANCE;
const { REDUX_LOADING_STATUS } = GLOBALS;
const { NOT_LOADED, PENDING } = REDUX_LOADING_STATUS;

type Props = {
  scorecardType: string;
};

const ScorecardsLandingToolbar = (props: Props) => {
  const { scorecardType } = props;
  const dispatch = useDispatch();

  const prevScorecardsTypeRef = useRef<typeof scorecardType | null>(null);
  const [currentScorecardType, setCurrentScorecardType] = useState<
    string | null
  >(scorecardType);

  const [filters, setFilters] = useState<LandingPageFilter[] | []>([]);

  const savedFilters = useSelector(getReportsFilterParams);
  const { totalSize } = useSelector(getScorecardsPagination) || {};
  const loadingStatus = useSelector(getScorecardsLoadingStatus);
  const listItems = useScorecardsListItems({
    scorecardType,
    selectedAdAccounts: savedFilters.platformAdAccounts,
  });
  const isPreflight = scorecardType === SCORECARD_TYPES.PRE_FLIGHT;
  const isLoading = [NOT_LOADED, PENDING].includes(loadingStatus);

  useEffect(() => {
    if (prevScorecardsTypeRef.current !== scorecardType) {
      setCurrentScorecardType(scorecardType);
    }
    prevScorecardsTypeRef.current = scorecardType;

    return () => {
      setCurrentScorecardType(null);
      prevScorecardsTypeRef.current = null;
    };
  }, [scorecardType]);

  useEffect(() => {
    setFilters(getFiltersByScorecardType(scorecardType));
  }, [scorecardType]);

  const handleDispatchFilterChange = ({
    filterId,
    value,
  }: {
    filterId: string;
    value: FilterSelectedValueType;
  }) => {
    if (currentScorecardType === scorecardType) {
      // only dispatch action if haven't switched tabs
      dispatch(
        setScorecardsLandingFilter({
          filter: { field: filterId, value },
        }),
      );
    }
    dispatch(fetchScorecards());
  };

  return (
    <CriteriaGroupingProvider>
      <VidMobStack
        direction="row"
        justifyContent="space-between"
        sx={{ mb: '16px' }}
      >
        <ScoringLandingPageControlBar
          filters={filters}
          isLoading={isLoading}
          totalSize={totalSize}
          itemsIntlKey="ui.creativeScoring.landingPages.numberAndTypeOfListItems.scorecards"
          filtersListItems={listItems}
          handleDispatchFilterChange={handleDispatchFilterChange}
          savedFilterParams={savedFilters}
          searchComponent={<ReportsSearchFieldFilter tab={scorecardType} />}
          context={scorecardType as LandingPageContext}
          criteriaList={[]}
        />
        <VidMobBox sx={{ ml: '16px' }}>
          {isPreflight && <CreateScorecardButton />}
        </VidMobBox>
      </VidMobStack>
    </CriteriaGroupingProvider>
  );
};

export default ScorecardsLandingToolbar;
