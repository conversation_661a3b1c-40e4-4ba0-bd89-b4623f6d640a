import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';
import { updateScorecard } from '../../../redux/actions/uploads.actions';
import {
  brandsSelector,
  countriesSelector,
} from '../../../../userManagement/redux/selectors/workspaces.selectors';
import { COMPLIANCE } from '../../../../constants';
import { ListItem } from '../../../../components/ReportFilters/types';
import {
  ChannelObjectiveMap,
  FlattenedObjectivesObject,
  Channel,
} from '../../__subpages/CreativeScoringPreFlightScorecard/types';
import { useObjectivesDropdown } from '../../__subpages/CreativeScoringPreFlightScorecard/hooks/useObjectivesDropdown';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { getPlatformDisplayIntlText } from '../../../../utils/feConstantsUtils';
import { useCriteriaGroups } from '../../__subpages/CreativeScoringPreFlightScorecard/hooks/useCriteriaGroups';
import { IdAndName } from '../../../../types/common.types';
import { Market } from '../../../../types/market.types';
import { Brand } from '../../../../types/brand.types';
import { Country } from '../../../../userManagement/redux/slices/workspaces.slice';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { CreateOrEditPreFlightCheck } from '../../__subpages/CreativeScoringPreFlightScorecard/CreateOrEditPreFlightCheck/CreateOrEditPreFlightCheck';
import { CriterionServerType } from '../../../types/criteriaManagement.types';
import { formatObjectivesForApi } from './utils';

const dialogSxProps = {
  explicitDialogWidth: '1170px',
  explicitDialogHeight: '800px',
  overflow: 'hidden',
  customContentStyles: {
    p: '24px',
  },
};

const iconSx = {
  width: '18px',
  height: '18px',
};

const chipChannelIconSx = {
  ...iconSx,
  mr: '6px',
};

export interface Props {
  isOpen: boolean;
  onClose: () => void;
  scorecard: Scorecard;
  isDetailsPage: boolean;
}

interface Scorecard {
  id: number | string;
  name: string;
  platforms: IdAndName[];
  criteriaGroupIds: string[];
  markets: Market[];
  brands: Brand[];
  batchType: string;
  objectives: string[];
}

const isCriteriaGroupsInPreFlightCreationEnabled = getFeatureFlag(
  'isCriteriaGroupsInPreFlightCreationEnabled',
);

const PreFlightCheckEditModal: React.FC<Props> = ({
  isOpen,
  onClose,
  scorecard,
  isDetailsPage,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const brandsApiResult = useSelector(brandsSelector);
  const marketsApiResult = useSelector(countriesSelector);

  const [checkName, setCheckName] = useState<string>('');
  const [selectedChannels, setSelectedChannels] = useState<ListItem[]>([]);
  const [selectedCriteriaGroups, setSelectedCriteriaGroups] = useState<
    ListItem[]
  >([]);
  const [selectedBrands, setSelectedBrands] = useState<ListItem[]>([]);
  const [selectedMarkets, setSelectedMarkets] = useState<ListItem[]>([]);
  const [selectedObjectivesPerChannel, setSelectedObjectivesPerChannel] =
    useState<Record<string, FlattenedObjectivesObject[]>>({});
  const [channelsToObjectives, setChannelsToObjectives] =
    useState<ChannelObjectiveMap>({});
  const [scorecardObjectives, setScorecardObjectives] = useState<string[]>([]);
  const [includedCriteria, setIncludedCriteria] = useState<
    CriterionServerType[]
  >([]);

  const trimmedCheckName = useMemo(() => checkName.trim(), [checkName]);

  useObjectivesDropdown(
    selectedChannels as Channel[],
    channelsToObjectives,
    setChannelsToObjectives,
    setSelectedObjectivesPerChannel,
    selectedObjectivesPerChannel,
    scorecardObjectives,
  );

  const channelOptions = useMemo(
    () =>
      COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.map((platformObject) => ({
        id: platformObject.id,
        name:
          getPlatformDisplayIntlText(platformObject.id, intl) ||
          platformObject.id,
        icon: getMUIIconForChannel(platformObject.id, chipChannelIconSx, true),
      })),
    [],
  );

  const {
    data: criteriaGroupsData,
    isLoading: areCriteriaGroupOptionsLoading,
  } = useCriteriaGroups();

  const criteriaGroupOptions = useMemo(
    () => criteriaGroupsData?.data,
    [criteriaGroupsData],
  ) as ListItem[] | undefined;
  const brandOptions = brandsApiResult?.data;
  const areBrandOptionsLoading = brandsApiResult?.isFetching;
  const marketOptions = marketsApiResult?.data?.map((market: Country) => ({
    id: market.isoCode,
    name: market.name,
  }));
  const areMarketOptionsLoading = marketsApiResult?.isFetching;
  const areAnyOptionsLoading =
    areBrandOptionsLoading ||
    areMarketOptionsLoading ||
    areCriteriaGroupOptionsLoading;

  useEffect(() => {
    if (scorecard && isOpen) {
      const { name, platforms, markets, brands } = scorecard;

      setCheckName(name);
      setSelectedChannels(
        platforms?.map((platform) => ({
          id: platform.id,
          name: getPlatformDisplayIntlText(platform.id, intl) || platform.id,
          icon: getMUIIconForChannel(platform.id, chipChannelIconSx, true),
        })),
      );
      setSelectedBrands(brands || []);
      setSelectedMarkets(markets || []);
      setChannelsToObjectives({});
      setScorecardObjectives(scorecard?.objectives || []);
    }
  }, [scorecard, isOpen]);

  useEffect(() => {
    if (
      scorecard?.criteriaGroupIds?.length &&
      isOpen &&
      criteriaGroupOptions?.length
    ) {
      const { criteriaGroupIds } = scorecard;
      const scorecardCriteriaGroups = criteriaGroupOptions.filter(
        (criteriaGroup) =>
          criteriaGroupIds.includes(criteriaGroup.id as string),
      );
      setSelectedCriteriaGroups(scorecardCriteriaGroups);
    } else {
      setSelectedCriteriaGroups([]);
    }
  }, [
    scorecard?.criteriaGroupIds?.length,
    isOpen,
    criteriaGroupOptions?.length,
  ]);

  const isSaveButtonEnabled =
    scorecard &&
    !areAnyOptionsLoading &&
    trimmedCheckName &&
    selectedChannels.length;

  const handleSave = () => {
    if (!isSaveButtonEnabled) {
      return;
    }

    const payload = {
      scorecardId: scorecard.id,
      name: trimmedCheckName,
      platforms: selectedChannels,
      markets: selectedMarkets,
      brands: selectedBrands.map((brand) => brand.id),
      isDetailsPage,
      objectives: formatObjectivesForApi(selectedObjectivesPerChannel),
      ...(isCriteriaGroupsInPreFlightCreationEnabled && {
        criteriaGroupIds: selectedCriteriaGroups.map((group) => group.id),
      }),
    };

    dispatch(updateScorecard(payload));

    onClose();
  };

  return (
    <CustomDialog
      headerText={intl.formatMessage({
        id: 'ui.compliance.scorecards.details.action.editDetails.preflight.modal.header',
        defaultMessage: 'Edit Pre-flight Check details',
      })}
      submitButtonLabel={intl.formatMessage({
        id: 'ui.compliance.scorecards.details.action.editDetails.modal.save',
        defaultMessage: 'Save',
      })}
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={handleSave}
      isSubmitButtonDisabled={!isSaveButtonEnabled}
      bodyChildren={
        <CreateOrEditPreFlightCheck
          checkName={checkName}
          setCheckName={setCheckName}
          channelOptions={channelOptions}
          selectedChannels={selectedChannels}
          setSelectedChannels={setSelectedChannels}
          criteriaGroupOptions={criteriaGroupOptions}
          areCriteriaGroupOptionsLoading={areCriteriaGroupOptionsLoading}
          selectedCriteriaGroups={selectedCriteriaGroups}
          setSelectedCriteriaGroups={setSelectedCriteriaGroups}
          brandOptions={brandOptions}
          areBrandOptionsLoading={areBrandOptionsLoading}
          selectedBrands={selectedBrands}
          setSelectedBrands={setSelectedBrands}
          marketOptions={marketOptions}
          areMarketOptionsLoading={areMarketOptionsLoading}
          selectedMarkets={selectedMarkets}
          setSelectedMarkets={setSelectedMarkets}
          channelsToObjectives={channelsToObjectives}
          selectedObjectivesPerChannel={selectedObjectivesPerChannel}
          setSelectedObjectivesPerChannel={setSelectedObjectivesPerChannel}
          includedCriteria={includedCriteria}
          setIncludedCriteria={setIncludedCriteria}
          isPreFlightEditModal
        />
      }
      {...dialogSxProps}
    />
  );
};

export default PreFlightCheckEditModal;
