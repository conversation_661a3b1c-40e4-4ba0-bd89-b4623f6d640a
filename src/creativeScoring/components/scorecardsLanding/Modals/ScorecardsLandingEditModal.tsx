// @ts-nocheck
import React, { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';
import { Box, TextField, Typography } from '@mui/material';
import { updateScorecard } from '../../../redux/actions/uploads.actions';
import {
  brandsSelector,
  countriesSelector,
} from '../../../../userManagement/redux/selectors/workspaces.selectors';
import { COMPLIANCE } from '../../../../constants';
import MultiSelectDropdown from '../../../../muiCustomComponents/MultiSelectDropdown';
import { ListItem } from '../../../../muiCustomComponents/MultiSelectDropdown/MultiSelectDropdown';
import {
  ChannelObjectiveMap,
  FlattenedObjectivesObject,
  Channel,
  ChannelKey,
} from '../../__subpages/CreativeScoringPreFlightScorecard/types';
import ObjectivesDropdown from '../../__subpages/CreativeScoringPreFlightScorecard/CreativeScoringPreFlightScorecardDetails/ObjectivesDropdown';
import { useObjectivesDropdown } from '../../__subpages/CreativeScoringPreFlightScorecard/hooks/useObjectivesDropdown';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { Props } from './PreFlightCheckEditModal';

const { SCORECARD_TYPES } = COMPLIANCE;

interface Market {
  name: string;
  isoCode: string;
}

interface SelectedItem {
  id: string;
  name: string;
}

const ScorecardsLandingEditModal: React.FC<Props> = ({
  isOpen,
  onClose,
  scorecard,
  isDetailsPage,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const [scorecardName, setScorecardName] = useState<string>(scorecard?.name);
  const [selectedPlatforms, setSelectedPlatforms] = useState(
    scorecard?.platforms,
  );

  const [selectedMarkets, setSelectedMarkets] = useState(scorecard?.markets);
  const [selectedBrandIds, setSelectedBrandIds] = useState(
    scorecard?.brands?.map((brand) => brand.id) ?? [],
  );
  const [isInflight, setIsInflight] = useState<boolean>(
    scorecard?.batchType === SCORECARD_TYPES.IN_FLIGHT,
  );

  const [selectedObjectivesPerChannel, setSelectedObjectivesPerChannel] =
    useState<{ [key: string]: FlattenedObjectivesObject[] }>({});

  const [transformedSelectedPlatforms, setTransformedSelectedPlatforms] =
    useState<Channel[]>([]);

  const [isPreFlight, setIsPreFlight] = useState<boolean>(false);
  const [channelsToObjectives, setChannelsToObjectives] =
    useState<ChannelObjectiveMap>({});

  const [scoreCardObjectives, setScoreCardObjectives] = useState<string[]>([]);

  const trimmedScorecardName = useMemo(
    () => scorecardName?.trim(),
    [scorecardName],
  );

  useObjectivesDropdown(
    transformedSelectedPlatforms,
    channelsToObjectives,
    setChannelsToObjectives,
    setSelectedObjectivesPerChannel,
    selectedObjectivesPerChannel,
    scoreCardObjectives,
  );

  const modalHeaderLabel = isInflight
    ? 'ui.compliance.scorecards.details.action.editDetails.inflight.modal.header'
    : 'ui.compliance.scorecards.details.action.editDetails.preflight.modal.header';

  useEffect(() => {
    if (scorecard && isOpen) {
      const { name, platforms, markets, brands, batchType } = scorecard || {};
      setScorecardName(name);
      setSelectedPlatforms(platforms);
      setSelectedMarkets(markets);
      setSelectedBrandIds(brands?.map((brand) => brand.id));
      setIsInflight(batchType === SCORECARD_TYPES.IN_FLIGHT);
      setIsPreFlight(batchType === SCORECARD_TYPES.PRE_FLIGHT);
      setChannelsToObjectives({});
      setScoreCardObjectives(scorecard?.objectives || []);
    }
  }, [scorecard, isOpen]);

  useEffect(() => {
    let transformedPlatforms: Channel[] = [];

    if (selectedPlatforms && selectedPlatforms.length > 0) {
      transformedPlatforms = selectedPlatforms?.map((platform) => {
        const id = Object.values(platform)[1]
          ? (Object.values(platform)[1] as ChannelKey)
          : (Object.values(platform)[0] as ChannelKey);
        return {
          id: id,
          name: String(
            availablePlatforms.find(
              (availablePlatform) => availablePlatform.id === id,
            )?.label,
          ),
        };
      });
    }
    setTransformedSelectedPlatforms(transformedPlatforms);
  }, [selectedPlatforms]);

  const selectedMarketIds = (selectedMarkets as unknown as SelectedItem[])?.map(
    (market) => market.id,
  );

  const availableMarkets = useSelector(countriesSelector)?.data?.map(
    (market: Market) => ({
      label: market.name,
      id: market.isoCode,
      defaultSelected: selectedMarketIds?.includes(market.isoCode),
    }),
  );

  const availableBrands =
    useSelector(brandsSelector)?.data?.map((brand) => ({
      label: brand.name,
      id: brand.id,
      defaultSelected: selectedBrandIds?.includes(brand.id),
    })) ?? [];

  const selectedPlatformIds = (
    selectedPlatforms as unknown as SelectedItem[]
  )?.map((channel) => channel.id);

  const availablePlatforms = COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.map(
    (platform) => ({
      label: intl.messages[platform.i18nName],
      id: platform.id,
      icon: getMUIIconForChannel(
        platform.id,
        { width: '20px', height: '20px' },
        true,
      ),
      defaultSelected: selectedPlatformIds?.includes(platform.id),
    }),
  );

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setScorecardName(event.target.value);
  };

  const formatObjectivesForApi = () => {
    return Object.values(selectedObjectivesPerChannel).flatMap((objectives) =>
      objectives.map((objective) => String(objective.id)),
    );
  };

  const isSaveButtonEnabled =
    scorecard && trimmedScorecardName && selectedPlatforms.length;

  const handleSave = () => {
    const payload = {
      scorecardId: scorecard?.id,
      name: trimmedScorecardName,
      platforms: selectedPlatforms,
      markets: isInflight ? null : selectedMarkets,
      brands: isInflight ? [] : selectedBrandIds,
      isDetailsPage,
      ...(isPreFlight && { objectives: formatObjectivesForApi() }),
    };

    dispatch(updateScorecard(payload));
    onClose();
  };

  const dialogBodyChildren = (
    <Box sx={{ p: '8px 0' }}>
      <Typography variant="subtitle2" sx={{ mb: '8px' }}>
        {
          intl.messages[
            'ui.compliance.scorecards.details.action.editDetails.modal.name'
          ] as string
        }
      </Typography>
      <TextField
        hiddenLabel
        fullWidth
        value={scorecardName}
        size="small"
        onChange={handleNameChange}
        sx={{ '& .MuiInputBase-input': { fontSize: '14px' } }}
      />
      {!isInflight && (
        <>
          <Typography variant="subtitle2" sx={{ mt: '32px', mb: '8px' }}>
            {
              intl.messages[
                'ui.compliance.scorecards.details.action.editDetails.modal.market'
              ] as string
            }
          </Typography>
          <MultiSelectDropdown
            label={
              intl.messages[
                'ui.creativeScoring.rollUpReports.multiselectDropdown.label.noneSelected'
              ]
            }
            listItems={availableMarkets as ListItem[]}
            handleSelections={(selected) =>
              setSelectedMarkets(selected.map((id) => ({ id })))
            }
            modalWidth="550px"
          />
        </>
      )}

      {/* BRANDS */}
      {!isInflight && (
        <>
          <Typography variant="subtitle2" sx={{ mt: '32px', mb: '8px' }}>
            {
              intl.messages[
                'ui.compliance.scorecards.details.action.editDetails.modal.brand'
              ] as string
            }
          </Typography>
          <MultiSelectDropdown
            label={
              intl.messages[
                'ui.creativeScoring.rollUpReports.multiselectDropdown.label.noneSelected'
              ]
            }
            listItems={availableBrands as ListItem[]}
            handleSelections={setSelectedBrandIds}
            modalWidth="550px"
          />
        </>
      )}

      {/* CHANNELS */}
      {!isInflight && (
        <>
          <Typography variant="subtitle2" sx={{ mt: '32px', mb: '8px' }}>
            {
              intl.messages[
                'ui.compliance.scorecards.details.action.editDetails.modal.channel'
              ] as string
            }
          </Typography>
          <MultiSelectDropdown
            label={
              intl.messages[
                'ui.creativeScoring.rollUpReports.multiselectDropdown.label.noneSelected'
              ]
            }
            listItems={availablePlatforms as ListItem[]}
            handleSelections={(selected) => {
              setSelectedPlatforms(selected.map((id) => ({ id })));
            }}
            modalWidth="550px"
          />
        </>
      )}
      {isPreFlight && Object.values(channelsToObjectives).length > 0 && (
        <ObjectivesDropdown
          selectedPlatforms={transformedSelectedPlatforms}
          channelsToObjectives={channelsToObjectives}
          selectedObjectivesPerChannel={selectedObjectivesPerChannel}
          setSelectedObjectivesPerChannel={setSelectedObjectivesPerChannel}
          customStackDropdownStyles={{ paddingTop: '32px' }}
          customWidth={{ width: '550px' }}
        />
      )}
    </Box>
  );

  return (
    <CustomDialog
      headerText={intl.messages[modalHeaderLabel]}
      submitButtonLabel={
        intl.messages[
          'ui.compliance.scorecards.details.action.editDetails.modal.save'
        ]
      }
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={handleSave}
      isSubmitButtonDisabled={!isSaveButtonEnabled}
      bodyChildren={dialogBodyChildren}
      explicitDialogWidth="600px"
      overflow="visible"
    />
  );
};

export default ScorecardsLandingEditModal;
