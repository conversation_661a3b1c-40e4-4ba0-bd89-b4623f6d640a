import React from 'react';
import { useDispatch } from 'react-redux';
import { useIntl } from 'react-intl';
import { generatePath } from 'react-router';
import history from '../../../../routing/history';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';
import { deleteScorecard } from '../../../redux/actions/batches.actions';
import siteMapRoutes from '../../../../routing/siteMapRoutes';

export interface Props {
  isOpen: boolean;
  onClose: () => void;
  scorecard: {
    id: string | number;
    batchType: string;
  };
  isDetailsPage: boolean;
}

const ScorecardsLandingDeleteModal: React.FC<Props> = ({
  isOpen,
  onClose,
  scorecard,
  isDetailsPage = false,
}) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const handleDelete = () => {
    dispatch(deleteScorecard(scorecard.id));

    if (isDetailsPage) {
      redirectToLanding();
    }

    onClose();
  };

  const redirectToLanding = () => {
    const baseLandingPagePath = generatePath(
      siteMapRoutes.creativeScoringScoreCardLanding,
      { scorecardType: scorecard?.batchType },
    );

    const queryParams = new URLSearchParams();
    queryParams.set('types', scorecard?.batchType);

    history.push({
      pathname: baseLandingPagePath,
      search: queryParams.toString(),
    });
  };

  return (
    <CustomDialog
      isDelete
      headerText={
        intl.messages[
          'ui.compliance.scorecardsLanding.action.deleteScorecard.confirmationModal.header'
        ]
      }
      headerSubText={
        intl.messages[
          'ui.compliance.scorecardsLanding.action.deleteScorecard.confirmationModal.body'
        ]
      }
      submitButtonLabel={
        intl.messages[
          'ui.compliance.scorecardsLanding.action.deleteScorecard.confirmationModal.cancel.confirm'
        ]
      }
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={() => handleDelete()}
    />
  );
};

export default ScorecardsLandingDeleteModal;
