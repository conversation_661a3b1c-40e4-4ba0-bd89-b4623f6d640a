import React from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { COMPLIANCE } from '../../../../../constants';
import { object } from 'prop-types';
import ScorecardsLandingTableScoreCell from './ScorecardsLandingTableScoreCell';
import ScorecardsLandingTableNameCell from './ScorecardsLandingTableNameCell';
import ScorecardsLandingTableSourceCell from './ScorecardsLandingTableSourceCell';
import { getCountriesList } from '../../../../redux/selectors/complianceBatches.selectors';
import { BasicTextCell } from '../../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells';
import abbreviateNumber from '../../../../../muiCustomComponents/BarGraph/utils/abbreviateNumber';
import ChannelCell from '../../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells/ChannelCell.tsx';
import IconAndTextCell from '../../../shared/ScoringDataGrid/ScoringDataGridCells/IconAndTextCell';
import { getPlatformIdentifierForLogo } from '../../../../../utils/feConstantsUtils';
import getMUIIconForChannel from '../../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';

const { SCORECARDS_LANDING_COLUMN_KEYS } = COMPLIANCE;
const {
  SCORE,
  NAME,
  CREATED_BY,
  MARKET,
  AD_ACCOUNT,
  BRAND,
  NUMBER_OF_CREATIVES,
  CHANNEL,
  SOURCE,
} = SCORECARDS_LANDING_COLUMN_KEYS;

const ScorecardsLandingTableCell = ({ cellValues }) => {
  const intl = useIntl();
  const { value, field } = cellValues;

  const countryList = useSelector((state) => getCountriesList(state));
  const hasNoDataForCell = !value || value?.length === 0;

  if (hasNoDataForCell) {
    return <div>-</div>;
  }

  if (field === CHANNEL) {
    return (
      <>
        <ChannelCell params={{ value: value[0].id }} />
      </>
    );
  }

  if (field === NUMBER_OF_CREATIVES) {
    return abbreviateNumber(value);
  }

  const renderCellTextValue = () => {
    if (field === MARKET && value.length === countryList.length) {
      return intl.messages['ui.compliance.reports.allLocations']; // if all markets we show 'All Locations' rather than long string
    }

    if (field === MARKET || field === BRAND) {
      return value.map((val) => val.name).join(', ');
    }

    return value;
  };

  if (field === SCORE) {
    return <ScorecardsLandingTableScoreCell score={value} />;
  }

  if (field === SOURCE) {
    return <ScorecardsLandingTableSourceCell deviceIdentifier={value} />;
  }

  if (field === NAME) {
    return <ScorecardsLandingTableNameCell cellValues={cellValues} />;
  }

  if (field === AD_ACCOUNT) {
    const { adAccount, platforms } = cellValues.row;

    const platformIdentifierForLogo = getPlatformIdentifierForLogo(
      platforms[0]?.id,
    );

    const iconStyles = {
      height: '22px',
      width: '22px',
    };

    return (
      <IconAndTextCell
        displayText={adAccount}
        icon={getMUIIconForChannel(platformIdentifierForLogo, iconStyles)}
        sx={{ gap: '10px' }}
      />
    );
  }

  if (field === CREATED_BY || field === MARKET || field === BRAND) {
    return <BasicTextCell params={{ value: renderCellTextValue() }} />;
  }

  return <div>{value}</div>;
};

ScorecardsLandingTableCell.propTypes = {
  cellValues: object.isRequired,
};

export default ScorecardsLandingTableCell;
