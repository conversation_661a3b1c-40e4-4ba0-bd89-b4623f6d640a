import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';
import { MessageFormatElement, useIntl } from 'react-intl';
import { useTheme } from '@mui/material';
import PageHeaderV2 from '../../../../components/PageHeaderV2';
import ScoreOverrideDashboardLaunchButton from '../../__subpages/ScoreOverridesDashboard/ScoreOverrideDashboardLaunchButton';
import ScorecardsLandingHeaderDescription from './ScorecardsLandingHeaderDescription';
import SCORING_CONSTANTS from '../../../constants/creativeScoring.constants';
import complianceBatchesSlice from '../../../redux/slices/complianceBatches.slice';
import scoringFilterPanelV2Slice from '../../../redux/slices/filterPanelV2.slice';
import { getCurrentPartnerPermissions } from '../../../../redux/selectors/partner.selectors';
import MovedScorecardsNotificationBanner from './MovedScorecardsNotificationBanner';

const { REPORT_TYPE } = SCORING_CONSTANTS;
const { setScorecardLandingViewType } = complianceBatchesSlice.actions;
const { resetScorecardsLandingFilters } = scoringFilterPanelV2Slice.actions;

interface ScorecardsLandingHeaderProps {
  scorecardType: string;
  shouldShowNotificationBanner: boolean;
  onCloseNotificationBanner: () => void;
}

interface Tab {
  identifier: string;
  label: string | MessageFormatElement[];
  selected: boolean;
}

const ScorecardsLandingHeader: React.FC<ScorecardsLandingHeaderProps> = ({
  scorecardType,
  shouldShowNotificationBanner,
  onCloseNotificationBanner,
}) => {
  const intl = useIntl();
  const theme = useTheme();
  const history = useHistory();
  const location = useLocation();
  const dispatch = useDispatch();
  const currentPartnerPermissions = useSelector(getCurrentPartnerPermissions);
  const canUpdateScoreOverride =
    currentPartnerPermissions?.canUpdateScoreOverrideRequest();

  const preFlightTab: Tab = {
    label: intl.formatMessage({
      id: 'ui.pageHeader.scoring.tabs.preFlight',
      defaultMessage: 'Pre-flight Checks',
    }),
    identifier: REPORT_TYPE.PRE_FLIGHT,
    selected: true,
  };

  const creativeDraftsTab: Tab = {
    label: intl.formatMessage({
      id: 'ui.pageHeader.scoring.tabs.creativeDrafts',
      defaultMessage: 'Creative Drafts',
    }),
    identifier: REPORT_TYPE.PLUGIN_MEDIA,
    selected: false,
  };

  const tabsToDisplay = [preFlightTab, creativeDraftsTab];

  const [tabs, setTabs] = useState<Tab[]>(tabsToDisplay);

  const setSelectedTab = (tabIdentifier: string) => {
    const updatedTab = (tabs: Tab[]) =>
      tabs.map((tab) =>
        tab.identifier === tabIdentifier
          ? { ...tab, selected: true }
          : { ...tab, selected: false },
      );

    setTabs(updatedTab);
  };

  const initialTabSetup = () => {
    const type = new URLSearchParams(location.search).get('types');
    if (!type) {
      history.replace({
        pathName: location.pathName,
        search: `?types=${REPORT_TYPE.PRE_FLIGHT}`,
      });
    }

    if (type) {
      setSelectedTab(type);
      // @ts-ignore
      dispatch(setScorecardLandingViewType({ type }));
    }
  };

  useEffect(() => {
    initialTabSetup();
  }, [location?.search]);

  const handleTabChange = (selectedTabIdentifier: string | number) => {
    history.replace({
      pathname: location.pathname,
      search: `?types=${selectedTabIdentifier}`,
    });

    setSelectedTab(selectedTabIdentifier.toString());
    // @ts-ignore
    dispatch(setScorecardLandingViewType({ type: selectedTabIdentifier }));
    dispatch(resetScorecardsLandingFilters()); // reset filters on tab change
  };

  return (
    <>
      <PageHeaderV2
        title={intl.messages['sideNav.preFlight']}
        additionalBoxStyles={{
          paddingRight: 0,
          paddingLeft: 0,
          borderBottom: `1px solid ${theme.palette.divider}`,
          marginBottom: '25px',
        }}
        tabs={tabs}
        onTabChange={handleTabChange}
        {...(shouldShowNotificationBanner && {
          notificationBanner: (
            <MovedScorecardsNotificationBanner
              onClose={onCloseNotificationBanner}
            />
          ),
        })}
      >
        {canUpdateScoreOverride && <ScoreOverrideDashboardLaunchButton />}
      </PageHeaderV2>
      <ScorecardsLandingHeaderDescription scorecardType={scorecardType} />
    </>
  );
};

export default ScorecardsLandingHeader;
