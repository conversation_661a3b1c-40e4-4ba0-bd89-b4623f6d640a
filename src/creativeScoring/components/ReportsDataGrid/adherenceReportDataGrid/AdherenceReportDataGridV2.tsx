import React, { MutableRefObject, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { GridApiPro } from '@mui/x-data-grid-pro/models/gridApiPro';
import {
  GridColDef,
  GridColumnGroupingModel,
  GridRowsProp,
} from '@mui/x-data-grid-pro';
import { VidMobStack } from '../../../../vidMobComponentWrappers';
import NestedRowAndNestedGroupColumnTableV2 from '../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/NestedRowAndNestedGroupColumnTableV2';
import NestedRowAndGroupColumnTable from '../../../../muiCustomComponents/NestedRowAndGroupColumnTable/NestedRowAndGroupColumnTable';
import { AdherenceReportState } from './types';
import { PINNED_COLUMNS } from './constants/adherenceReportConstants';
import { RollUpReportBlankStates } from '../../reports/rollUpReport/RollUpReportStates';
import { getReportTitle } from './utils/adherenceReportUtils';
import { createGroupByObject } from '../../reports/rollUpReport/helpers';
import {
  RollUpReportMetadata,
  ScoringReportType,
} from '../../../types/rollUpReports.types';
import { RollupReportEmptyState } from '../../reports/rollUpReport/RollUpReportStates/RollupReportEmptyState';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import { BREAKDOWN_TYPES } from '../../reports/rollUpReport/rollUpReport.constants';
import { useScoringReportContext } from '../../__providers/ScoringReportContext';
import { ROWS_COLUMNS_STATE } from '../../reports/rollUpReport/rollUpReport.constants';
import { NestedRowType } from '../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/types';
import {
  checkIfIsOverallColumnId,
  getFieldsToToggleAcrossAllColumnGroups,
} from '../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/utils';

const { NO_DATA, BLANK_STATE, SUCCESS } = AdherenceReportState;
const { EXPANDED } = ROWS_COLUMNS_STATE;
const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

type Props = {
  reportType: ScoringReportType;
  apiRef: MutableRefObject<GridApiPro>;
  metadata?: RollUpReportMetadata;
  setHasData: (hasData: boolean) => void;
  isLoading: boolean;
  isError: boolean;
  areFiltersValid: boolean;
  rows: GridRowsProp<NestedRowType>;
  columns: GridColDef[];
  columnGroupingModel: GridColumnGroupingModel;
  isDeepNestedColumnTable?: boolean;
  totalCriteriaCount?: number;
  state?: AdherenceReportState;
};

const AdherenceReportDataGridV2 = ({
  reportType,
  apiRef,
  metadata,
  setHasData,
  isLoading,
  isError,
  areFiltersValid,
  columns,
  rows,
  columnGroupingModel,
  isDeepNestedColumnTable,
  totalCriteriaCount,
  state,
}: Props) => {
  const intl = useIntl();
  const isDeepNested = Boolean(isDeepNestedColumnTable);
  const { setRowColumnState, viewBy, breakdownBy, groupBy } =
    useScoringReportContext();

  const reportTitle = getReportTitle(
    createGroupByObject(reportType, groupBy, viewBy, breakdownBy)!,
    intl,
  );

  useEffect(() => {
    if (metadata && !isLoading && columns && rows) {
      const areColumnsExpanded = metadata.dataGridState?.columns === EXPANDED;
      const areRowsExpanded = metadata.dataGridState?.rows === EXPANDED;

      const parentColumnIds = columnGroupingModel.map(
        (column) => column.groupId,
      );

      const mappedColumns = columns
        .filter(
          (column) =>
            parentColumnIds.includes(column.field) &&
            !checkIfIsOverallColumnId(column.field),
        )
        .map((column) => {
          return {
            id: column.field,
            isExpanded: areColumnsExpanded,
          };
        });

      const mappedRows = rows
        .filter((row) => row.hierarchy.length === 1) // only parent rows
        .map((row) => ({
          id: row.id,
          isExpanded: areRowsExpanded,
        }));

      setRowColumnState({ rows: mappedRows, columns: mappedColumns });

      // we need to initialize the column visibility model on load, so that we can access it in ExpandableColumnHeader
      const columnFieldsToToggle = getFieldsToToggleAcrossAllColumnGroups(
        isDeepNested,
        columnGroupingModel,
      );
      const newModel: Record<string, boolean> = {};
      columnFieldsToToggle.forEach((field) => {
        newModel[field] = areColumnsExpanded;
      });
      apiRef.current?.setColumnVisibilityModel(newModel);
    }
  }, [metadata, isLoading]);

  useEffect(() => {
    setHasData(state === SUCCESS);
  }, [state]);

  const getReportState = () => {
    if ((!areFiltersValid || state === BLANK_STATE) && !isLoading) {
      return <RollupReportEmptyState reportType={reportType} />;
    }

    if (isError || state === NO_DATA) {
      return <RollUpReportBlankStates isError={isError} />;
    }

    if (isCriteriaGroupsInReportsEnabled) {
      return (
        <NestedRowAndNestedGroupColumnTableV2
          apiRef={apiRef}
          nestedColumnHeaderTitle={intl.formatMessage({
            id: 'ui.compliance.rollUpReports.adherence.first.column.cell.title',
            defaultMessage: 'Criteria',
          })}
          nestedColumnWidth={300}
          pinnedColumns={PINNED_COLUMNS}
          columns={columns}
          columnGroupingModel={isLoading ? undefined : columnGroupingModel}
          rows={rows}
          isLoading={isLoading}
          isCriteriaGroupsBreakdown={
            breakdownBy?.value[0] === BREAKDOWN_TYPES.CRITERIA_GROUPS
          }
          totalCriteriaCount={totalCriteriaCount}
          isDeepNestedColumnTable={isDeepNested}
          reportType={reportType}
        />
      );
    } else {
      return (
        <NestedRowAndGroupColumnTable
          apiRef={apiRef}
          nestedColumnHeaderTitle={reportTitle}
          nestedColumnWidth={270}
          pinnedColumns={PINNED_COLUMNS}
          margin={0}
          columns={columns}
          columnGroupingModel={columnGroupingModel}
          rows={rows}
          isLoading={isLoading}
        />
      );
    }
  };

  return (
    <VidMobStack
      sx={{
        height: '100%',
        width: '100%',
        overflowY: 'auto',
      }}
      alignItems="center"
    >
      {getReportState()}
    </VidMobStack>
  );
};

export default AdherenceReportDataGridV2;
