import React from 'react';
import { SxProps, useTheme } from '@mui/material';
import {
  VidMobBox,
  VidMobSkeleton,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { getPercentLiftColor } from '../../../../../creativeAnalytics/reports/utils/getPercentLiftColor';
import { formatNestedGridCellValue } from '../../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/utils';

type Props = {
  isNorms: boolean;
  title?: string;
  value: null | number | undefined;
  isImpressionsColumn: boolean;
  isLoader?: boolean;
  containerSx?: SxProps;
};

const NestedGridHeaderCell = ({
  isNorms,
  title,
  value,
  isLoader,
  isImpressionsColumn,
}: Props) => {
  const theme = useTheme();
  if (isLoader) {
    return (
      <VidMobBox sx={{ p: '12px 0' }}>
        <VidMobSkeleton variant="text" width={80} />
      </VidMobBox>
    );
  }

  const normsColor = getPercentLiftColor({
    theme,
    value,
  });
  const color = isNorms ? normsColor : theme.palette.text.primary;
  const displayValue = formatNestedGridCellValue(
    value,
    isImpressionsColumn,
    true,
  );
  const tooltipValue = formatNestedGridCellValue(
    value,
    isImpressionsColumn,
    false,
  );

  return (
    <VidMobBox>
      <VidMobTypography variant="caption" color="#757575">
        {title}
      </VidMobTypography>
      <VidMobTooltip title={tooltipValue} position="above">
        <div>
          <VidMobTypography
            variant="body2"
            color={color}
            sx={{
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: isImpressionsColumn ? '66px' : '118px',
            }}
          >
            {displayValue}
          </VidMobTypography>
        </div>
      </VidMobTooltip>
    </VidMobBox>
  );
};

export default NestedGridHeaderCell;
