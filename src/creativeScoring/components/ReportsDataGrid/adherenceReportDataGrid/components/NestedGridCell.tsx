import React from 'react';
import {
  VidMobBox,
  VidMobSkeleton,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { getPercentLiftColor } from '../../../../../creativeAnalytics/reports/utils/getPercentLiftColor';
import { useTheme } from '@mui/material';
import { formatNestedGridCellValue } from '../../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/utils';

const loadingContainerSx = {
  p: '6px 0',
};

const containerSx = {
  pl: '6px',
};

type Props = {
  value: null | number | undefined;
  isImpressionsColumn: boolean;
  isNorms: boolean;
  isLoader: boolean;
};

const NestedGridCell = ({
  value,
  isLoader,
  isImpressionsColumn,
  isNorms,
}: Props) => {
  const theme = useTheme();

  if (isLoader) {
    return (
      <VidMobBox sx={loadingContainerSx}>
        <VidMobSkeleton variant="text" width={80} />
      </VidMobBox>
    );
  }

  const normsColor = getPercentLiftColor({
    theme,
    value,
  });

  const color = isNorms ? normsColor : theme.palette.text.primary;
  const displayValue = formatNestedGridCellValue(
    value,
    isImpressionsColumn,
    true,
  );
  const tooltipValue = formatNestedGridCellValue(
    value,
    isImpressionsColumn,
    false,
  );

  return (
    <VidMobBox sx={containerSx}>
      <VidMobTooltip title={tooltipValue} position="above">
        <div>
          <VidMobTypography
            variant="body2"
            color={color}
            sx={{
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: isImpressionsColumn ? '66px' : '118px',
            }}
          >
            {displayValue}
          </VidMobTypography>
        </div>
      </VidMobTooltip>
    </VidMobBox>
  );
};

export default NestedGridCell;
