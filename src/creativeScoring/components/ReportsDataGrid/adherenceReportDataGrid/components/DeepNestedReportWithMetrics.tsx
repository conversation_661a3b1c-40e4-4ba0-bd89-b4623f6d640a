import React, { MutableRefObject } from 'react';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import ReportToplineMetrics from '../../../reports/reportsSharedComponents/ReportToplineMetrics/ReportToplineMetrics';
import { useScoringReportContext } from '../../../__providers/ScoringReportContext';
import ReportFiltersControlBarV2 from '../../../reports/reportsSharedComponents/ReportFiltersControlBarV2/ReportFiltersControlBarV2';
import { ReportControlBarDropdownItem } from '../../../../../components/ReportFilters/types';
import { NormsConfigurationSelectionStateType } from '../../../../../types/normsConfiguration.types';
import AdherenceReportDataGridV2 from '../AdherenceReportDataGridV2';
import { GridApiPro } from '@mui/x-data-grid-pro';
import {
  DataLevelType,
  RollUpReportMetadata,
  ScoringReportType,
} from '../../../../types/rollUpReports.types';
import useAdherenceAndImpressionsAdherenceReportV2 from '../../../reports/rollUpReport/queries/useAdherenceAndImpressionsAdherenceReportV2';
import useAdherenceReportNorms from '../../../reports/rollUpReport/queries/useAdherenceReportsNorms';
import { AdherenceServerDataResponseModel } from '../types';
import { getDataGridState } from '../utils/adherenceReportUtils';

const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

type Props = {
  apiRef: MutableRefObject<GridApiPro>;
  metadata: RollUpReportMetadata | undefined;
  isLoadingMetadata: boolean;
  isPreFlightSelected: boolean;
  doesReportSupportsNorms: boolean;
  dataLevel: ReportControlBarDropdownItem | undefined;
  normsConfiguration: NormsConfigurationSelectionStateType;
  reportType: ScoringReportType;
  setHasData: (hasData: boolean) => void;
  handleSetDataLevel: (dataLevel: ReportControlBarDropdownItem) => void;
  handleSetGroupBy: (groupBy: ReportControlBarDropdownItem) => void;
  handleSetBreakdownBy: (breakdownBy: ReportControlBarDropdownItem) => void;
  handleSetViewBy: (viewBy: ReportControlBarDropdownItem) => void;
  handleSetNormsConfiguration: (
    normsConfiguration: NormsConfigurationSelectionStateType,
  ) => void;
  reportId?: string;
};

const DeepNestedReportWithMetrics = ({
  apiRef,
  metadata,
  isLoadingMetadata,
  isPreFlightSelected,
  doesReportSupportsNorms,
  dataLevel,
  setHasData,
  normsConfiguration,
  handleSetGroupBy,
  handleSetBreakdownBy,
  handleSetViewBy,
  handleSetDataLevel,
  handleSetNormsConfiguration,
  reportType,
  reportId,
}: Props) => {
  const { breakdownBy, viewBy, groupBy } = useScoringReportContext();

  const useGetNestedReportData = isCriteriaGroupsInReportsEnabled
    ? useAdherenceAndImpressionsAdherenceReportV2
    : useAdherenceReportNorms;

  const {
    data,
    isLoading: isLoadingData,
    isFetching: isFetchingData,
    isError,
    areFiltersValid,
  } = useGetNestedReportData({
    reportId,
    groupBy,
    metadata,
    reportType,
    dataLevel: dataLevel?.id as DataLevelType,
  });

  const isLoading = isLoadingMetadata || isLoadingData || isFetchingData;

  const state = getDataGridState(
    isLoading,
    isError,
    data as unknown as AdherenceServerDataResponseModel,
  );

  if (!reportType) {
    return null;
  }

  return (
    <>
      {isCriteriaGroupsInReportsEnabled && (
        <>
          <ReportToplineMetrics
            data={data?.toplineMetricsData}
            isLoading={isLoading}
            hasError={isError}
            breakdownBy={breakdownBy?.id}
            reportType={reportType}
          />
          <ReportFiltersControlBarV2
            reportType={reportType}
            breakdownBy={breakdownBy}
            groupBy={groupBy}
            onGroupByChange={handleSetGroupBy}
            viewBy={viewBy}
            onViewByChange={handleSetViewBy}
            dataLevel={dataLevel}
            onDataLevelChange={handleSetDataLevel}
            withNormsConfiguration={doesReportSupportsNorms}
            normsConfiguration={normsConfiguration}
            onNormsConfigurationChange={handleSetNormsConfiguration}
            isLoading={isLoading}
            isDataLevelDisabled={isPreFlightSelected}
            onBreakdownByChange={handleSetBreakdownBy}
          />
        </>
      )}
      <AdherenceReportDataGridV2
        reportType={reportType}
        apiRef={apiRef}
        metadata={metadata}
        setHasData={setHasData}
        isLoading={isLoading}
        isError={isError}
        areFiltersValid={areFiltersValid}
        columns={data?.columns}
        columnGroupingModel={data?.columnGroupingModel}
        isDeepNestedColumnTable={data?.isDeepNestedColumnTable}
        rows={data?.rows}
        totalCriteriaCount={data?.totalCriteriaCount}
        state={state}
      />
    </>
  );
};

export default DeepNestedReportWithMetrics;
