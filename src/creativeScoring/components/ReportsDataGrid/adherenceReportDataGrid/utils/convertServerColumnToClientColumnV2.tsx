import React from 'react';
import { IntlShape } from 'react-intl';
import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid-pro';
import NestedGridCell from '../components/NestedGridCell';
import NestedGridHeaderCell from '../components/NestedGridHeaderCell';
import { AdherenceServerDataResponseModelV2 } from '../types';
import { NormsConfigurationSelectionStateType } from '../../../../../types/normsConfiguration.types';
import { getNestedColumnGroupsModel } from '../../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/utils';
import { IMPRESSIONS_ID_SUFFIX } from '../constants/adherenceReportConstants';

const getColumn = (
  field: string,
  title: string,
  value: null | number | undefined,
  isLoader: boolean,
  isNorms: boolean,
  isImpressionsColumn: boolean,
  width?: number,
  minWidth?: number,
): GridColDef => ({
  field,
  type: 'string',
  sortable: false,
  minWidth: minWidth,
  width: width,
  renderCell: (params) => (
    <NestedGridCell
      isNorms={isNorms}
      value={params.formattedValue}
      isLoader={isLoader}
      isImpressionsColumn={isImpressionsColumn}
    />
  ),
  renderHeader: () => (
    <NestedGridHeaderCell
      isNorms={isNorms}
      value={value}
      isLoader={isLoader}
      title={title}
      isImpressionsColumn={isImpressionsColumn}
    />
  ),
});

export const convertServerColumnToClientColumnV2 = (
  serverResponse: AdherenceServerDataResponseModelV2,
  normsConfiguration: NormsConfigurationSelectionStateType,
  intl: IntlShape,
  groupByValue: string[] | undefined,
): {
  columns: GridColDef[];
  columnGroupingModel: GridColumnGroupingModel;
  isDeepNestedColumnTable: boolean;
} => {
  const { columns } = serverResponse;

  const formattedColumns: GridColDef[] = [];

  const isDeepNestedColumnTable =
    columns.filter((column) => column.parentId).length > 0;

  columns.forEach((column) => {
    const { id, isLoader, adherencePercent, impressions } = column;

    // TODO MF Ensure you handle norms here
    const isNormsColumn = Boolean(normsConfiguration?.isEnabled);
    const hasImpressions = impressions || impressions === 0;
    const columnMinWidth = hasImpressions ? undefined : 150;
    // TODO MF ADD NORMS TO THIS CHECK
    const columnnWidth = hasImpressions ? 98 : undefined;

    const title = intl.formatMessage({
      id: 'ui.compliance.rollUpReports.adherence.columnHeader.adherence',
      defaultMessage: 'Adherence',
    });

    formattedColumns.push(
      getColumn(
        id,
        title,
        adherencePercent,
        isLoader || false,
        false,
        false,
        columnnWidth,
        columnMinWidth,
      ),
    );

    if (hasImpressions) {
      const impressionsTitle = intl.formatMessage({
        id: 'ui.compliance.rollUpReports.adherence.columnHeader.impressions',
        defaultMessage: 'Impressions',
      });

      formattedColumns.push(
        getColumn(
          id + IMPRESSIONS_ID_SUFFIX,
          impressionsTitle,
          impressions,
          isLoader || false,
          isNormsColumn,
          true,
          columnnWidth,
          columnMinWidth,
        ),
      );
    }
  });

  const columnGroupingModel = getNestedColumnGroupsModel(
    columns,
    isDeepNestedColumnTable,
    intl,
    groupByValue,
  );

  return {
    columns: formattedColumns,
    columnGroupingModel,
    isDeepNestedColumnTable,
  };
};
