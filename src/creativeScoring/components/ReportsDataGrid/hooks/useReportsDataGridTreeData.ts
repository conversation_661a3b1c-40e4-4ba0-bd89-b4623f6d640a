import { useCallback } from 'react';
import { ReportsDataGridProps } from '../ReportsDataGrid';
import { useScoringReportContext } from '../../__providers/ScoringReportContext';

export const useReportsDataGridTreeData = (props: ReportsDataGridProps) => {
  let groupByRows = props.metadata.groupBy.rows || [];

  const scoringReportContext = useScoringReportContext();

  if (scoringReportContext?.groupBy?.value) {
    groupByRows = scoringReportContext.groupBy.value;
  }

  const treeData = true;

  const getTreeDataPath = useCallback(
    (row: any) => {
      return groupByRows
        .map((groupByFieldName) => {
          return row[groupByFieldName];
        })
        .filter(Boolean);
    },
    [groupByRows],
  );

  return { treeData, getTreeDataPath };
};
