import React, { ReactNode } from 'react';
import { DataGridPro } from '@mui/x-data-grid-pro';
import {
  AggregationColumn,
  ReportDataFlag,
  ReportDataGridMetaData,
  ReportsDataGridDatapoint,
} from './ReportsDataGrid.types';
import { useReportsDataGridTreeData } from './hooks/useReportsDataGridTreeData';
import ReportsGridModelContextProvider, {
  useReportsGridModel,
} from './ReportsGridModelContextProvider';
import { defaultDataGridProProperties } from './defaults';
import { Theme } from '@mui/material';
import { useColumnGroupingStyles } from './hooks/useColumnGroupingStyles';
import { useReportTypeConfigProps } from './hooks/useReportTypeConfig';
import { isEqual } from 'lodash';
import { GridApiPro } from '@mui/x-data-grid-pro/models/gridApiPro';
import { useGetAdoptionReportColumns } from './hooks/useGetAdoptionReportColumns';
import { ScoringReportType } from '../../types/rollUpReports.types';

const areDataGridPropsEqual = (
  prevProps: ReportsDataGridProps,
  nextProps: ReportsDataGridProps,
) => {
  const reportDataIsEqual = isEqual(
    prevProps.report.data,
    nextProps.report.data,
  );
  const reportFlagsAreEqual = isEqual(
    prevProps.report.flags,
    nextProps.report.flags,
  );
  const reportAggregationColumnsAreEqual = isEqual(
    prevProps.report.aggregationColumns,
    nextProps.report.aggregationColumns,
  );
  const reportMetadataIsEqual = isEqual(prevProps.metadata, nextProps.metadata);
  const isImpressionsCountDataEqual = isEqual(
    prevProps.report.impressionCountData,
    nextProps.report.impressionCountData,
  );

  return (
    reportDataIsEqual &&
    reportFlagsAreEqual &&
    reportAggregationColumnsAreEqual &&
    reportMetadataIsEqual &&
    isImpressionsCountDataEqual
  );
};

export interface ReportsDataGridProps {
  children?: ReactNode;
  apiRef: React.MutableRefObject<GridApiPro>;
  report: {
    data: ReportsDataGridDatapoint[];
    impressionCountData?: ReportsDataGridDatapoint[];
    flags: ReportDataFlag[];
    aggregationColumns: AggregationColumn[];
  };
  metadata: ReportDataGridMetaData;
}

const ReportsDataGridWrapper = React.memo((props: ReportsDataGridProps) => {
  const { treeData, getTreeDataPath } = useReportsDataGridTreeData(props);

  const getAdoptionFilteredColumns = useGetAdoptionReportColumns();

  const { initialState, columns, rows, columnGroupingModel } =
    useReportsGridModel();

  const columnGroupingStyles = useColumnGroupingStyles(columnGroupingModel);

  const isV2AdoptionReport =
    props.metadata.reportType === ScoringReportType.ADOPTION &&
    props.metadata.filtersVersion === 2;

  const filteredColumns = isV2AdoptionReport
    ? getAdoptionFilteredColumns()
    : columns;

  return (
    <DataGridPro
      {...defaultDataGridProProperties}
      initialState={initialState}
      apiRef={props.apiRef}
      treeData={treeData}
      getTreeDataPath={getTreeDataPath}
      columnGroupingModel={columnGroupingModel}
      columns={filteredColumns}
      disableColumnResize
      slots={{
        columnResizeIcon: () => null,
      }}
      rows={rows}
      sx={(theme: Theme) => ({
        ...(typeof defaultDataGridProProperties.sx === 'function' &&
          defaultDataGridProProperties.sx(theme)),
        ...columnGroupingStyles,
      })}
    />
  );
}, areDataGridPropsEqual);

const ReportsDataGrid = React.memo((props: ReportsDataGridProps) => {
  const enhancedProps = useReportTypeConfigProps(props);

  return (
    <ReportsGridModelContextProvider {...enhancedProps}>
      <ReportsDataGridWrapper {...enhancedProps} />
    </ReportsGridModelContextProvider>
  );
}, areDataGridPropsEqual);

ReportsDataGridWrapper.displayName = 'ReportsDataGridWrapper';
ReportsDataGrid.displayName = 'ReportsDataGrid';

export default ReportsDataGrid;
