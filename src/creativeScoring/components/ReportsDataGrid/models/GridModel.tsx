// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import React from 'react';
import { ReportsDataGridProps } from '../ReportsDataGrid';
import {
  AggregationColumn,
  ReportColumnMatchers,
  ReportDataFlag,
  ReportDataGridMetaData,
  ReportsDataGridDatapoint,
  ReportsDataGridDataPointColDef,
  ReportsDataGridDataPointRowDef,
} from '../ReportsDataGrid.types';
import RowModel from './RowModel';
import ColumnModel, { AGGREGATION_COLUMN_GROUP_BYS } from './ColumnModel';
import createCellModel from './CellModel/createCellModel';
import {
  GRID_TREE_DATA_GROUPING_FIELD,
  GridColumnGroup,
  GridColumnNode,
} from '@mui/x-data-grid-pro';
import { useIntl } from 'react-intl';
import getFieldSettings from '../fields';
import ColumnHeaderGroup from '../components/ColumnHeaderGroup';
import { useReportTypeConfig } from '../hooks/useReportTypeConfig';

class GridModel {
  private readonly groupByColumns: string[];
  private readonly groupByRows: string[];
  private readonly aggregationColumns: AggregationColumn[];
  private readonly flags: ReportDataFlag[];

  columns: Record<string, ColumnModel> = {};
  rows: Record<string, RowModel> = {};
  columnGroups: Record<string, string[]> = {};
  metadata: ReportDataGridMetaData;
  impressionCountData: ReportsDataGridDatapoint[];

  constructor(reportsDataGridProps: ReportsDataGridProps) {
    this.metadata = reportsDataGridProps.metadata;
    this.groupByColumns = reportsDataGridProps.metadata.groupBy.columns;
    this.groupByRows = reportsDataGridProps.metadata.groupBy.rows;
    this.aggregationColumns =
      reportsDataGridProps.metadata.aggregationColumns || [];
    this.flags = reportsDataGridProps.report.flags || [];
    this.impressionCountData =
      reportsDataGridProps.report.impressionCountData || [];
    this.rowTotalData = reportsDataGridProps.report.rowTotalData || [];
    this.initGridModel(reportsDataGridProps.report.data);

    this.fillColumnGroups();

    if (this.aggregationColumns.length) {
      // Must be before initParentRows() for aggregations to work properly
      this.initAggregationColumns();
    }

    if (this.groupByRows.length > 1) {
      this.initParentRows(); // Not currently supporting nesting more than 1 layer
    }
  }

  private initGridModel(data: ReportsDataGridDatapoint[]) {
    data.forEach((datapoint) => {
      const { row: rowFields, column: columnFields } = datapoint;

      const cellModel = createCellModel(datapoint);

      const rowId = GridModel.generateRowColumnId(this.groupByRows, rowFields);
      this.rows[rowId] =
        this.rows[rowId] || new RowModel(rowId, rowFields, this.groupByRows);

      const columnId = GridModel.generateRowColumnId(
        this.groupByColumns,
        columnFields,
      );
      this.columns[columnId] =
        this.columns[columnId] ||
        new ColumnModel(columnId, columnFields, this.groupByColumns);

      this.rows[rowId].addCell(cellModel, columnId);
      this.columns[columnId].addCell(cellModel, rowId);

      // Group Child Columns so siblings can be easily found
      const columnGroupId = this.columns[columnId].getGroupIdFromField(
        this.groupByColumns[0],
      );
      this.columnGroups[columnGroupId] = (
        this.columnGroups[columnGroupId] || []
      ).concat(columnId);
    });
  }

  fillColumnGroups() {
    this.getColumns().forEach((column) => {
      const childFieldName = column.getChildFieldName();

      const fieldSettings = getFieldSettings(childFieldName, this);

      if (!fieldSettings || !fieldSettings.fillColumnGroup) {
        return;
      }

      fieldSettings.fillColumnGroup(column, this);
    });
  }

  initParentRows() {
    const rows = Object.values(this.rows);

    rows.forEach((row) => {
      const parentGroupByRows = row.getParentGroupByRows();
      const parentFields = row.getFields(parentGroupByRows);

      const parentRowId = GridModel.generateRowColumnId(
        parentGroupByRows,
        parentFields,
      );

      if (!this.rows[parentRowId]) {
        this.rows[parentRowId] = new RowModel(
          parentRowId,
          parentFields,
          parentGroupByRows,
        );
      }

      this.rows[parentRowId].addRowToParent(row);
    });
  }

  testIsBlankAggregationColumn(aggregationColumn: AggregationColumn) {
    const { matchers } = aggregationColumn;
    const columnsIncludedInAggregation = this.getColumns().filter((column) => {
      return column.testMatchers(matchers);
    });

    return columnsIncludedInAggregation.every((column) =>
      column.isColumnBlank(),
    );
  }

  initAggregationColumns() {
    const rows = Object.values(this.rows);

    this.aggregationColumns.forEach((aggregationColumn) => {
      const isBlankAggregationColumn =
        this.testIsBlankAggregationColumn(aggregationColumn);
      if (isBlankAggregationColumn) {
        return;
      }

      const { group } = aggregationColumn;
      const isGroupAggregate = Boolean(this.getColumnGroupById(group).length);
      const columnId = GridModel.generateAggregationColumnId(aggregationColumn);

      this.columns[columnId] =
        this.columns[columnId] ||
        new ColumnModel(
          columnId,
          aggregationColumn,
          AGGREGATION_COLUMN_GROUP_BYS,
          false,
          isGroupAggregate,
        );

      rows.forEach((row) => {
        const cellModel = row.aggregateCells(
          aggregationColumn,
          this.impressionCountData,
          this.rowTotalData,
        );

        if (cellModel) {
          row.addCell(cellModel, columnId);
          this.columns[columnId].addCell(cellModel, row.rowId);
        }
      });
    });
  }

  getMatchingFlags(fields: Record<string, any>) {
    return this.flags.filter((flag) => {
      return GridModel.testMatchers(flag.matchers, fields);
    });
  }

  public getColumnsForDataGridProProps() {
    const columns = this.getColumns().reverse();
    const { reportType } = this.metadata;
    return columns
      .sort(this.getDefaultColumnSorting())
      .map((column) => column.getDataGridProProperties(reportType));
  }

  getDefaultColumnSorting() {
    const getGroupAggregationColumnSpoof = (column: ColumnModel) => {
      const columnGroupId = column.getField(AGGREGATION_COLUMN_GROUP_BYS[0]);
      const columnGroup = this.getColumnGroupById(columnGroupId);
      if (!columnGroup.length) {
        throw new Error(
          'Group Aggregation Column is defined but no columns can be found in its corresponding column group with id' +
            columnGroupId,
        );
      }

      const siblingColumn = this.getColumnById(columnGroup[0]);

      const columnFields = {
        [this.groupByColumns[0]]: siblingColumn.getField(
          this.groupByColumns[0],
        ),
      };

      return new ColumnModel(columnGroupId, columnFields, this.groupByColumns);
    };

    return (columnA: ColumnModel, columnB: ColumnModel) => {
      if (columnA.isAggregationColumn() && columnB.isAggregationColumn()) {
        const aggregationSorting = this.sortAggregationColumns(
          columnA,
          columnB,
        );
        if (aggregationSorting) {
          return aggregationSorting;
        }
      }

      if (
        columnA.isAggregationColumn() &&
        !columnA.isGroupAggregationColumn()
      ) {
        return -1;
      }

      if (
        columnB.isAggregationColumn() &&
        !columnB.isGroupAggregationColumn()
      ) {
        return 1;
      }

      return this.sortColumnByGroupOrder(
        columnA.isGroupAggregationColumn()
          ? getGroupAggregationColumnSpoof(columnA)
          : columnA,
        columnB.isGroupAggregationColumn()
          ? getGroupAggregationColumnSpoof(columnB)
          : columnB,
      );
    };
  }

  private sortColumnGroupDefault(
    currentGroupByColumn: string,
    columnA: ColumnModel,
    columnB: ColumnModel,
  ) {
    const groupIdA = columnA.getGroupIdFromField(currentGroupByColumn);
    const groupIdB = columnB.getGroupIdFromField(currentGroupByColumn);

    if (groupIdA !== groupIdB) {
      return groupIdA.localeCompare(groupIdB);
    }

    return 0;
  }

  private sortColumnByGroupOrder(columnA: ColumnModel, columnB: ColumnModel) {
    return this.groupByColumns.reduce((acc, currentGroupByColumn) => {
      if (acc) {
        return acc;
      }

      const fieldSettings = getFieldSettings(currentGroupByColumn, this);

      if (!fieldSettings || !fieldSettings.sort) {
        return this.sortColumnGroupDefault(
          currentGroupByColumn,
          columnA,
          columnB,
        );
      }

      const columnFieldValueA = columnA.getField(currentGroupByColumn);
      const columnFieldValueB = columnB.getField(currentGroupByColumn);

      if (!columnFieldValueA) {
        return 1;
      }

      if (!columnFieldValueB) {
        return -1;
      }

      return (
        fieldSettings.sort(columnFieldValueA, columnFieldValueB) ||
        this.sortColumnGroupDefault(currentGroupByColumn, columnA, columnB)
      );
    }, 0);
  }

  private sortAggregationColumns(columnA: ColumnModel, columnB: ColumnModel) {
    const columnGroupNameA = columnA.getField(AGGREGATION_COLUMN_GROUP_BYS[0]);
    const columnGroupNameB = columnB.getField(AGGREGATION_COLUMN_GROUP_BYS[0]);
    if (columnGroupNameA !== columnGroupNameB) {
      return 0;
    }

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { aggregationColumns = [] } = useReportTypeConfig(
      this.metadata.reportType,
    );
    const columnIndexA = this.findAggregationColumnIndex(
      aggregationColumns,
      columnA,
    );
    const columnIndexB = this.findAggregationColumnIndex(
      aggregationColumns,
      columnB,
    );
    return columnIndexA - columnIndexB;
  }

  private findAggregationColumnIndex(
    aggregationColumns: AggregationColumn[] = [],
    column: ColumnModel,
  ) {
    return aggregationColumns.findIndex((aggregationColumn) => {
      const isSameGroup = aggregationColumn.group === column.getField('group');
      const isSameName = aggregationColumn.name === column.getField('name');
      return isSameGroup && isSameName;
    });
  }

  public getRowsForDataGridProProps() {
    const rows = this.getRows();
    return rows
      .sort(this.getDefaultRowSorting())
      .map((row) => row.getDataGridProProperties());
  }

  getDefaultRowSorting() {
    return (rowA: RowModel, rowB: RowModel) => {
      const defaultSort = () => {
        return rowA.getId().localeCompare(rowB.getId());
      };

      return this.groupByRows.reduce((acc, currentGroupByRow) => {
        if (acc) {
          return acc;
        }

        const fieldSettings = getFieldSettings(currentGroupByRow, this);

        if (fieldSettings && fieldSettings.sort) {
          const sortValue = fieldSettings.sort(
            rowA.getField(currentGroupByRow),
            rowB.getField(currentGroupByRow),
          );

          if (sortValue) {
            return sortValue;
          }
        }

        return defaultSort();
      }, 0);
    };
  }

  public getColumnGroupingModel() {
    const groupId = this.groupByRows.join('.');

    const headerGroup: GridColumnGroup = {
      groupId,
      children: [{ field: GRID_TREE_DATA_GROUPING_FIELD }],
      renderHeaderGroup: () => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const intl = useIntl();
        return (
          intl.messages[
            'ui.creativeScoring.rollUpReports.grouping.' + groupId
          ] || groupId
        );
      },
    };

    const columnGroups = this.getColumnGroupsForColumns();

    return [headerGroup, ...columnGroups];
  }

  public getColumnGroupsForColumns() {
    const columnGroups: GridColumnGroup[] = [];

    const columns = Object.values(this.columns);
    columns.forEach((column) => {
      const groupByColumns = column.getGroupByColumns();
      this.assignColumnToGroupRecursive(columnGroups, column, groupByColumns);
    });

    return columnGroups;
  }

  private assignColumnToGroupRecursive(
    columnGroup: GridColumnNode[],
    column: ColumnModel,
    groupByColumns: string[],
  ) {
    const columnFields = [...groupByColumns];

    if (columnFields.length === 1) {
      columnGroup.push({ field: column.columnId });
      return;
    }

    const columnGroupField = columnFields[0];
    const currentGroups = columnGroup as GridColumnGroup[];
    // sets groupId to value of the field at current depth of iterating the column group tree
    const groupId = column.getGroupIdFromField(columnGroupField);

    // If this is a new group
    if (!currentGroups.some((group) => group.groupId === groupId)) {
      const groupToAdd: GridColumnGroup = {
        groupId,
        children: [],
        renderHeaderGroup: () => {
          return (
            <ColumnHeaderGroup
              fieldName={columnGroupField}
              fieldValue={column.getField(columnGroupField)}
            />
          );
        },
      };

      currentGroups.push(groupToAdd);
    }

    const currentGroup = currentGroups.find(
      (group) => group.groupId === groupId,
    ) as GridColumnGroup;

    this.assignColumnToGroupRecursive(
      currentGroup?.children,
      column,
      columnFields.slice(1),
    );
  }

  public getColumnById(columnId: string) {
    return this.columns[columnId];
  }

  public getColumnGroupById(columnGroupId: string) {
    return this.columnGroups[columnGroupId] || [];
  }

  public getColumnSiblingsIds(column: string | ColumnModel) {
    column = (
      typeof column === 'string' ? this.getColumnById(column) : column
    ) as ColumnModel;
    const columnGroupId = column.getGroupIdFromField(this.groupByColumns[0]);
    return this.getColumnGroupById(columnGroupId);
  }

  public getParentGroupByColumn() {
    return this.groupByColumns[0];
  }

  getGroupByColumns() {
    return this.groupByColumns;
  }

  getGroupByRow(index: number) {
    return this.groupByRows[index];
  }

  getRows() {
    return Object.values(this.rows);
  }

  getColumns() {
    return Object.values(this.columns);
  }

  setColumn(columnId: string, column: ColumnModel) {
    this.columns[columnId] = column;
  }

  getFilter(fieldName: string) {
    return this.metadata.filters?.find(
      (filter) => filter.fieldName === fieldName,
    );
  }

  static generateAggregationColumnId(aggregationColumn: any) {
    return GridModel.generateRowColumnId(
      AGGREGATION_COLUMN_GROUP_BYS,
      aggregationColumn,
    );
  }

  static generateRowColumnId(
    groupByFields: string[],
    elementDef: ReportsDataGridDataPointRowDef | ReportsDataGridDataPointColDef,
  ) {
    return groupByFields
      .map((groupByFieldName) => {
        const elementFieldValue = elementDef?.[groupByFieldName];
        if (typeof elementFieldValue === 'object') {
          return this.objToString(elementFieldValue);
        }

        return elementFieldValue;
      })
      .join('.');
  }

  static objToString(obj: any) {
    if (typeof obj === 'string') {
      return obj;
    }

    return [
      obj.criteriaPlatform,
      obj.criteriaIdentifier,
      obj.criteriaParameters,
    ].join(':');
  }

  static testMatchers(
    matchers: ReportColumnMatchers,
    fields: Record<string, any> = {},
  ): boolean {
    return Object.keys(matchers).every((key) => {
      if (!(key in fields)) {
        return false;
      }

      const typeOfMatcher = typeof matchers[key];
      const typeOfField = typeof fields[key];

      if (typeOfMatcher !== typeOfField) {
        return false;
      }

      if (typeOfMatcher === 'object') {
        return this.testMatchers(
          matchers[key] as ReportColumnMatchers,
          fields[key] as Record<string, any>,
        );
      }

      return matchers[key] === fields[key];
    });
  }
}

export default GridModel;
