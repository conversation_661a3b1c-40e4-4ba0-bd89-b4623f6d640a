import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useMemo,
} from 'react';
import GridModel from './models/GridModel';
import { ReportsDataGridProps } from './ReportsDataGrid';
import {
  DataGridProProps,
  GRID_TREE_DATA_GROUPING_FIELD,
  GridColumnGroup,
} from '@mui/x-data-grid-pro';
import { useReportsDataGridState } from './hooks/useReportsDataGridState';
import { useScoringReportContext } from '../__providers/ScoringReportContext';
import { useIntl } from 'react-intl';

export type ReportsGridModelContextValue = {
  gridModel: GridModel;
} & Pick<
  DataGridProProps,
  'initialState' | 'columns' | 'rows' | 'columnGroupingModel'
>;

const ReportsGridModelContext = createContext<
  ReportsGridModelContextValue | Record<string, never>
>({});

export const useReportsGridModel = (): ReportsGridModelContextValue =>
  useContext(ReportsGridModelContext) as ReportsGridModelContextValue;

const ReportsGridModelContextProvider: React.FC<ReportsDataGridProps> = (
  props,
) => {
  const [gridModel, setGridModel] = useState(() => new GridModel(props));
  const scoringReportContext = useScoringReportContext();
  const intl = useIntl();

  const groupBy = scoringReportContext?.groupBy;

  useEffect(() => {
    setGridModel(new GridModel(props));
  }, [props]);

  const [initialState] = useReportsDataGridState();

  const columns = useMemo(
    () => gridModel.getColumnsForDataGridProProps(),
    [gridModel],
  );
  const rows = useMemo(
    () => gridModel.getRowsForDataGridProProps(),
    [gridModel],
  );
  const columnGroupingModel = useMemo(() => {
    if (groupBy?.value) {
      const columnGroups = gridModel.getColumnGroupsForColumns();
      const groupId = groupBy?.value.join('.');
      const headerGroup: GridColumnGroup = {
        groupId,
        children: [{ field: GRID_TREE_DATA_GROUPING_FIELD }],
        renderHeaderGroup: () => {
          return (
            (intl.messages[
              'ui.creativeScoring.rollUpReports.grouping.' + groupId
            ] as string) || groupId
          );
        },
      };
      return [headerGroup, ...columnGroups];
    }

    return gridModel.getColumnGroupingModel();
  }, [gridModel, groupBy]);

  const contextValue = useMemo(
    () => ({
      gridModel,
      initialState,
      columns,
      rows,
      columnGroupingModel,
    }),
    [gridModel, initialState, columns, rows, columnGroupingModel],
  );

  return (
    <ReportsGridModelContext.Provider value={contextValue}>
      {props.children}
    </ReportsGridModelContext.Provider>
  );
};

export default React.memo(ReportsGridModelContextProvider);
