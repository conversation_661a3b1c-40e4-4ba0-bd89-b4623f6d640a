import React from 'react';
import { useSelector } from 'react-redux';
import SemanticList from '../../../../components/SemanticList';
import TableRow from '../../../../components/TableRow';
import {
  getSortedCriteriaData,
  getCriteriaScoreResultLoadingStatus,
} from '../../../redux/selectors/DAMIndividualAsset.selectors';
import { GLOBALS } from '../../../../constants';
import { useIntl } from 'react-intl';
import './DAMIndividualCriteriaList.scss';
import { getMediaFileType } from '../../../redux/selectors/complianceShared.selectors';
import DAMIndividualCriteriaListItem from '../DAMIndividualCriteriaListItem/DAMIndividualCriteriaListItem';

const { REDUX_LOADING_STATUS } = GLOBALS;

const DAMIndividualCriteriaList = ({ mediaId }) => {
  const intl = useIntl();
  const sortedCriteriaData = useSelector((state) =>
    getSortedCriteriaData(state, mediaId),
  );
  const criteriaScoreLoadingStatus = useSelector((state) =>
    getCriteriaScoreResultLoadingStatus(state),
  );
  const mediaFileType = useSelector((state) =>
    getMediaFileType(state, mediaId),
  );

  const children =
    criteriaScoreLoadingStatus === REDUX_LOADING_STATUS.SUCCESS ? (
      sortedCriteriaData?.map((criteria) => {
        const criteriaId = Object.keys(criteria)[0];

        return (
          <DAMIndividualCriteriaListItem
            key={criteria[criteriaId].id}
            criteria={criteria}
            mediaFileType={mediaFileType}
            intl={intl}
          />
        );
      })
    ) : (
      <></>
    );

  return (
    <>
      <TableRow className="header compliance-individual-criteria-list-header">
        <span className="sub-heading-2-left-aligned-gray">
          {intl.messages[
            'ui.compliance.singleAssetView.tableHeader.criteria'
          ].toUpperCase()}
        </span>
        <span className="sub-heading-2-left-aligned-gray result-column-header criteria-met">
          {intl.messages[
            'ui.compliance.singleAssetView.tableHeader.criteriaMet'
          ].toUpperCase()}
        </span>
        <div className="spacer" />
      </TableRow>
      <SemanticList className="criteria-list">{children}</SemanticList>
    </>
  );
};

export default DAMIndividualCriteriaList;
