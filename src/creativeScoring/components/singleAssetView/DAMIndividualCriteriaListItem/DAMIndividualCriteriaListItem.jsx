import React from 'react';
import { useSelector } from 'react-redux';
import TableRow from '../../../../components/TableRow';
import { getCriteriaDescriptionAndDefinition } from '../../../featureServices/CreativeScoring';
import { PLATFORM, GLOBALS } from '../../../../constants';
import ComplianceCriteriaResult from '../../brandScore/CriteriaResult';
import compliance from '../../../constants/creativeScoring.constants';
import vidmobIcon from '../../../../assets/icons/ic-vidmob-black.svg';
import CriteriaLoadingIcon from '../../../../assets/icons/ic-loading-gray.svg';
import './DAMIndividualCriteriaListItem.scss';
import {
  getMediaScoresLoadingStatus,
  getIndividualCriteriaScore,
} from '../../../redux/selectors/DAMIndividualAsset.selectors';

const { REDUX_LOADING_STATUS } = GLOBALS;

const DAMIndividualCriteriaListItem = ({ criteria, mediaFileType, intl }) => {
  const criteriaId = Object.keys(criteria)[0];

  // Pick the vidmob Icon if the platform identifier comes back as undefined. This set of constants doesn't include the vidmob icon.
  const iconUrl =
    PLATFORM.platformColorIconUrls[criteria[criteriaId].platformIdentifier] ||
    vidmobIcon;
  const criteriaDescription = criteria[criteriaId].parameters
    ? getCriteriaDescriptionAndDefinition(
        criteria[criteriaId].identifier,
        criteria[criteriaId].parameters,
      )?.description
    : intl.messages[
        compliance.CONTENT_AUDIT_CRITERIA_DESCRIPTION[
          criteria[criteriaId].identifier
        ]
      ];

  const mediaScoresLoadingStatus = useSelector((state) =>
    getMediaScoresLoadingStatus(state),
  );
  const criteriaScore = useSelector((state) =>
    getIndividualCriteriaScore(state, criteriaId),
  );

  return (
    <li
      className="compliance-individual-criteria-list-item"
      data-testid="list-item"
    >
      <TableRow className="table-row">
        <div className="criteria">
          <img className="criteria-platform-icon" src={iconUrl} alt="" />
          <span className="criteria-description body-left-aligned-black">
            {criteriaDescription}
          </span>
        </div>
        <div className="criteria-result">
          {mediaScoresLoadingStatus === REDUX_LOADING_STATUS.SUCCESS ? (
            <ComplianceCriteriaResult
              isAggregateResult={false}
              mediaFileType={mediaFileType}
              value={
                compliance
                  .INDIVIDUAL_CRITERIA_RESULTS_API_VALUES_TO_INTERNAL_VALUES[
                  criteriaScore
                ]
              }
            />
          ) : (
            <img
              src={CriteriaLoadingIcon}
              className="result-icon processing-icon"
              alt=""
              data-test="processing-icon"
            />
          )}
        </div>
        <div className="spacer" />
      </TableRow>
    </li>
  );
};

export default DAMIndividualCriteriaListItem;
