@import '../../../../styles/colors';

.compliance-individual-criteria-list-item {
  padding-right: 32px;
  &:first-child {
    border-top: 1px solid $medium-ash;
  }

  .new-table-row {
    display: flex;
    min-height: 44px;
    padding: 0 12px;
    border-bottom: 1px solid $medium-ash;
    height: auto;

    & > .cell:first-child {
      flex: 5;
    }
    .middle-children {
      flex: 8;
    }
    & > .cell:last-child {
      display: flex;
      justify-content: space-between;
      flex: 8;
    }

    .channel {
      display: flex;
      align-items: center;

      .platform-identifier {
        margin-left: 13px;
      }
    }

    .criteria {
      display: flex;
      align-items: center;
      overflow-wrap: break-word;

      .criteria-platform-icon {
        width: 24px;
        height: 24px;
      }

      .MuiChip-root {
        margin-top: 0 !important;
      }

      .criteria-description {
        width: 100%;

        &.is-optional {
          display: flex;
          align-items: center;
        }
      }

      .tooltip-underlined-description {
        text-decoration: underline;
        text-decoration-style: dotted;
      }
    }

    .criteria-result {
      .criteria-result-box {
        display: flex;
        flex-direction: row;
      }

      .chip-criteria-result {
        padding-left: 14px;

        .MuiChip-root {
          font-family: <PERSON><PERSON><PERSON><PERSON>, sans-serif;
          font-weight: 400;
        }
      }

      .processing-icon {
        height: 18px;
        width: 18px;
        background: url('../../../../assets/icons/ic-loading-gray.svg') center
          center no-repeat;
        animation: spin-load 1s linear infinite;
      }
    }
  }
}

.individual-creative-view-platform-icon {
  width: 24px;
  height: 24px;
}
