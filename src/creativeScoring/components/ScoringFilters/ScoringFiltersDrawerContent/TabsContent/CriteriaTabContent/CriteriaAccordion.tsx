import React, { useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import useReportFilters from '../../../../../../components/ReportFilters/hooks/useReportFilters';
import {
  FilterType,
  REPORT_CRITERIA_FILTERS,
  ReportCriteriaFilterKey,
  ReportScopeParametersFilterKey,
} from '../../../../../../components/ReportFilters/types';
import { CRITERIA_TAB_FILTERS } from '../../../constants';
import { ReportFilterKey } from '../../../../../../components/ReportFilters/types';
import AccordionFilterWrapper from '../../../../../../components/ReportFilters/components/AccordionFilterWrapper';
import { VidMobBox } from '../../../../../../vidMobComponentWrappers';
import { CRITERIA_TAB_KEY } from '../../../constants';
import { ALL_PLATFORMS } from '../../../../../../components/ReportFilters/constants';
import GenericSidePanel from '../../../../../../components/ReportFilters/components/GenericSidePanel';
import {
  Operator,
  Value,
} from '../../../../../../components/ReportFilters/types';
import CriteriaList from './CriteriaList';
import useFilteredWorkspaceCriteria from './useFilteredWorkspaceCriteria';
import { GridPaginationModel, GridSortModel } from '@mui/x-data-grid-pro';
import { generateFilterDisplayInformation, sortModelToSort } from './helpers';
import { useCriteriaFilterValues } from './useCriteriaFilterValues';
import { useSearchFilters } from './useSearchFilters';
import useCheckAllRules from './hooks/useCheckAllRules';
import { CriteriaResponse } from './useCriteria';
import { MediaType } from '../../../../../../types/mediaTypes.types';

const usePagination = () => {
  const [page, setPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(8);
  const onPaginationModelChange = (model: GridPaginationModel) => {
    setPage(model.page);
    setPageSize(model.pageSize);
  };
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const onSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
  };

  const resetPagination = () => {
    setPage(0);
  };
  const resetSort = () => {
    setSortModel([]);
  };

  return {
    page,
    pageSize,
    resetPagination,
    resetSort,
    onPaginationModelChange,
    sortModel,
    onSortModelChange,
  };
};

const CriteriaAccordion = () => {
  const intl = useIntl();

  const {
    page,
    onPaginationModelChange,
    pageSize,
    resetPagination,
    resetSort,
    onSortModelChange,
    sortModel,
  } = usePagination();

  const filters = useSearchFilters();
  const { criteriaId: criteriaRuleFilterValues } = useCriteriaFilterValues();

  const [searchText, setSearchText] = useState<string>('');
  const [totalSize, setTotalSize] = useState<number>(0);
  const [isCriteriaRulePanelOpen, setIsCriteriaRulePanelOpen] =
    useState<boolean>(false);

  const sort = sortModelToSort(sortModel);
  const {
    isLoading: criteriaLoading,
    data: filteredCriteriaRules,
    refetch,
  } = useFilteredWorkspaceCriteria({
    searchText,
    page,
    pageSize,
    fetchAll: false,
    enabled: true,
    onSuccess: (data: CriteriaResponse) => {
      setTotalSize(data?.pagination.totalSize);
    },
    sort,
    consideration: filters.isOptional,
    organizationCriteria: filters.globalStatuses,
    channelIds: filters.platforms,
    criteriaGroupIds: filters.criteriaGroupIds,
    mediaTypes: filters.mediaTypes as MediaType[],
  });

  const {
    getFilterValue,
    setFilterValue,
    removeStagingFilterValue,
    filterDefinitions,
    getAdvancedFilterValue,
  } = useReportFilters();

  const {
    checkedRuleRows,
    setCheckedRuleRows,
    handleSelectAll,
    allRowsChecked,
    isAllItemsLoading,
  } = useCheckAllRules({ searchText });

  const handleSearch = (text: string) => {
    setSearchText(text);
  };

  const handleOpenCriteriaRulePanel = () => {
    setIsCriteriaRulePanelOpen(true);
    setCheckedRuleRows(
      criteriaRuleFilterValues?.value as {
        id: number;
        name: string;
      }[],
    );
  };

  const handleCloseCriteriaRulePanel = () => {
    setIsCriteriaRulePanelOpen(false);
    resetPagination();
    resetSort();
    setSearchText('');
  };

  const onCriteriaRuleSave = () => {
    setFilterValue(REPORT_CRITERIA_FILTERS.CRITERIA_RULE, {
      value: checkedRuleRows || [],
      operator: Operator.IN,
    });
    handleCloseCriteriaRulePanel();
  };

  useEffect(() => {
    if (!criteriaRuleFilterValues?.value) {
      setFilterValue(
        REPORT_CRITERIA_FILTERS.CRITERIA_RULE,
        {
          value: [],
          operator: Operator.IN,
        },
        false,
      );
    }
    refetch();
  }, []);

  const filterKeys = CRITERIA_TAB_FILTERS.filter((filterKey) =>
    getFilterValue(filterKey),
  );

  const filterDisplayInformation = generateFilterDisplayInformation(
    filters,
    getFilterValue,
    getAdvancedFilterValue,
    intl,
  );

  const onAddFilter = useCallback(
    (filter: ReportCriteriaFilterKey) => {
      setFilterValue(filter, {
        value: '',
      });
    },
    [setFilterValue],
  );

  const addFilterMenuItems = CRITERIA_TAB_FILTERS.reduce<FilterType[]>(
    (acc, filterKey) => {
      const val = getFilterValue(filterKey);
      if (!val) {
        const key = filterKey;
        const data = filterDefinitions[key];

        if (data) {
          acc.push({
            id: data.key,
            labelKey: data.labelKey || '',
          });
        }
      }
      return acc;
    },
    [],
  );

  const onChange = ({
    filterKey,
    value,
    operator,
  }: {
    filterKey: ReportFilterKey;
    value: Value;
    operator: Operator;
  }) => {
    if (filterKey === REPORT_CRITERIA_FILTERS.CRITERIA_RULE) {
      handleOpenCriteriaRulePanel();
      return;
    }

    setFilterValue(REPORT_CRITERIA_FILTERS.CRITERIA_RULE, {
      value: [],
      operator: Operator.IN,
    });

    setFilterValue(filterKey as ReportScopeParametersFilterKey, {
      value,
      operator,
    });
  };

  const onDeleteFilter = (filterKey: ReportScopeParametersFilterKey) => {
    removeStagingFilterValue(filterKey);
  };

  return (
    <VidMobBox>
      <AccordionFilterWrapper
        name={intl.formatMessage({
          id: CRITERIA_TAB_KEY,
          defaultMessage: 'Criteria',
        })}
        filterKeys={filterKeys}
        addFilterMenuItems={addFilterMenuItems}
        hasDivider
        canAddFilter
        onAddFilter={onAddFilter as any}
        onChange={onChange}
        isStatic={filterKeys.length >= CRITERIA_TAB_FILTERS.length}
        onDeleteFilter={(filterKey: ReportFilterKey) =>
          onDeleteFilter(filterKey as ReportScopeParametersFilterKey)
        }
        isAdvancedFilter={false}
        channel={ALL_PLATFORMS}
      />

      <GenericSidePanel
        open={isCriteriaRulePanelOpen}
        onClose={handleCloseCriteriaRulePanel}
        headerTitle={intl.formatMessage({
          id: 'ui.criteriaFilters.tabs.filters.criteriaRule.title',
          defaultMessage: 'Select Criteria',
        })}
        isSubmitBtnDisabled={false}
        onSave={onCriteriaRuleSave}
        headerWithBottomBorder={false}
      >
        <CriteriaList
          listItems={filteredCriteriaRules?.data || []}
          checkedRuleRows={checkedRuleRows}
          allRowsChecked={allRowsChecked}
          setCheckedRuleRows={setCheckedRuleRows}
          isLoading={criteriaLoading || isAllItemsLoading}
          searchText={searchText}
          onSearch={handleSearch}
          filterDisplayInformation={filterDisplayInformation}
          page={page}
          onPaginationModelChange={onPaginationModelChange}
          pageSize={pageSize}
          totalSize={totalSize}
          sortModel={sortModel}
          onSortModelChange={onSortModelChange}
          handleSelectAll={handleSelectAll}
        />
      </GenericSidePanel>
    </VidMobBox>
  );
};

export default CriteriaAccordion;
