import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import useDebounce from '../../../../../../hooks/useDebounce';
import { useDidUpdateEffect } from '../../../../../../hooks/useDidUpdateEffect';
import Search from '../../../../../../muiCustomComponents/Search';
import { VidMobBox } from '../../../../../../vidMobComponentWrappers';

const CriteriaSearch = ({
  searchText,
  onSearch,
}: {
  searchText: string;
  onSearch: (text: string) => void;
}) => {
  const intl = useIntl();

  const [searchTerm, setSearchTerm] = useState(searchText);
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  useDidUpdateEffect(() => {
    onSearch(searchTerm);
  }, [debouncedSearchTerm]);

  const searchWrapperStyles = {
    width: '220px',
  };

  const searchCustomStyles = {
    height: '32px',
  };

  return (
    <VidMobBox sx={searchWrapperStyles} className="criteria-management-search">
      <Search
        placeholder={
          intl.messages[
            'ui.creativeScoring.criteriaManagementV2.search.placeholder'
          ] as string
        }
        onSearchChange={setSearchTerm}
        customStyles={searchCustomStyles}
      />
    </VidMobBox>
  );
};

export default CriteriaSearch;
