import React from 'react';
import CriteriaTable from './CriteriaTable';
import { useIntl } from 'react-intl';
import CriteriaSearch from './CriteriaSearch';
import { GridPaginationModel, GridSortModel } from '@mui/x-data-grid-pro';
import {
  VidMobTypography,
  VidMobBox,
  VidMobChip,
  VidMobDivider,
  VidMobTooltip,
} from '../../../../../../vidMobComponentWrappers';
import { CriterionServerType } from '../../../../../types/criteriaManagement.types';
import { NumberIdAndName } from '../../../../../../types/common.types';

const FilterChip = ({
  value,
  name,
  fullText,
}: {
  value: string;
  name: string;
  fullText: string;
}) => (
  <VidMobTooltip title={fullText}>
    <VidMobChip
      label={
        <VidMobBox sx={{ display: 'flex', gap: 2 }}>
          <VidMobTypography sx={{ fontSize: '12px' }}>
            {name}:{' '}
          </VidMobTypography>
          <VidMobTypography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
            {value}
          </VidMobTypography>
        </VidMobBox>
      }
    />
  </VidMobTooltip>
);

export type FilterKeyValue = {
  key: string;
  value: string;
  label: string;
  fullText: string;
};

const CriteriaList = ({
  listItems,
  checkedRuleRows,
  allRowsChecked,
  setCheckedRuleRows,
  isLoading,
  searchText,
  onSearch,
  filterDisplayInformation,
  page,
  onPaginationModelChange,
  pageSize,
  sortModel,
  onSortModelChange,
  handleSelectAll,
  totalSize,
}: {
  listItems: CriterionServerType[];
  checkedRuleRows: NumberIdAndName[];
  allRowsChecked: boolean;
  setCheckedRuleRows: (rules: NumberIdAndName[]) => void;
  isLoading: boolean;
  searchText: string;
  onSearch: (text: string) => void;
  filterDisplayInformation: FilterKeyValue[] | null;
  page: number;
  onPaginationModelChange: (model: GridPaginationModel) => void;
  pageSize: number;
  sortModel: GridSortModel;
  onSortModelChange: (model: GridSortModel) => void;
  handleSelectAll: (isChecked: boolean) => void;
  totalSize: number;
}) => {
  const intl = useIntl();
  const selectedItems = intl.formatMessage(
    {
      id: 'ui.criteriaFilters.tabs.filters.criteriaRule.itemsSelected',
    },
    { count: checkedRuleRows?.length || 0 },
  );

  return (
    <VidMobBox pb={10} px={5}>
      <VidMobBox
        mt={10}
        mb={10}
        ml={2}
        gap={2}
        sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}
      >
        {filterDisplayInformation
          ? filterDisplayInformation.map((keyValue) => (
              <FilterChip
                key={keyValue.key}
                name={keyValue.label}
                value={keyValue.value}
                fullText={keyValue.fullText}
              />
            ))
          : null}
      </VidMobBox>
      <VidMobBox
        mb={5}
        ml={2}
        sx={{ display: 'flex', gap: 10, alignItems: 'center' }}
      >
        <VidMobTypography style={{ fontWeight: 'bold' }}>
          {selectedItems}
        </VidMobTypography>
        <VidMobDivider orientation="vertical" flexItem />
        <CriteriaSearch searchText={searchText} onSearch={onSearch} />
      </VidMobBox>
      <CriteriaTable
        listItems={listItems}
        checkedRuleRows={checkedRuleRows}
        allRowsChecked={allRowsChecked}
        setCheckedRuleRows={setCheckedRuleRows}
        isLoading={isLoading}
        page={page}
        onPaginationModelChange={onPaginationModelChange}
        pageSize={pageSize}
        totalSize={totalSize}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
        handleSelectAll={handleSelectAll}
      />
    </VidMobBox>
  );
};

export default CriteriaList;
