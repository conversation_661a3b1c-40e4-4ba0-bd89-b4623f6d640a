import React from 'react';
import { useIntl } from 'react-intl';
import { GridCellParams, GridRenderCellParams } from '@mui/x-data-grid-pro';
import { DATA_GRID_FIELDS } from '../../../../../constants/criteriaManagement.constants';
import ChannelCell from '../../../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells/ChannelCell';
import BasicTextCell from '../../../../shared/ScoringDataGrid/ScoringDataGridCells/BasicTextCell';
import {
  CriteriaGroupCell,
  LoadingCell,
} from '../../../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells';
import { VidMobCheckbox } from '../../../../../../vidMobComponentWrappers';
import { getFeatureFlag } from '../../../../../../utils/featureFlagUtils';
import { NumberIdAndName } from '../../../../../../types/common.types';

const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');

export const useColumns = ({
  isLoading,
  checkedRows,
  onCheck,
  allRowsChecked,
  handleCheckAll,
}: {
  isLoading: boolean;
  // hotfix, need to be refactored
  checkedRows: number[] | NumberIdAndName[];
  onCheck: (row: NumberIdAndName, isChecked: boolean) => void;
  allRowsChecked: boolean;
  handleCheckAll: (isChecked: boolean) => void;
}) => {
  const intl = useIntl();
  const checkedRowsObj =
    Array.isArray(checkedRows) && typeof checkedRows[0] === 'object';

  const columns = [
    {
      field: 'check',
      width: 80,
      sortable: false,
      renderHeader: () =>
        isLoading ? null : (
          <VidMobCheckbox
            checked={allRowsChecked}
            indeterminate={!allRowsChecked && checkedRows.length > 0}
            onChange={(event) => handleCheckAll(event.target.checked)}
          />
        ),
      renderCell: (params: GridRenderCellParams) => {
        // hotfix, need to be refactored
        const isChecked = checkedRowsObj
          ? (checkedRows as NumberIdAndName[]).some(
              (checkedRow) => checkedRow.id === params.row.id,
            )
          : (checkedRows as number[]).includes(params.row.id);

        return (
          <VidMobCheckbox
            checked={isChecked}
            onChange={(event) => onCheck(params.row, event.target.checked)}
          />
        );
      },
    },
    {
      field: DATA_GRID_FIELDS.NAME,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.name'
      ] as string,
      minWidth: 200,
      renderCell: (params: GridCellParams) => <BasicTextCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.PLATFORM,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.channel'
      ] as string,
      width: 220,
      renderCell: (params: GridCellParams) => <ChannelCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.IS_OPTIONAL,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.consideration'
      ] as string,
      width: 180,
      renderCell: (params: GridCellParams) => <BasicTextCell params={params} />,
    },
    {
      field: DATA_GRID_FIELDS.CRITERIA_GROUPS,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.criteriaGroup'
      ] as string,
      width: 200,
      sortable: false,
      renderCell: (params: GridCellParams) => (
        <CriteriaGroupCell
          params={params}
          maxWidth={200}
          chipPadding={8}
          chipSx={{
            padding: '9px 10px',
          }}
        />
      ),
    },
    {
      field: DATA_GRID_FIELDS.IS_GLOBAL,
      headerName: intl.messages[
        'ui.creativeScoring.criteriaManagementV2.columnHeader.isGlobal'
      ] as string,
      width: 180,
      sortable: false,
      renderCell: (params: GridCellParams) => <BasicTextCell params={params} />,
    },
  ].filter(
    isCriteriaGroupsEnabled
      ? () => true
      : (column) => column.field !== DATA_GRID_FIELDS.CRITERIA_GROUPS,
  );

  if (isLoading) {
    return columns.map((column) => ({
      ...column,
      renderCell: () => <LoadingCell />,
    }));
  }

  return columns;
};
