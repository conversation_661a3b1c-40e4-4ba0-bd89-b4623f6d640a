import {
  BATCH_TYPES,
  Operator,
  REPORT_ADVANCED_FILTERS,
  REPORT_CRITERIA_FILTERS,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  REPORT_SCOPE_PARAMETERS_FILTERS,
  ReportAdvancedFilterDefinitions,
  ReportCriteriaFilterDefinitions,
  ReportFilterDefinitions,
  ReportScopeFilterDefinitions,
  ValueType,
} from '../../../../components/ReportFilters/types';
import { createElement } from 'react';
import { WorkspacesIcon } from '../../../../assets/vidmob-mui-icons/general/V2-WorkspacesIcon';
import {
  PLACEHOLDER_ALL_KEY,
  REPORTS_THAT_SUPPORT_ADHERENCE_FILTER,
  REPORTS_THAT_SUPPORT_CRITERIA_GROUPING,
  REPORTS_THAT_SUPPORT_CRITERIA_TAB,
  REPORTS_THAT_SUPPORT_SCORECARD_TYPE_FILTER,
} from '../constants';
import {
  BRAND_SELECTOR_INFO_TOOLTIP,
  DISABLED_AD_ACCOUNTS_FROM_SCORECARD_TYPE_KEY,
  DISABLED_SCORECARD_TYPE_IMPRESSION_ADHERENCE_REPORT_KEY,
  IN_FLIGHT_REPORT_CREATIVE_ADHERENCE_INFO_TOOLTIP,
  IN_FLIGHT_REPORT_CRITERIA_CONSIDERATION_INFO_TOOLTIP,
  MARKET_SELECTOR_INFO_TOOLTIP,
} from '../../../../components/ReportFilters/constants';
import { ScoreIcon } from '../../../../assets/vidmob-mui-icons/general/V2-ScoreIcon';
import { IntegrationIcon } from '../../../../assets/vidmob-mui-icons/general/V2-IntegrationIcon';
import { DataIcon } from '../../../../assets/vidmob-mui-icons/general/V2-DataIcon';
import { CreativeIcon } from '../../../../assets/vidmob-mui-icons/general/V2-CreativeIcon';
import {
  DataLevelType,
  ScoringReportType,
} from '../../../types/rollUpReports.types';
import {
  formatAdImpressionValue,
  formatCreativeAdherence,
  formatCreativeImpressionValue,
  formatCriteriaConsideration,
  formatLegacyBrands,
  formatLegacyChannels,
  formatLegacyDateRange,
  formatLegacyMarkets,
  formatLegacyMediaTypes,
  formatLegacyScorecardTypes,
  formatLegacyWorkspaces,
  formatOrganizationCriteria,
} from './formatters';
import {
  getBrandOptions,
  getChannelOptions,
  getCriteriaConsiderationOptions,
  getCriteriaGroupsOptions,
  getCriteriaResultsFilterOptions,
  getDefaultCriteriaResultsValue,
  getDefaultDateRangeFilterValue,
  getDefaultMediaTypeFilterValue,
  getDefaultScorecardTypeFilterValue,
  getMarketOptions,
  getMediaTypeOptions,
  getOrganizationCriteriaOptions,
  getScorecardTypeOptions,
  getWorkspaceOptions,
} from './getters';
import { CampaignIcon } from '../../../../assets/vidmob-mui-icons/general/V2-CampaignIcon';
import { CHANNELS } from '../../../../types/channels.types';
import {
  getAdPlacementOptions,
  getAdSetIdentifierOptions,
  getAdSetOptions,
  getAdTypeOptions,
  getCampaignObjectiveOptions,
  getCreativeByVidmobOptions,
  getDefaultSingleChannelFilterValue,
  getDefaultWorkspaceFilterValue,
} from '../../../../components/ReportFilters/utils/getters';
import { AdSetIcon } from '../../../../assets/vidmob-mui-icons/general/V2-AdSetIcon';
import { AdIcon } from '../../../../assets/vidmob-mui-icons/general/V2-AdIcon';
import { formatCreativeByVidmobValue } from '../../../../components/ReportFilters/utils/formatters';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import {
  isNumberPositive,
  isNumberWithinPercentageRange,
} from '../../../../components/ReportFilters/utils/typeCheckers';

const isCriteriaGroupsEnabled = getFeatureFlag('isCriteriaGroupsEnabled');

const getScopeFilterDefinitions = (
  reportType: ScoringReportType,
): ReportScopeFilterDefinitions => ({
  [REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]: {
    key: REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
    labelKey: 'ui.globalFilters.dateRange',
    valueType: ValueType.RANGE,
    operators: [Operator.BETWEEN],
    formatLegacy: formatLegacyDateRange,
    getDefaultValue: getDefaultDateRangeFilterValue,
  },
  [REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]: {
    key: REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
    labelKey: 'ui.globalFilters.workspaces',
    valueType: ValueType.MULTI,
    initialBlur: true,
    isDisabled: reportType === ScoringReportType.IN_FLIGHT,
    formatLegacy: formatLegacyWorkspaces,
    getOptions: getWorkspaceOptions,
    getDefaultValue: getDefaultWorkspaceFilterValue,
    icon: createElement(WorkspacesIcon),
  },
  ...(reportType !== ScoringReportType.IN_FLIGHT && {
    [REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE]: {
      key: REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
      labelKey: 'ui.globalFilters.assetSource',
      valueType: ValueType.SINGLE,
      initialBlur: true,
      formatLegacy: formatLegacyScorecardTypes,
      isDisabled:
        !REPORTS_THAT_SUPPORT_SCORECARD_TYPE_FILTER.includes(reportType),
      disabledTooltipKey:
        DISABLED_SCORECARD_TYPE_IMPRESSION_ADHERENCE_REPORT_KEY,
      getOptions: getScorecardTypeOptions,
      getDefaultValue: getDefaultScorecardTypeFilterValue,
      icon: createElement(ScoreIcon),
    },
  }),
  ...(reportType === ScoringReportType.IN_FLIGHT
    ? {
        [REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL]: {
          key: REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          labelKey: 'ui.globalFilters.channel',
          valueType: ValueType.SINGLE,
          initialBlur: true,
          getOptions: getChannelOptions,
          getDefaultValue: getDefaultSingleChannelFilterValue,
          icon: createElement(IntegrationIcon),
        },
      }
    : {
        [REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS]: {
          key: REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          labelKey: 'ui.globalFilters.channels',
          valueType: ValueType.MULTI,
          initialBlur: true,
          formatLegacy: formatLegacyChannels,
          getOptions: getChannelOptions,
          icon: createElement(IntegrationIcon),
        },
      }),
  [REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS]: {
    key: REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS,
    labelKey: 'ui.globalFilters.brands',
    placeholderOverrideKey: PLACEHOLDER_ALL_KEY,
    valueType: ValueType.MULTI,
    formatLegacy: formatLegacyBrands,
    getOptions: getBrandOptions,
    dependentOn: [REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES],
    icon: createElement(DataIcon),
    infoTooltipKey: BRAND_SELECTOR_INFO_TOOLTIP,
  },
  [REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS]: {
    key: REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS,
    labelKey: 'ui.globalFilters.markets',
    placeholderOverrideKey: PLACEHOLDER_ALL_KEY,
    valueType: ValueType.MULTI,
    formatLegacy: formatLegacyMarkets,
    getOptions: getMarketOptions,
    dependentOn: [REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES],
    icon: createElement(DataIcon),
    infoTooltipKey: MARKET_SELECTOR_INFO_TOOLTIP,
  },
  [REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS]: {
    key: REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
    labelKey: 'ui.globalFilters.adAccounts',
    placeholderOverrideKey: 'ui.globalFilters.adAccounts.placeholder',
    valueType: ValueType.GENERIC,
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    resetOn: [
      REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS,
      REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS,
    ],
    icon: createElement(IntegrationIcon),
    disabledTooltipKey: DISABLED_AD_ACCOUNTS_FROM_SCORECARD_TYPE_KEY,
  },
});

const getCriteriaFilterDefinitions = (
  reportType: ScoringReportType,
): Partial<ReportCriteriaFilterDefinitions> => ({
  [REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION]: {
    key: REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION,
    labelKey: 'ui.globalFilters.criteriaIsOptional',
    defaultOperator: Operator.EQUALS,
    valueType: ValueType.SINGLE,
    operators: [Operator.EQUALS],
    initialBlur: true,
    canDelete: true,
    formatValue: formatCriteriaConsideration,
    getOptions: getCriteriaConsiderationOptions,
    ...(reportType === ScoringReportType.IN_FLIGHT && {
      infoTooltipKey: IN_FLIGHT_REPORT_CRITERIA_CONSIDERATION_INFO_TOOLTIP,
    }),
  },
  ...(isCriteriaGroupsEnabled &&
    REPORTS_THAT_SUPPORT_CRITERIA_GROUPING.includes(reportType) && {
      [REPORT_CRITERIA_FILTERS.CRITERIA_GROUP]: {
        key: REPORT_CRITERIA_FILTERS.CRITERIA_GROUP,
        labelKey: 'ui.globalFilters.criteriaGroup',
        valueType: ValueType.MULTI,
        initialBlur: true,
        canDelete: true,
        getOptions: getCriteriaGroupsOptions,
      },
    }),
  [REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA]: {
    key: REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA,
    labelKey: 'ui.globalFilters.criteriaIsOrganizationCriteria',
    defaultOperator: Operator.EQUALS,
    valueType: ValueType.SINGLE,
    operators: [Operator.EQUALS],
    initialBlur: true,
    canDelete: true,
    formatValue: formatOrganizationCriteria,
    getOptions: getOrganizationCriteriaOptions,
  },
  [REPORT_CRITERIA_FILTERS.CRITERIA_RULE]: {
    key: REPORT_CRITERIA_FILTERS.CRITERIA_RULE,
    labelKey: 'ui.globalFilters.criteriaRule',
    placeholderOverrideKey: 'ui.globalFilters.criteriaRule.placeholder',
    valueType: ValueType.GENERIC,
    initialBlur: true,
    canDelete: false,
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    resetOn: [
      REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION,
      REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA,
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
    ],
  },
});

const getAdvancedFilterDefinitions = (
  reportType: ScoringReportType,
): ReportAdvancedFilterDefinitions => ({
  [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]: {
    key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.creativeMediaType',
    valueType: ValueType.MULTI,
    formatLegacy: formatLegacyMediaTypes,
    getOptions: getMediaTypeOptions,
    getDefaultValue: getDefaultMediaTypeFilterValue,
    icon: createElement(CreativeIcon),
    actsAsScopeFilter: true,
  },
  ...(reportType === ScoringReportType.IN_FLIGHT && {
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS]: {
      key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
      labelKey: 'ui.globalFilters.criteriaResults',
      defaultOperator: Operator.EQUALS,
      valueType: ValueType.MULTI,
      operators: [Operator.EQUALS],
      initialBlur: true,
      getOptions: getCriteriaResultsFilterOptions,
      getDefaultValue: getDefaultCriteriaResultsValue,
      icon: createElement(CreativeIcon),
      actsAsScopeFilter: true,
      disableOnActiveFilters: [
        REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
      ],
      disabledTooltipKey: 'ui.globalFilters.criteriaResults.disabled.tooltip',
    },
  }),
  ...(REPORTS_THAT_SUPPORT_ADHERENCE_FILTER.includes(reportType) && {
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]: {
      key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
      labelKey:
        'ui.creative.intelligence.filtersDrawer.filter.creativeAdherence',
      valueType: ValueType.TEXT,
      textFieldInputType: 'number',
      numberInputRule: isNumberWithinPercentageRange,
      operators: [Operator.GREATER_THAN, Operator.LESS_THAN],
      defaultOperator: Operator.GREATER_THAN,
      canDelete: true,
      formatValue: formatCreativeAdherence,
      suffix: '%',
      icon: createElement(CreativeIcon),
      actsAsScopeFilter: true,
      ...(reportType === ScoringReportType.IN_FLIGHT && {
        infoTooltipKey: IN_FLIGHT_REPORT_CREATIVE_ADHERENCE_INFO_TOOLTIP,
        disableOnActiveFilters: [
          REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
        ],
        disabledTooltipKey:
          'ui.creative.intelligence.filtersDrawer.filter.creativeAdherence.disabled.tooltip',
      }),
    },
  }),
  [REPORT_ADVANCED_FILTERS.CAMPAIGN_IDENTIFIER]: {
    key: REPORT_ADVANCED_FILTERS.CAMPAIGN_IDENTIFIER,
    labelKey:
      'ui.creative.intelligence.filtersDrawer.filter.campaignIdentifier',
    valueType: ValueType.GENERIC,
    icon: createElement(CampaignIcon),
    channels: [
      CHANNELS.FACEBOOK,
      CHANNELS.SNAPCHAT,
      CHANNELS.PINTEREST,
      CHANNELS.TWITTER,
      CHANNELS.ADWORDS,
      CHANNELS.DV360,
      CHANNELS.LINKEDIN,
      CHANNELS.TIKTOK,
      CHANNELS.AMAZONADVERTISING,
      CHANNELS.AMAZONADVERTISINGDSP,
      CHANNELS.REDDIT,
    ],
    placeholderOverrideKey:
      'ui.creative.intelligence.filtersDrawer.filter.campaignIdentifier.placeholder',
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
            REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
            REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.CAMPAIGN_OBJECTIVE]: {
    key: REPORT_ADVANCED_FILTERS.CAMPAIGN_OBJECTIVE,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.campaignObjective',
    valueType: ValueType.MULTI,
    hasGroupSelect: true,
    icon: createElement(CampaignIcon),
    getOptions: getCampaignObjectiveOptions,
    channels: [
      CHANNELS.FACEBOOK,
      CHANNELS.DV360,
      CHANNELS.LINKEDIN,
      CHANNELS.PINTEREST,
      CHANNELS.SNAPCHAT,
      CHANNELS.TIKTOK,
      CHANNELS.TWITTER,
    ],
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.AD_SET_IDENTIFIER]: {
    key: REPORT_ADVANCED_FILTERS.AD_SET_IDENTIFIER,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adSetIdentifier',
    valueType: ValueType.MULTI,
    icon: createElement(AdSetIcon),
    getOptions: getAdSetOptions,
    channels: [CHANNELS.FACEBOOK],
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
            REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
            REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.AD_IDENTIFIER]: {
    key: REPORT_ADVANCED_FILTERS.AD_IDENTIFIER,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adIdentifier',
    valueType: ValueType.MULTI,
    icon: createElement(AdIcon),
    getOptions: getAdSetIdentifierOptions,
    channels: [
      CHANNELS.FACEBOOK,
      CHANNELS.SNAPCHAT,
      CHANNELS.PINTEREST,
      CHANNELS.TWITTER,
      CHANNELS.ADWORDS,
      CHANNELS.DV360,
      CHANNELS.LINKEDIN,
      CHANNELS.TIKTOK,
      CHANNELS.AMAZONADVERTISING,
      CHANNELS.REDDIT,
    ],
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
            REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
            REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.AD_IMPRESSION]: {
    key: REPORT_ADVANCED_FILTERS.AD_IMPRESSION,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adImpression',
    valueType: ValueType.TEXT,
    textFieldInputType: 'number',
    numberInputRule: isNumberPositive,
    icon: createElement(AdIcon),
    defaultOperator: Operator.GREATER_THAN,
    operators: [Operator.GREATER_THAN, Operator.LESS_THAN],
    formatValue: formatAdImpressionValue,
    channels: [
      CHANNELS.FACEBOOK,
      CHANNELS.FACEBOOKPAGE,
      CHANNELS.INSTAGRAMPAGE,
      CHANNELS.SNAPCHAT,
      CHANNELS.PINTEREST,
      CHANNELS.TWITTER,
      CHANNELS.ADWORDS,
      CHANNELS.DV360,
      CHANNELS.LINKEDIN,
      CHANNELS.TIKTOK,
      CHANNELS.AMAZONADVERTISING,
      CHANNELS.AMAZONADVERTISINGDSP,
      CHANNELS.REDDIT,
    ],
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.AD_PLACEMENT]: {
    key: REPORT_ADVANCED_FILTERS.AD_PLACEMENT,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adPlacement',
    valueType: ValueType.MULTI,
    hasGroupSelect: true,
    icon: createElement(AdIcon),
    getOptions: getAdPlacementOptions,
    channels: [
      CHANNELS.FACEBOOK,
      CHANNELS.AMAZONADVERTISING,
      CHANNELS.LINKEDIN,
      CHANNELS.PINTEREST,
    ],
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.AD_TYPE]: {
    key: REPORT_ADVANCED_FILTERS.AD_TYPE,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.adType',
    valueType: ValueType.MULTI,
    icon: createElement(AdIcon),
    getOptions: getAdTypeOptions,
    channels: [CHANNELS.FACEBOOK, CHANNELS.ADWORDS, CHANNELS.DV360],
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.CREATIVE_IMPRESSION]: {
    key: REPORT_ADVANCED_FILTERS.CREATIVE_IMPRESSION,
    labelKey:
      'ui.creative.intelligence.filtersDrawer.filter.creativeImpression',
    valueType: ValueType.TEXT,
    textFieldInputType: 'number',
    numberInputRule: isNumberPositive,
    icon: createElement(CreativeIcon),
    defaultOperator: Operator.GREATER_THAN,
    operators: [Operator.GREATER_THAN, Operator.LESS_THAN],
    formatValue: formatCreativeImpressionValue,
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    hideOnDataLevel: DataLevelType.AD,
    canDelete: true,
  },
  [REPORT_ADVANCED_FILTERS.CREATIVE_BY_VIDMOB]: {
    key: REPORT_ADVANCED_FILTERS.CREATIVE_BY_VIDMOB,
    labelKey: 'ui.creative.intelligence.filtersDrawer.filter.createdByVidmob',
    valueType: ValueType.MULTI,
    icon: createElement(CreativeIcon),
    getOptions: getCreativeByVidmobOptions,
    formatValue: formatCreativeByVidmobValue,
    dependentOn:
      reportType === ScoringReportType.IN_FLIGHT
        ? [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
          ]
        : [
            REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
            REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
          ],
    disableOnBatchType: BATCH_TYPES.PRE_FLIGHT,
    hideOnDataLevel: DataLevelType.AD,
    canDelete: true,
  },
});

export const getFiltersDefinitions = (
  reportType: ScoringReportType,
): ReportFilterDefinitions => ({
  ...getScopeFilterDefinitions(reportType),
  ...(REPORTS_THAT_SUPPORT_CRITERIA_TAB.includes(reportType) &&
    getCriteriaFilterDefinitions(reportType)),
  advancedFilters: getAdvancedFilterDefinitions(reportType),
});
