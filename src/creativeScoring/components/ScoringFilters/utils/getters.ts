import {
  AdvancedFilterForBffType,
  AdvancedFiltersChannelType,
  AdvancedFiltersStorage,
  BackEndReportAdvancedFilterKey,
  BATCH_TYPES,
  FiltersStorage,
  FilterType,
  FilterValue,
  GetOptionsParams,
  ListItem,
  Operator,
  REPORT_CRITERIA_FILTERS,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  REPORT_SCOPE_PARAMETERS_FILTERS,
  ReportAdvancedFilterKey,
  ReportFilterDefinitions,
  SingleValue,
} from '../../../../components/ReportFilters/types';
import {
  ADVANCED_FILTERS_TO_BACKEND_MAP,
  CRITERIA_CONSIDERATION_FILTER_OPTIONS,
  ORGANIZATION_CRITERIA_FILTER_OPTIONS,
} from '../../../../components/ReportFilters/constants';
import {
  convertDateToISODateString,
  getLastThreeMonthsFromYesterday,
} from '../../../../utils/dateRangePickerMUIUtils';
import { PLATFORM } from '../../../../constants';
import {
  CRITERIA_RESULTS_FILTER_DEFAULT_OPTIONS,
  CRITERIA_RESULTS_FILTER_OPTIONS,
  IN_FLIGHT_SCORECARD_FILTER_OPTION,
  REPORTS_THAT_SUPPORT_ADHERENCE_FILTER,
  SCORECARD_FILTER_OPTIONS,
  SCORING_ADHERENCE_FILTER,
  SCORING_FILTERS_SUPPORTED_CHANNELS,
  SCORING_MEDIA_TYPE_FILTER_OPTIONS,
} from '../constants';
import { getWorkspaces } from '../services/getWorkspaces';
import { getIntl } from '../../../../utils/getIntl';
import { getPlatformDisplayIntlText } from '../../../../utils/feConstantsUtils';
import { getAllBrandsFromBff } from '../services/getBrands';
import { isListItemArray } from '../../../../components/ReportFilters/utils/typeCheckers';
import { getAllMarketsFromBff } from '../services/getMarkets';
import { CHANNELS } from '../../../../types/channels.types';
import { ScoringReportType } from '../../../types/rollUpReports.types';
import { IdAndName } from '../../../../types/common.types';
import { fetchCriteriaGroupsList } from '../../criteriaManagement/CriteriaGroupingManagement/helpers';

export const getDefaultDateRangeFilterValue = (): FilterValue => ({
  key: REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
  value: getLastThreeMonthsFromYesterday().map(convertDateToISODateString),
  operator: Operator.BETWEEN,
});

export const getWorkspaceOptions = async ({
  organizationId,
}: GetOptionsParams): Promise<ListItem[]> => {
  const workspaces = await getWorkspaces(organizationId);

  return workspaces.map((workspace) => ({
    id: workspace.id,
    name: workspace.name,
  }));
};

export const getScorecardTypeOptions = async (): Promise<ListItem[]> =>
  SCORECARD_FILTER_OPTIONS;

export const getDefaultScorecardTypeFilterValue = (): FilterValue => ({
  key: REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
  value: IN_FLIGHT_SCORECARD_FILTER_OPTION,
  operator: Operator.IN,
});

export const getChannelOptions = async (): Promise<ListItem[]> => {
  const intl = getIntl();

  return SCORING_FILTERS_SUPPORTED_CHANNELS.map((channelId) => ({
    id: channelId,
    name: getPlatformDisplayIntlText(channelId, intl) || '',
  }));
};

export const getBrandOptions = async ({
  organizationId,
}: GetOptionsParams): Promise<ListItem[]> => {
  const brands = await getAllBrandsFromBff(organizationId);

  return brands.map((brand) => ({
    id: brand.id,
    name: brand.name,
  }));
};

export const getMarketOptions = async ({
  filters,
}: GetOptionsParams): Promise<ListItem[]> => {
  const workspaces =
    filters?.[REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]?.value;
  if (!workspaces || !isListItemArray(workspaces)) {
    return [];
  }

  const markets = await getAllMarketsFromBff();

  return markets.map((market) => ({
    id: market.id,
    name: market.name,
  }));
};

export const getCriteriaGroupsOptions = async ({
  organizationId,
  filters,
}: GetOptionsParams): Promise<ListItem[]> => {
  const workspaces =
    filters?.[REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]?.value;
  if (!workspaces || !isListItemArray(workspaces)) {
    return [];
  }

  const criteriaGroups = await fetchCriteriaGroupsList({
    organizationId,
    workspaceId: workspaces[0].id as number,
    offset: 0,
    perPage: 1000,
  });

  return criteriaGroups.data.map((criteriaGroup: IdAndName) => ({
    id: criteriaGroup.id,
    name: criteriaGroup.name,
  }));
};

export const getCriteriaConsiderationOptions = async (): Promise<ListItem[]> =>
  CRITERIA_CONSIDERATION_FILTER_OPTIONS;

export const getOrganizationCriteriaOptions = async (): Promise<ListItem[]> =>
  ORGANIZATION_CRITERIA_FILTER_OPTIONS;

export const getMediaTypeOptions = async (): Promise<ListItem[]> =>
  SCORING_MEDIA_TYPE_FILTER_OPTIONS;

export const getDefaultMediaTypeFilterValue = (): FilterValue => ({
  key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
  value: SCORING_MEDIA_TYPE_FILTER_OPTIONS,
  operator: Operator.IN,
});

export const getFinalChannelKeyName = (channel: string): string =>
  channel === CHANNELS.AMAZONADVERTISING
    ? PLATFORM.AMAZON_ATTRIBUTION.IDENTIFIER
    : channel;

const getFormattedAdvancedFiltersForChannel = (
  filters: Record<ReportAdvancedFilterKey, FilterValue>,
  filterDefinitions: Partial<ReportFilterDefinitions>,
): AdvancedFilterForBffType[] | undefined =>
  Object.entries(filters).reduce(
    (acc: AdvancedFilterForBffType[] | undefined, [key, filter]) => {
      const filterDefinition =
        filterDefinitions.advancedFilters?.[key as ReportAdvancedFilterKey];
      const formattedValue = filterDefinition?.formatValue
        ? filterDefinition.formatValue(filter.value, filter.operator)
        : undefined;

      if (
        formattedValue === null ||
        !filter.value ||
        (Array.isArray(filter.value) && filter.value.length === 0)
      ) {
        return acc;
      }

      if (formattedValue) {
        return [
          ...(acc || []),
          formattedValue as unknown as AdvancedFilterForBffType,
        ];
      }

      return [
        ...(acc || []),
        {
          type: (ADVANCED_FILTERS_TO_BACKEND_MAP[
            key as ReportAdvancedFilterKey
          ] || key) as BackEndReportAdvancedFilterKey,
          operator: filter.operator || Operator.EQUALS,
          values: Array.isArray(filter.value)
            ? filter.value.map((item) => item.id || item.toString())
            : (filter.value as string[]),
        },
      ];
    },
    // do not create an array unless we are actually adding a filter
    undefined,
  );

export const getFormattedAdvancedFilters = (
  advancedFiltersValues: AdvancedFiltersStorage,
  filterDefinitions: Partial<ReportFilterDefinitions>,
) => {
  const formattedFilters: Partial<
    Record<AdvancedFiltersChannelType, AdvancedFilterForBffType[]>
  > = Object.entries(advancedFiltersValues).reduce(
    (acc, [channel, filters]) => {
      const formattedFiltersForChannel = getFormattedAdvancedFiltersForChannel(
        filters as Record<ReportAdvancedFilterKey, FilterValue>,
        filterDefinitions,
      );

      if (formattedFiltersForChannel?.length) {
        const key = getFinalChannelKeyName(channel);
        return {
          ...acc,
          [key]: formattedFiltersForChannel,
        };
      }

      return acc;
    },
    {},
  );

  return formattedFilters;
};

export const getAreInflightReportFiltersValid = (
  filters: FiltersStorage,
): boolean => {
  const dateRange = filters[REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]?.value;
  const hasDateRange = Array.isArray(dateRange) && dateRange.length === 2;
  const workspaces = filters[REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]?.value;
  const hasWorkspaces = Array.isArray(workspaces) && workspaces.length > 0;
  const channel = filters[REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL]?.value;
  const hasChannel = Boolean(channel) && !Array.isArray(channel);

  return hasDateRange && hasWorkspaces && hasChannel;
};

const getAreRollupReportFiltersValid = (filters: FiltersStorage): boolean => {
  const dateRange = filters[REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]?.value;
  const hasDateRange = Array.isArray(dateRange) && dateRange.length === 2;
  const workspaces = filters[REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]?.value;
  const hasWorkspaces = Array.isArray(workspaces) && workspaces.length > 0;
  const scorecardTypeValue = filters[REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE]
    ?.value as SingleValue;
  const scorecardType = scorecardTypeValue?.id;
  const hasValidScorecardType = scorecardType in BATCH_TYPES;
  const channels = filters[REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS]?.value;
  const hasChannels = Array.isArray(channels) && channels.length > 0;
  const areCriteriaFiltersValid =
    getAreScoringReportCriteriaFiltersValid(filters);

  return (
    hasDateRange &&
    hasWorkspaces &&
    hasValidScorecardType &&
    hasChannels &&
    areCriteriaFiltersValid
  );
};

export const getScoringReportFilterValidatorFunction = (
  reportType: ScoringReportType,
): ((filters: FiltersStorage) => boolean) => {
  if (reportType === ScoringReportType.IN_FLIGHT) {
    return getAreInflightReportFiltersValid;
  }

  return getAreRollupReportFiltersValid;
};

const getAreScoringReportCriteriaFiltersValid = (
  filters: FiltersStorage,
): boolean => {
  const criteriaConsiderationFilter =
    filters[REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION];
  const criteriaOrganizationFilter =
    filters[REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA];

  if (criteriaConsiderationFilter && !criteriaOrganizationFilter) {
    return Boolean(criteriaConsiderationFilter.value);
  }

  if (!criteriaConsiderationFilter && criteriaOrganizationFilter) {
    return Boolean(criteriaOrganizationFilter.value);
  }

  if (criteriaConsiderationFilter && criteriaOrganizationFilter) {
    return (
      Boolean(criteriaConsiderationFilter.value) &&
      Boolean(criteriaOrganizationFilter.value)
    );
  }

  return true;
};

export const getScoringFilterOptionsForAllChannels = (
  reportType: ScoringReportType,
): FilterType[] =>
  REPORTS_THAT_SUPPORT_ADHERENCE_FILTER.includes(reportType)
    ? [SCORING_ADHERENCE_FILTER]
    : [];

export const getCriteriaResultsFilterOptions = async (): Promise<ListItem[]> =>
  CRITERIA_RESULTS_FILTER_OPTIONS;

export const getDefaultCriteriaResultsValue = (): FilterValue => ({
  key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
  value: CRITERIA_RESULTS_FILTER_DEFAULT_OPTIONS,
  operator: Operator.IN,
});
