import {
  FilterType,
  REPORT_ADVANCED_FILTERS,
  REPORT_CRITERIA_FILTERS,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  REPORT_SCOPE_PARAMETERS_FILTERS,
  ReportAdvancedFilterKey,
  ReportControlBarDropdownItem,
  ReportCriteriaFilterKey,
  ReportFilterKey,
  ReportPillarSpecificAdvancedFilterKey,
  ReportScopeParametersFilterKey,
} from '../../../components/ReportFilters/types';
import { COMPLIANCE } from '../../../constants';
import { CHANNELS, ChannelType } from '../../../types/channels.types';
import { SCORING_REPORT_FILTERS, ScoringReportFilterKey } from './types';
import {
  CriteriaResult,
  ScorecardType,
  ScoringReportType,
} from '../../types/rollUpReports.types';
import { IdAndName } from '../../../types/common.types';
import { getIntl } from '../../../utils/getIntl';
import { MediaType } from '../../../types/mediaTypes.types';
import { MEDIA_TYPE_FILTER_OPTIONS } from '../../../components/ReportFilters/constants';

const intl = getIntl();

const { ALL_PLATFORMS_TEMPLATE_IDENTIFIER } = COMPLIANCE;

export const SCORING_FILTERS_LOCAL_STORAGE_KEY = 'scoringFilters';
export const SCORING_ADVANCED_FILTERS_LOCAL_STORAGE_KEY =
  'scoringAdvancedFilters';
export const SCOPE_TAB_KEY = 'ui.reportFilters.tabsLabels.scope';
export const FILTERS_TAB_KEY = 'ui.reportFilters.tabsLabels.filters';
export const CRITERIA_TAB_KEY = 'ui.reportFilters.tabsLabels.criteria';

export const DISABLED_FILTERS_TAB_TOOLTIP_KEY =
  'ui.reportFilters.tabs.filters.disabledTooltip';
export const DISABLED_CRITERIA_TAB_TOOLTIP_KEY =
  'ui.criteriaFilters.tabs.filters.disabledTooltip';
export const AD_ACCOUNTS_PANEL_TITLE_KEY =
  'ui.reportFilters.adAccounts.panelTitle';
export const ALL_CHANNELS_KEY = 'ui.reportFilters.tabs.filters.allChannels';
export const PLACEHOLDER_ALL_KEY = 'ui.reportFilters.multiValue.placeholder';
export const ALL_CHANNELS_TOOLTIP_KEY =
  'ui.reportFilters.tabs.filters.allChannels.info';
const ADHERENCE_RANGE_FILTER_KEY =
  'ui.reportFilters.tabs.filters.adherenceRange';

export const SCORING_FILTERS_SUPPORTED_CHANNELS: ChannelType[] = [
  CHANNELS.AMAZONADVERTISING,
  CHANNELS.AMAZONADVERTISINGDSP,
  CHANNELS.DV360,
  CHANNELS.ADWORDS,
  CHANNELS.LINKEDIN,
  CHANNELS.FACEBOOK,
  CHANNELS.PINTEREST,
  CHANNELS.REDDIT,
  CHANNELS.SNAPCHAT,
  CHANNELS.TIKTOK,
  CHANNELS.TWITTER,
];

export const SCORING_PLATFORMS_WITHOUT_ADVANCED_FILTERS: string[] = [
  ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
];

export const SCORING_ADHERENCE_FILTER: FilterType = {
  id: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
  labelKey: ADHERENCE_RANGE_FILTER_KEY,
};

export const BATCH_TYPE_CONSTANT = 'batchType';
export const CRITERIA_CONSTANT = 'criteria';

const PRE_FLIGHT_SCORECARD_FILTER_OPTION: IdAndName = {
  id: ScorecardType.PRE_FLIGHT,
  name: intl.formatMessage({
    id: 'ui.creativeScoring.rollUpReports.reportTypeFilter.value.preflight',
    defaultMessage: 'Pre-flight',
  }),
};

export const IN_FLIGHT_SCORECARD_FILTER_OPTION: IdAndName = {
  id: ScorecardType.IN_FLIGHT,
  name: intl.formatMessage({
    id: 'ui.creativeScoring.rollUpReports.reportTypeFilter.value.inflight',
    defaultMessage: 'In-flight',
  }),
};

export const SCORECARD_FILTER_OPTIONS: IdAndName[] = [
  PRE_FLIGHT_SCORECARD_FILTER_OPTION,
  IN_FLIGHT_SCORECARD_FILTER_OPTION,
];

export const VIEW_BY_OPTIONS = {
  MONTH: 'month',
  QUARTER: 'quarter',
};

export const SCORING_REPORT_VIEW_BY_OPTIONS: ReportControlBarDropdownItem[] = [
  {
    id: 'monthly',
    label: 'ui.creativeScoring.rollUpReports.viewByFilter.value.monthly',
    value: [VIEW_BY_OPTIONS.MONTH, BATCH_TYPE_CONSTANT],
  },
  {
    id: 'quarterly',
    label: 'ui.creativeScoring.rollUpReports.viewByFilter.value.quarterly',
    value: [VIEW_BY_OPTIONS.QUARTER, BATCH_TYPE_CONSTANT],
  },
];

export const NON_ADOPTION_REPORT_DEFAULT_GROUP_BY_COLUMNS = [
  CRITERIA_CONSTANT,
  BATCH_TYPE_CONSTANT,
];

export const ADHERENCE_DEFAULT_ROWS = [CRITERIA_CONSTANT];

export const SCOPE_TAB_FILTERS: ReportScopeParametersFilterKey[] = [
  REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
  REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
  REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
  REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS,
  REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS,
  REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
];

export const SCORING_REPORT_TYPES_TO_SCOPE_FILTER_KEYS: Record<
  ScoringReportType,
  ReportScopeParametersFilterKey[]
> = {
  [ScoringReportType.ADHERENCE]: SCOPE_TAB_FILTERS,
  [ScoringReportType.IMPRESSION_ADHERENCE]: SCOPE_TAB_FILTERS,
  [ScoringReportType.ADOPTION]: SCOPE_TAB_FILTERS,
  [ScoringReportType.IN_FLIGHT]: [
    REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
    REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS,
    REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS,
    REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
  ],
  [ScoringReportType.DIVERSITY]: SCOPE_TAB_FILTERS,
};

export const IN_FLIGHT_REPORT_SPECIFIC_FILTERS: ReportFilterKey[] = [
  REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
];

export const CRITERIA_TAB_FILTERS = [
  REPORT_CRITERIA_FILTERS.CRITERIA_RULE,
  REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION,
  REPORT_CRITERIA_FILTERS.CRITERIA_GROUP,
  REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA,
];

export const FILTER_TAB_FILTERS: ReportAdvancedFilterKey[] = [
  REPORT_ADVANCED_FILTERS.CAMPAIGN_IDENTIFIER,
  REPORT_ADVANCED_FILTERS.CAMPAIGN_OBJECTIVE,
  REPORT_ADVANCED_FILTERS.AD_SET_IDENTIFIER,
  REPORT_ADVANCED_FILTERS.AD_IDENTIFIER,
  REPORT_ADVANCED_FILTERS.AD_IMPRESSION,
  REPORT_ADVANCED_FILTERS.AD_PLACEMENT,
  REPORT_ADVANCED_FILTERS.AD_TYPE,
  REPORT_ADVANCED_FILTERS.CREATIVE_IMPRESSION,
  REPORT_ADVANCED_FILTERS.CREATIVE_BY_VIDMOB,
];

export const ALL_CHANNELS_FILTERS: ReportPillarSpecificAdvancedFilterKey[] = [
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
];

export const REPORTS_THAT_SUPPORT_NORMS: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
];

export const REPORTS_THAT_SUPPORT_BREAKDOWN_BY: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
  ScoringReportType.IMPRESSION_ADHERENCE,
];

export const REPORTS_THAT_SUPPORT_DEEP_NESTING: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
  ScoringReportType.IMPRESSION_ADHERENCE,
];

export const REPORTS_THAT_SUPPORT_SCORECARD_TYPE_FILTER: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
  ScoringReportType.ADOPTION,
  ScoringReportType.DIVERSITY,
];

export const REPORTS_THAT_SUPPORT_ADHERENCE_FILTER: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
  ScoringReportType.IMPRESSION_ADHERENCE,
  ScoringReportType.DIVERSITY,
  ScoringReportType.IN_FLIGHT,
];

export const REPORTS_THAT_SUPPORT_VIEW_BY: ScoringReportType[] = [
  ScoringReportType.ADOPTION,
];

export const REPORTS_THAT_SUPPORT_GROUP_BY: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
  ScoringReportType.IMPRESSION_ADHERENCE,
  ScoringReportType.ADOPTION,
];

export const REPORTS_THAT_SUPPORT_CRITERIA_TAB: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
  ScoringReportType.IMPRESSION_ADHERENCE,
  ScoringReportType.ADOPTION,
  ScoringReportType.IN_FLIGHT,
];

export const REPORTS_THAT_SUPPORT_DATA_LEVEL: ScoringReportType[] = [
  ScoringReportType.IMPRESSION_ADHERENCE,
  ScoringReportType.ADOPTION,
  ScoringReportType.ADHERENCE,
];

export const REPORTS_THAT_SUPPORT_CRITERIA_GROUPING: ScoringReportType[] = [
  ScoringReportType.ADHERENCE,
  ScoringReportType.IMPRESSION_ADHERENCE,
  ScoringReportType.IN_FLIGHT,
];

export const SCORING_REPORT_FILTER_KEY_TO_REPORT_FILTER_KEY: Record<
  ScoringReportFilterKey,
  | ReportScopeParametersFilterKey
  | ReportPillarSpecificAdvancedFilterKey
  | ReportCriteriaFilterKey
> = {
  [SCORING_REPORT_FILTERS.DATE]: REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
  [SCORING_REPORT_FILTERS.WORKSPACE]:
    REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES,
  [SCORING_REPORT_FILTERS.BATCH_TYPE]:
    REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
  [SCORING_REPORT_FILTERS.CHANNEL]: REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS,
  [SCORING_REPORT_FILTERS.BRAND]: REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS,
  [SCORING_REPORT_FILTERS.MARKET]: REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS,
  [SCORING_REPORT_FILTERS.AD_ACCOUNT]:
    REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS,
  [SCORING_REPORT_FILTERS.CREATIVE_MEDIA_TYPE]:
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
  [SCORING_REPORT_FILTERS.CREATIVE_ADHERENCE]:
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
  [SCORING_REPORT_FILTERS.NORMS_CONFIGURATION]:
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.NORMS_CONFIGURATION,
  [SCORING_REPORT_FILTERS.DATA_LEVEL]:
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL,
  [SCORING_REPORT_FILTERS.CRITERIA_RESULTS]:
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
  [SCORING_REPORT_FILTERS.CRITERIA_RULE]: REPORT_CRITERIA_FILTERS.CRITERIA_RULE,
  [SCORING_REPORT_FILTERS.CRITERIA_CONSIDERATION]:
    REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION,
  [SCORING_REPORT_FILTERS.CRITERIA_GROUP]:
    REPORT_CRITERIA_FILTERS.CRITERIA_GROUP,
  [SCORING_REPORT_FILTERS.ORGANIZATION_CRITERIA]:
    REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA,
};

export const REPORT_FILTER_KEY_TO_SCORING_REPORT_FILTER_KEY: Record<
  | ReportScopeParametersFilterKey
  | ReportPillarSpecificAdvancedFilterKey
  | ReportCriteriaFilterKey,
  ScoringReportFilterKey
> = {
  [REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]: SCORING_REPORT_FILTERS.DATE,
  [REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]:
    SCORING_REPORT_FILTERS.WORKSPACE,
  [REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE]:
    SCORING_REPORT_FILTERS.BATCH_TYPE,
  [REPORT_SCOPE_PARAMETERS_FILTERS.CHANNELS]: SCORING_REPORT_FILTERS.CHANNEL,
  [REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL]: SCORING_REPORT_FILTERS.CHANNEL,
  [REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS]: SCORING_REPORT_FILTERS.BRAND,
  [REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS]: SCORING_REPORT_FILTERS.MARKET,
  [REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS]:
    SCORING_REPORT_FILTERS.AD_ACCOUNT,
  [REPORT_SCOPE_PARAMETERS_FILTERS.SCOPE_AD_ACCOUNTS]:
    SCORING_REPORT_FILTERS.AD_ACCOUNT,
  [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]:
    SCORING_REPORT_FILTERS.CREATIVE_MEDIA_TYPE,
  [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]:
    SCORING_REPORT_FILTERS.CREATIVE_ADHERENCE,
  [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.NORMS_CONFIGURATION]:
    SCORING_REPORT_FILTERS.NORMS_CONFIGURATION,
  [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL]:
    SCORING_REPORT_FILTERS.DATA_LEVEL,
  [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS]:
    SCORING_REPORT_FILTERS.CRITERIA_RESULTS,
  [REPORT_CRITERIA_FILTERS.CRITERIA_RULE]: SCORING_REPORT_FILTERS.CRITERIA_RULE,
  [REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION]:
    SCORING_REPORT_FILTERS.CRITERIA_CONSIDERATION,
  [REPORT_CRITERIA_FILTERS.CRITERIA_GROUP]:
    SCORING_REPORT_FILTERS.CRITERIA_GROUP,
  [REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA]:
    SCORING_REPORT_FILTERS.ORGANIZATION_CRITERIA,
};

export const SCORING_MEDIA_TYPE_FILTER_OPTIONS: IdAndName[] = [
  ...MEDIA_TYPE_FILTER_OPTIONS,
  {
    id: MediaType.ANIMATED_IMAGE,
    name: intl.formatMessage({
      id: 'ui.reportFilters.tabs.filters.mediaType.animatedImage',
      defaultMessage: 'GIF',
    }),
  },
  {
    id: MediaType.HTML,
    name: intl.formatMessage({
      id: 'ui.reportFilters.tabs.filters.mediaType.html',
      defaultMessage: 'Display',
    }),
  },
];

export const CRITERIA_RESULTS_FILTER_OPTIONS: IdAndName[] = [
  {
    id: CriteriaResult.PASS,
    name: intl.formatMessage({
      id: 'ui.compliance.contentAuditResult.filter.option.criteriaMet',
      defaultMessage: 'Met Criteria',
    }),
  },
  {
    id: CriteriaResult.FAIL,
    name: intl.formatMessage({
      id: 'ui.compliance.contentAuditResult.filter.option.criteriaNotMet',
      defaultMessage: 'Did Not Meet Criteria',
    }),
  },
  {
    id: CriteriaResult.NO_DATA,
    name: intl.formatMessage({
      id: 'ui.compliance.contentAuditResult.filter.option.notAvailable',
      defaultMessage: 'Not Available',
    }),
  },
];

export const CRITERIA_RESULTS_FILTER_DEFAULT_OPTIONS =
  CRITERIA_RESULTS_FILTER_OPTIONS;
