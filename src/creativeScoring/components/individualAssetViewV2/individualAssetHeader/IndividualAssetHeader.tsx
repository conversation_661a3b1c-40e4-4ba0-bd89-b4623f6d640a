import React from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import PageHeaderV2 from '../../../../components/PageHeaderV2';
import {
  VidMobStack,
  VidMobSkeleton,
} from '../../../../vidMobComponentWrappers';
import IndividualAssetHeaderSubtitle from '../individualAssetHeaderSubtitle/IndividualAssetHeaderSubtitle';
import IndividualAssetHeaderButtons from '../individualAssetHeaderButtons/IndividualAssetHeaderButtons';
import { getScorecardsLandingViewLink } from '../../../redux/selectors/complianceShared.selectors';
import { PlatformAdAccountType } from '../../../../types/adAccount.types';
import { BreadcrumbType } from '../../../../components/PageHeaderV2/PageHeaderV2';
import { individualAssetHeaderStyles } from '../individualAssetViewStyles';
import { AdvancedFiltersChannelType } from '../../../../components/ReportFilters/types';
import { MediaObjectType } from '../individualAsset.types';

const { loadingStackStyle } = individualAssetHeaderStyles;

type Props = {
  isPreFlight: boolean;
  isPluginMedia: boolean;
  workspaceId: number;
  channels: AdvancedFiltersChannelType[];
  mediaId: string;
  mediaObject: MediaObjectType;
  isMediaLoading: boolean;
  hasMediaError: boolean;
  areScoresLoading: boolean;
  hasScoresError: boolean;
  scorecardName: string;
  platformAdAccount: PlatformAdAccountType | undefined;
  isVersionPanelOpen: boolean;
  setIsVersionPanelOpen: (isVersionPanelOpen: boolean) => void;
  backLink: string;
};

const IndividualAssetHeader = ({
  isPreFlight,
  isPluginMedia,
  workspaceId,
  channels,
  mediaId,
  mediaObject,
  isMediaLoading,
  hasMediaError,
  areScoresLoading,
  hasScoresError,
  scorecardName,
  platformAdAccount,
  isVersionPanelOpen,
  setIsVersionPanelOpen,
  backLink,
}: Props) => {
  const intl = useIntl();
  const history = useHistory();

  const backToScorecardsLink = useSelector(getScorecardsLandingViewLink);

  const mediaName = mediaObject?.displayName || mediaObject?.name || '';
  const mediaDownloadUrl = mediaObject?.downloadUrl || '';

  const handleBreadcrumbClick = (breadcrumb: BreadcrumbType) => {
    if (isPluginMedia) {
      history.push(backToScorecardsLink);
    } else if (
      breadcrumb.label === scorecardName ||
      breadcrumb.label === platformAdAccount?.platformAccountName
    ) {
      history.push(backLink, {
        needsReload: false,
        fromBrandGovernanceIndividualAsset: true,
        isFromAssetView: true,
      });
    } else {
      history.push(backToScorecardsLink);
    }
  };

  const getPreviousPageLabel = () => {
    if (isPreFlight) {
      return intl.messages['individualCreativeViewV2.breadcrumb.preflight'];
    } else if (isPluginMedia) {
      return intl.messages[
        'individualCreativeViewV2.breadcrumb.creativeDrafts'
      ];
    }

    return '';
  };

  const getBreadcrumbs = () => {
    const breadcrumbs = [
      {
        label: getPreviousPageLabel(),
        url: '',
      },
    ];

    if (!isPluginMedia) {
      breadcrumbs.push({
        label: scorecardName,
        url: '',
      });
    }

    return breadcrumbs;
  };

  const renderContent = () => {
    if (isMediaLoading) {
      return (
        <VidMobStack gap="16px" sx={loadingStackStyle}>
          <VidMobSkeleton variant="rounded" width={150} height={12} />
          <VidMobSkeleton variant="rounded" width={250} height={21} />
          <VidMobSkeleton variant="rounded" width={150} height={12} />
        </VidMobStack>
      );
    }

    return (
      <>
        <PageHeaderV2
          title={mediaName}
          breadcrumbActionOverride={handleBreadcrumbClick}
          breadcrumbs={getBreadcrumbs()}
        >
          <IndividualAssetHeaderButtons
            isPreFlight={isPreFlight}
            isPluginMedia={isPluginMedia}
            isVersionPanelOpen={isVersionPanelOpen}
            setIsVersionPanelOpen={setIsVersionPanelOpen}
            workspaceId={workspaceId}
            mediaId={mediaId}
            mediaName={mediaName}
            mediaDownloadUrl={mediaDownloadUrl}
            channels={channels}
            isMediaLoading={isMediaLoading}
            hasMediaError={hasMediaError}
            areScoresLoading={areScoresLoading}
            hasScoresError={hasScoresError}
          />
        </PageHeaderV2>
        <IndividualAssetHeaderSubtitle
          isPreFlight={isPreFlight}
          isPluginMedia={isPluginMedia}
          platformAdAccount={platformAdAccount}
        />
      </>
    );
  };

  return <>{renderContent()}</>;
};

export default IndividualAssetHeader;
