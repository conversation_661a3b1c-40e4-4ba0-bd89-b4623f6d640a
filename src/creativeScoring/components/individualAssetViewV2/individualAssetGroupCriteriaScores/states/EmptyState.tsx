import React from 'react';
import { useIntl } from 'react-intl';
import { BlankOrErrorState } from '../../../../../muiCustomComponents/BlankState/BlankState';
import { CriteriaIcon } from '../../../../../assets/vidmob-mui-icons/general';
import NavigateToCriteriaManagementButton from './NavigateToCriteriaManagementButton';
import {
  NO_SEARCH_RESULTS_MESSAGE_DEFAULT_KEYWORD_INTL_KEY,
  NO_SEARCH_RESULTS_MESSAGE_INTL_KEY,
  NO_SEARCH_RESULTS_TITLE_INTL_KEY,
} from '../../../../../creativeAnalytics/__pages/ImpactReport/ImpactReportConstants';
import { emptyStateContainerSx } from './styles';
import { VidMobBox } from '../../../../../vidMobComponentWrappers';

interface Props {
  searchTerm?: string;
}

export const EmptyState = ({ searchTerm }: Props) => {
  const intl = useIntl();

  if (searchTerm) {
    const title = intl.formatMessage({
      id: NO_SEARCH_RESULTS_TITLE_INTL_KEY,
      defaultMessage: 'No matching results',
    });

    const searchQuery =
      searchTerm ||
      intl.formatMessage({
        id: NO_SEARCH_RESULTS_MESSAGE_DEFAULT_KEYWORD_INTL_KEY,
        defaultMessage: 'search keyword',
      });

    const message = intl.formatMessage(
      {
        id: NO_SEARCH_RESULTS_MESSAGE_INTL_KEY,
        defaultMessage: `We couldn't find any matching results for search term ${searchQuery}. Please try again.`,
      },
      { searchQuery },
    );

    return (
      <VidMobBox sx={emptyStateContainerSx}>
        <BlankOrErrorState stateType="blank" title={title} message={message} />
      </VidMobBox>
    );
  }

  return (
    <VidMobBox sx={emptyStateContainerSx}>
      <BlankOrErrorState
        iconComponent={<CriteriaIcon />}
        title={intl.formatMessage({
          id: 'individualCreativeViewV2.scores.noData.title',
          defaultMessage: 'No criteria has been added',
        })}
        message={intl.formatMessage({
          id: 'individualCreativeViewV2.scores.noData.subtitle',
          defaultMessage:
            'No criteria found for this report. Go to your criteria management page to see available criteria.',
        })}
        buttonComponent={<NavigateToCriteriaManagementButton />}
      />
    </VidMobBox>
  );
};
