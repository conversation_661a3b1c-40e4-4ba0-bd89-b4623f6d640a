import React from 'react';
import { useIntl } from 'react-intl';
import { generatePath } from 'react-router';
import history from '../../../../../routing/history';
import siteMap, { routeParams } from '../../../../../routing/siteMap';
import {
  VidMobButton,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';

const NavigateToCriteriaManagementButton = () => {
  const intl = useIntl();

  const navigateToCriteriaManagement = () => {
    const path = generatePath(siteMap.creativeIntelligenceCompliance, {
      tab: routeParams.tabs.creativeIntelligenceCompliance.criteriaManagement,
    });
    history.push(path);
  };

  return (
    <VidMobButton
      variant="text"
      onClick={navigateToCriteriaManagement}
      sx={{
        color: 'var(--primary-main, #1842EF)',
        fontSize: '14px',
        fontStyle: 'normal',
        fontWeight: 600,
        textTransform: 'none',
      }}
    >
      <VidMobTypography fontWeight={600}>
        {intl.formatMessage({
          id: 'ui.creativeScoring.criteriaManagementV2.dialog.bestPracticeReminder.button.label.criteriaManagement',
          defaultMessage: 'Go to Criteria Management',
        })}
      </VidMobTypography>
    </VidMobButton>
  );
};

export default NavigateToCriteriaManagementButton;
