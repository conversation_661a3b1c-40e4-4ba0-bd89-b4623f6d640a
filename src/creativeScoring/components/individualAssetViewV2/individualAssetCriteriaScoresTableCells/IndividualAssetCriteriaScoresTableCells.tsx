import React, { useRef, JSX } from 'react';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { GridCellParams } from '@mui/x-data-grid';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobChip,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { SxProps } from '@mui/material';
import {
  SuccessIcon,
  CancelIcon,
  ProcessingIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import CriteriaIsOptionalPill from '../../shared/CriteriaIsOptionalPill';
import { BestPracticeIcon } from '../../../../assets/vidmob-mui-icons/general';
import { convertServerCriterionToCriteriaDetailsCriterionForScorecardIAV } from '../../shared/CriteriaDetailsPopover.tx/criteriaDetailsPopover.utils';
import { getPlatformDisplayIntlText } from '../../../../utils/feConstantsUtils';
import {
  getSelectedScorecard,
  getIsBatchSelected,
} from '../../../redux/selectors/complianceBatches.selectors';
import { getCurrentVersionMediaId } from '../../../redux/selectors/complianceIndividualAsset.selectors';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { getPlatformIdentifierForLogo } from '../../../../utils/feConstantsUtils';
import { COMPLIANCE } from '../../../../constants';
import { individualAssetCriteriaScoresGridCellStyles } from '../individualAssetViewStyles';
import { getCurrentPartnerName } from '../../../../redux/selectors/partner.selectors';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import ScoreResultChipWithTooltip from '../../ScoreResultChipWithTooltip/ScoreResultChipWithTooltip';
import { CriteriaResultType } from '../../../types/criteria.types';
import { GridRenderCellParams } from '@mui/x-data-grid-pro';
import useCriteriaDetailsPopoverV2 from '../../shared/CriteriaDetailsPopover.tx/useCriteriaDetailsPopoverV2';

const { ALL_PLATFORMS_TEMPLATE_IDENTIFIER, BATCH_TYPE, BATCH_STATUS } =
  COMPLIANCE;
const { COMPLETE } = BATCH_STATUS;

const {
  typographyStyle,
  criteriaCellBestPracticeStyle,
  criteriaCellOptionStyle,
  channelCellIconStyle,
  criteriaTypographyStyle,
  scoreProcessingIconStyle,
  scorePassIconStyle,
  scoreFailIconStyle,
  scoreNAChipStyle,
  dotIconStyle,
  scoreUnderReviewChipStyle,
} = individualAssetCriteriaScoresGridCellStyles;

const criteriaCellContainerSx = {
  width: '100%',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '4px',
};

const customIconSx = {
  height: '16px',
  width: '16px',
};

export const IndividualAssetCriteriaScoresCriteriaCell = ({
  row,
}: GridCellParams) => {
  const intl = useIntl();
  const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
    'isCriteriaGroupsInReportsEnabled',
  );

  const { isOptional, name, rule, isBestPractice, customIconUrl, icon } = row;
  const criteriaNameRef = useRef<HTMLDivElement>(null);
  const currentCriteriaNameDiv = criteriaNameRef?.current;
  const rect = currentCriteriaNameDiv?.getBoundingClientRect();
  const currentWorkspace = useSelector(getCurrentPartnerName);

  const { renderPopover, handlePopoverOpen, handlePopoverClose } =
    useCriteriaDetailsPopoverV2({
      criterion:
        convertServerCriterionToCriteriaDetailsCriterionForScorecardIAV(
          row,
          currentWorkspace,
          intl,
        ),
      customAnchor: {
        anchorReference: 'anchorPosition',
        anchorPosition: { top: rect?.top || 0, left: (rect?.left || 0) + 250 },
      },
      customIconUrl: customIconUrl,
    });

  return (
    <VidMobStack
      sx={{
        ...criteriaCellContainerSx,
        ...(isCriteriaGroupsInReportsEnabled && {
          ml: '56px',
          gap: '12px',
        }),
      }}
      ref={criteriaNameRef}
    >
      {icon && icon}
      <VidMobTypography
        variant="body2"
        sx={{ ...typographyStyle, ...criteriaTypographyStyle }}
        onMouseEnter={handlePopoverOpen}
        onMouseLeave={handlePopoverClose}
      >
        {name || rule}
      </VidMobTypography>
      <VidMobStack flexDirection="row" alignItems="center" gap="4px">
        {customIconUrl && (
          <VidMobAvatar src={customIconUrl} sx={customIconSx} />
        )}
        {Boolean(isBestPractice) && (
          <VidMobTooltip
            title={intl.formatMessage({
              id: 'ui.compliance.criteriaManagement.bestPractice.tooltip',
              defaultMessage: 'Channel best practice',
            })}
            placement="top"
          >
            <VidMobBox style={criteriaCellBestPracticeStyle}>
              <BestPracticeIcon sx={criteriaCellBestPracticeStyle} />
            </VidMobBox>
          </VidMobTooltip>
        )}
        {Boolean(isOptional) && (
          <CriteriaIsOptionalPill additionalStyles={criteriaCellOptionStyle} />
        )}
      </VidMobStack>
      {renderPopover()}
    </VidMobStack>
  );
};

export const IndividualAssetCriteriaScoresChannelCell = ({
  row,
}: GridCellParams) => {
  const intl = useIntl();

  const getPlatformName = (platformKey: string) => {
    if (platformKey === ALL_PLATFORMS_TEMPLATE_IDENTIFIER) {
      return intl.formatMessage({
        id: 'ui.compliance.contentAudit.vidmobCriteria.label',
        defaultMessage: 'Standard',
      });
    }

    return getPlatformDisplayIntlText(platformKey, intl);
  };

  return (
    <VidMobStack
      flexDirection="row"
      alignItems="center"
      gap="12px"
      width="100%"
    >
      {getMUIIconForChannel(
        getPlatformIdentifierForLogo(row.platformIdentifier),
        channelCellIconStyle,
      )}
      <VidMobTypography variant="body2" sx={typographyStyle}>
        {getPlatformName(row.platformIdentifier)}
      </VidMobTypography>
    </VidMobStack>
  );
};

export const IndividualAssetCriteriaScoresScoreCell = ({
  row,
}: GridCellParams) => {
  const intl = useIntl();
  const scoreResult = row.result;

  const selectedBatch = useSelector(getSelectedScorecard);
  const currentVersionMediaId = useSelector(getCurrentVersionMediaId);
  const isBatchSelected = useSelector(getIsBatchSelected);

  if (row.isParent) {
    return null;
  }

  const isPreFlightBatch = selectedBatch?.batchType === BATCH_TYPE.PRE_FLIGHT;
  const isPreviousVersion = isPreFlightBatch
    ? currentVersionMediaId !== Number(row.mediaId)
    : false;
  const isCurrentBatchProcessing =
    !isPreviousVersion && isBatchSelected && selectedBatch?.status !== COMPLETE;
  const isMediaProcessing = getFeatureFlag('isCriteriaGroupsInReportsEnabled')
    ? row.isMediaProcessing
    : isCurrentBatchProcessing;

  const renderIconAndText = (text: string, icon: JSX.Element | null = null) => (
    <VidMobStack flexDirection="row" alignItems="center" gap="12px">
      {icon}
      <VidMobTypography variant="body2" sx={typographyStyle}>
        {text}
      </VidMobTypography>
    </VidMobStack>
  );

  const dotIcon = <VidMobBox sx={dotIconStyle} />;

  const renderChip = (
    resultType: CriteriaResultType,
    icon?: JSX.Element,
    styles: SxProps = {},
  ) => (
    <ScoreResultChipWithTooltip
      resultType={resultType}
      icon={icon}
      styles={styles}
    />
  );

  const getScoreContent = () => {
    if (
      isMediaProcessing ||
      scoreResult === CriteriaResultType.CRITERIA_RESULT_PENDING
    ) {
      return renderChip(
        CriteriaResultType.CRITERIA_RESULT_PENDING,
        <ProcessingIcon sx={scoreProcessingIconStyle} isRotating />,
      );
    }

    if (scoreResult === CriteriaResultType.CRITERIA_PASS) {
      return renderIconAndText(
        intl.formatMessage({
          id: 'ui.compliance.individualCriteriaResult.pass',
          defaultMessage: 'Yes',
        }),
        <SuccessIcon sx={scorePassIconStyle} />,
      );
    }

    if (scoreResult === CriteriaResultType.CRITERIA_FAIL) {
      return renderIconAndText(
        intl.formatMessage({
          id: 'ui.compliance.individualCriteriaResult.fail',
          defaultMessage: 'No',
        }),
        <CancelIcon sx={scoreFailIconStyle} />,
      );
    }

    if (scoreResult === CriteriaResultType.CRITERIA_NOT_RUN) {
      return renderChip(CriteriaResultType.CRITERIA_NOT_RUN);
    }

    if (
      scoreResult === CriteriaResultType.CRITERIA_NO_DATA ||
      scoreResult === CriteriaResultType.CRITERIA_NO_MEDIA
    ) {
      return renderChip(
        CriteriaResultType.CRITERIA_NO_DATA,
        dotIcon,
        scoreNAChipStyle,
      );
    }

    if (scoreResult === CriteriaResultType.CRITERIA_ERROR) {
      return renderChip(CriteriaResultType.CRITERIA_ERROR);
    }

    if (scoreResult) {
      return renderChip(scoreResult);
    }

    return <></>;
  };

  return (
    <>
      {getScoreContent()}
      {row.isUnderReview && (
        <VidMobChip
          sx={scoreUnderReviewChipStyle}
          label={intl.formatMessage({
            id: 'ui.compliance.individualCriteriaResult.underReview',
            defaultMessage: 'Under review',
          })}
        />
      )}
    </>
  );
};

const parentCellContainerSx = {
  width: '100%',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
  gap: '12px',
  pl: '12px',
};

const textSx = {
  fontSize: '14px',
  fontWeight: 500,
};

const criteriaCountSx = {
  color: 'text.secondary',
};

export const IndividualAssetCriteriaScoresParentCell = ({
  row,
}: GridRenderCellParams) => {
  const intl = useIntl();
  const { icon, displayName, totalCriteriaCount } = row;

  return (
    <VidMobStack sx={parentCellContainerSx}>
      {icon && icon}
      <VidMobTypography variant="body2" sx={textSx}>
        {displayName}
      </VidMobTypography>
      {Boolean(totalCriteriaCount) && (
        <VidMobTypography variant="caption" sx={criteriaCountSx}>
          {`${totalCriteriaCount} ${intl.formatMessage({
            id: 'individualCreativeViewV2.scores.criteria',
            defaultMessage: 'criteria',
          })}`}
        </VidMobTypography>
      )}
    </VidMobStack>
  );
};
