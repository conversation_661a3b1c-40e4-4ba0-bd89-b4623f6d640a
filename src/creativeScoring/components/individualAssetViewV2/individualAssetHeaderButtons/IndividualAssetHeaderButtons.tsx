import React, {
  useState,
  useEffect,
  useRef,
  MouseEvent,
  ChangeEvent,
} from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useIntl } from 'react-intl';
import {
  VidMobStack,
  VidMobMenuItem,
  VidMobMenu,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { Menu, MenuItem } from '@mui/material';
import {
  FlagIcon,
  LinkIcon,
  ClockIcon,
  DownloadFilledIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import history from '../../../../routing/history';
import IndividualAssetHeaderButton from '../individualAssetHeaderButton/IndividualAssetHeaderButton';
import FlagCriteriaModal from '../../individualAssetView/FlagCriteriaModal';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, COMPLIANCE } from '../../../../constants';
import { uploadAssetVersion as uploadAssetVersionActionCreator } from '../../../redux/actions/uploads.actions';
import { canUserUpdateReportDetails } from '../../../featureServices/CreativeScoring';
import { getScorecardsLandingViewLink } from '../../../redux/selectors/complianceShared.selectors';
import { getBatchForIndividualAsset } from '../../../redux/selectors/complianceIndividualAsset.selectors';
import { getAssetVersionCreationStatus } from '../../../redux/selectors/complianceUploads.selectors';
import checkAndFilterSupportedFiles from '../../../featureServices/checkAndFilterSupportedFiles';
import { getCurrentPartnerPermissions } from '../../../../redux/selectors/partner.selectors';
import { getCurrentUserId } from '../../../../redux/selectors/user.selectors';
import { Scorecard } from '../../../types/scorecards.types';
import { useToastAlert } from '../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import BffScorecardDetailsService from '../../../../apiServices/BffScorecardDetailsService';
import { downloadCSV } from '../../../../utils/downloadCSV';
import vmErrorLog from '../../../../utils/vmErrorLog';
import { AdvancedFiltersChannelType } from '../../../../components/ReportFilters/types';

const { BATCH_STATUS, SUPPORTED_UPLOAD_FILE_TYPES } = COMPLIANCE;
const { SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

type Props = {
  isPreFlight?: boolean;
  isPluginMedia?: boolean;
  isVersionPanelOpen?: boolean;
  setIsVersionPanelOpen?: (isVersionPanelOpen: boolean) => void;
  workspaceId: number;
  mediaId: string;
  mediaName: string;
  mediaDownloadUrl: string;
  channels: AdvancedFiltersChannelType[];
  isMediaLoading: boolean;
  hasMediaError: boolean;
  areScoresLoading: boolean;
  hasScoresError: boolean;
};

const IndividualAssetHeaderButtons = ({
  isPreFlight = false,
  isPluginMedia = false,
  isVersionPanelOpen = false,
  setIsVersionPanelOpen = () => {},
  workspaceId,
  mediaId,
  mediaName,
  mediaDownloadUrl,
  channels,
  isMediaLoading,
  hasMediaError,
  areScoresLoading,
  hasScoresError,
}: Props) => {
  const intl = useIntl();
  const showToast = useToastAlert();
  const dispatch = useDispatch();
  const location = useLocation();
  const inputFieldRef = useRef<HTMLInputElement>(null);

  const selectedBatch = useSelector<Scorecard>(
    getBatchForIndividualAsset,
  ) as Scorecard;
  const assetVersionCreationStatus = useSelector(getAssetVersionCreationStatus);
  const partnerPermissions = useSelector(getCurrentPartnerPermissions);
  const currentUserId = useSelector(getCurrentUserId);
  const scorecardsLandingUrl = useSelector(getScorecardsLandingViewLink);

  const [isVersionsDropdownOpen, setIsVersionsDropdownOpen] = useState(false);
  const [versionsDropdownAnchor, setVersionsDropdownAnchor] =
    useState<HTMLElement | null>(null);

  const [isFlagScoresButtonEnabled, setIsFlagScoresButtonEnabled] =
    useState(false);
  const [isFlagCriteriaModalOpen, setIsFlagCriteriaModalOpen] = useState(false);

  const [isDownloadDropdownOpen, setIsDownloadDropdownOpen] = useState(false);
  const [downloadDropdownAnchor, setDownloadDropdownAnchor] =
    useState<HTMLElement | null>(null);
  const [isCsvDataLoading, setIsCsvDataLoading] = useState<boolean>(false);

  const revisionsLabelKey = isVersionPanelOpen
    ? 'ui.compliance.singleAssetView.dropdown.closeRevisions.button'
    : 'ui.compliance.singleAssetView.dropdown.viewRevisions.button';

  const canUserCreateAssetVersion = canUserUpdateReportDetails(
    partnerPermissions,
    selectedBatch,
    currentUserId,
  );

  const canAssetVersionBeCreated =
    canUserCreateAssetVersion && selectedBatch?.status !== BATCH_STATUS.ERROR;

  useEffect(() => {
    if (assetVersionCreationStatus === SUCCESS) {
      history.push(scorecardsLandingUrl);
    }
  }, [assetVersionCreationStatus]);

  const onLocalFileClick = () => {
    inputFieldRef?.current?.click();
  };

  const onLocalFileSelected = async (event: ChangeEvent<HTMLInputElement>) => {
    const { files } = event.target;
    const supportedFile = await checkAndFilterSupportedFiles(files);
    dispatch(
      uploadAssetVersionActionCreator(
        selectedBatch.id,
        supportedFile,
        Number(mediaId),
      ),
    );
    setIsVersionsDropdownOpen(false);
  };

  const renderVersionsDropdown = () => (
    <Menu
      id="basic-menu"
      anchorEl={versionsDropdownAnchor}
      open={isVersionsDropdownOpen}
      onClose={() => {
        setIsVersionsDropdownOpen(false);
      }}
    >
      {canAssetVersionBeCreated && (
        <MenuItem onClick={onLocalFileClick}>
          <VidMobTypography variant="body2">
            {intl.formatMessage({
              id: 'ui.compliance.singleAssetView.dropdown.uploadNewVersion.button',
              defaultMessage: 'Upload new version',
            })}
          </VidMobTypography>
        </MenuItem>
      )}
      <VidMobMenuItem
        onClick={() => {
          setIsVersionsDropdownOpen(false);
          setIsVersionPanelOpen(!isVersionPanelOpen);
        }}
      >
        <VidMobTypography variant="body2">
          {intl.formatMessage({ id: revisionsLabelKey })}
        </VidMobTypography>
      </VidMobMenuItem>
    </Menu>
  );

  const renderDownloadDropdown = () => (
    <VidMobMenu
      id="basic-menu"
      anchorEl={downloadDropdownAnchor}
      open={isDownloadDropdownOpen}
      onClose={() => {
        setIsDownloadDropdownOpen(false);
      }}
    >
      <VidMobMenuItem
        onClick={handleClickDownloadCSV}
        disabled={areScoresLoading || hasScoresError}
      >
        <VidMobTypography variant="body2">
          {intl.formatMessage({
            id: 'individualCreativeViewV2.buttons.exportCsv',
            defaultMessage: 'Export criteria scores as CSV',
          })}
        </VidMobTypography>
      </VidMobMenuItem>
      {mediaDownloadUrl && (
        <VidMobMenuItem
          onClick={handleDownloadMedia}
          disabled={isMediaLoading || hasMediaError}
        >
          <VidMobTypography variant="body2">
            {intl.formatMessage({
              id: 'individualCreativeViewV2.buttons.downloadAsset',
              defaultMessage: 'Download asset',
            })}
          </VidMobTypography>
        </VidMobMenuItem>
      )}
    </VidMobMenu>
  );

  const handleClickFlagButton = () => {
    setIsFlagCriteriaModalOpen(true);
  };

  const handleClickDownloadButton = (event: MouseEvent<HTMLButtonElement>) => {
    setDownloadDropdownAnchor(event.currentTarget);
    setIsDownloadDropdownOpen(!isDownloadDropdownOpen);
  };

  const handleClickDownloadCSV = async () => {
    setIsCsvDataLoading(true);

    try {
      const response = isPreFlight
        ? await BffScorecardDetailsService.createScorecardCsvReport({
            workspaceId,
            scorecardId: Number(selectedBatch.id),
            mediaIds: [Number(mediaId)],
          })
        : await BffScorecardDetailsService.createScorecardCsvReportV2({
            workspaceId,
            mediaId: Number(mediaId),
            platforms: channels,
          });

      const csvData = response?.data;
      setIsCsvDataLoading(false);
      if (csvData) {
        downloadCSV({
          csvData,
          fileName: `${mediaName || 'Media scores'}.csv`,
        });
      } else {
        showToast('individualCreativeViewV2.csv.error.noData', 'error');
      }

      setIsCsvDataLoading(false);
    } catch (error) {
      setIsCsvDataLoading(false);
      showToast('individualCreativeViewV2.csv.error', 'error');
      vmErrorLog(
        error as Error,
        'IndividualAssetCriteriaScoresTable handleClickDownloadCSV',
      );
    }
  };

  const handleDownloadMedia = () => {
    window.open(mediaDownloadUrl, '_blank');
  };

  const handleClickLinkButton = () => {
    const currentUrl = `${window.location.origin}${location.pathname}${location.search}${location.hash}`;
    navigator.clipboard.writeText(currentUrl);
    showToast(
      'ui.compliance.scorecardsLanding.toastAlert.page.linkCopied',
      'success',
    );
  };

  const handleClickVersionsButton = (event: MouseEvent<HTMLButtonElement>) => {
    setVersionsDropdownAnchor(event.currentTarget);
    setIsVersionsDropdownOpen(!isVersionsDropdownOpen);
  };

  const shouldRenderFlagButton = !isPluginMedia;

  return (
    <>
      <VidMobStack flexDirection="row" gap="8px" alignItems="center">
        {isPreFlight && (
          <IndividualAssetHeaderButton
            icon={<ClockIcon />}
            dropdown={renderVersionsDropdown()}
            onClickButton={handleClickVersionsButton}
            tooltipTitle={intl.formatMessage({
              id: 'individualCreativeViewV2.buttons.versions.tooltip',
              defaultMessage: 'Versions',
            })}
          />
        )}
        {shouldRenderFlagButton && (
          <IndividualAssetHeaderButton
            icon={<FlagIcon />}
            onClickButton={handleClickFlagButton}
            tooltipTitle={intl.formatMessage({
              id: 'individualCreativeViewV2.buttons.flag.tooltip',
              defaultMessage: 'Flag score',
            })}
            isDisabled={!isFlagScoresButtonEnabled || hasScoresError}
          />
        )}
        {isCriteriaGroupsInReportsEnabled && (
          <IndividualAssetHeaderButton
            icon={<DownloadFilledIcon />}
            onClickButton={handleClickDownloadButton}
            tooltipTitle={intl.formatMessage({
              id: 'individualCreativeViewV2.buttons.download.tooltip',
              defaultMessage: 'Download options',
            })}
            dropdown={renderDownloadDropdown()}
            isDisabled={hasMediaError && hasScoresError}
            isLoading={isCsvDataLoading}
          />
        )}
        <IndividualAssetHeaderButton
          icon={<LinkIcon />}
          onClickButton={handleClickLinkButton}
          tooltipTitle={intl.formatMessage({
            id: 'individualCreativeViewV2.buttons.link.tooltip',
            defaultMessage: 'Share link',
          })}
        />
      </VidMobStack>
      <FlagCriteriaModal
        isOpen={isFlagCriteriaModalOpen}
        setIsOpen={setIsFlagCriteriaModalOpen}
        workspaceId={workspaceId}
        mediaId={mediaId}
        selectedBatch={selectedBatch as Scorecard}
        setIsFlagScoresButtonEnabled={setIsFlagScoresButtonEnabled}
        setHasApiError={() => {}}
      />
      {isPreFlight && (
        <input
          ref={inputFieldRef}
          type="file"
          style={{ display: 'none' }}
          accept={SUPPORTED_UPLOAD_FILE_TYPES}
          data-testid="file-upload"
          onChange={onLocalFileSelected}
        />
      )}
    </>
  );
};

export default IndividualAssetHeaderButtons;
