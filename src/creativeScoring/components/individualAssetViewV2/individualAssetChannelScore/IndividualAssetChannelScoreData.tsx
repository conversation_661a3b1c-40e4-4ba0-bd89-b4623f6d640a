import React from 'react';
import {
  VidMobBox,
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { individualAssetChannelScoreStyles } from '../individualAssetViewStyles';
import { useIntl } from 'react-intl';
import { getPassPercentageString } from './getPassPercentageString';
import IndividualAssetChannelScoreHeader from './IndividualAssetChannelScoreHeader';

const { scoreMainBoxStyle, typographyStyle, mainStackStyle } =
  individualAssetChannelScoreStyles;

interface Props {
  filteredScores: string[] | [];
  overallScores: {
    [key: string]: { passCount: number; criteriaCount: number };
  };
}

const IndividualAssetChannelScoreData = ({
  filteredScores,
  overallScores,
}: Props) => {
  const intl = useIntl();

  return (
    <VidMobStack gap="24px" flexDirection="row" sx={mainStackStyle}>
      {filteredScores
        .filter((platformKey) => platformKey !== 'VERIZONNATIVE')
        .map((platformKey: string, idx: number) => {
          return (
            <VidMobBox sx={scoreMainBoxStyle} key={idx}>
              <IndividualAssetChannelScoreHeader platformKey={platformKey} />
              <VidMobTypography variant="h5">
                {getPassPercentageString(
                  overallScores[platformKey].passCount,
                  overallScores[platformKey].criteriaCount,
                )}
              </VidMobTypography>
              <VidMobTypography
                variant="caption"
                color="#757575"
                sx={typographyStyle}
              >
                {intl.formatMessage(
                  { id: 'individualCreativeViewV2.header.score.count' },
                  {
                    passCount: overallScores[platformKey].passCount,
                    criteriaCount: overallScores[platformKey].criteriaCount,
                  },
                )}
              </VidMobTypography>
            </VidMobBox>
          );
        })}
    </VidMobStack>
  );
};

export default IndividualAssetChannelScoreData;
