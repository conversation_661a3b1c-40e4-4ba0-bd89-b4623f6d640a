export const individualAssetViewStyles = {
  parentContentBox: {
    height: '100%',
    width: '100%',
    transition: 'width 0.3s ease-in-out',
  },
};

export const individualAssetHeaderStyles = {
  mainStackStyle: {
    gap: '8px',
    p: '0 24px',
    mt: '-10px',
    mb: '16px',
  },
  childStackStyle: { gap: '6px' },
  iconStyle: {
    color: '#757575',
    height: '16px',
    width: '16px',
  },
  creativeTypographyStyle: {
    color: '#757575',
    borderBottom: '1px dashed #757575',
    ':hover': {
      cursor: 'pointer',
    },
  },
  separatorStyle: {
    borderRadius: '50%',
    width: '4px',
    height: '4px',
    backgroundColor: '#757575',
  },
  channelLogoStyle: {
    height: '16px',
    width: '16px',
  },
  loadingStackStyle: {
    padding: '16px 24px',
  },
  popoverStackStyle: { gap: '9px' },
  popoverLinkTypographyStyle: {
    color: '#1842EF',
    textDecoration: 'none',
    ':hover': {
      textDecoration: 'underline',
    },
  },
};

export const individualAssetContentStyles = {
  mainBoxStyle: {
    padding: '0 24px',
    overflowY: 'hidden',
    minWidth: '800px',
  },
  tabsHeaderStyle: {
    marginTop: '24px',
    borderBottom: 'none',
    '& .MuiBox-root:first-of-type': {
      paddingLeft: 0,
    },
  },
  tabsHeightStyle: { height: '100%' },
  contentBoxStyle: { width: 'calc(100% - 350px)', height: '100%' },
  loadingStackStyle: { width: '100%', height: '100%' },
};

export const individualAssetChannelScoreStyles = {
  mainStackStyle: {
    overflowY: 'auto',
    width: '100%',
  },
  scoreMainBoxStyle: {
    width: '109px',
    height: '82px',
  },
  channelStackStyle: { marginBottom: '8px', flexShrink: 0 },
  iconStyle: {
    height: '16px',
    width: '16px',
  },
  typographyStyle: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  skeletonPercentageStyle: { marginTop: '10px' },
  skeletonCountStyle: { marginTop: '8px' },
};

export const individualAssetMediaPreviewStyles = {
  mainParentBoxStyle: {
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '325px',
    maxHeight: '325px !important',
    width: '325px',
    maxWidth: '325px !important',
    marginRight: '24px',
    flexShrink: 0,
    borderRadius: '8px',
  },
  imageBoxStyle: {
    maxWidth: '100%',
    maxHeight: '100%',
    objectFit: 'contain',
  },
  loadingStateStyle: { marginRight: '24px', flexShrink: 0 },
  errorStateBoxStyle: {
    height: '325px',
    width: '325px',
    marginRight: '24px',
    flexShrink: 0,
    backgroundColor: '#EEE',
    borderRadius: '6px',
  },
  errorStateButtonStyle: { height: '36px' },
  videoPreviewBoxStyle: {
    width: '325px',
    height: '325px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '8px',
    backgroundColor: '#0f0f0f',
    zIndex: 1,

    '.media-preview': {
      overflow: 'hidden',
      '.jwplayer': {
        maxWidth: '325px',
        maxHeight: '325px',
        '.jw-video': {
          objectFit: 'contain !important',
        },
      },
      '&.isSixteenByNine': {
        '.jwplayer': {
          width: '100%',
          height: 'auto !important',
        },
      },
      '&.isNineBySixteen, &.isTwoByThree, &.isFourByFive': {
        '.jwplayer': {
          width: 'auto !important',
          height: '100%',
        },
      },
      '&.isSquare': {
        '.jwplayer': {
          height: '100%',
          width: '100%',
        },
      },
    },
  },
};

export const individualAssetCriteriaScoresStyles = {
  errorStateStackStyle: { marginTop: '100px' },
  errorStateButtonStyle: { height: '36px' },
  dataGridStyle: {
    height: 'calc(100% - 60px)',
    width: 'auto',
    overflowX: 'auto',
    borderTop: 'none',
    borderLeft: 'none',
    borderRight: 'none',
    borderBottomColor: '#bdbdbd',
    borderRadius: 0,
    '& .MuiDataGrid-columnHeader, & .MuiDataGrid-cell': {
      minWidth: '100px',
      flex: 1,
    },
    '& .MuiDataGrid-row': {
      minWidth: '0',
      overflowX: 'auto',
    },
    '& .MuiDataGrid-columnHeader:focus-within, & .MuiDataGrid-cell:focus-within':
      {
        outline: 'none !important',
      },
    '& .MuiDataGrid-columnHeader:focus, & .MuiDataGrid-cell:focus': {
      outline: 'none !important',
    },
    '& .MuiDataGrid-columnHeaderTitle': {
      fontWeight: '600 !important',
    },
    '& .MuiDataGrid-withBorderColor': {
      borderColor: '#bdbdbd',
    },
  },
  gridBoxStyle: {
    width: '100%',
    height: '100%',
    overflowY: 'auto',
  },
};

export const individualAssetCriteriaScoresGridCellStyles = {
  typographyStyle: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
  criteriaTypographyStyle: {
    maxHeight: '20px',
    ':hover': {
      cursor: 'pointer',
      borderBottom: '1px dashed',
      borderBottomColor: 'text.secondary',
    },
  },
  criteriaCellBestPracticeStyle: {
    height: '16px',
    width: '16px',
  },
  criteriaCellOptionStyle: {
    marginTop: 0,
  },
  channelCellIconStyle: {
    height: '20px',
    width: '20px',
  },
  scoreProcessingIconStyle: {
    height: '20px',
    width: '20px',
    color: '#212121 !important',
  },
  scorePassIconStyle: {
    height: '20px',
    width: '20px',
    color: 'success.main',
  },
  scoreFailIconStyle: {
    height: '20px',
    width: '20px',
    color: 'icon.secondary',
  },
  scoreNATooltipStyle: {
    width: '200px',
  },
  scoreNAChipStyle: { ':hover': { cursor: 'pointer' } },
  scoreChipStyle: {
    height: '26px',
    padding: '3px 4px',
  },
  dotIconStyle: {
    width: '8px',
    height: '8px',
    borderRadius: '100px',
    backgroundColor: 'error.main',
  },
  scoreUnderReviewChipStyle: {
    backgroundColor: '#DCF9FE',
    color: '#0076A3',
    size: '14px !important',
    fontWeight: '400 !important',
    marginLeft: '10px',
    height: '28px',
  },
};
