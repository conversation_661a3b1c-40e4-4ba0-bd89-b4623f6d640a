import React from 'react';
import EngagementScrubBar from '../../../../../creativeAnalytics/__pages/IndividualCreativeView/IndividualCreativeViewData/ICVVideo/AudienceEngagementWidget/EngagementRateGraph/EngagementScrubBar';
import { formatTime } from '../IndividualAssetCreativeElementsUtils';
import {
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import {
  INDIVIDUAL_CREATIVE_VIEW_MAX_DURATION_INTERVALS,
  SCORING_ICV_DURATION_SCRUBBER_PADDING,
} from '../../../../constants/creativeScoring.constants';

type Props = {
  isImage: boolean;
  mediaId: string;
  rightPanelHeight: number;
  elementsRowCount: number;
  duration: number;
  rightPanelWidth: number;
  rightPaneRef: React.RefObject<HTMLDivElement>;
};

const IndividualAssetCreativeElementsHeader = (props: Props) => {
  const intl = useIntl();
  const {
    elementsRowCount,
    isImage,
    mediaId,
    rightPanelHeight,
    duration,
    rightPanelWidth,
    rightPaneRef,
  } = props;
  const title = intl.messages[
    isImage
      ? 'individualCreativeViewV2.elements'
      : 'individualCreativeViewV2.creative.elements'
  ] as string;
  const tooltipTitle = intl.messages[
    'individualCreativeViewV2.creative.elements.tooltip'
  ] as string;

  if (elementsRowCount === 0) return null;

  const getDurationIntervals = () => {
    let intervals: number[] = [];

    if (duration <= INDIVIDUAL_CREATIVE_VIEW_MAX_DURATION_INTERVALS) {
      intervals = Array.from(
        Array(Math.max(0, Math.round(Number(duration))))?.keys(),
      );
    } else {
      const length = INDIVIDUAL_CREATIVE_VIEW_MAX_DURATION_INTERVALS;
      const step = duration / (length - 1);

      for (let i = 0; i < length; i++) {
        intervals.push(Math.round(i * step));
      }

      intervals[length - 1] = duration;
    }

    return intervals.map((index) => (
      <VidMobTypography variant="caption" key={index}>
        {formatTime(index)}
      </VidMobTypography>
    ));
  };

  return (
    <VidMobStack
      direction="row"
      className="individual-asset-creative-elements-header-container"
    >
      <VidMobTooltip
        disableHoverListener={isImage}
        title={tooltipTitle}
        placement="top"
      >
        <VidMobStack
          justifyContent="flex-end"
          className="individual-asset-creative-elements-header-title"
        >
          <VidMobTypography variant="subtitle2">{title}</VidMobTypography>
        </VidMobStack>
      </VidMobTooltip>
      {!isImage && (
        <VidMobStack
          ref={rightPaneRef}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          className="individual-asset-creative-elements-header-timeline"
          sx={{
            '& .engagement-scrub-bar': {
              position: 'absolute',
              top: `${rightPanelHeight + 8}px !important`,
            },
          }}
        >
          {getDurationIntervals()}
          <EngagementScrubBar
            height={elementsRowCount * 45}
            chartWidth={rightPanelWidth}
            mediaId={parseInt(mediaId)}
            duration={duration}
            leftPadding={SCORING_ICV_DURATION_SCRUBBER_PADDING}
          />
        </VidMobStack>
      )}
    </VidMobStack>
  );
};

export default IndividualAssetCreativeElementsHeader;
