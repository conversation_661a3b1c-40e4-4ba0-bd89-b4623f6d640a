export function formatTime(input: number): string {
  const minutes = Math.floor(input / 60);
  const seconds = Math.round(input % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

export function getElementsWrapperStyle(
  isImage: boolean,
  isGraphAvailable: boolean,
  isBannerOpen: boolean,
) {
  const baseHeight = isGraphAvailable ? 584 : 430;
  const bannerOffset = isBannerOpen ? 65 : 0;

  return {
    '& .audience-engagement-widget-element-section': {
      overflowX: 'hidden',
      overflowY: 'auto',
      height: `calc(100vh - ${baseHeight + bannerOffset}px) !important`,
      maxHeight: '56vh !important',
      margin: '0px !important',
      '& .element-tag-pill-section': {
        width: isImage ? '100% !important' : '300px',
      },
      '& .element-timeline-indicator-section': {
        display: isImage ? 'none !important' : 'flex',
      },
    },
  };
}
