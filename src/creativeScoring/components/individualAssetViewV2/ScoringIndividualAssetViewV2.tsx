import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { generatePath } from 'react-router';
import siteMap, { routeParams } from '../../../routing/siteMap';
import history from '../../../routing/history';
import { VidMobBox, VidMobStack } from '../../../vidMobComponentWrappers';
import { BlankOrErrorState } from '../../../muiCustomComponents/BlankState/BlankState';
import IndividualAssetHeader from './individualAssetHeader/IndividualAssetHeader';
import IndividualAssetContentWrapper from './individualAssetViewContentWrapper/IndividualAssetContentWrapper';
import IndividualAssetVersionPanel from '../individualAssetView/IndividualAssetVersionPanel';
import {
  getAccountGroups,
  getAccounts,
} from '../../../redux/selectors/platformAccounts.selectors';
import {
  getCurrentPartnerFeatureList,
  getCurrentPartnerId,
} from '../../../redux/selectors/partner.selectors';
import {
  getAssetVersionLoadingStatus,
  getAssetVersionScoresLoadingStatus,
  getIndividualAssetScoresLoadingStatus,
  getBatchForIndividualAsset,
  getBatchForIndividualAssetLoadingStatus,
  getCurrentVersionMediaId,
} from '../../redux/selectors/complianceIndividualAsset.selectors';
import {
  setBatchForIndividualAsset,
  setIndividualAssetScores,
  setAssetVersions,
  setSelectedAssetVersionScores,
  setSelectedAssetVersionMediaObject,
} from '../../redux/actions/individualAsset.actions';
import { GLOBALS, COMPLIANCE } from '../../../constants';
import scoreOverrideSlice from '../../redux/slices/scoreOverride.slice';
import complianceBatchesSlice from '../../redux/slices/complianceBatches.slice';
import complianceShared from '../../redux/slices/complianceShared.slice';
import complianceUploadsSlice from '../../redux/slices/complianceUploads.slice';
import complianceIndividualAsset from '../../redux/slices/complianceIndividualAsset.slice';
import { BatchForIndividualAssetType } from '../../types/individualAsset.types';
import { individualAssetViewStyles } from './individualAssetViewStyles';
import { PreFlightInFlightGroupBy } from '../reports/preFlightOrInFlight/types';
import {
  AdvancedFiltersChannelType,
  ListItem,
} from '../../../components/ReportFilters/types';
import { getFeatureFlag } from '../../../utils/featureFlagUtils';
import { useIndividualAssetMedia } from './individualAssetCriteriaScores/hooks/useIndividualAssetMedia';
import { useToastAlert } from '../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
const { parentContentBox } = individualAssetViewStyles;
const { REDUX_LOADING_STATUS, PARTNER_SPECIFIC_FEATURES } = GLOBALS;
const { BRAND_GOVERNANCE } = PARTNER_SPECIFIC_FEATURES;
const { SUCCESS, NOT_LOADED, FAILED } = REDUX_LOADING_STATUS;
const { setSelectedScorecard } = complianceBatchesSlice.actions;
const { resetSharedData } = complianceShared.actions;
const { resetUploadsSlice } = complianceUploadsSlice.actions;
const { resetState } = complianceIndividualAsset.actions;
const { BATCH_TYPE, BATCH_STATUS } = COMPLIANCE;
const { COMPLETE } = BATCH_STATUS;
const { resetFlaggedScores } = scoreOverrideSlice.actions;

const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

interface RouteParams {
  adAccountId?: string;
  batchId: string;
  mediaId: string;
  platform: string;
  groupBy: PreFlightInFlightGroupBy;
}

const ScoringIndividualAssetViewV2 = () => {
  const dispatch = useDispatch();
  const showToast = useToastAlert();
  const urlParams = useParams<RouteParams>();
  const {
    adAccountId,
    batchId,
    mediaId,
    platform,
    groupBy = PreFlightInFlightGroupBy.CHANNEL,
  } = urlParams;

  const [isVersionPanelOpen, setIsVersionPanelOpen] = useState(false);
  const [selectedVersionMediaId, setSelectedVersionMediaId] = useState(mediaId);
  const [selectedVersionedBatchId, setSelectedVersionedBatchId] =
    useState(batchId);

  const workspaceId = useSelector(getCurrentPartnerId);
  const adAccounts = useSelector(getAccounts);
  const adAccountGroups = useSelector(getAccountGroups);
  const partnerFeatureList = useSelector(getCurrentPartnerFeatureList);
  const batchForIndividualAssetLoadingStatus = useSelector(
    getBatchForIndividualAssetLoadingStatus,
  );
  const batchForIndividualAsset = useSelector<BatchForIndividualAssetType>(
    getBatchForIndividualAsset,
  ) as BatchForIndividualAssetType;
  const hasErrorFetchingBatchDetails =
    batchForIndividualAssetLoadingStatus === FAILED;

  const nonVersionedScoresLoadingStatus = useSelector(
    getIndividualAssetScoresLoadingStatus,
  );
  const assetVersionsLoadingStatus = useSelector(getAssetVersionLoadingStatus);
  const isAssetVersionLoaded = assetVersionsLoadingStatus === SUCCESS;
  const selectedAssetVersionScoresLoadingStatus = useSelector((state: any) =>
    getAssetVersionScoresLoadingStatus(state, selectedVersionMediaId),
  );
  const currentVersionMediaId = useSelector(getCurrentVersionMediaId);

  const {
    data: mediaObject,
    isLoading: isMediaLoading,
    isError: hasMediaError,
  } = useIndividualAssetMedia({
    mediaId,
  });

  const allPlatformIds = COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.map(
    (channel) => channel.id,
  );
  const channels = (
    batchForIndividualAsset?.platforms?.length
      ? batchForIndividualAsset?.platforms
      : allPlatformIds
  ) as AdvancedFiltersChannelType[];

  const currentAdAccountId =
    batchForIndividualAsset?.platformAdAccount?.platformAccountId ||
    adAccountId;
  const currentAdAccountPlatform = adAccountId
    ? platform
    : batchForIndividualAsset?.platformAdAccount?.platform;
  const adAccountObj = [...(adAccounts || []), ...(adAccountGroups || [])].find(
    (account) => String(account.id) === String(currentAdAccountId),
  );

  const canShowKpiMetrics = Boolean(adAccountObj) && mediaObject;
  const isBatchSelected =
    batchForIndividualAsset && Object.keys(batchForIndividualAsset).length;
  const isPreFlight =
    batchForIndividualAsset?.batchType === BATCH_TYPE.PRE_FLIGHT;
  const isPluginMedia =
    batchForIndividualAsset?.batchType === BATCH_TYPE.PLUGIN_MEDIA;
  const currentBatchId = isPreFlight ? selectedVersionedBatchId : batchId;

  const isPreviousVersion = isPreFlight
    ? currentVersionMediaId !== Number(mediaId)
    : false;

  const isMediaProcessing =
    !isPreviousVersion &&
    Boolean(isBatchSelected) &&
    batchForIndividualAsset?.status !== COMPLETE;

  useEffect(() => {
    if (!partnerFeatureList[BRAND_GOVERNANCE]) {
      showToast('ui.compliance.singleAssetView.error', 'error');
      history.push(siteMap.activeProjects);
    }
  }, [partnerFeatureList]);

  useEffect(() => {
    if (batchForIndividualAssetLoadingStatus === NOT_LOADED) {
      dispatch(setBatchForIndividualAsset(batchId));
    }
  }, [batchForIndividualAssetLoadingStatus]);

  useEffect(() => {
    if (batchForIndividualAssetLoadingStatus === SUCCESS) {
      dispatch(
        setSelectedAssetVersionMediaObject(
          selectedVersionMediaId,
          batchForIndividualAsset.id,
        ),
      );
    }
  }, [
    selectedVersionMediaId,
    batchForIndividualAsset,
    isAssetVersionLoaded,
    batchForIndividualAssetLoadingStatus,
  ]);

  useEffect(() => {
    if (
      batchForIndividualAsset &&
      isPreFlight &&
      !selectedAssetVersionScoresLoadingStatus
    ) {
      dispatch(
        setSelectedAssetVersionScores(
          selectedVersionMediaId,
          selectedVersionedBatchId,
          null,
          channels,
          batchForIndividualAsset?.batchType,
          groupBy,
        ),
      );
    }
  }, [
    selectedVersionMediaId,
    selectedAssetVersionScoresLoadingStatus,
    batchForIndividualAsset,
  ]);

  useEffect(() => {
    const noBatchOrAdAccountToHandle =
      (!adAccountId || !currentAdAccountPlatform) && !batchForIndividualAsset;

    if (noBatchOrAdAccountToHandle) {
      return;
    }

    if (batchForIndividualAsset) {
      const { platformAdAccount, name } = batchForIndividualAsset;
      dispatch(
        // @ts-expect-error: state is not typed
        setSelectedScorecard({
          data: {
            ...batchForIndividualAsset,
            name: name || platformAdAccount?.platformAccountName,
          },
        }),
      );
      dispatch(resetSharedData());
    } else if (batchForIndividualAssetLoadingStatus === SUCCESS) {
      showToast('ui.compliance.singleAssetView.error', 'success');
      history.push(siteMap.creativeIntelligenceCompliance);
    }
  }, [
    batchForIndividualAsset,
    batchForIndividualAssetLoadingStatus,
    currentAdAccountPlatform,
    adAccountId,
  ]);

  useEffect(() => {
    if (
      isBatchSelected &&
      nonVersionedScoresLoadingStatus === NOT_LOADED &&
      !isPreFlight
    ) {
      dispatch(
        setIndividualAssetScores(
          mediaId,
          batchId,
          null,
          channels,
          batchForIndividualAsset?.batchType,
          groupBy,
        ),
      );
    }
  }, [
    batchForIndividualAsset,
    currentAdAccountPlatform,
    mediaObject,
    canShowKpiMetrics,
    isBatchSelected,
  ]);

  useEffect(() => {
    if (
      batchForIndividualAsset &&
      isPreFlight &&
      assetVersionsLoadingStatus === NOT_LOADED
    ) {
      const { baseReportId } = batchForIndividualAsset;
      dispatch(
        setAssetVersions(
          Number(baseReportId || batchId),
          Number(batchId),
          Number(mediaId),
        ),
      );
    }
  }, [batchForIndividualAsset, mediaId, assetVersionsLoadingStatus]);

  useEffect(
    () => () => {
      dispatch(resetState());
      dispatch(resetUploadsSlice());
      dispatch(resetFlaggedScores());
    },
    [],
  );

  const selectAssetVersion = (
    selectedId: string,
    selectedVersionBatchId: string,
  ) => {
    if (selectedId !== selectedVersionMediaId) {
      setSelectedVersionMediaId(selectedId);
      setSelectedVersionedBatchId(selectedVersionBatchId);
    }
  };

  const getBackLink = () => {
    return generatePath(siteMap.creativeScoringScoreCardDetails, {
      scorecardId: currentBatchId,
      // even for ad accounts we need to go back on the batch ID. each ad account has an internal batch.
    });
  };

  const onGroupByChange = (groupByOption: ListItem) => {
    const newPathParams: Record<string, string> = {
      ...urlParams,
      tab: routeParams.tabs.creativeIntelligenceCompliance.singleAssetView,
      groupBy: String(groupByOption.id),
    };

    const newUrl = generatePath(
      siteMap.creativeIntelligenceSingleAssetBatch,
      newPathParams,
    );
    history.push(newUrl);
  };

  const renderContent = () => {
    if (hasErrorFetchingBatchDetails) {
      return (
        <VidMobStack
          flexDirection="row"
          width="100%"
          height="100%"
          justifyContent="center"
          alignItems="center"
        >
          <BlankOrErrorState stateType="error" />
        </VidMobStack>
      );
    }

    return (
      <>
        <VidMobBox
          sx={{
            ...parentContentBox,
            width: `calc(100% - ${isVersionPanelOpen ? 312 : 0}px)`,
          }}
        >
          <IndividualAssetHeader
            isPreFlight={isPreFlight}
            isPluginMedia={isPluginMedia}
            workspaceId={workspaceId}
            channels={channels}
            mediaId={selectedVersionMediaId}
            mediaObject={mediaObject}
            isMediaLoading={isMediaLoading}
            hasMediaError={hasMediaError}
            areScoresLoading={
              isCriteriaGroupsInReportsEnabled
                ? false
                : batchForIndividualAssetLoadingStatus !== SUCCESS
            }
            hasScoresError={
              isCriteriaGroupsInReportsEnabled
                ? false
                : nonVersionedScoresLoadingStatus === FAILED
            }
            scorecardName={batchForIndividualAsset?.name}
            platformAdAccount={batchForIndividualAsset?.platformAdAccount}
            isVersionPanelOpen={isVersionPanelOpen}
            setIsVersionPanelOpen={setIsVersionPanelOpen}
            backLink={getBackLink()}
          />
          <IndividualAssetContentWrapper
            workspaceId={workspaceId}
            mediaId={selectedVersionMediaId}
            mediaObject={mediaObject}
            isMediaLoading={isMediaLoading}
            hasMediaError={hasMediaError}
            channels={channels}
            groupBy={groupBy}
            onGroupByChange={onGroupByChange}
            scorecardId={batchForIndividualAsset?.id}
            isV1Loading={
              !isCriteriaGroupsInReportsEnabled &&
              batchForIndividualAssetLoadingStatus !== SUCCESS
            }
            isMediaProcessing={isMediaProcessing}
          />
        </VidMobBox>
        <IndividualAssetVersionPanel
          isPanelOpen={isVersionPanelOpen}
          setIsPanelOpen={setIsVersionPanelOpen}
          selectAssetVersion={selectAssetVersion}
          selectedVersion={selectedVersionMediaId}
          latestMediaId={mediaId}
        />
      </>
    );
  };

  return (
    <VidMobStack flexDirection="row" width="100%" height="100%">
      {renderContent()}
    </VidMobStack>
  );
};

export default ScoringIndividualAssetViewV2;
