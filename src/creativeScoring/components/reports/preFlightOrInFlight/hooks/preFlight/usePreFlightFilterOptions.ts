import {
  Operator,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  ReportAdvancedFilterDefinition,
  ReportPillarSpecificAdvancedFilterKey,
  ValueType,
} from '../../../../../../components/ReportFilters/types';
import { CreativeIcon } from '../../../../../../assets/vidmob-mui-icons/general/V2-CreativeIcon';
import { createElement } from 'react';
import {
  CRITERIA_RESULTS_FILTER_DEFAULT_OPTIONS,
  CRITERIA_RESULTS_FILTER_OPTIONS,
  SCORING_MEDIA_TYPE_FILTER_OPTIONS,
} from '../../../../ScoringFilters/constants';
import { IdAndName } from '../../../../../../types/common.types';
import { isNumberWithinPercentageRange } from '../../../../../../components/ReportFilters/utils/typeCheckers';
import { IN_FLIGHT_REPORT_CREATIVE_ADHERENCE_INFO_TOOLTIP } from '../../../../../../components/ReportFilters/constants';
import { usePreFlightFilters } from './usePreFlightFilters';

const getCreativeIcon = () => createElement(CreativeIcon);

const usePreFlightFilterOptions = () => {
  const { disabledFilters } = usePreFlightFilters();
  const CREATIVE_TYPE_OPTIONS = [...SCORING_MEDIA_TYPE_FILTER_OPTIONS];

  const LOCAL_FILTER_DEFINITIONS: Partial<
    Record<
      ReportPillarSpecificAdvancedFilterKey,
      ReportAdvancedFilterDefinition & { valueOptions?: IdAndName[] }
    >
  > = {
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]: {
      key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE,
      labelKey:
        'ui.creative.intelligence.filtersDrawer.filter.creativeMediaType',
      defaultOperator: Operator.EQUALS,
      valueType: ValueType.MULTI,
      operators: [Operator.EQUALS],
      initialBlur: true,
      valueOptions: CREATIVE_TYPE_OPTIONS,
      icon: getCreativeIcon(),
      actsAsScopeFilter: true,
    },
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS]: {
      key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
      labelKey: 'ui.globalFilters.criteriaResults',
      defaultOperator: Operator.EQUALS,
      valueType: ValueType.MULTI,
      operators: [Operator.EQUALS],
      initialBlur: true,
      valueOptions: CRITERIA_RESULTS_FILTER_OPTIONS,
      icon: getCreativeIcon(),
      actsAsScopeFilter: true,
      getDefaultValue: () => ({
        key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
        value: CRITERIA_RESULTS_FILTER_DEFAULT_OPTIONS,
        operator: Operator.EQUALS,
      }),
      ...(disabledFilters.has(
        REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS,
      ) && {
        isDisabled: true,
        disabledTooltipKey: 'ui.globalFilters.criteriaResults.disabled.tooltip',
      }),
    },
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]: {
      key: REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
      labelKey:
        'ui.creative.intelligence.filtersDrawer.filter.creativeAdherence',
      valueType: ValueType.TEXT,
      textFieldInputType: 'number',
      numberInputRule: isNumberWithinPercentageRange,
      operators: [Operator.GREATER_THAN, Operator.LESS_THAN],
      defaultOperator: Operator.LESS_THAN,
      suffix: '%',
      icon: getCreativeIcon(),
      actsAsScopeFilter: true,
      ...(disabledFilters.has(
        REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE,
      )
        ? {
            isDisabled: true,
            disabledTooltipKey:
              'ui.creative.intelligence.filtersDrawer.filter.creativeAdherence.disabled.tooltip',
          }
        : {
            infoTooltipKey: IN_FLIGHT_REPORT_CREATIVE_ADHERENCE_INFO_TOOLTIP,
          }),
    },
  };

  const FILTER_OPTIONS_MAP: Partial<
    Record<ReportPillarSpecificAdvancedFilterKey, IdAndName[] | string>
  > = {
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]: CREATIVE_TYPE_OPTIONS,
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS]:
      CRITERIA_RESULTS_FILTER_OPTIONS,
    [REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE]: '',
  };

  return {
    CREATIVE_TYPE_OPTIONS,
    CRITERIA_RESULTS_FILTER_OPTIONS,
    LOCAL_FILTER_DEFINITIONS,
    FILTER_OPTIONS_MAP,
  };
};

export default usePreFlightFilterOptions;
