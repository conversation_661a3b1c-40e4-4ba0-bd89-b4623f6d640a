import { PreFlightFiltersContext } from '../../../../__subpages/PreFlightCheck/Filters/context/PreFlightFiltersContext';
import { useContext } from 'react';
import { SCORECARD_STATUS } from '../../../../../constants/scorecard.constants';
import { useToastAlert } from '../../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';

const useIsPreflightProcessing = () => {
  const showToastAlert = useToastAlert();
  const { scorecardStatus } = useContext(PreFlightFiltersContext);

  if (!scorecardStatus) {
    return false;
  }

  if (scorecardStatus === SCORECARD_STATUS.ERROR) {
    showToastAlert('ui.compliance.contentAudit.mediaProcessing.error', 'error');
    return false;
  }

  return [
    SCORECARD_STATUS.SUBMITTED,
    SCORECARD_STATUS.PROCESSING,
    SCORECARD_STATUS.RESUBMITTED,
  ].includes(scorecardStatus);
};

export default useIsPreflightProcessing;
