import {
  AdvancedFilterForBffBooleanType,
  AdvancedFilterForBffRangeType,
  AdvancedFiltersChannelType,
  AdvancedFiltersStorage,
  BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS,
  BackEndReportAdvancedFilterKey,
  BffTypeComparisonOperator,
  FiltersStorage,
  FilterValue,
  Operator,
  REPORT_CRITERIA_FILTERS,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  REPORT_SCOPE_PARAMETERS_FILTERS,
  ReportFilterDefinitions,
  ReportFilterKey,
} from '../../../../../components/ReportFilters/types';
import {
  CriteriaWithScore,
  InFlightReportSpecificProps,
  PRE_FLIGHT_IN_FLIGHT_CREATIVE_ADHERENCE_BACKEND_OPERATORS,
  PreFlightFilters,
  PreFlightInFlightFilters,
  PreFlightInFlightGroupBy,
  PreFlightInFlightScopeFilters,
  SavedInFlightReportAdvancedFilters,
  SavedInFlightReportAdvancedFiltersForChannel,
  SavedInFlightReportFilter,
  SavedInFlightReportFilters,
  SaveInFlightReportRequest,
} from '../types';
import { Workspace } from '../../../../../types/workspace.types';
import { IdAndName, NumberIdAndName } from '../../../../../types/common.types';
import { ScopeAdAccount } from '../../../../../types/adAccount.types';
import { ChannelType } from '../../../../../types/channels.types';
import { MediaType } from '../../../../../types/mediaTypes.types';
import {
  CriteriaResult,
  ScoringReportType,
} from '../../../../types/rollUpReports.types';
import { convertDateToYYYYMMDDString } from '../../../../../utils/dateRangePickerMUIUtils';
import { getFormattedAdvancedFilters } from '../../../ScoringFilters/utils/getters';
import {
  ALL_PLATFORMS,
  BACKEND_ADVANCED_FILTERS_KEYS_TO_REPORT_ADVANCED_FILTER_KEYS,
  CREATED_BY_VIDMOB_FILTER_OPTIONS,
  CRITERIA_CONSIDERATION_FILTER_OPTIONS,
  MINIMUM_DATE,
  ORGANIZATION_CRITERIA_FILTER_OPTIONS,
} from '../../../../../components/ReportFilters/constants';
import {
  IN_FLIGHT_PRE_FLIGHT_DEFAULT_ASSETS_SORT,
  DEFAULT_IN_FLIGHT_GROUP_BY,
  SAVED_IN_FLIGHT_REPORT_FILTER_KEYS_TO_REPORT_FILTER_KEYS,
  PRE_FLIGHT_IN_FLIGHT_CREATIVE_ADHERENCE_OPERATORS_TO_BACKEND_OPERATORS,
  PRE_FLIGHT_IN_FLIGHT_CREATIVE_ADHERENCE_BACKEND_OPERATORS_TO_FILTER_OPERATORS,
  DEFAULT_EMPTY_CRITERIA_RESULTS,
} from '../constants';
import BffPreFlightInFlightService from '../../../../../apiServices/BffPreFlightInFlightService';
import { downloadCSV } from '../../../../../utils/downloadCSV';
import vmErrorLog from '../../../../../utils/vmErrorLog';
import { CREATED_BY_VIDMOB } from '../../../../../types/createdByVidmob.types';
import { translateFilterName } from '../../../../../components/ReportFilters/utils/formatters';
import BffRollUpReportsService from '../../../../../apiServices/BffRollUpReportsService';
import dayjs from 'dayjs';

export const getScopeFilterParams = (
  filters: FiltersStorage,
  channelIdOverride?: AdvancedFiltersChannelType,
): PreFlightInFlightScopeFilters => {
  const workspaces = filters[REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]
    ?.value as Workspace[];
  const workspaceIds = workspaces?.map((workspace) => workspace.id);
  const channel = filters[REPORT_SCOPE_PARAMETERS_FILTERS.CHANNEL]
    ?.value as IdAndName;
  const channelId = channel?.id as ChannelType;
  const brands = filters[REPORT_SCOPE_PARAMETERS_FILTERS.BRANDS]
    ?.value as IdAndName[];
  const brandIds = brands?.length ? brands.map((brand) => brand.id) : undefined;
  const markets = filters[REPORT_SCOPE_PARAMETERS_FILTERS.MARKETS]
    ?.value as IdAndName[];
  const marketIds = markets?.length
    ? markets.map((market) => market.id)
    : undefined;
  const adAccounts = filters[REPORT_SCOPE_PARAMETERS_FILTERS.AD_ACCOUNTS]
    ?.value as ScopeAdAccount[];
  const adAccountIds = adAccounts?.length
    ? adAccounts.map((adAccount) => adAccount.id)
    : undefined;

  return {
    workspaceIds,
    channel:
      channelIdOverride === ALL_PLATFORMS
        ? channelId
        : channelIdOverride || channelId,
    brandIds,
    markets: marketIds,
    platformAccountIds: adAccountIds,
  };
};

export const getScoreResults = (
  criteriaResults: IdAndName[],
): CriteriaResult[] | undefined => {
  if (!criteriaResults?.length) {
    return DEFAULT_EMPTY_CRITERIA_RESULTS;
  }

  const criteriaResultIds = criteriaResults.map(
    (criteriaResult) => criteriaResult.id as CriteriaResult,
  );

  return criteriaResultIds;
};

export const getHasScoreResultsApplied = (
  isInFlight: boolean,
  filters: FiltersStorage | PreFlightFilters,
) => {
  const criteriaResults =
    filters[REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS];

  if (isInFlight) {
    const inFlightCriteriaResults = (criteriaResults as FilterValue)
      ?.value as IdAndName[];
    return Boolean(getScoreResults(inFlightCriteriaResults as IdAndName[]));
  }

  const preFlightCriteriaResults = criteriaResults as CriteriaResult[];
  return (
    preFlightCriteriaResults?.length > 0 && preFlightCriteriaResults?.length < 3
  );
};

interface GetInFlightFiltersForRequestParams {
  filters: FiltersStorage;
  filterDefinitions: Partial<ReportFilterDefinitions>;
  advancedFilters?: AdvancedFiltersStorage;
  channelId?: AdvancedFiltersChannelType;
  mediaNameSearchText?: string;
  includeStandardCriteria?: boolean;
  selectedCriteria?: CriteriaWithScore | null;
  isAllTimeSelectedOnSave?: boolean;
  groupBy?: PreFlightInFlightGroupBy;
  groupById?: string | null;
}

export const getInFlightFiltersForRequest = ({
  filters,
  filterDefinitions,
  advancedFilters,
  channelId,
  mediaNameSearchText,
  includeStandardCriteria,
  selectedCriteria,
  isAllTimeSelectedOnSave,
  groupBy,
  groupById,
}: GetInFlightFiltersForRequestParams): PreFlightInFlightFilters => {
  const dateRange = filters[REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]
    ?.value as [string, string];
  const [startDate, endDate] = dateRange.map(convertDateToYYYYMMDDString);

  const applicableFormats = selectedCriteria?.applicability.split(',');

  const mediaTypes = filters[REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.MEDIA_TYPE]
    ?.value as IdAndName[];
  const mediaTypeIds = mediaTypes?.length
    ? mediaTypes.map((mediaType) => mediaType.id as MediaType)
    : undefined;

  const filteredMediaTypes =
    applicableFormats && mediaTypeIds
      ? (mediaTypeIds.filter((mediaType) =>
          applicableFormats.includes(mediaType),
        ) as MediaType[])
      : mediaTypeIds;

  const criteriaResults = filters[
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CRITERIA_RESULTS
  ]?.value as IdAndName[];

  const scoreResults = getScoreResults(criteriaResults);

  const creativeAdherenceFilter =
    filters[REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.CREATIVE_ADHERENCE];
  const creativeAdherenceOperator = creativeAdherenceFilter?.operator as
    | Operator.GREATER_THAN
    | Operator.LESS_THAN;
  const creativeAdherenceValue = creativeAdherenceFilter?.value as string;
  const creativeAdherence = creativeAdherenceValue?.length
    ? {
        [PRE_FLIGHT_IN_FLIGHT_CREATIVE_ADHERENCE_OPERATORS_TO_BACKEND_OPERATORS[
          creativeAdherenceOperator
        ]]: Number(creativeAdherenceValue),
      }
    : undefined;

  // If creative adherence filter is applied, we need to send all score results
  const adjustedScoreResults = creativeAdherence
    ? DEFAULT_EMPTY_CRITERIA_RESULTS
    : scoreResults;
  // If score results are applied, we need to remove the creative adherence
  const adjustedCreativeAdherence =
    scoreResults && scoreResults?.length > 0 && scoreResults?.length < 3
      ? undefined
      : creativeAdherence;

  const criteria = filters[REPORT_CRITERIA_FILTERS.CRITERIA_RULE]
    ?.value as NumberIdAndName[];
  const criteriaIds = criteria?.length
    ? criteria.map((criterion) => criterion.id as number)
    : undefined;

  const criteriaGroups = filters[REPORT_CRITERIA_FILTERS.CRITERIA_GROUP]
    ?.value as IdAndName[];

  const criteriaGroupIds = criteriaGroups?.length
    ? criteriaGroups.map((criteriaGroup) => criteriaGroup.id)
    : undefined;

  const isOrganizationCriteriaValue = filters[
    REPORT_CRITERIA_FILTERS.ORGANIZATION_CRITERIA
  ]?.value as IdAndName;
  const criteriaIsOrganizationCriteria = isOrganizationCriteriaValue
    ? Boolean(isOrganizationCriteriaValue.id === 'true')
    : undefined;

  const isOptionalCriteriaValue = filters[
    REPORT_CRITERIA_FILTERS.CRITERIA_CONSIDERATION
  ]?.value as IdAndName;
  const criteriaIsOptional = isOptionalCriteriaValue
    ? Boolean(isOptionalCriteriaValue.id === 'true')
    : undefined;

  const analyticsFilters = advancedFilters
    ? getFormattedAdvancedFilters(advancedFilters, filterDefinitions)
    : undefined;

  return {
    ...getScopeFilterParams(filters, channelId),
    startDate: isAllTimeSelectedOnSave ? null : startDate,
    endDate: isAllTimeSelectedOnSave ? null : endDate,
    mediaTypes: filteredMediaTypes,
    scoreResults: adjustedScoreResults,
    creativeAdherence: adjustedCreativeAdherence,
    criteriaIds,
    criteriaGroupIds,
    criteriaIsOrganizationCriteria,
    criteriaIsOptional,
    analyticsFilters,
    mediaNameSearchText,
    includeStandardCriteria,
    groupBy,
    groupById,
  };
};

interface GetSaveInFlightReportRequestParams {
  inFlightReportSpecificProps?: InFlightReportSpecificProps;
  workspaceId: number;
  name: string;
  description?: string;
  filters: FiltersStorage;
  filterDefinitions: Partial<ReportFilterDefinitions>;
  advancedFilters?: AdvancedFiltersStorage;
}

export const getSaveInFlightReportRequestParams = ({
  inFlightReportSpecificProps,
  workspaceId,
  name,
  description,
  filters,
  filterDefinitions,
  advancedFilters,
}: GetSaveInFlightReportRequestParams): SaveInFlightReportRequest => {
  const reportToSave = {
    workspaceId,
    name,
    description,
    reportType: ScoringReportType.IN_FLIGHT,
    filtersVersion: 2,
    filters: getInFlightFiltersForRequest({
      filters,
      filterDefinitions,
      advancedFilters,
      mediaNameSearchText: inFlightReportSpecificProps?.mediaNameSearchText,
      isAllTimeSelectedOnSave: inFlightReportSpecificProps?.isAllTimeSelected,
    }),
    sortBy: {
      sortBy:
        inFlightReportSpecificProps?.sortModel?.sortBy ||
        IN_FLIGHT_PRE_FLIGHT_DEFAULT_ASSETS_SORT.sortBy,
      sortOrder:
        inFlightReportSpecificProps?.sortModel?.sortDirection ||
        IN_FLIGHT_PRE_FLIGHT_DEFAULT_ASSETS_SORT.sortDirection,
    },
    groupBy: inFlightReportSpecificProps?.groupBy || DEFAULT_IN_FLIGHT_GROUP_BY,
    aggregationColumns: [],
  };

  if (inFlightReportSpecificProps?.isAllTimeSelected) {
    reportToSave.filters = {
      ...reportToSave.filters,
      startDate: null,
      endDate: null,
    };
  }

  return reportToSave;
};

interface DownloadSavedInFlightReportCSVParams {
  reportId: string;
  organizationId: string;
  reportTitle: string;
  showToastAlert: (message: string, type: 'error') => void;
  setIsCsvDownloading: (isDownloading: boolean) => void;
}

export const downloadSavedInFlightReportCSV = async ({
  reportId,
  organizationId,
  reportTitle,
  showToastAlert,
  setIsCsvDownloading,
}: DownloadSavedInFlightReportCSVParams) => {
  setIsCsvDownloading(true);

  try {
    const savedReport =
      await BffRollUpReportsService.getReportMetadataByReportId({
        reportId,
        organizationId,
      });

    if (!savedReport) {
      setIsCsvDownloading(false);
      showToastAlert('ui.creativeScoring.inFlightReport.csv.error', 'error');
    }

    const { filters, sortBy } = savedReport;

    const csvData = await BffPreFlightInFlightService.getInFlightReportCsv(
      organizationId,
      filters,
      { sortBy: sortBy.sortBy, sortDirection: sortBy.sortOrder },
    );

    if (csvData) {
      downloadCSV({
        csvData,
        fileName: `${reportTitle || 'In Flight'}.csv`,
      });
    }

    setIsCsvDownloading(false);
  } catch (error) {
    setIsCsvDownloading(false);
    vmErrorLog(error as Error, 'getInFlightReportCSV');
    showToastAlert('ui.creativeScoring.inFlightReport.csv.error', 'error');
  }
};

export const getInFlightReportCSV = async (
  organizationId: string,
  {
    filters,
    filterDefinitions,
    advancedFilters,
  }: GetInFlightFiltersForRequestParams,
  showToastAlert: (messageKey: string, type: 'error') => void,
  setIsCsvDownloading: (isCsvDownloading: boolean) => void,
  inFlightReportSpecificProps?: InFlightReportSpecificProps,
  reportTitle?: string,
) => {
  if (!inFlightReportSpecificProps?.sortModel) {
    return;
  }

  setIsCsvDownloading(true);

  try {
    const inFlightReportRequestParams = getInFlightFiltersForRequest({
      filters,
      filterDefinitions,
      advancedFilters,
      mediaNameSearchText: inFlightReportSpecificProps?.mediaNameSearchText,
    });

    const csvData = await BffPreFlightInFlightService.getInFlightReportCsv(
      organizationId,
      inFlightReportRequestParams,
      inFlightReportSpecificProps.sortModel,
    );

    if (csvData) {
      downloadCSV({
        csvData,
        fileName: `${reportTitle || 'In Flight'}.csv`,
      });
    }
    setIsCsvDownloading(false);
  } catch (error) {
    setIsCsvDownloading(false);
    vmErrorLog(error as Error, 'getInFlightReportCSV');
    showToastAlert('ui.creativeScoring.inFlightReport.csv.error', 'error');
  }
};

interface GetInFlightReportRequestParams {
  startDate: string | null;
  endDate: string | null;
  setIsInFlightReportAllTimeSelected: (
    isInFlightReportAllTimeSelected: boolean,
  ) => void;
}

const getDateRangeForSavedReport = ({
  startDate,
  endDate,
  setIsInFlightReportAllTimeSelected,
}: GetInFlightReportRequestParams): [string, string] => {
  if (startDate && endDate) {
    return [startDate, endDate];
  }

  setIsInFlightReportAllTimeSelected(true);
  return [MINIMUM_DATE, dayjs().format('YYYY-MM-DD')];
};

export const formatSavedInFlightReportFilters = (
  savedFilters: SavedInFlightReportFilters,
  setIsInFlightReportAllTimeSelected: (
    isInFlightReportAllTimeSelected: boolean,
  ) => void,
): FilterValue[] => {
  const dateRange = getDateRangeForSavedReport({
    startDate: savedFilters[SavedInFlightReportFilter.START_DATE],
    endDate: savedFilters[SavedInFlightReportFilter.END_DATE],
    setIsInFlightReportAllTimeSelected,
  });

  const formattedFilters: FilterValue[] = [
    {
      key: REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
      operator: Operator.BETWEEN,
      value: dateRange,
    },
  ];

  Object.entries(savedFilters).forEach(([key, value]) => {
    const frontEndKey =
      SAVED_IN_FLIGHT_REPORT_FILTER_KEYS_TO_REPORT_FILTER_KEYS[
        key as SavedInFlightReportFilter
      ] as ReportFilterKey;
    switch (key) {
      case SavedInFlightReportFilter.WORKSPACES:
      case SavedInFlightReportFilter.BRANDS:
      case SavedInFlightReportFilter.MARKETS:
      case SavedInFlightReportFilter.PLATFORM_ACCOUNTS:
      case SavedInFlightReportFilter.MEDIA_TYPES:
      case SavedInFlightReportFilter.SCORE_RESULTS:
      case SavedInFlightReportFilter.CRITERIA:
      case SavedInFlightReportFilter.CRITERIA_GROUPS:
        formattedFilters.push({
          key: frontEndKey,
          operator: Operator.IN,
          value,
        });
        break;
      case SavedInFlightReportFilter.CHANNEL:
        formattedFilters.push({
          key: frontEndKey,
          operator: Operator.EQUALS,
          value,
        });
        break;
      case SavedInFlightReportFilter.CRITERIA_CONSIDERATION:
        formattedFilters.push({
          key: frontEndKey,
          operator: Operator.EQUALS,
          value: CRITERIA_CONSIDERATION_FILTER_OPTIONS.find(
            (option) => option.id === value.toString(),
          ),
        });
        break;
      case SavedInFlightReportFilter.ORGANIZATION_CRITERIA:
        formattedFilters.push({
          key: frontEndKey,
          operator: Operator.EQUALS,
          value: ORGANIZATION_CRITERIA_FILTER_OPTIONS.find(
            (option) => option.id === value.toString(),
          ),
        });
        break;
      case SavedInFlightReportFilter.CREATIVE_ADHERENCE:
        // eslint-disable-next-line no-case-declarations
        const criteriaScores =
          savedFilters[SavedInFlightReportFilter.SCORE_RESULTS] || [];
        if (
          // do not load Creative Adherence if report has a customized the Criteria Results filter
          !(criteriaScores.length > 0 && criteriaScores.length < 3)
        ) {
          const operatorAndValue = Object.entries(value || {})[0];
          const operator =
            (operatorAndValue?.[0] as PRE_FLIGHT_IN_FLIGHT_CREATIVE_ADHERENCE_BACKEND_OPERATORS) ||
            null;
          const adherencePercentage = operatorAndValue?.[1] || null;

          if (operator && typeof adherencePercentage === 'number') {
            formattedFilters.push({
              key: frontEndKey,
              operator:
                PRE_FLIGHT_IN_FLIGHT_CREATIVE_ADHERENCE_BACKEND_OPERATORS_TO_FILTER_OPERATORS[
                  operator
                ],
              value: adherencePercentage.toString(),
            });
          }
        }
        break;
      default:
        break;
    }
  });

  return formattedFilters;
};

const formatSavedInFlightReportAdvancedFiltersForChannel = (
  savedFiltersForChannel: SavedInFlightReportAdvancedFiltersForChannel,
): FiltersStorage => {
  const formattedFiltersForChannel: FiltersStorage = {};

  Object.entries(savedFiltersForChannel).forEach(([key, value]) => {
    const frontEndKey =
      BACKEND_ADVANCED_FILTERS_KEYS_TO_REPORT_ADVANCED_FILTER_KEYS[
        key as BackEndReportAdvancedFilterKey
      ];

    switch (key) {
      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.CAMPAIGN_IDENTIFIER:
      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.AD_SET_IDENTIFIER:
      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.AD_IDENTIFIER:
        formattedFiltersForChannel[frontEndKey] = {
          key: frontEndKey,
          value,
          operator: Operator.IN,
        };
        break;

      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.CAMPAIGN_OBJECTIVE:
      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.AD_PLACEMENT:
      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.AD_TYPE:
        // eslint-disable-next-line no-case-declarations
        const idAndNamesValue = value as IdAndName[];
        formattedFiltersForChannel[frontEndKey] = {
          key: frontEndKey,
          value: idAndNamesValue.map((selectedItem: IdAndName) => ({
            id: selectedItem.name,
            name: translateFilterName(selectedItem.name, frontEndKey),
          })),
          operator: Operator.IN,
        };
        break;

      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.AD_IMPRESSION:
      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.CREATIVE_IMPRESSION:
        // eslint-disable-next-line no-case-declarations
        const rangeValue = value as AdvancedFilterForBffRangeType;
        formattedFiltersForChannel[frontEndKey] = {
          key: frontEndKey,
          value: rangeValue.value,
          operator:
            rangeValue.operator === BffTypeComparisonOperator.GREATER_THAN
              ? Operator.GREATER_THAN
              : Operator.LESS_THAN,
        };
        break;

      case BACKEND_REPORT_ADVANCED_FILTER_IDENTIFIERS.CREATIVE_BY_VIDMOB:
        // eslint-disable-next-line no-case-declarations
        const booleanValue = value as AdvancedFilterForBffBooleanType;
        // eslint-disable-next-line no-case-declarations
        const adjustedValue = booleanValue.map((selectedBoolean) => {
          if (selectedBoolean === 'true') {
            return CREATED_BY_VIDMOB_FILTER_OPTIONS.find(
              (option) => option.id === CREATED_BY_VIDMOB.CREATED_BY_VIDMOB,
            );
          } else {
            return CREATED_BY_VIDMOB_FILTER_OPTIONS.find(
              (option) => option.id === CREATED_BY_VIDMOB.NOT_CREATED_BY_VIDMOB,
            );
          }
        });
        formattedFiltersForChannel[frontEndKey] = {
          key: frontEndKey,
          value: adjustedValue,
          operator: Operator.IN,
        };
        break;

      default:
        break;
    }
  });

  return formattedFiltersForChannel;
};

export const formatSavedInFlightReportAdvancedFilters = (
  savedAdvancedFilters: SavedInFlightReportAdvancedFilters,
): AdvancedFiltersStorage => {
  const formattedAdvancedFilters: AdvancedFiltersStorage = {};

  Object.entries(savedAdvancedFilters || {}).forEach(
    ([channelId, filtersForChannel]) => {
      formattedAdvancedFilters[channelId as AdvancedFiltersChannelType] =
        formatSavedInFlightReportAdvancedFiltersForChannel(filtersForChannel);
    },
  );

  return formattedAdvancedFilters;
};
