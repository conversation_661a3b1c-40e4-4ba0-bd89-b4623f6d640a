import React, { useRef } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../../../../../vidMobComponentWrappers';
import { CriteriaWithScore } from '../../../../../types';
import { GLOBALS } from '../../../../../../../../../constants';
import {
  BestPracticeIcon,
  ProcessingIcon,
} from '../../../../../../../../../assets/vidmob-mui-icons/general';
import CriteriaIsOptionalPill from '../../../../../../../shared/CriteriaIsOptionalPill';
import { convertServerCriterionToCriteriaDetailsCriterionForPreFlightScorecardAndInFlightReport } from '../../../../../../../shared/CriteriaDetailsPopover.tx/criteriaDetailsPopover.utils';
import { isNil } from '../../../../../../../../../utils/typeCheckUtils';
import useReportFilters from '../../../../../../../../../components/ReportFilters/hooks/useReportFilters';
import { REPORT_SCOPE_PARAMETERS_FILTERS } from '../../../../../../../../../components/ReportFilters/types';
import { Workspace } from '../../../../../../../../../types/workspace.types';
import useCriteriaDetailsPopoverV2 from '../../../../../../../shared/CriteriaDetailsPopover.tx/useCriteriaDetailsPopoverV2';

const { EM_DASH_UNICODE } = GLOBALS;

const containerSx = {
  maxHeight: '36px',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  cursor: 'pointer',
  borderRadius: '6px',
  p: '8px 12px',
  gap: '12px',
  '&:hover': {
    backgroundColor: 'secondary.main',
    '.MuiChip-root': {
      backgroundColor: 'secondary.dark',
    },
  },
};

const selectedContainerSx = {
  ...containerSx,
  color: 'primary.main',
  backgroundColor: 'primary.light',
  '&:hover': {},
};

const detailsSx = {
  width: '300px',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '8px',
};

const nameSx = {
  height: '21px',
  maxWidth: '200px',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  '&:hover': {
    borderBottom: '1px dashed',
    borderBottomColor: 'text.secondary',
  },
};

const selectedNameSx = {
  ...nameSx,
  '&:hover': {
    borderBottom: '1px dashed',
    borderBottomColor: 'primary.main',
  },
};

const iconSx = {
  width: '14px',
  height: '14px',
};

const optionalCriteriaPillSx = {
  m: 0,
};

interface Props {
  criteriaAndScore: CriteriaWithScore;
  isSelected: boolean;
  onSelect: (criteriaId: number) => void;
  isMediaProcessing: boolean;
}

export const CriteriaScore = ({
  criteriaAndScore,
  isSelected,
  onSelect,
  isMediaProcessing,
}: Props) => {
  const intl = useIntl();
  const { filters } = useReportFilters();
  const workspaces = filters[REPORT_SCOPE_PARAMETERS_FILTERS.WORKSPACES]
    ?.value as Workspace[];

  const {
    id,
    name,
    rule,
    passedPercent,
    customIconUrl,
    isBestPractice,
    isOptional,
  } = criteriaAndScore;

  const BestPracticeIconAndTooltip = () => (
    <VidMobTooltip
      placement="top"
      title={intl.formatMessage({
        id: 'ui.creativeScoring.inFlightReport.tab.criteria.accordion.tooltip.bestPractice',
        defaultMessage: 'Channel best practice',
      })}
      disableInteractive
    >
      <VidMobBox sx={iconSx}>
        <BestPracticeIcon sx={iconSx} />
      </VidMobBox>
    </VidMobTooltip>
  );

  const handleOnClick = () => {
    onSelect(id);
  };

  const criteriaScoreRef = useRef<HTMLDivElement>(null);
  const currentCriteriaScoreElement = criteriaScoreRef?.current;
  const rect = currentCriteriaScoreElement?.getBoundingClientRect();

  const { renderPopover, handlePopoverOpen, handlePopoverClose } =
    useCriteriaDetailsPopoverV2({
      criterion:
        convertServerCriterionToCriteriaDetailsCriterionForPreFlightScorecardAndInFlightReport(
          criteriaAndScore,
          workspaces,
          intl,
        ),
      customIconUrl,
      customAnchor: {
        anchorReference: 'anchorPosition',
        anchorPosition: {
          top: (rect?.top || 0) - 8,
          left: (rect?.left || 0) + 350,
        },
      },
    });

  const renderScore = () => {
    if (isMediaProcessing) {
      return <ProcessingIcon sx={iconSx} isRotating />;
    }

    return (
      <VidMobTypography variant={isSelected ? 'subtitle2' : 'body2'}>
        {isNil(passedPercent) ? EM_DASH_UNICODE : `${passedPercent}%`}
      </VidMobTypography>
    );
  };

  return (
    <VidMobStack
      ref={criteriaScoreRef}
      sx={isSelected ? selectedContainerSx : containerSx}
      onClick={handleOnClick}
    >
      <VidMobStack sx={detailsSx}>
        <VidMobTypography
          onMouseEnter={handlePopoverOpen}
          onMouseLeave={handlePopoverClose}
          variant={isSelected ? 'subtitle2' : 'body2'}
          sx={isSelected ? selectedNameSx : nameSx}
        >
          {name || rule}
        </VidMobTypography>
        {renderPopover()}
        {customIconUrl && <VidMobAvatar src={customIconUrl} sx={iconSx} />}
        {isBestPractice && <BestPracticeIconAndTooltip />}
        {isOptional && (
          <CriteriaIsOptionalPill additionalStyles={optionalCriteriaPillSx} />
        )}
      </VidMobStack>
      {renderScore()}
    </VidMobStack>
  );
};
