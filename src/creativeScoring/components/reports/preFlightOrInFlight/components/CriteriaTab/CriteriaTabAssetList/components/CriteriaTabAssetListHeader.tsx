import React from 'react';
import getMUIIconForChannel from '../../../../../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { getPlatformIdentifierForLogo } from '../../../../../../../../utils/feConstantsUtils';
import {
  VidMobAvatar,
  VidMobStack,
  VidMobTypography,
} from '../../../../../../../../vidMobComponentWrappers';
import { CriteriaWithScore } from '../../../../types';
import { AssetListPagination } from '../../../sharedComponents/AssetListPagination';
import { PaginationParams } from '../../../../../../../../types/pagination.types';
import ShimmerAndFadeLoadingState from '../../../../../../../../components/ShimmerAndFadeLoadingState';
import { Tooltip } from '@mui/material';

const headerSx = {
  minHeight: '32px',
  maxHeight: '32px',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
};

const loadingHeaderSx = {
  flexDirection: 'row',
  alignItems: 'center',
  gap: '6px',
};

const criteriaDetailsSx = {
  minHeight: '24px',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '6px',
};

const iconSx = {
  width: '20px',
  height: '20px',
  svg: {
    width: '100%',
    height: '100%',
  },
};

const criteriaNameSx = {
  maxWidth: '400px',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
};

interface Props {
  selectedCriteria: CriteriaWithScore | null;
  totalAssetCount?: number;
  isMediaListLoading: boolean;
  paginationParams: PaginationParams;
  onPageChange: (_: any, page: number) => void;
}

export const CriteriaTabAssetListHeader = ({
  selectedCriteria,
  totalAssetCount,
  isMediaListLoading,
  paginationParams,
  onPageChange,
}: Props) => {
  if (!selectedCriteria) {
    return null;
  }

  const { channel, name, rule, customIconUrl } = selectedCriteria;

  const displayName = name || rule;

  return (
    <VidMobStack sx={headerSx}>
      <VidMobStack sx={criteriaDetailsSx}>
        {isMediaListLoading ? (
          <VidMobStack sx={loadingHeaderSx}>
            <ShimmerAndFadeLoadingState
              height="20px"
              width="20px"
              borderRadius="50%"
            />
            <ShimmerAndFadeLoadingState
              height="24px"
              width="220px"
              borderRadius="8px"
            />
          </VidMobStack>
        ) : (
          <>
            {channel &&
              getMUIIconForChannel(
                getPlatformIdentifierForLogo(channel),
                iconSx,
              )}
            <Tooltip title={displayName} placement="top">
              <VidMobTypography variant="subtitle1" sx={criteriaNameSx}>
                {displayName}
              </VidMobTypography>
            </Tooltip>
            {customIconUrl && <VidMobAvatar src={customIconUrl} sx={iconSx} />}
          </>
        )}
      </VidMobStack>

      <AssetListPagination
        totalAssetCount={totalAssetCount}
        paginationParams={paginationParams}
        onPageChange={onPageChange}
        alignRight
      />
    </VidMobStack>
  );
};
