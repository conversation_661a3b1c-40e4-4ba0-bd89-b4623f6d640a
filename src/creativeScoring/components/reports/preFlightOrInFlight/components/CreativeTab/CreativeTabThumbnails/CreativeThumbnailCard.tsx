import React, { useEffect, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import {
  VidMobBox,
  VidMobCardMedia,
  VidMobChip,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../../../vidMobComponentWrappers';
import { GroupAggregateScore, MediaWithScore } from '../../../types';
import { GLOBALS } from '../../../../../../../constants';
import { Typography } from '@mui/material';
import MediaPreview from '../../../../../../../creativeAnalytics/components/MediaPreview/MediaPreview';
import { isNil } from '../../../../../../../utils/typeCheckUtils';
import { ProcessingIcon } from '../../../../../../../assets/vidmob-mui-icons/general';
import { getTextColor } from '../../../../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells/CriteriaGroupCell';

const { EM_DASH_UNICODE } = GLOBALS;

const containerSx = {
  flexDirection: 'column',
  minWidth: '212px',
  cursor: 'pointer',
};

const thumbnailContainerSx = {
  position: 'relative',
};

const thumbnailSx = {
  width: '100%',
  borderRadius: '6px 6px 0 0',
  borderWidth: '1px 1px 0 1px',
  borderStyle: 'solid',
  borderColor: 'action.disabled',
};

const thumbnailOverlaySx = {
  ...thumbnailSx,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'absolute',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
};

const thumbnailOverlayTextContainerSx = {
  backgroundColor: 'rgba(255, 255, 255, 0.08)',
  borderRadius: '6px',
  width: '92px',
  height: '32px',
  p: '6px 12px',
};

const thumbnailOverlayTextSx = {
  color: 'primary.contrastText',
};

const detailsSx = {
  borderRadius: '0 0 6px 6px',
  border: '1px solid',
  borderColor: 'action.disabled',
  p: '12px 16px',
  gap: '8px',
};

const nameSx = {
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  overflow: 'hidden',
};

const scoreRowsSx = {
  flexDirection: 'column',
  gap: '6px',
};

const scoreRowSx = {
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '8px',
};

const chipSx = {
  maxWidth: '75%',
};

const iconAndGroupSx = {
  flexDirection: 'row',
  alignItems: 'center',
  gap: '6px',
  maxWidth: '160px',
};

const processingIconSx = {
  width: '14px',
  height: '14px',
};

const iconContainerSx = {
  width: '16px',
  height: '16px',
  svg: {
    width: '100%',
    height: '100%',
  },
};

interface Props {
  isInFlight: boolean;
  groups: GroupAggregateScore[];
  mediaWithScore: MediaWithScore;
  onAssetClick: (params: { id: string | number }) => void;
  isMediaProcessing: boolean;
}

export const CreativeThumbnailCard = ({
  isInFlight,
  groups,
  mediaWithScore,
  onAssetClick,
  isMediaProcessing,
}: Props) => {
  const intl = useIntl();
  const [isHovered, setIsHovered] = useState(false);
  const {
    id,
    mediaObject,
    passedPercent,
    passedPercentByChannel,
    passedPercentByGroup,
  } = mediaWithScore;
  const mediaName = mediaObject?.displayName || mediaObject?.name || '';
  const height = mediaObject?.height || 640;
  const width = mediaObject?.width || 640;
  const thumbnails = Object.values(mediaObject?.thumbnails || {});
  const thumbnail = thumbnails[0];
  const aspectRatio = height / width;

  const [currentWidth, setCurrentWidth] = useState(255);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }

    const resizeObserver = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        setCurrentWidth(entry.contentRect.width);
      });
    });

    resizeObserver.observe(containerRef.current);

    return () => resizeObserver.disconnect();
  }, []);

  const computedHeight = currentWidth * aspectRatio;

  const renderThumbnailOverlay = () => (
    <VidMobStack sx={thumbnailOverlaySx}>
      <VidMobStack sx={thumbnailOverlayTextContainerSx}>
        <VidMobTypography variant="subtitle2" sx={thumbnailOverlayTextSx}>
          {intl.formatMessage({
            id: 'ui.creativeScoring.inFlightReport.tab.creative.assets.thumbnail.overlay.viewAsset',
            defaultMessage: 'View asset',
          })}
        </VidMobTypography>
      </VidMobStack>
    </VidMobStack>
  );

  const renderAverageScore = () => {
    const formattedAverageScore = isNil(passedPercent)
      ? EM_DASH_UNICODE
      : `${passedPercent}%`;

    return (
      <VidMobStack sx={scoreRowSx}>
        <VidMobTypography variant="body2">
          {intl.formatMessage({
            id: 'ui.creativeScoring.inFlightReport.tab.creative.assets.dataGrid.column.average',
            defaultMessage: 'Average',
          })}
        </VidMobTypography>
        {isMediaProcessing ? (
          <ProcessingIcon sx={processingIconSx} isRotating />
        ) : (
          <VidMobTypography variant="body2">
            {formattedAverageScore}
          </VidMobTypography>
        )}
      </VidMobStack>
    );
  };

  const renderScores = () =>
    groups.map((group) => {
      const { id, displayName, icon, color } = group;

      const renderGroupDescription = () => {
        if (color) {
          return (
            <VidMobChip
              sx={{ ...chipSx, backgroundColor: color }}
              label={
                <VidMobStack direction="row" alignItems="center">
                  <VidMobTooltip
                    title={displayName}
                    placement="top"
                    disableInteractive
                  >
                    <Typography
                      variant="subtitle3"
                      sx={{ ...nameSx, color: getTextColor(color) }}
                    >
                      {displayName}
                    </Typography>
                  </VidMobTooltip>
                </VidMobStack>
              }
            />
          );
        }

        return (
          <VidMobStack sx={iconAndGroupSx}>
            {icon && <VidMobBox sx={iconContainerSx}>{icon}</VidMobBox>}
            <VidMobTooltip
              title={displayName}
              placement="top"
              disableInteractive
            >
              <Typography variant="body2" sx={nameSx}>
                {displayName}
              </Typography>
            </VidMobTooltip>
          </VidMobStack>
        );
      };

      const renderScore = () => {
        if (isMediaProcessing) {
          return <ProcessingIcon sx={processingIconSx} isRotating />;
        }

        const scores = passedPercentByChannel || passedPercentByGroup;
        const score = scores?.[id] ?? null;
        const formattedScore = isNil(score) ? EM_DASH_UNICODE : `${score}%`;

        return (
          <VidMobTypography variant="body2">{formattedScore}</VidMobTypography>
        );
      };

      return (
        <VidMobStack key={id} sx={scoreRowSx}>
          {renderGroupDescription()}
          {renderScore()}
        </VidMobStack>
      );
    });

  return (
    <VidMobStack
      ref={containerRef}
      sx={containerSx}
      onClick={() => onAssetClick({ id })}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {thumbnail && (
        <VidMobStack sx={thumbnailContainerSx}>
          <VidMobCardMedia
            image={thumbnail.url}
            sx={{
              ...thumbnailSx,
              height: computedHeight,
            }}
          />
          {isHovered && renderThumbnailOverlay()}
        </VidMobStack>
      )}
      <VidMobStack sx={detailsSx}>
        <VidMobTooltip title={mediaName} placement="top">
          <Typography variant="subtitle2" sx={nameSx}>
            {mediaName}
          </Typography>
        </VidMobTooltip>
        <VidMobStack sx={scoreRowsSx}>
          {!isInFlight && renderAverageScore()}
          {renderScores()}
        </VidMobStack>
      </VidMobStack>
      {isHovered && thumbnail && (
        <MediaPreview mediaPreview={mediaObject} rightPosition={24} />
      )}
    </VidMobStack>
  );
};
