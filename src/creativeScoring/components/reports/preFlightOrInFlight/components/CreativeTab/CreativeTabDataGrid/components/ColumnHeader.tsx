import React from 'react';
import { GridColumnHeaderParams } from '@mui/x-data-grid-pro';
import {
  VidMobBox,
  VidMobChip,
  VidMobStack,
  VidMobTooltip,
} from '../../../../../../../../vidMobComponentWrappers';
import { Typography } from '@mui/material';
import { getTextColor } from '../../../../../../criteriaManagement/CriteriaManagementDataGrid/DataGridCells/CriteriaGroupCell';

const containerSx = {
  flexDirection: 'row',
  gap: '6px',
};

const chipSx = {
  cursor: 'pointer',
};

const iconSx = {
  display: 'flex',
  width: '20px',
  height: '20px',
  svg: {
    width: '100%',
    height: '100%',
  },
};

const headerNameSx = {
  maxWidth: '160px',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
};

export const ColumnHeader = ({ colDef }: GridColumnHeaderParams) => {
  if (!colDef) {
    return null;
  }

  // @ts-expect-error: colDef.icon and colDef.color are not defined in GridColumnHeaderParams.colDef,
  // but they are added in createCreativeTabDataGridColumns
  const { headerName, icon, color } = colDef;

  if (color) {
    return (
      <VidMobChip
        sx={{ ...chipSx, backgroundColor: color }}
        label={
          <VidMobStack direction="row" alignItems="center">
            <VidMobTooltip
              title={headerName}
              placement="top"
              disableInteractive
            >
              <Typography
                variant="subtitle3"
                sx={{ ...headerNameSx, color: getTextColor(color) }}
              >
                {headerName}
              </Typography>
            </VidMobTooltip>
          </VidMobStack>
        }
      />
    );
  }

  return (
    <VidMobStack sx={containerSx}>
      {icon && <VidMobBox sx={iconSx}>{icon}</VidMobBox>}
      <VidMobTooltip title={headerName} placement="top" disableInteractive>
        <Typography variant="subtitle2" sx={headerNameSx}>
          {headerName}
        </Typography>
      </VidMobTooltip>
    </VidMobStack>
  );
};
