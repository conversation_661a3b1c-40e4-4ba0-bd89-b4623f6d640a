import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export const exportDiversityReportAsPDF = async ({
  reportName,
  name,
  date,
}: {
  reportName: string;
  name: string;
  date: string;
}) => {
  try {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.getWidth();

    const content = document.getElementById(
      'diversity-report-pdf',
    ) as HTMLElement;
    const canvas = await html2canvas(content, {
      // prevent the export button from being included in the PDF
      ignoreElements: (element) => element.id === 'export-pdf-button',
    });
    const contentHeight = (canvas.height * pageWidth) / canvas.width;
    const topMargin = 5;
    const leftMargin = 0;
    pdf.addImage(
      canvas,
      'PNG',
      leftMargin,
      topMargin,
      pageWidth,
      contentHeight,
    );

    // header
    const graphs = document.getElementsByClassName(
      'diversity-report-graph-pdf',
    );

    let yOffset = 73; // start after the header, metadata and widgets
    for (let i = 0; i < graphs.length; i++) {
      const graph = graphs[i];
      if (graph) {
        const canvasGraph = await html2canvas(graph as HTMLElement);
        const graphWidth = pageWidth - 13;
        const graphMargin = 6.5;
        const graphHeight =
          (canvasGraph.height * graphWidth) / canvasGraph.width;

        pdf.addImage(
          canvasGraph,
          'PNG',
          graphMargin,
          yOffset,
          graphWidth,
          graphHeight,
        );
        yOffset += graphHeight + 6; // Add space between elements
      }
    }

    // footer
    const footer = `Generated by ${name}, Vidmob at ${date}`;
    const pageHeight = pdf.internal.pageSize.getHeight();
    pdf.setFontSize(8);
    pdf.text(footer, 6.5, pageHeight - 5);
    pdf.save(`Diversity Report_${reportName}.pdf`);
  } catch (error) {
    console.error('Error exporting PDF', error);
  }
};
