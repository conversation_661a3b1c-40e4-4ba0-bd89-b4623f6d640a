import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import dayjs from 'dayjs';
import { ExportDiversityReportToPDFButton } from './ExportDiversityReportToPDFButton';
import DiversityReportPDFHeader from './DiversityReportPDFHeader';
import DiversityReportPDFMetadata from './DiversityReportPDFMetadata';
import DiversityReportPDFWidgets from './DiversityReportPDFWidgets';
import DiversityReportGraphs from '../DiversityReportGraphs';
import {
  VidMobStack,
  VidMobBox,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import RollUpReportsSlice from '../../../../redux/slices/rollUpReports.slice';
import { getCurrentUser } from '../../../../../redux/selectors/user.selectors';
import { exportDiversityReportAsPDF } from './exportDiversityReportAsPDF';
import { getIsRollUpReportPageLoading } from '../../../../redux/selectors/rollUpReports.selectors';
import { formatFiltersForDownloading } from '../../../ScoringFilters/utils/formatters';
import { ScoringReportType } from '../../../../types/rollUpReports.types';

const { exportPDF, resetReport } = RollUpReportsSlice.actions;

const pageStyles = {
  width: '1536px',
  height: '100%',
  overflowY: 'scroll',
  overflowX: 'scroll',
};

const DiversityReportPDF = () => {
  const dispatch = useDispatch();
  const intl = useIntl();
  const reportType = ScoringReportType.DIVERSITY;
  const isReportLoading = useSelector(getIsRollUpReportPageLoading(reportType));
  const { firstName, lastName } = useSelector(getCurrentUser);
  const name = `${firstName} ${lastName}`;

  const today = new Date();
  const localStorageValue =
    localStorage.getItem('diversityReportPdfData') || '[]';
  const { filters, reportName, filtersVersion } = JSON.parse(localStorageValue);
  const timezone = today.toString().match(/\(([^)]+)\)/);
  const formattedDate = dayjs(today)
    .locale('en')
    .format('MMMM D, YYYY, h:mm A')
    .concat(` ${timezone ? timezone[1] : ''}`);

  useEffect(() => {
    dispatch(
      exportPDF({
        filters: formatFiltersForDownloading(filters),
        reportType,
        filtersVersion,
      }),
    );

    return () => {
      dispatch(resetReport({ reportType }));
    };
  }, []);

  const DiversityReportExportPDFButton = () => (
    <ExportDiversityReportToPDFButton
      label={intl.formatMessage({
        id: 'ui.creativeScoring.rollUpReports.button.exportPDF',
      })}
      onClick={() =>
        exportDiversityReportAsPDF({ reportName, name, date: formattedDate })
      }
      disabled={isReportLoading}
    />
  );

  return (
    <VidMobStack sx={pageStyles}>
      <VidMobStack gap="40px" sx={{ p: '0 48px' }} id="diversity-report-pdf">
        <DiversityReportPDFHeader
          ExportPDFButton={DiversityReportExportPDFButton}
        />
        <DiversityReportPDFMetadata />
        <DiversityReportPDFWidgets />
      </VidMobStack>
      <VidMobBox sx={{ p: '15px 24px', width: '1532px' }}>
        <DiversityReportGraphs />
      </VidMobBox>
      <VidMobTypography
        className="diversity-report-pdf-footer"
        variant="body1"
        sx={{ m: '0 48px 48px 48px' }}
      >
        {intl.formatMessage(
          { id: 'ui.creativeScoring.rollUpReports.pdf.footer' },
          { name, date: formattedDate },
        )}
      </VidMobTypography>
    </VidMobStack>
  );
};

export default DiversityReportPDF;
