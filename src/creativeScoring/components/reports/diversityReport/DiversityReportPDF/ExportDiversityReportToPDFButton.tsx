import React from 'react';
import { DownloadFilledIcon } from '../../../../../assets/vidmob-mui-icons/general';
import { VidMobButton } from '../../../../../vidMobComponentWrappers';

type Props = {
  label: string;
  onClick: () => void;
  disabled?: boolean;
};

export const ExportDiversityReportToPDFButton = ({
  label,
  onClick,
  disabled = false,
}: Props) => (
  <VidMobButton
    id="export-pdf-button"
    sx={{ height: '40px' }}
    onClick={onClick}
    startIcon={<DownloadFilledIcon />}
    variant="text"
    disabled={disabled}
  >
    {label}
  </VidMobButton>
);
