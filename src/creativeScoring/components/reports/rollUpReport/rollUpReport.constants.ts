import { ElementType } from 'react';
import {
  ImpressionsIcon,
  PercentageIcon,
  MetricIcon,
  TeamIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import { generatePath } from 'react-router';
import BetaChip from '../../../../muiCustomComponents/BetaChip';
import siteMap from '../../../../routing/siteMap';
import { NORMS_CONFIGURATION_KEY } from '../../../../components/NormsConfiguration/NormsConfigurationConstants';
import {
  DIVERSITY_REPORT_HELP_CENTER_URL,
  IN_FLIGHT_REPORT_HELP_CENTER_URL,
  SCORECARD_AND_REPORTS_HELP_CENTER_URL,
} from '../../../constants/creativeScoring.constants';
import {
  DataLevelType,
  ScoringReportIntlType,
  ScoringReportType,
  ScoringReportUrlType,
} from '../../../types/rollUpReports.types';
import { InflightIcon } from '../../../../assets/vidmob-mui-icons/general/InflightIcon';

export const SCORING_REPORTS_TYPE_TO_URL: Record<
  ScoringReportType,
  ScoringReportUrlType
> = {
  [ScoringReportType.ADHERENCE]: ScoringReportUrlType.ADHERENCE,
  [ScoringReportType.IMPRESSION_ADHERENCE]:
    ScoringReportUrlType.IMPRESSION_ADHERENCE,
  [ScoringReportType.ADOPTION]: ScoringReportUrlType.ADOPTION,
  [ScoringReportType.IN_FLIGHT]: ScoringReportUrlType.IN_FLIGHT,
  [ScoringReportType.DIVERSITY]: ScoringReportUrlType.DIVERSITY,
};

export const SCORING_REPORTS_URL_TO_TYPE: Record<
  ScoringReportUrlType,
  ScoringReportType
> = {
  [ScoringReportUrlType.ADHERENCE]: ScoringReportType.ADHERENCE,
  [ScoringReportUrlType.IMPRESSION_ADHERENCE]:
    ScoringReportType.IMPRESSION_ADHERENCE,
  [ScoringReportUrlType.ADOPTION]: ScoringReportType.ADOPTION,
  [ScoringReportUrlType.IN_FLIGHT]: ScoringReportType.IN_FLIGHT,
  [ScoringReportUrlType.DIVERSITY]: ScoringReportType.DIVERSITY,
};

export const SCORING_REPORTS_TYPE_TO_INTL: Record<
  ScoringReportType,
  ScoringReportIntlType
> = {
  [ScoringReportType.ADHERENCE]: ScoringReportIntlType.ADHERENCE,
  [ScoringReportType.IMPRESSION_ADHERENCE]:
    ScoringReportIntlType.IMPRESSION_ADHERENCE,
  [ScoringReportType.ADOPTION]: ScoringReportIntlType.ADOPTION,
  [ScoringReportType.IN_FLIGHT]: ScoringReportIntlType.IN_FLIGHT,
  [ScoringReportType.DIVERSITY]: ScoringReportIntlType.DIVERSITY,
};

export const SCORING_REPORTS_HELP_CENTER_LINKS: Record<
  ScoringReportType,
  string
> = {
  [ScoringReportType.ADHERENCE]: SCORECARD_AND_REPORTS_HELP_CENTER_URL,
  [ScoringReportType.IMPRESSION_ADHERENCE]:
    SCORECARD_AND_REPORTS_HELP_CENTER_URL,
  [ScoringReportType.ADOPTION]: SCORECARD_AND_REPORTS_HELP_CENTER_URL,
  [ScoringReportType.IN_FLIGHT]: IN_FLIGHT_REPORT_HELP_CENTER_URL,
  [ScoringReportType.DIVERSITY]: DIVERSITY_REPORT_HELP_CENTER_URL,
};

const LEARN_ABOUT_ADHERENCE_REPORTS_KEY =
  'ui.compliance.rollUpReports.emptyState.url.button.label.adherence';
const LEARN_ABOUT_IN_FLIGHT_REPORTS_KEY =
  'ui.compliance.rollUpReports.emptyState.url.button.label.inFlight';
const LEARN_ABOUT_DIVERSITY_REPORT_KEY =
  'ui.compliance.rollUpReports.emptyState.url.button.label.diversity';

export const SCORING_REPORTS_LEARN_ABOUT_BUTTON_TEXT_KEY: Record<
  ScoringReportType,
  string
> = {
  [ScoringReportType.ADHERENCE]: LEARN_ABOUT_ADHERENCE_REPORTS_KEY,
  [ScoringReportType.IMPRESSION_ADHERENCE]: LEARN_ABOUT_ADHERENCE_REPORTS_KEY,
  [ScoringReportType.ADOPTION]: LEARN_ABOUT_ADHERENCE_REPORTS_KEY,
  [ScoringReportType.IN_FLIGHT]: LEARN_ABOUT_IN_FLIGHT_REPORTS_KEY,
  [ScoringReportType.DIVERSITY]: LEARN_ABOUT_DIVERSITY_REPORT_KEY,
};

export const SCORING_REPORT_ROOT_PATH = siteMap.creativeScoringRollUpReport;

export const SCORING_REPORTS_CREATE_OPTIONS: Partial<
  Record<
    // we can remove Partial once we have all the options not behind a feature flag
    ScoringReportType,
    {
      id: string;
      className: string;
      reportNameKey: string;
      reportDescriptionKey: string;
      icon: ElementType;
      chip?: ElementType;
      path: string;
    }
  >
> = {
  [ScoringReportType.IN_FLIGHT]: {
    id: SCORING_REPORTS_TYPE_TO_URL.IN_FLIGHT,
    className: 'rollup-reports-create-modal-in-flight-option',
    reportNameKey:
      'ui.compliance.rollUpReports.modal.createReport.reportName.inflight',
    reportDescriptionKey:
      'ui.compliance.rollUpReports.modal.createReport.reportDescription.inflight',
    icon: InflightIcon,
    path: generatePath(SCORING_REPORT_ROOT_PATH, {
      reportType: SCORING_REPORTS_TYPE_TO_URL.IN_FLIGHT,
    }),
  },
  [ScoringReportType.ADHERENCE]: {
    id: SCORING_REPORTS_TYPE_TO_URL.ADHERENCE,
    className: 'rollup-reports-create-modal-adherence-option',
    reportNameKey:
      'ui.compliance.rollUpReports.modal.createReport.reportName.adherence',
    reportDescriptionKey:
      'ui.compliance.rollUpReports.modal.createReport.reportDescription.adherence',
    icon: PercentageIcon,
    path: generatePath(SCORING_REPORT_ROOT_PATH, {
      reportType: SCORING_REPORTS_TYPE_TO_URL.ADHERENCE,
    }),
  },
  [ScoringReportType.IMPRESSION_ADHERENCE]: {
    id: SCORING_REPORTS_TYPE_TO_URL.IMPRESSION_ADHERENCE,
    className: 'rollup-reports-create-modal-impression-adherence-option',
    reportNameKey:
      'ui.compliance.rollUpReports.modal.createReport.reportName.impressionAdherence',
    reportDescriptionKey:
      'ui.compliance.rollUpReports.modal.createReport.reportDescription.impressionAdherence',
    icon: ImpressionsIcon,
    path: generatePath(SCORING_REPORT_ROOT_PATH, {
      reportType: SCORING_REPORTS_TYPE_TO_URL.IMPRESSION_ADHERENCE,
    }),
  },
  [ScoringReportType.ADOPTION]: {
    id: SCORING_REPORTS_TYPE_TO_URL.ADOPTION,
    className: 'rollup-reports-create-modal-adoption-option',
    reportNameKey:
      'ui.compliance.rollUpReports.modal.createReport.reportName.adoption',
    reportDescriptionKey:
      'ui.compliance.rollUpReports.modal.createReport.reportDescription.adoption',
    icon: MetricIcon,
    path: generatePath(SCORING_REPORT_ROOT_PATH, {
      reportType: SCORING_REPORTS_TYPE_TO_URL.ADOPTION,
    }),
  },
  [ScoringReportType.DIVERSITY]: {
    id: SCORING_REPORTS_TYPE_TO_URL.DIVERSITY,
    className: 'rollup-reports-create-modal-diversity-option',
    reportNameKey:
      'ui.compliance.rollUpReports.modal.createReport.reportName.diversity',
    reportDescriptionKey:
      'ui.compliance.rollUpReports.modal.createReport.reportDescription.diversity',
    icon: TeamIcon,
    chip: BetaChip,
    path: generatePath(SCORING_REPORT_ROOT_PATH, {
      reportType: SCORING_REPORTS_TYPE_TO_URL.DIVERSITY,
    }),
  },
};

export const SCORING_REPORTS_COLUMN_WIDTHS: Partial<
  Record<
    ScoringReportType,
    {
      aggregate: number;
      child: number;
    }
  >
> = {
  [ScoringReportType.ADOPTION]: {
    aggregate: 80,
    child: 80,
  },
  [ScoringReportType.ADHERENCE]: {
    aggregate: 80,
    child: 80,
  },
  [ScoringReportType.IMPRESSION_ADHERENCE]: {
    aggregate: 120,
    child: 80,
  },
};

export const BATCH_TYPE_CONSTANT = 'batchType';
export const FILTER_CONSTANTS = {
  WORKSPACE: 'workspaceId',
  REPORT_TYPE: 'batchType',
  DATE_RANGE: 'mediaCreateDate',
  MARKET: 'market',
  CHANNEL: 'channel',
  BRAND: 'brandId',
  CRITERIA_SET: 'criteriaSetId',
  CRITERIA_IS_OPTIONAL: 'criteriaIsOptional',
  NORMS: NORMS_CONFIGURATION_KEY,
  CREATIVE_TYPE: 'creativeType',
};

export const GROUPING_CONSTANTS = {
  ROWS: 'rows',
  COLUMNS: 'columns',
};

/*
Report Type Filter
 */
export const BATCH_TYPE = {
  ALL: '*',
  IN_FLIGHT: 'IN_FLIGHT',
  PRE_FLIGHT: 'PRE_FLIGHT',
};

export const BATCH_TYPE_FILTER_DISPLAYS = [
  {
    label: 'ui.creativeScoring.rollUpReports.reportTypeFilter.value.all',
    value: BATCH_TYPE.ALL,
  },
  {
    label: 'ui.creativeScoring.rollUpReports.reportTypeFilter.value.preflight',
    value: BATCH_TYPE.PRE_FLIGHT,
  },
  {
    label: 'ui.creativeScoring.rollUpReports.reportTypeFilter.value.inflight',
    value: BATCH_TYPE.IN_FLIGHT,
  },
];

/*
Optional Mandatory Criteria Filter
 */

export const CRITERIA_IS_OPTIONAL_TYPES = {
  ALL: '*',
  OPTIONAL: true,
  MANDATORY: false,
};

export const CRITERIA_IS_OPTIONAL_FILTER_DISPLAYS = [
  {
    label: 'ui.creativeScoring.rollUpReports.reportTypeFilter.value.all',
    value: CRITERIA_IS_OPTIONAL_TYPES.ALL,
  },
  {
    label:
      'ui.creativeScoring.reportDetails.filter.v2.criteria.criteriaIsOptional.optional.label',
    value: CRITERIA_IS_OPTIONAL_TYPES.OPTIONAL,
  },
  {
    label:
      'ui.creativeScoring.reportDetails.filter.v2.criteria.criteriaIsOptional.mandatory.label',
    value: CRITERIA_IS_OPTIONAL_TYPES.MANDATORY,
  },
];

/*
Breakdown Filter
 */
export const BREAKDOWN_TYPES = {
  ALL: 'all',
  MARKET: 'market',
  WORKSPACE: 'workspace',
  BRAND: 'brand',
  CRITERIA_GROUPS: 'criteriaGroup',
  CHANNEL: 'channel',
};

/*
Data Level Filter
*/

export const DATA_LEVELS = [
  {
    id: DataLevelType.AD,
    label: 'ui.creativeScoring.rollUpReports.dataFilter.value.ads',
    value: [DataLevelType.AD],
    popoverName: 'ui.reportFilters.header.dataLevel.ad.tooltip.name',
    description: 'ui.reportFilters.header.dataLevel.ad.tooltip.description',
  },
  {
    id: DataLevelType.ASSET,
    label: 'ui.creativeScoring.rollUpReports.dataFilter.value.assets',
    value: [DataLevelType.ASSET],
    popoverName: 'ui.reportFilters.header.dataLevel.asset.tooltip.name',
    description: 'ui.reportFilters.header.dataLevel.asset.tooltip.description',
  },
];

export const ROWS_GROUPING = [
  {
    id: 'brands',
    label: 'ui.creativeScoring.rollUpReports.breakdownFilter.value.brands',
    value: [BREAKDOWN_TYPES.BRAND],
  },
  {
    id: 'brandsAndMarkets',
    label:
      'ui.creativeScoring.rollUpReports.breakdownFilter.value.brandsAndMarkets',
    value: [BREAKDOWN_TYPES.BRAND, BREAKDOWN_TYPES.MARKET],
  },
  {
    id: 'markets',
    label: 'ui.creativeScoring.rollUpReports.breakdownFilter.value.markets',
    value: [BREAKDOWN_TYPES.MARKET],
  },
  {
    id: 'marketsAndBrands',
    label:
      'ui.creativeScoring.rollUpReports.breakdownFilter.value.marketsAndBrands',
    value: [BREAKDOWN_TYPES.MARKET, BREAKDOWN_TYPES.BRAND],
  },
  {
    id: 'marketsAndWorkspaces',
    label:
      'ui.creativeScoring.rollUpReports.breakdownFilter.value.marketsAndWorkspaces',
    value: [BREAKDOWN_TYPES.MARKET, BREAKDOWN_TYPES.WORKSPACE],
  },
  {
    id: 'workspaces',
    label: 'ui.creativeScoring.rollUpReports.breakdownFilter.value.workspaces',
    value: [BREAKDOWN_TYPES.WORKSPACE],
  },
  {
    id: 'workspacesAndMarkets',
    label:
      'ui.creativeScoring.rollUpReports.breakdownFilter.value.workspacesAndMarkets',
    value: [BREAKDOWN_TYPES.WORKSPACE, BREAKDOWN_TYPES.MARKET],
  },
];

/*
View By Filter
 */
export const VIEW_BY_OPTIONS = {
  MONTH: 'month',
  QUARTER: 'quarter',
};

// TODO: including 'batchType' in the value is a hack so that we can send eg. ['month', 'batchType'] but it does not really belong here
export const COLUMNS_GROUPING = [
  {
    label: 'ui.creativeScoring.rollUpReports.viewByFilter.value.monthly',
    value: [VIEW_BY_OPTIONS.MONTH, BATCH_TYPE_CONSTANT],
  },
  {
    label: 'ui.creativeScoring.rollUpReports.viewByFilter.value.quarterly',
    value: [VIEW_BY_OPTIONS.QUARTER, BATCH_TYPE_CONSTANT],
  },
];

/*
Landing Page Modals
 */
export const MODAL_TYPES = {
  RENAME: 'rename',
  DELETE: 'delete',
};

export const SCORING_REPORTS_MISSING_FILTERS_KEY =
  'ui.compliance.rollUpReports.emptyState.message';

/*
Breakdown by filter
 */
export const BREAKDOWN_BY_FILTER_OPTIONS = [
  {
    id: 'channel',
    label: 'ui.creativeScoring.rollUpReports.breakdownFilter.value.channel',
    value: [BREAKDOWN_TYPES.CHANNEL],
  },
  {
    id: 'criteriaGroup',
    label:
      'ui.creativeScoring.rollUpReports.breakdownFilter.value.criteriaGroup',
    value: [BREAKDOWN_TYPES.CRITERIA_GROUPS],
  },
];

// remove these when isCriteriaGroupsInReportsEnabled flag is removed
export const ADHERENCE_REPORT_CELL_WIDTHS = {
  NORMS_COLUMNS_WIDTH: 196,
  DEFAULT_COLUMNS_WIDTH: 150,
};

export const ROWS_COLUMNS_STATE = {
  COLLAPSED: 'COLLAPSED',
  EXPANDED: 'EXPANDED',
} as const;
