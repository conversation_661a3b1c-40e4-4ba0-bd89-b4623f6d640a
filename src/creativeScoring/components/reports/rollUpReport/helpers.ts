import {
  FilterValue,
  ListItem,
  Operator,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  ReportControlBarDropdownItem,
  ReportFilterKey,
} from '../../../../components/ReportFilters/types';
import {
  AdherenceReportMetadataFilter,
  V2ScoringReportFilters,
} from '../../ReportsDataGrid/adherenceReportDataGrid/types';
import {
  DATA_LEVELS,
  SCORING_REPORTS_TYPE_TO_URL,
  ROWS_GROUPING,
  SCORING_REPORTS_URL_TO_TYPE,
  BREAKDOWN_BY_FILTER_OPTIONS,
} from './rollUpReport.constants';
import {
  NON_ADOPTION_REPORT_DEFAULT_GROUP_BY_COLUMNS,
  REPORTS_THAT_SUPPORT_DATA_LEVEL,
  REPORTS_THAT_SUPPORT_BREAKDOWN_BY,
  REPORTS_THAT_SUPPORT_VIEW_BY,
  SCORING_REPORT_VIEW_BY_OPTIONS,
  ADHERENCE_DEFAULT_ROWS,
} from '../../ScoringFilters/constants';
import { generatePath } from 'react-router-dom';
import siteMap, { routeParams } from '../../../../routing/siteMap';
import history from '../../../../routing/history';
import { SCORING_REPORT_FILTERS } from '../../ScoringFilters/types';
import { getFeatureFlag } from '../../../../utils/featureFlagUtils';
import {
  DataLevelType,
  ScoringReportType,
  ScoringReportUrlType,
} from '../../../types/rollUpReports.types';
import BffRollUpReportsService from '../../../../apiServices/BffRollUpReportsService';
import vmErrorLog from '../../../../utils/vmErrorLog';

export const findGroupByOption = (groupByValue?: string[]) => {
  if (!groupByValue) {
    return ROWS_GROUPING[3];
  }

  return (
    ROWS_GROUPING.find(
      (option) =>
        option.value.length === groupByValue.length &&
        option.value.every((value, index) => groupByValue[index] === value),
    ) || ROWS_GROUPING[3]
  );
};

export const findDataLevelOption = (
  dataLevel?: ReportControlBarDropdownItem,
  dataLevelValueFromFilters?: string,
): ReportControlBarDropdownItem => {
  if (dataLevel) {
    return dataLevel;
  }
  if (dataLevelValueFromFilters) {
    return (
      DATA_LEVELS.find((option) => option.id === dataLevelValueFromFilters) ||
      DATA_LEVELS[1]
    );
  }
  return DATA_LEVELS[1];
};

export const findViewByOption = (viewByValue?: string[]) => {
  if (!viewByValue) {
    return SCORING_REPORT_VIEW_BY_OPTIONS[0];
  }

  return (
    SCORING_REPORT_VIEW_BY_OPTIONS.find((option) =>
      option.value.every((value, index) => viewByValue[index] === value),
    ) || SCORING_REPORT_VIEW_BY_OPTIONS[0]
  );
};

export const findBreakdownByOption = (breakdownByValue?: string[]) => {
  if (!breakdownByValue) {
    return BREAKDOWN_BY_FILTER_OPTIONS[0];
  }

  return (
    BREAKDOWN_BY_FILTER_OPTIONS.find((option) =>
      option.value.every((value, index) => breakdownByValue[index] === value),
    ) || BREAKDOWN_BY_FILTER_OPTIONS[0]
  );
};

export const createGroupByObject = (
  reportType: ScoringReportType,
  groupBy?: ReportControlBarDropdownItem,
  viewBy?: ReportControlBarDropdownItem,
  breakdownBy?: ReportControlBarDropdownItem,
) => {
  const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
    'isCriteriaGroupsInReportsEnabled',
  );

  const viewByValue = findViewByOption(viewBy?.value)?.value;
  const groupByValue = findGroupByOption(groupBy?.value)?.value || [];
  const breakdownByValue =
    findBreakdownByOption(breakdownBy?.value)?.value || [];

  if (
    isCriteriaGroupsInReportsEnabled &&
    REPORTS_THAT_SUPPORT_BREAKDOWN_BY.includes(reportType)
  ) {
    return {
      columns: groupByValue,
      rows: [...breakdownByValue, ...ADHERENCE_DEFAULT_ROWS],
    };
  }

  const columns = REPORTS_THAT_SUPPORT_VIEW_BY.includes(reportType)
    ? viewByValue
    : NON_ADOPTION_REPORT_DEFAULT_GROUP_BY_COLUMNS;

  return {
    columns,
    rows: groupByValue,
  };
};

export const getReportChannels = (filters: V2ScoringReportFilters[]) => {
  if (!filters) {
    return [];
  }
  const channels = filters.find(
    (filter: { key: string }) => filter.key === SCORING_REPORT_FILTERS.CHANNEL,
  )?.value;

  return channels ?? [];
};

export const getReportType = (
  pathname: string,
  reportType: ScoringReportUrlType,
): ScoringReportType => {
  let finalReportType: ScoringReportUrlType = reportType;

  if (!finalReportType) {
    finalReportType = pathname.split('/')[4] as ScoringReportUrlType;
  }

  return SCORING_REPORTS_URL_TO_TYPE[finalReportType];
};

export const getAreRollupReportFiltersValid = (
  filters: AdherenceReportMetadataFilter[],
): boolean => {
  const hasDateRange =
    filters.find((field) => field.fieldName === SCORING_REPORT_FILTERS.DATE)
      ?.value.length === 2;
  const hasWorkspaces =
    filters.find(
      (filter) => filter.fieldName === SCORING_REPORT_FILTERS.WORKSPACE,
    )?.value.length > 0;
  const scorecardType = filters.find(
    (filter) => filter.fieldName === SCORING_REPORT_FILTERS.BATCH_TYPE,
  );
  const hasValidScorecardType =
    scorecardType?.operator === Operator.EQUALS &&
    Boolean(scorecardType?.value);
  const hasChannels =
    filters.find(
      (filter) => filter.fieldName === SCORING_REPORT_FILTERS.CHANNEL,
    )?.value.length > 0;

  return hasDateRange && hasWorkspaces && hasValidScorecardType && hasChannels;
};

export const getEntityValue = (
  reportType: ScoringReportType,
  getFilterValue: (
    filterKey: ReportFilterKey,
    isFromLocalStorage?: boolean | undefined,
  ) => FilterValue | null,
  dataLevel?: ReportControlBarDropdownItem,
): DataLevelType | undefined => {
  if (REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType)) {
    return (
      (dataLevel?.id as DataLevelType) ||
      (
        getFilterValue(REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL)
          ?.value as ListItem
      )?.id ||
      DataLevelType.ASSET
    );
  }
  return undefined;
};

export const navigateToNewReport = (
  reportType: ScoringReportType,
  reportId: string,
) => {
  const path = generatePath(siteMap.creativeScoringRollUpReport, {
    reportType: SCORING_REPORTS_TYPE_TO_URL[reportType],
    reportId,
  });

  history.push(path);
};

export const deleteReport = async (
  reportId: string,
  userId: number,
  showToastAlert: (messageKey: string, type: 'info' | 'error') => void,
) => {
  try {
    await BffRollUpReportsService.deleteReport(reportId, userId);
    const navigateToReports = () => {
      const path = generatePath(siteMap.creativeIntelligenceCompliance, {
        tab: routeParams.tabs.creativeIntelligenceCompliance.rollupReports,
      });
      history.push(path);
    };

    navigateToReports();
  } catch (error) {
    vmErrorLog(error as Error, 'deleteReport');
    showToastAlert(
      'ui.creativeScoring.rollUpReports.notification.delete.error',
      'error',
    );
  }
};
