import { useCallback, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import BffService from '../../../../../apiServices/BffService';
import { REPORTS_THAT_SUPPORT_DATA_LEVEL } from '../../../ScoringFilters/constants';
import { NORMS_CONFIGURATION_KEY } from '../../../../../components/NormsConfiguration/NormsConfigurationConstants';
import { getFormattedAdvancedFilters } from '../../../ScoringFilters/utils/getters';
import { getCurrentPartner } from '../../../../../redux/selectors/partner.selectors';
import { convertReportFiltersV2ToReportFiltersV1 } from '../../../ScoringFilters/utils/convertFilterVersions';
import { createGroupByObject } from '../helpers';
import { useScoringReportContext } from '../../../__providers/ScoringReportContext';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { ScoringReportType } from '../../../../types/rollUpReports.types';
import {
  AdherenceReportMetadataFilter,
  ColumnIdentifiersType,
} from '../../../ReportsDataGrid/adherenceReportDataGrid/types';
import { SCORING_REPORT_FILTERS } from '../../../ScoringFilters/types';
import { Operator } from '../../../../../components/ReportFilters/types';

const { handleBffApiPost } = BffService;

export const useAdherenceMetadataDownloadV2 = () => {
  const { groupBy, reportType, breakdownBy, viewBy } =
    useScoringReportContext();
  const {
    filters: appliedScoringFilters,
    advancedFilters: appliedAnalyticsFilters,
    filterDefinitions,
  } = useReportFilters();

  const showToastAlert = useToastAlert();

  const shouldIncludeDataLevel =
    (reportType && REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType)) ||
    false;

  const formattedScoringFilters = convertReportFiltersV2ToReportFiltersV1({
    filters: appliedScoringFilters,
    filterDefinitions,
    isWithNorms: true,
    shouldIncludeDataLevel,
    shouldIncludeCreativeAdherence: true,
  });

  const formattedAdvancedFilters = getFormattedAdvancedFilters(
    appliedAnalyticsFilters,
    filterDefinitions,
  );

  const currentWorkspace = useSelector(getCurrentPartner);

  const [updatedFilters, setUpdatedFilters] = useState<
    AdherenceReportMetadataFilter[] | null
  >(null);
  const isDownloadingRef = useRef(false);

  const getUpdatedFilters = useCallback(
    (identifiers?: ColumnIdentifiersType) => {
      const currentFilters = formattedScoringFilters;
      const { MARKET, BRAND, WORKSPACE } = SCORING_REPORT_FILTERS;

      if (identifiers?.brandId) {
        const filterIndex = currentFilters.findIndex(
          (filter) => filter.fieldName === BRAND,
        );
        if (currentFilters[filterIndex]) {
          currentFilters[filterIndex].value = [identifiers.brandId];
        } else {
          currentFilters.push({
            fieldName: BRAND,
            value: [identifiers.brandId],
            operator: Operator.IN,
          });
        }
      }

      if (identifiers?.workspaceId) {
        const filterIndex = currentFilters.findIndex(
          (filter) => filter.fieldName === WORKSPACE,
        );

        if (currentFilters[filterIndex]) {
          currentFilters[filterIndex].value = [
            identifiers.workspaceId.toString(),
          ];
        } else {
          currentFilters.push({
            fieldName: WORKSPACE,
            value: [identifiers.workspaceId.toString()],
            operator: Operator.IN,
          });
        }
      }

      if (identifiers?.marketId) {
        const filterIndex = currentFilters.findIndex(
          (filter) => filter.fieldName === MARKET,
        );

        if (currentFilters[filterIndex]) {
          currentFilters[filterIndex].value = [identifiers.marketId];
        } else {
          currentFilters.push({
            fieldName: MARKET,
            value: [identifiers.marketId],
            operator: Operator.IN,
          });
        }
      }

      return [...currentFilters].filter(
        (item) => item.fieldName !== NORMS_CONFIGURATION_KEY,
      );
    },
    [formattedScoringFilters],
  );

  const download = useCallback(
    (identifiers?: ColumnIdentifiersType) => {
      if (isDownloadingRef.current) {
        return;
      }

      showToastAlert(
        'ui.creativeScoring.adherenceMetadata.download.inProgress',
        'info',
      );

      const formattedFilters = getUpdatedFilters(identifiers);

      setUpdatedFilters(formattedFilters);
      isDownloadingRef.current = true; // Mark download in progress
    },
    [formattedScoringFilters, getUpdatedFilters],
  );

  useQuery(
    [
      'adherenceMetadata',
      formattedScoringFilters,
      formattedAdvancedFilters,
      updatedFilters,
    ],
    async () => {
      const groupByObj = createGroupByObject(
        ScoringReportType.ADHERENCE,
        groupBy,
        viewBy,
        breakdownBy,
      );

      const response = await handleBffApiPost(
        `/v1/reports/adherence/workspace/${currentWorkspace.id}/metadata/csv`,
        {
          filters: updatedFilters,
          advancedFilters: formattedAdvancedFilters,
          groupBy: groupByObj,
        },
      );
      return response;
    },
    {
      enabled: !!updatedFilters && isDownloadingRef.current,
      retry: false,
      onSuccess: (data) => {
        const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute(
          'download',
          `report_metadata_${new Date().toISOString()}.csv`,
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        isDownloadingRef.current = false;
      },
      onError: () => {
        isDownloadingRef.current = false;
      },
    },
  );

  return { download };
};
