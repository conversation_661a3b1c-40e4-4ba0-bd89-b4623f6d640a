import { useCallback, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useScoringReportContext } from '../../../__providers/ScoringReportContext';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import { getFormattedAdvancedFilters } from '../../../ScoringFilters/utils/getters';
import { getCurrentPartner } from '../../../../../redux/selectors/partner.selectors';
import { useSelector } from 'react-redux';
import { getAvailableFilterOptionsV2 } from '../../../../redux/selectors/rollUpReports.selectors';
import BffService from '../../../../../apiServices/BffService';
import { AdherenceReportMetadataFilter } from '../../../ReportsDataGrid/adherenceReportDataGrid/types';
import { createGroupByObject } from '../helpers';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { NORMS_CONFIGURATION_KEY } from '../../../../../components/NormsConfiguration/NormsConfigurationConstants';
import { convertReportFiltersV2ToReportFiltersV1 } from '../../../ScoringFilters/utils/convertFilterVersions';
import { REPORTS_THAT_SUPPORT_DATA_LEVEL } from '../../../ScoringFilters/constants';
import { ScoringReportType } from '../../../../types/rollUpReports.types';

const { handleBffApiPost } = BffService;
const { ADHERENCE } = ScoringReportType;

interface DownloadParams {
  parent: string;
  leaf: string;
}

export const useAdherenceMetadataDownload = () => {
  const { groupBy, reportType } = useScoringReportContext();
  const {
    filters: appliedScoringFilters,
    advancedFilters: appliedAnalyticsFilters,
    filterDefinitions,
  } = useReportFilters();

  const showToastAlert = useToastAlert();

  const shouldIncludeDataLevel =
    (reportType && REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType)) ||
    false;

  const formattedScoringFilters = convertReportFiltersV2ToReportFiltersV1({
    filters: appliedScoringFilters,
    filterDefinitions,
    isWithNorms: true,
    shouldIncludeDataLevel,
    shouldIncludeCreativeAdherence: true,
  });

  const formattedAdvancedFilters = getFormattedAdvancedFilters(
    appliedAnalyticsFilters,
    filterDefinitions,
  );

  const currentPartner = useSelector(getCurrentPartner);
  const availableFilterOptions = useSelector(
    getAvailableFilterOptionsV2(reportType || ADHERENCE),
  );

  const [updatedFilters, setUpdatedFilters] = useState<
    AdherenceReportMetadataFilter[] | null
  >(null);
  const isDownloadingRef = useRef(false);

  const groupByKeyToFilterKeyMap: { [key: string]: string[] } = {
    marketsAndWorkspaces: ['market', 'workspaceId'],
    workspacesAndMarkets: ['workspaceId', 'market'],
    brandsAndMarkets: ['brandId', 'market'],
    marketsAndBrands: ['market', 'brandId'],
    markets: ['market'],
    workspaces: ['workspaceId'],
    brands: ['brandId'],
    criteriaSets: ['batchType'],
    status: ['channel'],
  };

  const getRelevantFilters = useCallback(
    (leaf: string, parent: string) => {
      const groupByKey = groupBy?.id as string;
      const filterKeys = groupByKeyToFilterKeyMap[groupByKey] || [];
      const relevantFilters: { [key: string]: [{ id: string }] } = {};

      filterKeys.forEach((key) => {
        const filterValues = availableFilterOptions[key];

        if (filterValues) {
          const matchedFilterValues = filterValues.filter(
            (filterValue: { id: string; name: string }) =>
              [leaf, parent].includes(filterValue.name) ||
              [leaf, parent].includes(filterValue.id),
          );

          if (matchedFilterValues.length > 0) {
            relevantFilters[key] = matchedFilterValues;
          }
        }
      });

      return relevantFilters;
    },
    [groupBy, availableFilterOptions],
  );

  const getUpdatedFilters = useCallback(
    (relevantFilters: { [key: string]: [{ id: string }] }) => {
      const newFilters = Object.keys(relevantFilters).map((key) => ({
        fieldName: key,
        operator: 'equals',
        value: relevantFilters[key].map((filterValue) => filterValue.id)[0],
      }));

      const updatedFilters = formattedScoringFilters.filter(
        (filter) => !(filter.fieldName in relevantFilters),
      );

      return [...updatedFilters, ...newFilters].filter(
        (item) => item.fieldName !== NORMS_CONFIGURATION_KEY,
      );
    },
    [formattedScoringFilters],
  );

  const download = useCallback(
    ({ leaf, parent }: DownloadParams) => {
      if (isDownloadingRef.current) {
        return;
      }

      showToastAlert(
        'ui.creativeScoring.adherenceMetadata.download.inProgress',
        'info',
      );

      const relevantFilters = getRelevantFilters(leaf, parent);
      const updatedFilters = getUpdatedFilters(relevantFilters);
      setUpdatedFilters(updatedFilters);
      isDownloadingRef.current = true; // Mark download in progress
    },
    [getRelevantFilters, getUpdatedFilters],
  );

  useQuery(
    [
      'adherenceMetadata',
      formattedScoringFilters,
      formattedAdvancedFilters,
      updatedFilters,
    ],
    async () => {
      const groupByObj = createGroupByObject(
        ScoringReportType.ADHERENCE,
        groupBy,
      );
      const response = await handleBffApiPost(
        `/v1/reports/adherence/workspace/${currentPartner.id}/metadata/csv`,
        {
          filters: updatedFilters,
          advancedFilters: formattedAdvancedFilters,
          groupBy: groupByObj,
        },
      );
      return response;
    },
    {
      enabled: !!updatedFilters && isDownloadingRef.current,
      retry: false,
      onSuccess: (data) => {
        const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute(
          'download',
          `report_metadata_${new Date().toISOString()}.csv`,
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        isDownloadingRef.current = false;
      },
      onError: () => {
        isDownloadingRef.current = false;
      },
    },
  );

  return { download };
};
