import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import { getCurrentPartner } from '../../../../../redux/selectors/partner.selectors';
import BffService from '../../../../../apiServices/BffService';
import { getFormattedAdvancedFilters } from '../../../ScoringFilters/utils/getters';
import { ReportControlBarDropdownItem } from '../../../../../components/ReportFilters/types';
import {
  createGroupByObject,
  getEntityValue,
  getAreRollupReportFiltersValid,
} from '../helpers';
import {
  RollUpReportMetadata,
  ScoringReportType,
} from '../../../../types/rollUpReports.types';
import { SCORING_REPORTS_TYPE_TO_URL } from '../rollUpReport.constants';
import { convertReportFiltersV2ToReportFiltersV1 } from '../../../ScoringFilters/utils/convertFilterVersions';
import {
  REPORTS_THAT_SUPPORT_ADHERENCE_FILTER,
  REPORTS_THAT_SUPPORT_DATA_LEVEL,
} from '../../../ScoringFilters/constants';

const { handleBffApiPost } = BffService;

interface Params {
  groupBy?: ReportControlBarDropdownItem;
  viewBy?: ReportControlBarDropdownItem;
  breakdownBy?: ReportControlBarDropdownItem;
  metadata?: RollUpReportMetadata;
  dataLevel?: ReportControlBarDropdownItem;
  reportType: ScoringReportType;
  enabled: boolean;
}

const useScoringReportData = ({
  groupBy,
  viewBy,
  breakdownBy,
  dataLevel,
  metadata,
  reportType,
  enabled,
}: Params) => {
  const { filters, advancedFilters, filterDefinitions, getFilterValue } =
    useReportFilters();
  const reportTypeForEndpoint = SCORING_REPORTS_TYPE_TO_URL[reportType];

  const currentPartner = useSelector(getCurrentPartner);

  const formattedAdvancedFilters = getFormattedAdvancedFilters(
    advancedFilters,
    filterDefinitions,
  );

  const shouldIncludeDataLevel =
    REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType);

  const shouldIncludeCreativeAdherence =
    REPORTS_THAT_SUPPORT_ADHERENCE_FILTER.includes(reportType);

  const reportFilters = convertReportFiltersV2ToReportFiltersV1({
    filters,
    filterDefinitions,
    shouldIncludeDataLevel,
    shouldIncludeCreativeAdherence,
  });

  const groupByValue =
    createGroupByObject(reportType, groupBy, viewBy, breakdownBy) ||
    metadata?.groupBy;

  const dataLevelValue = getEntityValue(reportType, getFilterValue, dataLevel);
  const areFiltersValid = getAreRollupReportFiltersValid(reportFilters);

  const query = useQuery({
    queryKey: [
      'scoringReportData',
      reportFilters,
      formattedAdvancedFilters,
      groupByValue,
      reportTypeForEndpoint,
      dataLevelValue,
    ],
    refetchOnWindowFocus: false,
    enabled: enabled && !!metadata && areFiltersValid,
    queryFn: async () => {
      const response = await handleBffApiPost(
        `/v2/reports/${reportTypeForEndpoint}/workspace/${currentPartner.id}`,
        {
          filters: reportFilters,
          analyticsFilters: formattedAdvancedFilters,
          groupBy: groupByValue,
          entityType: dataLevelValue,
        },
      );

      const reportData = {
        status: 'DATA_LOADED',
        data: response.data,
        metadata,
        impressionCountData: response.impressionCountData,
        rowTotalData: response.rowTotalData,
        flags: [],
        aggregationColumns: [],
      };

      return reportData;
    },
  });

  return {
    ...query,
    areFiltersValid,
  };
};

export default useScoringReportData;
