import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid-pro';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import { convertNormsToAPIModel } from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/adherenceReportUtils';
import { getNormsObjectives } from '../../../../redux/selectors/normsConfiguration.selectors';
import { getCurrentPartner } from '../../../../../redux/selectors/partner.selectors';
import BffService from '../../../../../apiServices/BffService';
import { getFormattedAdvancedFilters } from '../../../ScoringFilters/utils/getters';
import {
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  ReportControlBarDropdownItem,
  ReportToplineMetricType,
} from '../../../../../components/ReportFilters/types';
import {
  createGroupByObject,
  getAreRollupReportFiltersValid,
} from '../helpers';
import { AdherenceServerDataResponseModelV2 } from '../../../ReportsDataGrid/adherenceReportDataGrid/types';
import { getAdherenceReportLoadingCellsV2 } from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/getAdherenceReportLoadingCellsV2';
import {
  DataLevelType,
  RollUpReportMetadata,
  ScoringReportType,
} from '../../../../types/rollUpReports.types';
import { convertAdherenceServerToClientModelV2 } from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/convertAdherenceServerToClientModelV2';
import { convertReportFiltersV2ToReportFiltersV1 } from '../../../ScoringFilters/utils/convertFilterVersions';
import { NormsConfigurationSelectionStateType } from '../../../../../types/normsConfiguration.types';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import { REPORTS_THAT_SUPPORT_DATA_LEVEL } from '../../../ScoringFilters/constants';
import { getTotalCriteriaCountInRows } from '../../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/utils';
import { NestedRowType } from '../../../../../muiCustomComponents/NestedRowAndNestedGroupColumnTableV2/types';
import { convertAdherenceServerToToplineMetrics } from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/convertAdherenceServerToToplineMetrics';
import { useScoringReportContext } from '../../../__providers/ScoringReportContext';

const { handleBffApiPost } = BffService;
const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);
const { ADHERENCE, IMPRESSION_ADHERENCE } = ScoringReportType;

export interface AdherenceClientDataModelV3Type {
  columns: GridColDef[];
  rows: readonly NestedRowType[];
  columnGroupingModel: GridColumnGroupingModel;
  isDeepNestedColumnTable?: boolean;
  totalCriteriaCount?: number;
  hasServerResponse?: boolean;
  toplineMetricsData?: ReportToplineMetricType[];
}

interface Params {
  groupBy?: ReportControlBarDropdownItem;
  metadata?: RollUpReportMetadata;
  reportType?: ScoringReportType;
  dataLevel?: DataLevelType;
  reportId?: string;
}

const useAdherenceAndImpressionsAdherenceReportV2 = ({
  groupBy,
  metadata,
  reportType,
  dataLevel,
  reportId,
}: Params) => {
  const intl = useIntl();
  const { filters, advancedFilters, getFilterValue, filterDefinitions } =
    useReportFilters();
  const { breakdownBy, viewBy } = useScoringReportContext();

  const currentPartner = useSelector(getCurrentPartner);
  const isImpressionsAdherence = reportType === IMPRESSION_ADHERENCE;
  const isAdherence = reportType === ADHERENCE;

  const shouldIncludeDataLevel = Boolean(
    reportType && REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType),
  );

  const adherenceReportFilters = convertReportFiltersV2ToReportFiltersV1({
    filters,
    filterDefinitions,
    isWithNorms: true,
    shouldIncludeDataLevel,
    shouldIncludeCreativeAdherence: true,
  });

  const formattedAdvancedFilters = getFormattedAdvancedFilters(
    advancedFilters,
    filterDefinitions,
  );

  const normsConfigObjectiveOptions = useSelector(getNormsObjectives);

  const groupByValue =
    createGroupByObject(ADHERENCE, groupBy, viewBy, breakdownBy) ||
    metadata?.groupBy;

  const normsConfiguration = getFilterValue(
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.NORMS_CONFIGURATION,
    true,
  )?.value as NormsConfigurationSelectionStateType;

  const areFiltersValid = getAreRollupReportFiltersValid(
    adherenceReportFilters,
  );

  const tempInitialData = convertAdherenceServerToClientModelV2(
    getAdherenceReportLoadingCellsV2(),
    normsConfiguration!,
    groupBy?.value,
    intl,
  );

  const queryV3 = useQuery<AdherenceClientDataModelV3Type>({
    queryKey: [
      'adherenceReportWithNormsOrImpressionAdherenceReport',
      adherenceReportFilters,
      formattedAdvancedFilters,
      groupByValue,
      reportId,
    ],
    initialData: tempInitialData,
    refetchOnWindowFocus: false,
    enabled: !!metadata && areFiltersValid && isCriteriaGroupsInReportsEnabled,
    queryFn: async () => {
      let endpointReport = '';
      if (isAdherence) {
        endpointReport = 'adherence-norms';
      } else if (isImpressionsAdherence) {
        endpointReport = 'impression-adherence';
      }

      const response = await handleBffApiPost(
        `/v3/reports/${endpointReport}/workspace/${currentPartner.id}`,
        {
          filters: convertNormsToAPIModel(
            adherenceReportFilters,
            normsConfigObjectiveOptions,
          ),
          analyticsFilters: formattedAdvancedFilters,
          groupBy: groupByValue,
          entityType: dataLevel,
        },
      );

      const groupByValueForGridCopy = groupBy?.value
        ? groupBy?.value
        : groupByValue?.columns;

      const { rows, columns, columnGroupingModel, isDeepNestedColumnTable } =
        convertAdherenceServerToClientModelV2(
          response as AdherenceServerDataResponseModelV2,
          normsConfiguration!,
          groupByValueForGridCopy,
          intl,
        );

      const toplineMetricsData =
        convertAdherenceServerToToplineMetrics(response);

      return {
        columns,
        rows,
        columnGroupingModel,
        hasServerResponse: Boolean(response),
        totalCriteriaCount: getTotalCriteriaCountInRows(response.rows),
        isDeepNestedColumnTable,
        toplineMetricsData,
      };
    },
  });

  return { areFiltersValid, ...queryV3 };
};

export default useAdherenceAndImpressionsAdherenceReportV2;
