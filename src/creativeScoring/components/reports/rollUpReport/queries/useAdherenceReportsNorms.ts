import { useQuery } from '@tanstack/react-query';
import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid-pro';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import { convertNormsToAPIModel } from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/adherenceReportUtils';
import { useSelector } from 'react-redux';
import { getNormsObjectives } from '../../../../redux/selectors/normsConfiguration.selectors';
import { getCurrentPartner } from '../../../../../redux/selectors/partner.selectors';
import BffService from '../../../../../apiServices/BffService';
import { getFormattedAdvancedFilters } from '../../../ScoringFilters/utils/getters';
import {
  ListItem,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  REPORT_SCOPE_PARAMETERS_FILTERS,
  ReportControlBarDropdownItem,
  ReportToplineMetricType,
} from '../../../../../components/ReportFilters/types';
import {
  createGroupByObject,
  getAreRollupReportFiltersValid,
} from '../helpers';
import { AdherenceReportColumnType } from '../../../ReportsDataGrid/adherenceReportDataGrid/types';
import { convertAdherenceServerToClientModel } from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/convertAdherenceServerToClientModel';
import { adherenceReportGetLoader } from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/adherenceReportGetLoader';
import {
  getColumnGroups,
  getNestedRows,
} from '../../../../../muiCustomComponents/NestedRowAndGroupColumnTable/utils';
import {
  DataLevelType,
  RollUpReportMetadata,
  ScoringReportType,
} from '../../../../types/rollUpReports.types';
import { convertReportFiltersV2ToReportFiltersV1 } from '../../../ScoringFilters/utils/convertFilterVersions';
import { NormsConfigurationSelectionStateType } from '../../../../../types/normsConfiguration.types';
import { REPORTS_THAT_SUPPORT_DATA_LEVEL } from '../../../ScoringFilters/constants';
import { NestedRowType } from '../../../../../muiCustomComponents/NestedRowAndGroupColumnTable/types';
import { useScoringReportContext } from '../../../__providers/ScoringReportContext';

const { handleBffApiPost } = BffService;

export interface AdherenceClientDataModelV2Type {
  columns: GridColDef[];
  rows: readonly NestedRowType[];
  columnGroupingModel: GridColumnGroupingModel;
  isDeepNestedColumnTable?: boolean;
  totalCriteriaCount?: number;
  hasServerResponse?: boolean;
  toplineMetricsData?: ReportToplineMetricType[];
}

interface Params {
  groupBy?: ReportControlBarDropdownItem;
  metadata?: RollUpReportMetadata;
  reportType?: ScoringReportType;
  dataLevel?: DataLevelType;
  reportId?: string;
}

const useAdherenceReportNorms = ({
  groupBy,
  metadata,
  reportType,
  dataLevel,
  reportId,
}: Params) => {
  const { filters, advancedFilters, getFilterValue, filterDefinitions } =
    useReportFilters();
  const { breakdownBy, viewBy } = useScoringReportContext();

  const currentPartner = useSelector(getCurrentPartner);

  const shouldIncludeDataLevel = Boolean(
    reportType && REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(reportType),
  );

  const adherenceReportFilters = convertReportFiltersV2ToReportFiltersV1({
    filters,
    filterDefinitions,
    isWithNorms: true,
    shouldIncludeDataLevel,
    shouldIncludeCreativeAdherence: true,
  });

  const formattedAdvancedFilters = getFormattedAdvancedFilters(
    advancedFilters,
    filterDefinitions,
  );

  const normsConfigObjectiveOptions = useSelector(getNormsObjectives);

  const groupByValue =
    createGroupByObject(
      ScoringReportType.ADHERENCE,
      groupBy,
      viewBy,
      breakdownBy,
    ) || metadata?.groupBy;

  const columnTypeValue = getFilterValue(
    REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE,
    true,
  )?.value as ListItem;

  const columnType = columnTypeValue?.id as AdherenceReportColumnType;

  const normsConfiguration = getFilterValue(
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.NORMS_CONFIGURATION,
    true,
  )?.value as NormsConfigurationSelectionStateType;

  const areFiltersValid = getAreRollupReportFiltersValid(
    adherenceReportFilters,
  );

  const { tmpRows, tmpColumns, tmpColumnGroups } =
    convertAdherenceServerToClientModel(
      adherenceReportGetLoader(),
      normsConfiguration!,
      columnType,
    );

  const initialData = {
    columns: tmpColumns,
    rows: getNestedRows(tmpRows),
    columnGroupingModel: getColumnGroups(tmpColumnGroups),
  };

  const queryV2 = useQuery<AdherenceClientDataModelV2Type>({
    queryKey: [
      'adherenceReportWithNorms',
      adherenceReportFilters,
      formattedAdvancedFilters,
      groupByValue,
      columnType,
      reportId,
    ],
    initialData,
    refetchOnWindowFocus: false,
    enabled: !!metadata && areFiltersValid,
    queryFn: async () => {
      const response = await handleBffApiPost(
        `/v2/reports/adherence-norms/workspace/${currentPartner.id}`,
        {
          filters: convertNormsToAPIModel(
            adherenceReportFilters,
            normsConfigObjectiveOptions,
          ),
          analyticsFilters: formattedAdvancedFilters,
          groupBy: groupByValue,
          entityType: dataLevel,
        },
      );

      const { tmpRows, tmpColumns, tmpColumnGroups } =
        convertAdherenceServerToClientModel(
          response,
          normsConfiguration!,
          columnType,
        );

      return {
        columns: tmpColumns,
        rows: getNestedRows(tmpRows),
        columnGroupingModel: getColumnGroups(tmpColumnGroups),
        hasServerResponse: Boolean(response),
        isDeepNestedColumnTable: false,
        toplineMetricsData: undefined,
      };
    },
  });

  return { ...queryV2, areFiltersValid };
};

export default useAdherenceReportNorms;
