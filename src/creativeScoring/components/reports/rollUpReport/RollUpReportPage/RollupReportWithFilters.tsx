import './RollUpReportPage.scss';
import React, { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import classnames from 'classnames';
import _ from 'lodash';
import { useGridApiRef } from '@mui/x-data-grid-pro';
import { VidMobBox } from '../../../../../vidMobComponentWrappers';
import {
  RollUpReportBlankStates,
  RollUpReportLoadingState,
} from '../RollUpReportStates';
import ReportFiltersControlBar from '../../reportsSharedComponents/ReportFiltersControlBar';
import ReportsDataGrid from '../../../ReportsDataGrid/ReportsDataGrid';
import ReportUpdateNotifier from '../../reportsSharedComponents/ReportPageHeader/ReportNotification/ReportUpdateNotifier';
import DeepNestedReportWithMetrics from '../../../ReportsDataGrid/adherenceReportDataGrid/components/DeepNestedReportWithMetrics';
import ReportPageHeader from '../../reportsSharedComponents/ReportPageHeader/ReportPageHeader';
import ReportDataLimitationBanner from '../../reportsSharedComponents/ReportDataLimitationBanner/ReportDataLimitationBanner';
import DiversityReportGraphs from '../../diversityReport/DiversityReportGraphs';
import DiversityReportSummaryWidgets from '../../diversityReport/DiversityReportSummaryWidgets';
import { RollupReportEmptyState } from '../RollUpReportStates/RollupReportEmptyState';
import PreFlightOrInFlight from '../../preFlightOrInFlight';
import {
  REPORTS_THAT_SUPPORT_DATA_LEVEL,
  REPORTS_THAT_SUPPORT_BREAKDOWN_BY,
  REPORTS_THAT_SUPPORT_DEEP_NESTING,
  REPORTS_THAT_SUPPORT_NORMS,
} from '../../../ScoringFilters/constants';
import {
  DEFAULT_IN_FLIGHT_GROUP_BY,
  IN_FLIGHT_PRE_FLIGHT_DEFAULT_ASSETS_SORT,
} from '../../preFlightOrInFlight/constants';
import { BATCH_TYPE, DATA_LEVELS } from '../rollUpReport.constants';
import useScoringReportMetadata from '../queries/useScoringReportMetadata';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import { useScoringReportContext } from '../../../__providers/ScoringReportContext';
import useUpdateReportNotification from '../../reportsSharedComponents/ReportPageHeader/ReportNotification/useUpdateReportNotification';
import { useInFlightReportMetadata } from '../../preFlightOrInFlight/hooks/inFlight/useInFlightReportMetadata';
import useInFlightWorkspaceChannelAndGroups from '../../preFlightOrInFlight/hooks/inFlight/useInFlightWorkspaceChannelAndGroups';
import useScoringReportData from '../queries/useScoringReportData';
import { getAreInflightReportFiltersValid } from '../../../ScoringFilters/utils/getters';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import {
  findDataLevelOption,
  findGroupByOption,
  findBreakdownByOption,
  findViewByOption,
  getReportChannels,
  getReportType,
} from '../helpers';
import { SortParams } from '../../../../../types/sort.types';
import {
  ListItem,
  Operator,
  REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS,
  REPORT_SCOPE_PARAMETERS_FILTERS,
  ReportControlBarDropdownItem,
} from '../../../../../components/ReportFilters/types';
import {
  InFlightReportGroupBy,
  InFlightReportSpecificProps,
} from '../../preFlightOrInFlight/types';
import { CHANNELS } from '../../../../../types/channels.types';
import { NormsConfigurationSelectionStateType } from '../../../../../types/normsConfiguration.types';
import { V2ScoringReportFilters } from '../../../ReportsDataGrid/adherenceReportDataGrid/types';
import {
  DataLevelType,
  ScoringReportType,
  ScoringReportUrlType,
} from '../../../../types/rollUpReports.types';

const { IMPRESSION_ADHERENCE, DIVERSITY, IN_FLIGHT } = ScoringReportType;

const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

const RollupReportWithFilters = () => {
  const { reportType, reportId } = useParams<{
    reportType: ScoringReportUrlType;
    reportId: string;
  }>();
  const apiRef = useGridApiRef();
  const location = useLocation();
  const { pathname } = location;

  const [inFlightSortModel, setInFlightSortModel] = useState<SortParams>(
    IN_FLIGHT_PRE_FLIGHT_DEFAULT_ASSETS_SORT,
  );
  const [inFlightGroupBy, setInFlightGroupBy] = useState<InFlightReportGroupBy>(
    DEFAULT_IN_FLIGHT_GROUP_BY,
  );
  const [inFlightMediaNameSearchText, setInFlightMediaNameSearchText] =
    useState<string>('');
  const [isInFlightReportAllTimeSelected, setIsInFlightReportAllTimeSelected] =
    useState<boolean>(false);

  const [hasData, setHasData] = useState<boolean>(false);

  const [isReportUpdateNotificationOpen, setIsReportUpdateNotificationOpen] =
    useState<boolean>(false);

  const {
    setReportType,
    setReportMetadata,
    setReportData,
    setReportRowTotalData,
    breakdownBy,
    setBreakdownBy,
    viewBy,
    setViewBy,
    groupBy,
    setGroupBy,
    dataLevel,
    setDataLevel,
  } = useScoringReportContext();

  const {
    getFilterValue,
    saveFilterValueWithNoApply,
    setHasUnsavedAppliedFilters,
    filters,
    advancedFilters,
    stagingFilters,
  } = useReportFilters();

  const parsedReportType = getReportType(pathname, reportType);
  const doesReportSupportNorms =
    REPORTS_THAT_SUPPORT_NORMS.includes(parsedReportType);
  const isImpressionAdherenceReport = parsedReportType === IMPRESSION_ADHERENCE;
  const isDiversityReport = parsedReportType === DIVERSITY;
  const isInFlightReport = parsedReportType === IN_FLIGHT;

  const doesReportSupportDeepNesting =
    REPORTS_THAT_SUPPORT_DEEP_NESTING.includes(parsedReportType);
  const doesReportSupportDeepNestingAndFlagIsEnabled =
    isCriteriaGroupsInReportsEnabled && doesReportSupportDeepNesting;

  const normsConfiguration = (getFilterValue(
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.NORMS_CONFIGURATION,
  )?.value as NormsConfigurationSelectionStateType) || { selectedScope: 'd' };

  const dataLevelFilterValue = getFilterValue(
    REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL,
  );

  const isPreFlightSelected =
    (
      getFilterValue(REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE)
        ?.value as ListItem
    )?.id === BATCH_TYPE.PRE_FLIGHT ||
    (
      stagingFilters[REPORT_SCOPE_PARAMETERS_FILTERS.BATCH_TYPE]
        ?.value as ListItem
    )?.id === BATCH_TYPE.PRE_FLIGHT;

  useEffect(() => {
    if (isPreFlightSelected) {
      handleSetDataLevel(DATA_LEVELS[1]);
    }
  }, [isPreFlightSelected]);

  useEffect(() => {
    setGroupBy(undefined);
    setViewBy(undefined);
    setDataLevel(undefined);
    setBreakdownBy(undefined);
    setReportType(parsedReportType);
  }, [parsedReportType]);

  const {
    data: metadata,
    isLoading: isLoadingMetadata,
    isError: isErrorMetadata,
  } = useScoringReportMetadata({
    id: reportId,
    groupBy,
    viewBy,
    reportType: parsedReportType,
    breakdownBy,
  });

  const shouldLoadInDataLevelFilter =
    metadata?.filters &&
    REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(parsedReportType);

  const { workspaceId: inFlightWorkspaceId, channelId: inFlightChannelId } =
    useInFlightWorkspaceChannelAndGroups();

  useEffect(() => {
    if (shouldLoadInDataLevelFilter) {
      const dataLevelValue = findDataLevelOption(
        dataLevel,
        dataLevelFilterValue?.value as string,
      );
      setDataLevel(dataLevelValue);
    }

    if (metadata?.groupBy && !isInFlightReport) {
      let groupByValue;
      let viewByValue;
      let breakdownByValue;

      if (
        isCriteriaGroupsInReportsEnabled &&
        REPORTS_THAT_SUPPORT_BREAKDOWN_BY.includes(parsedReportType)
      ) {
        groupByValue = groupBy || findGroupByOption(metadata?.groupBy.columns);
        viewByValue = viewBy || findViewByOption(metadata?.groupBy.rows);
        breakdownByValue =
          breakdownBy || findBreakdownByOption(metadata?.groupBy.rows);
      } else {
        groupByValue = groupBy || findGroupByOption(metadata?.groupBy.rows);
        viewByValue = viewBy || findViewByOption(metadata?.groupBy.columns);
      }

      setGroupBy(groupByValue);
      setViewBy(viewByValue);
      setBreakdownBy(breakdownByValue);
    }
  }, [metadata]);

  const inFlightReportSpecificProps: InFlightReportSpecificProps = {
    sortModel: inFlightSortModel,
    groupBy: inFlightGroupBy,
    mediaNameSearchText: inFlightMediaNameSearchText,
    isAllTimeSelected: isInFlightReportAllTimeSelected,
    setIsAllTimeSelected: setIsInFlightReportAllTimeSelected,
  };

  const {
    data: inFlightReportMetadata,
    isLoading: isLoadingInFlightReportMetadata,
    isError: isErrorInFlightReportMetadata,
  } = useInFlightReportMetadata({
    enabled: isInFlightReport,
    setInFlightGroupBy,
    setInFlightSortModel,
    setInFlightMediaNameSearchText,
    setIsInFlightReportAllTimeSelected,
    reportId,
  });

  const { reportHasBeenUpdated } = useUpdateReportNotification(
    inFlightReportMetadata,
  );

  useEffect(() => {
    if (reportHasBeenUpdated) {
      setIsReportUpdateNotificationOpen(true);
    }
  }, [reportHasBeenUpdated]);

  const {
    data: reportV2,
    isLoading,
    isError,
    areFiltersValid: areReportV2FiltersValid,
  } = useScoringReportData({
    groupBy,
    viewBy,
    breakdownBy,
    dataLevel,
    metadata,
    reportType: parsedReportType,
    enabled:
      !doesReportSupportDeepNestingAndFlagIsEnabled &&
      !isInFlightReport &&
      // remove this last check once isCriteriaGroupsInReportsEnabled flag is turned on / old code removed
      !doesReportSupportNorms,
  });

  const areFiltersValid = isInFlightReport
    ? getAreInflightReportFiltersValid(filters)
    : areReportV2FiltersValid;

  useEffect(() => {
    if (reportV2) {
      // set v2 context report data
      setReportData(reportV2.data);
      setReportMetadata(reportV2.metadata);
      setReportRowTotalData(reportV2.rowTotalData);

      if (isDiversityReport) {
        const reportsWithData = (reportV2?.data || [])?.filter(
          (report: { dataPoints: number }) => report?.dataPoints > 0,
        );

        setHasData(Boolean(reportV2?.data && reportsWithData?.length > 0));
        return;
      }

      setHasData(reportV2?.data?.length);
    }
  }, [reportV2]);

  const reportChannelsForImpressionAdherenceReport = isImpressionAdherenceReport
    ? getReportChannels(
        metadata?.filters as unknown as V2ScoringReportFilters[],
      )
    : [];

  const renderImpressionAdherenceWarningBanner =
    isImpressionAdherenceReport &&
    (reportChannelsForImpressionAdherenceReport.some(
      (channel) => channel.id === CHANNELS.FACEBOOK,
    ) || // existing report with Meta selected
      reportChannelsForImpressionAdherenceReport.length === 0); // new report with no channels selected

  const handleSetNormsConfiguration = (
    normsConfiguration: NormsConfigurationSelectionStateType,
  ) => {
    saveFilterValueWithNoApply(
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.NORMS_CONFIGURATION,
      {
        value: normsConfiguration,
        operator: Operator.EQUALS,
      },
      true,
    );
  };

  const isLoadingOverall = isLoading || isLoadingMetadata;

  const onApplyNewDataLevel = (dataLevel: DataLevelType) => {
    if (
      _.isEqual(
        stagingFilters[REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL]
          ?.value,
        dataLevel,
      )
    ) {
      return;
    }
    saveFilterValueWithNoApply(
      REPORT_PILLAR_SPECIFIC_ADVANCED_FILTERS.DATA_LEVEL,
      {
        value: dataLevel,
        operator: Operator.EQUALS,
      },
      true,
    );
  };

  const handleSetGroupBy = (newGroupBy: ReportControlBarDropdownItem) => {
    setGroupBy(newGroupBy);
    setHasUnsavedAppliedFilters(true);
  };

  const handleSetBreakdownBy = (
    newBreakdownBy: ReportControlBarDropdownItem,
  ) => {
    setBreakdownBy(newBreakdownBy);
    setHasUnsavedAppliedFilters(true);
  };

  const handleSetViewBy = (newViewBy: ReportControlBarDropdownItem) => {
    setViewBy(newViewBy);
    setHasUnsavedAppliedFilters(true);
  };

  const handleSetDataLevel = (newDataLevel: ReportControlBarDropdownItem) => {
    setDataLevel(newDataLevel);
    onApplyNewDataLevel(newDataLevel.id as DataLevelType);
  };

  const handleSetInFlightGroupBy = (
    newInFlightGroupBy: InFlightReportGroupBy,
  ) => {
    setInFlightGroupBy(newInFlightGroupBy);
    setHasUnsavedAppliedFilters(true);
  };

  const handleSetInFlightSortModel = (newSortModel: SortParams) => {
    if (
      inFlightSortModel?.sortBy === newSortModel?.sortBy &&
      inFlightSortModel?.sortDirection === newSortModel?.sortDirection
    ) {
      return;
    }

    setInFlightSortModel(newSortModel);
    setHasUnsavedAppliedFilters(true);
  };

  const handleSetInFlightMediaSearchText = (newText: string) => {
    if (newText === inFlightMediaNameSearchText) {
      return;
    }

    setInFlightMediaNameSearchText(newText);
    setHasUnsavedAppliedFilters(true);
  };

  const tableWrapperClasses = classnames({
    'table-wrapper': true,
    loading: isLoadingOverall,
  });

  const renderWrapperContent = () => {
    if (!areFiltersValid) {
      return <RollupReportEmptyState reportType={parsedReportType} />;
    }

    if (isInFlightReport) {
      return (
        <PreFlightOrInFlight
          isInFlight
          isLoading={isLoadingInFlightReportMetadata}
          isError={isErrorInFlightReportMetadata}
          reportId={reportId}
          workspaceId={inFlightWorkspaceId || null}
          channelId={inFlightChannelId || null}
          areInFlightHooksEnabled={
            areFiltersValid && Boolean(inFlightReportMetadata)
          }
          filters={filters}
          advancedFilters={advancedFilters}
          sortModel={inFlightSortModel}
          setSortModel={handleSetInFlightSortModel}
          inFlightGroupBy={inFlightGroupBy}
          setInFlightGroupBy={handleSetInFlightGroupBy}
          inFlightMediaNameSearchText={inFlightMediaNameSearchText}
          setInFlightMediaNameSearchText={handleSetInFlightMediaSearchText}
          setHasData={setHasData}
          isAnyBannerOpen={isReportUpdateNotificationOpen}
        />
      );
    }

    if (
      doesReportSupportDeepNestingAndFlagIsEnabled ||
      doesReportSupportNorms
    ) {
      return (
        <DeepNestedReportWithMetrics
          apiRef={apiRef}
          metadata={metadata}
          isLoadingMetadata={isLoadingMetadata}
          isPreFlightSelected={isPreFlightSelected}
          dataLevel={findDataLevelOption(
            dataLevel,
            dataLevelFilterValue?.value as string,
          )}
          normsConfiguration={normsConfiguration}
          setHasData={setHasData}
          handleSetDataLevel={handleSetDataLevel}
          handleSetBreakdownBy={handleSetBreakdownBy}
          handleSetGroupBy={handleSetGroupBy}
          handleSetNormsConfiguration={handleSetNormsConfiguration}
          handleSetViewBy={handleSetViewBy}
          doesReportSupportsNorms={doesReportSupportNorms}
          reportType={parsedReportType}
          reportId={reportId}
        />
      );
    }

    if (isError) {
      return (
        <RollUpReportBlankStates
          isError
          data-testid="rollup-reports-error-state"
        />
      );
    }

    if (reportV2?.data?.length === 0) {
      return (
        <RollUpReportBlankStates data-testid="rollup-reports-blank-state" />
      );
    }

    if (isDiversityReport) {
      return (
        <div
          style={{
            height: '100%',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <DiversityReportSummaryWidgets
            reportData={reportV2?.data || []}
            isLoading={isLoadingOverall}
          />
          <DiversityReportGraphs
            isFromV2
            reportData={reportV2?.data || []}
            isLoading={isLoadingOverall}
          />
          <VidMobBox sx={{ height: '50px' }} />
        </div>
      );
    }

    if (isLoadingOverall) {
      return (
        <div className={tableWrapperClasses}>
          <RollUpReportLoadingState data-testid="rollup-reports-loading-state" />
        </div>
      );
    }

    if (reportV2) {
      return (
        <div className={tableWrapperClasses}>
          <ReportsDataGrid
            apiRef={apiRef}
            report={reportV2}
            metadata={metadata!}
            // we need to re-render the grid when groupBy, breakdownBy, or viewBy changes
            key={`${groupBy?.id}-${viewBy?.id}-${breakdownBy?.id}`}
          />
        </div>
      );
    }

    return null;
  };

  const renderControlBar = () => {
    if (isInFlightReport || doesReportSupportDeepNestingAndFlagIsEnabled)
      return null;

    return (
      <ReportFiltersControlBar
        reportType={parsedReportType}
        groupBy={groupBy}
        onGroupByChange={handleSetGroupBy}
        viewBy={viewBy}
        onViewByChange={handleSetViewBy}
        dataLevel={findDataLevelOption(
          dataLevel,
          dataLevelFilterValue?.value as string,
        )}
        onDataLevelChange={handleSetDataLevel}
        withNormsConfiguration={doesReportSupportNorms}
        normsConfiguration={normsConfiguration}
        onNormsConfigurationChange={handleSetNormsConfiguration}
        isLoading={
          doesReportSupportNorms ? isLoadingMetadata : isLoadingOverall
        }
        isDataLevelDisabled={isPreFlightSelected}
      />
    );
  };

  return (
    <VidMobBox className="rollup-report-page">
      <ReportPageHeader
        apiRef={apiRef}
        reportType={parsedReportType}
        metadata={isInFlightReport ? inFlightReportMetadata : metadata}
        noData={!hasData}
        areFiltersValid={areFiltersValid}
        isLoadingMetadata={
          isInFlightReport ? isLoadingInFlightReportMetadata : isLoadingMetadata
        }
        reportError={
          isInFlightReport ? isErrorInFlightReportMetadata : isErrorMetadata
        }
        inFlightReportSpecificProps={inFlightReportSpecificProps}
      />

      <ReportUpdateNotifier
        metadata={isInFlightReport ? inFlightReportMetadata : metadata}
        onClose={() => setIsReportUpdateNotificationOpen(false)}
      />

      <ReportDataLimitationBanner
        showBanner={renderImpressionAdherenceWarningBanner}
      />

      {renderControlBar()}

      <VidMobBox
        className="main-section"
        sx={{
          overflow: 'auto',
          height: '100%',
          paddingBottom: doesReportSupportDeepNesting ? '2px' : '250px',
          gap: doesReportSupportDeepNesting ? '0' : '15px',
        }}
      >
        {renderWrapperContent()}
      </VidMobBox>
    </VidMobBox>
  );
};

export default RollupReportWithFilters;
