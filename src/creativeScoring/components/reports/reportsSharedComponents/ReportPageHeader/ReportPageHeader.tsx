import React, {
  MutableRefObject,
  useEffect,
  useState,
  FC,
  createElement,
} from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { generatePath, Prompt } from 'react-router-dom';
import siteMap from '../../../../../routing/siteMap';
import { useIntl } from 'react-intl';
import WarnBeforeUnload from '../../../../../components/WarnBeforeUnload';
import PageHeaderV2 from '../../../../../components/PageHeaderV2';
import { Button } from '@mui/material';
import { VidMobDivider } from '../../../../../vidMobComponentWrappers';
import rollUpReportsSlice from '../../../../redux/slices/rollUpReports.slice';
import {
  getCurrentPartnerId,
  getOrganizationId,
} from '../../../../../redux/selectors/partner.selectors';
import {
  SCORING_REPORTS_TYPE_TO_INTL,
  SCORING_REPORTS_HELP_CENTER_LINKS,
  SCORING_REPORTS_CREATE_OPTIONS,
} from '../../rollUpReport/rollUpReport.constants';
import { getCurrentUserId } from '../../../../../redux/selectors/user.selectors';
import {
  ScoringReportType,
  RollUpReportMetadata,
} from '../../../../types/rollUpReports.types';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import {
  Operator,
  REPORT_SCOPE_PARAMETERS_FILTERS,
} from '../../../../../components/ReportFilters/types';
import { exportReportAsCsvClientSide } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsUtils/exportReportUtils';
import { GridApiPro } from '@mui/x-data-grid-pro/models/gridApiPro';
import {
  MODAL_CONFIRMATION_CLEAR_FILTERS_CONFIRM_KEY,
  MODAL_CONFIRMATION_CLEAR_FILTERS_MESSAGE_KEY,
  MODAL_CONFIRMATION_CLEAR_FILTERS_TITLE_KEY,
} from '../../../../../components/ReportFilters/constants';
import ModalConfirmWillClearFilters from '../../../../../components/ReportFilters/components/ModalConfirmWillClearFilters/ModalConfirmWillClearFilters';
import _ from 'lodash';
import siteMapRoutes from '../../../../../routing/siteMapRoutes';
import { getDefaultReportTitleWithDate } from '../../../../../utils/getDefaultReportTitle';
import { getReportTypeCopyKey } from '../../rollUpReport/rollUpReport.utils';
import { getFiltersToReset } from '../../../../../components/ReportFilters/utils/getters';
import { formatRollupReportFiltersForSaving } from '../../../ScoringFilters/utils/formatters';
import ScoringReportPageHeaderDetails from './ScoringReportPageHeaderDetails';
import {
  InFlightReportSpecificProps,
  SaveOrUpdateInFlightReportRequest,
} from '../../preFlightOrInFlight/types';
import {
  getInFlightReportCSV,
  getSaveInFlightReportRequestParams,
} from '../../preFlightOrInFlight/utils/filterUtils';
import { useToastAlert } from '../../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import ReportActionButtons from './ReportActionButtons';
import { createGroupByObject, deleteReport } from '../../rollUpReport/helpers';
import RollUpReportsDeleteReportModal from '../../../rollUpReportsLandingPage/Modals/RollUpReportsDeleteReportModal';
import { useScoringReportContext } from '../../../__providers/ScoringReportContext';
import { REPORTS_THAT_SUPPORT_DEEP_NESTING } from '../../../ScoringFilters/constants';
import { getFeatureFlag } from '../../../../../utils/featureFlagUtils';
import {
  getDataGridStateForReportSave,
  getIsRowAndColStateDifferent,
} from '../../../ReportsDataGrid/adherenceReportDataGrid/utils/adherenceReportUtils';
import { trackCustomEventGainsight } from '../../../../../utils/gainsight';

const { saveReport, duplicateReport } = rollUpReportsSlice.actions;
const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

interface ReportPageHeaderProps {
  reportType: ScoringReportType;
  metadata?: RollUpReportMetadata;
  noData: boolean;
  apiRef: MutableRefObject<GridApiPro>;
  areFiltersValid: boolean;
  isLoadingMetadata?: boolean;
  reportError?: boolean;
  inFlightReportSpecificProps?: InFlightReportSpecificProps;
}

const ReportPageHeader: FC<ReportPageHeaderProps> = ({
  reportType,
  metadata,
  noData,
  apiRef,
  areFiltersValid,
  isLoadingMetadata,
  reportError,
  inFlightReportSpecificProps,
}) => {
  const intl = useIntl();
  const showToastAlert = useToastAlert();
  const dispatch = useDispatch();
  const organizationId = useSelector(getOrganizationId);
  const partnerId = useSelector(getCurrentPartnerId);
  const currentUserId = useSelector(getCurrentUserId);
  const { rowColumnState, groupBy, viewBy, breakdownBy } =
    useScoringReportContext();

  const {
    saveFilterValueWithNoApply,
    filters,
    advancedFilters,
    hasUnsavedAppliedFilters,
    setHasUnsavedAppliedFilters,
    stagingFilters,
    stagingAdvancedFilters,
    filterDefinitions,
    supportedChannels,
    isFilterDrawerLoading,
  } = useReportFilters();

  const name = metadata?.name;
  const description = metadata?.description;
  const owner = metadata?.owner;
  const isUserReportOwner = owner ? owner?.id === currentUserId : true;
  const lastUpdated = metadata?.lastUpdated;
  const filtersVersion = metadata?.filtersVersion;
  const reportDateRange = filters[REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]
    ?.value as [string, string];
  const isDiversityReport = reportType === ScoringReportType.DIVERSITY;
  const isInFlightReport = reportType === ScoringReportType.IN_FLIGHT;
  const isDeepNestedReport =
    isCriteriaGroupsInReportsEnabled &&
    REPORTS_THAT_SUPPORT_DEEP_NESTING.includes(reportType);

  const reportTypeCopyKey = getReportTypeCopyKey(reportType);

  const [reportTitle, setReportTitle] = useState(
    name ||
      getDefaultReportTitleWithDate(
        intl.formatMessage({ id: reportTypeCopyKey }),
      ),
  );
  const [reportDescription, setReportDescription] = useState(description || '');
  const [isSaveButtonEnabled, setIsSaveButtonEnabled] = useState(false);
  const [isDeleteReportModalOpen, setIsDeleteReportModalOpen] = useState(false);
  const [isDisplayRouterPromptEnabled, setIsDisplayRouterPromptEnabled] =
    useState(true);
  const [isClearFiltersConfirmModalOpen, setIsClearFiltersConfirmModalOpen] =
    useState<boolean>(false);
  const [tmpDateRange, setTmpDateRange] = useState<
    [string, string] | undefined
  >();

  const [isCsvDownloading, setIsCsvDownloading] = useState(false);

  const getAreDateDependentAdvancedFiltersApplied = (
    date: [string, string],
  ): boolean => {
    const filtersToReset = getFiltersToReset({
      filterId: REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
      newValue: date,
      filterDefinitions,
      stagingFilters,
      stagingAdvancedFilters,
      supportedChannels,
    });

    return Boolean(filtersToReset);
  };

  const applyNewDateRange = (date: [string, string]) => {
    if (
      _.isEqual(
        stagingFilters[REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]?.value,
        date,
      )
    ) {
      return;
    }
    saveFilterValueWithNoApply(
      REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE,
      {
        value: date,
        operator: Operator.BETWEEN,
      },
      true,
    );
  };

  const handleCloseClearFiltersConfirmModal = () => {
    setIsClearFiltersConfirmModalOpen(false);
    setTmpDateRange(undefined);
  };

  const handleConfirmClearFiltersConfirmModal = () => {
    if (tmpDateRange) {
      applyNewDateRange(tmpDateRange);
      setTmpDateRange(undefined);
    }
    setIsClearFiltersConfirmModalOpen(false);
  };

  const onDateRangeChange = (date: [string, string]) => {
    if (
      _.isEqual(
        stagingFilters[REPORT_SCOPE_PARAMETERS_FILTERS.DATE_RANGE]?.value,
        date,
      )
    ) {
      return;
    }

    if (getAreDateDependentAdvancedFiltersApplied(date)) {
      setTmpDateRange(date);
      setIsClearFiltersConfirmModalOpen(true);
    } else {
      applyNewDateRange(date);
    }
  };

  const isEditable = isUserReportOwner && !isLoadingMetadata && !reportError;

  // set report title and description
  useEffect(() => {
    if (name) {
      setReportTitle(name);
    }
    if (description !== undefined) {
      setReportDescription(description);
    }
  }, [name, description]);

  // enable save button if unsaved report
  useEffect(() => {
    if (metadata && !metadata.id && areFiltersValid) {
      setIsSaveButtonEnabled(true);
    }
  }, [metadata]);

  // enable/disable save button if title or description has changed
  useEffect(() => {
    const isDirtyTitle =
      name !== undefined && Boolean(reportTitle) && reportTitle !== name;
    const isDirtyDescription =
      description !== undefined && reportDescription !== description;

    const shouldEnableSaveButton =
      Boolean(reportTitle) &&
      areFiltersValid &&
      (isDirtyTitle || isDirtyDescription);
    if (shouldEnableSaveButton) {
      setIsSaveButtonEnabled(true);
    }
  }, [reportTitle, reportDescription, areFiltersValid]);

  // enable/disable save button if filters have changed
  useEffect(() => {
    const shouldEnableSaveButton =
      Boolean(reportTitle) && areFiltersValid && hasUnsavedAppliedFilters;
    if (shouldEnableSaveButton) {
      setIsSaveButtonEnabled(true);
    }
  }, [hasUnsavedAppliedFilters, areFiltersValid]);

  // enable/disable save button if rowColumnState expanded/collapsed state has changed
  useEffect(() => {
    if (isDeepNestedReport) {
      const { isRowsStateDifferent, isColumnStateDifferent } =
        getIsRowAndColStateDifferent(rowColumnState, metadata?.dataGridState);

      if (isRowsStateDifferent || isColumnStateDifferent) {
        setIsSaveButtonEnabled(true);
      }
    }
  }, [rowColumnState]);

  const getRollupReportMetadataWithFilters = () => {
    const { id, ...filtersWithoutId } = filters;

    const metaDataWithFilters = {
      ...metadata,
      name: reportTitle,
      description: reportDescription,
      filters: formatRollupReportFiltersForSaving(
        Object.values(filtersWithoutId),
      ),
      analyticsFilters: advancedFilters,
    };

    if (isDeepNestedReport) {
      metaDataWithFilters.dataGridState =
        getDataGridStateForReportSave(rowColumnState);
      metaDataWithFilters.filtersVersion = 3;
      metaDataWithFilters.groupBy = createGroupByObject(
        reportType,
        groupBy,
        viewBy,
        breakdownBy,
      );
    }

    return metaDataWithFilters;
  };

  const getInFlightReportMetadataWithFilters =
    (): SaveOrUpdateInFlightReportRequest => {
      const nameToSave = reportTitle || '';
      const descriptionToSave = reportDescription || '';

      return {
        ...metadata,
        ...getSaveInFlightReportRequestParams({
          inFlightReportSpecificProps,
          workspaceId: partnerId,
          name: nameToSave,
          description: descriptionToSave,
          filters,
          filterDefinitions,
          advancedFilters,
        }),
      };
    };

  const trackEvent = (metadata: any) => {
    const filters = metadata.filters;
    if (!filters) {
      return;
    }

    try {
      const scope: string[] = [];
      if (Array.isArray(filters)) {
        const platformAccountId = filters.find(
          (item: any) => item.key === 'platformAccountId',
        );
        platformAccountId && scope.push('Ad Account');
        const brandId = filters.find((item: any) => item.key === 'brandId');
        brandId && scope.push('Brand');
        const market = filters?.find((item: any) => item.key === 'market');
        market && scope.push('Market');
      } else {
        filters.markets && scope.push('Market');
        filters.platformAccountIds && scope.push('Ad Account');
        filters.brandIds && scope.push('Brand');
      }

      trackCustomEventGainsight('Scope Selection', {
        reportType: metadata.reportType,
        scope: scope.length ? scope : ['No selections (default)'],
      });
    } catch (error) {
      console.error('Error tracking event:', error);
    }

    try {
      if (metadata?.groupBy) {
        let values = metadata?.groupBy?.columns;
        if (metadata.reportType.includes(ScoringReportType.ADHERENCE)) {
          values = metadata?.groupBy?.rows;
        }
        if (Array.isArray(values)) {
          trackCustomEventGainsight('Selected breakdown in report', {
            breakdownBy: values[0],
          });
        }
      }
    } catch (error) {
      console.error('Error tracking event:', error);
    }

    try {
      if (metadata.reportType === ScoringReportType.ADHERENCE) {
        let batchItem: any = {};
        if (Array.isArray(metadata.filters)) {
          batchItem = metadata.filters?.find(
            (item: any) => item.key === 'batchType',
          );
        }
        const batchType = batchItem?.value?.name;
        trackCustomEventGainsight('Adherence Report', {
          reportType: metadata.reportType,
          batchType: batchType,
        });
      }
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  };

  const onClickSave = () => {
    // When we click save, we want to disable the router prompt for a second
    // because on saving new report (no metadata.id), we do redirect to the new report id
    // and the router prompt will be triggered.
    if (!metadata?.id) {
      setIsDisplayRouterPromptEnabled(false);
      setTimeout(() => {
        setIsDisplayRouterPromptEnabled(true);
      }, 1000);
    }

    const payload = isInFlightReport
      ? getInFlightReportMetadataWithFilters()
      : getRollupReportMetadataWithFilters();

    trackEvent(payload);
    dispatch(saveReport({ metadata: payload, partnerId }));
    setIsSaveButtonEnabled(false);
    setHasUnsavedAppliedFilters(false);
  };

  const onClickDuplicate = () => {
    dispatch(
      duplicateReport({
        metadata: isInFlightReport
          ? getInFlightReportMetadataWithFilters()
          : getRollupReportMetadataWithFilters(),
        reportType: metadata?.reportType,
      }),
    );
  };

  const handleDeleteReport = async () => {
    if (!metadata?.id) {
      return;
    }

    await deleteReport(metadata.id, currentUserId, showToastAlert);
    setIsDeleteReportModalOpen(false);
  };

  const onClickDelete = () => {
    setIsDeleteReportModalOpen(true);
  };

  const onRollupReportClickDownloadCSV = () =>
    exportReportAsCsvClientSide({
      apiRef,
      titleText: reportTitle || name,
      reportTypeCopy: SCORING_REPORTS_TYPE_TO_INTL[reportType]
        ? intl.formatMessage({
            id: `ui.creativeScoring.rollUpReports.${SCORING_REPORTS_TYPE_TO_INTL[reportType]}.csvFileName`,
          })
        : '',
    });

  const onInFlightReportClickDownloadCSV = async () => {
    getInFlightReportCSV(
      organizationId,
      {
        filters,
        filterDefinitions,
        advancedFilters,
      },
      showToastAlert,
      setIsCsvDownloading,
      inFlightReportSpecificProps,
      reportTitle || name,
    );
  };

  const onClickExportAsCSV = isInFlightReport
    ? onInFlightReportClickDownloadCSV
    : onRollupReportClickDownloadCSV;

  const onClickExportAsPDF = () => {
    window.open(generatePath(siteMapRoutes.diversityReportPDF), '_blank');

    const LOCAL_STORAGE_KEY = 'diversityReportPdfData';
    localStorage.setItem(
      LOCAL_STORAGE_KEY,
      JSON.stringify({
        filters,
        reportName: reportTitle || name,
        lastUpdated,
        filtersVersion,
      }),
    );
  };

  const getReportDetails = () => {
    const reportDescriptionKey = `ui.compliance.rollUpReports.header.details.description.${SCORING_REPORTS_TYPE_TO_INTL[reportType]}`;

    return {
      reportType,
      reportTypeCopy: intl.formatMessage({ id: reportTypeCopyKey }),
      reportDescription: intl.formatMessage({ id: reportDescriptionKey }),
      reportIcon: createElement(
        // @ts-ignore
        SCORING_REPORTS_CREATE_OPTIONS[reportType]?.icon,
      ),
      learnMoreLink: SCORING_REPORTS_HELP_CENTER_LINKS[reportType],
      learnMoreTextOverride: intl.formatMessage({
        id: 'ui.creativeScoring.rollUpReports.header.details.learnMore',
        defaultMessage: 'How is this report calculated?',
      }),
      owner: metadata?.owner,
      lastUpdated: metadata?.lastUpdated || '',
      isBeta: reportType === ScoringReportType.DIVERSITY,
      reportDateRange,
      onDateRangeChange,
      isSavedDateRangeLoading: isFilterDrawerLoading,
      includeAllTimeOption: isInFlightReport,
      inFlightReportSpecificProps,
      customSx: { padding: '0' },
    };
  };

  return (
    <div className="page-header-wrapper" id="pdf-report-header">
      <Prompt
        when={
          isDisplayRouterPromptEnabled &&
          isSaveButtonEnabled &&
          isUserReportOwner
        }
        message={intl.formatMessage({
          id: 'ui.creativeScoring.rollUpReports.prompt.message',
        })}
      />
      {isSaveButtonEnabled && isUserReportOwner && <WarnBeforeUnload />}
      <PageHeaderV2
        breadcrumbs={[
          {
            label: intl.formatMessage({ id: 'sideNav.reports' }),
            url: generatePath(siteMap.creativeIntelligenceRollupReportsLanding),
          },
        ]}
        isEditable={isEditable}
        title={reportTitle || ''}
        subtitle={reportDescription || ''}
        onTitleChange={setReportTitle}
        onSubtitleChange={setReportDescription}
        customTitlePlaceholder={intl.formatMessage({
          id: 'ui.creativeScoring.rollUpReports.customTitlePlaceholder',
        })}
        additionalBoxStyles={{ p: '16px 0 4px' }}
        isLoading={isLoadingMetadata}
      >
        <ReportActionButtons
          isSavedReport={Boolean(metadata?.id)}
          loadingOrError={Boolean(isLoadingMetadata || reportError)}
          noData={noData}
          onClickDuplicate={onClickDuplicate}
          onClickDelete={onClickDelete}
          isLoadingDownloadCSV={isCsvDownloading}
          canEditOrDelete={isUserReportOwner}
          deletePermissionTooltipKey="ui.reports.button.delete.disabled.forbidden"
          defaultDeletePermissionTooltip="Only the owner of the report can delete it"
          {...(isDiversityReport
            ? {
                onClickExportAsPDF,
              }
            : {
                onClickExportAsCSV,
              })}
        />
        {metadata && isUserReportOwner && (
          <>
            <VidMobDivider
              orientation="vertical"
              variant="middle"
              flexItem
              sx={{ maxHeight: 20, margin: '8px 8px 0 8px' }}
            />
            <Button
              variant="contained"
              color="primary"
              disabled={!isSaveButtonEnabled}
              onClick={onClickSave}
              sx={{ height: '36px' }}
            >
              {metadata?.id
                ? intl.formatMessage({
                    id: 'ui.creativeScoring.rollUpReports.button.update',
                    defaultMessage: 'Update',
                  })
                : intl.formatMessage({
                    id: 'ui.creativeScoring.rollUpReports.button.save',
                    defaultMessage: 'Save',
                  })}
            </Button>
          </>
        )}
      </PageHeaderV2>
      <ScoringReportPageHeaderDetails {...getReportDetails()} />
      <ModalConfirmWillClearFilters
        title={intl.formatMessage({
          id: MODAL_CONFIRMATION_CLEAR_FILTERS_TITLE_KEY,
        })}
        bodyText={intl.formatMessage({
          id: MODAL_CONFIRMATION_CLEAR_FILTERS_MESSAGE_KEY,
        })}
        confirmButtonText={intl.formatMessage({
          id: MODAL_CONFIRMATION_CLEAR_FILTERS_CONFIRM_KEY,
        })}
        isOpen={isClearFiltersConfirmModalOpen}
        onClose={handleCloseClearFiltersConfirmModal}
        onConfirm={handleConfirmClearFiltersConfirmModal}
      />
      {isDeleteReportModalOpen && metadata && (
        <RollUpReportsDeleteReportModal
          isOpen={isDeleteReportModalOpen}
          onClose={() => setIsDeleteReportModalOpen(false)}
          onDelete={handleDeleteReport}
          report={{
            ...metadata,
            nameAndDescription: {
              name: reportTitle,
              description: reportDescription,
            },
          }}
        />
      )}
    </div>
  );
};

export default ReportPageHeader;
