import React, { useEffect } from 'react';
import { VidMobStack } from '../../../../../vidMobComponentWrappers';
import NormsConfigurationTriggerButton from './NormsConfigurationTriggerButton';
import { ReportControlBarDropdownItem } from '../../../../../components/ReportFilters/types';
import NormsConfiguration from '../../../../../components/NormsConfiguration/NormsConfiguration';
import {
  NORMS_CONFIGURATION_HELP_CENTER_LINK_SCORING,
  NORMS_CONFIGURATION_SUBTITLE_KEY_SCORING,
  NORMS_CONFIGURATION_SWITCH_LABEL_KEY_SCORING,
} from '../../../../../components/NormsConfiguration/NormsConfigurationConstants';
import {
  NormsConfigurationContextType,
  NormsConfigurationSelectionStateType,
} from '../../../../../types/normsConfiguration.types';
import { useIntl } from 'react-intl';
import {
  DATA_LEVELS,
  ROWS_GROUPING,
  BREAKDOWN_BY_FILTER_OPTIONS,
} from '../../rollUpReport/rollUpReport.constants';
import {
  REPORTS_THAT_SUPPORT_DATA_LEVEL,
  REPORTS_THAT_SUPPORT_GROUP_BY,
  REPORTS_THAT_SUPPORT_VIEW_BY,
  SCORING_REPORT_VIEW_BY_OPTIONS,
  REPORTS_THAT_SUPPORT_BREAKDOWN_BY,
} from '../../../ScoringFilters/constants';
import ControlBarDropdown from '../../../../../components/ReportFilters/components/ControlBarDropdown';
import {
  DATA_LEVEL_DISABLED_TOOLTIP_KEY,
  DATA_LEVEL_DROPDOWN_LABEL_KEY,
  VIEW_BY_DROPDOWN_LABEL_KEY,
} from '../../../../../components/ReportFilters/constants';
import { ReportFiltersButton } from '../../../../../components/ReportFilters/components/FilterButton';
import { DescriptionPopover } from '../../../../../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersDrawer/FilterValueInput/DescriptionPopover';
import { ScoringReportType } from '../../../../types/rollUpReports.types';
import useReportFilters from '../../../../../components/ReportFilters/hooks/useReportFilters';
import {
  ColumnTripleIcon,
  TableRowsFilledIcon,
} from '../../../../../assets/vidmob-mui-icons/general';
import { trackFeatureUsageGainsight } from '../../../../../utils/gainsight';

interface ReportFiltersControlBarProps {
  reportType: ScoringReportType;
  groupBy?: ReportControlBarDropdownItem;
  onGroupByChange: (groupBy: ReportControlBarDropdownItem) => void;
  viewBy?: ReportControlBarDropdownItem;
  onViewByChange: (viewBy: ReportControlBarDropdownItem) => void;
  dataLevel?: ReportControlBarDropdownItem;
  onDataLevelChange: (dataLevel: ReportControlBarDropdownItem) => void;
  withNormsConfiguration: boolean;
  normsConfiguration: NormsConfigurationSelectionStateType;
  onNormsConfigurationChange: (
    normsConfiguration: NormsConfigurationSelectionStateType,
  ) => void;
  isLoading: boolean;
  isDataLevelDisabled: boolean;
  disabledDataLevelTooltipKey?: string;
  onBreakdownByChange?: (breakdownBy: ReportControlBarDropdownItem) => void;
  breakdownBy?: ReportControlBarDropdownItem;
}

const ReportFiltersControlBarV2 = ({
  reportType,
  groupBy = ROWS_GROUPING[3],
  onGroupByChange,
  viewBy = SCORING_REPORT_VIEW_BY_OPTIONS[0],
  onViewByChange,
  dataLevel,
  onDataLevelChange,
  normsConfiguration,
  onNormsConfigurationChange,
  withNormsConfiguration,
  isLoading,
  isDataLevelDisabled,
  onBreakdownByChange = () => {},
  breakdownBy,
}: ReportFiltersControlBarProps) => {
  const intl = useIntl();
  const { setIsFilterDrawerOpen, isFilterDrawerOpen } = useReportFilters();

  useEffect(() => {
    trackFeatureUsageGainsight('Toggle Ad and Asset Views', {
      Context: `Toggle Ad and Asset Views for ${reportType}`,
    });
  }, []);

  return (
    <VidMobStack
      sx={{
        flexDirection: 'row',
        spacing: 2,
        alignItems: 'space-between',
        marginBottom: '12px',
      }}
    >
      <VidMobStack
        sx={{ flexGrow: 2, alignItems: 'start', flexDirection: 'row', gap: 4 }}
      >
        {REPORTS_THAT_SUPPORT_DATA_LEVEL.includes(
          reportType as ScoringReportType,
        ) && (
          <ControlBarDropdown
            value={dataLevel}
            options={DATA_LEVELS.map((dataLevel) => ({
              id: dataLevel.id,
              name: intl.formatMessage({
                id: dataLevel.label,
              }),
              value: dataLevel.value,
              popoverName: intl.formatMessage({
                id: dataLevel.popoverName,
              }),
              description: intl.formatMessage({
                id: dataLevel.description,
              }),
              learnMoreLink:
                'https://help.vidmob.com/en/articles/10517526-understanding-ad-level-vs-asset-level-views-in-scoring-reports',
            }))}
            onChange={onDataLevelChange}
            label={intl.formatMessage({
              id: DATA_LEVEL_DROPDOWN_LABEL_KEY,
              defaultMessage: 'Data level',
            })}
            isLoading={isLoading}
            hoverComponent={DescriptionPopover}
            disabled={isDataLevelDisabled}
            disabledTooltipKey={
              isDataLevelDisabled ? DATA_LEVEL_DISABLED_TOOLTIP_KEY : ''
            }
          />
        )}
        {REPORTS_THAT_SUPPORT_VIEW_BY.includes(
          reportType as ScoringReportType,
        ) && (
          <ControlBarDropdown
            value={viewBy}
            options={SCORING_REPORT_VIEW_BY_OPTIONS.map((viewByOption) => ({
              id: viewByOption.id,
              name: intl.formatMessage({
                id: viewByOption.label,
              }),
              value: viewByOption.value,
            }))}
            onChange={onViewByChange}
            label={intl.formatMessage({
              id: VIEW_BY_DROPDOWN_LABEL_KEY,
              defaultMessage: 'View by',
            })}
            isLoading={isLoading}
          />
        )}
        {withNormsConfiguration && (
          <NormsConfiguration
            TriggerButton={NormsConfigurationTriggerButton}
            subtitleIntlKey={NORMS_CONFIGURATION_SUBTITLE_KEY_SCORING}
            switchLabelIntlKey={NORMS_CONFIGURATION_SWITCH_LABEL_KEY_SCORING}
            helpCenterLink={NORMS_CONFIGURATION_HELP_CENTER_LINK_SCORING}
            context={NormsConfigurationContextType.SCORING}
            normsConfiguration={normsConfiguration}
            onNormsConfigurationChange={onNormsConfigurationChange}
          />
        )}
      </VidMobStack>
      {reportType !== ScoringReportType.IN_FLIGHT && (
        <VidMobStack
          flexDirection="row"
          justifyContent="flex-end"
          gap="10px"
          sx={{ flexGrow: 1, alignItems: 'end' }}
        >
          {REPORTS_THAT_SUPPORT_BREAKDOWN_BY.includes(
            reportType as ScoringReportType,
          ) && (
            <ControlBarDropdown
              value={breakdownBy}
              options={BREAKDOWN_BY_FILTER_OPTIONS.map((breakdownBy) => ({
                id: breakdownBy.id,
                name: intl.formatMessage({
                  id: breakdownBy.label,
                }),
                value: breakdownBy.value,
              }))}
              onChange={onBreakdownByChange}
              label={''}
              isLoading={isLoading}
              icon={
                <TableRowsFilledIcon
                  sx={{ mt: '3px', color: 'iconSecondary.main' }}
                  viewBox="0 0 20 20"
                />
              }
              selectedTextSx={{ mr: '6px' }}
            />
          )}
          {REPORTS_THAT_SUPPORT_GROUP_BY.includes(
            reportType as ScoringReportType,
          ) && (
            <ControlBarDropdown
              value={groupBy}
              options={ROWS_GROUPING.map((groupBy) => ({
                id: groupBy.id,
                name: intl.formatMessage({
                  id: groupBy.label,
                }),
                value: groupBy.value,
              }))}
              onChange={onGroupByChange}
              label={''}
              isLoading={isLoading}
              icon={
                <ColumnTripleIcon
                  sx={{ mt: '3px', color: 'iconSecondary.main' }}
                  viewBox="0 0 20 20"
                />
              }
              selectedTextSx={{ mr: '6px' }}
            />
          )}
          <ReportFiltersButton
            setIsFilterDrawerOpen={setIsFilterDrawerOpen}
            isFilterDrawerOpen={isFilterDrawerOpen}
            filterButtonSx={{
              height: '32px !important',
              width: '32px !important',
            }}
            filterIconSx={{
              height: '16px !important',
              width: '16px !important',
            }}
          />
        </VidMobStack>
      )}
    </VidMobStack>
  );
};

export default ReportFiltersControlBarV2;
