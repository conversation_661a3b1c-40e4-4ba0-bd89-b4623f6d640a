import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useIntl } from 'react-intl';
import Search from '../../../../muiCustomComponents/Search';
import rollupReportsManagementSlice from '../../../redux/slices/rollupReportsManagement.slice';
import { GLOBALS } from '../../../../constants';
import { VidMobBox } from '../../../../vidMobComponentWrappers';

const containerSx = {
  pb: '0px',
  maxWidth: '220px',
};

const searchSx = {
  height: '32px',
};

const ReportsLandingPageSearch: React.FC<{
  isLoading: boolean;
  isClearable: boolean;
  onChange: (value: string) => void;
  value: string;
}> = ({ onChange, value = '', isLoading, isClearable }) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const { setReportsFilters, setReportsStatus } =
    rollupReportsManagementSlice.actions;

  const handleSearch = useCallback((searchTerm: string) => {
    if (onChange) {
      onChange(searchTerm);
    }
    dispatch(setReportsFilters({ searchTerm }));
    dispatch(
      setReportsStatus({ status: GLOBALS.REDUX_LOADING_STATUS.PENDING }),
    ); /* changing the search query triggers loadReports (in CreativeScoringRollUpReportsLanding)
    which triggers saga and sets status to pending, but including here so that when search query is removed,
    it goes directly to loading state instead of showing generic blank state first
     */
  }, []);

  return (
    <VidMobBox sx={containerSx}>
      <Search
        canCollapseAndClear={isClearable}
        placeholder={intl.messages['ui.reports.search.placeholder'] as string}
        onSearchChange={handleSearch}
        debounceTime={300}
        isDisabled={isLoading}
        searchTerm={value}
        customStyles={searchSx}
      />
    </VidMobBox>
  );
};

export default ReportsLandingPageSearch;
