import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { generatePath } from 'react-router-dom';
import classnames from 'classnames';
import { Box } from '@mui/material';
import { DataGridPro } from '@mui/x-data-grid-pro';
import siteMap from '../../../../routing/siteMap';
import history from '../../../../routing/history';
import { RollUpReportsDeleteReportModal } from '../Modals';
import { RollUpReportsRenameReportModal } from '../Modals';
import { useRollUpReportsColumns } from './useRollUpReportsColumns';
import { useRollUpReportsRows } from './useRollUpReportsRows';
import { useRollUpReportsTableStyles } from './useRollUpReportsTableStyles';
import { SCORING_REPORTS_TYPE_TO_URL } from '../../reports/rollUpReport/rollUpReport.constants';
import { MODAL_TYPES } from '../../reports/rollUpReport/rollUpReport.constants';

import rollupReportsManagementSlice from '../../../redux/slices/rollupReportsManagement.slice';
import { getRollupReportsPagination } from '../../../redux/selectors/rollupReportsManagement.selectors';
import { trackCustomEventGainsight } from '../../../../utils/gainsight';

const { setReportsFilters } = rollupReportsManagementSlice.actions;

const RollUpReportsTable = ({
  isLoading,
  reports,
  paginationModel,
  columnVisibilityModel,
}) => {
  const dispatch = useDispatch();
  const [selectedReport, setSelectedReport] = useState(null);
  const [activeModal, setActiveModal] = useState(null);
  const { totalSize } = useSelector(getRollupReportsPagination);

  const onCloseModal = () => {
    setActiveModal(null);
    setSelectedReport(null);
  };

  const onOpenModal = (modalType, report) => {
    setActiveModal(modalType);
    setSelectedReport(report);
  };

  const trackEvent = (reportType, reportId) => {
    try {
      trackCustomEventGainsight('Open Save Reports', {
        pillar: 'Scoring',
        reportType: reportType,
        reportId: reportId,
      });
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  };

  const handleRowClick = (params) => {
    if (isLoading) {
      return;
    }

    const { reportType, id } = params.row;
    trackEvent(reportType, id);
    const reportTypeUrlParam = SCORING_REPORTS_TYPE_TO_URL[reportType];

    const path = generatePath(siteMap.creativeScoringRollUpReport, {
      reportType: reportTypeUrlParam,
      reportId: id,
    });

    history.push(path);
  };

  const handleServerSort = (sortData) => {
    const filters = {
      sortBy: sortData.length ? sortData[0].field : 'dateCreated',
      sortOrder: sortData.length ? sortData[0].sort : '',
    };
    dispatch(setReportsFilters(filters));
  };

  const columns = useRollUpReportsColumns({ onOpenModal, isLoading });
  const rows = useRollUpReportsRows({ columns, reports, isLoading });
  const { actionMenuStyles, dataGridStyle } =
    useRollUpReportsTableStyles(isLoading);

  const dataGridWrapperClasses = classnames({
    'rollup-reports-table-wrapper': true,
    loading: isLoading,
  });

  return (
    <Box
      sx={{
        position: 'absolute',
        width: '100%',
        height: 'calc(100% - 100px)',
        pb: '60px',
        pr: '45px',
      }}
      className={dataGridWrapperClasses}
    >
      <DataGridPro
        key={`scoringDataGrid-${JSON.stringify(columnVisibilityModel)}`}
        sx={dataGridStyle}
        columns={columns}
        rows={rows}
        onRowClick={handleRowClick}
        disableRowSelectionOnClick={isLoading}
        rowHeight={72}
        disableExtendRowFullWidth
        disableColumnMenu={true}
        slotProps={{
          basePopper: {
            sx: actionMenuStyles,
          },
        }}
        sortingMode="server"
        onSortModelChange={handleServerSort}
        initialState={{
          sorting: { sortingModel: [{ field: 'dateCreated', sort: 'desc' }] },
          pagination: { paginationModel },
          columns: { columnVisibilityModel },
        }}
        hideFooter
        hideFooterPagination
        pagination
        paginationMode="server"
        paginationModel={paginationModel}
        rowCount={totalSize}
      />
      <RollUpReportsDeleteReportModal
        isOpen={activeModal === MODAL_TYPES.DELETE}
        onClose={onCloseModal}
        report={selectedReport}
      />
      <RollUpReportsRenameReportModal
        isOpen={activeModal === MODAL_TYPES.RENAME}
        onClose={onCloseModal}
        report={selectedReport}
      />
    </Box>
  );
};

export default RollUpReportsTable;
