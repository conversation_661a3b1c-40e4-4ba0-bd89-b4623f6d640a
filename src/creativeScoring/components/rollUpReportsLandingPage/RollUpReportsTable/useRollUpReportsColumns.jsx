import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { getCurrentUser } from '../../../../redux/selectors/user.selectors';
import { GridActionsCellItem } from '@mui/x-data-grid-pro';
import rollupReportsManagementSlice from '../../../redux/slices/rollupReportsManagement.slice';
import LoadingCell from '../../shared/ScoringDataGrid/ScoringDataGridCells/LoadingCell';
import RollUpReportsTableNameColumnCell from './RollUpReportsTableCustomCells/RollUpReportsTableNameColumnCell';
import RollUpReportsTableReportTypeColumnCell from './RollUpReportsTableCustomCells/RollUpReportsTableReportTypeColumnCell';
import RollUpReportsTableOwnerColumnCell from './RollUpReportsTableCustomCells/RollUpReportsTableOwnerColumnCell';
import { MODAL_TYPES } from '../../reports/rollUpReport/rollUpReport.constants';
import { getIntl } from '../../../../utils/getIntl';
import RollUpReportsTableAdAccountsColumnCell from './RollUpReportsTableCustomCells/RollUpReportsAdAccountsColumnCell';
import ChannelColumnCell from '../../../../muiCustomComponents/ChannelColumnCell';
import { ScoringReportType } from '../../../types/rollUpReports.types';
import { useToastAlert } from '../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { downloadSavedInFlightReportCSV } from '../../reports/preFlightOrInFlight/utils/filterUtils';
import { getOrganizationId } from '../../../../redux/selectors/partner.selectors';
import { VidMobCircularProgress } from '../../../../vidMobComponentWrappers';
import { useQueryClient } from '@tanstack/react-query';
import { SCORING_SAVED_REPORTS_LIST_QUERY_KEY } from '../../__subpages/CreativeScoringRollUpReportsLanding/queries/constants';

const intl = getIntl();

const disabledActionSx = {
  pointerEvents: 'none',
};

const csvLoadingSx = {
  width: '16px !important',
  height: '16px !important',
};

export const ROLLUP_REPORTS_LANDING_COLUMNS = [
  {
    field: 'nameAndDescription',
    id: 'nameAndDescription',
    headerName: intl.messages['ui.creativeScoring.rollUpReports.column.name'],
    minWidth: 400,
    flex: 1,
    width: 300,
    renderCell: (params) => (
      <RollUpReportsTableNameColumnCell {...params.value} />
    ),
    hide: false,
    hideable: false,
  },
  {
    field: 'reportType',
    id: 'reportType',
    headerName:
      intl.messages['ui.creativeScoring.rollUpReports.column.reportType'],
    width: 163,
    renderCell: (params) => (
      <RollUpReportsTableReportTypeColumnCell reportType={params.value} />
    ),
    hide: false,
  },
  {
    field: 'ownerInfo',
    id: 'ownerInfo',
    headerName: intl.messages['ui.creativeScoring.rollUpReports.column.owner'],
    width: 200,
    hide: false,
    renderCell: (params) => (
      <RollUpReportsTableOwnerColumnCell {...params.value} />
    ),
  },
  {
    field: 'channels',
    id: 'channels',
    headerName:
      intl.messages['ui.creativeScoring.rollUpReports.column.channel'],
    width: 150,
    sortable: true,
    hide: false,
    renderCell: (params) => <ChannelColumnCell cellData={params} />,
  },
  {
    field: 'platformAdAccount',
    id: 'platformAdAccount',
    headerName:
      intl.messages[
        'ui.creativeScoring.rollUpReports.column.platformAdAccount'
      ],
    width: 150,
    sortable: true,
    hide: false,
    renderCell: (params) => (
      <RollUpReportsTableAdAccountsColumnCell adAccounts={params.value} />
    ),
  },
  {
    field: 'lastUpdated',
    id: 'lastUpdated',
    headerName:
      intl.messages['ui.creativeScoring.rollUpReports.column.lastUpdated'],
    width: 163,
    renderCell: undefined,
    hide: false,
  },
  {
    field: 'dateCreated',
    id: 'dateCreated',
    headerName:
      intl.messages['ui.creativeScoring.rollUpReports.column.dateCreated'],
    width: 163,
    minWidth: 163,
    flex: 1,
    renderCell: undefined,
    hide: false,
  },
];

export const useRollUpReportsColumns = ({ onOpenModal, isLoading }) => {
  const showToastAlert = useToastAlert();
  const dispatch = useDispatch();
  const intl = useIntl();
  const queryClient = useQueryClient();

  const { id: userId } = useSelector(getCurrentUser);
  const organizationId = useSelector(getOrganizationId);

  const [isCsvDownloading, setIsCsvDownloading] = useState(false);

  if (isLoading) {
    return ROLLUP_REPORTS_LANDING_COLUMNS.map((col) => ({
      ...col,
      renderCell: () => <LoadingCell />,
    }));
  }

  const handleDuplicateReport = (reportId, reportType) => {
    dispatch(
      rollupReportsManagementSlice.actions.duplicateReport({
        reportId,
        reportType,
        onDuplicate: () => {
          queryClient.refetchQueries([SCORING_SAVED_REPORTS_LIST_QUERY_KEY]);
        },
      }),
    );
  };

  const handleDownloadInFlightReport = (report) => {
    downloadSavedInFlightReportCSV({
      reportId: report.id,
      organizationId,
      reportTitle: report.nameAndDescription?.name || '',
      showToastAlert,
      setIsCsvDownloading,
    });
  };

  const renderActions = (cell) => {
    const { row: report } = cell;
    const { reportType, id, ownerInfo } = report;

    const isUserReportOwner = userId === ownerInfo.userId;

    return [
      isUserReportOwner && (
        <GridActionsCellItem
          key="rename"
          label={
            intl.messages['ui.creativeScoring.rollUpReports.action.rename']
          }
          onClick={() => onOpenModal(MODAL_TYPES.RENAME, report)}
          showInMenu
        />
      ),
      reportType === ScoringReportType.IN_FLIGHT && (
        <GridActionsCellItem
          key="download"
          sx={isCsvDownloading ? disabledActionSx : {}}
          label={
            isCsvDownloading ? (
              <VidMobCircularProgress sx={csvLoadingSx} />
            ) : (
              intl.messages['ui.creativeScoring.rollUpReports.action.download']
            )
          }
          onClick={() => handleDownloadInFlightReport(report)}
          showInMenu
        />
      ),
      <GridActionsCellItem
        key="duplicate"
        label={
          intl.messages['ui.creativeScoring.rollUpReports.action.duplicate']
        }
        onClick={() => handleDuplicateReport(id, reportType)}
        showInMenu
      />,
      isUserReportOwner && (
        <GridActionsCellItem
          key="delete"
          label={
            intl.messages['ui.creativeScoring.rollUpReports.action.delete']
          }
          sx={{ color: 'error.main' }}
          onClick={() => onOpenModal(MODAL_TYPES.DELETE, report)}
          showInMenu
        />
      ),
    ].filter(Boolean); // remove any falsy values from the array
  };

  return [
    ...ROLLUP_REPORTS_LANDING_COLUMNS,
    {
      field: 'actions',
      type: 'actions',
      width: 52,
      resizable: false,
      getActions: renderActions,
    },
  ];
};
