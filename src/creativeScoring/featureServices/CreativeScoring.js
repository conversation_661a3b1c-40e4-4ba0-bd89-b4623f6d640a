import { number, oneOf, string } from 'prop-types';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../constants/global.constants';
import { COMPLIANCE, PLATFORM } from '../../constants';
import { getCriteriaAsShape } from './transforms';
import { getIntl } from '../../utils/getIntl';
import { isNil } from '../../utils/typeCheckUtils';
import { generatePath } from 'react-router-dom';
import routes from '../../routing/siteMapRoutes';
import useIntl from '../../hooks/useIntl';
import CreativeScoringConstants from '../constants/creativeScoring.constants';

const {
  CONTENT_AUDIT_FILTER_STATES,
  ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
  CONTENT_AUDIT_CRITERIA,
  CUSTOM_CRITERIA_DEFINITION_SNIPPET,
  CRITERIA_TEMPLATE_DESCRIPTION,
  CUSTOM_CRITERIA_DISPLAY_NAMES,
  CRITERIA_DEFINITIONS,
  STANDARD_CRITERIA_PLATFORM_IDENTIFIER,
  VIDMOB_CRITERIA_LABEL,
  BATCH_TYPE,
  CONTENT_AUDIT_CRITERIA_DESCRIPTION,
  CUSTOM_CRITERIA_IDENTIFIERS,
} = COMPLIANCE;

const { IN_FLIGHT, PRE_FLIGHT } = BATCH_TYPE;
const { ALL_EVALUATED } = CONTENT_AUDIT_FILTER_STATES;
const { PLATFORM_IDENTIFIERS_TO_LOCALES } = PLATFORM;

const getRingTemplate = () => ({
  rings: {
    keyVisuals: { value: 0, position: 4, foregroundColor: '#1abbc1' },
    messaging: { value: 0, position: 3, foregroundColor: '#ffc841' },
    branding: { value: 0, position: 2, foregroundColor: '#ff6946' },
    duration: { value: 0, position: 1, foregroundColor: '#5564ff' },
  },
  overall: { value: 0 },
});

const MFS_KEYS = {
  BRAND_SCORE_PERCENTILE: 'MOBILE_FITNESS_SCORE:BRAND_SCORE_PERCENTILE',
  CLUTTER_SCORE_PERCENTILE: 'MOBILE_FITNESS_SCORE:CLUTTER_SCORE_PERCENTILE',
  DURATION_SCORE: 'MOBILE_FITNESS_SCORE:DURATION_SCORE',
  MESSAGING_SCORE_PERCENTILE: 'MOBILE_FITNESS_SCORE:MESSAGING_SCORE_PERCENTILE',
  MOBILE_FITNESS_SCORE: 'MOBILE_FITNESS_SCORE:MOBILE_FITNESS_SCORE',
};

const formatNumber = (number) => Math.round(number);

export const parseRingData = (scores) => {
  if (!scores) {
    return { hasData: false, hasNext: false };
  }

  if (Object.keys(scores).length) {
    const data = getRingTemplate();

    if (scores[MFS_KEYS.CLUTTER_SCORE_PERCENTILE]) {
      data.rings.keyVisuals = {
        ...data.rings.keyVisuals,
        value: formatNumber(scores[MFS_KEYS.CLUTTER_SCORE_PERCENTILE]),
      };
    }

    if (scores[MFS_KEYS.MESSAGING_SCORE_PERCENTILE]) {
      data.rings.messaging = {
        ...data.rings.messaging,
        value: formatNumber(scores[MFS_KEYS.MESSAGING_SCORE_PERCENTILE]),
      };
    }

    if (scores[MFS_KEYS.BRAND_SCORE_PERCENTILE]) {
      data.rings.branding = {
        ...data.rings.branding,
        value: formatNumber(scores[MFS_KEYS.BRAND_SCORE_PERCENTILE]),
      };
    }

    if (scores[MFS_KEYS.DURATION_SCORE]) {
      data.rings.duration = {
        ...data.rings.duration,
        value: formatNumber(scores[MFS_KEYS.DURATION_SCORE]),
      };
    }

    if (scores[MFS_KEYS.MOBILE_FITNESS_SCORE]) {
      data.overall = {
        ...data.overall,
        value: formatNumber(scores[MFS_KEYS.MOBILE_FITNESS_SCORE]),
      };
    }

    return { hasData: true, data, hasNext: false };
  }

  return { hasData: false, hasNext: false };
};

export const getFilteredCriteria = (
  criteriaSets,
  isBatchSelected,
  selectedBatchType,
  selectedBatchPlatform,
) =>
  isBatchSelected && selectedBatchType === IN_FLIGHT
    ? criteriaSets.map((criteriaSet) => ({
        ...criteriaSet,
        criteria: getFilteredCriteriaByPlatform(
          criteriaSet.criteria,
          selectedBatchPlatform,
          selectedBatchType,
        ),
      }))
    : criteriaSets;

export const getFilteredCriteriaByPlatform = (
  filteredCriteria,
  platforms,
  batchType,
) => {
  if (batchType === PRE_FLIGHT) {
    return filteredCriteria.filter(
      (criteria) =>
        !platforms || platforms?.includes(criteria?.platformIdentifier),
    );
  }

  return filteredCriteria.filter(
    (criteria) =>
      !platforms ||
      platforms?.includes(criteria?.platformIdentifier) ||
      criteria.platformIdentifier === 'ALL_PLATFORMS',
  );
};

export const getCustomTransformedCriteriaList = (criteriaSets) => {
  const criteriaList = [];
  criteriaSets?.forEach((criteriaSet) => {
    criteriaSet?.criteria.forEach((criteria) => {
      criteriaList.push(getCriteriaAsShape(criteria, criteria.id));
    });
  });

  return criteriaList;
};

export const getDefaultTransformedCriteriaList = (isJnJSegmentSelected) => {
  const criteriaList = [];
  Object.keys(CONTENT_AUDIT_CRITERIA).forEach((criteria) => {
    if (
      criteria !== CONTENT_AUDIT_CRITERIA.BRAND_APPEARS_ENOUGH ||
      (isJnJSegmentSelected && criteria !== CONTENT_AUDIT_CRITERIA.FORMAT)
    ) {
      criteriaList.push(getCriteriaAsShape(criteria, criteria));
    }
  });

  return criteriaList;
};

export const getTransformedCriteriaList = (
  criteriaSets,
  isJnJSegmentSelected,
  isBatchSelected,
  selectedBatchType,
  selectedBatchPlatform,
) => {
  if (isBatchSelected) {
    const filteredCriteria = getFilteredCriteria(
      criteriaSets,
      isBatchSelected,
      selectedBatchType,
      selectedBatchPlatform,
    );
    return getCustomTransformedCriteriaList(filteredCriteria);
  }

  return getDefaultTransformedCriteriaList(isJnJSegmentSelected);
};

export const isValidJSONString = (str) => {
  try {
    JSON.parse(str);
    return true;
    // eslint-disable-next-line no-unused-vars
  } catch (error) {
    return false;
  }
};

export const useCriteriaDescriptionAndDefinition = (criteria) => {
  const intl = useIntl();
  return getCriteriaDefinitionAndDescriptionWithIntl(intl, criteria);
};

export const getCriteriaDescriptionAndDefinition = (
  identifier,
  parameters,
  custom = {},
) => {
  const intl = getIntl();
  return getCriteriaDefinitionAndDescriptionWithIntl(intl, {
    identifier,
    parameters,
    custom,
  });
};

const getCriteriaDefinitionAndDescriptionWithIntl = (intl, criteria) => {
  const { identifier, parameters, custom = {} } = criteria;

  let definition = '';
  let description = '';
  const parsedCriterionParameters = isValidJSONString(parameters)
    ? JSON.parse(parameters)
    : parameters;
  const formattedParameters = { ...parsedCriterionParameters };
  const isCustom = CUSTOM_CRITERIA_IDENTIFIERS.includes(identifier);

  if (isCustom) {
    const { customValues, customName } = custom;
    const parsedCustomValues = isValidJSONString(customValues)
      ? JSON.parse(customValues)
      : customValues;
    const isCustomAudio =
      identifier === CreativeScoringConstants.CUSTOM_AUDIO_IDENTIFIER;
    const textOrAudioSnippet = isCustomAudio
      ? CUSTOM_CRITERIA_DEFINITION_SNIPPET.AUDIO
      : CUSTOM_CRITERIA_DEFINITION_SNIPPET.TEXT;
    const customDefinition = parsedCustomValues
      ? intl.formatMessage(
          {
            id: 'ui.creativeScoring.criteriaManagement.template.audio.custom.definition',
          },
          {
            textOrAudioSnippet: textOrAudioSnippet,
            parsedCustomText: parsedCustomValues
              .map((record) => record.value)
              .join(', '),
          },
        )
      : '';
    const customDescription =
      intl.messages[CUSTOM_CRITERIA_DISPLAY_NAMES[customName]] ||
      intl.messages[CRITERIA_TEMPLATE_DESCRIPTION[identifier]];

    return {
      definition: customDefinition,
      description: customDescription,
    };
  }

  Object.keys(formattedParameters).forEach((param) => {
    if (typeof formattedParameters[param] === 'object') {
      formattedParameters[param] = formattedParameters[param].join(', ');
    }
  });

  if (
    parsedCriterionParameters &&
    !isNil(CRITERIA_TEMPLATE_DESCRIPTION[identifier])
  ) {
    description = intl.formatMessage(
      { id: CRITERIA_TEMPLATE_DESCRIPTION[identifier] },
      { ...formattedParameters },
    );
  } else {
    description = intl.messages[CONTENT_AUDIT_CRITERIA_DESCRIPTION[identifier]];
  }

  if (!isNil(CRITERIA_DEFINITIONS[identifier])) {
    definition = intl.formatMessage(
      { id: CRITERIA_DEFINITIONS[identifier] },
      { ...formattedParameters },
    );
  }

  return { definition, description };
};

export const canUserUpdateReportDetails = (
  partnerPermissions,
  selectedBatch,
  currentUserId,
) =>
  partnerPermissions?.canUpdateComplianceReportDetails() ||
  selectedBatch?.personId === currentUserId;

export const canUserUpdateScorecardDetail = (
  partnerPermissions,
  personId,
  currentUserId,
) =>
  partnerPermissions?.canUpdateComplianceReportDetails() ||
  personId === currentUserId;

export const mapIdentifierToCriteriaLabel = (platformIdentifier) => {
  const intl = getIntl();
  if (
    [
      STANDARD_CRITERIA_PLATFORM_IDENTIFIER,
      ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
    ].includes(platformIdentifier)
  ) {
    return intl.messages[VIDMOB_CRITERIA_LABEL];
  }

  return intl.messages[PLATFORM_IDENTIFIERS_TO_LOCALES[platformIdentifier]];
};

export const getBrandScoreViewLink = (item) => {
  if (item && item.id) {
    return generatePath(routes.creativeScoringScoreCardDetails, {
      scorecardId: item.id,
    });
  }

  return '';
};

export const filterRowOnMediaScoreResult = (
  score,
  criteriaResultFilter,
  recognitionStatus,
) => {
  if (criteriaResultFilter === ALL_EVALUATED) {
    return true;
  }

  // MAFER-TODO SUBSTITUTE FOR CONSTANTS
  if (criteriaResultFilter === 'NOT_AVAILABLE') {
    if (['NOT_RUN', 'FAILED'].includes(recognitionStatus)) {
      return true;
    }

    if (!score) {
      return true;
    }
  }

  if (criteriaResultFilter === 'CRITERIA_MET' && score === 'PASS') {
    return true;
  }

  if (criteriaResultFilter === 'CRITERIA_NOT_MET' && score === 'FAIL') {
    return true;
  }

  return false;
};
