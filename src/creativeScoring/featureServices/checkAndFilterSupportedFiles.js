import toastAlertSlice from '../../redux/slices/toastAlert.slice';
import store from '../../redux/store';
import { getIntl } from '../../utils/getIntl';
import {
  DEFAULT_MAX_VIDEO_DURATION,
  validateFile,
} from './files/utils/validateFile';

const intl = getIntl();

export const checkAndFilterSupportedFiles = async (
  files,
  uploadedAssets,
  maxVideoLengthSec = DEFAULT_MAX_VIDEO_DURATION,
) => {
  const acceptedFiles = [];
  let unsupportedFilesCount = 0;

  if (uploadedAssets && uploadedAssets.length) {
    for (const existingFile of uploadedAssets) {
      for (const newFile of files) {
        // Don't allow files with same names. Backend does not accept it.
        if (newFile.name === existingFile.name) {
          store.dispatch(
            toastAlertSlice.actions.enqueueToastAlert({
              message: intl.formatMessage(
                { id: 'blank.compliance.duplicateFiles.error.message' },
                { fileName: newFile.name },
              ),
              type: 'error',
              autoClose: true,
            }),
          );

          return acceptedFiles;
        }
      }
    }
  }

  const processFile = async (file) => {
    if (!file.type || !file.size) {
      store.dispatch(
        toastAlertSlice.actions.enqueueToastAlert({
          message: intl.formatMessage(
            {
              id: 'files.validation.emptyOrCorrupted',
            },
            {
              fileName: file.name,
            },
          ),
          type: 'error',
          autoClose: true,
        }),
      );
      return;
    }

    const validationResult = await validateFile(file, maxVideoLengthSec);
    if (validationResult.isValid) {
      acceptedFiles.push(file);
    } else {
      unsupportedFilesCount++;
      validationResult.errors.forEach((errorKey) => {
        store.dispatch(
          toastAlertSlice.actions.enqueueToastAlert({
            message: intl.formatMessage(
              {
                id: errorKey,
              },
              {
                fileName: file.name,
              },
            ),
            type: 'error',
            autoClose: true,
          }),
        );
      });
    }
  };

  if (Array.isArray(files)) {
    for (const file of files) {
      await processFile(file);
    }
  } else {
    for (const key in files) {
      if (Object.hasOwn(files, key)) {
        await processFile(files[key]);
      }
    }
  }

  if (unsupportedFilesCount) {
    store.dispatch(
      toastAlertSlice.actions.enqueueToastAlert({
        message: intl.formatMessage(
          { id: 'blank.compliance.unsupportedFormats.error.message' },
          { number: unsupportedFilesCount },
        ),
        type: 'error',
        autoClose: true,
      }),
    );
  }

  return acceptedFiles;
};

export default checkAndFilterSupportedFiles;
