import { COMPLIANCE, PLATFORM } from '../../constants';
import vmErrorLog from '../../utils/vmErrorLog';
import genericVidMobIcon from '../../assets/icons/ic-vidmob-black.svg';
import { getCriteriaDescriptionAndDefinition } from './CreativeScoring';
import { getIntl } from '../../utils/getIntl';
import { isNil } from '../../utils/isObject';
import { PAGE_PLATFORMS } from '../../constants/platform.constants';
import { matches } from '../../utils/isMatchingObjects.js';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { getDraft } from '../components/scorecardsLanding/ScorecardsLandingTableDataGrid/helpers';

dayjs.extend(utc);

const {
  CONTENT_AUDIT_CRITERIA_DESCRIPTION,
  ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
  STANDARD_CRITERIA_PLATFORM_IDENTIFIER,
  REPORT_TYPE,
} = COMPLIANCE;
const { IN_FLIGHT } = REPORT_TYPE;
const { COMPLIANCE_PLATFORM_ACCOUNT_LIST, platformColorIconUrls } = PLATFORM;

const validPlatformsSet = new Set(
  COMPLIANCE_PLATFORM_ACCOUNT_LIST.map(({ IDENTIFIER }) =>
    IDENTIFIER.toLowerCase(),
  ),
);

export const getMinimalAdAccountData = (adAccount) => {
  const { platform, name, id } = adAccount;
  // Older CI-in-ACS code expects platform identifiers to be lowercase, vanilla ACS
  // expects uppercase, to match the identifiers in the db.
  const platformIdentifier = (platform || '').toUpperCase();
  return {
    name: name || id,
    id,
    platform,
    platformIdentifier,
    // This uniquely identifies this ad account, usable for keys in lists.
    uniqueIdentifier: `${platformIdentifier}/${id}`,
  };
};

export const getSanitizedAdAccountListData = (adAccountList) => {
  return adAccountList.reduce((sanitizedAdAccountList, adAccount) => {
    const newAdAccount = getMinimalAdAccountData(adAccount);

    // Remove anything invalid from the list, and log an error.
    if (!newAdAccount.id) {
      vmErrorLog(
        new Error('Missing required ad account field: id'),
        'featureServices compliance transforms',
        JSON.stringify(adAccount),
      );
      return sanitizedAdAccountList;
    }

    if (
      newAdAccount.platformIdentifier &&
      !validPlatformsSet.has(newAdAccount.platformIdentifier.toLowerCase())
    ) {
      vmErrorLog(
        new Error('Invalid platform supplied for ad account'),
        'featureServices compliance transforms',
        JSON.stringify(adAccount),
      );
      return sanitizedAdAccountList;
    }

    // NOTE: The name property is not mandatory and an account that doesn't have a name is valid.
    // Copy ID field into name field for accounts that don't have a name.
    // This makes sure drop-down filtering for such accounts still works.
    if (!newAdAccount.name) {
      newAdAccount.name = newAdAccount.id;
    }

    return [...sanitizedAdAccountList, newAdAccount];
  }, []);
};

export const isValidAdAccountForCreativeScoring = (adAccount) => {
  const newAdAccount = getMinimalAdAccountData(adAccount);

  // to remove malformed accounts, those without valid platforms and those WITH pages.

  if (!newAdAccount.id) {
    return false;
  }

  if (
    newAdAccount.platformIdentifier &&
    !validPlatformsSet.has(newAdAccount.platformIdentifier.toLowerCase())
  ) {
    return false;
  }

  if (PAGE_PLATFORMS.includes(adAccount.platform?.toUpperCase())) {
    return false;
  }

  return true;
};

export const handleBooleanParameters = (parameters, criterion) => {
  if (COMPLIANCE.BOOLEAN_TEMPLATES.includes(criterion.identifier)) {
    const intl = getIntl();
    const booleanParams = {};
    Object.keys(parameters).forEach((key) => {
      booleanParams[key] =
        intl.messages[COMPLIANCE.BOOLEAN_VALUE_STRINGS[parameters[key]]];
    });
    return booleanParams;
  }

  return parameters;
};

export const getCriteriaAsShape = (criteria, index, criteriaTemplates) => {
  const isString = typeof criteria === 'string';
  const isCustom = getIsCustom(criteria);

  const { definition, description } = getCriteriaDescriptionAndDefinition(
    criteria.identifier,
    handleBooleanParameters(criteria.parameters, criteria),
    criteria?.custom,
  );

  const intl = getIntl();
  const identifier = getIdentifier(criteria, isString);
  const platformIdentifier = getPlatformIdentifier(criteria, isString);
  const { isBestPractice } = getIsPlatformBestPractice(
    criteria,
    criteriaTemplates,
  );

  return {
    ...getCriteriaObject(criteria, isString),
    platformIdentifier,
    identifier,
    definition,
    description: getDescription(intl, criteria, isString, description),
    iconUrl: getIconUrl(criteria, isString),
    id: index,
    isBestPractice,
    isCustom,
  };
};

/**
 * @param criteria
 */
function getIsCustom(criteria) {
  // Currently a string is coming back from the API on isCustom instead of a boolean.
  // The below implementation will work both now and after we fix that.
  return criteria.isCustom === 'true' || criteria.isCustom === true;
}

/**
 * @param criteria
 * @param isString
 */
function getIdentifier(criteria, isString) {
  return isString ? criteria : criteria.identifier;
}

/**
 * @param criteria
 * @param isString
 */
function getPlatformIdentifier(criteria, isString) {
  if (
    isString ||
    [
      ALL_PLATFORMS_TEMPLATE_IDENTIFIER,
      STANDARD_CRITERIA_PLATFORM_IDENTIFIER,
    ].includes(criteria.platformIdentifier)
  ) {
    return ALL_PLATFORMS_TEMPLATE_IDENTIFIER;
  }

  return criteria.platformIdentifier;
}

/**
 * @param criteria
 * @param isString
 */
function getCriteriaObject(criteria, isString) {
  if (isString) {
    return {};
  }

  return { ...criteria };
}

/**
 * @param intl
 * @param criteria
 * @param isString
 * @param description
 */
function getDescription(intl, criteria, isString, description) {
  return isString
    ? intl.messages[CONTENT_AUDIT_CRITERIA_DESCRIPTION[criteria]]
    : description;
}

/**
 * @param criteria
 * @param isString
 */
export function getIconUrl(criteria, isString) {
  if (
    criteria.platformIdentifier === ALL_PLATFORMS_TEMPLATE_IDENTIFIER ||
    isString
  ) {
    return genericVidMobIcon;
  }

  return platformColorIconUrls[criteria.platformIdentifier];
}

const getIsPlatformBestPractice = (criteria, criteriaTemplates) => {
  let matchingCriteriaTemplate;
  if (criteriaTemplates && criteriaTemplates?.length) {
    matchingCriteriaTemplate = criteriaTemplates.find(
      (template) => template.id === criteria.criteriaTemplateId,
    );
  }

  if (
    typeof matchingCriteriaTemplate !== 'undefined' &&
    confirmIsBestPractice(criteria, matchingCriteriaTemplate)
  ) {
    return { isBestPractice: matchingCriteriaTemplate.isBestPractice };
  }

  return { isBestPractice: 0 };
};

const confirmIsBestPractice = (criteria, template) => {
  const isConfirmedBestPractice =
    criteria.identifier === template.identifier &&
    criteria.platformIdentifier === template.platformIdentifier &&
    template.isBestPractice === 1 &&
    matches(criteria.parameters, template.defaultInstanceParameters);
  return isConfirmedBestPractice;
};

export const getBatchName = (platformAdAccountDetails, partnerBatches) => {
  const currentDate = new Date(Date.now());
  const currentMonth = String(currentDate.getMonth() + 1) + '/';
  const currentDay = String(currentDate.getDate()) + '/';
  const currentYear = String(currentDate.getFullYear());
  const partialBatchName = String(
    currentMonth + currentDay + currentYear + ' - ' + platformAdAccountDetails,
  );
  const filteredBatchNames = partnerBatches.filter((batch) =>
    batch.name.startsWith(partialBatchName),
  );
  const batchNumber = filteredBatchNames.length + 1;
  return filteredBatchNames.length > 0
    ? partialBatchName + ' - #' + batchNumber
    : partialBatchName;
};

const getFormattedScorecardsDate = (date) => {
  return dayjs.utc(date).format('MMM DD, YYYY');
};

const getDateObject = (date) => {
  return dayjs.utc(date);
};

const formatPersonName = (person) => {
  if (isNil(person)) {
    return null;
  }

  if (isNil(person?.alias)) {
    return `${person?.firstName} ${person?.lastName}`;
  }

  return person?.alias;
};

/**
 *
 * @param {object} person - Object containing person data
 * @returns {null|number} Returns the person id if it is a number, otherwise null
 */
const formatPersonId = (person) => {
  if (isNil(person)) {
    return null;
  }

  return isNaN(Number(person.id)) ? null : Number(person.id);
};

const getFormattedScorecardPlatforms = (scorecard) => {
  const platforms = scorecard?.platforms;

  return platforms?.length
    ? platforms
        .map((platformId) =>
          COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.find(
            (platformObject) => platformObject.id === platformId,
          ),
        )
        .map((platformObject) => ({
          name: platformObject.i18nName,
          id: platformObject.id,
          iconUrl: platformObject.iconUrl,
        }))
    : COMPLIANCE.PARTNER_CRITERIA_PLATFORMS.map((platformObject) => ({
        name: platformObject.i18nName,
        id: platformObject.id,
        iconUrl: platformObject.iconUrl,
      }));
};

const getFormattedScorecardMarkets = (markets) => {
  return markets?.length
    ? markets.map((market) => ({
        name: market?.name,
        id: market.isoCode,
      }))
    : [];
};

const getAdAccountName = (scorecard) => {
  const { platformAdAccount, batchType } = scorecard;

  return batchType === IN_FLIGHT
    ? platformAdAccount?.platformAccountName
    : null;
};

const getAdAccountId = (scorecard) => {
  const { platformAdAccount, batchType } = scorecard;

  return batchType === IN_FLIGHT ? platformAdAccount?.platformAccountId : null;
};

const getScorecardName = (scorecard) => {
  const { name, platformAdAccount, isInternal } = scorecard;
  if (isInternal) {
    // ie. is an ad account
    return platformAdAccount?.platformAccountName;
  }

  return name;
};

export const getFormattedScorecard = (scorecard) => {
  const intl = getIntl();

  const {
    id,
    criteriaSetId,
    dateCreated,
    lastUpdated,
    person,
    markets,
    brands,
    score,
    status,
    batchType,
    isOutdated,
    isInternal,
    startDate,
    endDate,
    countMediaWithLifecycle,
    totalMediaCount,
    projectName,
    projectId,
    iterationMediaId,
    outputVideoName,
    iteration,
    iterationVersion,
    objectives,
    deviceIdentifier,
    criteriaGroupIds,
  } = scorecard;

  return {
    id,
    name: getScorecardName(scorecard),
    dateCreated: dateCreated ? getFormattedScorecardsDate(dateCreated) : null,
    lastModified: lastUpdated ? getFormattedScorecardsDate(lastUpdated) : null,
    createdBy: formatPersonName(person),
    personId: formatPersonId(person),
    platforms: getFormattedScorecardPlatforms(scorecard),
    markets: getFormattedScorecardMarkets(markets),
    brands,
    score,
    status,
    batchType,
    isOutdated,
    adAccount: getAdAccountName(scorecard),
    adAccountId: getAdAccountId(scorecard),
    isInternal,
    startDate: startDate ? getDateObject(startDate) : null,
    endDate: endDate ? getDateObject(endDate) : null,
    creativesOptimized: countMediaWithLifecycle || null,
    totalMediaCount,
    deviceIdentifier: deviceIdentifier || null,
    criteriaSetId,
    projectName,
    projectId,
    iterationMediaId,
    outputVideoName,
    draft: getDraft(intl, projectName, iteration, iterationVersion),
    objectives,
    criteriaGroupIds,
  };
};
