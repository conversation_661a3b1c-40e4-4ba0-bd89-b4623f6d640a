import { ErrorWithMessage } from '../../../components/__subpages/CreativeScoringCriteriaCreate/types';
import { createMediaElement } from './createMediaElement';
import isMimeTypeMatchingExtension from './isMimeTypeMatchingExtension';
import { isSupportedFileType } from './isSupportedFileType';
import { isValidDimensions } from './isValidDimensions';
import { isValidDuration } from './isValidDuration';
import { isValidFileName } from './isValidFileName';

export const DEFAULT_MAX_VIDEO_DURATION = 300; // 5 minutes in seconds
export const EXTENDED_MAX_VIDEO_DURATION = 900; // 15 minutes in seconds

const MIN_IMAGE_DIMENSIONS = { width: 80, height: 80 };
const MAX_FILE_SIZES = { image: 5000000, video: 524000000 };

export const validateFile = async (
  file: File,
  maxVideoDurationSec = DEFAULT_MAX_VIDEO_DURATION,
) => {
  const errors: string[] = [];

  const isImage = file.type.startsWith('image/');
  const maxSize = isImage ? MAX_FILE_SIZES.image : MAX_FILE_SIZES.video;

  const isFileNameInvalid = !isValidFileName(file.name);
  if (isFileNameInvalid) {
    errors.push('files.validation.invalidFileName');
  }

  const isTooLarge = file.size > maxSize;
  if (isTooLarge) {
    errors.push('files.validation.fileTooLarge');
  }

  const isUnsupportedType = !isSupportedFileType(file);
  if (isUnsupportedType) {
    errors.push('files.validation.unsupportedFormat');
  }

  const isExtensionMismatch = !isMimeTypeMatchingExtension(file);
  if (isExtensionMismatch) {
    errors.push('files.validation.extensionMismatch');
  }

  if (isUnsupportedType || isExtensionMismatch) {
    return {
      isValid: false,
      errors,
    };
  }

  try {
    const media = await createMediaElement(file);

    if (file.type.startsWith('image/')) {
      if (
        !isValidDimensions(
          media,
          MIN_IMAGE_DIMENSIONS.width,
          MIN_IMAGE_DIMENSIONS.height,
        )
      ) {
        errors.push('files.validation.imageDimensionsInvalid');
      }
    }

    if (file.type.startsWith('video/')) {
      if (!isValidDuration(media as HTMLVideoElement, maxVideoDurationSec)) {
        errors.push('files.validation.videoDurationInvalid');
      }
    }
  } catch (error) {
    errors.push((error as ErrorWithMessage)?.message);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
