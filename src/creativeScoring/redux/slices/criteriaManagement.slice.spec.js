import criteriaManagementSlice from './criteriaManagement.slice';
import { GLOBALS } from '../../../constants';
import { CRITERIA_MANAGEMENT_INITIAL_FILTERS } from '../../constants/criteriaManagement.constants';
import { mockCriteriaTemplates } from '../sagas/criteriaManagement.sagas.spec';

const { PENDING, SUCCESS, FAILED } = GLOBALS.REDUX_LOADING_STATUS;
const { actions, name, reducer, getInitialState } = criteriaManagementSlice;

const {
  setCriteria,
  setCriteriaStatus,
  setCriteriaPagination,
  setCriteriaFilter,
  setCriteriaSearchText,
  setBestPracticesData,
  setBestPracticesStatus,
  setBrandIdentifiers,
  setBrandIdentifiersStatus,
  setCriteriaTemplates,
  setCriteriaTemplatesStatus,
} = actions;

const testReducer = (action) => reducer(getInitialState(), action);

describe('Criteria Management Reducer initializes with', () => {
  it('correct reducer name', () => {
    const expectedName = 'criteriaManagement';
    expect(name).toEqual(expectedName);
  });

  it('correct initial state', () => {
    const expectedInitialState = reducer(undefined, { type: 'OTHER' });
    expect(expectedInitialState).toEqual(getInitialState());
  });
});

describe('Criteria Management Reducer sets', () => {
  it('list of criteria', () => {
    const nextState = testReducer(setCriteria({ criteria: mockCriteriaList }));
    expect(nextState.criteria.data).toEqual(mockCriteriaList);
  });
  it('redux loading status on load', () => {
    const nextState = testReducer(setCriteriaStatus({ status: PENDING }));
    expect(nextState.criteria.status).toEqual(PENDING);
  });
  it('redux loading status on fail', () => {
    const nextState = testReducer(setCriteriaStatus({ status: FAILED }));
    expect(nextState.criteria.status).toEqual(FAILED);
  });
  it('redux loading status on success', () => {
    const nextState = testReducer(setCriteriaStatus({ status: SUCCESS }));
    expect(nextState.criteria.status).toEqual(SUCCESS);
  });
  it('pagination', () => {
    const nextState = testReducer(
      setCriteriaPagination({ pagination: mockPagination }),
    );
    expect(nextState.pagination).toEqual(mockPagination);
  });
  it('filters', () => {
    const nextState = testReducer(setCriteriaFilter({ filter: mockFilter }));
    const expectedNextState = {
      ...CRITERIA_MANAGEMENT_INITIAL_FILTERS,
      [mockFilter.field]: mockFilter.value,
    };
    expect(nextState.filters).toEqual(expectedNextState);
  });
  it('search text', () => {
    const nextState = testReducer(
      setCriteriaSearchText({ searchText: mockSearchText }),
    );
    expect(nextState.searchText).toEqual(mockSearchText);
  });
  it('best practices data', () => {
    const nextState = testReducer(
      setBestPracticesData({ data: mockBestPractices }),
    );
    expect(nextState.bestPractices.data).toEqual(mockBestPractices);
  });
  it('best practices status', () => {
    const nextState = testReducer(setBestPracticesStatus({ status: PENDING }));
    expect(nextState.bestPractices.status).toEqual(PENDING);
  });
  it('brand identifiers', () => {
    const nextState = testReducer(
      setBrandIdentifiers({ brandIdentifiers: ['test'] }),
    );
    expect(nextState.brandIdentifiers.data).toEqual(['test']);
  });
  it('brand identifiers status', () => {
    const nextState = testReducer(
      setBrandIdentifiersStatus({ status: PENDING }),
    );
    expect(nextState.brandIdentifiers.status).toEqual(PENDING);
  });
  it('criteria templates', () => {
    const nextState = testReducer(
      setCriteriaTemplates({ criteriaTemplates: mockCriteriaTemplates }),
    );
    expect(nextState.criteriaTemplates.data).toEqual(mockCriteriaTemplates);
  });
  it('criteria templates status', () => {
    const nextState = testReducer(
      setCriteriaTemplatesStatus({ status: PENDING }),
    );
    expect(nextState.criteriaTemplates.status).toEqual(PENDING);
  });
});

const mockCriteriaList = [
  {
    id: 14,
    identifier: 'HUMAN_PRESENCE',
    criteriaType: 'VIDMOB_DEFAULT',
    platformIdentifier: 'ALL_PLATFORMS',
    usesBrandIdentifier: 'false',
    parameters: [
      {
        identifier: 'maxFirstHumanPresence',
        dataType: 'INTEGER',
        units: 'SECONDS',
        multiselect: false,
        values: [1, 2, 3],
      },
    ],
    isBestPractice: 0,
    sequence: 20,
    defaultInstanceParameters: {
      maxFirstHumanPresence: 3,
    },
    applicability: 'ALL',
  },
  {
    id: 37,
    identifier: 'SOUND_OFF',
    criteriaType: 'VIDMOB_OPTIONAL',
    platformIdentifier: 'ALL_PLATFORMS',
    usesBrandIdentifier: 'false',
    parameters: [],
    isBestPractice: 0,
    sequence: 240,
    defaultInstanceParameters: {},
    applicability: 'ALL',
  },
];

const mockPagination = {
  offset: 0,
  perPage: 10,
  nextOffset: 10,
  totalSize: 183,
};

const mockFilter = {
  field: 'ownerIds',
  value: ['1234'],
};

const mockSearchText = 'test';

const mockBestPractices = {
  data: {
    FACEBOOK: {
      bestPracticesTotal: 10,
      bestPracticesIncluded: 8,
      hasAllBestPractices: false,
    },
    INSTAGRAM: {
      bestPracticesTotal: 8,
      bestPracticesIncluded: 8,
      hasAllBestPractices: true,
    },
  },
};
