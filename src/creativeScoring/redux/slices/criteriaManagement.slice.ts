import { createSlice } from '@reduxjs/toolkit';
import { G<PERSON><PERSON>BALS } from '../../../constants';
import {
  CriterionServerType,
  BestPracticesDataV1,
  AvailableOwner,
  CriteriaTemplate,
} from '../../types/criteriaManagement.types';
import {
  CRITERIA_MANAGEMENT_INITIAL_FILTERS,
  SORT_ORDER,
} from '../../constants/criteriaManagement.constants';

const { NOT_LOADED, FAILED, SUCCESS, PENDING } = GLOBALS.REDUX_LOADING_STATUS;
const { ASC, DESC } = SORT_ORDER;

interface CriteriaManagementV2State {
  criteria: Criteria;
  pagination: Pagination;
  sort: SortParams;
  filters: Filters;
  searchText: string;
  bestPractices: BestPractices;
  brandIdentifiers: BrandIdentifiers;
  availableFilterOptions: AvailableFilterOptions;
  criteriaTemplates: CriteriaTemplates;
}

interface Pagination {
  totalSize: number;
  offset: number;
  nextOffset: number;
  perPage: number;
}

interface SortParams {
  sortBy: string | null;
  sortOrder: typeof ASC | typeof DESC | null;
}

interface Filters {
  platforms: string[];
  mediaTypes: string[];
  isOptional: string[];
  ownerIds: number[];
}

interface Criteria {
  data: CriterionServerType[] | [];
  status: Status;
}

interface BestPractices {
  status: Status;
  data: BestPracticesDataV1;
}

interface BrandIdentifiers {
  status: Status;
  data: string | null;
}

interface AvailableFilterOptions {
  owners: AvailableOwner[];
  status: Status;
}

interface CriteriaTemplates {
  data: CriteriaTemplate[];
  status: Status;
}

type Status =
  | typeof NOT_LOADED
  | typeof FAILED
  | typeof SUCCESS
  | typeof PENDING;

const getInitialState = (): CriteriaManagementV2State => ({
  criteria: {
    data: [],
    status: NOT_LOADED,
  },
  pagination: {
    totalSize: 0,
    offset: 0,
    nextOffset: 25,
    perPage: 25,
  },
  sort: {
    sortBy: null,
    sortOrder: null,
  },
  filters: CRITERIA_MANAGEMENT_INITIAL_FILTERS,
  searchText: '',
  bestPractices: {
    data: {},
    status: NOT_LOADED,
  },
  brandIdentifiers: {
    data: null,
    status: NOT_LOADED,
  },
  availableFilterOptions: {
    owners: [],
    status: NOT_LOADED,
  },
  criteriaTemplates: {
    data: [],
    status: NOT_LOADED,
  },
});

const criteriaManagementSlice = createSlice({
  name: 'criteriaManagement',
  initialState: getInitialState(),
  reducers: {
    loadCriteria: (state, _action) => {
      return state;
    },
    loadBestPractices: (state, _action) => {
      return state;
    },
    loadBrandIdentifiers: (state, _action) => {
      return state;
    },
    loadAvailableFilterOptions: (state, _action) => {
      return state;
    },
    loadCriteriaTemplates: (state, _action) => {
      return state;
    },
    setCriteria: (state, action) => {
      const { criteria } = action.payload;
      state.criteria.data = criteria;
    },
    setCriteriaStatus: (state, action) => {
      const { status } = action.payload;
      state.criteria.status = status;
    },
    setBestPracticesData: (state, action) => {
      const { data } = action.payload;
      state.bestPractices.data = data;
    },
    setBestPracticesStatus: (state, action) => {
      const { status } = action.payload;
      state.bestPractices.status = status;
    },
    setBrandIdentifiers: (state, action) => {
      const { brandIdentifiers } = action.payload;
      state.brandIdentifiers.data = brandIdentifiers;
    },
    setBrandIdentifiersStatus: (state, action) => {
      const { status } = action.payload;
      state.brandIdentifiers.status = status;
    },
    setCriteriaTemplates: (state, action) => {
      const { criteriaTemplates } = action.payload;
      state.criteriaTemplates.data = criteriaTemplates;
    },
    setCriteriaTemplatesStatus: (state, action) => {
      const { status } = action.payload;
      state.criteriaTemplates.status = status;
    },
    setCriteriaPagination: (state, action) => {
      const { pagination } = action.payload;
      state.pagination = pagination;
    },
    setCriteriaSort: (state, action) => {
      const { sortBy, sortOrder } = action.payload;
      state.sort.sortBy = sortBy;
      state.sort.sortOrder = sortOrder;
    },
    setCriteriaFilter: (state, action) => {
      const {
        filter: { value, field },
      } = action.payload;
      state.filters = {
        ...state.filters,
        [field]: value,
      };
    },
    setCriteriaSearchText: (state, action) => {
      const { searchText } = action.payload;
      state.searchText = searchText;
    },
    createCriteria: (state, _action) => {
      return state;
    },
    deleteCriteria: (state, _action) => {
      return state;
    },
    createBestPractices: (state, _action) => {
      return state;
    },
    createBrandIdentifiers: (state, _action) => {
      return state;
    },
    setAvailableFilterOptions: (state, action) => {
      const { owners } = action.payload;
      state.availableFilterOptions.owners = owners;
    },
    setAvailableFilterOptionsStatus: (state, action) => {
      const { status } = action.payload;
      state.availableFilterOptions.status = status;
    },
    editCriteria: (state, _action) => {
      return state;
    },
    reset: (state, _action) => {
      const {
        criteria,
        pagination,
        sort,
        filters,
        searchText,
        brandIdentifiers,
        bestPractices,
      } = getInitialState();
      state.criteria = criteria;
      state.pagination = pagination;
      state.sort = sort;
      state.filters = filters;
      state.searchText = searchText;
      state.brandIdentifiers = brandIdentifiers;
      state.bestPractices = bestPractices;
    },
  },
});

export default criteriaManagementSlice;
