import complianceBatchesSlice from '../slices/complianceBatches.slice';
import { COMPLIANCE } from '../../../constants';

const { BATCH_TYPE } = COMPLIANCE;
const { PRE_FLIGHT, IN_FLIGHT } = BATCH_TYPE;
const { name } = complianceBatchesSlice;

/**
 * Gets the compliance batches for partner
 *
 * @param {object} state - the redux state object
 * @returns {[]} - list of criteria sets
 */
const getPartnerBatches = (state) => {
  return state[name].batches.data;
};

/**
 * Gets loading status of batches
 *
 * @param {object} state - the redux state object
 * @returns {string} - the loading status
 */
const getBatchesLoadingStatus = (state) => {
  return state[name].batches.status;
};

const getSelectedScorecard = (state) => {
  return state[name].selectedScorecard.data;
};

/**
 * Gets the ad account selected for the reports inflight creation modal.
 *
 * @param {object} state - the redux state object
 * @returns {[]} - list for templates
 */
const getSelectedAdAccountForReports = (state) => {
  return state[name].selectedAdAccountForReports;
};

/**
 * Get is preflight or inflight scorecard selected.
 *
 * @param {object} state - the redux state object
 * @returns {boolean} - the boolean response for selected batch check
 */
const getIsBatchSelected = (state) => {
  return [IN_FLIGHT, PRE_FLIGHT].includes(
    state[name].selectedScorecard?.data?.batchType,
  );
};

const getIsAdAccount = (state) => {
  const scoreCardIsSet = state[name].selectedScorecard?.data;
  const hasInflightType = [IN_FLIGHT].includes(
    state[name].selectedScorecard?.data?.batchType,
  );
  const isInternal = state[name].selectedScorecard?.data?.isInternal;
  return Boolean(scoreCardIsSet && hasInflightType && isInternal);
};

/**
 * Gets if type of report is Preflight
 *
 * @param {object} state - the redux state object
 * @returns {boolean} - the boolean response for selected batch type check
 */
const getIsPreFlightReport = (state) => {
  return state[name].selectedBatch.data?.batchType === PRE_FLIGHT;
};

/**
 * Gets the platforms of the selected report
 *
 * @param {object} state - the redux state object
 * @returns {string} - the response for selected batch platforms
 */
const getSelectedReportPlatforms = (state) => {
  return state[name].selectedBatch?.data?.platform;
};

/**
 * Gets report type of the selected report
 *
 * @param {object} state - the redux state object
 * @returns {number} - the response for selected batch id
 */
const getSelectedScorecardId = (state) => {
  return state[name].selectedScorecard?.data?.id;
};

/**
 * Gets the status of batch API call
 *
 * @param {object} state - the redux state object
 * @returns {string} - status of the API call
 */
const getSelectedBatchSubmissionStatus = (state) => {
  return state[name].selectedScorecard.status;
};

/**
 * Retrieves a full list of the countries users can associate with batches.
 *
 * @param {object} state - the redux state object
 * @returns {array} - a list of countries as array of objects
 *  */
const getCountriesList = (state) => {
  return state[name].countries.list;
};

const getCountriesListLoadingStatus = (state) => {
  return state[name].countries.status;
};

const getReportSortingParams = (state) => {
  return state[name].reportSortingParams;
};

const getInFlightReportName = (state) => {
  return state[name].inFlightReportCreation.reportName;
};

const getUpdateBatchStatusLoadingStatus = (state) => {
  return state[name].updateBatchStatusLoadingStatus;
};

const getSelectedScorecardStatus = (state) => {
  return state[name].selectedScorecard.status;
};

const getAdAccountMediaForInFlightBatchCreation = (state) => {
  return state[name].adAccountMediaForInflightBatch;
};

const getParamsForInflightReportCreation = (state) => {
  return {
    reportName: state[name].inFlightReportCreation.reportName,
    selectedAdAccountName:
      state[name].inFlightReportCreation.selectedAdAccountName,
    selectedLocations: state[name].inFlightReportCreation.selectedLocations,
    selectedStartDate: state[name].inFlightReportCreation.selectedStartDate,
    selectedEndDate: state[name].inFlightReportCreation.selectedEndDate,
    filters: state[name].inFlightReportCreation.filters,
  };
};

const getScorecards = (state) => {
  return state[name].scorecards.data;
};

const getScorecardsLoadingStatus = (state) => {
  return state[name].scorecards.status;
};

const getScorecardsPagination = (state) => {
  return state[name].scorecards.pagination;
};

const getHasAnyScorecardsOnWorkspace = (state) => {
  return state[name].scorecards.hasAnyScorecardsOnWorkspace;
};

const getHasAnyScorecardsLoadingStatus = (state) => {
  return state[name].scorecards.hasAnyScorecardsLoadingStatus;
};

const getCurrentScorecardLandingViewType = (state) => {
  return state[name].currentScorecardsLandingTypeView;
};

export {
  getPartnerBatches,
  getBatchesLoadingStatus,
  getSelectedScorecard,
  getSelectedAdAccountForReports,
  getSelectedBatchSubmissionStatus,
  getIsBatchSelected,
  getCountriesList,
  getCountriesListLoadingStatus,
  getReportSortingParams,
  getInFlightReportName,
  getSelectedScorecardStatus,
  getUpdateBatchStatusLoadingStatus,
  getAdAccountMediaForInFlightBatchCreation,
  getParamsForInflightReportCreation,
  getIsPreFlightReport,
  getSelectedReportPlatforms,
  getSelectedScorecardId,
  getScorecards,
  getScorecardsLoadingStatus,
  getScorecardsPagination,
  getCurrentScorecardLandingViewType,
  getIsAdAccount,
  getHasAnyScorecardsOnWorkspace,
  getHasAnyScorecardsLoadingStatus,
};
