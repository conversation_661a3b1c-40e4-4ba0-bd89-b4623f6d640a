import complianceBatchesSlice from '../slices/complianceBatches.slice';
import {
  getPartnerBatches,
  getBatchesLoadingStatus,
} from './complianceBatches.selectors';
import ComplianceBatchesService from '../../apiServices/__mocks__/ComplianceBatchesService';
import { GLOBALS } from '../../../constants';

const { name } = complianceBatchesSlice;
const { REDUX_LOADING_STATUS } = GLOBALS;
ComplianceBatchesService.setMockApiTimeout(0); // set mock timeout to zero for unit tests

describe('Compliance batches selectors', () => {
  const testState = {
    [name]: {
      batches: {
        status: REDUX_LOADING_STATUS.NOT_LOADED,
        data: [],
      },
      currentScorecardsLandingTypeView: 'PRE_FLIGHT',
    },
  };

  beforeAll(async () => {
    const batchesDataResponse =
      await ComplianceBatchesService.getComplianceBatchesByPartnerId(12345);
    testState[name].batches.status = REDUX_LOADING_STATUS.SUCCESS;
    testState[name].batches.data = batchesDataResponse.data.batches;
  });

  it('get criteria sets for partner', () => {
    const batches = getPartnerBatches(testState);

    expect.assertions(1);
    expect(batches).toEqual(testState[name].batches.data);
  });

  it('get the data loading status of batches', () => {
    const status = getBatchesLoadingStatus(testState);

    expect.assertions(1);
    expect(status).toEqual(REDUX_LOADING_STATUS.SUCCESS);
  });
});
