import scoringFilterPanelV2Slice from '../slices/filterPanelV2.slice';
import { createSelector } from 'reselect';
import { GLOBALS } from '../../../constants';

const { name } = scoringFilterPanelV2Slice;
const { SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const getReportsFilterParams = (state) => {
  return state[name].reportsManagement.filterParams;
};

const getAvailableFilterOptions = (state) => {
  return state[name].reportsManagement.availableFilters;
};

const getAvailableFiltersStatus = (state) => {
  return state[name].reportsManagement.availableFilters.status;
};

const areFilterOptionsLoaded = createSelector(
  [getAvailableFiltersStatus],
  (status) => {
    return [SUCCESS].includes(status);
  },
);

const getScorecardsLandingStartDate = (state) => {
  return state[name].reportsManagement.filterParams.startDate;
};

const getScorecardsLandingEndDate = (state) => {
  return state[name].reportsManagement.filterParams.endDate;
};

const getScorecardsLandingChannels = (state) => {
  return state[name].reportsManagement.filterParams.channels;
};

const getScorecardsLandingSearchText = (state) => {
  return state[name].reportsManagement.filterParams.searchText;
};

const getScorecardsLandingSortingParams = (state) => {
  return state[name].reportsManagement.sortingParams;
};

const getSelectedFiltersForScorecardsLandingControlBar = createSelector(
  [
    getScorecardsLandingStartDate,
    getScorecardsLandingEndDate,
    getScorecardsLandingChannels,
    getScorecardsLandingSearchText,
  ],
  (startDate, endDate, platforms, searchText) => {
    return {
      startDate,
      endDate,
      platforms,
      searchText,
    };
  },
);

const getReportDetailsAppliedFilters = (state) => {
  return state[name].reportDetails.appliedFilters;
};

const getReportDetailsScorecardId = (state) => {
  return state[name].reportDetails.scorecardId;
};

export {
  getReportsFilterParams,
  getAvailableFilterOptions,
  getAvailableFiltersStatus,
  areFilterOptionsLoaded,
  getReportDetailsAppliedFilters,
  getSelectedFiltersForScorecardsLandingControlBar,
  getScorecardsLandingSortingParams,
  getScorecardsLandingChannels,
  getReportDetailsScorecardId,
};
