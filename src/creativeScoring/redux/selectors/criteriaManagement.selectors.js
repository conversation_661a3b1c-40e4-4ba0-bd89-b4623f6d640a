import CriteriaManagementSlice from '../slices/criteriaManagement.slice';
import { G<PERSON><PERSON><PERSON><PERSON> } from '../../../constants';
import { createSelector } from 'reselect';
import { INITIAL_FILTERS } from '../../constants/criteriaManagement.constants';

const { PENDING, FAILED, NOT_LOADED, SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

const { name } = CriteriaManagementSlice;

export const getCriteriaData = (state) => {
  return state[name].criteria.data;
};

export const getIsCriteriaLoading = (state) => {
  const { status } = state[name].criteria;
  return [NOT_LOADED, PENDING].includes(status);
};

export const getCriteriaPagination = (state) => {
  return state[name].pagination;
};

export const getCriteriaSorting = (state) => {
  return state[name].sort;
};

export const getCriteriaFilters = (state) => {
  return state[name].filters;
};

export const getCriteriaSearchText = (state) => {
  return state[name].searchText;
};

export const getBrandIdentifiersData = (state) => {
  return state[name].brandIdentifiers.data;
};

export const getHaveBrandIdentifiersLoaded = (state) => {
  return state[name].brandIdentifiers.status === SUCCESS;
};

export const getIsBrandIdentifiersLoading = (state) => {
  const { status } = state[name].brandIdentifiers;
  return [NOT_LOADED, PENDING].includes(status);
};

export const getHasBrandIdentifiersFailed = (state) => {
  return state[name].brandIdentifiers.status === FAILED;
};

export const getAvailableFilterOptions = (state) => {
  const { owners } = state[name].availableFilterOptions;
  return {
    owners,
  };
};

export const getIsAvailableFilterOptionsLoading = (state) => {
  const { status } = state[name].availableFilterOptions;
  return [NOT_LOADED, PENDING].includes(status);
};

export const getCriteriaTemplates = (state) => {
  return state[name].criteriaTemplates.data;
};

export const getIsCriteriaTemplatesLoading = (state) => {
  const { status } = state[name].availableFilterOptions;
  return [NOT_LOADED, PENDING].includes(status);
};

export const getCanUserUpdateCriteria = (state) => {
  return state.partner?.currentPartner?.permissions?.canUpdatePartnerCriteria();
};

export const getIsLoading = createSelector(
  [
    getIsCriteriaLoading,
    getIsAvailableFilterOptionsLoading,
    getIsBrandIdentifiersLoading,
    getCanUserUpdateCriteria,
  ],
  (
    isCriteriaLoading,
    isAvailableFilterOptionsLoading,
    isBrandIdentifiersLoading,
    canUserUpdateCriteria,
  ) => {
    return (
      isCriteriaLoading ||
      isAvailableFilterOptionsLoading ||
      (isBrandIdentifiersLoading && canUserUpdateCriteria)
    );
  },
);

// page fail if criteria, best practices or available filter options fails
export const getHasFailed = (state) => {
  const criteriaFailed = state[name].criteria.status === FAILED;
  const bestPracticesFailed = state[name].bestPractices.status === FAILED;
  const availableFilterOptionsFailed =
    state[name].availableFilterOptions.status === FAILED;
  return criteriaFailed || bestPracticesFailed || availableFilterOptionsFailed;
};

const getHasAppliedFilters = createSelector(
  [getCriteriaFilters, getAvailableFilterOptions],
  (currentFilters, availableFilterOptions) => {
    const { owners } = availableFilterOptions;
    return Object.keys(currentFilters).some((filterKey) => {
      const initialFilters = {
        ...INITIAL_FILTERS,
        ownerIds: owners.map((owner) => owner.id),
      };
      return (
        initialFilters[filterKey].length !== currentFilters[filterKey]?.length
      );
    });
  },
);

const getHasSearchText = (state) => {
  return state[name].searchText.length > 0;
};

// no results can be no data at all, or no data with filters or search applied
export const getHasNoResultsFromApi = (state) => {
  const hasLoaded = state[name].criteria.status === SUCCESS;
  const hasNoData = state[name].criteria.data.length === 0;

  return hasNoData && hasLoaded;
};

export const getHasNoData = createSelector(
  [getHasNoResultsFromApi, getHasAppliedFilters, getHasSearchText],
  (hasNoData, hasAppliedFilters, hasSearchText) => {
    return !hasAppliedFilters && !hasSearchText && hasNoData;
  },
);

export const getHasNoFilterResults = createSelector(
  [getHasNoResultsFromApi, getHasAppliedFilters],
  (hasNoData, hasAppliedFilters) => {
    return hasAppliedFilters && hasNoData;
  },
);

export const getHasNoSearchResults = createSelector(
  [getHasNoResultsFromApi, getHasSearchText],
  (hasNoData, hasSearchText) => {
    return hasSearchText && hasNoData;
  },
);

// for preflight create
export const getPlatformCriteriaCounts = createSelector(
  getCriteriaData,
  (criteria) => {
    const criteriaCountTable = {};
    if (criteria) {
      for (const criterion of criteria) {
        const { platform } = criterion;

        if (criteriaCountTable[platform]) {
          criteriaCountTable[platform] += 1;
        }

        if (criteriaCountTable[platform] === undefined) {
          criteriaCountTable[platform] = 1;
        }
      }
    }

    return criteriaCountTable;
  },
);
