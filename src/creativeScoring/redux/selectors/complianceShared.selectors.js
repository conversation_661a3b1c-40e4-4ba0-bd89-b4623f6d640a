import complianceShared from '../slices/complianceShared.slice';
import { createSelector } from 'reselect';
import { COMPLIANCE, GLOBALS } from '../../../constants';
import {
  getContentAuditSearchTermForCompliance,
  getCurrentCriteria,
  getMediaCriteriaResults,
} from './complianceContentAudit.selectors';
import {
  getActiveAccountsExceptPagesForCompliance,
  getInactiveAccountsForCompliance,
  getIsPlatformAccountsLoaded,
} from '../../../redux/selectors/platformAccounts.selectors';
import getIsSplitEnabled from '../../../utils/getIsSplitEnabled';
import {
  getIsBatchSelected,
  getPartnerBatches,
  getBatchesLoadingStatus,
  getReportSortingParams,
  getScorecards,
  getCurrentScorecardLandingViewType,
  getSelectedScorecard,
} from './complianceBatches.selectors';
import { getFormattedScorecard } from '../../featureServices/transforms';
import {
  getReportsFilterParams,
  getReportDetailsAppliedFilters,
  getScorecardsLandingSortingParams,
} from './filterPanelV2.selectors';
import { generatePath } from 'react-router-dom';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const { REDUX_LOADING_STATUS } = GLOBALS;
const { SUCCESS, NOT_LOADED, PENDING } = REDUX_LOADING_STATUS;
const {
  INDIVIDUAL_CRITERIA_RESULTS_API_VALUES,
  BATCH_STATUS,
  APPLICABILITY_MEDIA_TYPES,
  REPORT_TYPE,
} = COMPLIANCE;

const { name } = complianceShared;

// Compliance shared selectors
export const getCurrentAdAccountId = (state) => {
  return state[name].currentAdAccountId;
};

export const getCurrentAdAccountPlatform = (state) => {
  return state[name].currentAdAccountPlatform;
};

export const getMediaIds = (state) => {
  const isLoaded = state[name].videoMediaIds.status === SUCCESS;
  const { data } = state[name].videoMediaIds;
  return isLoaded ? data : null;
};

export const getMediaDataObject = (state, mediaId) => {
  const isLoaded = state[name].videoMediaIds.status === SUCCESS;
  const isIdValid = mediaId && mediaId in state[name].videoMediaData;
  return isLoaded && isIdValid ? state[name].videoMediaData[mediaId] : {};
};

export const getMediaObject = createSelector(
  getMediaDataObject,
  getIsBatchSelected,
  (mediaDataObject, isReportSelected) => {
    return mediaDataObject
      ? isReportSelected
        ? mediaDataObject
        : mediaDataObject.media
      : null;
  },
);

export const getIsJAndJSegmentSelected = (state) => {
  return Boolean(state[name].currentJAndJSegment);
};

export const hasBlankState = createSelector(
  getIsPlatformAccountsLoaded,
  getActiveAccountsExceptPagesForCompliance,
  getInactiveAccountsForCompliance,
  getIsSplitEnabled,
  getPartnerBatches,
  getBatchesLoadingStatus,
  (params) => {
    const {
      arePlatformAccountsLoaded,
      activeAccounts,
      inactiveAccounts,
      isJAndJOptionVisible,
      partnerBatches,
      partnerBatchesLoadingStatus,
    } = params;
    const isBatchesLoading = [NOT_LOADED, PENDING].includes(
      partnerBatchesLoadingStatus,
    );
    const filteredBatches =
      !isBatchesLoading && partnerBatches
        ? partnerBatches.filter((batch) => batch.status !== BATCH_STATUS.SETUP)
        : [];
    return (
      arePlatformAccountsLoaded &&
      !activeAccounts?.length &&
      !inactiveAccounts?.length &&
      !isJAndJOptionVisible &&
      !filteredBatches?.length
    );
  },
);

export const getMediaName = createSelector(
  getMediaObject,
  getIsJAndJSegmentSelected,
  (media, isJnJSelected) => {
    // fall back on file name if display name isn't available
    return media
      ? isJnJSelected && media.title
        ? media.title
        : media.displayName || media.name
      : null;
  },
);

export const getMediaFileType = createSelector(getMediaObject, (media) => {
  return media ? media.fileType : null;
});

export const getVideoMediaData = (state) => {
  return state[name].videoMediaData;
};

export const getScoredMediaObjectsList = createSelector(
  [
    getMediaIds,
    getVideoMediaData,
    getMediaCriteriaResults,
    getCurrentCriteria,
    getIsBatchSelected,
  ],
  (
    mediaIds,
    mediaObjects,
    mediaCriteriaResults,
    currentCriteria,
    isBatchSelected,
  ) => {
    if (mediaIds && mediaObjects) {
      const scoresMap = {
        [INDIVIDUAL_CRITERIA_RESULTS_API_VALUES.CRITERIA_PASS]: [],
        [INDIVIDUAL_CRITERIA_RESULTS_API_VALUES.CRITERIA_FAIL]: [],
        [INDIVIDUAL_CRITERIA_RESULTS_API_VALUES.CRITERIA_NOT_RUN]: [],
        [INDIVIDUAL_CRITERIA_RESULTS_API_VALUES.CRITERIA_NO_DATA]: [],
        OTHER: [],
      };

      mediaIds.forEach((mediaId) => {
        const mediaName =
          mediaObjects[mediaId]?.name || mediaObjects[mediaId]?.displayName;
        const currentCriteriaId = isBatchSelected
          ? currentCriteria?.id
          : currentCriteria;
        const criteriaResult =
          mediaCriteriaResults[currentCriteriaId]?.data[mediaId]?.status;
        const isAnimatedImage =
          mediaObjects[mediaId]?.fileType === APPLICABILITY_MEDIA_TYPES.IMAGE &&
          mediaObjects[mediaId]?.mimeType === 'image/gif';
        const fileType = isAnimatedImage
          ? APPLICABILITY_MEDIA_TYPES.ANIMATED_IMAGE
          : isBatchSelected
            ? mediaObjects[mediaId]?.fileType
            : mediaObjects[mediaId]?.media?.fileType;

        const scoreObject = {
          mediaId,
          mediaName,
          criteriaResult,
          fileType,
        };

        if (criteriaResult in scoresMap) {
          scoresMap[criteriaResult].push(scoreObject);
        } else {
          scoresMap.OTHER.push(scoreObject);
        }
      });

      const result = [].concat(...Object.values(scoresMap));

      return result;
    }
  },
);

/**
 * Gets list of media IDs that can be filtered on search term and media type.
 * This is the list of IDs provided to the aggregate scores V2 endpoint.
 *
 * @param {object[]} scoredMediaObjects - Array of media objects with scores.
 * @param {boolean} isBatchSelected - Indicates whether a batch of media objects is selected.
 * @param {string} searchTerm - The search term used to filter media names.
 * @param {string} currentFilter - The current filter state.
 * @param {object} appliedFilterV2 - The applied filter object (V2 version).
 * @returns {string[]} - An array of filtered media IDs.
 */
export const getFilteredMediaIds = createSelector(
  [
    getScoredMediaObjectsList,
    getIsBatchSelected,
    getContentAuditSearchTermForCompliance,
    getReportDetailsAppliedFilters,
  ],
  (scoredMediaObjects, isBatchSelected, searchTerm, appliedFilterV2) => {
    let filteredMediaIds = null;

    if (Array.isArray(scoredMediaObjects)) {
      filteredMediaIds = [];
      /**
       * Check if a media object is included in the search result based on the search term.
       *
       * @param {string} mediaId - The media ID.
       * @param {string} mediaName - The media name.
       * @returns {boolean} - Whether the media object is included in the search result.
       */
      const isMediaInSearchResult = (mediaId, mediaName) => {
        if (searchTerm) {
          return (
            mediaId &&
            mediaName &&
            mediaName
              .toLocaleLowerCase()
              .includes(searchTerm.toLocaleLowerCase())
          );
        }

        return true;
      };

      /**
       * Check if a media object is included in the filtered media types based on the applied filter.
       *
       * @param {object} mediaObject - The media object.
       * @returns {boolean} - Whether the media object is included in the filtered media types.
       */
      const isMediaInFilteredMediaTypes = (mediaObject) => {
        const currentFileType = mediaObject.fileType;
        return appliedFilterV2.fileType.includes(currentFileType);
      };

      scoredMediaObjects.forEach((mediaObject) => {
        const { mediaId, mediaName } = mediaObject;

        if (
          isMediaInSearchResult(mediaId, mediaName) &&
          isMediaInFilteredMediaTypes(mediaObject)
        ) {
          filteredMediaIds.push(mediaId);
        }
      });
    }

    return filteredMediaIds;
  },
);

export const getShowOutdatedBanner = (state) => {
  return state[name].showOutdatedBanner;
};

export const getHasUpdatedCriteriaOrBrand = (state) => {
  return state[name].hasUpdatedCriteriaOrBrand;
};

export const getMapOfScoredMediaObjects = createSelector(
  getScoredMediaObjectsList,
  (scoredMediaObjects) => {
    if (Array.isArray(scoredMediaObjects)) {
      return scoredMediaObjects.reduce((map, mediaObject) => {
        map[mediaObject.mediaId] = mediaObject;
        return map;
      }, {});
    }
  },
);

export const getFormattedScorecards = createSelector(
  getScorecards,
  getReportSortingParams,
  (scorecards, sortParams) => {
    if (!scorecards) {
      return [];
    }

    const { searchText } = sortParams;

    const isScorecardInSearchResult = (scorecard) => {
      if (searchText) {
        return scorecard.name
          ?.toLocaleLowerCase()
          .includes(searchText.toLocaleLowerCase());
      }

      return true;
    };

    const getFormattedAndFilteredScorecards = (scorecards) => {
      return (scorecards || [])
        .map((scorecard) => getFormattedScorecard(scorecard))
        .filter(isScorecardInSearchResult);
    };

    return getFormattedAndFilteredScorecards(scorecards);
  },
);

export const getScorecardsLandingFilterPayloadV2 = createSelector(
  getReportsFilterParams,
  getScorecardsLandingSortingParams,
  getCurrentScorecardLandingViewType,
  (filterParams, sortingParams, scorecardType) => {
    const {
      creators = [],
      dateCreated = [null, null],
      locations = [],
      channels = [],
      brands = [],
      platformAdAccounts = [],
      projects = [],
      searchText,
    } = filterParams;

    const { sortOrder, sortBy } = sortingParams;

    const sortByMap = {
      lastModified: 'lastModifiedDate',
      totalMediaCount: 'totalMediaCount',
      score: 'score',
      dateCreated: 'createdDate',
      createdBy: 'owner',
      markets: 'market',
      name: 'name',
      brands: 'brand',
    };

    const formatDate = (date) => {
      if (!date) {
        return null;
      }
      return dayjs.utc(date).endOf('day').format('YYYY-MM-DDTHH:mm:ss[Z]');
    };

    const params = {
      platforms: channels,
      creators: creators.map((creator) => creator.toString()),
      startDate: formatDate(dateCreated[0]),
      endDate: formatDate(dateCreated[1]),
      markets: locations,
      platformAdAccounts,
      searchText,
      sortOrder: sortOrder ? sortOrder.toUpperCase() : 'DESC',
      sortBy: sortBy ? sortByMap[sortBy] : sortByMap.lastModified,
    };
    if (scorecardType === REPORT_TYPE.PLUGIN_MEDIA) {
      params.projects = projects;
    }

    if (scorecardType !== REPORT_TYPE.PLUGIN_MEDIA) {
      params.brands = brands;
    }

    return params;
  },
);

export const getScorecardsLandingViewLink = createSelector(
  getCurrentScorecardLandingViewType,
  getSelectedScorecard,
  (type, selectedBatch) => {
    const scorecardType = type || selectedBatch?.batchType || 'PRE_FLIGHT';
    return generatePath(
      '/creativeIntelligence/creative-scoring/scorecards-landing?types=:type',
      { type: scorecardType },
    );
  },
);
