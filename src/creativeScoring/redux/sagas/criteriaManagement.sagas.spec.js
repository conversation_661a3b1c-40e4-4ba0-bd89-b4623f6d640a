import { call, select } from 'redux-saga/effects';
import { expectSaga } from 'redux-saga-test-plan';
import {
  handleLoadCriteria,
  handleLoadBestPractices,
  handleCreateBestPractices,
  handleDeleteCriteria,
  handleLoadBrandIdentifiers,
  handleCreateBrandIdentifiers,
  getCriteriaListApiCall,
  getBestPracticesApiCall,
  createBestPracticesApiCall,
  deleteCriteriaApiCall,
  getBrandIdentifiersApiCall,
  editCriteriaApiCall,
  handleEditCriteria,
  handleLoadCriteriaTemplates,
  getCriteriaTemplatesApiCall,
  createBrandIdentifiersApiCall,
} from './criteriaManagement.sagas';
import { getCurrentPartnerId } from '../../../redux/selectors/partner.selectors';
import {
  getCriteriaFilters,
  getCriteriaSearchText,
  getCriteriaSorting,
  getCriteriaPagination,
} from '../selectors/criteriaManagement.selectors';
import CriteriaManagementSlice from '../slices/criteriaManagement.slice';
import ToastAlertSlice from '../../../redux/slices/toastAlert.slice';
import { GLOBALS } from '../../../constants';
import { LandingPageFilterId } from '../../components/ScoringLandingFilters/scoringLandingFilters.types';
import { getIntl } from '../../../utils/getIntl';

const {
  setCriteria,
  setCriteriaStatus,
  setCriteriaPagination,
  setBestPracticesData,
  setBestPracticesStatus,
  setBrandIdentifiers,
  setBrandIdentifiersStatus,
  setCriteriaTemplatesStatus,
  setCriteriaTemplates,
} = CriteriaManagementSlice.actions;
const { PENDING, SUCCESS, FAILED } = GLOBALS.REDUX_LOADING_STATUS;
const { PLATFORMS, MEDIA_TYPES, IS_OPTIONAL, OWNER_IDS } = LandingPageFilterId;

it('handleLoadCriteria should handle successful criteria loading', () => {
  const mockAction = { type: 'LOAD_CRITERIA' };

  return expectSaga(handleLoadCriteria, mockAction)
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [select(getCriteriaSorting), mockSort],
      [select(getCriteriaFilters), mockFilters],
      [select(getCriteriaSearchText), mockSearchText],
      [select(getCriteriaPagination), mockPagination],
      [
        call(getCriteriaListApiCall, {
          workspaceId: mockWorkspaceId,
          offset: mockPagination.offset,
          pageSize: mockPagination.perPage,
          sort: mockSort,
          filters: mockFilters,
          searchText: mockSearchText,
        }),
        MOCK_CRITERIA_API_RESULT,
      ],
    ])
    .put(setCriteriaStatus({ status: PENDING }))
    .put(setCriteria({ criteria: MOCK_CRITERIA_API_RESULT.data }))
    .put(
      setCriteriaPagination({
        pagination: MOCK_CRITERIA_API_RESULT.pagination,
      }),
    )
    .put(setCriteriaStatus({ status: SUCCESS }))
    .run();
});

it('handleLoadCriteria should handle failed criteria loading', () => {
  const mockError = new Error('An error occurred');

  return expectSaga(handleLoadCriteria)
    .provide({
      call: () => {
        throw mockError;
      },
    })
    .put(setCriteriaStatus({ status: PENDING }))
    .put(setCriteriaStatus({ status: FAILED }))
    .run();
});

it('handles loading best practices successfully', () => {
  return expectSaga(handleLoadBestPractices)
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(getBestPracticesApiCall, mockWorkspaceId),
        { data: mockBestPracticesData },
      ],
    ])
    .put(setBestPracticesStatus({ status: PENDING }))
    .put(
      setBestPracticesData({
        data: mockTransformedBestPracticesData,
      }),
    )
    .put(setBestPracticesStatus({ status: SUCCESS }))
    .run();
});

it('handles failure in loading best practices', () => {
  const error = new Error('An error occurred');

  return expectSaga(handleLoadBestPractices)
    .provide({
      call: () => {
        throw error;
      },
    })
    .put(setBestPracticesStatus({ status: PENDING }))
    .put(setBestPracticesStatus({ status: FAILED }))
    .run();
});

it('handles creating best practices successfully', () => {
  return expectSaga(handleCreateBestPractices, {
    payload: { workspaceId: mockWorkspaceId, channels: [] },
  })
    .provide([[call(createBestPracticesApiCall, mockWorkspaceId, []), {}]])
    .put(
      ToastAlertSlice.actions.showToastAlert({
        message: 'Best practices successfully added.',
        type: 'success',
      }),
    )
    .call(handleLoadBestPractices, { payload: {} })
    .call(handleLoadCriteria, {})
    .run();
});

it('handles failure in creating best practices', () => {
  const error = new Error('An error occurred');
  const mockChannels = ['channel1', 'channel2'];

  return expectSaga(handleCreateBestPractices, {
    payload: { workspaceId: mockWorkspaceId, channels: mockChannels },
  })
    .provide({
      call: () => {
        throw error;
      },
    })
    .put(
      ToastAlertSlice.actions.showToastAlert({
        message: 'Best practices not added. Try again.',
        type: 'error',
      }),
    )
    .run();
});

it('handles successful criteria deletion', () => {
  return expectSaga(handleDeleteCriteria, {
    payload: { criterion: { isBestPractice: true, id: 12 } },
  })
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(deleteCriteriaApiCall, {
          criteriaId: 12,
          workspaceId: mockWorkspaceId,
        }),
        {},
      ],
    ])

    .put(
      ToastAlertSlice.actions.showToastAlert({
        message: 'Criteria successfully removed',
        type: 'success',
      }),
    )
    .call(handleLoadCriteria, { payload: {} })
    .run();
});

it('handles error on criteria deletion', () => {
  const mockError = new Error('An error occurred');

  return expectSaga(handleDeleteCriteria, {
    payload: { criterion: { isBestPractice: true, id: 12 } },
  })
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(deleteCriteriaApiCall, {
          criteriaId: 12,
          workspaceId: mockWorkspaceId,
        }),
        Promise.reject(mockError),
      ],
    ])

    .put(
      ToastAlertSlice.actions.showToastAlert({
        message: 'Criteria not removed. Try again.',
        type: 'error',
      }),
    )
    .run();
});

it('successfully loads brand identifiers', () => {
  const mockWorkspaceId = 'workspace123';
  const mockData = {
    data: [{ brandIdentifier: 'Brand1' }, { brandIdentifier: 'Brand2' }],
  };

  return expectSaga(handleLoadBrandIdentifiers)
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [call(getBrandIdentifiersApiCall, mockWorkspaceId), mockData],
    ])
    .put(setBrandIdentifiersStatus({ status: PENDING }))
    .put(setBrandIdentifiers({ brandIdentifiers: 'Brand1, Brand2' }))
    .put(setBrandIdentifiersStatus({ status: SUCCESS }))
    .run();
});

it('handles failure in loading brand identifiers', () => {
  const mockError = new Error('An error occurred');

  return expectSaga(handleLoadBrandIdentifiers)
    .provide({
      call: () => {
        throw mockError;
      },
    })
    .put(setBrandIdentifiersStatus({ status: PENDING }))
    .put(setBrandIdentifiersStatus({ status: FAILED }))
    .run();
});

it('successfully creates brand identifiers', () => {
  const mockWorkspaceId = 'workspace123';
  const actionPayload = { brandIdentifiers: 'Brand1, Brand2, ' };

  return expectSaga(handleCreateBrandIdentifiers, { payload: actionPayload })
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(createBrandIdentifiersApiCall, {
          workspaceId: mockWorkspaceId,
          brandIdentifiers: ['Brand1', 'Brand2'],
        }),
        {},
      ],
    ])
    .put(
      ToastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.brandIdentifier.add.success.v2'
          ],
        type: 'success',
      }),
    )
    .put(
      setBrandIdentifiers({ brandIdentifiers: actionPayload.brandIdentifiers }),
    )
    .run();
});

it('handles failure in creating brand identifiers', () => {
  const mockError = new Error('An error occurred');
  const actionPayload = { brandIdentifiers: 'Brand1, Brand2, ' };

  return expectSaga(handleCreateBrandIdentifiers, { payload: actionPayload })
    .provide({
      call: () => {
        throw mockError;
      },
    })
    .put(
      ToastAlertSlice.actions.showToastAlert({
        message: 'Brand identifier not saved. Try again.',
        type: 'error',
      }),
    )
    .run();
});

it('handles successful criteria edit', () => {
  const actionPayload = {
    criteriaId: 12,
    params: {
      criteriaName: 'New Criteria Name',
      isGlobal: true,
    },
  };

  return expectSaga(handleEditCriteria, { payload: actionPayload })
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(editCriteriaApiCall, {
          workspaceId: mockWorkspaceId,
          ...actionPayload,
        }),
        {},
      ],
    ])
    .put(
      ToastAlertSlice.actions.showToastAlert({
        message: 'Criteria successfully updated.',
        type: 'success',
      }),
    )
    .call(handleLoadCriteria, {})
    .run();
});

it('handles error on criteria edit', () => {
  const actionPayload = {
    criteriaId: 12,
    params: {
      criteriaName: 'New Criteria Name',
      isGlobal: true,
    },
  };
  const mockError = new Error('Edit criteria failed');

  return expectSaga(handleEditCriteria, { payload: actionPayload })
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(editCriteriaApiCall, {
          workspaceId: mockWorkspaceId,
          ...actionPayload,
        }),
        Promise.reject(mockError),
      ],
    ])
    .put(
      ToastAlertSlice.actions.showToastAlert({
        message: 'Criteria not updated. Try again.',
        type: 'error',
      }),
    )
    .run();
});

it('successfully loads criteria templates', () => {
  return expectSaga(handleLoadCriteriaTemplates)
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(getCriteriaTemplatesApiCall, { workspaceId: mockWorkspaceId }),
        mockCriteriaTemplates,
      ],
    ])
    .put(setCriteriaTemplatesStatus({ status: PENDING }))
    .put(setCriteriaTemplates({ criteriaTemplates: mockCriteriaTemplates }))
    .put(setCriteriaTemplatesStatus({ status: SUCCESS }))
    .run();
});

it('handles error on criteria templates loading', () => {
  const mockError = new Error('Mock error');

  return expectSaga(handleLoadCriteriaTemplates)
    .provide([
      [select(getCurrentPartnerId), mockWorkspaceId],
      [
        call(getCriteriaTemplatesApiCall, { workspaceId: mockWorkspaceId }),
        Promise.reject(mockError),
      ],
    ])
    .put(setCriteriaTemplatesStatus({ status: PENDING }))
    .put(setCriteriaTemplatesStatus({ status: FAILED }))
    .run();
});

const MOCK_CRITERIA_API_RESULT = {
  data: [
    {
      id: 14,
      identifier: 'HUMAN_PRESENCE',
      criteriaType: 'VIDMOB_DEFAULT',
      platformIdentifier: 'ALL_PLATFORMS',
      usesBrandIdentifier: 'false',
      parameters: [
        {
          identifier: 'maxFirstHumanPresence',
          dataType: 'INTEGER',
          units: 'SECONDS',
          multiselect: false,
          values: [1, 2, 3],
        },
      ],
      isBestPractice: 0,
      sequence: 20,
      defaultInstanceParameters: {
        maxFirstHumanPresence: 3,
      },
      applicability: 'ALL',
    },
    {
      id: 37,
      identifier: 'SOUND_OFF',
      criteriaType: 'VIDMOB_OPTIONAL',
      platformIdentifier: 'ALL_PLATFORMS',
      usesBrandIdentifier: 'false',
      parameters: [],
      isBestPractice: 0,
      sequence: 240,
      defaultInstanceParameters: {},
      applicability: 'ALL',
    },
  ],
  pagination: {
    offset: 0,
    perPage: 10,
    nextOffset: 10,
    totalSize: 183,
  },
};
const mockWorkspaceId = 1470;

const mockSort = { sortBy: 'name', sortOrder: 'asc' };

const mockFilters = {
  [PLATFORMS]: ['facebook', 'tiktok'],
  [MEDIA_TYPES]: ['video', 'image'],
  [IS_OPTIONAL]: ['optional'],
  [OWNER_IDS]: [14567],
};

const mockSearchText = '';
const mockPagination = { offset: 0, perPage: 50 };

const mockBestPracticesData = {
  ADWORDS: {
    totalBestPracticeTemplates: 2,
    totalBestPracticeCriteria: 0,
    hasAllBestPractices: false,
    bestPracticeDetails: [
      {
        templateId: 104,
        identifier: 'BRAND_NAME_OR_LOGO',
        criteriaIds: null,
        name: 'Brand Name or Logo',
      },
      {
        templateId: 125,
        identifier: 'SOUND_ON',
        criteriaIds: null,
        name: 'Sound On',
      },
    ],
  },
  FACEBOOK: {
    totalBestPracticeTemplates: 2,
    totalBestPracticeCriteria: 2,
    hasAllBestPractices: true,
    bestPracticeDetails: [
      {
        templateId: 104,
        identifier: 'BRAND_NAME_OR_LOGO',
        criteriaIds: null,
      },
      {
        templateId: 125,
        identifier: 'SOUND_ON',
        criteriaIds: null,
      },
    ],
  },
};
const mockTransformedBestPracticesData = {
  ADWORDS: {
    bestPracticesTotal: 2,
    bestPracticesIncluded: 0,
    hasAllBestPractices: false,
    bestPracticeDetails: mockBestPracticesData.ADWORDS.bestPracticeDetails,
  },
  FACEBOOK: {
    bestPracticesTotal: 2,
    bestPracticesIncluded: 2,
    hasAllBestPractices: true,
    bestPracticeDetails: mockBestPracticesData.FACEBOOK.bestPracticeDetails,
  },
};

export const mockCriteriaTemplates = [
  {
    id: 1234,
    identifier: 'BRAND_OR_LOGO',
    defaultDisplayName: 'Brand or logo present anytime',
    platformIdentifier: 'facebook',
  },
  {
    id: 3412,
    identifier: 'FRAMED_FOR_MOBILE',
    defaultDisplayName: 'Aspect ratio',
    platformIdentifier: 'tiktok',
  },
];
