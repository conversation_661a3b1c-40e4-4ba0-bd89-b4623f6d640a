import { all, call, fork, put, takeLatest, select } from 'redux-saga/effects';
import { getIntl } from '../../../utils/getIntl';
// slices
import CriteriaManagementSlice from '../slices/criteriaManagement.slice';
import toastAlertSlice from '../../../redux/slices/toastAlert.slice';
// selectors
import { getCurrentPartnerId } from '../../../redux/selectors/partner.selectors';
import {
  getCriteriaSorting,
  getCriteriaFilters,
  getCriteriaSearchText,
  getCriteriaPagination,
  getAvailableFilterOptions,
} from '../selectors/criteriaManagement.selectors';
import { getCurrentUserId } from '../../../redux/selectors/user.selectors';
// services
import BffCriteriaManagementService from '../../../apiServices/BffCriteriaManagementService';
import ComplianceCriteriaService from '../../apiServices/ComplianceCriteriaService';
import errorHandler from '../../featureServices/errorHandler';
import { ERR_MSGS } from '../../featureServices/errorMessages';
// constants
import { GLOBALS, COMPLIANCE } from '../../../constants';

const { PARTNER_CRITERIA_PLATFORMS } = COMPLIANCE;

const {
  loadCriteria,
  loadBestPractices,
  loadBrandIdentifiers,
  loadAvailableFilterOptions,
  loadCriteriaTemplates,
  createBestPractices,
  setCriteria,
  setCriteriaStatus,
  setCriteriaPagination,
  setBestPracticesData,
  setBestPracticesStatus,
  setAvailableFilterOptions,
  setAvailableFilterOptionsStatus,
  deleteCriteria,
  setBrandIdentifiers,
  setBrandIdentifiersStatus,
  createBrandIdentifiers,
  editCriteria,
  setCriteriaTemplates,
  setCriteriaTemplatesStatus,
  createCriteria,
} = CriteriaManagementSlice.actions;
const { FAILED, SUCCESS, PENDING } = GLOBALS.REDUX_LOADING_STATUS;

export const getCriteriaListApiCall =
  BffCriteriaManagementService.getCriteriaList.bind(
    BffCriteriaManagementService,
  );
export const getBestPracticesApiCall =
  ComplianceCriteriaService.getBestPracticeCriteria.bind(
    ComplianceCriteriaService,
  );
export const createBestPracticesApiCall =
  ComplianceCriteriaService.createBestPracticeCriteria.bind(
    ComplianceCriteriaService,
  );
export const deleteCriteriaApiCall =
  BffCriteriaManagementService.deleteCriteria.bind(
    BffCriteriaManagementService,
  );
export const getBrandIdentifiersApiCall =
  ComplianceCriteriaService.getPartnerBrandIdentifier.bind(
    ComplianceCriteriaService,
  );
export const createBrandIdentifiersApiCall =
  BffCriteriaManagementService.createBrandIdentifier.bind(
    BffCriteriaManagementService,
  );
export const getAvailableFilterOptionsApiCall =
  BffCriteriaManagementService.getAvailableFilterOptions.bind(
    BffCriteriaManagementService,
  );
export const editCriteriaApiCall =
  BffCriteriaManagementService.editCriteria.bind(BffCriteriaManagementService);
export const getCriteriaTemplatesApiCall =
  BffCriteriaManagementService.getCriteriaTemplates.bind(
    BffCriteriaManagementService,
  );
export const createCriteriaApiCall =
  BffCriteriaManagementService.createCriteria.bind(
    BffCriteriaManagementService,
  );

export function* watchCriteriaManagement() {
  yield all([
    fork(watchLoadCriteria),
    fork(watchLoadBestPractices),
    fork(watchLoadBrandIdentifiers),
    fork(watchLoadAvailableFilterOptions),
    fork(watchLoadCriteriaTemplates),
    fork(watchCreateBestPractices),
    fork(watchCreateBrandIdentifiers),
    fork(watchDeleteCriteria),
    fork(watchEditCriteria),
    fork(watchCreateCriteria),
  ]);
}

export function* watchLoadAvailableFilterOptions() {
  yield takeLatest(
    loadAvailableFilterOptions,
    handleLoadAvailableFilterOptions,
  );
}

export function* watchLoadCriteria() {
  yield takeLatest(loadCriteria, handleLoadCriteria);
}

export function* watchLoadBestPractices() {
  yield takeLatest(loadBestPractices, handleLoadBestPractices);
}

export function* watchLoadBrandIdentifiers() {
  yield takeLatest(loadBrandIdentifiers, handleLoadBrandIdentifiers);
}

export function* watchLoadCriteriaTemplates() {
  yield takeLatest(loadCriteriaTemplates, handleLoadCriteriaTemplates);
}

export function* watchCreateCriteria() {
  yield takeLatest(createCriteria, handleCreateCriteria);
}

export function* watchCreateBestPractices() {
  yield takeLatest(createBestPractices, handleCreateBestPractices);
}

export function* watchDeleteCriteria() {
  yield takeLatest(deleteCriteria, handleDeleteCriteria);
}

export function* watchCreateBrandIdentifiers() {
  yield takeLatest(createBrandIdentifiers, handleCreateBrandIdentifiers);
}

export function* watchEditCriteria() {
  yield takeLatest(editCriteria, handleEditCriteria);
}

export function* handleLoadAvailableFilterOptions() {
  try {
    yield put(setAvailableFilterOptionsStatus({ status: PENDING }));
    const workspaceId = yield select(getCurrentPartnerId);
    const { owners } = yield call(getAvailableFilterOptionsApiCall, {
      workspaceId,
    });
    yield put(setAvailableFilterOptions({ owners }));
    yield put(setAvailableFilterOptionsStatus({ status: SUCCESS }));
  } catch (error) {
    yield put(setAvailableFilterOptionsStatus({ status: FAILED }));
    yield errorHandler(error, [ERR_MSGS.LOAD_AVAILABLE_FILTER_OPTIONS]);
  }
}

export function* handleLoadCriteria() {
  try {
    yield put(setCriteriaStatus({ status: PENDING }));
    const workspaceId = yield select(getCurrentPartnerId);
    const sort = yield select(getCriteriaSorting);
    const filters = yield select(getCriteriaFilters) ?? [];
    const searchText = yield select(getCriteriaSearchText) ?? '';
    const { offset, perPage: pageSize } = yield select(getCriteriaPagination);

    const params = { workspaceId, offset, pageSize, sort, filters, searchText };
    const result = yield call(getCriteriaListApiCall, params);
    yield put(setCriteria({ criteria: result.data }));
    yield put(setCriteriaPagination({ pagination: result.pagination }));
    yield put(setCriteriaStatus({ status: SUCCESS }));
  } catch (error) {
    yield put(setCriteriaStatus({ status: FAILED }));
    yield errorHandler(error, [ERR_MSGS.LOAD_CRITERIA_LIST]);
  }
}

export function* handleLoadBestPractices() {
  try {
    yield put(setBestPracticesStatus({ status: PENDING }));
    const partnerId = yield select(getCurrentPartnerId);
    const { data } = yield call(getBestPracticesApiCall, partnerId);

    const transformedData = Object.keys(data).reduce((acc, platform) => {
      acc[platform] = {
        bestPracticesTotal: data[platform].totalBestPracticeTemplates,
        bestPracticesIncluded: data[platform].totalBestPracticeCriteria,
        hasAllBestPractices: data[platform].hasAllBestPractices,
        bestPracticeDetails: data[platform].bestPracticeDetails,
      };
      return acc;
    }, {});

    yield put(setBestPracticesData({ data: transformedData }));
    yield put(setBestPracticesStatus({ status: SUCCESS }));
  } catch (error) {
    yield put(setBestPracticesStatus({ status: FAILED }));
    yield errorHandler(error, [ERR_MSGS.LOAD_BEST_PRACTICES]);
  }
}

export function* handleLoadBrandIdentifiers() {
  try {
    yield put(setBrandIdentifiersStatus({ status: PENDING }));
    const workspaceId = yield select(getCurrentPartnerId);
    const { data } = yield call(getBrandIdentifiersApiCall, workspaceId);
    const brandIdentifiers = data.map((d) => d.brandIdentifier).join(', '); // saving as a comma separated string
    yield put(setBrandIdentifiers({ brandIdentifiers }));
    yield put(setBrandIdentifiersStatus({ status: SUCCESS }));
  } catch (error) {
    yield put(setBrandIdentifiersStatus({ status: FAILED }));
    yield errorHandler(error, [ERR_MSGS.LOAD_BRAND_IDENTIFIERS]);
  }
}

export function* handleLoadCriteriaTemplates() {
  try {
    yield put(setCriteriaTemplatesStatus({ status: PENDING }));
    const workspaceId = yield select(getCurrentPartnerId);
    const result = yield call(getCriteriaTemplatesApiCall, { workspaceId });
    yield put(setCriteriaTemplates({ criteriaTemplates: result }));
    yield put(setCriteriaTemplatesStatus({ status: SUCCESS }));
  } catch (error) {
    yield put(setCriteriaTemplatesStatus({ status: FAILED }));
    yield errorHandler(error, [ERR_MSGS.LOAD_CRITERIA_TEMPLATES]);
  }
}

export function* handleCreateBrandIdentifiers(action) {
  const { brandIdentifiers } = action.payload;
  try {
    const workspaceId = yield select(getCurrentPartnerId);
    const formattedBrandIdentifiers = brandIdentifiers.length
      ? brandIdentifiers
          .split(',')
          .map((i) => i.trim())
          .filter((i) => i)
      : []; // removing empty strings and trimming
    yield call(createBrandIdentifiersApiCall, {
      workspaceId,
      brandIdentifiers: formattedBrandIdentifiers,
    });
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.brandIdentifier.add.success.v2'
          ],
        type: 'success',
      }),
    );
    yield put(setBrandIdentifiers({ brandIdentifiers }));
  } catch (error) {
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.brandIdentifier.add.error'
          ],
        type: 'error',
      }),
    );
    yield errorHandler(error, [ERR_MSGS.CREATE_BRAND_IDENTIFIERS]);
  }
}

export function* handleCreateBestPractices(action) {
  const { workspaceId, channels } = action.payload;
  try {
    yield call(createBestPracticesApiCall, workspaceId, channels);
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.bestPractices.add.success'
          ],
        type: 'success',
      }),
    );
    yield call(handleLoadBestPractices, { payload: {} }); // Reload best practices data to update bestPracticesIncluded and hasAllBestPractices
    yield call(handleLoadCriteria, {}); // Reload full list of criteria with newly added best practices
  } catch (error) {
    yield errorHandler(error, [ERR_MSGS.CREATE_PARTNER_CRITERIA]);
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.bestPractices.add.error'
          ],
        type: 'error',
      }),
    );
  }
}

export function* handleCreateCriteria(action) {
  const { params } = action.payload;
  try {
    yield call(createCriteriaApiCall, { ...params });
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.create.success'
          ],
        type: 'success',
      }),
    );

    // if it's the first time user is creating a criteria, their id won't be included in availableFilterOptions.owners, so reload the available filter options
    const currentUserId = yield select(getCurrentUserId);
    const { owners } = yield select(getAvailableFilterOptions);
    const availableOwnerIds = owners.map((owner) => owner.id);
    if (!availableOwnerIds.includes(currentUserId.toString())) {
      yield call(handleLoadAvailableFilterOptions, {});
    }

    yield call(handleLoadCriteria, { payload: {} });
  } catch (error) {
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message: generateToastMessage(error),
        type: 'error',
      }),
    );
    yield errorHandler(error, [ERR_MSGS.CREATE_CRITERIA]);
  }
}

export function* handleDeleteCriteria(action) {
  const { id: criteriaId, isBestPractice } = action.payload.criterion;
  const workspaceId = yield select(getCurrentPartnerId);

  try {
    yield call(deleteCriteriaApiCall, {
      criteriaId,
      workspaceId,
    });

    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.delete.success'
          ],
        type: 'success',
      }),
    );
    yield call(handleLoadCriteria, { payload: {} }); // Reload full list of criteria after deletion
    if (isBestPractice) {
      yield call(handleLoadBestPractices, { payload: {} }); // Reload best practices data to update bestPracticesIncluded and hasAllBestPractices
    }
  } catch (error) {
    yield errorHandler(error, [
      ERR_MSGS.DELETE_CRITERIA,
      criteriaId,
      workspaceId,
    ]);
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.delete.error'
          ],
        type: 'error',
      }),
    );
  }
}

export function* handleEditCriteria(action) {
  const { params, criteriaId } = action.payload;
  const workspaceId = yield select(getCurrentPartnerId);
  try {
    yield call(editCriteriaApiCall, { params, criteriaId, workspaceId });
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.edit.success'
          ],
        type: 'success',
      }),
    );
    yield call(handleLoadCriteria, {});
  } catch (error) {
    yield put(
      toastAlertSlice.actions.showToastAlert({
        message:
          getIntl().messages[
            'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.edit.error'
          ],
        type: 'error',
      }),
    );
    yield errorHandler(error, [
      ERR_MSGS.EDIT_CRITERIA,
      criteriaId,
      workspaceId,
    ]);
  }
}

function generateToastMessage(error) {
  if (error.responseData.includes('None were created')) {
    const errorMessage = error.responseData;
    const platforms = errorMessage.match(/\[(.*?)\]/)[1];
    return getIntl().formatMessage(
      {
        id: 'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.create.error.alreadyExists',
      },
      { platform: formatPlatformNames(platforms) },
    );
  }

  return getIntl().messages[
    'ui.creativeScoring.criteriaManagementV2.toastAlert.criteria.create.error'
  ];
}

function formatPlatformNames(platforms) {
  const platformsMapping = PARTNER_CRITERIA_PLATFORMS.reduce(
    (acc, platform) => {
      acc[platform.id] = getIntl().messages[platform.i18nName];
      return acc;
    },
    {},
  );

  return platforms
    .split(', ')
    .map((platform) => platformsMapping[platform])
    .join(', ');
}
