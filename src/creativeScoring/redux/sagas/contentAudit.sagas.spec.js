import { expectSaga } from 'redux-saga-test-plan';
import * as matchers from 'redux-saga-test-plan/matchers';
import complianceContentAuditSlice from '../slices/complianceContentAudit.slice';
import {
  watchAggregateCriteriaDataSetting,
  watchMediaCriteriaDataSetting,
  getMediaAggregateScoresV2,
  getMediaContentAuditScoresV2,
} from './contentAudit.sagas';
import {
  setContentAuditAggData as setContentAuditAggDataActionCreator,
  setContentAuditMediaData as setContentAuditMediaDataActionCreator,
} from '../actions/contentAudit.actions';
import { getFilteredMediaIds } from '../selectors/complianceShared.selectors.js';

jest.mock('../selectors/complianceShared.selectors', () => ({
  getCurrentAdAccountId: jest.fn(),
  getFilteredMediaIds: jest.fn(),
}));

jest.mock('../selectors/complianceBatches.selectors', () => ({
  getSelectedReportPlatforms: jest.fn(),
  getSelectedScorecardId: jest.fn(),
}));

jest.mock(
  '../../../redux/selectors/adAcctSharing/adAcctSharing.selectors',
  () => ({
    getSelectedAdAccountId: jest.fn(),
  }),
);

jest.mock('../selectors/complianceContentAudit.selectors', () => ({
  getAggregateScorePlatforms: jest.fn(),
}));

jest.mock('../selectors/complianceCriteriaManagement.selectors', () => ({
  getDefaultCriteria: jest.fn(),
}));

describe('Content audit saga', () => {
  it('sets aggregate criteria results', () => {
    const mediaIds = [134533, 134534]; // non-empty array
    const apiResponse = {
      data: {
        TALENT: { passedPercent: 0, numMediaExcluded: 2 },
        BRAND_APPEARS_ENOUGH: { passedPercent: 0, numMediaExcluded: 2 },
        BRAND_APPEARS: { passedPercent: 0, numMediaExcluded: 2 },
        FORMAT: { passedPercent: 0, numMediaExcluded: 2 },
        WORDS_PER_SECOND: { passedPercent: 0, numMediaExcluded: 2 },
        POSITIVE_EMOTION: { passedPercent: 0, numMediaExcluded: 2 },
        DURATION: { passedPercent: 0, numMediaExcluded: 2 },
      },
    };

    return expectSaga(watchAggregateCriteriaDataSetting)
      .provide([
        [matchers.call.fn(getMediaAggregateScoresV2), apiResponse],
        [matchers.select.selector(getFilteredMediaIds), mediaIds],
      ])
      .put(
        complianceContentAuditSlice.actions.setAggregateCriteriaStatusPendingV2(),
      )
      .put(
        complianceContentAuditSlice.actions.setAggregateCriteriaDataV2({
          results: apiResponse.data,
        }),
      )
      .dispatch(setContentAuditAggDataActionCreator(mediaIds))
      .silentRun();
  });

  it('sets media criteria results', () => {
    const mediaIDs = [134533, 134534];
    const apiResponse = {
      data: {
        3103: { 134534: { status: 'NO_DATA' }, 134533: { status: 'NO_DATA' } },
        3104: { 134534: { status: 'NO_DATA' }, 134533: { status: 'NO_DATA' } },
        3105: { 134534: { status: 'NO_DATA' }, 134533: { status: 'NO_DATA' } },
      },
    };
    const selectedBatchId = 123;
    const currentAdAccountId = null;
    const channelsArray = ['ALL_PLATFORMS', 'TIKTOK', 'FACEBOOK'];
    const batchType = 'PRE_FLIGHT';

    return expectSaga(watchMediaCriteriaDataSetting)
      .provide([[matchers.call.fn(getMediaContentAuditScoresV2), apiResponse]])
      .put(complianceContentAuditSlice.actions.setMediaCriteriaResultsPending())
      .put(
        complianceContentAuditSlice.actions.setAllMediaCriteriaResultsData({
          results: apiResponse.data,
        }),
      )
      .put(
        complianceContentAuditSlice.actions.setMediaCriteriaResultsComplete(),
      )
      .dispatch(
        setContentAuditMediaDataActionCreator(
          mediaIDs,
          null,
          selectedBatchId,
          currentAdAccountId,
          channelsArray,
          batchType,
        ),
      )
      .silentRun();
  });
});
