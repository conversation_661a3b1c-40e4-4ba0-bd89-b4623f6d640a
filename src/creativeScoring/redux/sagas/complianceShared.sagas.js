import {
  takeLatest,
  call,
  put,
  all,
  fork,
  delay,
  select,
} from 'redux-saga/effects';
import ComplianceSharedService from '../../apiServices/ComplianceSharedService';
import PartnerService from '../../../apiServices/PartnerService';
import complianceShared from '../slices/complianceShared.slice';
import complianceContentAuditSlice from '../slices/complianceContentAudit.slice';
import complianceIndividualAssetSlice from '../slices/complianceIndividualAsset.slice';
import { showLocalizedConfirmationModal } from '../../../utils/confirmationModalControls';
import { COMPLIANCE, GLOBALS } from '../../../constants';
import { getBatchFolderId } from '../selectors/complianceUploads.selectors';
import {
  getCurrentPartner,
  getCurrentPartnerPermissions,
} from '../../../redux/selectors/partner.selectors';
import moment from 'moment';
import complianceBatchesSlice from '../slices/complianceBatches.slice';
import { ERR_MSGS } from '../../featureServices/errorMessages.js';
import errorHandler from '../../featureServices/errorHandler.js';
import {
  setCanAccessExecutiveDashboard as setCanAccessExecutiveDashboardAction,
  applyFilterPanelV2SelectedDates as applyFilterPanelV2SelectedDatesAction,
} from '../actions/shared.actions';
import { setAndLoadScorecardFromURL } from '../actions/contentAudit.actions';
import { getSingleScorecard } from './complianceBatches.sagas';
import { getFormattedScorecard } from '../../featureServices/transforms';

const {
  updateSelectedAdAccount,
  updateSelectedJAndJSegment,
  setStatusPending,
  setStatusLoaded,
  setStatusFailed,
  canAccessExecutiveDashboard,
} = complianceShared.actions;
const { setSelectedScorecard } = complianceBatchesSlice.actions;
const {
  updateLoadScorecardFromURLStatus,
  resetCurrentSearchTerm,
  resetCurrentFilter,
  resetMediaCriteriaResultsData,
  resetContentAuditApiErrorStatus,
  resetMediaCriteriaResultsAndStatus,
} = complianceContentAuditSlice.actions;
const { BATCH_TYPE, J_AND_J_DAM_SEGMENTS } = COMPLIANCE;
const { FAILED, PENDING, SUCCESS } = GLOBALS.REDUX_LOADING_STATUS;

/**
 *
 */
export function* watchComplianceShared() {
  yield all([
    fork(watchAdAccountChanges),
    fork(watchBatchChanges),
    fork(watchSetIsUserAdmin),
    fork(watchLoadAndSetSelectedScorecardFromURL),
  ]);
}

/**
 *
 */
export function* watchAdAccountChanges() {
  // We take only the most recent ad account or J&J segment selected
  // The 1st argument is the action type this saga is watching - or an array of matchers
  // (createSlice auto-generates action types for the reducers it's passed)
  yield takeLatest(
    [
      updateSelectedAdAccount,
      updateSelectedJAndJSegment,
      applyFilterPanelV2SelectedDatesAction,
    ],
    loadNewAdAccount,
  );
}

export function* watchSetIsUserAdmin() {
  yield takeLatest(
    setCanAccessExecutiveDashboardAction,
    handleCanAccessExecutiveDashboard,
  );
}

export function* watchBatchChanges() {
  yield takeLatest(setSelectedScorecard, loadNewComplianceBatch);
}

export function* watchLoadAndSetSelectedScorecardFromURL() {
  yield takeLatest(
    setAndLoadScorecardFromURL,
    loadAndSetSelectedScorecardFromURL,
  );
}

// bind function so the value of 'this' is not null when it is called from the saga
export const getAdAccountMedia = ComplianceSharedService.getAdAccountMedia.bind(
  ComplianceSharedService,
);
export const getJNJMedia = ComplianceSharedService.getJNJMedia.bind(
  ComplianceSharedService,
);
export const getPartnerAssetsInFolder =
  PartnerService.getPartnerAssetsInFolder.bind(PartnerService);
export const getAssetFile = PartnerService.getAssetFile.bind(PartnerService);

/**
 * @param action
 */
export function* loadNewAdAccount(action) {
  yield put(setStatusPending());
  // get ad account info directly from the updateSelectedAdAccount action
  const { platformIdentifier, adAccountId } = action.payload;
  try {
    // Reset user interactions that aren't maintained between selections
    yield put(resetCurrentSearchTerm());
    yield put(resetCurrentFilter());
    yield put(resetContentAuditApiErrorStatus());

    // Only resets if selecting new report.
    yield put(resetMediaCriteriaResultsData({ newReportId: adAccountId }));

    // Different API calls need to happen for J&J media vs ad account media
    if (action.type === updateSelectedJAndJSegment.type) {
      const { brand, market } =
        J_AND_J_DAM_SEGMENTS[action.payload.jAndJSegment];
      const apiResult = yield call(getJNJMedia, brand, market);
      const JNJMedias = apiResult.data;
      const mediaIds = [];
      const videoMediaData = {};
      Object.keys(JNJMedias).forEach((key) => {
        const { mediaId } = JNJMedias[key];
        mediaIds.push(mediaId);
        videoMediaData[mediaId] = JNJMedias[key];
        videoMediaData[mediaId].complianceMediaType = 'JNJMedia';
        videoMediaData[mediaId].media.title = JNJMedias[key].title
          ? JNJMedias[key].title
          : '';
      });
      yield put(setStatusLoaded({ data: mediaIds, videoMediaData }));
    } else {
      const platformMediasResult = yield call(
        getAdAccountMedia,
        adAccountId,
        platformIdentifier,
      );
      const { platformMedias } = platformMediasResult.data;
      const mediaIds = [];
      const videoMediaData = {};
      Object.keys(platformMedias).forEach((key) => {
        const { mediaId } = platformMedias[key];
        mediaIds.push(mediaId);
        videoMediaData[mediaId] = platformMedias[key];
        videoMediaData[mediaId].complianceMediaType = 'platformMedia';
      });
      mediaIds.sort((a, b) => b - a); // descending sort

      yield put(setStatusLoaded({ data: mediaIds, videoMediaData }));
      // We have a useEffect waiting for this loading status to change back to default
      // That way we can get the rest of the scores/data for the media in the new dates
      if (action.type === 'APPLY_REPORT_DETAILS_DATE_FILTER') {
        yield put(resetMediaCriteriaResultsAndStatus());
      }
    }
  } catch (err) {
    try {
      const errorStatus = err.status;
      if (errorStatus === 404) {
        // JnJ-specific error code
        yield errorHandler(err, [ERR_MSGS.COMP_RETRIEVE_JNJ_MEDIA_404]);
        yield put(setStatusFailed({ error: err }));
      } else if (errorStatus === 401) {
        // JnJ-specific error code
        yield errorHandler(err, [ERR_MSGS.COMP_RETRIEVE_JNJ_MEDIA_401]);
        yield put(setStatusFailed({ error: err }));
      } else if (errorStatus === 400) {
        yield errorHandler(err, [ERR_MSGS.COMP_RETRIEVE_ACCOUNT_MEDIA_400]);
        yield put(setStatusFailed({ error: err }));
      } else if (errorStatus >= 500 && errorStatus <= 599) {
        yield errorHandler(err, [ERR_MSGS.COMP_RETRIEVE_ACCOUNT_MEDIA_5XX]);
        yield put(setStatusFailed({ error: err }));
      } else {
        yield errorHandler(err, [ERR_MSGS.COMP_RETRIEVE_ACCOUNT_MEDIA_UNKNOWN]);
        yield put(setStatusFailed({ error: err }));
        yield call(
          showLocalizedConfirmationModal,
          'error.compliance.refresh.header',
          'error.compliance.refresh.adaccount',
          'error.compliance.refresh.submit',
          null,
          { hideCancelOption: true },
        );
        location.reload();
      }
    } catch (err) {
      // if the err is undefined or there's no status field, catch and log as unknown error
      yield errorHandler(err, [ERR_MSGS.COMP_RETRIEVE_ACCOUNT_MEDIA_UNDEFINED]);
      yield put(setStatusFailed({ error: { status: 'error unknown' } }));
      yield call(
        showLocalizedConfirmationModal,
        'error.compliance.refresh.header',
        'error.compliance.refresh.adaccount',
        'error.compliance.refresh.submit',
        null,
        { hideCancelOption: true },
      );
      location.reload();
    }
  }
}

/**
 * @param action
 */
export function* loadNewComplianceBatch(action) {
  const { data } = action.payload;
  const { id: batchId } = data;

  let { partnerAssetFolderId } = data;
  if (!partnerAssetFolderId) {
    while (!partnerAssetFolderId) {
      partnerAssetFolderId = yield select(getBatchFolderId);
      yield delay(1000);
    }
  }

  try {
    yield put(setStatusPending());
    // Reset user interactions that aren't maintained between selections
    yield put(resetCurrentSearchTerm());
    yield put(resetCurrentFilter());
    yield put(resetContentAuditApiErrorStatus());

    // Only resets if selecting new report.
    yield put(resetMediaCriteriaResultsData({ newReportId: batchId }));

    const mediaIds = [];
    const videoMediaData = {};

    // We need ad account media data which contains platform media ID.
    // Platform media ID is needed to query KPI metrics.
    if (data.batchType === BATCH_TYPE.IN_FLIGHT) {
      const { setStartDate, setEndDate } =
        complianceIndividualAssetSlice.actions;
      const startDate = data?.startDate
        ? moment(data.startDate).format('YYYY-MM-DD')
        : moment().subtract(3, 'month').format('YYYY-MM-DD');
      const endDate = data?.endDate
        ? moment(data.endDate).format('YYYY-MM-DD')
        : moment().format('YYYY-MM-DD');
      yield put(setStartDate({ startDate }));
      yield put(setEndDate({ endDate }));
    }

    yield put(setStatusLoaded({ data: mediaIds, videoMediaData }));
  } catch (err) {
    yield errorHandler(err, [
      ERR_MSGS.LOAD_COMP_BATCH,
      data.id,
      data.partnerId,
    ]);
    yield put(setStatusFailed({ error: { status: err.status } }));
  }
}

export function* handleCanAccessExecutiveDashboard() {
  const partner = yield select(getCurrentPartner);
  const currentPartnerPermissions = yield select(getCurrentPartnerPermissions);
  // Only scoring managers can update score override requests
  const isScoringManager =
    currentPartnerPermissions?.canUpdateScoreOverrideRequest();

  try {
    if (partner) {
      const hasAccessToExecutiveDashboard =
        partner.hasAdminRole || partner.hasManagerRole || isScoringManager;
      yield put(
        canAccessExecutiveDashboard({
          canAccessExecutiveDashboard: hasAccessToExecutiveDashboard,
        }),
      );
    }
  } catch (err) {
    yield errorHandler(err, [ERR_MSGS.LOAD_PARTNER_MEMBERS]);
  }
}

export function* loadAndSetSelectedScorecardFromURL(action) {
  const { scorecardId } = action.payload;
  try {
    yield put(updateLoadScorecardFromURLStatus({ status: PENDING }));
    const apiResponse = yield call(getSingleScorecard, scorecardId);
    const formattedScorecardData = yield call(
      getFormattedScorecard,
      apiResponse,
    );
    yield put(setSelectedScorecard({ data: formattedScorecardData }));

    yield put(updateLoadScorecardFromURLStatus({ status: SUCCESS }));
  } catch (err) {
    yield put(updateLoadScorecardFromURLStatus({ status: FAILED }));
    yield errorHandler(err, [ERR_MSGS.LOAD_SCORECARD_FROM_URL]);
  }
}
