import { useQuery, useQueryClient } from '@tanstack/react-query';
import BffBestPracticesService from '../../apiServices/BffBestPracticesService';
import { OutdatedBestPracticesData } from '../types/criteriaManagement.types';

type Props = {
  workspaceId: number;
  channels?: string[];
  enabled: boolean;
};

export const useGetOutdatedBestPractices = ({
  workspaceId,
  channels,
  enabled,
}: Props): {
  isLoading: boolean;
  isError: boolean;
  data?: OutdatedBestPracticesData;
  refetch: () => void;
} => {
  const queryClient = useQueryClient();
  const queryKey = ['getOutdatedBestPractices', { workspaceId, channels }];
  const cachedData = queryClient.getQueryData(queryKey);

  const { isLoading, isError, data, refetch } = useQuery({
    queryKey,
    queryFn: () =>
      BffBestPracticesService.getOutdatedBestPractices(workspaceId, channels),
    enabled: enabled && Boolean(workspaceId) && !cachedData,
    refetchOnWindowFocus: false,
  });

  return { isLoading, isError, data: cachedData || data, refetch };
};
