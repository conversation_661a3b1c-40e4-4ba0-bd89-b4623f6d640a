import { useQuery, useQueryClient } from '@tanstack/react-query';
import BffBestPracticesService from '../../apiServices/BffBestPracticesService';
import { CurrentBestPracticesData } from '../types/criteriaManagement.types';

type Props = {
  workspaceId: number;
  channels?: string[];
};

export const useGetCurrentBestPractices = ({
  workspaceId,
  channels,
}: Props): {
  isLoading: boolean;
  isError: boolean;
  data?: CurrentBestPracticesData;
  refetch: () => void;
} => {
  const queryClient = useQueryClient();

  const queryKey = ['getCurrentBestPractices', { workspaceId, channels }];
  const cachedData = queryClient.getQueryData(queryKey);

  const { isLoading, isError, data, refetch } = useQuery({
    queryKey,
    queryFn: () =>
      BffBestPracticesService.getCurrentBestPractices(workspaceId, channels),
    enabled: Boolean(workspaceId) && !cachedData,
    refetchOnWindowFocus: false,
  });

  return { isLoading, isError, data: cachedData || data, refetch };
};
