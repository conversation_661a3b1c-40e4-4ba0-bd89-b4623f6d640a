import axios from 'axios';
import vidMobConfig from '../../../configs/vidMobConfigConstants';
import { vmSessionManager } from '../../vidmobConnectionManager';
import BffApiInsightServiceV2 from '../../apiServices/BffApiInsightServiceV2';

export class InsightService {
  handlePost = async (endPoint, request) => {
    const url =
      vidMobConfig[process.env.VIDMOB_ENV || 'local'].serviceUrl + endPoint;
    const token = vmSessionManager.getToken();
    const config = {
      headers: {
        Authorization: 'Bearer ' + token,
      },
    };

    const { data: dataFromRequest } = await axios.post(url, request, config);
    if (dataFromRequest?.data) {
      return dataFromRequest.data;
    }

    return {};
  };

  createInsight = async (insight, organizationId, workspaceIds) =>
    BffApiInsightServiceV2.createInsightV2({
      insight,
      organizationId,
      workspaceIds,
    });
}

export default new InsightService();
