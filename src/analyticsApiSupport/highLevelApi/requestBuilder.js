import { isNil, isString } from '../../utils/typeCheckUtils';
import {
  REPORT_TABLE_VIEW_IDS,
  FILE_TYPES,
  KPI_OPTIONS_TIME_BASED,
  TIME_BASED,
  ORGANIC_PLATFORMS,
  TAG_TYPES,
} from '../../constants/ci.constants';
import {
  PLATFORM_FACEBOOK,
  SUPPORTED_PLATFORMS,
} from '../../constants/platform.constants';
import getPlatformAnalytics from '../../creativeAnalytics/services/PlatformAnalytics/index';
import {
  ELEMENT_AVERAGE,
  ELEMENT_TYPES,
  GROUP_BYS,
  REQUEST_TYPES,
  DEFAULT_MIN_TAG_CONFIDENCE_LEVEL,
} from '../../constants/analytics.api.constants';
import { getCelebrityTagConfidence } from '../../utils/tagProcessorUtils';
import { KPI_FORMATS } from '../../types/kpi.types';
/**
 * Build a high level api request
 */
export class HighLevelApiRequestBuilder {
  constructor(platformForRequest) {
    if (!platformForRequest) {
      throw new Error('Missing platform');
    }

    if (typeof platformForRequest !== 'string') {
      throw new Error('Platform not a string');
    }

    const platform = platformForRequest;

    if (!SUPPORTED_PLATFORMS.includes(platform)) {
      throw new Error('Unsupported platform');
    }

    this.platform = platform.toUpperCase();
    this.metrics = [];
    this.platformAnalytics = getPlatformAnalytics(this.platform);
    this.elementAverage = ELEMENT_AVERAGE.element;
    this.kpiOptions = {};
    this.createdByVidmob = undefined;
    this.includeMissingGroups = false;
    this.areMultiAssetAdsDisplayed = false;
    this.includeMetadata = true;
    this.filter = {};
    this.statFilters = {};
    this.minTagConfidenceLevel = DEFAULT_MIN_TAG_CONFIDENCE_LEVEL;
    this.elementType = ELEMENT_TYPES.tag;
    this.organizationId = null;
    this.workspaceIds = null;
  }

  /**
   * @param {string} startDate YYYY-MM-DD
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with startDate
   */
  withStartDate(startDate) {
    if (!startDate) {
      throw new Error('Missing start date');
    }

    this.startDate = startDate;
    return this;
  }

  withOrganizationId(organizationId) {
    this.organizationId = organizationId;
    return this;
  }

  withWorkspaceIds(workspaceIds) {
    if (!workspaceIds || workspaceIds.length === 0) {
      throw new Error('Missing workspace ids');
    }

    this.workspaceIds = workspaceIds;
    return this;
  }

  withStatFilter = (statFilter) => {
    if (!statFilter) {
      statFilter = {};
    }

    this.statFilters = { ...this.statFilters, ...statFilter };
    return this;
  };

  withIncludeMissingGroups(shouldIncludeMissingGroups) {
    this.includeMissingGroups = shouldIncludeMissingGroups;
    return this;
  }

  /**
   * @param {string} endDate YYYY-MM-DD
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with endDate
   */
  withEndDate(endDate) {
    if (!endDate) {
      throw new Error('Missing start date');
    }

    this.endDate = endDate;
    return this;
  }

  /**
   * @param {object} reportTableView reportTableView that contains the id to determine the elementType
   * @returns {HighLevelApiRequestBuilder}  Returns reference to instance with metric
   */
  withReportTableViewType(reportTableView) {
    if (!reportTableView) {
      return this;
    }

    if (reportTableView.id === REPORT_TABLE_VIEW_IDS.ELEMENT_TABLE_VIEW) {
      this.elementType = ELEMENT_TYPES.tag;
    } else if (reportTableView.id === REPORT_TABLE_VIEW_IDS.KPIS_TABLE_VIEW) {
      this.elementType = ELEMENT_TYPES.kpi;
    } else if (
      reportTableView.id === REPORT_TABLE_VIEW_IDS.MEDIA_DURATION_TABLE_VIEW
    ) {
      this.elementType = ELEMENT_TYPES.mediaDuration;
    } else {
      this.elementType = ELEMENT_TYPES.none;
    }

    return this;
  }

  withTagTimeRangeBased(isDefaultKPITimeRangeUsed = true) {
    if (isDefaultKPITimeRangeUsed) {
      this.kpiOptions.timeBased = KPI_OPTIONS_TIME_BASED.kpiDefault;
    } else {
      this.kpiOptions.timeBased = KPI_OPTIONS_TIME_BASED.custom;
      this.kpiOptions.timeBasedCustom = {
        value: 1,
        type: TIME_BASED.percentage,
      };
    }

    return this;
  }

  /**
   * @param {boolean/undefined} createdByVidmob boolean/undefined for including/excluding Vidmob creative
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with boolean
   */
  withVidmobCreative(createdByVidmob) {
    this.createdByVidmob = createdByVidmob;
    return this;
  }

  /**
   * @param {number} minConfidenceLevel boolean for the number of min confidence for tags
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with boolean
   */
  withCustomTagConfidenceLevel(minConfidenceLevel) {
    this.minTagConfidenceLevel = minConfidenceLevel;
    return this;
  }

  withAdSetFilterActive(selectedAdSets) {
    if (
      this.platformAnalytics.supportAdSetFilter() &&
      selectedAdSets &&
      selectedAdSets.length > 0
    ) {
      const adSetIds = selectedAdSets.map((adSet) => adSet.id);
      this.filter = {
        ...this.filter,
        ...this.platformAnalytics.getRequestFilterModifierToAddAdSetSearch(
          adSetIds,
        ),
      };
    }

    return this;
  }

  withSparkAdsFilterActive(isSparkAdsFilterActive) {
    if (this.platformAnalytics.supportSparkAdsFilter()) {
      this.areSparkAdsDisplayed = isSparkAdsFilterActive;
      this.filter = {
        ...this.filter,
        ...this.platformAnalytics.getRequestFilterModifierToAddSparkAds(
          isSparkAdsFilterActive,
        ),
      };
    } else {
      this.areSparkAdsDisplayed = false;
    }

    return this;
  }

  withSponsoredDisplayFilterActive = (isSponsoredDisplayActive) => {
    if (
      isSponsoredDisplayActive &&
      this.platformAnalytics.supportSponsoredAdsFilter()
    ) {
      this.filter = {
        ...this.filter,
        ...this.platformAnalytics.getRequestFilterModifierToAddSponsoredDisplayAds(),
      };
    }

    return this;
  };

  withNoBreakDown() {
    this.elementType = ELEMENT_TYPES.none;
    return this;
  }

  withMetrics(metrics) {
    this.metrics = [...metrics];
    return this;
  }

  withDimensions(dimensions) {
    this.dimensions = dimensions;
    return this;
  }

  withCustomFilters(customFilters) {
    if (customFilters && !isNil(customFilters)) {
      const filterSelections =
        this.platformAnalytics.getCustomFiltersModalDescription();
      Object.keys(customFilters).forEach((filterIdentifier) => {
        const filterOptions = filterSelections.find(
          (i) => i.identifier === filterIdentifier,
        ).options;
        if (customFilters[filterIdentifier]) {
          Object.keys(customFilters[filterIdentifier]).forEach(
            (selectedOptionIdentifier) => {
              const selectedOption = filterOptions.find(
                (i) => i.identifier === selectedOptionIdentifier,
              );
              // if the option is a filter type
              if (selectedOption.filter) {
                Object.keys(selectedOption.filter).forEach(
                  (filterDimension) => {
                    if (
                      customFilters[filterIdentifier][selectedOptionIdentifier]
                    ) {
                      if (!this.filter[filterDimension]) {
                        this.filter[filterDimension] = [];
                      }

                      this.filter[filterDimension] = [
                        ...this.filter[filterDimension],
                        ...selectedOption.filter[filterDimension],
                      ];
                    }
                  },
                );
              }

              // can add other option types
            },
          );
        }
      });
    }

    return this;
  }

  withImpressionsFilter(min, max) {
    if (!min && !max) {
      return this;
    }

    const impressionFilterValues = [];
    if (min) {
      impressionFilterValues.push(
        `$metric(${this.platformAnalytics.getImpressionMetricKey()}, gte, ${min})`,
      );
    }

    if (max) {
      impressionFilterValues.push(
        `$metric(${this.platformAnalytics.getImpressionMetricKey()}, lte, ${max})`,
      );
    }

    const adDimension = this.platformAnalytics.getAdDimensions();

    if (Array.isArray(adDimension)) {
      adDimension.forEach((dimension) => {
        if (!this.filter[dimension]) {
          this.filter[dimension] = [];
        }

        this.filter[dimension] = [
          ...this.filter[dimension],
          ...impressionFilterValues,
        ];
      });
    } else {
      this.filter[adDimension] = impressionFilterValues;
    }

    return this;
  }

  /**
   * Include metadata options
   *
   * @param {boolean} includeMetadata include count of metadata
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with OP filters
   */
  withIncludeMetadatas(includeMetadata) {
    this.includeMetadata = includeMetadata;

    return this;
  }

  /**
   * Add Custom Kpi List For Response
   *
   * @param {Kpi[]} kpis kpi array
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with OP filters
   */
  withKpis(kpis, currency) {
    if (!this.kpiOptions) {
      this.kpiOptions = {};
    }

    if (kpis && kpis.length) {
      this.kpiOptions.kpis = kpis.map((kpi) => `${kpi.id}`);
    }

    if (
      kpis?.length === 1 &&
      kpis[0]?.format?.name === KPI_FORMATS.SPEND.toLowerCase() &&
      currency
    ) {
      this.kpiOptions.currency = currency;
    }

    return this;
  }

  /**
   * @param {object} filters main filter, includes account info, etc..
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with filters
   */
  withFilters(filters) {
    this.filter = this.platformAnalytics.getFilters({
      ...this.filter,
      ...filters,
    });
    return this;
  }

  withElementType(elementType) {
    if (elementType) {
      if (!ELEMENT_TYPES[elementType]) {
        throw new Error('Unrecognized Element Type');
      }

      this.elementType = ELEMENT_TYPES[elementType];
    }

    return this;
  }

  withElementAverage(elementAverage) {
    if (elementAverage) {
      if (!ELEMENT_AVERAGE[elementAverage]) {
        throw new Error('Unrecognized Element Average');
      }

      this.elementAverage = ELEMENT_AVERAGE[elementAverage];
    }

    return this;
  }

  /**
   * @param {Array} fileTypes Array of String from FILE_TYPES
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with fileTypes
   */
  withFileTypes(fileTypes) {
    if (!Array.isArray(fileTypes)) {
      throw new Error('Invalid file types');
    }

    let fileTypesToFilter = fileTypes;

    // we allow the user to de-select all media types in the panel, which indicates that they want all media types
    if (!fileTypes.length) {
      fileTypesToFilter = this.platformAnalytics.supportedMediaTypeFilters;
    }

    this.fileTypes = [...new Set(fileTypesToFilter)].filter((i) => {
      if (!isString(i)) {
        return false;
      }

      return FILE_TYPES[i.toLowerCase()];
    });
    return this;
  }

  /**
   * @param {Array} values Array Groupby Values
   * @returns {HighLevelApiRequestBuilder} Returns reference to instance with groupBy values
   */
  withGroupByValues(values) {
    this.values = values;
    return this;
  }

  _getFileTypes() {
    if (this.fileTypes) {
      return this.fileTypes;
    }

    return [FILE_TYPES.video];
  }

  _requestTemplate() {
    if (!this.filter) {
      throw new Error('Missing creative request info');
    }

    if (this.platformAnalytics.supportSparkAdsFilter()) {
      this.filter = {
        ...this.filter,
        ...this.platformAnalytics.getRequestFilterModifierToAddSparkAds(
          this.areSparkAdsDisplayed,
        ),
      };
    }

    const startEndDate = {};
    if (this.startDate && this.endDate) {
      startEndDate.startDate = this.startDate;
      startEndDate.endDate = this.endDate;
    }

    const filters = {
      platform: this.platform,
      ...startEndDate,
      filter: this.filter,
      metrics: this.metrics,
      fileTypes: this._getFileTypes(),
      kpiOptions: this.kpiOptions,
      options: {
        includeMetadata: this.includeMetadata,
        createdByVidmob: this.createdByVidmob,
        disableDeduplicateStats: false,
      },
      dimensions: this.dimensions ? this.dimensions : [],
      elementBy: {},
      groupBy: {},
      statFilters: this.statFilters ? this.statFilters : {},
    };

    if (this.organizationId) {
      filters.organizationId = this.organizationId;
    }

    if (this.workspaceIds) {
      filters.workspaceIds = this.workspaceIds;
    }

    return filters;
  }

  _buildAdOrCreative(option) {
    const shouldAggregateAllAdTypesForFacebook =
      this.platform.toUpperCase() === PLATFORM_FACEBOOK;

    return {
      ...this._requestTemplate(),
      ...(shouldAggregateAllAdTypesForFacebook
        ? { aggregateAllAdTypes: true }
        : {}),
      dimensions: this.dimensions ? this.dimensions : [],
      elementBy: {
        type: ELEMENT_TYPES.none,
      },
      groupBy: {
        type: option,
      },
      options: {
        includeMetadata: this.includeMetadata,
        metadataDetails: true,
        createdByVidmob: this.createdByVidmob,
        disableDeduplicateStats: false,
      },
    };
  }

  _buildMediaGrouping() {
    if (
      !this.filter ||
      !this.values ||
      !this.values.length ||
      !this.elementType
    ) {
      throw new Error('Missing creative grouping request info');
    }

    const values = this.values.filter(
      (item) =>
        item.title && item.platformMediaIds && item.platformMediaIds.length,
    );

    if (!values.length) {
      throw new Error('Invalid value list');
    }

    return {
      ...this._requestTemplate(),
      elementBy: {
        type: ELEMENT_TYPES[this.elementType],
        average: this.elementAverage,
        minTagConfidence: this.minTagConfidenceLevel,
        minTagConfidences: this.getMinTagConfidences(),
      },
      groupBy: {
        type: GROUP_BYS.creative,
        values,
      },
      options: {
        ...this._requestTemplate().options,
        includeGenericTagTypeTags: true,
        includeAccountAverage: true,
      },
    };
  }

  _buildFormat() {
    if (!this.filter || !this.elementType) {
      throw new Error('Missing format request info');
    }

    const values = this.platformAnalytics.getFormat();
    const type = this.platformAnalytics.getFormatType();
    const groupBy = { values, type };
    if (type === GROUP_BYS.dimension) {
      if (!this.platformAnalytics.getFormatFormatter()) {
        throw new Error(
          'Missing implementation for getFormatFormatter on platform ' +
            this.platform,
        );
      }

      groupBy.formatter = this.platformAnalytics.getFormatFormatter();
    }

    return {
      ...this._requestTemplate(),
      elementBy: {
        type: ELEMENT_TYPES[this.elementType],
        average: this.elementAverage,
        minTagConfidence: this.minTagConfidenceLevel,
        minTagConfidences: this.getMinTagConfidences(),
      },
      groupBy,
    };
  }

  _buildAudience() {
    if (
      !this.filter ||
      !this.values ||
      !this.values.length ||
      !this.elementType
    ) {
      throw new Error('Missing audience request info');
    }

    const values = this.values
      .filter((item) => item.title && item.filter)
      .map((item) => ({
        ...item,
        filter: this.platformAnalytics.formatFilters(item.filter),
      }));

    if (!values.length) {
      throw new Error('Invalid value list');
    }

    return {
      ...this._requestTemplate(),
      elementBy: {
        type: ELEMENT_TYPES[this.elementType],
        average: this.elementAverage,
        minTagConfidence: this.minTagConfidenceLevel,
        minTagConfidences: this.getMinTagConfidences(),
      },
      groupBy: {
        type: GROUP_BYS.filter,
        values,
      },
    };
  }

  _buildObjectivePlacement(options) {
    const isOrganic = ORGANIC_PLATFORMS.includes(this.platform);

    if (!this.platformAnalytics.supportOp()) {
      throw new Error(
        'Platform ' +
          this.platform +
          ' does not support objective/placement breakdown',
      );
    }

    if (!this.filter || !this.elementType || !options) {
      throw new Error('Missing objective/placement request info');
    }

    const template = this._requestTemplate();
    const result = {
      ...template,
      dimensions: [
        ...new Set([...template.dimensions, ...options.extraDimension]),
      ],
      filter: { ...options.filter, ...this.filter },
      elementBy: {
        type: ELEMENT_TYPES[this.elementType],
        average: this.elementAverage,
        minTagConfidence: this.minTagConfidenceLevel,
        minTagConfidences: this.getMinTagConfidences(),
      },
      groupBy: {
        type: GROUP_BYS.dimension,
        formatter: {
          dimension: options.dimension,
          method: options.method,
        },
        values: options.formats,
      },
    };

    if (options.filterBy && options.filterBy.length) {
      result.filter = {
        ...result.filter,
        [options.dimension]: options.filterBy,
      };
    }

    if (isOrganic) {
      result.options = {
        ...template.options,
        includeGenericTagTypeTags: true,
        includeAccountAverage: true,
      };
    }

    return result;
  }

  build(requestType, option) {
    switch (requestType) {
      case REQUEST_TYPES.ad:
        return this._buildAdOrCreative(GROUP_BYS.ad);
      case REQUEST_TYPES.creative:
        return this._buildAdOrCreative(GROUP_BYS.creative);
      case REQUEST_TYPES.creativeGrouping:
        return this._buildMediaGrouping();
      case REQUEST_TYPES.format:
        return this._buildFormat();
      case REQUEST_TYPES.audience:
        return this._buildAudience();
      case REQUEST_TYPES.objective:
        return this._buildObjectivePlacement(option);
      case REQUEST_TYPES.placement:
        return this._buildObjectivePlacement(option);
      default:
        throw new Error(`Unrecognized Request Type ${requestType}`);
    }
  }

  getMinTagConfidences() {
    return {
      defaultTo: this.minTagConfidenceLevel,
      [TAG_TYPES.CELEBRITY_NAME]: getCelebrityTagConfidence(
        this.minTagConfidenceLevel,
      ),
    };
  }
}
