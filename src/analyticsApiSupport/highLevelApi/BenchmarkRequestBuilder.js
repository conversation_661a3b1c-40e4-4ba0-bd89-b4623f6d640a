import { ACCOUNT_TYPE } from '../../constants/ci.constants';
import {
  GenericReportRequestBuilder,
  REQUEST_TYPE_BENCHMARK,
} from './genericReportRequestBuilder';
import getPlatformAnalytics from '../../creativeAnalytics/services/PlatformAnalytics';
import { ciStabilityErrorHandler } from '../../utils/vmErrorLog';
import {
  BENCHMARK_REPORT_DEFAULT_TIMEFRAME,
  BENCHMARK_REPORT_FACTOR_TYPE_NONE,
  KPI_NORM_TYPES,
} from '../../constants/analytics.api.constants';

/**
 * Simplifying benchmark request building process by auto generate norms item of platform and industry if presented
 */
export class BenchmarkRequestBuilder extends GenericReportRequestBuilder {
  _getRequestType = () => {
    return REQUEST_TYPE_BENCHMARK;
  };

  _createPayload = () => {
    if (!this.account) {
      const error = new Error('Missing account info');
      ciStabilityErrorHandler(
        error,
        'BenchmarkRequestBuilder',
        'createPayload()',
        'this.account',
      );
      throw error;
    }

    const build = {
      accountId: this.account.id,
      platform: this.account.platform,
      norms: [],
    };

    if (this.startDate && this.endDate) {
      build.startDate = this.startDate;
      build.endDate = this.endDate;
    }

    // building norm request against platform
    let factorType = BENCHMARK_REPORT_FACTOR_TYPE_NONE;
    const factors = [];
    if (this.objective) {
      factorType = this.platformAnalytics.getBenchmarkObjectiveFactorType();
      factors.push(this.objective);
    }

    const normsForPlatform = {
      factorType,
      factors,
      timeframeType: BENCHMARK_REPORT_DEFAULT_TIMEFRAME,
      normType: KPI_NORM_TYPES.PLATFORM,
    };
    build.norms.push(normsForPlatform);

    // building norm request for industry
    if (this.industryId) {
      const normsForIndustry = {
        factorType,
        factors,
        timeframeType: BENCHMARK_REPORT_DEFAULT_TIMEFRAME,
        industryId: this.industryId,
        normType: KPI_NORM_TYPES.INDUSTRY,
      };
      build.norms.push(normsForIndustry);
    } else if (this.includeIndustry) {
      const normsForIndustry = {
        factorType,
        factors,
        timeframeType: BENCHMARK_REPORT_DEFAULT_TIMEFRAME,
        industryId: 0, // api will figure out correct industry for the account
        normType: KPI_NORM_TYPES.INDUSTRY,
      };
      build.norms.push(normsForIndustry);
    }

    if (this.includeCustomNorms) {
      build.norms.push({
        factorType,
        factors: [BENCHMARK_REPORT_FACTOR_TYPE_NONE],
        timeframeType: BENCHMARK_REPORT_DEFAULT_TIMEFRAME,
        normType: KPI_NORM_TYPES.CUSTOM,
      });
    }

    return build;
  };

  /**
   * Set account for request
   *
   * @param {{id, platform, type}} currentSelectedAccount is object from redux
   * @returns {BenchmarkRequestBuilder} for chain operation
   */
  withAccount = (currentSelectedAccount) => {
    if (!currentSelectedAccount) {
      throw new Error('Missing current account');
    }

    if (currentSelectedAccount.type === ACCOUNT_TYPE.group) {
      throw new Error('Not support account type group');
    }

    this.account = currentSelectedAccount;
    this.platformAnalytics = getPlatformAnalytics(
      currentSelectedAccount.platform,
    );
    return this;
  };

  /**
   * set start and end date
   *
   * @param {string} startDate format YYYY-MM-DD
   * @param {string} endDate format YYYY-MM-DD
   * @returns {BenchmarkRequestBuilder} for chain operation
   */
  withDate = (startDate, endDate) => {
    this.startDate = startDate;
    this.endDate = endDate;
    return this;
  };

  /**
   * set objective
   *
   * @param {string} objective objective value to be used
   * @returns {BenchmarkRequestBuilder} for chain operation
   */
  withObjective = (objective) => {
    this.objective = objective;
    return this;
  };

  /**
   * Can use this function to make the request always ask for comparing against industry norm,
   * the api will figure out the industry that the account belong to itself
   *
   * @returns {BenchmarkRequestBuilder} for chain operation
   */
  includeIndustryByDefault = () => {
    this.includeIndustry = true;
    return this;
  };

  /**
   * Can use this function to make the request always ask for comparing against custom norm
   * benchmark for that account, factor and factor type
   *
   * @returns {BenchmarkRequestBuilder} for chain operation
   */
  includeCustomNormsByDefault = () => {
    this.includeCustomNorms = true;
    return this;
  };

  /**
   * Manually set industry id to the request - not recommended, shoudl just use includeIndustryByDefault and let the api figure out the industry
   *
   * @param {number} industryId industry id
   * @returns {BenchmarkRequestBuilder} for chain operation
   */
  withIndustryId = (industryId) => {
    this.industryId = industryId;
    return this;
  };
}
