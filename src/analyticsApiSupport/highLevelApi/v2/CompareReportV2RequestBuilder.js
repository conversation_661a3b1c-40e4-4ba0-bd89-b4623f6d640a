import {
  GenericReportRequestBuilder,
  REQUEST_TYPE_COMPAREREPORT,
} from '../genericReportRequestBuilder';
import {
  FILE_TYPES,
  QUERY_OPTIONS,
  REPORT_TABLE_VIEW_IDS,
  TAG_TYPES,
  TIME_BASED,
} from '../../../constants/ci.constants';
import {
  AVERAGE_BYS_TYPE,
  DEFAULT_MIN_TAG_CONFIDENCE_LEVEL,
  ELEMENT_AVERAGE,
  ELEMENT_TYPES,
  GROUP_BYS,
  REQUEST_TYPES,
} from '../../../constants/analytics.api.constants';
import { isNil, isString } from '../../../utils/typeCheckUtils';
import { getCelebrityTagConfidence } from '../../../utils/tagProcessorUtils';
import { PLATFORM_FACEBOOK } from '../../../constants/platform.constants';
import { KPI_FORMATS } from '../../../types/kpi.types';
import { NOT_IN_USE_KPI } from '../../../creativeAnalytics/components/AnalyticsFilters/AnalyticsFiltersConstants';
import { getFeatureFlag } from '../../../utils/featureFlagUtils';
import { MediaType } from '../../../types/mediaTypes.types';

const isMessagingApertureEnabled = getFeatureFlag('isMessagingApertureEnabled');

export class CompareReportV2RequestBuilder extends GenericReportRequestBuilder {
  filter = {};

  organizationId = null;

  workspaceIds = null;

  fileTypes = [FILE_TYPES.video];

  averageBy = {
    type: AVERAGE_BYS_TYPE.none,
  };

  _getRequestType = () => {
    return REQUEST_TYPE_COMPAREREPORT;
  };

  _createPayload = () => {
    if (!this.filter) {
      throw new Error('Missing creative request info');
    }

    if (!this.kpiOptions) {
      throw new Error('Missing KPI configuration');
    }

    if (!this.elementBy) {
      throw new Error('Missing element by setup');
    }

    if (!this.groupBy) {
      throw new Error('Missing group by setup');
    }

    const startEndDate = {};
    if (this.startDate && this.endDate) {
      startEndDate.startDate = this.startDate;
      startEndDate.endDate = this.endDate;
    }

    const shouldAggregateAllAdTypesForFacebook =
      this.platform.toUpperCase() === PLATFORM_FACEBOOK;

    const filters = {
      platform: this.platform,
      ...startEndDate,
      filter: this.filter,
      metrics: this.metrics,
      fileTypes: this.fileTypes,
      kpiOptions: this.kpiOptions,
      element: this.element,
      options: {
        createdByVidmob: this.createdByVidmob,
        includeTracker: Boolean(this.includeTracker),
        includeMissingGroups: Boolean(this.includeMissingGroups),
        includeMergedStats: Boolean(this.includeMergedStats),
      },
      dimensions: this.dimensions?.length ? this.dimensions : [],
      elementBy: this.elementBy,
      groupBy: this.groupBy,
      averageBy: this.averageBy,
      ...(shouldAggregateAllAdTypesForFacebook
        ? { aggregateAllAdTypes: true }
        : {}),
    };

    if (this.organizationId) {
      filters.organizationId = this.organizationId;
    }

    if (this.workspaceIds) {
      filters.workspaceIds = this.workspaceIds;
    }

    if (isMessagingApertureEnabled) {
      filters.options = { ...filters.options, includeApertureTags: true };
    }

    return filters;
  };

  withIncludeMissingGroups(shouldIncludeMissingGroups) {
    this.includeMissingGroups = shouldIncludeMissingGroups;
    return this;
  }

  withStartDate(startDate) {
    if (!startDate) {
      throw new Error('Missing start date');
    }

    this.startDate = startDate;
    return this;
  }

  withOrganizationId(organizationId) {
    if (!organizationId) {
      throw new Error('Missing organization id');
    }

    this.organizationId = organizationId;
    return this;
  }

  withWorkspaceId(workspaceIds) {
    if (!workspaceIds || workspaceIds.length === 0) {
      throw new Error('Missing workspace ids');
    }

    this.workspaceIds = workspaceIds;
    return this;
  }

  /**
   * @param {string} endDate YYYY-MM-DD
   * @returns {CompareReportV2RequestBuilder} Returns reference to instance with endDate
   */
  withEndDate(endDate) {
    if (!endDate) {
      throw new Error('Missing start date');
    }

    this.endDate = endDate;
    return this;
  }

  withReportTableView(
    reportTableView,
    minimumTagConfidenceLevel,
    elementAverage = ELEMENT_AVERAGE.element,
  ) {
    if (!reportTableView) {
      return this;
    }

    if (reportTableView.id === REPORT_TABLE_VIEW_IDS.ELEMENT_TABLE_VIEW) {
      this.elementType = ELEMENT_TYPES.tagAndType;
    } else if (reportTableView.id === REPORT_TABLE_VIEW_IDS.KPIS_TABLE_VIEW) {
      this.elementType = ELEMENT_TYPES.kpi;
    } else if (
      reportTableView.id === REPORT_TABLE_VIEW_IDS.MEDIA_DURATION_TABLE_VIEW
    ) {
      this.elementType = ELEMENT_TYPES.mediaDuration;
    } else {
      this.elementType = ELEMENT_TYPES.none;
    }

    this.withElementBy(
      this.elementType,
      minimumTagConfidenceLevel,
      elementAverage,
    );

    return this;
  }

  /**
   * build element by
   *
   * @param {string} elementType elementType item from ELEMENT_TYPES
   * @param {number} minTagConfidence min confidence
   * @param {string} elementAverage element Average
   * @returns {CompareReportV2RequestBuilder} for nested call
   */
  withElementBy(
    elementType,
    minTagConfidence,
    elementAverage = ELEMENT_AVERAGE.element,
  ) {
    if (ELEMENT_TYPES[elementType]) {
      this.elementBy = {
        type: ELEMENT_TYPES[elementType],
        average: ELEMENT_AVERAGE[elementAverage]
          ? ELEMENT_AVERAGE[elementAverage]
          : ELEMENT_AVERAGE.element,
        minTagConfidence: minTagConfidence || DEFAULT_MIN_TAG_CONFIDENCE_LEVEL,
        minTagConfidences: {
          defaultTo: minTagConfidence,
          [TAG_TYPES.CELEBRITY_NAME]:
            getCelebrityTagConfidence(minTagConfidence),
        },
      };
      if (
        ELEMENT_TYPES[elementType] === ELEMENT_TYPES.tag ||
        ELEMENT_TYPES[elementType] === ELEMENT_TYPES.tagAndType
      ) {
        this.withDimensions(['$sum(media_tag[vrs])']);
      }
    }

    return this;
  }

  withElement(element) {
    if (element) {
      if (element.title) {
        this.element = { value: element.title, tagType: element.tagType };
      }

      if (element.duration) {
        this.element = { duration: element.duration };
      }
    }

    return this;
  }

  /**
   * build group by
   *
   * @param {string} reportType one of REQUEST_TYPES
   * @param {{[values]: [{}], [groupType]: string, [groupByValues]: [{}]}} option values for report type
   * @returns {CompareReportV2RequestBuilder} for nested calls
   */

  withReportType = (reportType, option) => {
    let values;

    const handleDimensionGroupBy = () => {
      values = [option.groupType];
      if (values?.length === 1) {
        this.groupBy = {
          type: GROUP_BYS.dimensionGroupBy,
          values,
          reportType,
        };
      }
    };

    const handleFilterGroupBy = () => {
      values = option.groupByValues.map((item) => {
        // fix for VID-13031: [HLA] image columns in duration report don't calculate lift against report media type filters
        if (option.isImageColumn) {
          item.filter[QUERY_OPTIONS.ANALYTICS_MEDIA_FILE_TYPE] = [
            MediaType.IMAGE,
          ];
        }
        return {
          ...item,
          filter: this.platformAnalytics.formatFilters(item.filter),
          reportType,
        };
      });
      if (values?.length === 1) {
        this.groupBy = {
          type: GROUP_BYS.filter,
          values,
          reportType,
        };
      }
    };

    const handleSponsorDisplayAds = () => {
      if (option.isSponsoredAdFilterActive) {
        this.groupBy = {
          type: GROUP_BYS.filter,
          values: this.platformAnalytics.getSponsoredDisplayFormat(),
          reportType,
        };
      }
    };

    const handleDefaultGroupBy = (groupByType, values) => {
      this.groupBy = {
        type: groupByType,
        values,
        reportType,
      };
    };

    switch (reportType) {
      case REQUEST_TYPES.adType:
        handleSponsorDisplayAds();
        break;
      case REQUEST_TYPES.creative:
        handleDefaultGroupBy(GROUP_BYS.creative);
        break;
      case REQUEST_TYPES.creativeGrouping:
        values = option.groupByValues.filter(
          (item) =>
            item.title && item.platformMediaIds && item.platformMediaIds.length,
        );
        if (values.length) {
          handleDefaultGroupBy(GROUP_BYS.creativeGrouping, values);
        }

        break;
      case REQUEST_TYPES.format:
        handleDefaultGroupBy(
          GROUP_BYS.ratioFormat,
          this.platformAnalytics.getFormat(),
        );
        break;
      case REQUEST_TYPES.audience:
      case REQUEST_TYPES.campaign:
      case REQUEST_TYPES.brand:
      case REQUEST_TYPES.market:
      case REQUEST_TYPES.elementPresence:
      case REQUEST_TYPES.duration:
      case REQUEST_TYPES.vidMobPerformance:
        handleFilterGroupBy();
        break;
      case REQUEST_TYPES.objective:
      case REQUEST_TYPES.placement:
        handleDimensionGroupBy();
        break;
      default:
    }

    return this;
  };

  // build averageBy
  withAccountAverage = (includeAccountAverage = true, isLifetime = false) => {
    if (includeAccountAverage) {
      this.averageBy = {
        type: isLifetime ? AVERAGE_BYS_TYPE.lifetime : AVERAGE_BYS_TYPE.custom,
      };
    }

    return this;
  };

  // ===========
  // options
  withVidmobCreative = (createdByVidmob) => {
    this.createdByVidmob = createdByVidmob;
    return this;
  };

  withPerformanceBreakdown = (includePerformanceBreakdown) => {
    this.includeTracker = includePerformanceBreakdown;
    return this;
  };

  withMissingGroups = (includeMissingGroups) => {
    this.includeMissingGroups = includeMissingGroups;
    return this;
  };

  withMergedStats = (includeMergedStats) => {
    this.includeMergedStats = includeMergedStats;
    return this;
  };

  // ===========
  // kpiOptions
  withKpi = (kpi, isDefaultKPITimeRangeActive, currency) => {
    const { tagTimeRange, tagTimeRangeType } = kpi || {};

    const isTimeConstraintKpiSelected =
      Boolean(tagTimeRange) || tagTimeRange === 0;

    const customTagTimeRangeForTimeConstraintKpi = () => ({
      value: tagTimeRange?.value || tagTimeRange,
      type: TIME_BASED[tagTimeRangeType.toLowerCase()],
    });

    // defaultKPITimeRange is First 3 Seconds, otherwise it is 100% of the creative
    const customTagTimeRangeForNonTimeConstraintKpi = () => ({
      value: isDefaultKPITimeRangeActive ? 3 : 1,
      type: isDefaultKPITimeRangeActive
        ? TIME_BASED.duration
        : TIME_BASED.percentage,
    });

    this.kpiOptions = {
      kpiIds: [kpi.id],
      customTagTimeRange: isTimeConstraintKpiSelected
        ? customTagTimeRangeForTimeConstraintKpi()
        : customTagTimeRangeForNonTimeConstraintKpi(),
    };

    if (
      currency &&
      (kpi?.format?.name === KPI_FORMATS.SPEND.toLowerCase() ||
        kpi?.id === NOT_IN_USE_KPI)
    ) {
      this.kpiOptions.currency = currency;
    }

    return this;
  };

  withTagTimeRange = (tagTypeRange) => {
    this.kpiOptions = {
      tagTimeRange: {
        value: tagTypeRange.value,
        type: tagTypeRange.type,
      },
    };
    return this;
  };

  // ===========
  // filter
  withSparkAdsFilterActive = (isSparkAdsFilterActive) => {
    if (this.platformAnalytics.supportSparkAdsFilter()) {
      this.filter = {
        ...this.filter,
        ...this.platformAnalytics.getRequestFilterModifierToAddSparkAds(
          isSparkAdsFilterActive,
        ),
      };
    }

    return this;
  };

  withSponsoredDisplayFilterActive = (isSponsoredDisplayActive) => {
    if (
      isSponsoredDisplayActive &&
      this.platformAnalytics.supportSponsoredAdsFilter()
    ) {
      this.filter = {
        ...this.filter,
        ...this.platformAnalytics.getRequestFilterModifierToAddSponsoredDisplayAds(),
      };
    }

    return this;
  };

  withAdSetFilterActive(selectedAdSets, reportType) {
    if (reportType === 'campaign') {
      return this;
    }

    if (this.platformAnalytics.supportAdSetFilter() && selectedAdSets?.length) {
      const adSetIds = selectedAdSets.map((adSet) => adSet.id);
      this.filter = {
        ...this.filter,
        ...this.platformAnalytics.getRequestFilterModifierToAddAdSetSearch(
          adSetIds,
        ),
      };
    }

    return this;
  }

  withFilter = (filter) => {
    this.filter = { ...this.filter, ...filter };
    return this;
  };

  withCustomFilters(customFilters) {
    if (customFilters && !isNil(customFilters)) {
      const filterSelections =
        this.platformAnalytics.getCustomFiltersModalDescription();
      Object.keys(customFilters).forEach((filterIdentifier) => {
        const filterOptions = filterSelections.find(
          (i) => i.identifier === filterIdentifier,
        ).options;
        if (customFilters[filterIdentifier]) {
          Object.keys(customFilters[filterIdentifier]).forEach(
            (selectedOptionIdentifier) => {
              const selectedOption = filterOptions.find(
                (i) => i.identifier === selectedOptionIdentifier,
              );
              // if the option is a filter type
              if (selectedOption.filter) {
                Object.keys(selectedOption.filter).forEach(
                  (filterDimension) => {
                    if (
                      customFilters[filterIdentifier][selectedOptionIdentifier]
                    ) {
                      if (!this.filter[filterDimension]) {
                        this.filter[filterDimension] = [];
                      }

                      this.filter[filterDimension] = [
                        ...this.filter[filterDimension],
                        ...selectedOption.filter[filterDimension],
                      ];
                    }
                  },
                );
              }

              // can add other option types
            },
          );
        }
      });
    }

    return this;
  }

  withImpressionsFilter(min, max) {
    if (!min && !max) {
      return this;
    }

    const impressionFilterValues = [];
    if (min) {
      impressionFilterValues.push(
        `$metric(${this.platformAnalytics.getImpressionMetricKey()}, gte, ${min})`,
      );
    }

    if (max) {
      impressionFilterValues.push(
        `$metric(${this.platformAnalytics.getImpressionMetricKey()}, lte, ${max})`,
      );
    }

    const adDimension = this.platformAnalytics.getAdDimensions();

    if (Array.isArray(adDimension)) {
      adDimension.forEach((dimension) => {
        if (!this.filter[dimension]) {
          this.filter[dimension] = [];
        }

        this.filter[dimension] = [
          ...this.filter[dimension],
          ...impressionFilterValues,
        ];
      });
    } else {
      this.filter[adDimension] = impressionFilterValues;
    }

    return this;
  }

  // ===========
  // filetype
  withFileTypes = (fileTypes) => {
    if (!Array.isArray(fileTypes)) {
      throw new Error('Invalid file types');
    }

    let fileTypesToFilter = fileTypes;

    // we allow the user to de-select all media types in the panel, which indicates that they want all media types
    if (!fileTypes.length) {
      fileTypesToFilter = this.platformAnalytics.supportedMediaTypeFilters;
    }

    this.fileTypes = [...new Set(fileTypesToFilter)].filter((i) => {
      if (!isString(i)) {
        return false;
      }

      return FILE_TYPES[i.toLowerCase()];
    });
    return this;
  };

  // ===========
  // dimensions and metrics
  withMetrics(metrics) {
    this.metrics = [...metrics];
    return this;
  }

  withDimensions(dimensions) {
    const aggableDim = dimensions.map((dim) => {
      if (dim.startsWith('$')) {
        return dim;
      }

      return `$count(${dim})`;
    });

    if (this.dimensions) {
      this.dimensions = [...aggableDim, ...this.dimensions];
    } else {
      this.dimensions = aggableDim;
    }

    return this;
  }
}
