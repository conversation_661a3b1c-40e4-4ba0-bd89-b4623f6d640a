import React, { useMemo, useState } from 'react';
import <PERSON><PERSON>eaderV2 from '../../../../components/PageHeaderV2';
import { useIntl } from 'react-intl';
import {
  CREATE_EXPORT_TITLE_INTL,
  DATA_EXPORTS_CREATE_PAGE_CREATE_ERROR_INTL,
  DATA_EXPORTS_CREATE_PAGE_DATA_FILTERS_INTL,
  DATA_EXPORTS_CREATE_PAGE_DATA_FILTERS_TOOLTIP_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_DETAILS_INTL,
  DATA_EXPORTS_CREATE_PAGE_GET_BRANDS_ERROR_INTL,
  DATA_EXPORTS_CREATE_PAGE_GET_MARKETS_ERROR_INTL,
  DATA_EXPORTS_CREATE_PAGE_MAIN_CONTENT_MARGIN,
  DATA_EXPORTS_CREATE_PAGE_NAME_DEFAULT_TEXT_INTL,
  DATA_EXPORTS_CREATE_PAGE_SECTION_SPACING,
  DATA_EXPORTS_EXPORT_TYPES,
  DATA_EXPORTS_NAME_DEFAULTS,
  DATA_EXPORTS_SCORECARD_IDENTIFIERS,
  DATA_EXPORTS_SCORECARD_OPTIONS,
} from './createExportConstants';
import {
  VidMobBox,
  VidMobButton,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import {
  DATA_EXPORTS_CREATE_PAGE_CANCEL_BUTTON_INTL,
  DATA_EXPORTS_CREATE_PAGE_CREATE_BUTTON_INTL,
} from './createExportConstants';
import {
  AdAccountsFormItem,
  BrandsFormItem,
  ChannelsFormItem,
  ExportDateRangeFormItem,
  ExportNameFormItem,
  ExportTypeFormItem,
  ExportTypeInfoBox,
  MarketsFormItem,
  ScorecardTypeFormItem,
  WorkspacesFormItem,
} from './createExportFormItems';
import dayjs from 'dayjs';
import { getUserWorkspacesByOrganization } from '../../../../redux/selectors/user.selectors';
import { useSelector } from 'react-redux';
import { Market } from '../dataExportsTypes';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import BffDataExportsService from '../../../../apiServices/BffDataExportsService';
import { getOrganizationId } from '../../../../redux/selectors/partner.selectors';
import {
  DATA_EXPORTS_CREATION_SUCCESS_TOAST_INTL,
  DATA_EXPORTS_PLATFORMS,
  DATA_EXPORTS_QUERY,
} from '../DataExportsLandingPage/dataExportsLandingPageConstants';
import { InfoFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import { IdAndName } from '../../../../types/common.types';
import { useToastAlert } from '../../../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { useGetBrands } from '../../../../hooks/useGetBrands';
import { useGetMarkets } from '../../../../hooks/useGetMarkets';
import { SelectableDataGridRow } from '../../../../components/ReportFilters/components/SelectableDataGrid/types';
import ReportDataLimitationBanner from '../../../../creativeScoring/components/reports/reportsSharedComponents/ReportDataLimitationBanner/ReportDataLimitationBanner';

const { createDataExport } = BffDataExportsService;

interface CreateExportPageProps {
  setIsCreatePageVisible: (visible: boolean) => void;
  refetch: () => void;
}

export const CreateExportPage = ({
  setIsCreatePageVisible,
  refetch,
}: CreateExportPageProps) => {
  const intl = useIntl();

  const showToastAlert = useToastAlert();

  const today = dayjs().toISOString();
  const oneYearAgo = dayjs().subtract(1, 'year').toISOString();

  const [selectedExportType, setSelectedExportType] = useState(
    DATA_EXPORTS_EXPORT_TYPES.CREATIVE_SCORING,
  );
  const [selectedScorecards, setSelectedScorecards] = useState<IdAndName[]>([
    DATA_EXPORTS_SCORECARD_OPTIONS[0],
  ]);
  const [exportName, setExportName] = useState(
    `${DATA_EXPORTS_NAME_DEFAULTS[selectedExportType]} ${intl.messages[DATA_EXPORTS_CREATE_PAGE_NAME_DEFAULT_TEXT_INTL]}`,
  );
  const [selectedDateRange, setSelectedDateRange] = useState([
    oneYearAgo,
    today,
  ]);
  const [selectedWorkspaces, setSelectedWorkspaces] = useState<IdAndName[]>([]);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<IdAndName[]>([]);
  const [selectedMarkets, setSelectedMarkets] = useState<IdAndName[]>([]);
  const [selectedAdAccounts, setSelectedAdAccounts] = useState<
    SelectableDataGridRow[]
  >([]);
  const [nameInputInErrorState, setNameInputInErrorState] = useState(false);

  const organizationId: string = useSelector(getOrganizationId);
  const workspacesList = useSelector(getUserWorkspacesByOrganization);
  const workspacesListWithStringIds = useMemo(
    () =>
      workspacesList.map((workspace: IdAndName) => ({
        ...workspace,
        id: workspace.id.toString(),
      })),
    [workspacesList],
  );

  const adAccountsDropdownIsDisabled = !selectedWorkspaces.length;

  const { data: brandsList = [], error: brandsError } = useGetBrands();
  if (brandsError) {
    showToastAlert(DATA_EXPORTS_CREATE_PAGE_GET_BRANDS_ERROR_INTL, 'error');
  }
  const brandsDropdownIsDisabled = brandsList.length === 0;

  const { data: marketsResult = [], error: marketsError } = useGetMarkets();
  if (marketsError) {
    showToastAlert(DATA_EXPORTS_CREATE_PAGE_GET_MARKETS_ERROR_INTL, 'error');
  }
  const allCountries = marketsResult.reduce(
    (
      acc: Market[],
      region: { region: string; regionId: number; countries: Market[] },
    ) => acc.concat(region.countries),
    [],
  );
  const marketsList = allCountries.sort((a: Market, b: Market) => {
    a.name.localeCompare(b.name);
  });
  const marketsDropdownIsDisabled = marketsList.length === 0;

  const channelsList = useMemo(
    () =>
      DATA_EXPORTS_PLATFORMS.map((channel) => ({
        id: channel.id,
        name: intl.messages[channel.i18nName] as string,
      })),
    [],
  );

  const filteredChannelsList = useMemo(() => {
    if (selectedChannels.length === 0) {
      return channelsList;
    }
    return channelsList.filter((channel) =>
      selectedChannels.includes(channel.id),
    );
  }, [channelsList, selectedChannels]);

  const queryClient = useQueryClient();

  const createDataExportMutation = useMutation({
    mutationFn: createDataExport,
    onSuccess: () => {
      queryClient.invalidateQueries([DATA_EXPORTS_QUERY, organizationId]);
      refetch();
      setIsCreatePageVisible(false);
      showToastAlert(
        DATA_EXPORTS_CREATION_SUCCESS_TOAST_INTL,
        'info',
        undefined,
        5000,
        true,
      );
    },
    onError: () => {
      showToastAlert(DATA_EXPORTS_CREATE_PAGE_CREATE_ERROR_INTL, 'error');
    },
  });

  const isCreateButtonDisabled =
    !exportName.length || !selectedScorecards.length || nameInputInErrorState;

  const handleBreadcrumbClick = () => {
    goBackToLandingPage();
  };

  const handleCreateButtonClick = () => {
    const dataToSend = {
      reportType: selectedExportType,
      reportName: exportName,
      startDate: selectedDateRange[0],
      endDate: selectedDateRange[1],
      workspaces: selectedWorkspaces.map((workspace) => parseInt(workspace.id)),
      channels: selectedChannels,
      brands: selectedBrands.map((brand) => brand.id),
      markets: selectedMarkets.map((market) => market.id),
      adAccounts: selectedAdAccounts.map((adAccount) => adAccount.id),
    };
    createDataExportMutation.mutate({
      organizationId,
      exportData: dataToSend,
    });
  };

  const handleCancelButtonClick = () => {
    goBackToLandingPage();
  };

  const goBackToLandingPage = () => {
    setIsCreatePageVisible(false);
  };

  const cancelButton = (
    <VidMobButton
      onClick={handleCancelButtonClick}
      key="create-export-page-cancel-button"
    >
      <VidMobTypography>
        {intl.messages[DATA_EXPORTS_CREATE_PAGE_CANCEL_BUTTON_INTL] as string}
      </VidMobTypography>
    </VidMobButton>
  );

  const createButton = (
    <VidMobButton
      variant="contained"
      color="primary"
      onClick={handleCreateButtonClick}
      key="create-export-page-create-button"
      disabled={isCreateButtonDisabled}
    >
      <VidMobTypography>
        {intl.messages[DATA_EXPORTS_CREATE_PAGE_CREATE_BUTTON_INTL] as string}
      </VidMobTypography>
    </VidMobButton>
  );

  const formSX = {
    margin: DATA_EXPORTS_CREATE_PAGE_MAIN_CONTENT_MARGIN,
    height: 'calc(100vh - 150px)',
    overflowY: 'auto',
    paddingBottom: '240px',
  };

  const isScorecardTypeDropdownVisible =
    selectedExportType === DATA_EXPORTS_EXPORT_TYPES.CREATIVE_SCORING;

  const isInFlightSelected = selectedScorecards.some(
    (item) => item.id === DATA_EXPORTS_SCORECARD_IDENTIFIERS.INFLIGHT,
  );

  // This ensures that when the export type is changed, the default name is updated for the new export type.
  // If the user had changed the name, it will not be updated.
  const handleExportTypeChange = (
    newExportType: React.SetStateAction<string>,
  ) => {
    const getNewExportType = (prevType: string) => {
      if (typeof newExportType === 'function') {
        return newExportType(prevType);
      }
      return newExportType;
    };

    setSelectedExportType((prevType) => {
      const updatedType = getNewExportType(prevType);

      // Calculate the default name for the new type and previous type
      const defaultName = `${DATA_EXPORTS_NAME_DEFAULTS[updatedType]} ${intl.messages[DATA_EXPORTS_CREATE_PAGE_NAME_DEFAULT_TEXT_INTL]}`;
      const prevDefaultName = `${DATA_EXPORTS_NAME_DEFAULTS[prevType]} ${intl.messages[DATA_EXPORTS_CREATE_PAGE_NAME_DEFAULT_TEXT_INTL]}`;

      // Only update the name if it matches the previous default pattern
      if (exportName === prevDefaultName) {
        setExportName(defaultName);
      }
      return updatedType;
    });
  };

  const formContent = (
    <VidMobStack sx={formSX}>
      <VidMobTypography variant="subtitle1">
        {intl.messages[DATA_EXPORTS_CREATE_PAGE_EXPORT_DETAILS_INTL] as string}
      </VidMobTypography>
      <ExportTypeFormItem
        selectedExportType={selectedExportType}
        setSelectedExportType={handleExportTypeChange}
      />
      {selectedExportType && (
        <ExportTypeInfoBox selectedExportType={selectedExportType} />
      )}
      {isScorecardTypeDropdownVisible && (
        <>
          <ScorecardTypeFormItem
            selectedScorecards={selectedScorecards}
            setSelectedScorecards={setSelectedScorecards}
            scorecardsList={DATA_EXPORTS_SCORECARD_OPTIONS}
          />
          <VidMobBox sx={{ marginRight: '24px' }}>
            <ReportDataLimitationBanner showBanner={isInFlightSelected} />
          </VidMobBox>
        </>
      )}
      <ExportNameFormItem
        exportName={exportName}
        setExportName={setExportName}
        setNameInputInErrorState={setNameInputInErrorState}
      />
      <VidMobStack direction="row">
        <VidMobTypography
          variant="subtitle1"
          sx={{ marginTop: DATA_EXPORTS_CREATE_PAGE_SECTION_SPACING }}
        >
          {intl.messages[DATA_EXPORTS_CREATE_PAGE_DATA_FILTERS_INTL] as string}
        </VidMobTypography>
        <VidMobTooltip
          title={
            intl.messages[
              DATA_EXPORTS_CREATE_PAGE_DATA_FILTERS_TOOLTIP_INTL
            ] as string
          }
        >
          <InfoFilledIcon
            sx={{ width: '16px', color: 'text.secondary', marginTop: '32px' }}
          />
        </VidMobTooltip>
      </VidMobStack>
      {selectedExportType ===
      DATA_EXPORTS_EXPORT_TYPES.CREATIVE_ELEMENTS ? null : (
        <ExportDateRangeFormItem
          selectedDateRange={selectedDateRange}
          setSelectedDateRange={setSelectedDateRange}
        />
      )}
      <WorkspacesFormItem
        selectedWorkspaces={selectedWorkspaces}
        setSelectedWorkspaces={setSelectedWorkspaces}
        workspacesList={workspacesListWithStringIds}
      />
      <ChannelsFormItem
        selectedChannels={selectedChannels}
        setSelectedChannels={setSelectedChannels}
        channelsList={channelsList}
      />
      <BrandsFormItem
        isDisabled={brandsDropdownIsDisabled}
        selectedBrands={selectedBrands}
        setSelectedBrands={setSelectedBrands}
        brandsList={brandsList}
      />
      <MarketsFormItem
        isDisabled={marketsDropdownIsDisabled}
        selectedMarkets={selectedMarkets}
        setSelectedMarkets={setSelectedMarkets}
        marketsList={marketsList}
      />
      <AdAccountsFormItem
        channelsList={filteredChannelsList}
        isDisabled={adAccountsDropdownIsDisabled}
        selectedAdAccounts={selectedAdAccounts}
        setSelectedAdAccounts={setSelectedAdAccounts}
        selectedWorkspaces={selectedWorkspaces}
      />
    </VidMobStack>
  );

  return (
    <VidMobBox>
      <PageHeaderV2
        title={intl.messages[CREATE_EXPORT_TITLE_INTL]}
        breadcrumbActionOverride={handleBreadcrumbClick}
        breadcrumbs={[{ label: 'Data exports', url: '/data-exports' }]}
      >
        {[cancelButton, createButton]}
      </PageHeaderV2>
      {formContent}
    </VidMobBox>
  );
};
