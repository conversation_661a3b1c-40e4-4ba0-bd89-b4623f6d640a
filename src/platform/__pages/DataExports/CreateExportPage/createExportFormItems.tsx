import {
  DATA_EXPORTS_CREATE_PAGE_DATE_RANGE_INTL,
  DATA_EXPORTS_CREATE_PAGE_DATE_RANGE_TOOLTIP_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_NAME_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_NAME_SUBTITLE_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_OPTION_CREATIVE_SCORING_ALERT_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_OPTION_CREATIVE_ELEMENTS_ALERT_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_OPTION_CREATIVE_ANALYTICS_ALERT_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_TOOLTIP_CONTENT_INTL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_TOOLTIP_LEARN_MORE_URL,
  DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_TOOLTIP_TITLE_INTL,
  DATA_EXPORTS_CREATE_PAGE_ITEM_WIDTH,
  DATA_EXPORTS_CREATE_PAGE_SCORECARD_TYPE_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_AD_ACCOUNTS_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_AD_ACCOUNTS_TOOLTIP_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_BRANDS_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_BRANDS_TOOLTIP_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_CHANNELS_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_CHANNELS_TOOLTIP_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_MARKETS_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_MARKETS_TOOLTIP_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_WORKSPACES_INTL,
  DATA_EXPORTS_CREATE_PAGE_SELECT_WORKSPACES_TOOLTIP_INTL,
  DATA_EXPORTS_EXPORT_TYPES,
} from './createExportConstants';
import React from 'react';
import {
  AdAccountsDropdown,
  ChannelsDropdown,
  DateRangeDropdown,
  ExportNameDropdown,
  ExportTypeDropdown,
  MarketsDropdown,
  WorkspacesDropdown,
  BrandsDropdown,
} from './CreateExportDropdowns';
import { useIntl } from 'react-intl';
import { Alert } from '@mui/material';
import { FormItemVariants, Market } from '../dataExportsTypes';
import { IdAndName } from '../../../../types/common.types';
import { ScorecardTypeDropdown } from './CreateExportDropdowns/ScorecardTypeDropdown';
import { SelectableDataGridRow } from '../../../../components/ReportFilters/components/SelectableDataGrid/types';
import { useRenderFormItem } from './CreateExportDropdowns/useRenderFormItem';

const {
  FormItem,
  FormItemWithCustomTooltip,
  FormItemWithSubtitle,
  FormItemWithTooltip,
} = FormItemVariants;

interface ExportTypeFormItemProps {
  isDisabled?: boolean;
  selectedExportType: string;
  setSelectedExportType: React.Dispatch<React.SetStateAction<string>>;
}

export const ExportTypeFormItem = ({
  isDisabled,
  selectedExportType,
  setSelectedExportType,
}: ExportTypeFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithCustomTooltip,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_INTL,
    tooltipIntlKey: DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_TOOLTIP_TITLE_INTL,
    tooltipContentIntlKey:
      DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_TOOLTIP_CONTENT_INTL,
    learnMoreUrl: DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_TOOLTIP_LEARN_MORE_URL,
    dropdownComponent: (
      <ExportTypeDropdown
        isDisabled={isDisabled}
        selectedExportType={selectedExportType}
        setSelectedExportType={setSelectedExportType}
      />
    ),
  });
};

interface ScorecardTypeFormItemProps {
  isDisabled?: boolean;
  selectedScorecards: IdAndName[] | never;
  setSelectedScorecards: React.Dispatch<React.SetStateAction<IdAndName[]>>;
  scorecardsList: IdAndName[] | never;
}

export const ScorecardTypeFormItem = ({
  isDisabled = false,
  selectedScorecards,
  setSelectedScorecards,
  scorecardsList,
}: ScorecardTypeFormItemProps) => {
  return useRenderFormItem({
    variant: FormItem,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_SCORECARD_TYPE_INTL,
    dropdownComponent: (
      <ScorecardTypeDropdown
        isDisabled={isDisabled}
        selectedScorecards={selectedScorecards}
        setSelectedScorecards={setSelectedScorecards}
        scorecardsList={scorecardsList}
      />
    ),
  });
};

interface ExportTypeInfoBoxProps {
  selectedExportType: string;
}

export const ExportTypeInfoBox = ({
  selectedExportType,
}: ExportTypeInfoBoxProps) => {
  const intl = useIntl();
  const { CREATIVE_SCORING, CREATIVE_ELEMENTS, CREATIVE_ANALYTICS } =
    DATA_EXPORTS_EXPORT_TYPES;
  let alertText;

  switch (selectedExportType) {
    case CREATIVE_SCORING:
      alertText = intl.messages[
        DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_OPTION_CREATIVE_SCORING_ALERT_INTL
      ] as string;
      break;
    case CREATIVE_ELEMENTS:
      alertText = intl.messages[
        DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_OPTION_CREATIVE_ELEMENTS_ALERT_INTL
      ] as string;
      break;
    case CREATIVE_ANALYTICS:
      alertText = intl.messages[
        DATA_EXPORTS_CREATE_PAGE_EXPORT_TYPE_OPTION_CREATIVE_ANALYTICS_ALERT_INTL
      ] as string;
      break;
    default:
      alertText = '';
  }

  return (
    <Alert
      severity="info"
      sx={{
        width: DATA_EXPORTS_CREATE_PAGE_ITEM_WIDTH,
      }}
    >
      {alertText}
    </Alert>
  );
};

interface ExportNameFormItemProps {
  exportName: string;
  setExportName: React.Dispatch<React.SetStateAction<string>>;
  setNameInputInErrorState: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ExportNameFormItem = ({
  exportName,
  setExportName,
  setNameInputInErrorState,
}: ExportNameFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithSubtitle,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_EXPORT_NAME_INTL,
    subtitleIntlKey: DATA_EXPORTS_CREATE_PAGE_EXPORT_NAME_SUBTITLE_INTL,
    dropdownComponent: (
      <ExportNameDropdown
        exportName={exportName}
        setExportName={setExportName}
        setNameInputInErrorState={setNameInputInErrorState}
      />
    ),
  });
};

interface ExportDateRangeFormItemProps {
  selectedDateRange: string[];
  setSelectedDateRange: React.Dispatch<React.SetStateAction<string[]>>;
}

export const ExportDateRangeFormItem = ({
  selectedDateRange,
  setSelectedDateRange,
}: ExportDateRangeFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithTooltip,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_DATE_RANGE_INTL,
    tooltipIntlKey: DATA_EXPORTS_CREATE_PAGE_DATE_RANGE_TOOLTIP_INTL,
    dropdownComponent: (
      <DateRangeDropdown
        selectedDateRange={selectedDateRange}
        setSelectedDateRange={setSelectedDateRange}
      />
    ),
  });
};

interface WorkspacesFormItemProps {
  selectedWorkspaces: IdAndName[] | never;
  setSelectedWorkspaces: React.Dispatch<
    React.SetStateAction<IdAndName[] | never>
  >;
  workspacesList: IdAndName[] | never;
}

export const WorkspacesFormItem = ({
  selectedWorkspaces,
  setSelectedWorkspaces,
  workspacesList,
}: WorkspacesFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithTooltip,
    isOptional: true,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_WORKSPACES_INTL,
    tooltipIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_WORKSPACES_TOOLTIP_INTL,
    dropdownComponent: (
      <WorkspacesDropdown
        selectedWorkspaces={selectedWorkspaces}
        setSelectedWorkspaces={setSelectedWorkspaces}
        workspacesList={workspacesList}
      />
    ),
  });
};

interface ChannelsFormItemProps {
  selectedChannels: string[] | never;
  setSelectedChannels: React.Dispatch<React.SetStateAction<string[]>>;
  channelsList: IdAndName[] | never;
}

export const ChannelsFormItem = ({
  selectedChannels,
  setSelectedChannels,
  channelsList,
}: ChannelsFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithTooltip,
    isOptional: true,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_CHANNELS_INTL,
    tooltipIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_CHANNELS_TOOLTIP_INTL,
    dropdownComponent: (
      <ChannelsDropdown
        selectedChannels={selectedChannels}
        setSelectedChannels={setSelectedChannels}
        channelsList={channelsList}
      />
    ),
  });
};

interface BrandsFormItemProps {
  isDisabled: boolean;
  selectedBrands: IdAndName[] | never;
  setSelectedBrands: React.Dispatch<React.SetStateAction<IdAndName[]>>;
  brandsList: IdAndName[] | never;
}

export const BrandsFormItem = ({
  isDisabled,
  selectedBrands,
  setSelectedBrands,
  brandsList,
}: BrandsFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithTooltip,
    isOptional: true,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_BRANDS_INTL,
    tooltipIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_BRANDS_TOOLTIP_INTL,
    dropdownComponent: (
      <BrandsDropdown
        isDisabled={isDisabled}
        selectedBrands={selectedBrands}
        setSelectedBrands={setSelectedBrands}
        brandsList={brandsList}
      />
    ),
  });
};

interface MarketsFormItemProps {
  isDisabled: boolean;
  selectedMarkets: IdAndName[] | never;
  setSelectedMarkets: React.Dispatch<React.SetStateAction<IdAndName[]>>;
  marketsList: Market[] | never;
}

export const MarketsFormItem = ({
  isDisabled,
  selectedMarkets,
  setSelectedMarkets,
  marketsList,
}: MarketsFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithTooltip,
    isOptional: true,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_MARKETS_INTL,
    tooltipIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_MARKETS_TOOLTIP_INTL,
    dropdownComponent: (
      <MarketsDropdown
        isDisabled={isDisabled}
        selectedMarkets={selectedMarkets}
        setSelectedMarkets={setSelectedMarkets}
        marketsList={marketsList}
      />
    ),
  });
};

interface AdAccountsFormItemProps {
  isDisabled: boolean;
  channelsList: IdAndName[] | never;
  selectedAdAccounts: SelectableDataGridRow[] | never;
  setSelectedAdAccounts: React.Dispatch<
    React.SetStateAction<SelectableDataGridRow[]>
  >;
  selectedWorkspaces: IdAndName[] | never;
}

export const AdAccountsFormItem = ({
  isDisabled,
  channelsList,
  selectedAdAccounts,
  setSelectedAdAccounts,
  selectedWorkspaces,
}: AdAccountsFormItemProps) => {
  return useRenderFormItem({
    variant: FormItemWithTooltip,
    isOptional: true,
    titleIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_AD_ACCOUNTS_INTL,
    tooltipIntlKey: DATA_EXPORTS_CREATE_PAGE_SELECT_AD_ACCOUNTS_TOOLTIP_INTL,
    dropdownComponent: (
      <AdAccountsDropdown
        isDisabled={isDisabled}
        channelsList={channelsList}
        selectedAdAccounts={selectedAdAccounts}
        setSelectedAdAccounts={setSelectedAdAccounts}
        selectedWorkspaces={selectedWorkspaces}
      />
    ),
  });
};
