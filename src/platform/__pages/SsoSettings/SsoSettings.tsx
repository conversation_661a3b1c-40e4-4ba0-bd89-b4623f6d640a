import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import PageHeaderV2 from '../../../components/PageHeaderV2';
import { SsoSettingsContent } from './SsoSettingsContent/SsoSettingsContent';
import {
  SSO_LANDING_PAGE_SUBTITLE,
  SSO_LANDING_PAGE_TITLE,
  SSO_SETTINGS_DEFAULT_PAGE_SIZE,
} from './ssoSettingsConstants';
import { GridPaginationModel } from '@mui/x-data-grid-pro';
import { SsoSettingsControlBar } from './SsoSettingsControlBar/SsoSettingsControlBar';
import { useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { getOrganizationPermissions } from '../../../userManagement/redux/selectors/organization.selectors';
import { ORGANIZATION_PERMISSIONS } from '../../../constants/organization.constants';
import { SsoSettingsDeleteModal } from './SsoSettingsDeleteModal/SsoSettingsDeleteModal';
import { SsoSettingsItem } from './ssoSettingsTypes';
import { SsoSettingsCreateModal } from './SsoSettingsCreateModal/SsoSettingsCreateModal';
import { SsoSettingsCreateButton } from './SsoSettingsCreateButton';
import { withOrganizationPermissions } from '../../../components/__providers/withOrganizationPermissions';
import { SsoSettingsUpdateModal } from './SsoSettingsUpdateModal/SsoSettingsUpdateModal';
import { VidMobTypography } from '../../../vidMobComponentWrappers';

const { READ_SSO_SETTINGS } = ORGANIZATION_PERMISSIONS;

const SsoSettings = () => {
  const intl = useIntl();
  const history = useHistory();

  const orgPermissions = useSelector(getOrganizationPermissions);
  const canReadSsoSettings =
    orgPermissions?.permissionsKeySet?.has(READ_SSO_SETTINGS);

  const [isCreateButtonDisabled, setIsCreateButtonDisabled] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [ssoSettingsCount, setSsoSettingsCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: SSO_SETTINGS_DEFAULT_PAGE_SIZE,
  });
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [ssoSettingsToDelete, setSsoSettingToDelete] = useState({
    domain: '',
    id: '',
  });
  const [isUpdateModalVisible, setIsUpdateModalVisible] = useState(false);
  const [ssoSettingsToUpdate, setSsoSettingsToUpdate] = useState({
    domain: '',
    id: '',
  });

  if (!canReadSsoSettings) {
    history.push('/');
  }

  const handlePageChange = (newPaginationModel: GridPaginationModel) => {
    setPaginationModel(newPaginationModel);
  };

  const handleCreateButtonClick = () => {
    setIsCreateModalOpen(true);
  };

  const handleActionMenuOptionClick = (row: SsoSettingsItem) => {
    setSsoSettingsToUpdate(row);
    setIsUpdateModalVisible(true);
  };

  const handleDeleteMenuOptionClick = (row: SsoSettingsItem) => {
    setSsoSettingToDelete(row);
    setIsDeleteModalVisible(true);
  };

  return (
    <>
      <PageHeaderV2
        title={intl.messages[SSO_LANDING_PAGE_TITLE]}
        customSubtitleComponent={
          <VidMobTypography variant="body2" color="text.secondary">
            {intl.messages[SSO_LANDING_PAGE_SUBTITLE] as string}
          </VidMobTypography>
        }
      >
        <SsoSettingsCreateButton
          isDisabled={isCreateButtonDisabled}
          handleClick={handleCreateButtonClick}
        />
      </PageHeaderV2>
      <SsoSettingsControlBar
        ssoSettingsCount={ssoSettingsCount}
        setSearchTerm={setSearchTerm}
      />
      <SsoSettingsContent
        searchTerm={searchTerm}
        paginationModel={paginationModel}
        setSsoSettingsCount={setSsoSettingsCount}
        setIsCreateModalVisible={setIsCreateModalOpen}
        setIsCreateButtonDisabled={setIsCreateButtonDisabled}
        handleActionButtonClick={handleActionMenuOptionClick}
        handleDeleteButtonClick={handleDeleteMenuOptionClick}
        handlePageChange={handlePageChange}
      />
      <SsoSettingsCreateModal
        isModalOpen={isCreateModalOpen}
        setIsModalOpen={setIsCreateModalOpen}
      />
      <SsoSettingsDeleteModal
        isOpen={isDeleteModalVisible}
        ssoSettingsToDelete={ssoSettingsToDelete}
        paginationModel={paginationModel}
        setSsoSettingToDelete={setSsoSettingToDelete}
        setIsDeleteModalVisible={setIsDeleteModalVisible}
      />
      <SsoSettingsUpdateModal
        isOpen={isUpdateModalVisible}
        ssoSettingsToUpdate={ssoSettingsToUpdate}
        paginationModel={paginationModel}
        setSsoSettingsToUpdate={setSsoSettingsToUpdate}
        setIsUpdateModalVisible={setIsUpdateModalVisible}
      />
    </>
  );
};

export default withOrganizationPermissions(SsoSettings);
