import React from 'react';
import <PERSON>HeaderV2 from '../../../components/PageHeaderV2';
import { useIntl } from 'react-intl';
import { ApiKeyManagementControlBar } from './ApiKeyManagementControlBar';
import { ApiKeyManagementContent } from './ApiKeyManagementContent';
import { ApiKeyManagementCreateButton } from './ApiKeyManagementComponents';
import { ApiKeyCreateModal } from './ApiKeyCreateModal';
import { useSelector } from 'react-redux';
import { getOrganizationPermissions } from '../../../userManagement/redux/selectors/organization.selectors';
import { ORGANIZATION_PERMISSIONS } from '../../../constants/organization.constants';
import {
  API_KEY_MANAGEMENT_HEADER_TOOLTIP_TOO_MANY_KEYS_INTL,
  API_KEY_MANAGEMENT_LANDING_PAGE_SUBTITLE,
  API_KEY_MANAGEMENT_LANDING_PAGE_TITLE,
  API_KEY_MAXIMUM_COUNT,
} from './ApiKeyManagementConstants';
import { useHistory } from 'react-router-dom';
import { ApiKeyData } from './ApiKeyManagementTypes';
import { ApiKeyRevokeModal } from './ApiKeyRevokeModal';
import { ApiKeyCopyModal } from './ApiKeyCopyModal';
import { VidMobTooltip } from '../../../vidMobComponentWrappers';
import { GridRowParams } from '@mui/x-data-grid-pro';

const { ORGANIZATION_WORKSPACE_ALL_UPDATE } = ORGANIZATION_PERMISSIONS;

export const ApiKeyManagement = () => {
  const intl = useIntl();
  const history = useHistory();
  const orgPermissions = useSelector(getOrganizationPermissions);
  const isOrgAdmin = orgPermissions?.permissionsKeySet?.has(
    ORGANIZATION_WORKSPACE_ALL_UPDATE,
  );
  const [isCreateButtonDisabled, setIsCreateButtonDisabled] =
    React.useState(false);
  const [isCreateModalVisible, setIsCreateModalVisible] = React.useState(false);
  const [dataForCreateModal, setDataForCreateModal] =
    React.useState<ApiKeyData | null>(null);

  const [isRevokeModalVisible, setIsRevokeModalVisible] = React.useState(false);
  const [keyToRevoke, setKeyToRevoke] = React.useState({ name: '', id: '' });

  const [isCopyModalVisible, setIsCopyModalVisible] = React.useState(false);
  const [keyToCopy, setKeyToCopy] = React.useState({ apiKey: '' });

  const [searchTerm, setSearchTerm] = React.useState('');
  const [apiKeyCount, setApiKeyCount] = React.useState(0);

  if (!isOrgAdmin) {
    history.push('/');
  }

  const handleCreateButtonClick = () => {
    setIsCreateModalVisible(true);
  };

  const handleRevokeButtonClick = (row: ApiKeyData) => {
    const { name, id } = row;
    setKeyToRevoke({ name, id });
    setIsRevokeModalVisible(true);
  };

  const handleRowOnClick = (params: GridRowParams) => {
    setKeyDataAndOpenModal(params.row);
  };

  const setKeyDataAndOpenModal = (cellData: ApiKeyData) => {
    setDataForCreateModal(cellData);
    setIsCreateModalVisible(true);
  };

  const createButton =
    apiKeyCount >= API_KEY_MAXIMUM_COUNT ? (
      <VidMobTooltip
        title={
          intl.messages[
            API_KEY_MANAGEMENT_HEADER_TOOLTIP_TOO_MANY_KEYS_INTL
          ] as string
        }
      >
        <span>
          <ApiKeyManagementCreateButton
            handleClick={handleCreateButtonClick}
            isDisabled={true}
          />
        </span>
      </VidMobTooltip>
    ) : (
      <ApiKeyManagementCreateButton
        handleClick={handleCreateButtonClick}
        isDisabled={isCreateButtonDisabled}
      />
    );

  return (
    <>
      <PageHeaderV2
        title={intl.messages[API_KEY_MANAGEMENT_LANDING_PAGE_TITLE]}
        subtitle={intl.messages[API_KEY_MANAGEMENT_LANDING_PAGE_SUBTITLE]}
      >
        {createButton}
      </PageHeaderV2>
      <ApiKeyManagementControlBar
        apiKeyCount={apiKeyCount}
        setSearchTerm={setSearchTerm}
      />
      <ApiKeyManagementContent
        handleRowOnClick={handleRowOnClick}
        handleRevokeButtonClick={handleRevokeButtonClick}
        searchTerm={searchTerm}
        setApiKeyCount={setApiKeyCount}
        setIsCreateButtonDisabled={setIsCreateButtonDisabled}
        setIsCreateModalVisible={setIsCreateModalVisible}
      />
      <ApiKeyCreateModal
        apiKeyData={dataForCreateModal}
        isOpen={isCreateModalVisible}
        setDataForCreateModal={setDataForCreateModal}
        setIsCreateModalVisible={setIsCreateModalVisible}
        setIsCopyModalVisible={setIsCopyModalVisible}
        setKeyToCopy={setKeyToCopy}
      />
      <ApiKeyRevokeModal
        isOpen={isRevokeModalVisible}
        keyToRevoke={keyToRevoke}
        setIsRevokeModalVisible={setIsRevokeModalVisible}
      />
      <ApiKeyCopyModal
        isOpen={isCopyModalVisible}
        keyToCopy={keyToCopy}
        setIsCopyModalVisible={setIsCopyModalVisible}
      />
    </>
  );
};
