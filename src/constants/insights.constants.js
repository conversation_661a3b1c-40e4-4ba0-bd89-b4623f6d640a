export const INSIGHT_TYPES = {
  BRAND: 'BRAND',
  PLATFORM: 'PLATFORM',
  INDUSTRY: 'INDUSTRY',
};

export const INSIGHT_ATTRIBUTES_KEYS = {
  adAccountIdsKey: 'adAccountIds',
  adAccountsKey: 'adAccounts',
  adSetKey: 'adSet',
  audienceKey: 'audiences',
  campaignsKey: 'campaignsInfo',
  createdByKey: 'createdBy',
  createdTimeKey: 'createdTime',
  detailKey: 'detail',
  detailedInsightBucketsKey: 'detailedInsightBuckets', // aka categories
  endDateKey: 'endDate',
  findingKey: 'title',
  formatsKey: 'formats',
  highLevelInsightBucketsKey: 'highLevelInsightBuckets',
  industryGroupsKey: 'industryGroupIds',
  industriesKey: 'industryIds',
  industryKey: 'industryId',
  insightCreativeExamplesKey: 'insightCreativeExamples',
  insightIdKey: 'insightId',
  kpisKey: 'kpis',
  kpisInfoKey: 'kpisInfo',
  mediaTypesKey: 'mediaTypes',
  objectivesKey: 'objectives',
  placementNestingOptionsKey: 'placementNestingOptions',
  placementsKey: 'placements',
  platformsKey: 'platform',
  projectsKey: 'projects',
  publishDateKey: 'publishDate',
  recommendationKey: 'recommendation',
  startDateKey: 'startDate',
  statusKey: 'status',
  titleKey: 'title',
  workspacesKey: 'workspaceIds',
};

export const INSIGHTS_STATUSES = {
  ARCHIVED: 'ARCHIVED',
  ARCHIVED_DRAFT: 'ARCHIVED_DRAFT',
  ARCHIVED_SCHEDULED: 'ARCHIVED_SCHEDULED',
  SCHEDULED: 'SCHEDULED',
  PUBLISHED: 'PUBLISHED',
  DRAFT: 'DRAFT',
};

// Types of possible creative examples as they are stored in the DB
export const CREATIVE_EXAMPLE_TYPE_ELEMENT = 'ELEMENT';
export const CREATIVE_EXAMPLE_TYPE_DURATION = 'DURATION';
export const CREATIVE_EXAMPLE_TYPE_KPI = 'KPI';
export const CREATIVE_EXAMPLE_TYPE_MEDIA = 'MEDIA'; // This is a placeholder for creative examples coming from Creative Manager

// This maps the values from reportTableView to the example types as they are stored in the DB
export const CREATIVE_EXAMPLE_VIEW_TYPES = {
  element: CREATIVE_EXAMPLE_TYPE_ELEMENT,
  duration: CREATIVE_EXAMPLE_TYPE_DURATION,
  kpi: CREATIVE_EXAMPLE_TYPE_KPI,
  media: CREATIVE_EXAMPLE_TYPE_MEDIA, // This is a placeholder for creative examples coming from Creative Manager
};

export const UPDATE_INSIGHT_ACTIONS = {
  PUBLISH: {
    type: 'PUBLISH',
    errorMsgId: 'ui.user.insightsLibrary.manageInsight.publish.error',
  },
  ARCHIVE: {
    type: 'ARCHIVE',
    errorMsgId: 'ui.user.insightsLibrary.manageInsight.archive.error',
  },
  SAVE_EDIT: {
    type: 'SAVE_EDIT',
    errorMsgId: 'ui.user.insightsLibrary.manageInsight.saveEdit.error',
  },
  UNARCHIVE: {
    type: 'UNARCHIVE',
    errorMsgId: 'ui.user.insightsLibrary.manageInsight.unarchive.error',
  },
};

export const INSIGHTS_LIBRARY_FILTER_PANEL_CONTEXT_NAME =
  'INSIGHTS_LIBRARY_FILTER_PANEL_CONTEXT';
export const INSIGHT_CREATE_CONTEXT_NAME = 'INSIGHT_CREATE_CONTEXT';

export default {
  INSIGHT_TYPES,
};

export const INSIGHT_LOCATIONS = {
  INSIGHTS_LIBRARY: 'Insights Library',
  SHARED_INSIGHTS: 'Shared Insights Page',
  DASHBOARD_WIDGET: 'Dashboard Widget',
  PROJECT_BRIEF: 'Project Brief',
  PROJECT_BRIEF_APPLIED: 'Applied to Project Brief',
  PROJECT_BRIEF_RECOMMENDATION: 'Project Brief Recommendation',
  VIEW_ALL_INSIGHTS_MODAL: 'View all Insights Modal', // used in project brief
  INSIGHT_DETAILS_PAGE: 'Insight Details Page', // insight PDF download
};

export const INSIGHT_MODALS_CONTEXT_NAME = 'INSIGHT_MODAL_CONTEXT';

export const INSIGHT_SOURCES = {
  MANUAL: 'MANUAL',
  AUTOMATED: 'AUTOMATED',
};

export const INSIGHT_USER_EVENTS = {
  DISMISSED_FROM_PROJECT_RECOMMENDATION:
    'DISMISSED_FROM_PROJECT_RECOMMENDATION',
  FAVORITED: 'FAVORITED',
  LINKED_TO_PROJECT: 'LINKED_TO_PROJECT',
  VIEW: 'VIEW',
  VIEW_DETAILS: 'VIEW_DETAILS',
};

export const ARCHIVED_STATUSES = [
  INSIGHTS_STATUSES.ARCHIVED,
  INSIGHTS_STATUSES.ARCHIVED_DRAFT,
  INSIGHTS_STATUSES.ARCHIVED_SCHEDULED,
];
