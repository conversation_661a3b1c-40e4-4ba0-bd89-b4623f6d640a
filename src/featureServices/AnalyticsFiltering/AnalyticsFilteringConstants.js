// redux keys for use in actions and in reducers

export const analyticsFilteringReduxKeys = {
  isLoadingCampaignsKey: 'isLoadingCampaigns',
  isLoadingAdSetsKey: 'isLoadingAdSets',
  availableAudienceFiltersKey: 'availableAudienceFilters',
  selectedAudienceFiltersKey: 'selectedAudienceFilters',
  availableObjectiveFiltersKey: 'availableObjectiveFilters',
  selectedObjectiveFiltersKey: 'selectedObjectiveFilters',
  availablePlacementFiltersKey: 'availablePlacementFilters',
  availablePlacementNestingOptionsKey: 'availablePlacementNestingOptions',
  selectedPlacementFiltersKey: 'selectedPlacementFilters',
  selectedPlacementNestingOptionKey: 'selectedPlacementNestingOption',
  availableCampaignFiltersKey: 'availableCampaignFilters',
  selectedCampaignFiltersKey: 'selectedCampaignFilters',
  availableAdSetFiltersKey: 'availableAdSetFilters',
  selectedAdSetFiltersKey: 'selectedAdSetFilters',
  availableMediaTypeFiltersKey: 'availableMediaTypeFilters',
  selectedMediaTypeFiltersKey: 'selectedMediaTypeFilters',
  analyticsStartDateKey: 'analyticsStartDate',
  analyticsEndDateKey: 'analyticsEndDate',
  createdByVidmobKey: 'createdByVidmob',
  isShowAppAdsFilterKey: 'isShowAppAdsActive',
  isSparkAdsFilterKey: 'isSparkAdsFilterActive',
  impressionFilterMinValueKey: 'impressionFilterMinValue',
  impressionFilterMaxValueKey: 'impressionFilterMaxValue',
};

export const selectedAnalyticsFilterReduxKeys = [
  analyticsFilteringReduxKeys.selectedMediaTypeFiltersKey,
  analyticsFilteringReduxKeys.selectedAudienceFiltersKey,
  analyticsFilteringReduxKeys.selectedCampaignFiltersKey,
  analyticsFilteringReduxKeys.selectedAdSetFiltersKey,
  analyticsFilteringReduxKeys.selectedObjectiveFiltersKey,
  analyticsFilteringReduxKeys.selectedPlacementFiltersKey,
  analyticsFilteringReduxKeys.isCreatedWithVidMobFilterKey,
  analyticsFilteringReduxKeys.isShowAppAdsFilterKey,
  analyticsFilteringReduxKeys.isSparkAdsFilterKey,
];

export const CUSTOM_COMPARE_REPORT = 'CUSTOM_COMPARE_REPORT';
export const CREATIVE_MANAGER = 'CREATIVE_MANAGER';

export const ANALYTICS_REPORT_FILTER_CONTEXT_NAME =
  'ANALYTICS_REPORT_FILTER_CONTEXT';
