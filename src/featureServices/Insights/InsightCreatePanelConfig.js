import {
  CREATIVE_MANAGER,
  OBJECTIVE_REPORT,
  AUDIENCE_REPORT,
  PLACEMENT_REPORT,
  FORMAT_REPORT,
  AD_TYPE_REPORT,
  MEDIA_POST_TYPE_REPORT,
  ELEMENT_PRESENCE_REPORT,
  CUSTOM_COMPARE_REPORT,
  CAMPAIGN_REPORT,
  MARKET_REPORT,
  BRAND_REPORT,
  VIDMOB_PERFORMANCE_REPORT,
  DURATION_REPORT,
} from '../../constants/creativeAnalytics.constants';

import { DateRangeFilterSection } from '../../components/ComposableModalWithContext/ModalSectionComponents';

import {
  FindingSection,
  CategorySection,
  RecommendationSection,
  KpiSection,
  CreateEditCreativeExamplesSection,
  TitleSection,
} from '../../creativeAnalytics/insights/components/InsightModalsSectionComponents';

import {
  InsightCreatePanelContext,
  InsightCreatePanelContextProvider,
} from '../../creativeAnalytics/insights/components/__providers/InsightCreatePanelContextProvider';
import { getIsInsightCreatePanelVisible } from '../../redux/selectors/creativeAnalytics/insights.selectors';
import { getCurrentCreativeIntelligenceView } from '../../redux/selectors/creativeAnalytics/analyticsConfiguration.selectors';

import InsightCreatePanelHeader from '../../components/InsightCreatePanel/InsightCreatePanelHeader';
import InsightCreatePanelFooter from '../../components/InsightCreatePanel/InsightCreatePanelFooter';

import { MODAL_TYPES } from '../../constants/ui.constants';
const { RIGHT_PANEL } = MODAL_TYPES;

const INSIGHT_CREATE_DATE_RANGE_CONFIG = {
  name: 'dateRange',
  isCollapsible: false,
  isExpandedByDefault: false,
  displaySectionSubheader: true,
  isDisabled: true,
  isRequired: false,
  hasTooltip: false,
  sectionHeaderTitleIntlKey:
    'ui.user.insightCreatePanel.dateRange.sectionHeader.title',
  hasDisplayRestrictions: false,
  SectionComponent: DateRangeFilterSection,
};

const INSIGHT_CREATE_TITLE_CONFIG = {
  name: 'title',
  isCollapsible: false,
  isExpandedByDefault: false,
  displaySectionSubheader: true,
  hasToolTip: false,
  isDisabled: false,
  isRequired: true,
  hasDisplayRestrictions: false,
  sectionHeaderTitleIntlKey:
    'ui.user.insightCreatePanel.title.sectionHeader.title',
  SectionComponent: TitleSection,
};

const INSIGHT_CREATE_FINDING_CONFIG = {
  name: 'finding',
  isCollapsible: false,
  isExpandedByDefault: false,
  displaySectionSubheader: true,
  hasToolTip: false,
  isDisabled: false,
  isRequired: true, // required in all reports
  hasDisplayRestrictions: false,
  sectionHeaderTitleIntlKey:
    'ui.user.insightCreatePanel.finding.sectionHeader.title',
  sectionHeaderSubtitleIntlKey:
    'ui.user.insightCreatePanel.finding.sectionHeader.subtitle',
  SectionComponent: FindingSection,
};

const INSIGHT_CREATE_RECOMMENDATION_CONFIG = {
  name: 'recommendation',
  isCollapsible: false,
  isExpandedByDefault: false,
  displaySectionSubheader: true,
  hasToolTip: false,
  isDisabled: false,
  isRequired: false,
  hasDisplayRestrictions: false,
  sectionHeaderTitleIntlKey:
    'ui.user.insightCreatePanel.recommendation.sectionHeader.title',
  sectionHeaderSubtitleIntlKey:
    'ui.user.insightCreatePanel.recommendation.sectionHeader.subtitle',
  SectionComponent: RecommendationSection,
};

const INSIGHT_CREATE_KPI_CONFIG = {
  name: 'kpi',
  isReadOnly: false,
  isCollapsible: false,
  isExpandedByDefault: false,
  displaySectionSubheader: true,
  hasTooltip: false,
  isRequired: false,
  hasDisplayRestrictions: false,
  sectionHeaderTitleIntlKey:
    'ui.user.insightCreatePanel.kpi.sectionHeader.title',
  SectionComponent: KpiSection,
};

const INSIGHT_CREATE_CATEGORY_CONFIG = {
  name: 'category',
  isReadOnly: false,
  isCollapsible: false,
  isExpandedByDefault: false,
  displaySectionSubheader: true,
  isRequired: false,
  hasTooltip: false,
  hasDisplayRestrictions: false,
  sectionHeaderTitleIntlKey:
    'ui.user.insightCreatePanel.category.sectionHeader.title',
  SectionComponent: CategorySection,
};

const INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG = {
  name: 'creativeExamples',
  isCollapsible: false,
  isExpandedByDefault: false,
  displaySectionSubheader: true,
  hasToolTip: false,
  isDisabled: false,
  isRequired: false,
  hasDisplayRestrictions: false,
  sectionHeaderTitleIntlKey:
    'ui.user.insightCreatePanel.creativeExamples.sectionHeader.title',
  sectionHeaderSubtitleIntlKey:
    'ui.user.insightCreatePanel.creativeExamples.sectionHeader.subTitle',
  SectionComponent: CreateEditCreativeExamplesSection,
};

const INSIGHT_CREATE_PANEL_BASE_CONFIG = {
  modalType: RIGHT_PANEL,
  modalClassName: 'insight-create-modal',
  Context: InsightCreatePanelContext,
  ContextProvider: InsightCreatePanelContextProvider,
  visibilitySelector: getIsInsightCreatePanelVisible,
  sectionConfigKeySelector: getCurrentCreativeIntelligenceView,
  needsDatePickerOverflowFix: false,
  hasPages: true,
  headerConfig: {
    hasHeaderCloseButton: true,
    headerTitleIntlKey: 'ui.user.insightCreatePanel.header',
    CustomModalHeaderComponent: InsightCreatePanelHeader,
  },
  footerConfig: {
    submitButtonTextIntlKey: 'ui.notes.subpages.newNote.editor.publishLabel',
    cancelButtonTextIntlKey:
      'ui.user.insightCreatePanel.footer.saveDraft.label',
    CustomModalFooterComponent: InsightCreatePanelFooter,
  },
};

const getCommonSections = () => ({
  TITLE: INSIGHT_CREATE_TITLE_CONFIG,
  FINDING: INSIGHT_CREATE_FINDING_CONFIG,
  RECOMMENDATION: INSIGHT_CREATE_RECOMMENDATION_CONFIG,
  CATEGORY: INSIGHT_CREATE_CATEGORY_CONFIG,
  DATE_RANGE: INSIGHT_CREATE_DATE_RANGE_CONFIG,
  KPI: INSIGHT_CREATE_KPI_CONFIG,
});

const INSIGHT_CREATE_PANEL_MULTIPLE_PAGE_CONFIG = {
  modalPages: [
    {
      pageSections: {
        [CREATIVE_MANAGER]: {
          SECTIONS: getCommonSections(),
        },
        [OBJECTIVE_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [AUDIENCE_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [PLACEMENT_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [FORMAT_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [AD_TYPE_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [MEDIA_POST_TYPE_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [ELEMENT_PRESENCE_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [CUSTOM_COMPARE_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [CAMPAIGN_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [BRAND_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [MARKET_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [VIDMOB_PERFORMANCE_REPORT]: {
          SECTIONS: getCommonSections(),
        },
        [DURATION_REPORT]: {
          SECTIONS: getCommonSections(),
        },
      },
      headerConfig: {
        CustomModalHeaderComponent: InsightCreatePanelHeader,
      },
      footerConfig: {
        CustomModalFooterComponent: InsightCreatePanelFooter,
      },
    },
    {
      pageSections: {
        [CREATIVE_MANAGER]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [OBJECTIVE_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [AUDIENCE_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [PLACEMENT_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [FORMAT_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [AD_TYPE_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [MEDIA_POST_TYPE_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [ELEMENT_PRESENCE_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [CUSTOM_COMPARE_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [CAMPAIGN_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [BRAND_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [MARKET_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [VIDMOB_PERFORMANCE_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
        [DURATION_REPORT]: {
          SECTIONS: {
            CREATIVE_EXAMPLES: INSIGHT_CREATE_CREATIVE_EXAMPLES_CONFIG,
          },
        },
      },
      headerConfig: {
        CustomModalHeaderComponent: InsightCreatePanelHeader,
      },
      footerConfig: {
        CustomModalFooterComponent: InsightCreatePanelFooter,
      },
    },
  ],
};

export const INSIGHT_CREATE_PANEL_CONFIG = {
  ...INSIGHT_CREATE_PANEL_BASE_CONFIG,
  ...INSIGHT_CREATE_PANEL_MULTIPLE_PAGE_CONFIG,
};
