import history from '../../routing/history';
import ProjectService from '../../apiServices/ProjectService';
import { isFunction } from '../../utils/typeCheckUtils';
import { showLocalizedErrorBar } from '../../utils/showConfirmationBar';
import { generatePath } from 'react-router';
import siteMap from '../../routing/siteMap';

export const doCopyProject = (
  intl,
  projectToCopyId,
  includeCollaborators = false,
  newProjectName = null,
  postCopyAction = null,
) => {
  return ProjectService.copyProject(projectToCopyId, includeCollaborators)
    .then((apiResponse) => {
      if (newProjectName) {
        ProjectService.updateProject(apiResponse.data.id, {
          name: newProjectName,
        })
          .then((renamedApiResponse) => {
            afterProjectCopy(renamedApiResponse.data.id, postCopyAction);
          })
          .catch(() => {
            afterProjectCopy(apiResponse.data.id, postCopyAction);
            showLocalizedErrorBar('error.api.project.copiedNotRenamed');
          });
      } else {
        afterProjectCopy(apiResponse.data.id, postCopyAction);
      }
    })
    .catch(() => {
      showLocalizedErrorBar('error.api.project.notCopied');
    });
};

const afterProjectCopy = (newProjectId, postCopyAction) => {
  if (postCopyAction && isFunction(postCopyAction)) {
    postCopyAction();
  } else {
    const newProjectPath = generatePath(siteMap.currentProject, {
      status: 'active',
      projectId: newProjectId,
    });
    history.push(newProjectPath);
  }
};
