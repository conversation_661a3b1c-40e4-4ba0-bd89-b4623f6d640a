import history from '../../routing/history';
import { addConfirmationBar } from '../../redux/actions/confirmationBar.actions';
import ProjectService from '../../apiServices/ProjectService';
import { showConfirmationModal } from '../../utils/confirmationModalControls';
import { isFunction } from '../../utils/typeCheckUtils';
import { GLOBALS } from '../../constants';
import siteMap from '../../routing/siteMap';

export const confirmAndDoProjectDelete = (
  projectToDeleteId,
  intl,
  postDeleteAction = null,
) => {
  return showConfirmationModal({
    cancelButtonLabel: intl.messages['button.global.cancel.label'],
    submitButtonLabel: intl.messages['button.global.delete.label'],
    headerText: intl.messages['modal.deleteProject.header'],
  })
    .then(() => {
      ProjectService.deleteProject(projectToDeleteId)
        .then(() => {
          if (postDeleteAction && isFunction(postDeleteAction)) {
            postDeleteAction();
          } else {
            // by default, when the current project is deleted, redirect to active projects list
            history.push(siteMap.activeProjects);
          }
        })
        .catch(() => {
          addConfirmationBar(
            intl.messages['error.api.project.notDeleted'],
            GLOBALS.CONFIRMATION_BAR_TYPES.ERROR,
          );
        });
    })
    .catch(() => {});
};
