import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { generatePath, useNavigate } from 'react-router-dom-v5-compat';
import { getOrganizationId } from '../../redux/selectors/partner.selectors';
import { useSelector } from 'react-redux';
import { useMemo, useState } from 'react';
import siteMap from '../../routing/siteMap';
import { createDashboard } from '../components/landingPage/hooks/useCreateDashboard';
import { updateDashboard } from '../components/landingPage/hooks/useUpdateDashboard';
import { DashboardResponse, SharingScope, Widget } from '../dashboard.types';
import { buildSaveDashboardPayload } from '../utils/formatDashboardForSaving';
import BffService from '../../apiServices/BffService';
import { useToastAlert } from '../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { LastUpdatedReason } from '../types/dashboardTypes';
import { getCurrentUser } from '../../redux/selectors/user.selectors';
import { trackCustomEventGainsight } from '../../utils/gainsight';
import vmErrorLog from '../../utils/vmErrorLog';
import {
  DashboardValidationError,
  DashboardValidationErrorCodes,
} from '../components/landingPage/DashboardValidtionError';

export const useDashboardData = (
  dashboardId?: string,
  onLastUpdated?: (reason: LastUpdatedReason) => void,
) => {
  const currentUser = useSelector(getCurrentUser);
  const isNew = !dashboardId;

  const defaultDashboardName = useMemo(() => {
    return currentUser?.displayName?.trim()
      ? `${currentUser.displayName}'s dashboard`
      : 'Untitled dashboard';
  }, [currentUser?.displayName]);

  const [title, setTitle] = useState(isNew ? defaultDashboardName : '');
  const [description, setDescription] = useState('');
  const [sharingScope, setSharingScope] = useState<SharingScope>(
    SharingScope.PRIVATE,
  );

  const organizationId = useSelector(getOrganizationId);
  const toast = useToastAlert();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const getDashboardById = async (
    organizationId: string,
    dashboardId?: string,
  ) => {
    const response = await BffService.handleBffApiGet(
      `v1/executive-dashboard/organization/${organizationId}/dashboard/${dashboardId}`,
    );

    return response as DashboardResponse;
  };

  const { data: dashboard, ...query } = useQuery({
    queryKey: ['dashboard', dashboardId],
    queryFn: () => getDashboardById(organizationId, dashboardId!),
    enabled: !!dashboardId && !isNew,
    onSuccess: ({ name, description, sharingScope }) => {
      setTitle(name);
      setDescription(description);
      setSharingScope(sharingScope ?? SharingScope.PRIVATE);
    },
  });

  const saveMutation = useMutation({
    mutationFn: async ({ widgets }: { widgets: Widget[] }) => {
      const payload = buildSaveDashboardPayload({
        name: title.trim() || defaultDashboardName,
        description,
        sharingScope,
        dashboardFilter: dashboard?.dashboardFilter,
        widgets,
      });

      if (isNew) {
        return createDashboard({ organizationId, payload });
      } else {
        return updateDashboard({
          organizationId,
          dashboardId: dashboardId!,
          payload,
        });
      }
    },
    onSuccess: (data) => {
      toast('ui.dashboard.landingPage.save.toast', 'success');
      onLastUpdated?.(LastUpdatedReason.SAVED);

      queryClient.invalidateQueries({ queryKey: ['dashboards'] });
      if (isNew) {
        navigate(
          generatePath(siteMap.executiveDashboard, { dashboardId: data.id }),
        );
      } else {
        queryClient.invalidateQueries({ queryKey: ['dashboard', dashboardId] });
      }
    },
    onError: (err: any) => {
      vmErrorLog(err, 'useDashboardData.saveDashboard');
      let toastKey = 'ui.dashboard.landingPage.save.error.toast';
      if (err instanceof DashboardValidationError) {
        toastKey =
          err.code === DashboardValidationErrorCodes.NAME_REQUIRED
            ? 'ui.dashboard.landingPage.save.error.nameRequired'
            : 'ui.dashboard.landingPage.save.error.widgetsLimit';
      }
      toast(toastKey, 'error');
    },
  });

  const saveDashboard = (widgets: Widget[]) => saveMutation.mutate({ widgets });

  const handleUpdateTitle = (title: string) => {
    setTitle(title);
    onLastUpdated?.(LastUpdatedReason.UPDATE_TITLE);
  };

  const handleUpdateDescription = (description: string) => {
    setDescription(description);
    onLastUpdated?.(LastUpdatedReason.UPDATE_DESCRIPTION);
  };

  const handleUpdateSharingScope = (sharingScope: SharingScope) => {
    setSharingScope(sharingScope);
    onLastUpdated?.(LastUpdatedReason.UPDATE_SHARING_SCOPE);
    trackCustomEventGainsight('Dashboard Shared', {
      sharingScope: sharingScope,
    });
  };

  return {
    ...query,
    dashboard,
    dashboardTitle: title,
    dashboardDescription: description,
    dashboardSharingScope: sharingScope,
    setDashboardTitle: handleUpdateTitle,
    setDashboardDescription: handleUpdateDescription,
    setDashboardSharingScope: handleUpdateSharingScope,
    saveDashboardMutation: saveMutation,
    saveDashboard,
  };
};
