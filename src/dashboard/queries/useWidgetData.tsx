import { useQuery, Query<PERSON><PERSON> } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import BffService from '../../apiServices/BffService';
import { WidgetDataRequest, WidgetDataResponse } from '../dashboard.types';
import { VisualizationType } from '../../widgets/types/visualizationType';
import { getOrganizationId } from '../../redux/selectors/partner.selectors';
import { WIDGET_ID_PREFIX } from '../constants/widgetConstants';
import vmErrorLog from '../../utils/vmErrorLog';
import { ResponseError } from '../../types/errors.types';

function findBackendErrorObject(e: unknown): ResponseError | undefined {
  if (!e || typeof e !== 'object') return;

  const anyErr = e as any;
  return anyErr.baseError?.response?.responseError;
}

const isWorkspaceForbidden = (e: unknown) => {
  const errorObject = findBackendErrorObject(e);

  return (
    (errorObject?.identifier ?? '')
      .toLowerCase()
      .includes('workspace_access') ||
    (errorObject?.message ?? '').toLowerCase().includes('workspace_access')
  );
};

const widgetDataKey = (orgId: string, req: WidgetDataRequest): QueryKey => [
  'widgetData',
  orgId,
  req.widgetId ?? 'preview',
  req.widgetType,
  req.visualizationType,
  JSON.stringify(req.filter),
  JSON.stringify(req.dashboardFilter),
  JSON.stringify(req.widgetCustomProperties),
];

async function fetchWidgetData(
  orgId: string,
  req: WidgetDataRequest,
): Promise<WidgetDataResponse> {
  const body = {
    widgetType: req.widgetType,
    visualizationType: req.visualizationType,
    parameters: req.parameters,
    filter: req.filter,
    dashboardFilter: req.dashboardFilter,
    ...req.widgetCustomProperties,
  };
  const hasID =
    req.widgetId &&
    !req.widgetId.startsWith(WIDGET_ID_PREFIX) &&
    req.widgetId !== 'preview';
  const endpoint = hasID
    ? `v1/executive-dashboard/organization/${orgId}/widget/${req.widgetId}/data`
    : `v1/executive-dashboard/organization/${orgId}/widget/data`;
  return BffService.handleBffApiPost(endpoint, body);
}

export function useWidgetData(req: WidgetDataRequest) {
  const organizationId = useSelector(getOrganizationId);

  const isTextWidget = req.visualizationType === VisualizationType.text;

  const query = useQuery({
    queryKey: widgetDataKey(organizationId, req),
    queryFn: () => fetchWidgetData(organizationId, req),
    enabled:
      !!organizationId &&
      !!req.widgetType &&
      !isTextWidget &&
      (req.filter?.filters?.length ?? 0) > 0,
    onError: (error) => {
      vmErrorLog(error as Error, 'useWidgetData');
    },
  });

  const noPermissions = query.isError && isWorkspaceForbidden(query.error);

  return {
    ...query,
    noPermissions,
    isLoading: isTextWidget ? false : query.isLoading,
  };
}
