import React from 'react';
import { DashboardProvider } from './providers/DashboardProvider';
import { VidMobBox } from '../vidMobComponentWrappers';
import { DashboardPage } from './components/dashboardPage/components/DashboardPage';
import { PDF_DOWNLOAD_IDS } from './constants/dashboardPDFDownloadConstants';

const DashboardContent: React.FC = () => {
  return (
    <VidMobBox
      id={PDF_DOWNLOAD_IDS.DASHBOARD_PAGE_PARENT}
      sx={{
        height: 'calc(100vh - 52px)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >
      <VidMobBox
        id={PDF_DOWNLOAD_IDS.DASHBOARD_PAGE_CHILD}
        sx={{
          flexGrow: 1,
          display: 'flex',
          overflow: 'hidden',
        }}
      >
        <DashboardPage />
      </VidMobBox>
    </VidMobBox>
  );
};

const WrappedDashboard: React.FC = () => (
  <DashboardProvider>
    <DashboardContent />
  </DashboardProvider>
);

export default WrappedDashboard;
