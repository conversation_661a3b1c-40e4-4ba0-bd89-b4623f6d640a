import { IntlShape } from 'react-intl';
import { User } from '../../types/user.types';
import { Workspace } from '../../types/workspace.types';
import { ToastAlertSnackbarType } from '../../redux/slices/toastAlert.slice';

export interface DashboardContextType {
  currentUser: User;
  currentWorkspace: Workspace;
  organizationId: string;
  formatMessage: IntlShape['formatMessage'];
  showToastAlert: (message: string, type: ToastAlertSnackbarType) => void;
  // Dashboard state
  drawerOpen: boolean;
  toggleDrawer: () => void;
  setDrawerOpen: (isOpen: boolean) => void;
}

export interface SeriesData {
  name: string;
  data: number[];
  previousPeriodData?: number[];
  changePercentageData?: number[];
  iconUrl?: string;
}

export enum LastUpdatedReason {
  DUPLICATE_WIDGET = 'duplicateWidget',
  DELETE_WIDGET = 'deleteWidget',
  HANDLE_WIDGET_ADD = 'handleWidgetAdd',
  ON_DROP = 'onDrop',
  ON_RESIZE = 'onResize',
  UPDATE_WIDGET_PROPERTIES = 'updateWidgetProperties',
  UPDATE_TITLE = 'updateTitle',
  UPDATE_DESCRIPTION = 'updateDescription',
  UPDATE_SHARING_SCOPE = 'updateSharingScope',
  MOVED_WIDGET = 'movedWidget',
  SAVED = 'saved',
  UPDATED_WIDGET_DETAILS = 'updatedWidgetDetails',
}

export enum ActionType {
  Favorite = 'favorite',
  Download = 'download',
  Duplicate = 'duplicate',
  Delete = 'delete',
  Share = 'share',
}

export enum QuickRangeKey {
  P7D = 'P7D',
  P30D = 'P30D',
  P3M = 'P3M',
  P6M = 'P6M',
  P12M = 'P12M',
  MTD = 'MTD',
  QTD = 'QTD',
  YTD = 'YTD',
}

export type DateFilterValue =
  | { type: DateFilterType.PRESET; key: QuickRangeKey }
  | { type: DateFilterType.CUSTOM; startDate: string; endDate: string };

export enum DateFilterType {
  PRESET = 'preset',
  CUSTOM = 'custom',
}
