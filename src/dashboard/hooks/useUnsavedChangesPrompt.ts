import { useEffect, useRef, useState, useCallback } from 'react';
import { useHistory } from 'react-router-dom';

interface BlockedLocation {
  pathname: string;
  search: string;
  hash: string;
}

export function useUnsavedChangesPrompt(hasUnsavedChanges: boolean) {
  const history = useHistory();
  const unblockRef = useRef<(() => void) | null>(null);

  const [blockedLoc, setBlockedLoc] = useState<BlockedLocation | null>(null);

  useEffect(() => {
    if (!hasUnsavedChanges) return;

    const handler = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
    };
    addEventListener('beforeunload', handler);
    return () => removeEventListener('beforeunload', handler);
  }, [hasUnsavedChanges]);

  useEffect(() => {
    if (!hasUnsavedChanges) return;

    unblockRef.current = history.block((tx: any) => {
      setBlockedLoc({
        pathname: tx.pathname,
        search: tx.search,
        hash: tx.hash,
      });
      return false;
    });

    return () => {
      unblockRef.current?.();
      unblockRef.current = null;
    };
  }, [hasUnsavedChanges, history]);

  const leaveWithoutSave = useCallback(() => {
    if (!blockedLoc) return;

    unblockRef.current?.();
    history.push(blockedLoc.pathname + blockedLoc.search + blockedLoc.hash);
    setBlockedLoc(null);
  }, [blockedLoc, history]);

  const stayAndEdit = useCallback(() => setBlockedLoc(null), []);

  return { showModal: !!blockedLoc, leaveWithoutSave, stayAndEdit };
}
