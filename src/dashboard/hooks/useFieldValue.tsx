import { useInfiniteQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import BffService from '../../apiServices/BffService';
import { getOrganizationId } from '../../redux/selectors/partner.selectors';
import {
  WidgetFilterKey,
  FilterEntry,
  FilterOptions,
  FilterValue,
  WidgetType,
} from '../../widgets/types/widgetTypes';

const PER_PAGE = 20;

interface GetWidgetFilterValuesProps {
  organizationId: string;
  widgetType: WidgetType;
  key: WidgetFilterKey | string;
  offset?: number;
  perPage?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  filters?: FilterEntry[];
}

const getWidgetFilterValues = async ({
  organizationId,
  widgetType,
  key,
  offset = 0,
  perPage = PER_PAGE,
  sortBy,
  sortOrder,
  search,
  filters,
}: GetWidgetFilterValuesProps) => {
  const queryParams = new URLSearchParams();
  queryParams.set('offset', offset.toString());
  queryParams.set('perPage', perPage.toString());
  if (sortBy) queryParams.set('sortBy', sortBy);
  if (sortOrder) queryParams.set('sortOrder', sortOrder);
  if (search) queryParams.set('search', search);

  const queryString = queryParams.toString();
  const url = `v1/executive-dashboard/organization/${organizationId}/widget/type/${widgetType}/filter/${key}${
    queryString ? `?${queryString}` : ''
  }`;

  const response = await BffService.handleBffApiPost(url, {
    filters,
  });

  const values = (response?.values as FilterValue[]) || [];

  return {
    values,
    nextOffset: values.length === perPage ? offset + perPage : undefined,
  };
};

interface UseFieldValueProps {
  initialData: FilterOptions | undefined;
  widgetType: WidgetType;
  key: WidgetFilterKey | string;
  filters?: FilterEntry[];
  search?: string;
  enabled?: boolean;
}

export const useFieldValue = ({
  initialData,
  widgetType,
  key,
  search,
  filters = [],
  enabled = true,
}: UseFieldValueProps) => {
  const organizationId = useSelector(getOrganizationId);

  return useInfiniteQuery({
    queryKey: ['fieldValues', organizationId, widgetType, key, search, filters],
    queryFn: ({ pageParam = 0 }) => {
      return getWidgetFilterValues({
        organizationId: organizationId!,
        widgetType,
        key,
        offset: pageParam,
        perPage: PER_PAGE,
        search,
        filters,
      });
    },
    getNextPageParam: (lastPage: any) => lastPage.nextOffset,
    initialData: initialData
      ? {
          pages: [
            {
              values: initialData.values,
              nextOffset: initialData.pagination?.nextOffset,
            },
          ],
          pageParams: [initialData.pagination?.offset],
        }
      : undefined,
    enabled: !!organizationId && !!widgetType && !!key && enabled,
  });
};
