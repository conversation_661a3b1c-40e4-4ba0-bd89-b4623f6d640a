import { useCallback, useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';
import { Layout, ItemCallback } from 'react-grid-layout';
import {
  buildLayoutItem,
  findNextSpot,
  getMinHeight,
  getWidgetLayout,
  makeLayoutId,
  snapLayoutArray,
  splitLayoutId,
  findLandingY,
} from '../utils/dashboardUtils';
import { getDataVisualizationRegistryItem } from '../../widgets/utils/dataVisualizationRegistry';
import { uniqueId } from '../../utils/uniqueIdGenerator';
import { useWidgetInfoList } from '../queries/useWidgetInfoList';
import { trackCustomEventGainsight } from '../../utils/gainsight';
import { BASE_HEIGHT, COLUMNS } from '../constants/dashboardConstants';
import { WIDGET_ID_PREFIX } from '../constants/widgetConstants';
import {
  DashboardResponse,
  Widget,
  WidgetFilter,
  WidgetVisualizationDto,
} from '../dashboard.types';
import { LastUpdatedReason } from '../types/dashboardTypes';
import { WidgetKeyEnum, WidgetType } from '../../widgets/types/widgetTypes';

export const useDashboardWidgets = (
  dashboard: DashboardResponse | undefined,
  updateLastUpdated: (reason: LastUpdatedReason) => void,
) => {
  const [layout, setLayout] = useState<Layout[]>([]);
  const [editedWidgets, setEditedWidgets] = useState<string[]>([]);
  const [currentDragType, setCurrentDragType] = useState<
    WidgetType | undefined
  >(undefined);
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [isDraggingWidget, setIsDraggingWidget] = useState<boolean>(false);
  const { data: widgetInfoList } = useWidgetInfoList();

  const intl = useIntl();

  const getWidgetInfo = useCallback(
    (widgetType: WidgetType) => {
      if (!widgetInfoList) return null;
      const widgetInfo = widgetInfoList.find(
        (widget) => widget.widgetType === widgetType,
      );
      if (!widgetInfo) return null;
      return widgetInfo;
    },
    [widgetInfoList],
  );

  const getLayoutFromDashboard = useCallback((dashboard: DashboardResponse) => {
    const newLayout: Layout[] = dashboard.widgets.map((dashboardWidget) => {
      const visualizationType = dashboardWidget.visualizationType;
      const minW = getDataVisualizationRegistryItem(visualizationType).minW;
      const layoutId = makeLayoutId(
        dashboardWidget.visualizationType,
        dashboardWidget.id,
      );

      return {
        i: layoutId,
        x: dashboardWidget.gridX,
        y: dashboardWidget.gridY,
        w: dashboardWidget.gridWidth,
        h: dashboardWidget.gridHeight,
        minW,
        minH: BASE_HEIGHT,
        maxH: BASE_HEIGHT,
        isResizable: true,
      };
    });

    return newLayout;
  }, []);

  useEffect(() => {
    if (dashboard?.widgets) {
      const newLayout = getLayoutFromDashboard(dashboard);
      setLayout(newLayout);
      setWidgets(dashboard.widgets);
    }
  }, [dashboard?.widgets, getLayoutFromDashboard]);

  const updateWidgetProperties = useCallback(
    (
      widgetId: string,
      properties: WidgetVisualizationDto,
      key: WidgetKeyEnum = WidgetKeyEnum.DATA,
    ) => {
      setWidgets((prev) =>
        prev.map((w) => (w.id === widgetId ? { ...w, [key]: properties } : w)),
      );
      updateLastUpdated(LastUpdatedReason.UPDATE_WIDGET_PROPERTIES);
    },
    [],
  );

  const updateWidget = useCallback((widget: Widget) => {
    setWidgets((prev) => prev.map((w) => (w.id === widget.id ? widget : w)));
  }, []);

  const handleWidgetAdd = useCallback(
    (
      widgetType: WidgetType,
      x: number,
      y: number,
      customData: Partial<Widget> & {
        gridWidth?: number;
        gridHeight?: number;
      } = {},
    ) => {
      const info = getWidgetInfo(widgetType);

      if (!info) return;

      const widgetId = String(uniqueId(WIDGET_ID_PREFIX));
      const layoutId = makeLayoutId(info.defaultVisualizationType, widgetId);

      const { gridWidth, gridHeight, filter, ...restData } = customData;

      const rawFields = filter;
      const defaultFields = rawFields ?? info.defaultFilter;
      let normalizedFilter: WidgetFilter = { filters: [] };
      if (filter) {
        normalizedFilter = filter;
      } else {
        normalizedFilter = defaultFields;
      }

      const newLayoutItem = buildLayoutItem(
        info,
        layoutId,
        x,
        y,
        gridWidth,
        gridHeight,
      );
      setLayout((prev) => [...prev, newLayoutItem]);

      const newWidget: Widget = {
        id: widgetId,
        widgetType: info.widgetType,
        visualizationType: info.defaultVisualizationType,
        name: info.name,
        dateCreated: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),

        gridX: newLayoutItem.x,
        gridY: newLayoutItem.y,
        gridWidth: newLayoutItem.w,
        gridHeight: newLayoutItem.h,
        filter: normalizedFilter,

        ...restData,
      };

      setWidgets((prev) => [...prev, newWidget]);
      updateLastUpdated(LastUpdatedReason.HANDLE_WIDGET_ADD);

      trackCustomEventGainsight('Added Widget', {
        type: info.widgetType,
      });
    },
    [getWidgetInfo, updateLastUpdated],
  );

  const addWidget = useCallback(
    (widgetType: WidgetType) => {
      const widgetMeta = getWidgetInfo(widgetType);
      if (!widgetMeta) return;

      const def = getDataVisualizationRegistryItem(
        widgetMeta.defaultVisualizationType,
      );
      const newW = def.defaultW;
      const newH = BASE_HEIGHT;

      const { x, y } = findNextSpot(layout, newW, newH, COLUMNS);

      handleWidgetAdd(widgetType, x, y, {
        gridWidth: newW,
        gridHeight: newH,
      });
    },
    [handleWidgetAdd, layout, getWidgetInfo],
  );

  const deleteWidget = useCallback(
    (widgetId: string) => {
      setLayout((prev) => {
        const without = prev.filter(
          (item) => splitLayoutId(item.i).widgetId !== widgetId,
        );
        return snapLayoutArray(without);
      });
      setWidgets((prev) => prev.filter((w) => w.id !== widgetId));
      updateLastUpdated(LastUpdatedReason.DELETE_WIDGET);
    },
    [updateLastUpdated],
  );

  const duplicateWidget = useCallback(
    (widgetId: string) => {
      const originalLayout = layout.find(
        (l) => splitLayoutId(l.i).widgetId === widgetId,
      );
      const widget = widgets.find((w) => w.id === widgetId);
      if (!originalLayout || !widget) return;

      const { w: origW, h: origH, y: origY } = originalLayout;

      const { x: newX, y: newY } = findNextSpot(
        layout,
        origW,
        origH,
        COLUMNS,
        origY,
      );

      handleWidgetAdd(widget.widgetType as WidgetType, newX, newY, {
        parameters: widget.parameters,
        filter: widget.filter,
        gridWidth: origW,
        gridHeight: origH,
        name: `${widget.name} (Copy)`,
        visualizationType: widget.visualizationType,
        isCompareToPreviousPeriodEnabled:
          widget.isCompareToPreviousPeriodEnabled,
        isViewDataLabelsEnabled: widget.isViewDataLabelsEnabled,
      });

      updateLastUpdated(LastUpdatedReason.DUPLICATE_WIDGET);
    },
    [layout, widgets, handleWidgetAdd, updateLastUpdated],
  );

  const onDrag: ItemCallback = useCallback(
    (layout, _oldItem, newItem, placeholder) => {
      placeholder.x = Math.max(0, Math.min(newItem.x, COLUMNS - newItem.w));
      placeholder.y = findLandingY(layout, placeholder, newItem.y);
    },
    [],
  );

  const onResize: ItemCallback = useCallback(
    (_l, _old, newItem, placeholder) => {
      const { vis } = splitLayoutId(newItem.i);

      const minW = newItem.minW || 1;
      const finalW = Math.max(newItem.w, minW);

      const minH = getMinHeight(vis, finalW);

      const clampY = Math.max(0, newItem.y);

      placeholder.y = newItem.y = clampY;
      placeholder.w = newItem.w = finalW;
      placeholder.h = newItem.h = Math.max(newItem.h, minH);
      placeholder.minH = newItem.minH = minH;

      updateLastUpdated(LastUpdatedReason.ON_RESIZE);
    },
    [updateLastUpdated],
  );

  const onDrop = useCallback(
    (resolvedLayout: Layout[], placeholder: Layout) => {
      if (!currentDragType) return;
      const widgetMeta = getWidgetInfo(currentDragType);
      if (!widgetMeta) return;
      const widgetId = String(uniqueId(WIDGET_ID_PREFIX));
      const layoutId = makeLayoutId(
        widgetMeta.defaultVisualizationType,
        widgetId,
      );
      const def = getDataVisualizationRegistryItem(
        widgetMeta.defaultVisualizationType,
      );

      const withNewItem = resolvedLayout.map((l) =>
        l.i === placeholder.i
          ? {
              ...l,
              i: layoutId,
              minW: def.minW,
              minH: BASE_HEIGHT,
              maxH: BASE_HEIGHT,
              isResizable: true,
            }
          : l,
      );

      const snapped = snapLayoutArray(withNewItem);
      setLayout(snapped);

      const layoutDefaults = getWidgetLayout(
        snapped,
        layoutId,
        def.defaultW,
        BASE_HEIGHT,
      );
      const filters = {
        filters: widgetMeta.defaultFilter?.filters || [],
      };

      const newWidget: Widget = {
        id: widgetId,
        widgetType: widgetMeta.widgetType,
        visualizationType: widgetMeta.defaultVisualizationType,
        data: {} as WidgetVisualizationDto,
        filter: filters,
        name: widgetMeta.name,
        dateCreated: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        ...layoutDefaults,
      };

      setWidgets((prev) => [...prev, newWidget]);
      setCurrentDragType(undefined);
      updateLastUpdated(LastUpdatedReason.ON_DROP);

      trackCustomEventGainsight('Added Widget', {
        type: widgetMeta.widgetType,
      });
    },
    [currentDragType, intl, updateLastUpdated],
  );

  const onDragStart = useCallback((widgetType: WidgetType) => {
    setCurrentDragType(widgetType);
  }, []);

  const onDragEnd = useCallback(() => {
    setCurrentDragType(undefined);
  }, []);

  const onDragStop: ItemCallback = useCallback(
    (_layout, _oldItem, newItem, _placeholder) => {
      if (_oldItem.x !== newItem.x || _oldItem.y !== newItem.y) {
        updateLastUpdated(LastUpdatedReason.MOVED_WIDGET);
      }
    },
    [],
  );

  const updatedLayout = useMemo(() => {
    return layout.map((item) => {
      const { widgetId } = splitLayoutId(item.i);
      if (editedWidgets.includes(widgetId)) {
        return { ...item, isDraggable: false, isResizable: false };
      } else {
        return { ...item, isDraggable: true, isResizable: true };
      }
    });
  }, [layout, editedWidgets]);

  const handleEditingWidget = useCallback(
    (widgetId: string, isEditing: boolean) => {
      if (isEditing) {
        setEditedWidgets((prev) => [...prev, widgetId]);
      } else {
        setEditedWidgets((prev) => prev.filter((id) => id !== widgetId));
      }
    },
    [],
  );

  const updateWidgetName = useCallback(
    (widgetId: string, newName: string) => {
      setWidgets((prev) =>
        prev.map((w) => (w.id === widgetId ? { ...w, name: newName } : w)),
      );
      updateLastUpdated(LastUpdatedReason.UPDATE_WIDGET_PROPERTIES);
    },
    [updateLastUpdated],
  );

  const commitLayout = useCallback((raw: Layout[]) => {
    const snapped = snapLayoutArray(raw);
    setLayout(snapped);

    setWidgets((prev) =>
      prev.map((w) => {
        const layoutId = makeLayoutId(w.visualizationType, w.id);

        const { defaultW } = getDataVisualizationRegistryItem(
          w.visualizationType,
        );

        const { gridX, gridY, gridWidth, gridHeight } = getWidgetLayout(
          snapped,
          layoutId,
          defaultW,
          BASE_HEIGHT,
        );

        return {
          ...w,
          gridX,
          gridY,
          gridWidth,
          gridHeight,
        };
      }),
    );
  }, []);

  const isAddDisabled: boolean = useMemo(() => {
    return widgets.length >= 16;
  }, [widgets.length]);

  const handleDrawerDragStart = useCallback(
    (widgetType: WidgetType) => {
      setIsDraggingWidget(true);
      onDragStart(widgetType);
    },
    [onDragStart],
  );

  const handleDrawerDragEnd = useCallback(() => {
    setIsDraggingWidget(false);
    onDragEnd();
  }, [onDragEnd]);

  return {
    layout: updatedLayout,
    currentDragType,
    addWidget,
    deleteWidget,
    duplicateWidget,
    onDrag,
    onResize,
    onDrop,
    handleEditingWidget,
    widgets,
    updateWidgetProperties,
    updateWidget,
    commitLayout,
    onDragStop,
    updateWidgetName,
    isAddDisabled,
    handleDrawerDragStart,
    handleDrawerDragEnd,
    isDraggingWidget,
  };
};
