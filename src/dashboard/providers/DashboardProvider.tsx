import React, { ReactNode, useState } from 'react';
import { useSelector } from 'react-redux';

import { getCurrentUser } from '../../redux/selectors/user.selectors';
import { DashboardContext } from '../hooks/useDashboardPage';
import {
  getCurrentPartner,
  getOrganizationId,
} from '../../redux/selectors/partner.selectors';
import { useIntl } from 'react-intl';
import { useToastAlert } from '../../creativeAnalytics/__pages/SavedReports/SavedReportsCustomHooks';
import { useParams } from 'react-router-dom';

interface DashboardProviderProps {
  children: ReactNode;
}

export const DashboardProvider: React.FC<DashboardProviderProps> = ({
  children,
}) => {
  const currentUser = useSelector(getCurrentUser);
  const currentWorkspace = useSelector(getCurrentPartner);
  const organizationId = useSelector(getOrganizationId);
  const intl = useIntl();
  const { dashboardId } = useParams<{ dashboardId: string }>();

  const showToastAlert = useToastAlert();

  const [drawerOpen, setDrawerOpen] = useState(dashboardId ? false : true);

  const toggleDrawer = () => setDrawerOpen((o) => !o);

  return (
    <DashboardContext.Provider
      value={{
        currentUser,
        currentWorkspace,
        organizationId,
        formatMessage: intl.formatMessage,
        showToastAlert,
        drawerOpen,
        toggleDrawer,
        setDrawerOpen,
      }}
    >
      {children}
    </DashboardContext.Provider>
  );
};
