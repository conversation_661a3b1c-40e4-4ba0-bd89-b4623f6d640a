import * as React from 'react';
import { IntlShape } from 'react-intl';
import {
  VidMobBox,
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import TopRightDecoration from '../../../../assets/images/Top-Blocks.svg';
import BottomRightDecoration from '../../../../assets/images/Bottom-Blocks.svg';
import {
  LandingPageNavigationElement,
  LandingPageNavigationElementProps,
} from './LandingPageNavigationElement';
import { generatePath, NavigateFunction } from 'react-router-dom-v5-compat';
import { LANDING_PAGE_INTL_KEYS } from '../copy/landingPageCopy';

interface LandingPageNavigationProps {
  formatMessage: IntlShape['formatMessage'];
  cards: LandingPageNavigationElementProps[];
  navigate: NavigateFunction;
  headingLabel?: React.ReactNode;
}

export const LandingPageNavigation: React.FC<LandingPageNavigationProps> = ({
  formatMessage,
  cards,
  navigate,
  headingLabel,
}) => (
  <VidMobBox
    sx={{
      position: 'relative',
      p: '16px',
      borderRadius: '6px',
      backgroundColor: 'background.contrast',
      overflow: 'hidden',
      mb: '36px',
    }}
  >
    <VidMobBox
      component="img"
      src={TopRightDecoration}
      sx={{
        position: 'absolute',
        top: 0,
        right: 0,
        pointerEvents: 'none',
        userSelect: 'none',
        zIndex: 0,
      }}
    />
    <VidMobBox
      component="img"
      src={BottomRightDecoration}
      sx={{
        position: 'absolute',
        bottom: 0,
        right: 0,
        pointerEvents: 'none',
        userSelect: 'none',
        zIndex: 0,
      }}
    />

    <VidMobBox sx={{ position: 'relative', zIndex: 1 }}>
      <VidMobTypography variant="subtitle1" mb={8}>
        {headingLabel ??
          formatMessage(LANDING_PAGE_INTL_KEYS.quickLinks.navLabel)}
      </VidMobTypography>

      <VidMobStack direction="row" flexWrap="wrap" gap={4}>
        {cards.map((card) => (
          <LandingPageNavigationElement
            key={card.identifier}
            {...card}
            onClick={(p) => p && navigate(generatePath(p))}
          />
        ))}
      </VidMobStack>
    </VidMobBox>
  </VidMobBox>
);
