import React, { useMemo } from 'react';
import {
  FilterChangeArgs,
  FilterField,
} from '../../../../widgets/types/widgetTypes';
import { WidgetType } from '../../../../widgets/types/widgetTypes';
import FilterEntry from '../../../../components/ReportFilters/components/FilterEntry';
import {
  ValueType,
  Operator,
  Value,
} from '../../../../components/ReportFilters/types';
import { useFieldValue } from '../../../hooks/useFieldValue';
import { useIntl } from 'react-intl';
import { getShouldDisableOption } from '../widgetEdit.utils';

const FilterEntryDataWrapper = ({
  filter,
  widgetType,
  transformedValue,
  valueType,
  handleFilterChange,
  handleDeleteFilter,
  icon,
}: {
  filter: FilterField;
  widgetType: WidgetType;
  currentValue: any;
  transformedValue: Value;
  valueType: ValueType;
  handleFilterChange: (args: FilterChangeArgs) => void;
  handleDeleteFilter: (key: string) => void;
  icon?: JSX.Element;
}) => {
  const intl = useIntl();
  const { data, isLoading } = useFieldValue({
    widgetType,
    key: filter.key,
    initialData: undefined,
  });

  const options = useMemo(
    () =>
      data?.pages.flatMap((page) =>
        page.values.map((value: any) => ({
          ...value,
          ...getShouldDisableOption(intl, filter.key, value),
        })),
      ) || [],
    [data],
  );

  const internalOnChange = (args: {
    filterKey: any;
    value: Value;
    operator: Operator;
  }) => {
    handleFilterChange({
      key: args.filterKey,
      value: args.value,
      operator: args.operator,
    });
  };

  return (
    <FilterEntry
      key={filter.key}
      filterKey={filter.key}
      labelKey={filter.displayLabel || filter.key}
      value={transformedValue}
      valueType={valueType}
      operator={Operator.IN}
      operators={[Operator.IN]}
      onChange={internalOnChange}
      disabled={isLoading}
      hideOperator={true}
      icon={icon}
      canDelete={true}
      onDelete={handleDeleteFilter}
      valueOptions={options}
      displaySearch={true}
      autoSelectIfSingleValue={false}
      shouldShowSelectAll={true}
      shouldShowRemoveAll={false}
      hasGroupSelect={false}
      isMandatory={false}
      hideError={false}
    />
  );
};

export default FilterEntryDataWrapper;
