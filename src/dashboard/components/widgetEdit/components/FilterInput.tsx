import React from 'react';
import {
  FilterField,
  FilterInputType,
  FilterOptions,
  FilterValue,
} from '../../../../widgets/types/widgetTypes';
import {
  WidgetType,
  WidgetFilterKey,
} from '../../../../widgets/types/widgetTypes';
import DateFilter from './FilterInputs/DateFilter';
import Multiselect from './FilterInputs/Multiselect';
import SingleSelect from './FilterInputs/SingleSelect';
import { DateFilterValue } from '../../../types/dashboardTypes';
import {
  serializeDateFilter,
  toDateFilterValue,
} from '../../../utils/dateUtils';
import { FilterOperator } from '../../../dashboard.types';
import { VidMobBox } from '../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import { FILTER_EXTRA_OPTIONS } from '../widgetEdit.config';

interface FilterInputProps {
  filter: FilterField;
  widgetType: WidgetType;
  value: any;
  updateFilterValue: (
    key: WidgetFilterKey,
    value: any,
    operator?: FilterOperator,
  ) => void;
  updateProperty: (property: string, value: any) => void;
  getProperty: (property: string) => any;
  initialFilterValues?: FilterOptions;
}

const FilterInput = ({
  filter,
  widgetType,
  value,
  updateFilterValue,
  updateProperty,
  getProperty,
  initialFilterValues,
}: FilterInputProps) => {
  const intl = useIntl();

  const onChange = (value: FilterValue | Array<FilterValue>) => {
    updateFilterValue(filter.key as WidgetFilterKey, value);
  };

  function renderFilterInput() {
    switch (filter.type) {
      case FilterInputType.DATE_RANGE: {
        const dateValue = toDateFilterValue(value);

        const handleDateChange = (
          newVal: DateFilterValue,
          operator?: FilterOperator,
        ) => {
          updateFilterValue(
            filter.key as WidgetFilterKey,
            serializeDateFilter(newVal),
            operator,
          );
        };

        return (
          <DateFilter
            label={filter.displayLabel || filter.key}
            value={dateValue}
            onChange={handleDateChange}
          />
        );
      }

      case FilterInputType.MULTI_SELECT:
        return (
          <Multiselect
            label={filter.displayLabel || filter.key}
            widgetType={widgetType}
            filterKey={filter.key}
            value={value || []}
            onChange={onChange}
            initialData={initialFilterValues}
            dependsOn={filter.dependsOn}
          />
        );

      case FilterInputType.FILTERED_MULTI_SELECT:
        return (
          <Multiselect
            label={filter.displayLabel || filter.key}
            widgetType={widgetType}
            filterKey={filter.key}
            value={value?.map((item: { id: string }) => item.id) || []}
            onChange={onChange}
            initialData={initialFilterValues}
            dependsOn={filter.dependsOn}
          />
        );

      case FilterInputType.SINGLE_SELECT:
        return (
          <SingleSelect
            label={filter.displayLabel || filter.key}
            widgetType={widgetType}
            filterKey={filter.key}
            value={value || null}
            onChange={onChange}
            initialData={initialFilterValues}
            dependsOn={filter.dependsOn}
          />
        );

      default:
        return null;
    }
  }

  return (
    <VidMobBox gap="4px" flexDirection="column" display="flex">
      {renderFilterInput()}
      {FILTER_EXTRA_OPTIONS.map(
        (option) =>
          option.shouldShow(filter, widgetType) &&
          option.component(intl, getProperty(option.value), updateProperty),
      )}
    </VidMobBox>
  );
};

export default FilterInput;
