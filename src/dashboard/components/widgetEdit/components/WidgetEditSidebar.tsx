import React from 'react';
import {
  VidMobDivider,
  VidMobStack,
} from '../../../../vidMobComponentWrappers';
import { VidMobBox } from '../../../../vidMobComponentWrappers';
import {
  FilterField,
  FilterOptions,
  WidgetType,
} from '../../../../widgets/types/widgetTypes';
import { VisualizationType } from '../../../../widgets/types/visualizationType';
import { useWidgetEdit } from '../context';
import OptionalFiltersSection from './OptionalFiltersSection';
import RequiredFiltersSection from './RequiredFiltersSection';
import ChartSection from './ChartSection';

interface WidgetEditSidebarProps {
  widgetType: WidgetType;
  defaultVisualizationType: VisualizationType;
  visualizationTypes: VisualizationType[];
  requiredFilters: FilterField[];
  optionalFilters: FilterField[];
  filterValues?: Record<string, FilterOptions>;
}

export const WidgetEditSidebar = ({
  widgetType,
  defaultVisualizationType,
  visualizationTypes,
  requiredFilters,
  optionalFilters,
  filterValues,
}: WidgetEditSidebarProps) => {
  const {
    selectedVisualizationType,
    updateFilterValue,
    updateVisualizationType,
    getFilterValue,
    updateProperty,
    getProperty,
    activeOptionalFilters,
    addOptionalFilter,
    removeOptionalFilter,
    canResetFilters,
    resetFilters,
  } = useWidgetEdit();

  const currentVisualizationType =
    selectedVisualizationType || defaultVisualizationType;

  return (
    <VidMobBox sx={{ width: '480px', height: '100%', p: '24px' }}>
      <VidMobStack flexDirection="column" gap={8}>
        <RequiredFiltersSection
          requiredFilters={requiredFilters}
          widgetType={widgetType}
          getFilterValue={getFilterValue}
          updateFilterValue={updateFilterValue}
          updateProperty={updateProperty}
          getProperty={getProperty}
          filterValues={filterValues}
          canResetFilters={canResetFilters}
          resetFilters={resetFilters}
        />
        {visualizationTypes.length > 1 ? (
          <>
            <VidMobDivider />
            <ChartSection
              defaultVisualizationType={currentVisualizationType}
              visualizationTypes={visualizationTypes}
              onChange={updateVisualizationType}
            />
          </>
        ) : null}
        <VidMobDivider />
        <OptionalFiltersSection
          optionalFilters={optionalFilters}
          widgetType={widgetType}
          getFilterValue={getFilterValue}
          updateFilterValue={updateFilterValue}
          activeOptionalFilters={activeOptionalFilters}
          addOptionalFilter={addOptionalFilter}
          removeOptionalFilter={removeOptionalFilter}
        />
      </VidMobStack>
    </VidMobBox>
  );
};
