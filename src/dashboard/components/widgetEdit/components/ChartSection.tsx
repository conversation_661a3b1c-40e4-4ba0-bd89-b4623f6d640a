import React from 'react';
import {
  VidMobBox,
  VidMobTypography,
  VidMobStack,
  VidMobTooltip,
} from '../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import { DASHBOARD_INTL_KEYS } from '../../../copy/dashboardCopy';
import { VisualizationType } from '../../../../widgets/types/visualizationType';
import ChartTypeSelector from './FilterInputs/ChartTypeSelector';
import { InfoFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import { VISUALIZATIONS_WITH_VIEW_DATA_LABELS } from '../widgetEdit.config';
import { useWidgetEdit } from '../context';
import SwitchInput from './FilterInputs/SwitchInput';
import { WidgetCustomProperties } from '../../../constants/dashboardConstants';

export const ChartSection = ({
  defaultVisualizationType,
  visualizationTypes,
  onChange,
}: {
  defaultVisualizationType: VisualizationType;
  visualizationTypes: VisualizationType[];
  onChange: (type: VisualizationType) => void;
}) => {
  const { getProperty, updateProperty, selectedVisualizationType } =
    useWidgetEdit();

  const isViewDataLabelsEnabled = getProperty(
    WidgetCustomProperties.IS_VIEW_DATA_LABELS_ENABLED,
  );

  const currentVisualizationType =
    selectedVisualizationType ?? defaultVisualizationType;

  const intl = useIntl();
  return (
    <VidMobBox gap="16px" flexDirection="column" display="flex">
      <VidMobStack flexDirection="row" alignItems="center" gap={2}>
        <VidMobTypography variant="subtitle2">
          {intl.formatMessage(
            DASHBOARD_INTL_KEYS.editSidebar.chartSectionTitle,
          )}
        </VidMobTypography>
        <VidMobTooltip
          title={intl.formatMessage(
            DASHBOARD_INTL_KEYS.editSidebar.chartSectionInfo,
          )}
          placement="top"
        >
          <InfoFilledIcon htmlColor="grey" className="small" />
        </VidMobTooltip>
      </VidMobStack>
      <VidMobStack flexDirection="column" gap={2}>
        <VidMobTypography variant="subtitle3">
          {intl.formatMessage(DASHBOARD_INTL_KEYS.editSidebar.chartSectionType)}
        </VidMobTypography>
        <VidMobStack flexDirection="column" gap={2}>
          <ChartTypeSelector
            chartType={defaultVisualizationType}
            visualizationTypes={visualizationTypes}
            onChange={onChange}
          />
        </VidMobStack>

        {VISUALIZATIONS_WITH_VIEW_DATA_LABELS.includes(
          currentVisualizationType,
        ) && (
          <SwitchInput
            label={intl.formatMessage(
              DASHBOARD_INTL_KEYS.editSidebar.viewDataLabels,
            )}
            value={isViewDataLabelsEnabled}
            onChange={(value) =>
              updateProperty(
                WidgetCustomProperties.IS_VIEW_DATA_LABELS_ENABLED,
                value,
              )
            }
          />
        )}
      </VidMobStack>
    </VidMobBox>
  );
};

export default ChartSection;
