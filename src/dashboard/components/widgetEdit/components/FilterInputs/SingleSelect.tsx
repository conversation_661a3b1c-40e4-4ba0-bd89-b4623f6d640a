import React, { useEffect, useMemo, useState } from 'react';
import {
  VidMobBox,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { useTheme } from '@mui/material';
import {
  WidgetType,
  WidgetFilterKey,
  FilterOptions,
  FilterValue,
  FilterEntry,
} from '../../../../../widgets/types/widgetTypes';
import { DropdownSingleSelect } from '../../../../../muiCustomComponents/Dropdowns';
import { DropdownOption } from '../../../../../muiCustomComponents/Dropdowns/types';
import { useFieldValue } from '../../../../hooks/useFieldValue';
import useDebounce from '../../../../../hooks/useDebounce';
import { Operator } from '../../../../../components/ReportFilters/types';
import { useWidgetEdit } from '../../context';
import MuiIconForChannel from '../../../../../assets/vidmob-mui-icons/channels/MuiIconForChannel';
import { useIntl } from 'react-intl';
import { getShouldDisableOption } from '../../widgetEdit.utils';

interface SingleSelectProps {
  label: string;
  widgetType: WidgetType;
  filterKey: WidgetFilterKey | string;
  value: FilterValue | [FilterValue];
  onChange: (value: [FilterValue]) => void;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  displaySearch?: boolean;
  autoSelectIfSingleValue?: boolean;
  initialData: FilterOptions | undefined;
  dependsOn?: string[];
}

const iconSx = {
  height: '20px',
  width: '20px',
};

const SingleSelect = ({
  label,
  widgetType,
  filterKey,
  value,
  onChange,
  disabled = false,
  required = false,
  displaySearch = true,
  autoSelectIfSingleValue = false,
  initialData,
  dependsOn,
}: SingleSelectProps) => {
  const theme = useTheme();
  const intl = useIntl();
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const { getFilterValue } = useWidgetEdit();

  const { dependentFilters, hasMissingDependency } = useMemo(() => {
    if (!dependsOn || dependsOn.length === 0) {
      return {
        dependentFilters: [] as FilterEntry[],
        hasMissingDependency: false,
      };
    }

    const filters: FilterEntry[] = [];
    let missing = false;

    dependsOn.forEach((depKey) => {
      const depVal: any = getFilterValue(depKey);

      const isArray = Array.isArray(depVal);

      if (
        depVal === null ||
        depVal === undefined ||
        (isArray && depVal.length === 0)
      ) {
        missing = true;
        return;
      }

      if (isArray) {
        filters.push({
          key: depKey as WidgetFilterKey,
          operator: Operator.IN,
          value: depVal.map((v: { id: string }) => v.id),
        });
      } else {
        filters.push({
          key: depKey as WidgetFilterKey,
          operator: Operator.EQUALS,
          value: depVal.id ?? depVal,
        });
      }
    });

    return { dependentFilters: filters, hasMissingDependency: missing };
  }, [dependsOn, getFilterValue]);

  const { data, isLoading } = useFieldValue({
    initialData,
    widgetType,
    key: filterKey,
    search: debouncedSearchTerm,
    filters: dependentFilters,
  });

  const options = useMemo(
    () => data?.pages.flatMap((page) => page.values) || [],
    [data],
  );

  const handleChange = (option: DropdownOption | null) => {
    if (!option) {
      return;
    }
    onChange([{ id: option.id as string, name: option.name }]);
  };

  useEffect(() => {
    // if the current value is not in the options, set it to the first option
    if (
      value &&
      !options.some((option) => option.id === (value as [FilterValue])[0].id)
    ) {
      onChange([{ id: options[0].id, name: options[0].name }]);
    }
  }, [options, value, onChange]);

  const isChannel = filterKey === WidgetFilterKey.CHANNEL;

  const dropdownOptions: DropdownOption[] = options.map((option) => {
    return {
      id: option.id,
      name: option.name,
      ...getShouldDisableOption(intl, filterKey, option),
      icon: option.iconUrl ? (
        <VidMobBox
          component="img"
          src={option.iconUrl}
          alt={option.name}
          sx={iconSx}
        />
      ) : isChannel ? (
        <MuiIconForChannel
          platform={option.id}
          shouldShowMetaForFacebook
          sxStyling={{
            height: '20px',
            width: '20px',
          }}
        />
      ) : undefined,
    };
  });

  const selectedOption = value
    ? dropdownOptions.find((option) => {
        if (Array.isArray(value)) {
          return value.some((v) => v.id === option.id);
        }
        return value.id === option.id;
      }) || null
    : null;

  return (
    <VidMobBox sx={{ width: '100%' }}>
      {label && (
        <VidMobTypography variant="subtitle2" sx={{ mb: 4, fontWeight: 500 }}>
          {label}
          {required && (
            <span
              style={{ color: theme.palette.error.main, marginLeft: '4px' }}
            >
              *
            </span>
          )}
        </VidMobTypography>
      )}
      <DropdownSingleSelect
        options={dropdownOptions}
        selectedOption={selectedOption}
        setSelectedOption={handleChange}
        disabled={disabled || isLoading || hasMissingDependency}
        displaySearch={displaySearch}
        autoSelectIfSingleValue={autoSelectIfSingleValue}
        displayActionButtons={false}
        onSearchChange={setSearchTerm}
        customButtonSx={{
          height: '36px',
        }}
      />
    </VidMobBox>
  );
};

export default SingleSelect;
