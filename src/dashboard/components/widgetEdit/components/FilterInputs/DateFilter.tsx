import React, { useState, useRef } from 'react';
import {
  VidMob<PERSON>ox,
  VidMobButton,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { Stack, Typography, useTheme } from '@mui/material';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import { FilterDropdownDateFilter } from '../../../../../muiCustomComponents/Filter/FilterDropdown/FilterDropdownDateFilter';
import { FilterDateValueType } from '../../../../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import {
  CaretDownIcon,
  CaretUpIcon,
} from '../../../../../assets/vidmob-mui-icons/general';
import { useIntl } from 'react-intl';
import { formatDateRange } from '../../widgetEdit.utils';

import { QuickRangeDropdown } from './QuickRangeDropdown';
import {
  DateFilterType,
  DateFilterValue,
  QuickRangeKey,
} from '../../../../types/dashboardTypes';
import { QUICK_RANGES } from '../../../../utils/dateUtils';
import { WIDGET_INTL_KEYS } from '../../../../../widgets/copy/widgetCopy';
import formatMessage from '../../../../../utils/intl';
import { FilterOperator } from '../../../../dashboard.types';

interface DateFilterProps {
  label: string;
  value: DateFilterValue;
  onChange: (value: DateFilterValue, operator?: FilterOperator) => void;
  disabled?: boolean;
  required?: boolean;
  error?: string;
}

const buttonSx = {
  border: '1px solid var(--divider, #BDBDBD)',
  borderRadius: '8px',
  p: '12px 16px',
  height: '36px',
  backgroundColor: 'white',
  '&:hover': {
    borderColor: 'var(--divider, #BDBDBD)',
    backgroundColor: 'var(--background-secondary, #F9FAFB)',
  },
  width: '100%',
  justifyContent: 'space-between',
  textTransform: 'none',
  color: 'black',
} as const;

const caretIconSx = { height: 18, width: 18 };

const DateFilter: React.FC<DateFilterProps> = ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
}) => {
  const theme = useTheme();
  const intl = useIntl();

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [isCustomOpen, setIsCustomOpen] = useState(false);
  const customPickerRef = useRef<HTMLDivElement>(null);

  const openMenu = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled) setAnchorEl(e.currentTarget);
  };

  const choosePreset = (key: QuickRangeKey) =>
    onChange({ type: DateFilterType.PRESET, key }, FilterOperator.AFTER);

  const chooseCustom = () => setIsCustomOpen(true);

  const handleCustomChange = (range: FilterDateValueType) => {
    const [start, end] = range;
    if (start && end) {
      onChange(
        {
          type: DateFilterType.CUSTOM,
          startDate:
            typeof start === 'object'
              ? // @ts-ignore
                start.format('YYYY-MM-DD')
              : start.toString(),
          endDate:
            // @ts-ignore
            typeof end === 'object' ? end.format('YYYY-MM-DD') : end.toString(),
        },
        FilterOperator.BETWEEN,
      );
    }
  };

  const hasValue =
    value.type === DateFilterType.PRESET
      ? !!value.key
      : !!(value.startDate && value.endDate);

  const displayLabel =
    value.type === DateFilterType.PRESET
      ? intl.formatMessage({
          id:
            QUICK_RANGES.find((r) => r.key === value.key)?.intlId ??
            WIDGET_INTL_KEYS.dateRange.custom.id,
          defaultMessage:
            QUICK_RANGES.find((r) => r.key === value.key)?.defaultMessage ??
            WIDGET_INTL_KEYS.dateRange.custom.defaultMessage,
        })
      : formatDateRange(
          { startDate: value.startDate, endDate: value.endDate },
          formatMessage,
        );

  return (
    <VidMobBox sx={{ width: '100%' }}>
      {label && (
        <VidMobTypography variant="subtitle2" sx={{ mb: 4, fontWeight: 500 }}>
          {label}
          {required && (
            <span style={{ color: theme.palette.error.main, marginLeft: 4 }}>
              *
            </span>
          )}
        </VidMobTypography>
      )}

      <VidMobBox sx={{ position: 'relative' }} mb={4}>
        <VidMobButton
          onClick={openMenu}
          disabled={disabled}
          sx={buttonSx}
          disableRipple
        >
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
          >
            <Typography
              variant="subtitle2"
              sx={{
                color: hasValue ? 'text.primary' : 'text.secondary',
                textAlign: 'left',
              }}
              noWrap
            >
              {displayLabel}
            </Typography>
            {anchorEl || isCustomOpen ? (
              <CaretUpIcon sx={caretIconSx} />
            ) : (
              <CaretDownIcon sx={caretIconSx} />
            )}
          </Stack>
        </VidMobButton>

        <QuickRangeDropdown
          anchorEl={anchorEl}
          onClose={() => setAnchorEl(null)}
          onSelectPreset={choosePreset}
          onSelectCustom={chooseCustom}
        />

        {isCustomOpen && (
          <ClickAwayListener onClickAway={() => setIsCustomOpen(false)}>
            <VidMobBox ref={customPickerRef}>
              <FilterDropdownDateFilter
                hidePresets
                setIsDropdownOpen={setIsCustomOpen}
                setSelectedDateRange={handleCustomChange}
                selectedDateRange={[
                  value.type === DateFilterType.CUSTOM ? value.startDate : null,
                  value.type === DateFilterType.CUSTOM ? value.endDate : null,
                ]}
              />
            </VidMobBox>
          </ClickAwayListener>
        )}
      </VidMobBox>
    </VidMobBox>
  );
};

export default DateFilter;
