import React from 'react';
import {
  VidMobDivider,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { VidMobBox } from '../../../../../vidMobComponentWrappers';
import { VisualizationType } from '../../../../../widgets/types/visualizationType';
import {
  BarChartHorizontalIcon,
  BarChartVerticalIcon,
  LineChartIcon,
  MetricIcon,
  DonutChartIcon,
  TableIcon,
} from '../../../../../assets/vidmob-mui-icons/general';
import { WIDGET_INTL_KEYS } from '../../../../../widgets/copy/widgetCopy';
import { MessageDescriptor, useIntl } from 'react-intl';

import barPreview from '../../../../../assets/images/BarChart.png';
import donutPreview from '../../../../../assets/images/Donut.png';
import linePreview from '../../../../../assets/images/Line.png';
import columnPreview from '../../../../../assets/images/Column.png';
import metricPreview from '../../../../../assets/images/Metric.png';
import tablePreview from '../../../../../assets/images/Table.png';

interface ChartTypeProps {
  chartType: VisualizationType;
  visualizationTypes: VisualizationType[];
  onChange: (chartType: VisualizationType) => void;
}

const selectorStyle = {
  display: 'flex',
  width: '60px',
  height: '60px',
  padding: '8px',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '4px',
  flexShrink: 0,
  borderRadius: '6px',
  cursor: 'pointer',
};

const selectedStyle = {
  border: '1px solid',
  borderColor: 'primary.main',
  bgcolor: 'primary.light',
};

const hoverStyle = {
  bgcolor: 'primary.light',
  border: 'none',
  color: 'primary.main',
};

const chartTypeToIcon: Partial<Record<VisualizationType, React.ElementType>> = {
  [VisualizationType.bar]: BarChartHorizontalIcon,
  [VisualizationType.line]: LineChartIcon,
  [VisualizationType.donut]: DonutChartIcon,
  [VisualizationType.column]: BarChartVerticalIcon,
  [VisualizationType.metric]: MetricIcon,
  [VisualizationType.table]: TableIcon,
};

const chartTypeToPreview: Partial<Record<VisualizationType, string>> = {
  [VisualizationType.bar]: barPreview,
  [VisualizationType.donut]: donutPreview,
  [VisualizationType.line]: linePreview,
  [VisualizationType.column]: columnPreview,
  [VisualizationType.metric]: metricPreview,
  [VisualizationType.table]: tablePreview,
};

const tooltipKeys: Partial<
  Record<
    VisualizationType,
    {
      title: MessageDescriptor;
      description: MessageDescriptor;
    }
  >
> = {
  [VisualizationType.bar]: WIDGET_INTL_KEYS.chartTooltips.bar,
  [VisualizationType.donut]: WIDGET_INTL_KEYS.chartTooltips.donut,
  [VisualizationType.line]: WIDGET_INTL_KEYS.chartTooltips.line,
  [VisualizationType.column]: WIDGET_INTL_KEYS.chartTooltips.column,
  [VisualizationType.metric]: WIDGET_INTL_KEYS.chartTooltips.metric,
  [VisualizationType.table]: WIDGET_INTL_KEYS.chartTooltips.table,
};

const ChartTypeSelector = ({
  chartType,
  visualizationTypes,
  onChange,
}: ChartTypeProps) => {
  const intl = useIntl();

  return (
    <VidMobBox sx={{ display: 'flex', gap: '8px' }}>
      {visualizationTypes.map((type) => {
        const Icon = chartTypeToIcon[type];
        const isSelected = chartType === type;
        const { title, description } = tooltipKeys[type]!;

        return (
          <VidMobTooltip
            key={type}
            position="above"
            componentsProps={{
              tooltip: {
                sx: {
                  bgcolor: 'background.default',
                  border: '1px solid',
                  borderColor: 'action.focus',
                  borderRadius: '12px',
                  boxShadow: 'none',
                  width: 200,
                  p: 0,
                  boxSizing: 'border-box',
                  overflow: 'hidden',
                },
              },
            }}
            title={
              <VidMobBox>
                <VidMobBox
                  component="img"
                  src={chartTypeToPreview[type]}
                  alt={`${type} preview`}
                  sx={{
                    width: '100%',
                    height: 'auto',
                    margin: 0,
                    display: 'block',
                  }}
                />
                <VidMobDivider
                  sx={{ p: 0, m: 0, borderColor: 'action.focus' }}
                />
                <VidMobBox
                  sx={{
                    p: 8,
                  }}
                >
                  <VidMobTypography variant="subtitle2" color="text.primary">
                    {intl.formatMessage(title)}
                  </VidMobTypography>
                  <VidMobBox sx={{ height: '9px' }} />
                  <VidMobTypography variant="caption" color="text.primary">
                    {intl.formatMessage(description)}
                  </VidMobTypography>
                </VidMobBox>
              </VidMobBox>
            }
          >
            <VidMobBox
              key={type}
              sx={{
                ...selectorStyle,
                color: isSelected ? 'primary.main' : 'text.primary',
                ...(isSelected
                  ? selectedStyle
                  : {
                      '&:hover': hoverStyle,
                    }),
              }}
              onClick={() => onChange(type)}
            >
              {Icon && <Icon sx={{ fontSize: '20px' }} />}
              <VidMobTypography variant="caption">{type}</VidMobTypography>
            </VidMobBox>
          </VidMobTooltip>
        );
      })}
    </VidMobBox>
  );
};

export default ChartTypeSelector;
