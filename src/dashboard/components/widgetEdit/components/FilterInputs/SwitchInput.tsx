import React, { ChangeEvent } from 'react';
import {
  VidMobSwitch,
  VidMobTooltip,
} from '../../../../../vidMobComponentWrappers';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../../vidMobComponentWrappers';
import { InfoFilledIcon } from '../../../../../assets/vidmob-mui-icons/general';

interface SwitchInputProps {
  label: string;
  infoTooltip?: string;
  value: boolean;
  onChange: (value: boolean) => void;
}

const SwitchInput: React.FC<SwitchInputProps> = ({
  label,
  infoTooltip,
  value,
  onChange,
}) => {
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    onChange(event.target.checked);
  };

  return (
    <VidMobStack flexDirection="row" alignItems="center" gap={2}>
      <VidMobSwitch checked={value} onChange={handleChange} />
      <VidMobTypography variant="body2" color="text.primary">
        {label}
      </VidMobTypography>
      {infoTooltip && (
        <VidMobTooltip title={infoTooltip} placement="top">
          <InfoFilledIcon htmlColor="grey" className="small" />
        </VidMobTooltip>
      )}
    </VidMobStack>
  );
};

export default SwitchInput;
