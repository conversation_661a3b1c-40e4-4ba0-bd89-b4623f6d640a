import React from 'react';
import {
  Vid<PERSON>obBox,
  VidMobStack,
  VidMobTypography,
  VidMobTooltip,
} from '../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import {
  FilterField,
  FilterOptions,
} from '../../../../widgets/types/widgetTypes';
import {
  WidgetType,
  WidgetFilterKey,
} from '../../../../widgets/types/widgetTypes';
import {
  DASHBOARD_INTL_KEYS,
  WIDGET_TYPE_SPECIFIC_COPY,
} from '../../../copy/dashboardCopy';
import FilterInput from './FilterInput';
import { InfoFilledIcon } from '../../../../assets/vidmob-mui-icons/general';
import { useTheme } from '@mui/material';

const RequiredFiltersSection = ({
  requiredFilters,
  widgetType,
  getFilterValue,
  updateFilterValue,
  updateProperty,
  getProperty,
  filterValues,
  canResetFilters,
  resetFilters,
}: {
  requiredFilters: FilterField[];
  widgetType: WidgetType;
  getFilterValue: (key: string) => any;
  updateFilterValue: (key: WidgetFilterKey, value: any) => void;
  updateProperty: (property: string, value: any) => void;
  getProperty: (property: string) => any;
  filterValues?: Record<string, FilterOptions>;
  canResetFilters: boolean;
  resetFilters: () => void;
}) => {
  const { formatMessage } = useIntl();
  const theme = useTheme();

  return (
    <VidMobBox>
      <VidMobStack
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        mb={8}
      >
        <VidMobStack flexDirection="row" alignItems="center" gap={2}>
          <VidMobTypography variant="subtitle2">
            {formatMessage(
              WIDGET_TYPE_SPECIFIC_COPY[widgetType]?.requiredFiltersTitle ??
                DASHBOARD_INTL_KEYS.editSidebar.requiredFiltersTitle,
            )}
          </VidMobTypography>
          <VidMobTooltip
            title={formatMessage(
              DASHBOARD_INTL_KEYS.editSidebar.requiredFiltersInfo,
            )}
            placement="top"
          >
            <InfoFilledIcon htmlColor="grey" className="small" />
          </VidMobTooltip>
        </VidMobStack>
        <button
          onClick={resetFilters}
          disabled={!canResetFilters}
          style={{
            color: !canResetFilters
              ? theme.palette.text.disabled
              : theme.palette.primary.main,
            textDecoration: 'underline',
            fontSize: '14px',
            fontWeight: 400,
            cursor: !canResetFilters ? 'default' : 'pointer',
          }}
        >
          {formatMessage(DASHBOARD_INTL_KEYS.editSidebar.resetFilters)}
        </button>
      </VidMobStack>
      <VidMobStack flexDirection="column" gap={8}>
        {requiredFilters.map((filter) => (
          <VidMobBox key={filter.key}>
            <FilterInput
              filter={filter}
              widgetType={widgetType}
              value={getFilterValue(filter.key)}
              updateFilterValue={updateFilterValue}
              updateProperty={updateProperty}
              getProperty={getProperty}
              initialFilterValues={filterValues?.[filter.key]}
            />
          </VidMobBox>
        ))}
      </VidMobStack>
    </VidMobBox>
  );
};

export default RequiredFiltersSection;
