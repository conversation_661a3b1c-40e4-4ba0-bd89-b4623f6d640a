import React from 'react';

import { VidMobCard } from '../../../../vidMobComponentWrappers';
import WidgetVisualization from '../../widget/WidgetVisualization';
import { WidgetVisualizationDto } from '../../../dashboard.types';
import { VisualizationType } from '../../../../widgets/types/visualizationType';
import { WidgetType } from '../../../../widgets/types/widgetTypes';
import { useWidgetEdit } from '../context';
import { useWidgetData } from '../../../queries/useWidgetData';

const WidgetPreviewContent = () => {
  const {
    selectedVisualizationType,
    getFinalWidgetDetails,
    widgetCustomProperties,
    sortBy,
    updateSortBy,
  } = useWidgetEdit();

  const widgetDetails = getFinalWidgetDetails();
  const {
    data: widgetPreview,
    isLoading,
    isError,
    noPermissions,
  } = useWidgetData({
    visualizationType: selectedVisualizationType || VisualizationType.bar,
    widgetType: widgetDetails.widgetType || WidgetType.ASSETS_SCORED,
    filter: widgetDetails.filter,
    widgetCustomProperties,
    dashboardFilter: null,
    sortBy,
  });

  return (
    <VidMobCard
      sx={{ width: '90%', height: '400px', p: 12 }}
      style={{
        borderRadius: '12px',
        boxShadow: '0px 2px 6px 0px rgba(15, 15, 15, 0.08)',
      }}
    >
      <WidgetVisualization
        key={`${selectedVisualizationType}-${JSON.stringify(widgetDetails.filter)}`}
        visualizationType={
          widgetPreview?.visualizationType as VisualizationType
        }
        widgetType={widgetDetails.widgetType}
        widgetProperties={widgetPreview?.data as WidgetVisualizationDto}
        isCompareToPreviousPeriodEnabled={
          widgetDetails.isCompareToPreviousPeriodEnabled
        }
        isViewDataLabelsEnabled={widgetDetails.isViewDataLabelsEnabled}
        isKpiLiftEnabled={widgetDetails.isKpiLiftEnabled}
        sortBy={sortBy}
        onSortChange={updateSortBy}
        isLoading={isLoading}
        isError={isError}
        noPermissions={noPermissions}
        isSortEnabled
      />
    </VidMobCard>
  );
};

export default WidgetPreviewContent;
