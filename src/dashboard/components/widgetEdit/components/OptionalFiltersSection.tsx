import React, { useState } from 'react';
import {
  Vid<PERSON><PERSON><PERSON><PERSON>,
  VidMob<PERSON>tack,
  VidMobT<PERSON>pography,
  VidMobButton,
  VidMobMenu,
  VidMobTooltip,
} from '../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import {
  FilterChangeArgs,
  FilterField,
  FilterInputType,
} from '../../../../widgets/types/widgetTypes';
import {
  WidgetType,
  WidgetFilterKey,
} from '../../../../widgets/types/widgetTypes';
import {
  DASHBOARD_INTL_KEYS,
  WIDGET_TYPE_SPECIFIC_COPY,
} from '../../../copy/dashboardCopy';
import { ValueType, Value } from '../../../../components/ReportFilters/types';
import {
  AddIcon,
  InfoFilledIcon,
  WorkspaceIcon,
  IntegrationIcon,
  DataIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import FilterEntryDataWrapper from './FilterEntryDataWrapper';
import { MenuItem } from '@mui/material';

const FilterToIcon = {
  [WidgetFilterKey.WORKSPACE]: WorkspaceIcon,
  [WidgetFilterKey.CHANNEL]: IntegrationIcon,
  [WidgetFilterKey.BRAND]: DataIcon,
  [WidgetFilterKey.MARKET]: DataIcon,
};

const OptionalFiltersSection = ({
  optionalFilters,
  widgetType,
  getFilterValue,
  updateFilterValue,
  activeOptionalFilters,
  addOptionalFilter,
  removeOptionalFilter,
}: {
  optionalFilters: FilterField[];
  widgetType: WidgetType;
  getFilterValue: (key: string) => any;
  updateFilterValue: (key: WidgetFilterKey, value: any) => void;
  activeOptionalFilters: WidgetFilterKey[];
  addOptionalFilter: (key: WidgetFilterKey) => void;
  removeOptionalFilter: (key: WidgetFilterKey) => void;
}) => {
  const { formatMessage } = useIntl();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const getValueType = (filterType: string): ValueType => {
    switch (filterType) {
      case FilterInputType.MULTI_SELECT:
      case FilterInputType.FILTERED_MULTI_SELECT:
        return ValueType.MULTI;
      case FilterInputType.SINGLE_SELECT:
        return ValueType.SINGLE;
      case FilterInputType.DATE_RANGE:
        return ValueType.RANGE;
      default:
        return ValueType.SINGLE;
    }
  };

  const ensureArray = (val: any): string[] => {
    if (Array.isArray(val)) {
      return val;
    }
    if (val === null || val === undefined) {
      return [];
    }
    return [String(val)];
  };

  const transformValue = (filter: FilterField, value: any): Value => {
    switch (filter.type) {
      case FilterInputType.DATE_RANGE:
        if (value && Array.isArray(value)) {
          return value;
        }
        return [undefined, undefined];
      case FilterInputType.MULTI_SELECT:
      case FilterInputType.FILTERED_MULTI_SELECT:
        return ensureArray(value);
      default:
        return value || '';
    }
  };

  const handleFilterChange = (args: FilterChangeArgs) => {
    const key = args.key as unknown as WidgetFilterKey;

    if (Array.isArray(args.value)) {
      updateFilterValue(key, args.value);
      return;
    }

    updateFilterValue(key, args.value);
  };

  const handleDeleteFilter = (key: string) => {
    removeOptionalFilter(key as WidgetFilterKey);
  };

  const availableFilters = optionalFilters.filter(
    (filter) => !activeOptionalFilters.includes(filter.key as WidgetFilterKey),
  );

  const activeFilters = optionalFilters.filter((filter) =>
    activeOptionalFilters.includes(filter.key as WidgetFilterKey),
  );

  const dropdownButtonSx = {
    mb: '16px',
    height: '36px',
  };

  const dropdownButtonOpenedSx = {
    ...dropdownButtonSx,
    borderRadius: '6px',
    backgroundColor: 'action.hover',
    color: 'primary.main',
    border: '1px solid action.hover',
  };

  const handleAddFilter = (key: WidgetFilterKey) => {
    addOptionalFilter(key);
    setAnchorEl(null);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const filterEntriesSx = {
    gap: 4,
    width: '100%',
    mb: '8px',
  };

  const menuSx = {
    border: '1px solid divider',
  };

  const menuListSx = {
    overflow: 'hidden',
    width: '220px',
    padding: '8px',
    borderRadius: '6px',
  };

  const menuItemSx = {
    borderRadius: '6px',
    height: '36px',
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    gap: '8px',
    '&:hover': {
      backgroundColor: 'background.hover !important',
    },
  };

  return (
    <VidMobBox>
      <VidMobStack
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <VidMobStack flexDirection="row" alignItems="center" gap={2}>
          <VidMobTypography variant="subtitle2">
            {formatMessage(
              DASHBOARD_INTL_KEYS.editSidebar.optionalFiltersTitle,
            )}
          </VidMobTypography>
          <VidMobTooltip
            title={formatMessage(
              DASHBOARD_INTL_KEYS.editSidebar.optionalFiltersInfo,
            )}
            placement="top"
          >
            <InfoFilledIcon htmlColor="grey" className="small" />
          </VidMobTooltip>
        </VidMobStack>
      </VidMobStack>

      <VidMobStack direction="column" sx={filterEntriesSx}>
        {activeFilters.map((filter) => {
          const currentValue = getFilterValue(filter.key);
          const transformedValue = transformValue(filter, currentValue);
          const valueType = getValueType(filter.type);
          const Icon = FilterToIcon[filter.key as keyof typeof FilterToIcon];

          return (
            <VidMobBox key={filter.key}>
              <FilterEntryDataWrapper
                filter={filter}
                icon={
                  Icon ? (
                    <Icon
                      sx={{
                        width: '16px',
                        height: '16px',
                        color: 'text.secondary',
                      }}
                    />
                  ) : undefined
                }
                widgetType={widgetType}
                currentValue={currentValue}
                transformedValue={transformedValue}
                valueType={valueType}
                handleFilterChange={handleFilterChange}
                handleDeleteFilter={handleDeleteFilter}
              />
            </VidMobBox>
          );
        })}
      </VidMobStack>

      <VidMobBox>
        {availableFilters.length > 0 && (
          <>
            <VidMobButton
              onClick={handleMenuOpen}
              variant="text"
              startIcon={<AddIcon />}
              sx={anchorEl ? dropdownButtonOpenedSx : dropdownButtonSx}
              disabled={false}
            >
              <VidMobTypography variant="subtitle2">
                {formatMessage(
                  WIDGET_TYPE_SPECIFIC_COPY[widgetType]?.addOptionalFilter ??
                    DASHBOARD_INTL_KEYS.editSidebar.addOptionalFilter,
                )}
              </VidMobTypography>
            </VidMobButton>

            <VidMobMenu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              elevation={2}
              sx={menuSx}
              MenuListProps={{
                sx: menuListSx,
              }}
            >
              {availableFilters.map((filter) => {
                const Icon =
                  FilterToIcon[filter.key as keyof typeof FilterToIcon];
                return (
                  <MenuItem
                    key={filter.key}
                    onClick={() =>
                      handleAddFilter(filter.key as WidgetFilterKey)
                    }
                    sx={menuItemSx}
                  >
                    {Icon && (
                      <Icon
                        sx={{
                          width: '20px',
                          height: '20px',
                          color: 'text.secondary',
                        }}
                      />
                    )}

                    <VidMobTypography variant="body2">
                      {filter.displayLabel || filter.key}
                    </VidMobTypography>
                  </MenuItem>
                );
              })}
            </VidMobMenu>
          </>
        )}
      </VidMobBox>
    </VidMobBox>
  );
};

export default OptionalFiltersSection;
