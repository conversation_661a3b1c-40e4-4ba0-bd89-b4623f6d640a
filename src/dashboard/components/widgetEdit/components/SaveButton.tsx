import React from 'react';
import { VidMobButton } from '../../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';
import { DASHBOARD_INTL_KEYS } from '../../../copy/dashboardCopy';
import { useWidgetEdit } from '../context';
import { FilterField } from '../../../../widgets/types/widgetTypes';

export const SaveButton = ({
  requiredFilters,
}: {
  requiredFilters?: FilterField[];
}) => {
  const { formatMessage } = useIntl();
  const { saveWidget, isSaving, widgetFilters } = useWidgetEdit();

  const handleSave = async () => {
    try {
      await saveWidget();
    } catch (error) {
      console.error('Save failed:', error);
    }
  };

  const canSave = () => {
    if (!requiredFilters) {
      return true;
    }

    return requiredFilters?.every((filter) => {
      return widgetFilters.find((f) => f.key === filter.key);
    });
  };

  return (
    <VidMobButton
      sx={{ mb: 2 }}
      variant="contained"
      onClick={handleSave}
      disabled={isSaving || !canSave()}
    >
      {isSaving
        ? formatMessage(DASHBOARD_INTL_KEYS.applying)
        : formatMessage(DASHBOARD_INTL_KEYS.apply)}
    </VidMobButton>
  );
};
