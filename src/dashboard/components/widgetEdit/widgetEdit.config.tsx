import React from 'react';
import { IntlShape } from 'react-intl';
import {
  Fi<PERSON><PERSON><PERSON>,
  WidgetFilter<PERSON><PERSON>,
  WidgetType,
} from '../../../widgets/types/widgetTypes';
import SwitchInput from './components/FilterInputs/SwitchInput';
import { DASHBOARD_INTL_KEYS } from '../../copy/dashboardCopy';
import { VisualizationType } from '../../../widgets/types/visualizationType';
import { WidgetCustomProperties } from '../../constants/dashboardConstants';

export const FILTER_EXTRA_OPTIONS = [
  {
    value: WidgetCustomProperties.IS_COMPARE_TO_PREVIOUS_PERIOD_ENABLED,
    shouldShow: (filter: FilterField, widgetType: WidgetType) => {
      return (
        (filter.key === WidgetFilterKey.DATE_RANGE ||
          filter.key === WidgetFilterKey.MEDIA_CREATE_DATE) &&
        (widgetType === WidgetType.ADHERENCE_SCORE ||
          widgetType === WidgetType.CHANNEL_ADHERENCE_TRENDS ||
          widgetType === WidgetType.IMPRESSIONS ||
          widgetType === WidgetType.ASSETS_SCORED ||
          widgetType === WidgetType.MARKET_ADHERENCE ||
          widgetType === WidgetType.BRAND_ADHERENCE)
      );
    },
    component: (
      intl: IntlShape,
      isCompareToPreviousPeriodEnabled: boolean,
      handleCompareToPreviousPeriodChange: (
        property: string,
        value: boolean,
      ) => void,
    ) => (
      <SwitchInput
        label={intl.formatMessage(
          DASHBOARD_INTL_KEYS.editSidebar.compareToPreviousPeriod,
        )}
        value={isCompareToPreviousPeriodEnabled ?? false}
        onChange={(value) =>
          handleCompareToPreviousPeriodChange(
            WidgetCustomProperties.IS_COMPARE_TO_PREVIOUS_PERIOD_ENABLED,
            value,
          )
        }
      />
    ),
  },
  {
    value: WidgetCustomProperties.IS_KPI_LIFT_ENABLED,
    shouldShow: (filter: FilterField, _widgetType: WidgetType) =>
      filter.key === WidgetFilterKey.KPI,
    component: (
      intl: IntlShape,
      value: boolean,
      handleChange: (property: string, value: boolean) => void,
    ) => (
      <SwitchInput
        label={intl.formatMessage(
          DASHBOARD_INTL_KEYS.editSidebar.compareToAssetsThatDidNotMeetCriteria,
        )}
        infoTooltip={intl.formatMessage(
          DASHBOARD_INTL_KEYS.editSidebar
            .compareToAssetsThatDidNotMeetCriteriaTooltip,
        )}
        value={value ?? false}
        onChange={(value) =>
          handleChange(WidgetCustomProperties.IS_KPI_LIFT_ENABLED, value)
        }
      />
    ),
  },
  {
    value: WidgetCustomProperties.IS_INCLUDE_TOTAL_ENABLED,
    shouldShow: (filter: FilterField, widgetType: WidgetType) =>
      widgetType === WidgetType.ADHERENCE_SCORE &&
      filter.key === WidgetFilterKey.ASSET_SOURCE,
    component: (
      intl: IntlShape,
      value: boolean,
      handleChange: (property: string, value: boolean) => void,
    ) => (
      <SwitchInput
        label={intl.formatMessage(
          DASHBOARD_INTL_KEYS.editSidebar.viewAverageMetric,
        )}
        value={value ?? false}
        onChange={(value) =>
          handleChange(WidgetCustomProperties.IS_INCLUDE_TOTAL_ENABLED, value)
        }
      />
    ),
  },
  {
    value: WidgetCustomProperties.IS_COMPARE_TO_AVERAGE_ENABLED,
    shouldShow: (filter: FilterField, widgetType: WidgetType) =>
      widgetType === WidgetType.ASSET_OVERVIEW &&
      filter.key === WidgetFilterKey.KPI,
    component: (
      intl: IntlShape,
      value: boolean,
      handleChange: (property: string, value: boolean) => void,
    ) => (
      <SwitchInput
        label={intl.formatMessage(
          DASHBOARD_INTL_KEYS.editSidebar.compareToAverage,
        )}
        value={value ?? false}
        onChange={(value) =>
          handleChange(
            WidgetCustomProperties.IS_COMPARE_TO_AVERAGE_ENABLED,
            value,
          )
        }
      />
    ),
  },
];

export const VISUALIZATION_EXTRA_OPTIONS = [
  {
    value: WidgetCustomProperties.IS_VIEW_DATA_LABELS_ENABLED,
    shouldShow: (
      visualizationType: VisualizationType,
      _widgetType: WidgetType,
    ) =>
      ![VisualizationType.table, VisualizationType.metric].includes(
        visualizationType,
      ),
    component: (
      intl: IntlShape,
      value: boolean | null,
      handleChange: (property: string, value: boolean) => void,
    ) => (
      <SwitchInput
        label={intl.formatMessage(
          DASHBOARD_INTL_KEYS.editSidebar.viewDataLabels,
        )}
        value={value ?? false}
        onChange={(value) =>
          handleChange(
            WidgetCustomProperties.IS_VIEW_DATA_LABELS_ENABLED,
            value,
          )
        }
      />
    ),
  },
];

export const VISUALIZATIONS_WITH_VIEW_BY = [
  VisualizationType.table,
  VisualizationType.line,
  VisualizationType.column,
];

export const VISUALIZATIONS_WITH_VIEW_DATA_LABELS = [
  VisualizationType.bar,
  VisualizationType.column,
  VisualizationType.line,
  VisualizationType.donut,
];
