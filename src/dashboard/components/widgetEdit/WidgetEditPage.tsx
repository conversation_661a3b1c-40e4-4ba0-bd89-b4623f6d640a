import React from 'react';
import <PERSON>HeaderV2 from '../../../components/PageHeaderV2';
import {
  VidMobBox,
  VidMobButton,
  VidMobCircularProgress,
  VidMobStack,
} from '../../../vidMobComponentWrappers';
import { DASHBOARD_INTL_KEYS } from '../../copy/dashboardCopy';
import { useIntl } from 'react-intl';
import { WidgetEditSidebar } from './components/WidgetEditSidebar';
import { SaveButton } from './components/SaveButton';

import { Widget as WidgetI } from '../../dashboard.types';
import { VisualizationType } from '../../../widgets/types/visualizationType';
import useWidgetType from '../../queries/useWidgetType';
import {
  WidgetType,
  WidgetDetailsResponse,
} from '../../../widgets/types/widgetTypes';
import { WidgetEditProvider } from './context/WidgetEditContext';
import vmErrorLog from '../../../utils/vmErrorLog';
import WidgetPreview from './components/WidgetPreview';

interface WidgetEditPageProps {
  dashboardTitle: string;
  dashboardId: string;
  widget: WidgetI;
  onSave: (widget: WidgetI) => void;
  onCancel: () => void;
}

const WidgetEditPage = ({
  dashboardTitle,
  dashboardId,
  widget,
  onSave,
  onCancel,
}: WidgetEditPageProps) => {
  const { formatMessage } = useIntl();

  const { data: widgetType, isLoading: isWidgetDetailsLoading } = useWidgetType(
    widget.widgetType as WidgetType,
  );

  const handleSaveWidget = async (widgetDetails: WidgetDetailsResponse) => {
    try {
      onSave({
        ...widget,
        filter: widgetDetails.filter,
        visualizationType: widgetDetails.visualizationType,
        isCompareToPreviousPeriodEnabled:
          widgetDetails.isCompareToPreviousPeriodEnabled,
        isViewDataLabelsEnabled: widgetDetails.isViewDataLabelsEnabled,
        isKpiLiftEnabled: widgetDetails.isKpiLiftEnabled,
        isIncludeTotalEnabled: widgetDetails.isIncludeTotalEnabled,
      });
    } catch (error) {
      vmErrorLog(error as Error, 'Failed to save widget');
    }
  };

  const widgetDetailsForProvider: WidgetDetailsResponse = {
    widgetId: widget.id,
    widgetType: widget.widgetType as WidgetType,
    visualizationType: widget.visualizationType,
    filter: (widget.filter as any) || { filters: [] },
    dashboardFilter: { filters: [] }, // TODO: get this from the dashboard
    isCompareToPreviousPeriodEnabled:
      widget.isCompareToPreviousPeriodEnabled || false,
    isViewDataLabelsEnabled: widget.isViewDataLabelsEnabled || false,
    isKpiLiftEnabled: widget.isKpiLiftEnabled || false,
    isIncludeTotalEnabled: widget.isIncludeTotalEnabled || false,
  };

  return (
    <WidgetEditProvider
      widgetDetailsData={widgetDetailsForProvider}
      isLoading={isWidgetDetailsLoading}
      onSave={handleSaveWidget}
    >
      <VidMobBox
        sx={{
          height: '100%',
          overflowY: 'hidden',
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: 0,
          width: '100%',
          backgroundColor: 'background.default',
        }}
      >
        <VidMobBox
          sx={{
            borderBottom: '1px solid',
            borderColor: 'background.secondary',
          }}
        >
          <PageHeaderV2
            title={widgetType?.name ?? ''}
            isEditable={false}
            breadcrumbs={[
              {
                label: 'Home',
                url: '/',
              },
              {
                label: dashboardTitle,
                url: `/executive-dashboard/${dashboardId || ''}`,
                onClick: onCancel,
              },
            ]}
          >
            <VidMobStack
              direction="row"
              spacing={8}
              alignItems="center"
              height="100%"
            >
              <VidMobButton
                sx={{ mb: 2 }}
                variant="text"
                color="primary"
                onClick={onCancel}
              >
                {formatMessage(DASHBOARD_INTL_KEYS.cancel)}
              </VidMobButton>
              <SaveButton requiredFilters={widgetType?.requiredFilters} />
            </VidMobStack>
          </PageHeaderV2>
        </VidMobBox>
        <VidMobStack
          flexDirection="row"
          sx={{
            height: '100%',
            width: '100%',
          }}
        >
          <VidMobBox
            sx={{
              width: '100%',
              height: '100%',
              backgroundColor: 'background.contrast',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <WidgetPreview />
          </VidMobBox>
          <VidMobBox
            sx={{
              width: '480px',
              height: '100%',
              backgroundColor: 'background.default',
              borderLeft: '1px solid',
              borderColor: 'divider',
            }}
          >
            {widgetType && !isWidgetDetailsLoading ? (
              <WidgetEditSidebar
                widgetType={widgetType.widgetType}
                defaultVisualizationType={
                  widgetType?.defaultVisualizationType as VisualizationType
                }
                visualizationTypes={
                  widgetType?.visualizationTypes.map(
                    (type) => type as VisualizationType,
                  ) ?? []
                }
                requiredFilters={widgetType?.requiredFilters ?? []}
                optionalFilters={widgetType?.optionalFilters ?? []}
                filterValues={widgetType?.filterValues}
              />
            ) : (
              <VidMobBox sx={{ width: '100%', height: '100%', p: '24px' }}>
                <VidMobBox
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <VidMobCircularProgress />
                </VidMobBox>
              </VidMobBox>
            )}
          </VidMobBox>
        </VidMobStack>
      </VidMobBox>
    </WidgetEditProvider>
  );
};

export default WidgetEditPage;
