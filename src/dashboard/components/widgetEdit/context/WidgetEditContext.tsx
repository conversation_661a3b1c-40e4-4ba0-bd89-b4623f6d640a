import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  ReactNode,
} from 'react';
import {
  WidgetFilterKey,
  WidgetDetailsResponse,
  FilterEntry,
} from '../../../../widgets/types/widgetTypes';
import { VisualizationType } from '../../../../widgets/types/visualizationType';
import { Operator } from '../../../../components/ReportFilters/types';
import { WidgetCustomProperties } from '../../../constants/dashboardConstants';
import vmErrorLog from '../../../../utils/vmErrorLog';
import {
  sendAppliedDateRangeToGainsight,
  sendAppliedFiltersToGainsight,
} from '../widgetEdit.utils';
import { SortDirection } from '../../../../types/sort.types';

export type SortBy = { sortBy: string; sortOrder: SortDirection };
interface WidgetEditContextType {
  // State
  widgetFilters: FilterEntry[];
  selectedVisualizationType: VisualizationType | null;
  widgetCustomProperties: Partial<Record<WidgetCustomProperties, any>>;
  activeOptionalFilters: WidgetFilterKey[];
  isLoading: boolean;
  isSaving: boolean;
  sortBy: SortBy | null;

  // Methods
  updateFilterValue: (key: WidgetFilterKey, value: any) => void;
  updateVisualizationType: (type: VisualizationType) => void;
  resetFilters: () => void;
  canResetFilters: boolean;
  getFilterValue: (key: string) => any;
  updateProperty: (property: string, value: any) => void;
  getProperty: (property: string) => any;
  getFinalWidgetDetails: () => WidgetDetailsResponse;
  addOptionalFilter: (key: WidgetFilterKey) => void;
  removeOptionalFilter: (key: WidgetFilterKey) => void;
  saveWidget: () => Promise<void>;
  updateSortBy: (field: string | null, order?: 'asc' | 'desc') => void;
}

const WidgetEditContext = createContext<WidgetEditContextType | undefined>(
  undefined,
);

interface WidgetEditProviderProps {
  children: ReactNode;
  widgetDetailsData: WidgetDetailsResponse | null | undefined;
  isLoading?: boolean;
  onSave?: (widgetDetails: WidgetDetailsResponse) => Promise<void>;
}

export const WidgetEditProvider = ({
  children,
  widgetDetailsData,
  isLoading = false,
  onSave,
}: WidgetEditProviderProps) => {
  const [widgetFilters, setWidgetFilters] = useState<FilterEntry[]>([]);
  const [selectedVisualizationType, setSelectedVisualizationType] =
    useState<VisualizationType | null>(null);
  const [widgetCustomProperties, setWidgetCustomProperties] = useState<
    Partial<Record<WidgetCustomProperties, any>>
  >({});
  const [activeOptionalFilters, setActiveOptionalFilters] = useState<
    WidgetFilterKey[]
  >([]);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [sortBy, setSortBy] = useState<SortBy | null>(null);

  useEffect(() => {
    if (widgetDetailsData) {
      if (widgetDetailsData.filter) {
        if (widgetDetailsData.filter.filters) {
          setWidgetFilters(widgetDetailsData.filter.filters);

          const existingFilterFieldNames = widgetDetailsData.filter.filters.map(
            (filter) => filter.key,
          );
          setActiveOptionalFilters(
            existingFilterFieldNames.filter(
              (key) => key !== WidgetFilterKey.DATE_RANGE,
            ),
          );
        }
        if (widgetDetailsData.filter.sortBy) {
          const { sortBy, sortOrder } = widgetDetailsData.filter.sortBy;
          if (sortOrder && typeof sortOrder === 'string') {
            updateSortBy(sortBy, sortOrder.toLowerCase() as 'asc' | 'desc');
          } else {
            updateSortBy(sortBy, 'desc');
          }
        }
      } else {
        setWidgetFilters([]);
        setActiveOptionalFilters([]);
      }
      setSelectedVisualizationType(widgetDetailsData.visualizationType);

      setWidgetCustomProperties({
        [WidgetCustomProperties.IS_COMPARE_TO_PREVIOUS_PERIOD_ENABLED]:
          widgetDetailsData.isCompareToPreviousPeriodEnabled || false,
        [WidgetCustomProperties.IS_VIEW_DATA_LABELS_ENABLED]:
          widgetDetailsData.isViewDataLabelsEnabled || false,
        [WidgetCustomProperties.IS_KPI_LIFT_ENABLED]:
          widgetDetailsData.isKpiLiftEnabled || false,
        [WidgetCustomProperties.IS_INCLUDE_TOTAL_ENABLED]:
          widgetDetailsData.isIncludeTotalEnabled,
      });
    }
  }, [widgetDetailsData]);

  const updateFilterValue = useCallback(
    (key: WidgetFilterKey, value: any, operator?: Operator) => {
      setWidgetFilters((prev) => {
        const existingFilterIndex = prev.findIndex(
          (filter) => filter.key === key,
        );

        if (existingFilterIndex >= 0) {
          const updatedFilters = [...prev];
          const updatingFilter = updatedFilters[existingFilterIndex];
          updatedFilters[existingFilterIndex] = {
            ...updatingFilter,
            value,
            operator: operator || updatingFilter.operator,
          };
          return updatedFilters;
        } else {
          return [
            ...prev,
            {
              key,
              operator: operator || Operator.IN,
              value,
            },
          ];
        }
      });
    },
    [],
  );

  const updateVisualizationType = useCallback((type: VisualizationType) => {
    setSelectedVisualizationType(type);
  }, []);

  const getFilterValue = useCallback(
    (key: string) => {
      const filter = widgetFilters.find((f) => f.key === key);
      return filter?.value;
    },
    [widgetFilters],
  );

  const addOptionalFilter = useCallback((key: WidgetFilterKey) => {
    setActiveOptionalFilters((prev) => {
      if (!prev.includes(key)) {
        return [...prev, key];
      }
      return prev;
    });

    setWidgetFilters((prev) => {
      const existingFilterIndex = prev.findIndex(
        (filter) => filter.key === key,
      );

      if (existingFilterIndex >= 0) {
        return prev;
      }

      return [
        ...prev,
        {
          key,
          operator: Operator.IN,
          value: null,
        },
      ];
    });
  }, []);

  const removeOptionalFilter = useCallback((key: WidgetFilterKey) => {
    setActiveOptionalFilters((prev) => prev.filter((f) => f !== key));
    setWidgetFilters((prev) => prev.filter((filter) => filter.key !== key));
  }, []);

  const resetFilters = useCallback(() => {
    if (widgetDetailsData) {
      setWidgetFilters(widgetDetailsData.filter?.filters || []);
      setSelectedVisualizationType(widgetDetailsData.visualizationType || null);
      setWidgetCustomProperties({
        [WidgetCustomProperties.IS_COMPARE_TO_PREVIOUS_PERIOD_ENABLED]:
          widgetDetailsData.isCompareToPreviousPeriodEnabled || false,
        [WidgetCustomProperties.IS_VIEW_DATA_LABELS_ENABLED]:
          widgetDetailsData.isViewDataLabelsEnabled || false,
        [WidgetCustomProperties.IS_KPI_LIFT_ENABLED]:
          widgetDetailsData.isKpiLiftEnabled,
        [WidgetCustomProperties.IS_INCLUDE_TOTAL_ENABLED]:
          widgetDetailsData.isIncludeTotalEnabled || false,
      });

      const existingFilterFieldNames = (
        widgetDetailsData.filter?.filters || []
      ).map((filter) => filter.key);
      setActiveOptionalFilters(
        existingFilterFieldNames.filter(
          (key) => key !== WidgetFilterKey.DATE_RANGE,
        ),
      );
    } else {
      setWidgetFilters([]);
      setSelectedVisualizationType(null);
      setWidgetCustomProperties({});
      setActiveOptionalFilters([]);
    }
  }, [widgetDetailsData]);

  const canResetFilters = useMemo(() => {
    if (!widgetDetailsData) {
      return false; // No original data to compare against
    }

    // Compare filters
    const originalFilters = widgetDetailsData.filter?.filters || [];
    if (widgetFilters.length !== originalFilters.length) {
      return true;
    }

    // Check if filters are different (deep comparison)
    const filtersChanged = widgetFilters.some((filter, index) => {
      const originalFilter = originalFilters[index];
      return (
        !originalFilter ||
        filter.key !== originalFilter.key ||
        filter.operator !== originalFilter.operator ||
        JSON.stringify(filter.value) !== JSON.stringify(originalFilter.value)
      );
    });

    if (filtersChanged) {
      return true;
    }

    // Compare visualization type
    if (selectedVisualizationType !== widgetDetailsData.visualizationType) {
      return true;
    }

    // Compare custom properties
    const originalIsCompareToPreviousPeriodEnabled =
      widgetDetailsData.isCompareToPreviousPeriodEnabled || false;
    const originalIsViewDataLabelsEnabled =
      widgetDetailsData.isViewDataLabelsEnabled || false;
    const originalIsKpiLiftEnabled =
      widgetDetailsData.isKpiLiftEnabled || false;

    if (
      widgetCustomProperties[
        WidgetCustomProperties.IS_COMPARE_TO_PREVIOUS_PERIOD_ENABLED
      ] !== originalIsCompareToPreviousPeriodEnabled ||
      widgetCustomProperties[
        WidgetCustomProperties.IS_VIEW_DATA_LABELS_ENABLED
      ] !== originalIsViewDataLabelsEnabled ||
      widgetCustomProperties[WidgetCustomProperties.IS_KPI_LIFT_ENABLED] !==
        originalIsKpiLiftEnabled
    ) {
      return true;
    }

    // Compare sortBy
    const originalSortBy = widgetDetailsData.filter?.sortBy;
    if (sortBy || originalSortBy) {
      // If one is null and the other isn't, they're different
      if (!sortBy || !originalSortBy) {
        return true;
      }
      // Compare the actual values (convert to strings for comparison)
      if (
        sortBy.sortBy !== originalSortBy.sortBy ||
        sortBy.sortOrder.toLowerCase() !==
          originalSortBy.sortOrder?.toString().toLowerCase()
      ) {
        return true;
      }
    }

    return false;
  }, [
    widgetDetailsData,
    widgetFilters,
    selectedVisualizationType,
    widgetCustomProperties,
    sortBy,
  ]);

  const updateProperty = useCallback((property: string, value: any) => {
    setWidgetCustomProperties((prev) => ({
      ...prev,
      [property]: value,
    }));
  }, []);

  const getProperty = useCallback(
    (property: string) => {
      return widgetCustomProperties[property as WidgetCustomProperties];
    },
    [widgetCustomProperties],
  );

  const getFinalWidgetDetails = useCallback(() => {
    return {
      filter: {
        filters: widgetFilters.filter(
          (filter) => filter.value !== null && filter.value.length > 0,
        ),
        ...(sortBy ? { sortBy } : {}),
      },
      visualizationType: selectedVisualizationType,
      ...widgetCustomProperties,
      widgetId: widgetDetailsData?.widgetId,
      widgetType: widgetDetailsData?.widgetType,
      dashboardFilter: widgetDetailsData?.dashboardFilter,
    } as WidgetDetailsResponse;
  }, [
    widgetFilters,
    selectedVisualizationType,
    widgetCustomProperties,
    sortBy,
    widgetDetailsData?.widgetId,
    widgetDetailsData?.widgetType,
    widgetDetailsData?.dashboardFilter,
  ]);

  const saveWidget = useCallback(async () => {
    if (!onSave) {
      console.warn('No onSave handler provided to WidgetEditProvider');
      return;
    }

    setIsSaving(true);
    try {
      const widgetDetails = getFinalWidgetDetails();
      await onSave(widgetDetails);

      sendAppliedDateRangeToGainsight(widgetDetails);
      sendAppliedFiltersToGainsight(widgetDetails);
    } catch (error) {
      vmErrorLog(error as Error, 'Failed to save widget');
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [onSave, getFinalWidgetDetails]);

  const updateSortBy = useCallback(
    (field: string | null, order?: 'asc' | 'desc') =>
      setSortBy(
        field
          ? {
              sortBy: field,
              sortOrder: order?.toUpperCase() as SortDirection,
            }
          : null,
      ),
    [],
  );

  const value: WidgetEditContextType = {
    sortBy,
    widgetFilters,
    selectedVisualizationType,
    widgetCustomProperties,
    activeOptionalFilters,
    isLoading,
    isSaving,
    updateFilterValue,
    updateVisualizationType,
    resetFilters,
    canResetFilters,
    getFilterValue,
    updateProperty,
    getProperty,
    getFinalWidgetDetails,
    addOptionalFilter,
    removeOptionalFilter,
    saveWidget,
    updateSortBy,
  };

  return (
    <WidgetEditContext.Provider value={value}>
      {children}
    </WidgetEditContext.Provider>
  );
};

export const useWidgetEdit = (): WidgetEditContextType => {
  const context = useContext(WidgetEditContext);
  if (context === undefined) {
    throw new Error('useWidgetEdit must be used within a WidgetEditProvider');
  }
  return context;
};
