import dayjs from 'dayjs';
import { IntlShape } from 'react-intl';
import {
  DASHBOARD_INTL_KEYS,
  SERVICES_COPY,
  VidmobServices,
} from '../../copy/dashboardCopy';
import { trackCustomEventGainsight } from '../../../utils/gainsight';
import {
  WidgetDetailsResponse,
  WidgetFilterKey,
} from '../../../widgets/types/widgetTypes';
import { QUICK_RANGES } from '../../utils/dateUtils';
import { convertDateToMMMDYYYYString } from '../../../utils/dateRangePickerMUIUtils';

export const formatDateRange = (
  value: {
    startDate?: string;
    endDate?: string;
  },
  formatMessage: IntlShape['formatMessage'],
) => {
  const { startDate, endDate } = value;

  if (!startDate || !endDate) {
    return formatMessage(DASHBOARD_INTL_KEYS.editSidebar.selectDateRange);
  }

  const formatDate = (date: string) => {
    return dayjs(date).format('MMM DD, YYYY');
  };

  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
};

export const sendAppliedDateRangeToGainsight = (
  widgetDetails: WidgetDetailsResponse,
) => {
  const dateFilter = widgetDetails.filter.filters.find(
    (filter) => filter.key === WidgetFilterKey.DATE_RANGE,
  );
  if (!dateFilter) return;

  let dateString;

  const formattedDateValue = QUICK_RANGES.find(
    (range) => range.key === dateFilter.value,
  );

  if (formattedDateValue) {
    dateString = formattedDateValue.defaultMessage; // gainsight so no need for localization
  } else {
    dateString = `${convertDateToMMMDYYYYString(dateFilter.value[0])} - ${convertDateToMMMDYYYYString(dateFilter.value[1])}`;
  }

  trackCustomEventGainsight('Date Range Applied', {
    dateRange: dateString,
    widgetType: widgetDetails.widgetType,
  });
};

export const sendAppliedFiltersToGainsight = (
  widgetDetails: WidgetDetailsResponse,
) => {
  const kpiFilter = widgetDetails.filter.filters.find(
    (filter) => filter.key === WidgetFilterKey.KPI,
  );
  const filtersWithoutDateOrKpi = widgetDetails.filter.filters.filter(
    (widget) =>
      widget.key !== WidgetFilterKey.DATE_RANGE &&
      widget.key !== WidgetFilterKey.KPI,
  );
  const filterValuesArray = filtersWithoutDateOrKpi.map((widget) => {
    if (Array.isArray(widget.value)) {
      const valuesAsNameStrings = widget.value
        .map((value: { name: string }) => value.name)
        .join(' - ');
      return `${widget.key}: ${valuesAsNameStrings}`;
    }
    return `${widget.key}: ${widget.value}`;
  });

  trackCustomEventGainsight('Filter Applied', {
    widgetType: widgetDetails.widgetType,
    chartType: widgetDetails.visualizationType,
    kpi: kpiFilter ? kpiFilter.value.name : undefined,
    filters: filterValuesArray.join(', '),
  });
};

export const getShouldDisableOption = (
  intl: IntlShape,
  filterKey: WidgetFilterKey | string,
  option: {
    spendEnabled: boolean;
    isSubscribed: boolean;
    subscriptionsMissing?: VidmobServices[];
  },
) => {
  if (filterKey === WidgetFilterKey.KPI) {
    return {
      disabled: option.spendEnabled === false,
      disabledReason:
        option.spendEnabled === false
          ? intl.formatMessage(DASHBOARD_INTL_KEYS.editSidebar.kpiSpendDisabled)
          : undefined,
    };
  } else if (filterKey === WidgetFilterKey.WORKSPACE) {
    return {
      disabled: option.isSubscribed === false,
      disabledReason:
        option.isSubscribed === true
          ? undefined
          : intl.formatMessage(
              DASHBOARD_INTL_KEYS.editSidebar.disabledWorkspace,
              {
                // @ts-ignore
                servicesNames: new Intl.ListFormat('en', {
                  style: 'long',
                  type: 'conjunction',
                }).format(
                  option.subscriptionsMissing?.map((service: VidmobServices) =>
                    intl.formatMessage(SERVICES_COPY[service]),
                  ),
                ),
              },
            ),
    };
  }
  return {
    disabled: false,
    disabledReason: undefined,
  };
};
