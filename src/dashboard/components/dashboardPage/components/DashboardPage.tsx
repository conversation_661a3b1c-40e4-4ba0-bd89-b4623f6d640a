import React, { use<PERSON>allback, useMemo, useState } from 'react';
import ReactGridLayout, { WidthProvider } from 'react-grid-layout';
import { useParams } from 'react-router-dom';
import { useNavigate, generatePath } from 'react-router-dom-v5-compat';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  VidMobBox,
  VidMobButton,
  VidMobStack,
  VidMobTooltip,
} from '../../../../vidMobComponentWrappers';
import { useDashboardPage } from '../../../hooks/useDashboardPage';
import { DASHBOARD_INTL_KEYS } from '../../../copy/dashboardCopy';
import Widget from '../../widget/Widget';
import { useDashboardWidgets } from '../../../hooks/useDashboardWidgets';
import PageHeaderV2 from '../../../../components/PageHeaderV2';
import { AddIcon } from '../../../../assets/vidmob-mui-icons/general';
import { useDashboardData } from '../../../queries/useDashboardData';
import { VisualizationType } from '../../../../widgets/types/visualizationType';
import DashboardActionButtons from './DashboardActionButtons';
import { splitLayoutId } from '../../../utils/dashboardUtils';
import { useFavoriteDashboard } from '../../landingPage/hooks/useFavoriteDashboard';
import { useCreateDashboard } from '../../landingPage/hooks/useCreateDashboard';
import { LandingPageDeleteModal } from '../../landingPage/components/LandingPageDeleteModal';
import DashboardWidgetDrawer from './DashboardWidgetDrawer';
import {
  BASE_HEIGHT,
  BASE_WIDTH,
  COLUMNS,
  ROW_HEIGHT,
} from '../../../constants/dashboardConstants';
import {
  ReportFilterInputDto,
  WidgetType,
} from '../../../../widgets/types/widgetTypes';
import { LastUpdatedReason } from '../../../types/dashboardTypes';
import { Widget as IWidget } from '../../../dashboard.types';
import WidgetEditPage from '../../widgetEdit/WidgetEditPage';
import { DashboardDuplicateModal } from './DuplicateDashboardModal';
import siteMap from '../../../../routing/siteMap';
import vmErrorLog from '../../../../utils/vmErrorLog';
import { getWidgetChannels } from '../utils/getWidgetChannels';
import { trackCustomEventGainsight } from '../../../../utils/gainsight';
import {
  PDF_DOWNLOAD_IDS,
  PDF_DOWNLOAD_IDS_TO_IGNORE,
} from '../../../constants/dashboardPDFDownloadConstants';
import DashboardSubheader from './DashboardSubheader';
import DashboardPageBlankState from './DashboardPageBlankState';
import { useExportPageToPDF } from '../../../../hooks/useExportPageAsPDF';
import { useUnsavedChangesPrompt } from '../../../hooks/useUnsavedChangesPrompt';
import UnsavedChangesModal from './DashboardUnsavedChangesDialog';

const GridLayoutWrapped = WidthProvider(ReactGridLayout);
const MIN_GRID_HEIGHT = BASE_HEIGHT * ROW_HEIGHT * 2;

export const DashboardPage: React.FC = () => {
  const { dashboardId } = useParams<{ dashboardId: string }>();
  const navigate = useNavigate();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<string | null>(
    null,
  );
  const [editingWidget, setEditingWidget] = useState<IWidget | null>(null);
  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = useState<
    string | null
  >(null);

  const [lastUpdated, setLastUpdated] = useState<{
    reason: LastUpdatedReason;
    timestamp: number;
  } | null>(null);

  const updateLastUpdated = useCallback((reason: LastUpdatedReason) => {
    setLastUpdated({
      timestamp: Date.now(),
      reason,
    });
  }, []);

  const {
    dashboard,
    saveDashboardMutation,
    dashboardTitle,
    dashboardDescription,
    dashboardSharingScope,
    setDashboardTitle,
    setDashboardDescription,
    setDashboardSharingScope,
  } = useDashboardData(dashboardId, updateLastUpdated);

  const {
    formatMessage,
    drawerOpen,
    toggleDrawer,
    organizationId,
    currentUser,
    setDrawerOpen,
  } = useDashboardPage();

  const {
    layout,
    addWidget,
    deleteWidget,
    duplicateWidget,
    onDrag,
    onResize,
    onDrop,
    handleDrawerDragStart,
    handleDrawerDragEnd,
    handleEditingWidget,
    widgets,
    updateWidgetProperties,
    updateWidget,
    commitLayout,
    onDragStop,
    updateWidgetName,
    isAddDisabled,
    isDraggingWidget,
  } = useDashboardWidgets(dashboard, updateLastUpdated);
  const { onClickExportAsPDF } = useExportPageToPDF({
    pdfTitle: `${dashboardTitle} - dashboard.pdf`,
    elementsIdsToIgnore: PDF_DOWNLOAD_IDS_TO_IGNORE,
    mainContentElementId: PDF_DOWNLOAD_IDS.DASHBOARD_CONTENT,
    errorAlertCopyId: DASHBOARD_INTL_KEYS.actions.downloadFailed.id,
    elementIdToAddBorderAfter: PDF_DOWNLOAD_IDS.HEADER_BORDER,
    pageSubheaderId: PDF_DOWNLOAD_IDS.PAGE_SUBHEADER,
    elementsIdsToUpdateOverflow: [
      PDF_DOWNLOAD_IDS.DASHBOARD_PAGE_PARENT,
      PDF_DOWNLOAD_IDS.DASHBOARD_PAGE_CHILD,
    ],
  });

  const hasUnsavedChanges = useMemo(() => {
    return (
      lastUpdated?.reason && lastUpdated.reason !== LastUpdatedReason.SAVED
    );
  }, [lastUpdated]);

  const { showModal, leaveWithoutSave, stayAndEdit } = useUnsavedChangesPrompt(
    Boolean(hasUnsavedChanges),
  );

  const hasWidgets = widgets?.length > 0 || isDraggingWidget;

  const isOwner = useMemo(() => {
    return currentUser?.id === dashboard?.createdBy?.id;
  }, [currentUser, dashboard]);

  const userCanUpdateDashboard = Boolean(isOwner || !dashboardId);

  const { mutate: toggleFavorite } = useFavoriteDashboard();
  const { mutateAsync: duplicateDashboard } = useCreateDashboard();

  const handleFavorite = () =>
    dashboard &&
    toggleFavorite({
      id: dashboard.id,
      isFavorite: !dashboard.isFavorite,
      organizationId,
    });

  const handleDelete = () => dashboard && setIsDeleteModalOpen(dashboard.id);

  const handleDuplicate = async () => {
    if (hasUnsavedChanges && dashboardId && isOwner) {
      setIsDuplicateModalOpen(dashboardId);
      return;
    } else if (dashboardId) {
      try {
        const { id: newDashboardId } = await duplicateDashboard({
          organizationId,
          payload: { sourceDashboardId: dashboardId },
        });

        navigate(
          generatePath(siteMap.executiveDashboard, {
            dashboardId: newDashboardId,
          }),
        );
      } catch (error) {
        vmErrorLog(
          error as Error,
          'dashboard page',
          'duplicate dashboard error',
        );
      }
    }
  };

  const handleSaveDashboard = () => {
    if (!dashboardId) {
      trackCustomEventGainsight('Dashboard Saved', {
        count: widgets.length,
        types: widgets.map((w) => w.widgetType).join(', '),
      });
    }

    saveDashboardMutation.mutate({ widgets });
  };

  const handleEditWidgetDetails = (widgetId: string) => {
    const widgetToEdit = widgets.find((w) => w.id === widgetId);
    if (widgetToEdit) {
      setDrawerOpen(false);
      setEditingWidget(widgetToEdit);
    }
  };

  const handleSaveWidget = (widget: IWidget) => {
    updateWidget(widget);
    updateLastUpdated(LastUpdatedReason.UPDATED_WIDGET_DETAILS);
    setEditingWidget(null);
  };

  const renderGridItem = useCallback(
    (li: any) => {
      const { widgetId } = splitLayoutId(li.i);
      const dashboardWidget = widgets.find((w) => w.id === widgetId);

      if (!dashboardWidget) return null;
      const itemConfig = {
        ...li,
        isDraggable: userCanUpdateDashboard,
        isResizable: userCanUpdateDashboard,
      };

      return (
        <VidMobBox
          key={li.i}
          data-grid={itemConfig}
          sx={{
            overflow: 'visible',
            position: 'relative',
            cursor: 'pointer',
            '&:hover .widget-delete-button': {
              opacity: 1,
            },
            '& .widget-delete-button': {
              pointerEvents: 'auto',
            },
            // Prevent text selection during drag
            userSelect: 'none',
            '&.react-grid-item.react-grid-placeholder': {
              pointerEvents: 'none',
            },
          }}
        >
          <Widget
            name={dashboardWidget?.name ?? ''}
            dateCreated={dashboardWidget?.dateCreated ?? ''}
            channels={getWidgetChannels(dashboardWidget)}
            widgetId={widgetId}
            widgetType={dashboardWidget?.widgetType as WidgetType}
            widgetParameters={dashboardWidget?.parameters}
            onUpdateWidgetProperties={(widgetId, properties, key) =>
              updateWidgetProperties(widgetId, properties, key)
            }
            visualizationType={
              dashboardWidget?.visualizationType as VisualizationType
            }
            filter={dashboardWidget?.filter}
            dashboardFilter={
              dashboard?.dashboardFilter || ({} as ReportFilterInputDto)
            }
            onDelete={() => deleteWidget(widgetId)}
            onDuplicate={() => duplicateWidget(widgetId)}
            onEditDetails={() => handleEditWidgetDetails(widgetId)}
            setEditing={(isEditing) =>
              isEditing ? handleEditingWidget(widgetId, isEditing) : () => {}
            }
            formatMessage={formatMessage}
            userCanUpdateDashboard={userCanUpdateDashboard}
            onChangeTitle={(newTitle) => updateWidgetName(widgetId, newTitle)}
            isAddDisabled={isAddDisabled}
            isCompareToPreviousPeriodEnabled={
              dashboardWidget?.isCompareToPreviousPeriodEnabled
            }
            isViewDataLabelsEnabled={dashboardWidget?.isViewDataLabelsEnabled}
            isKpiLiftEnabled={dashboardWidget?.isKpiLiftEnabled}
            isIncludeTotalEnabled={dashboardWidget?.isIncludeTotalEnabled}
          />
        </VidMobBox>
      );
    },
    [dashboard, deleteWidget, widgets],
  );

  return (
    <VidMobBox
      id={PDF_DOWNLOAD_IDS.DASHBOARD_CONTENT}
      sx={{
        display: 'flex',
        width: '100%',
        height: '100%',
        '& .react-grid-placeholder, & .react-grid-dropping-placeholder': {
          borderRadius: '6px',
          bgcolor: (theme) => `${theme.palette.primary.light} !important`,
          border: (theme) =>
            `1px solid ${theme.palette.primary.main} !important`,
          opacity: 0.5,
        },
      }}
    >
      {userCanUpdateDashboard && (
        <DashboardWidgetDrawer
          open={drawerOpen}
          onToggle={toggleDrawer}
          onAdd={addWidget}
          onDragStart={handleDrawerDragStart}
          onDragEnd={handleDrawerDragEnd}
          isAddDisabled={isAddDisabled}
        />
      )}

      <VidMobBox
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0,
        }}
      >
        <PageHeaderV2
          customTitlePlaceholder={formatMessage(
            DASHBOARD_INTL_KEYS.dashboardTitlePlaceholder,
          )}
          title={dashboardTitle}
          subtitle={dashboardDescription}
          onTitleChange={setDashboardTitle}
          onSubtitleChange={setDashboardDescription}
          isEditable={userCanUpdateDashboard}
          breadcrumbs={[
            {
              label: formatMessage({ id: 'home.section.togo.home.title' }),
              url: '/',
            },
          ]}
        >
          <VidMobStack
            id={PDF_DOWNLOAD_IDS.PAGE_HEADER_ACTION_BUTTONS}
            direction="row"
            spacing={8}
            alignItems="center"
          >
            <DashboardActionButtons
              isFavorite={dashboard?.isFavorite ?? false}
              onFavorite={handleFavorite}
              onDownloadPdf={() => {
                if (drawerOpen) {
                  toggleDrawer();
                }
                onClickExportAsPDF();
              }}
              onDuplicate={handleDuplicate}
              onDelete={handleDelete}
              dashboardId={dashboardId}
              setSharingScope={setDashboardSharingScope}
              sharingScope={dashboardSharingScope}
              ownerId={dashboard?.createdBy?.id}
            />
            <VidMobTooltip
              position="above"
              title={
                userCanUpdateDashboard
                  ? ''
                  : formatMessage(DASHBOARD_INTL_KEYS.saveDisabledTooltip)
              }
            >
              <span>
                <VidMobButton
                  variant="contained"
                  onClick={handleSaveDashboard}
                  disabled={
                    saveDashboardMutation.status === 'loading' ||
                    !hasUnsavedChanges ||
                    !userCanUpdateDashboard
                  }
                >
                  {dashboardId
                    ? formatMessage(DASHBOARD_INTL_KEYS.update)
                    : formatMessage(DASHBOARD_INTL_KEYS.save)}
                </VidMobButton>
              </span>
            </VidMobTooltip>
          </VidMobStack>
        </PageHeaderV2>

        <VidMobBox sx={{ px: 12 }} id={PDF_DOWNLOAD_IDS.PAGE_SUBHEADER}>
          <DashboardSubheader
            owner={dashboard?.createdBy}
            lastUpdated={dashboard?.lastUpdated}
          />
          <VidMobTooltip
            title={
              !userCanUpdateDashboard
                ? formatMessage(DASHBOARD_INTL_KEYS.widgetDrawerDisabledTooltip)
                : ''
            }
          >
            <span>
              <VidMobButton
                id={PDF_DOWNLOAD_IDS.ADD_WIDGET_BUTTON}
                sx={{
                  height: 32,

                  backgroundColor: 'inherit',
                  '&:hover': {
                    color: 'primary.main',
                    borderColor: 'transparent',
                    backgroundColor: 'action.hover',
                  },
                }}
                variant="outlined"
                startIcon={<AddIcon sx={{ fontSize: 16 }} />}
                onClick={toggleDrawer}
                disabled={!userCanUpdateDashboard}
              >
                {formatMessage(DASHBOARD_INTL_KEYS.widgetDrawerOpen)}
              </VidMobButton>
            </span>
          </VidMobTooltip>
        </VidMobBox>
        <VidMobBox id={PDF_DOWNLOAD_IDS.HEADER_BORDER} />
        <VidMobBox sx={{ flexGrow: 1 }}>
          {hasWidgets ? (
            <GridLayoutWrapped
              layout={layout}
              cols={COLUMNS}
              rowHeight={ROW_HEIGHT}
              draggableHandle=".widget-drag-handle"
              style={{ minHeight: MIN_GRID_HEIGHT, padding: '6px 12px' }}
              droppingItem={{ i: '__DROP__', w: BASE_WIDTH, h: BASE_HEIGHT }}
              onDrop={onDrop}
              onDrag={onDrag}
              onResize={onResize}
              resizeHandles={['se']}
              onLayoutChange={commitLayout}
              onDragStop={onDragStop}
              isDroppable={userCanUpdateDashboard}
              isDraggable={userCanUpdateDashboard}
              isResizable={userCanUpdateDashboard}
            >
              {layout.map(renderGridItem)}
            </GridLayoutWrapped>
          ) : (
            <DashboardPageBlankState />
          )}
        </VidMobBox>
      </VidMobBox>
      <LandingPageDeleteModal
        isDeleteModalOpen={isDeleteModalOpen}
        organizationId={organizationId}
        setIsDeleteModalOpen={setIsDeleteModalOpen}
        formatMessage={formatMessage}
        dashboardName={dashboard?.name}
      />
      {editingWidget && (
        <WidgetEditPage
          key={editingWidget.id}
          dashboardTitle={dashboardTitle}
          dashboardId={dashboardId}
          widget={editingWidget}
          onSave={handleSaveWidget}
          onCancel={() => setEditingWidget(null)}
        />
      )}
      <DashboardDuplicateModal
        isDuplicateModalOpen={isDuplicateModalOpen}
        organizationId={organizationId}
        setIsDuplicateModalOpen={setIsDuplicateModalOpen}
        formatMessage={formatMessage}
      />
      <UnsavedChangesModal
        isOpen={showModal}
        leaveWithoutSave={leaveWithoutSave}
        stayAndEdit={stayAndEdit}
        formatMessage={formatMessage}
      />
    </VidMobBox>
  );
};
