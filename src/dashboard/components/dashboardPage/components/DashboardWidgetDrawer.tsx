import React from 'react';
import { DRAWER_WIDTH } from '../../../constants/dashboardConstants';
import { AddWidgetCard } from './AddWidgetCard';
import {
  Vid<PERSON>ob<PERSON><PERSON><PERSON>,
  VidMob<PERSON>con<PERSON>utton,
  <PERSON>id<PERSON>ob<PERSON>ist,
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { DASHBOARD_INTL_KEYS } from '../../../copy/dashboardCopy';
import { useDashboardPage } from '../../../hooks/useDashboardPage';
import {
  CollapsePanelLeftIcon,
  TextIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import { controlBarButtonSx } from '../../../../components/ReportFilters/styles';
import { WidgetType } from '../../../../widgets/types/widgetTypes';
import { useWidgetInfoList } from '../../../queries/useWidgetInfoList';
import { PDF_DOWNLOAD_IDS } from '../../../constants/dashboardPDFDownloadConstants';
import { getWidgetIcon } from '../../../constants/widgetConstants';

interface Props {
  open: boolean;
  onToggle: () => void;
  onAdd: (type: WidgetType) => void;
  onDragStart: (type: WidgetType) => void;
  onDragEnd: () => void;
  isAddDisabled: boolean;
}

const DashboardWidgetDrawer: React.FC<Props> = ({
  open,
  onToggle,
  onAdd,
  onDragStart,
  onDragEnd,
  isAddDisabled,
}) => {
  const { formatMessage } = useDashboardPage();

  const { data: widgetInfoList } = useWidgetInfoList();

  return (
    <VidMobDrawer
      id={PDF_DOWNLOAD_IDS.ADD_WIDGET_DRAWER}
      variant="persistent"
      anchor="left"
      open={open}
      sx={{
        width: open ? DRAWER_WIDTH : 0,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          padding: '16px',
          width: DRAWER_WIDTH,
          boxSizing: 'border-box',
          position: 'relative',
          zIndex: 7,
        },
      }}
    >
      <VidMobStack
        direction="column"
        gap="16px"
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          justifyContent: 'center',
          mb: '16px',
        }}
      >
        <VidMobIconButton
          onClick={onToggle}
          sx={{ ...controlBarButtonSx, width: '32px', height: '32px' }}
        >
          <CollapsePanelLeftIcon />
        </VidMobIconButton>
        <VidMobTypography variant="subtitle1">
          {formatMessage(DASHBOARD_INTL_KEYS.widgetDrawerOpen)}
        </VidMobTypography>
      </VidMobStack>
      <VidMobList sx={{ padding: 0 }}>
        {widgetInfoList?.map((widget) => (
          <AddWidgetCard
            key={widget.widgetType}
            onAdd={() => onAdd(widget.widgetType)}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
            title={widget.name}
            icon={getWidgetIcon(widget.widgetType, 'default') ?? TextIcon}
            description={widget.description}
            widgetType={widget.widgetType}
            isAddDisabled={isAddDisabled}
          />
        ))}
      </VidMobList>
    </VidMobDrawer>
  );
};

export default DashboardWidgetDrawer;
