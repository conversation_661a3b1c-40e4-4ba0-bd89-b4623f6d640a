import React from 'react';
import CustomDialog from '../../../../muiCustomComponents/CustomDialog';
import {
  VidMobStack,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { IntlShape } from 'react-intl';
import { DASHBOARD_INTL_KEYS } from '../../../copy/dashboardCopy';

type Props = {
  isOpen: boolean;
  stayAndEdit: () => void;
  leaveWithoutSave: () => void;
  formatMessage: IntlShape['formatMessage'];
};

const UnsavedChangesModal: React.FC<Props> = ({
  isOpen,
  stayAndEdit,
  leaveWithoutSave,
  formatMessage,
}) => (
  <CustomDialog
    isOpen={isOpen}
    onClose={leaveWithoutSave}
    onSubmit={stayAndEdit}
    bodyChildren={
      <VidMobStack gap={4}>
        <VidMobTypography variant="h6">
          {formatMessage(DASHBOARD_INTL_KEYS.unsavedModal.title)}
        </VidMobTypography>
        <VidMobTypography variant="body2">
          {formatMessage(DASHBOARD_INTL_KEYS.unsavedModal.description)}
        </VidMobTypography>
      </VidMobStack>
    }
    cancelButtonLabel={formatMessage(
      DASHBOARD_INTL_KEYS.unsavedModal.leaveButton,
    )}
    submitButtonLabel={formatMessage(
      DASHBOARD_INTL_KEYS.unsavedModal.stayButton,
    )}
  />
);

export default UnsavedChangesModal;
