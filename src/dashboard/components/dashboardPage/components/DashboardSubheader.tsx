import React from 'react';
import { useIntl } from 'react-intl';
import { User } from '../../../dashboard.types';
import {
  VidMobAvatar,
  VidMobBox,
  VidMobStack,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import DateSection from '../../../../components/ReportPageHeaderReportDetails/DateSection';

const CREATED_BY_KEY = 'ui.reports.header.createdBy';

const containerSx = {
  gap: '6px',
  pb: '12px',
};

const avatarSx = {
  width: '20px',
  height: '20px',
  borderRadius: '50%',
  fontSize: '12px',
  backgroundColor: 'icon.secondary',
};

const boxSx = {
  borderRadius: '50%',
  width: '4px',
  height: '4px',
  backgroundColor: 'icon.secondary',
};

interface OwnerProps {
  owner?: User;
  lastUpdated?: string;
}

const DashboardSubheader: React.FC<OwnerProps> = ({ owner, lastUpdated }) => {
  const intl = useIntl();

  if (!owner || !lastUpdated) {
    return null;
  }

  const displayText = `${intl.messages[CREATED_BY_KEY]} ${owner.displayName}`;

  return (
    <VidMobStack direction="row" sx={containerSx} alignItems="center">
      <VidMobAvatar
        src={owner.photoUrl}
        alt={owner.displayName}
        sx={avatarSx}
      />
      <VidMobTooltip title={displayText} placement="top">
        <VidMobTypography
          variant="caption"
          color="text.secondary"
          sx={{
            maxWidth: '50%',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
          }}
          noWrap
        >
          {displayText}
        </VidMobTypography>
      </VidMobTooltip>
      <VidMobBox sx={boxSx} />
      <DateSection lastUpdated={lastUpdated} />
    </VidMobStack>
  );
};

export default DashboardSubheader;
