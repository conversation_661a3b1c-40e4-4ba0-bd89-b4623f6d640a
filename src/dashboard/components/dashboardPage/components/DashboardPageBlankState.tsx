import React from 'react';
import { useIntl } from 'react-intl';
import { BlankOrErrorState } from '../../../../muiCustomComponents/BlankState/BlankState';
import { VidMobBox } from '../../../../vidMobComponentWrappers';
import { DASHBOARD_INTL_KEYS } from '../../../copy/dashboardCopy';
import WidgetBlankStateIcon from '../../../../assets/images/widget-blank-state.png';

const DashboardPageBlankState = () => {
  const intl = useIntl();

  const icon = (
    <VidMobBox
      sx={{ height: '92px', width: '280px' }}
      component="img"
      src={WidgetBlankStateIcon}
    />
  );

  return (
    <VidMobBox
      sx={{
        maxWidth: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <BlankOrErrorState
        iconComponent={icon}
        title={intl.formatMessage(DASHBOARD_INTL_KEYS.blankState.title)}
        message={intl.formatMessage(DASHBOARD_INTL_KEYS.blankState.description)}
        messageStyle={{ width: '100%', maxWidth: '280px' }}
      />
    </VidMobBox>
  );
};

export default DashboardPageBlankState;
