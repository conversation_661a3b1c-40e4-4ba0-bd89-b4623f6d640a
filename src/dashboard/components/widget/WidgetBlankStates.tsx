import React from 'react';
import { IntlShape, useIntl } from 'react-intl';
import { BlankOrErrorState } from '../../../muiCustomComponents/BlankState/BlankState';
import { WIDGET_INTL_KEYS } from '../../../widgets/copy/widgetCopy';
import { VidMobBox } from '../../../vidMobComponentWrappers';

interface WidgetBlankStateProps {
  isError?: boolean;
  noPermissions?: boolean;
  noData?: boolean;
}

const WidgetBlankState = ({
  isError = false,
  noPermissions = false,
  noData = false,
}: WidgetBlankStateProps) => {
  const intl = useIntl();
  const { title, description } = getTitleAndDescription(
    intl,
    isError,
    noPermissions,
    noData,
  );
  return (
    <VidMobBox
      sx={{
        maxWidth: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <BlankOrErrorState
        stateType="error"
        title={title}
        message={description}
        messageStyle={{ width: '100%', maxWidth: '248px' }}
      />
    </VidMobBox>
  );
};

export default WidgetBlankState;

const getTitleAndDescription = (
  intl: IntlShape,
  isError: boolean,
  noPermissions: boolean,
  noData: boolean,
) => {
  if (noPermissions) {
    return {
      title: intl.formatMessage(WIDGET_INTL_KEYS.noPermissions.title),
      description: intl.formatMessage(
        WIDGET_INTL_KEYS.noPermissions.description,
      ),
    };
  }

  if (noData) {
    return {
      title: intl.formatMessage(WIDGET_INTL_KEYS.noWidgetData.title),
      description: intl.formatMessage(
        WIDGET_INTL_KEYS.noWidgetData.description,
      ),
    };
  }

  if (isError) {
    return {
      title: intl.formatMessage(WIDGET_INTL_KEYS.error.title),
      description: intl.formatMessage(WIDGET_INTL_KEYS.error.description),
    };
  }

  return {
    title: intl.formatMessage(WIDGET_INTL_KEYS.noData.title),
    description: intl.formatMessage(WIDGET_INTL_KEYS.noData.description),
  };
};
