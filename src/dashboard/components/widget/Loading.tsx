import React, { useEffect, useState } from 'react';
import {
  VidMobBox,
  VidMobCircularProgress,
  VidMobTypography,
} from '../../../vidMobComponentWrappers';
import { useIntl } from 'react-intl';

const LOADING_TEXTS_KEYS = [
  'ui.dashboard.loading.text.1',
  'ui.dashboard.loading.text.2',
  'ui.dashboard.loading.text.3',
  'ui.dashboard.loading.text.4',
  'ui.dashboard.loading.text.5',
  'ui.dashboard.loading.text.6',
];

const LOADING_TEXT_INTERVAL = 5000;

function Loading() {
  const intl = useIntl();
  const [loadingText, setLoadingText] = useState(
    intl.formatMessage({ id: LOADING_TEXTS_KEYS[0] }),
  );

  useEffect(() => {
    const interval = setInterval(() => {
      setLoadingText(
        intl.formatMessage({
          id: LOADING_TEXTS_KEYS[
            Math.floor(Math.random() * LOADING_TEXTS_KEYS.length)
          ],
        }),
      );
    }, LOADING_TEXT_INTERVAL);

    return () => clearInterval(interval);
  }, []);

  return (
    <VidMobBox
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <VidMobCircularProgress />
      <VidMobTypography
        variant="subtitle3"
        align="center"
        sx={{ width: '100%' }}
      >
        {loadingText}
      </VidMobTypography>
    </VidMobBox>
  );
}

export default Loading;
