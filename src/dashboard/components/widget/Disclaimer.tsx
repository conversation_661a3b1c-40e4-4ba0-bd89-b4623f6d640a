import React from 'react';
import { InfoFilledIcon } from '../../../assets/vidmob-mui-icons/general';
import { VidMobBox, VidMobTypography } from '../../../vidMobComponentWrappers';
import { WidgetType } from '../../../widgets/types/widgetTypes';
import { useIntl } from 'react-intl';
import { WIDGET_INTL_KEYS } from '../../../widgets/copy/widgetCopy';

const disclaimerWidgetTypes = new Set<WidgetType>([
  WidgetType.ASSETS_SCORED,
  WidgetType.IMPRESSIONS,
]);

export const Disclaimer: React.FC<{ widgetType: WidgetType }> = ({
  widgetType,
}) => {
  const intl = useIntl();
  if (!disclaimerWidgetTypes.has(widgetType)) return null;

  return (
    <VidMobBox
      sx={{
        display: 'flex',
        gap: 4,
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <InfoFilledIcon
        sx={{ color: 'icon.secondary', height: '14px', width: '14px' }}
      />
      <VidMobTypography variant="caption" color="icon.secondary">
        {intl.formatMessage(WIDGET_INTL_KEYS.dislaimer)}
      </VidMobTypography>
    </VidMobBox>
  );
};
