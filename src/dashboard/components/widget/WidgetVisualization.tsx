import React from 'react';
import { WidgetVisualizationDto } from '../../dashboard.types';
import { VisualizationType } from '../../../widgets/types/visualizationType';
import { WidgetType } from '../../../widgets/types/widgetTypes';
import { SortBy } from '../widgetEdit/context/WidgetEditContext';
import WidgetBlankState from './WidgetBlankStates';
import Loading from './Loading';
import { isEmptyVisualization } from '../../utils/isEmptyVisualization';
import { visualizationRenderers } from './WidgetVisualizationRenderer';
import { Disclaimer } from './Disclaimer';
import { VidMobBox } from '../../../vidMobComponentWrappers';

type WidgetVisualizationProps = {
  visualizationType: VisualizationType;
  widgetProperties: WidgetVisualizationDto;
  widgetType: WidgetType;
  isCompareToPreviousPeriodEnabled?: boolean;
  isKpiLiftEnabled?: boolean;
  isViewDataLabelsEnabled?: boolean;
  isSortEnabled?: boolean;
  sortBy?: SortBy | null;
  onSortChange?: (field: string | null, order?: 'asc' | 'desc') => void;
  isLoading: boolean;
  isError: boolean;
  noPermissions: boolean;
};

export default function WidgetVisualization(props: WidgetVisualizationProps) {
  const {
    visualizationType,
    widgetType,
    widgetProperties,
    isCompareToPreviousPeriodEnabled,
    isViewDataLabelsEnabled,
    isKpiLiftEnabled,
    isSortEnabled,
    sortBy,
    onSortChange,
    isLoading,
    isError,
    noPermissions,
  } = props;

  if (isLoading) return <Loading />;
  if (isError)
    return <WidgetBlankState isError noPermissions={noPermissions} />;
  if (isEmptyVisualization(visualizationType, widgetProperties)) {
    return <WidgetBlankState noData />;
  }

  const Renderer = visualizationRenderers[visualizationType];
  if (!Renderer) {
    return null;
  }

  return (
    <VidMobBox
      sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}
    >
      <VidMobBox sx={{ flex: 1, minHeight: 0, overflow: 'auto' }}>
        <Renderer
          widgetType={widgetType}
          widgetProperties={widgetProperties}
          isCompareToPreviousPeriodEnabled={Boolean(
            isCompareToPreviousPeriodEnabled,
          )}
          isViewDataLabelsEnabled={Boolean(isViewDataLabelsEnabled)}
          isKpiLiftEnabled={Boolean(isKpiLiftEnabled)}
          isSortEnabled={Boolean(isSortEnabled)}
          sortBy={sortBy}
          onSortChange={onSortChange}
        />
      </VidMobBox>
      <Disclaimer widgetType={widgetType} />
    </VidMobBox>
  );
}
