import React, { useState } from 'react';
import {
  VidMobBox,
  VidMobIconButton,
  VidMobTextField,
  VidMobTypography,
  VidMobTooltip,
} from '../../../../vidMobComponentWrappers';
import {
  DragIcon,
  EditFilledIcon,
  EllipseIcon,
} from '../../../../assets/vidmob-mui-icons/general';
import OverflowTip from '../../../../muiCustomComponents/OverflowTip';
import MoreActionsButton from '../MoreActionsButton';
import { WidgetType } from '../../../../widgets/types/widgetTypes';
import { IntlShape } from 'react-intl';
import { WIDGET_INTL_KEYS } from '../../../../widgets/copy/widgetCopy';
import { DateFilterValue } from '../../../types/dashboardTypes';
import { getDateRangeLabel } from '../../../utils/dateUtils';
import { WidgetDetailsTooltip } from './WidgetDetailsTooltip';
import { WidgetFilter } from '../../../dashboard.types';
import { useWidgetInfo } from '../../../queries/useWidgetInfo';
import { getWidgetIcon } from '../../../constants/widgetConstants';
import { PDF_DOWNLOAD_IDS } from '../../../constants/dashboardPDFDownloadConstants';
import MuiIconForChannel from '../../../../assets/vidmob-mui-icons/channels/MuiIconForChannel';

const ROW_HEIGHT = 32;
const DRAG_ICON_SIZE = 20;
const WIDGET_ICON_SIZE = 20;
const ICON_GAP = 4;
const DRAG_OVERLAY_WIDTH = DRAG_ICON_SIZE + ICON_GAP + WIDGET_ICON_SIZE;

interface WidgetTitleProps {
  widgetType: WidgetType;
  filter: WidgetFilter;
  name: string;
  channels: string[];
  onChangeTitle: (title: string) => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onEditDetails: () => void;
  userCanUpdateDashboard: boolean;
  formatMessage: IntlShape['formatMessage'];
  isAddDisabled: boolean;
  dateRange?: DateFilterValue;
  isLoading: boolean;
}

const WidgetDetails: React.FC<WidgetTitleProps> = ({
  widgetType,
  filter,
  name,
  channels,
  onChangeTitle,
  onDelete,
  onDuplicate,
  onEditDetails,
  userCanUpdateDashboard,
  formatMessage,
  isAddDisabled,
  dateRange,
  isLoading,
}) => {
  const [editedTitle, setEditedTitle] = useState(name);
  const [isEditing, setIsEditing] = useState(false);

  const { data: widgetInfo } = useWidgetInfo(widgetType);

  const WidgetIcon = getWidgetIcon(widgetType, 'secondary');
  const dateRangeLabel = getDateRangeLabel(dateRange, formatMessage);
  const hasChannels = channels && channels.length > 0;
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setEditedTitle(e.target.value);

  const handleSave = () => {
    if (editedTitle !== name) {
      onChangeTitle(editedTitle);
    }
    setIsEditing(false);
  };

  const renderChannels = () => {
    return channels.map((channel) => {
      return (
        <span key={channel}>
          <MuiIconForChannel
            platform={channel}
            sxStyling={{
              width: 14,
              height: 14,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            shouldShowMetaForFacebook={true}
          />
        </span>
      );
    });
  };

  return (
    <VidMobBox
      sx={{
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        height: ROW_HEIGHT,
        width: '100%',
        flexWrap: 'nowrap',
        overflow: 'hidden',
      }}
    >
      {userCanUpdateDashboard && (
        <VidMobTooltip
          title={formatMessage(WIDGET_INTL_KEYS.drag.tooltip)}
          position="above"
        >
          <VidMobBox
            className="widget-drag-handle"
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: DRAG_OVERLAY_WIDTH,
              height: ROW_HEIGHT,
              cursor: 'move',
            }}
          />
        </VidMobTooltip>
      )}
      <VidMobBox
        sx={{
          width: WIDGET_ICON_SIZE,
          height: ROW_HEIGHT,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
          mr: 4,
          position: 'relative',
          pointerEvents: 'none',
        }}
      >
        <VidMobBox
          sx={{
            position: 'absolute',
            inset: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'opacity 120ms',
            opacity: 1,
            pointerEvents: 'none',
            ...(userCanUpdateDashboard && {
              '.react-grid-item:hover &': { opacity: 0 },
            }),
          }}
        >
          {WidgetIcon && <WidgetIcon sx={{ width: 20, height: 20 }} />}
        </VidMobBox>
        {userCanUpdateDashboard && (
          <VidMobBox
            sx={{
              position: 'absolute',
              inset: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'opacity 120ms',
              opacity: 0,
              '.react-grid-item:hover &': { opacity: 1 },
            }}
          >
            <DragIcon />
          </VidMobBox>
        )}
      </VidMobBox>

      <VidMobBox sx={{ flexGrow: 1, minWidth: 0, overflow: 'hidden' }}>
        {isEditing ? (
          <VidMobTextField
            value={editedTitle}
            onChange={handleTitleChange}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter') handleSave();
              if (e.key === 'Escape') {
                setEditedTitle(name);
                setIsEditing(false);
              }
            }}
            autoFocus
            disabled={!userCanUpdateDashboard}
            sx={{
              maxWidth: '70%',
              width: '100%',
              '& input': {
                p: '4px 8px',
              },
            }}
          />
        ) : (
          <VidMobBox
            onClick={() => userCanUpdateDashboard && setIsEditing(true)}
            sx={{
              cursor: userCanUpdateDashboard ? 'pointer' : 'default',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              display: 'flex',
              flexDirection: 'column',
              minWidth: 0,
            }}
          >
            <OverflowTip variant="subtitle2">{name}</OverflowTip>
            <VidMobBox
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                flexWrap: 'nowrap',
                overflow: 'hidden',
                minWidth: 0,
              }}
            >
              {dateRangeLabel && (
                <>
                  <VidMobTypography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {dateRangeLabel}
                  </VidMobTypography>
                </>
              )}
              {hasChannels && (
                <VidMobBox display="flex" alignItems="center">
                  <EllipseIcon
                    sx={{
                      fontSize: 8,
                      color: 'text.secondary',
                      transform: 'translateY(1px)',
                    }}
                  />
                  <VidMobBox
                    sx={{
                      display: 'flex',
                      gap: 2,
                      flexShrink: 0,
                      marginLeft: '6px',
                    }}
                  >
                    {renderChannels()}
                  </VidMobBox>
                </VidMobBox>
              )}
              <WidgetDetailsTooltip widgetInfo={widgetInfo} filter={filter}>
                <VidMobBox id={PDF_DOWNLOAD_IDS.WIDGET_DETAILS_LINK}>
                  <EllipseIcon
                    sx={{
                      fontSize: 8,
                      color: 'text.secondary',
                      transform: 'translateY(1px)',
                    }}
                  />
                  <VidMobTypography
                    variant="caption"
                    sx={{
                      color: 'text.secondary',
                      textDecoration: 'underline',
                      marginLeft: '6px',
                      cursor: 'pointer',
                      '&:hover': { color: 'primary.main' },
                    }}
                  >
                    {formatMessage(WIDGET_INTL_KEYS.details)}
                  </VidMobTypography>
                </VidMobBox>
              </WidgetDetailsTooltip>
            </VidMobBox>
          </VidMobBox>
        )}
      </VidMobBox>

      <VidMobBox sx={{ pl: 4, display: 'flex', alignItems: 'center', gap: 4 }}>
        <VidMobIconButton
          id={PDF_DOWNLOAD_IDS.WIDGET_EDIT_BUTTON}
          onClick={onEditDetails}
          disabled={!userCanUpdateDashboard || isLoading}
          size="small"
        >
          <EditFilledIcon sx={{ width: 16, height: 16 }} />
        </VidMobIconButton>

        <MoreActionsButton
          onDuplicate={onDuplicate}
          onDelete={onDelete}
          userCanUpdateDashboard={userCanUpdateDashboard}
          isDuplicateDisabled={isAddDisabled}
          isLoading={isLoading}
        />
      </VidMobBox>
    </VidMobBox>
  );
};

export default WidgetDetails;
