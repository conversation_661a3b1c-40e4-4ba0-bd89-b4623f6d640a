import React from 'react';
import { CalendarIcon } from '../../../../assets/vidmob-mui-icons/general';
import { VidMobTypography } from '../../../../vidMobComponentWrappers';
import { VidMobBox } from '../../../../vidMobComponentWrappers';
import { DateFilterType, DateFilterValue } from '../../../types/dashboardTypes';
import {
  checkIfDateValueIsPreset,
  getDateRangeLabel,
} from '../../../utils/dateUtils';
import { useIntl } from 'react-intl';

const DateValueDisplay = ({ value }: { value: string | string[] }) => {
  const intl = useIntl();

  const isPreset = checkIfDateValueIsPreset(value);

  const dateValue = isPreset
    ? { type: DateFilterType.PRESET, key: value }
    : {
        type: DateFilterType.CUSTOM,
        startDate: value[0],
        endDate: value[1],
      };

  return (
    <VidMobBox sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
      <CalendarIcon sx={{ width: '14px', height: '14px' }} />
      <VidMobTypography variant="caption">
        {getDateRangeLabel(dateValue as DateFilterValue, intl.formatMessage)}
      </VidMobTypography>
    </VidMobBox>
  );
};

export default DateValueDisplay;
