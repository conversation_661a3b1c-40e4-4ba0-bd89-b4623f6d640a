import React from 'react';
import {
  VidMobBox,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { FilterValue } from '../../../../widgets/types/widgetTypes';

const ArrayValueDisplay = ({
  value,
  getIcon,
}: {
  value: FilterValue[];
  getIcon?: (value: FilterValue) => React.ReactNode;
}) => {
  return (
    <VidMobTypography
      variant="body1"
      sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}
    >
      {value.map((v, idx) => (
        <React.Fragment key={v.id}>
          <VidMobBox
            sx={{ display: 'inline-flex', alignItems: 'center', gap: '4px' }}
            component="span"
          >
            {getIcon && getIcon(v)}
            <VidMobTypography variant="caption">{v.name}</VidMobTypography>
          </VidMobBox>
          {idx < value.length - 1 && (
            <VidMobTypography variant="caption" sx={{ mx: 0.5 }}>
              {', '}
            </VidMobTypography>
          )}
        </React.Fragment>
      ))}
    </VidMobTypography>
  );
};

export default ArrayValueDisplay;
