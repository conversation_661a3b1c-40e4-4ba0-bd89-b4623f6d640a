import React, { useMemo } from 'react';
import {
  Vid<PERSON>ob<PERSON>ox,
  VidMobCard,
  VidMobDivider,
  VidMobTooltip,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { WidgetFilter, WidgetTypeInfo } from '../../../dashboard.types';
import { WidgetFilterKey } from '../../../../widgets/types/widgetTypes';
import getMUIIconForChannel from '../../../../assets/vidmob-mui-icons/channels/getMUIIconForChannel';
import { getPlatformIdentifierForLogo } from '../../../../utils/feConstantsUtils';
import ArrayValueDisplay from './ArrayValueDisplay';
import DateValueDisplay from './DateValueDisplay';

const getChannelIcon = (platform: string) => {
  const iconIdentifier = getPlatformIdentifierForLogo(platform);
  return getMUIIconForChannel(iconIdentifier, {
    height: '14px',
    width: '14px',
  });
};

export const WidgetDetailsTooltip = ({
  widgetInfo,
  filter,
  children,
}: {
  children: React.ReactElement;
  widgetInfo?: WidgetTypeInfo;
  filter?: WidgetFilter;
}) => {
  const filterMap = useMemo(
    () => new Map(filter?.filters?.map((f) => [f.key, f.value]) ?? []),
    [filter],
  );

  if (!widgetInfo) {
    return null;
  }
  const { requiredFilters, optionalFilters } = widgetInfo;

  const hasOptionalFilters = optionalFilters?.some((fd) => {
    const v = filterMap.get(fd.key);
    return Array.isArray(v) ? v.length > 0 : !!v;
  });

  return (
    <VidMobTooltip
      placement="bottom-start"
      componentsProps={{
        tooltip: {
          sx: { bgcolor: 'transparent', boxShadow: 'none', p: 0 },
        },
      }}
      title={
        <VidMobCard
          sx={{
            p: '16px',
            maxHeight: '360px',
            width: '260px',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
          }}
        >
          <VidMobTypography variant="subtitle2">
            {widgetInfo.name}
          </VidMobTypography>
          <VidMobBox
            sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}
          >
            {requiredFilters.map((filterDefinition) => {
              const filterValue = filterMap.get(filterDefinition.key);

              const isDateFilter =
                filterDefinition.key === WidgetFilterKey.DATE_RANGE ||
                filterDefinition.key === WidgetFilterKey.MEDIA_CREATE_DATE;
              return (
                <VidMobBox key={filterDefinition.key}>
                  <VidMobTypography variant="subtitle2" fontSize="12px">
                    {filterDefinition.displayLabel}
                  </VidMobTypography>
                  <VidMobTypography variant="caption">
                    {isDateFilter ? (
                      <DateValueDisplay value={filterValue} />
                    ) : Array.isArray(filterValue) ? (
                      <ArrayValueDisplay
                        value={filterValue}
                        getIcon={(value) => {
                          if (
                            filterDefinition.key === WidgetFilterKey.CHANNEL
                          ) {
                            const channelIcon = getChannelIcon(value.id);

                            return channelIcon;
                          }
                          return null;
                        }}
                      />
                    ) : (
                      filterValue?.name
                    )}
                  </VidMobTypography>
                </VidMobBox>
              );
            })}
          </VidMobBox>
          {hasOptionalFilters && (
            <>
              <VidMobDivider />
              <VidMobBox
                sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}
              >
                {optionalFilters.map((filterDefinition) => {
                  const filterValue = filterMap.get(filterDefinition.key);

                  if (!filterValue) {
                    return null;
                  }

                  return (
                    <VidMobBox key={filterDefinition.key}>
                      <VidMobTypography variant="subtitle2" fontSize="12px">
                        {filterDefinition.displayLabel}
                      </VidMobTypography>
                      <ArrayValueDisplay value={filterValue} />
                    </VidMobBox>
                  );
                })}
              </VidMobBox>
            </>
          )}
        </VidMobCard>
      }
    >
      {children}
    </VidMobTooltip>
  );
};
