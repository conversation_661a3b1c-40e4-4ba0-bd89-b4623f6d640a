import React from 'react';
import {
  VidMobBox,
  VidMobIconButton,
  VidMobMenu,
  VidMobMenuItem,
  VidMobTooltip,
  VidMobTypography,
} from '../../../vidMobComponentWrappers';
import {
  CopyIcon,
  DeleteFilledIcon,
  MoreVerticalIcon,
} from '../../../assets/vidmob-mui-icons/general';
import { DASHBOARD_INTL_KEYS } from '../../copy/dashboardCopy';
import { useIntl } from 'react-intl';
import { PDF_DOWNLOAD_IDS } from '../../constants/dashboardPDFDownloadConstants';

const rowActionStyles = {
  padding: '8px',
  borderRadius: '6px',
  minWidth: '167px',
};

const iconStyles = {
  height: 20,
  width: 20,
  color: 'icon.secondary',
  mr: 4,
};

type Props = {
  onDuplicate: () => void;
  onDelete: () => void;
  userCanUpdateDashboard: boolean;
  isDuplicateDisabled: boolean;
  isLoading: boolean;
};

const MoreActionsButton: React.FC<Props> = ({
  onDuplicate,
  onDelete,
  userCanUpdateDashboard,
  isDuplicateDisabled,
  isLoading,
}) => {
  const { formatMessage } = useIntl();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(e.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const options = [
    {
      label: formatMessage(DASHBOARD_INTL_KEYS.duplicate),
      disabled: isDuplicateDisabled,
      disabledTooltipKey: DASHBOARD_INTL_KEYS.addDisabled,
      icon: <CopyIcon sx={iconStyles} />,
      onClick: () => {
        handleClose();
        onDuplicate();
      },
    },
    {
      label: formatMessage(DASHBOARD_INTL_KEYS.delete),
      icon: <DeleteFilledIcon sx={iconStyles} />,
      onClick: () => {
        handleClose();
        onDelete();
      },
    },
  ];

  return (
    <VidMobBox id={PDF_DOWNLOAD_IDS.WIDGET_DROPDOWN_BUTTON}>
      <VidMobTooltip
        title={
          userCanUpdateDashboard
            ? ''
            : formatMessage(DASHBOARD_INTL_KEYS.noEditAccess)
        }
      >
        <span>
          <VidMobIconButton
            size="small"
            onClick={handleClick}
            disabled={!userCanUpdateDashboard || isLoading}
          >
            <MoreVerticalIcon />
          </VidMobIconButton>
        </span>
      </VidMobTooltip>

      <VidMobMenu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        {options.map(
          ({ label, icon, onClick, disabled, disabledTooltipKey }) => (
            <VidMobTooltip
              key={label}
              position="above"
              title={
                disabled && disabledTooltipKey
                  ? formatMessage(disabledTooltipKey)
                  : ''
              }
            >
              <div>
                <VidMobMenuItem
                  key={label}
                  sx={rowActionStyles}
                  onClick={onClick}
                  disabled={disabled}
                >
                  {icon}
                  <VidMobTypography variant="body2">{label}</VidMobTypography>
                </VidMobMenuItem>
              </div>
            </VidMobTooltip>
          ),
        )}
      </VidMobMenu>
    </VidMobBox>
  );
};

export default MoreActionsButton;
