import React from 'react';
import {
  MultiSeries<PERSON><PERSON>Dto,
  DonutChartDto,
  MultiSeriesChartType,
  MetricDto,
  TableDto,
  WidgetVisualizationDto,
} from '../../dashboard.types';
import MultiSeriesChart from '../visualizations/MultiSeriesChart';
import Donut<PERSON>hart from '../visualizations/DonutChart';
import TableVisualization from '../visualizations/Table/Table';
import CriteriaPerformanceTable from '../visualizations/Table/CriteriaPerformanceTable';
import MetricVisualization from '../visualizations/Metric';
import { VisualizationType } from '../../../widgets/types/visualizationType';
import { WidgetType } from '../../../widgets/types/widgetTypes';
import { SortBy } from '../widgetEdit/context/WidgetEditContext';

export type RendererProps = {
  widgetType: WidgetType;
  widgetProperties: WidgetVisualizationDto;
  isCompareToPreviousPeriodEnabled: boolean;
  isViewDataLabelsEnabled: boolean;
  isKpiLiftEnabled: boolean;
  isSortEnabled: boolean;
  sortBy?: SortBy | null;
  onSortChange?: (field: string | null, order?: 'asc' | 'desc') => void;
};

export const visualizationRenderers: Partial<
  Record<VisualizationType, React.FC<RendererProps>>
> = {
  [VisualizationType.bar]: ({
    widgetProperties,
    isCompareToPreviousPeriodEnabled,
    isViewDataLabelsEnabled,
  }) => {
    const dto = widgetProperties as MultiSeriesChartDto;
    return (
      <MultiSeriesChart
        chartType={MultiSeriesChartType.BAR}
        categories={dto.categories}
        series={dto.series}
        unitLabelValues={dto.unitLabelValues}
        previousCategories={dto.previousCategories}
        horizontal
        currency={dto.currency}
        showDataLabels={isViewDataLabelsEnabled}
        isCompareToPreviousPeriodEnabled={isCompareToPreviousPeriodEnabled}
      />
    );
  },

  [VisualizationType.column]: ({
    widgetProperties,
    isCompareToPreviousPeriodEnabled,
    isViewDataLabelsEnabled,
  }) => {
    const dto = widgetProperties as MultiSeriesChartDto;
    return (
      <MultiSeriesChart
        chartType={MultiSeriesChartType.COLUMN}
        categories={dto.categories}
        series={dto.series}
        unitLabelValues={dto.unitLabelValues}
        previousCategories={dto.previousCategories}
        currency={dto.currency}
        showDataLabels={isViewDataLabelsEnabled}
        isCompareToPreviousPeriodEnabled={isCompareToPreviousPeriodEnabled}
      />
    );
  },

  [VisualizationType.line]: ({
    widgetProperties,
    isCompareToPreviousPeriodEnabled,
    isViewDataLabelsEnabled,
  }) => {
    const dto = widgetProperties as MultiSeriesChartDto;
    return (
      <MultiSeriesChart
        chartType={MultiSeriesChartType.LINE}
        categories={dto.categories}
        series={dto.series}
        unitLabelValues={dto.unitLabelValues}
        previousCategories={dto.previousCategories}
        currency={dto.currency}
        showDataLabels={isViewDataLabelsEnabled}
        isCompareToPreviousPeriodEnabled={isCompareToPreviousPeriodEnabled}
      />
    );
  },

  [VisualizationType.donut]: ({
    widgetProperties,
    isViewDataLabelsEnabled,
  }) => {
    const dto = widgetProperties as DonutChartDto;
    return (
      <DonutChart
        values={dto.values}
        total={dto.total}
        unitLabel={dto.unitLabel}
        currency={dto.currency}
        reportPeriodLabel={dto.reportPeriodLabel}
        previousPeriodLabel={dto.previousPeriodLabel}
        showDataLabels={isViewDataLabelsEnabled}
        showLegend
      />
    );
  },

  [VisualizationType.table]: (props) => {
    const dto = props.widgetProperties as TableDto;
    const Table =
      props.widgetType === WidgetType.CRITERIA_PERFORMANCE
        ? CriteriaPerformanceTable
        : TableVisualization;

    return (
      <Table
        columns={dto.columns}
        data={dto.rows.map((r, i) => ({ id: `${r.value}-${i}`, ...r }))}
        isCompareToPreviousPeriodEnabled={
          props.isCompareToPreviousPeriodEnabled
        }
        isSortEnabled={props.isSortEnabled}
        sortBy={props.sortBy}
        onSortChange={props.onSortChange}
        isKpiLiftEnabled={props.isKpiLiftEnabled}
      />
    );
  },

  [VisualizationType.metric]: ({
    widgetProperties,
    isCompareToPreviousPeriodEnabled,
  }) => {
    const dto = widgetProperties as MetricDto;
    return (
      <MetricVisualization
        values={dto.values}
        unitLabel={dto.unitLabel}
        currency={dto.currency}
        isCompareToPreviousPeriodEnabled={isCompareToPreviousPeriodEnabled}
        changeDirection={dto.changeDirection}
      />
    );
  },
};
