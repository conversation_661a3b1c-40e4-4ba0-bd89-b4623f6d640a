import React, { useMemo } from 'react';
import {
  TextWidgetDto,
  WidgetVisualizationDto,
  WidgetFilter,
} from '../../dashboard.types';
import { useWidgetData } from '../../queries/useWidgetData';
import Text from '../visualizations/Text';
import { VisualizationType } from '../../../widgets/types/visualizationType';
import {
  ReportFilterInputDto,
  WidgetFilterKey,
  WidgetKeyEnum,
  WidgetType,
} from '../../../widgets/types/widgetTypes';
import {
  VidMobBox,
  VidMobCard,
  VidMobStack,
  VidMobTooltip,
} from '../../../vidMobComponentWrappers';
import WidgetDetails from './WidgetDetails/WidgetDetails';
import MoreActionsButton from './MoreActionsButton';
import { DragIcon } from '../../../assets/vidmob-mui-icons/general';
import { WIDGET_INTL_KEYS } from '../../../widgets/copy/widgetCopy';
import { IntlShape } from 'react-intl';
import { extractDateFilterValue } from '../../utils/dateUtils';
import WidgetVisualization from './WidgetVisualization';

type WidgetProps = {
  name: string;
  dateCreated: string;
  channels: string[];
  widgetId: string;
  widgetType: WidgetType;
  visualizationType: VisualizationType;
  filter: WidgetFilter | null;
  dashboardFilter: ReportFilterInputDto | null;
  isCompareToPreviousPeriodEnabled?: boolean;
  isViewDataLabelsEnabled?: boolean;
  isKpiLiftEnabled?: boolean;
  isIncludeTotalEnabled?: boolean;
  onDelete: (widgetId: string) => void;
  onDuplicate: (widgetId: string) => void;
  setEditing: (isEditing: boolean) => void;
  widgetParameters?: WidgetVisualizationDto;
  userCanUpdateDashboard: boolean;
  formatMessage: IntlShape['formatMessage'];
  onUpdateWidgetProperties: (
    widgetId: string,
    properties: WidgetVisualizationDto,
    key: WidgetKeyEnum,
  ) => void;
  onChangeTitle: (title: string) => void;
  isAddDisabled: boolean;
  onEditDetails: (widgetId: string) => void;
};

const Widget = (props: WidgetProps) => {
  const {
    name,
    channels,
    widgetId,
    widgetType,
    visualizationType,
    filter,
    dashboardFilter,
    isCompareToPreviousPeriodEnabled = false,
    isViewDataLabelsEnabled = false,
    isKpiLiftEnabled = false,
    isIncludeTotalEnabled = false,
    onDelete,
    onDuplicate,
    setEditing,
    widgetParameters,
    onUpdateWidgetProperties,
    formatMessage,
    userCanUpdateDashboard,
    onChangeTitle,
    isAddDisabled,
    onEditDetails,
  } = props;

  const { data, isLoading, isError, noPermissions } = useWidgetData({
    widgetId,
    widgetType,
    visualizationType,
    filter,
    dashboardFilter,
    widgetCustomProperties: {
      isCompareToPreviousPeriodEnabled,
      isViewDataLabelsEnabled,
      isKpiLiftEnabled,
      isIncludeTotalEnabled,
    },
  });

  const widgetProperties = data?.data as WidgetVisualizationDto;
  const isTextWidget = visualizationType === VisualizationType.text;

  const handleDelete = () => {
    onDelete(widgetId);
  };

  const handleDuplicate = () => {
    onDuplicate(widgetId);
  };

  const handleEdit = () => {
    onEditDetails(widgetId);
  };

  const shouldRenderDetails = useMemo(() => {
    return visualizationType !== VisualizationType.text;
  }, [visualizationType]);

  const renderBody = () => {
    if (isTextWidget) {
      const text = (widgetParameters as TextWidgetDto)?.content ?? '';
      return (
        <VidMobBox
          sx={{
            width: '100%',
            height: '100%',
            padding: 2,
            paddingTop: 4,
            flexDirection: 'row',
            display: 'flex',
          }}
        >
          <VidMobBox
            sx={{
              flexGrow: 1,
              overflow: 'hidden',
              height: '100%',
              '&:hover': {
                overflow: 'auto',
              },
            }}
          >
            <Text
              userCanUpdateDashboard={userCanUpdateDashboard}
              onEditText={(text) => {
                setEditing(false);
                onUpdateWidgetProperties(
                  widgetId,
                  { content: text },
                  WidgetKeyEnum.PARAMETERS,
                );
              }}
              onEdit={setEditing}
              key={widgetId}
            >
              {text}
            </Text>
          </VidMobBox>
        </VidMobBox>
      );
    }

    return (
      <WidgetVisualization
        visualizationType={visualizationType}
        widgetType={widgetType}
        widgetProperties={widgetProperties}
        isCompareToPreviousPeriodEnabled={isCompareToPreviousPeriodEnabled}
        isViewDataLabelsEnabled={isViewDataLabelsEnabled}
        isKpiLiftEnabled={isKpiLiftEnabled}
        isSortEnabled={false}
        sortBy={filter?.sortBy}
        isError={isError}
        isLoading={isLoading}
        noPermissions={noPermissions}
      />
    );
  };

  return (
    <VidMobCard
      elevation={0}
      sx={{
        gap: 12,
        display: 'flex',
        flexDirection: 'column',
        p: '24px',
        width: '100%',
        height: '100%',
        border: '1px solid',
        borderColor: 'secondary.main',
        borderRadius: '8px',
        overflow: 'hidden',
        backgroundColor: isTextWidget
          ? 'background.contrast'
          : 'background.default',
      }}
    >
      {shouldRenderDetails ? (
        <VidMobBox>
          <WidgetDetails
            key={name}
            isLoading={isLoading}
            widgetType={widgetType}
            filter={filter ?? { filters: [] }}
            name={name}
            channels={channels}
            onChangeTitle={onChangeTitle}
            onDelete={handleDelete}
            onDuplicate={handleDuplicate}
            userCanUpdateDashboard={userCanUpdateDashboard}
            formatMessage={formatMessage}
            isAddDisabled={isAddDisabled}
            onEditDetails={handleEdit}
            dateRange={extractDateFilterValue(
              filter?.filters?.find(
                (f) =>
                  f.key === WidgetFilterKey.DATE_RANGE ||
                  f.key === WidgetFilterKey.MEDIA_CREATE_DATE,
              ),
            )}
          />
        </VidMobBox>
      ) : (
        <VidMobStack direction="row" justifyContent="space-between">
          {userCanUpdateDashboard ? (
            <VidMobTooltip
              position="above"
              title={formatMessage(WIDGET_INTL_KEYS.drag.tooltip)}
            >
              <VidMobBox
                className="widget-drag-handle"
                sx={{
                  width: 36,
                  height: 24,
                  display: 'grid',
                  placeItems: 'center',
                  cursor: 'move',
                  ml: '-8px',
                  opacity: 0,
                  transition: 'opacity 120ms',
                  '.react-grid-item:hover &': { opacity: 1 },
                }}
              >
                <DragIcon pointerEvents="none" />
              </VidMobBox>
            </VidMobTooltip>
          ) : (
            <VidMobBox sx={{ width: 36, height: 24 }} />
          )}

          <MoreActionsButton
            onDuplicate={handleDuplicate}
            onDelete={handleDelete}
            userCanUpdateDashboard={userCanUpdateDashboard}
            isDuplicateDisabled={isAddDisabled}
            isLoading={isLoading}
          />
        </VidMobStack>
      )}

      <VidMobBox
        sx={{
          flex: 1,
          minHeight: 0,
          overflow: 'auto',
          position: 'relative',
        }}
      >
        {renderBody()}
      </VidMobBox>
    </VidMobCard>
  );
};

export default Widget;
