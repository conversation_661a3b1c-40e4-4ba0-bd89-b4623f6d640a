import React, { useRef } from 'react';
import useCriteriaDetailsPopoverV2 from '../../../creativeScoring/components/shared/CriteriaDetailsPopover.tx/useCriteriaDetailsPopoverV2';
import { BestPracticeIcon } from '../../../assets/vidmob-mui-icons/general';
import { VidMobBox, VidMobTypography } from '../../../vidMobComponentWrappers';
import { CriteriaDetailsCriterionData } from '../../../creativeScoring/components/shared/CriteriaDetailsPopover.tx/criteriaDetailsPopover.types';
import { IdAndName } from '../../../types/common.types';

export type RawCriterion = Omit<CriteriaDetailsCriterionData, 'workspaces'> & {
  defaultDisplayName: string;
  workspaces?: IdAndName[];
};

type Props = { criterion: RawCriterion };

const CriteriaCell = ({ criterion }: Props) => {
  const criteriaCellRef = useRef<HTMLDivElement>(null);
  const currentCriteriaInfoRowRef = criteriaCellRef?.current;

  const rect = currentCriteriaInfoRowRef?.getBoundingClientRect();

  const { renderPopover, handlePopoverOpen, handlePopoverClose } =
    useCriteriaDetailsPopoverV2({
      criterion: {
        ...criterion,
        workspaces: criterion.workspaces
          ? criterion.workspaces.map((w) => w.name).join(', ')
          : '',
      },
      customAnchor: {
        anchorReference: 'anchorPosition',
        anchorPosition: {
          top: (rect?.top || 0) - 21,
          left: (rect?.left || 0) + 150,
        },
      },
    });

  return (
    <>
      <VidMobBox
        ref={criteriaCellRef}
        component="span"
        onMouseEnter={handlePopoverOpen}
        onMouseLeave={handlePopoverClose}
        onBlur={handlePopoverClose}
        sx={{
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          overflow: 'hidden',
          flexGrow: 1,
          minWidth: 0,
        }}
      >
        <VidMobTypography variant="body2" noWrap>
          {criterion.defaultDisplayName || criterion.name}
        </VidMobTypography>

        {criterion.isBestPractice && (
          <BestPracticeIcon
            sx={{ ml: 4, flexShrink: 0, width: 16, height: 16 }}
          />
        )}
      </VidMobBox>

      {renderPopover()}
    </>
  );
};

export default CriteriaCell;
