import React, {
  FC,
  useMemo,
  useRef,
  useState,
  useLayoutEffect,
  useEffect,
} from 'react';
import Highcharts, { Series } from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useTheme } from '@mui/material/styles';
import { VidMobBox, VidMobTypography } from '../../../vidMobComponentWrappers';

import { MetricWidgetItem, WidgetFormatTypeEnum } from '../../dashboard.types';
import { formatWidgetValue } from '../../utils/formatValues';
import { makeDonutTooltipFormatter } from '../../utils/tooltipUtils';
import { buildLegend } from './utils/chartHelpers';
import { useIntl } from 'react-intl';
import { Currency } from '../../../types/currency.types';

interface DonutChartProps {
  values: MetricWidgetItem[];
  total?: MetricWidgetItem;
  unitLabel: WidgetFormatTypeEnum;
  reportPeriodLabel?: string;
  previousPeriodLabel?: string;
  showLegend?: boolean;
  showDataLabels?: boolean;
  currency?: Currency;
}

const hasCenter = (s: Series): s is Series & { center: [number, number] } =>
  Array.isArray((s as any).center);

const DonutChart: FC<DonutChartProps> = ({
  values,
  total,
  unitLabel,
  showLegend = true,
  showDataLabels,
  currency,
}) => {
  const intl = useIntl();
  const theme = useTheme();
  const colors = theme.palette.chart.seriesColors;

  // Prepare total value and series data from values
  const { totalValue, seriesData } = useMemo(() => {
    const totalValue =
      total?.value ?? values.reduce((sum, v) => sum + v.value, 0);
    const seriesData = values.map((v, idx) => ({
      name: v.label,
      y: v.value,
      color: colors[idx % colors.length],
      custom: { iconUrl: v.iconUrl },
    }));
    return { totalValue, seriesData };
  }, [values, total, colors]);

  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<HighchartsReact.RefObject>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });

  useLayoutEffect(() => {
    if (!containerRef.current) return;
    const ro = new ResizeObserver((e) => {
      const { width, height } = e[0].contentRect;
      setSize({ width, height });
    });
    ro.observe(containerRef.current);
    return () => ro.disconnect();
  }, []);

  useEffect(() => {
    const hc = chartRef.current?.chart;
    if (
      hc &&
      size.width &&
      size.height &&
      (hc.chartWidth !== size.width || hc.chartHeight !== size.height)
    ) {
      hc.setSize(size.width, size.height, false);
    }
  }, [size]);

  const [center, setCenter] = useState<{ x: number; y: number } | null>(null);

  useEffect(() => {
    const chart = chartRef.current?.chart;
    if (!chart) return;

    const update = () => {
      const s0 = chart.series[0];
      if (!hasCenter(s0)) return;
      const [cx, cy] = s0.center;
      const pos = { x: chart.plotLeft + cx, y: chart.plotTop + cy };
      setCenter((p) => (p && p.x === pos.x && p.y === pos.y ? p : pos));
    };

    update();
    Highcharts.addEvent(chart, 'render', update);
    return () => Highcharts.removeEvent(chart, 'render', update);
  }, []);

  const tooltipFormatter = useMemo(
    () => makeDonutTooltipFormatter(unitLabel, intl, currency),
    [unitLabel, currency, intl],
  );

  const legend = buildLegend();

  const options: Highcharts.Options = {
    chart: { type: 'pie' },
    title: { text: undefined },
    tooltip: {
      useHTML: true,
      formatter: tooltipFormatter,
      borderWidth: 0,
      backgroundColor: 'transparent',
      shadow: false,
      outside: true,
    },
    legend,
    plotOptions: {
      pie: {
        innerSize: '60%',
        borderWidth: 0,
        dataLabels: {
          useHTML: true,
          enabled: showDataLabels ?? values.length < 6,
          formatter() {
            if (typeof this.y !== 'number' || Number.isNaN(this.y)) {
              return `<span style="color:${theme.palette.text.secondary};font-weight:600">${this.key}</span>`;
            }

            const formattedVal = formatWidgetValue(this.y, unitLabel, {
              intl,
              currency,
              kpiOnly: true,
            });
            return `
              <span style="display:block;color:${theme.palette.text.secondary};font-weight:600">
                ${this.key}
              </span>
              <span style="display:block;color:${theme.palette.text.primary};font-weight:500">
                ${formattedVal}
              </span>`;
          },
          style: {
            whiteSpace: 'normal',
            textOutline: 'none',
            fontFamily: 'MaisonNeue,Roboto,sans-serif',
            fontSize: '12px',
            fontWeight: '500',
          },
        },
        states: {
          hover: {
            halo: { size: 4, opacity: 1 },
            brightness: 0,
          },
        },
        showInLegend: showLegend,
      },
    },
    series: [{ type: 'pie', data: seriesData }],
    credits: { enabled: false },
  };

  return (
    <VidMobBox
      ref={containerRef}
      sx={{ width: '100%', height: '100%', position: 'relative' }}
    >
      <HighchartsReact
        ref={chartRef}
        highcharts={Highcharts}
        options={options}
        containerProps={{ style: { width: '100%', height: '100%' } }}
      />

      {center && (
        <VidMobBox
          sx={{
            position: 'absolute',
            left: center.x,
            top: center.y,
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            pointerEvents: 'none',
          }}
        >
          <VidMobTypography variant="h6">
            {formatWidgetValue(totalValue, unitLabel, {
              intl,
              fuzzy: true,
              currency,
            })}
          </VidMobTypography>
        </VidMobBox>
      )}
    </VidMobBox>
  );
};

export default DonutChart;
