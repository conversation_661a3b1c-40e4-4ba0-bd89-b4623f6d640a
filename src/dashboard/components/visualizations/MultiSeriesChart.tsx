import React, { FC, useRef, useEffect, useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useTheme } from '@mui/material/styles';
import { VidMobBox } from '../../../vidMobComponentWrappers';
import { SeriesData } from '../../types/dashboardTypes';
import { makeDefaultTooltipFormatter } from '../../utils/tooltipUtils';
import {
  buildLegend,
  buildPlotOptions,
  buildSeries,
} from './utils/chartHelpers';
import {
  MultiSeriesChartType,
  WidgetFormatTypeEnum,
} from '../../dashboard.types';
import { useIntl } from 'react-intl';
import { Currency } from '../../../types/currency.types';
import { formatWidgetValue } from '../../utils/formatValues';

const H = Highcharts as any;
if (!H.seriesTypes.line.prototype._squareLegendPatched) {
  H.seriesTypes.line.prototype.drawLegendSymbol =
    H.seriesTypes.column.prototype.drawLegendSymbol;
  H.seriesTypes.line.prototype._squareLegendPatched = true;
}
export interface MultiSeriesChartProps {
  categories: string[];
  previousCategories?: string[];
  series: SeriesData[];
  unitLabelValues?: WidgetFormatTypeEnum;
  showDataLabels: boolean;
  chartType: MultiSeriesChartType;
  horizontal?: boolean;
  currency?: Currency;
  isCompareToPreviousPeriodEnabled: boolean;
}

const MultiSeriesChart: FC<MultiSeriesChartProps> = ({
  categories,
  series,
  unitLabelValues = WidgetFormatTypeEnum.NONE,
  showDataLabels,
  chartType,
  horizontal = false,
  previousCategories = [],
  currency,
  isCompareToPreviousPeriodEnabled,
}) => {
  const intl = useIntl();
  const theme = useTheme();
  const primaryColors = theme.palette.chart.seriesColors;
  const secondaryColors = theme.palette.chart.secondarySeriesColors;

  const hcSeries = buildSeries(
    chartType,
    series,
    primaryColors,
    secondaryColors,
    isCompareToPreviousPeriodEnabled,
    categories,
  );

  const plotOptions = buildPlotOptions(
    chartType,
    showDataLabels,
    unitLabelValues,
    intl,
  );

  const legend = buildLegend(chartType);

  const tooltipFormatter = makeDefaultTooltipFormatter({
    rawSeries: series,
    unitLabel: unitLabelValues,
    primaryColors,
    secondaryColors,
    previousCategories,
    intl,
    currency,
    isCompareToPreviousPeriodEnabled,
    categories,
    // if we want the label to be different than the x axis -- used in Bar graph
    // TODO link to backend when it's sending
    // displayCategories: ['jan 1- jan2'],
    // displayPreviousCategories: ['dec1-dec'],
  });

  const chartOptions: Highcharts.Options = {
    chart: {
      type: chartType,
      spacing: [10, 10, 10, 10],
      inverted:
        chartType === MultiSeriesChartType.BAR ||
        chartType === MultiSeriesChartType.COLUMN
          ? horizontal
          : false,
    },
    title: { text: undefined },
    xAxis: {
      categories,
      minPadding: 0,
      maxPadding: 0,
      startOnTick: false,
      endOnTick: false,
      title: { text: null },
      labels: {
        style: { fontFamily: 'MaisoNeue,Roboto,sans-serif' },
        formatter(this: Highcharts.AxisLabelsFormatterContextObject) {
          return formatWidgetValue(this.value as number, unitLabelValues, {
            currency,
            intl,
          });
        },
      },
    },
    yAxis: {
      title: { text: '' },
      labels: {
        style: { fontFamily: 'MaisoNeue,Roboto,sans-serif' },
        formatter(this: Highcharts.AxisLabelsFormatterContextObject) {
          return formatWidgetValue(this.value as number, unitLabelValues, {
            currency,
            intl,
          });
        },
      },
    },
    legend,
    plotOptions,
    tooltip: {
      useHTML: true,
      shared: false,
      borderWidth: 0,
      backgroundColor: 'transparent',
      shadow: false,
      formatter: tooltipFormatter,
    },
    credits: { enabled: false },
    series: hcSeries,
  };

  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<HighchartsReact.RefObject>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!containerRef.current) return;
    const ro = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });
    ro.observe(containerRef.current);
    return () => ro.disconnect();
  }, []);

  useEffect(() => {
    const hc = chartRef.current?.chart;
    if (
      hc &&
      size.width > 0 &&
      size.height > 0 &&
      (hc.chartWidth !== size.width || hc.chartHeight !== size.height)
    ) {
      hc.setSize(size.width, size.height, false);
    }
  }, [size]);

  return (
    <VidMobBox
      ref={containerRef}
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
        ref={chartRef}
        updateArgs={[true, true, true]}
        containerProps={{
          style: { width: '100%', height: '100%' },
        }}
      />
    </VidMobBox>
  );
};

export default MultiSeriesChart;
