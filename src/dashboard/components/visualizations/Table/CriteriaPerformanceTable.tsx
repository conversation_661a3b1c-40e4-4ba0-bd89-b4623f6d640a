import {
  GridRowsProp,
  GridColDef,
  GridAlignment,
  GridRenderCellParams,
  GridSortModel,
  DataGridPro,
} from '@mui/x-data-grid-pro';
import React, { useMemo, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { TableColumnDto, ChangeDirection } from '../../../dashboard.types';
import { SortBy } from '../../widgetEdit/context/WidgetEditContext';
import {
  useSortModel,
  defaultRenderCell,
  baseGridSx,
} from '../utils/TableUtils';
import { rowNumberCol } from './rowNumberCol';
import { withCriteriaPopover } from '../utils/withCriteriaPopover';

interface CriteriaPerformanceTableProps {
  columns: TableColumnDto[];
  data: GridRowsProp;
  isKpiLiftEnabled: boolean;
  isSortEnabled: boolean;
  sortBy?: SortBy | null;
  onSortChange?: (field: string | null, order?: 'asc' | 'desc') => void;
}

export const CriteriaPerformanceTable: React.FC<
  CriteriaPerformanceTableProps
> = ({
  columns,
  data,
  isKpiLiftEnabled,
  isSortEnabled,
  sortBy,
  onSortChange,
}) => {
  const intl = useIntl();

  const decoratedColumns = useMemo(
    () => withCriteriaPopover(columns),
    [columns],
  );

  const noRows = data.length === 0;
  const { sortModel, setSortModel } = useSortModel(sortBy);

  const pinnedLeft = useMemo(() => [rowNumberCol.field, 'criteria'], []);

  const gridColumns: GridColDef[] = useMemo(() => {
    const baseCols = decoratedColumns.map((col) => {
      const { key, label, type, sortable, changeDirection, units, currency } =
        col;

      const fallbackRender = (params: GridRenderCellParams) =>
        defaultRenderCell({
          params,
          fieldName: key,
          type,
          intl,
          units,
          currency,
          changeDirection: changeDirection as ChangeDirection | undefined,
          isCompareEnabled: isKpiLiftEnabled,
          isKpiLiftEnabled,
          kpiField: 'kpiValue',
        });

      const isCurrentlySorted = sortModel.some((m) => m.field === key);

      return {
        ...col,
        field: key,
        headerName: label,
        sortable: (isSortEnabled && sortable) || isCurrentlySorted,
        type,
        headerAlign: 'left' as GridAlignment,
        renderCell: col.renderCell ?? fallbackRender,
        flex: col.flex ?? 1,
        minWidth: col.minWidth ?? 100,
      } as GridColDef;
    });
    return [rowNumberCol, ...baseCols];
  }, [sortModel, decoratedColumns, intl, isKpiLiftEnabled, isSortEnabled]);

  const handleSortModelChange = useCallback(
    (newModel: GridSortModel) => {
      if (!isSortEnabled) return;

      if (newModel.length === 0) {
        setSortModel([]);
        onSortChange?.(null);
        return;
      }

      const { field, sort } = newModel[0];
      const colDef = decoratedColumns.find((c) => c.key === field);

      if (!colDef?.sortable) {
        // ignore clicks on non-sortable headers
        setSortModel(sortModel);
        return;
      }

      setSortModel([newModel[0]]);
      onSortChange?.(field, sort as 'asc' | 'desc');
    },
    [decoratedColumns, isSortEnabled, onSortChange, sortModel],
  );

  return (
    <DataGridPro
      rows={data}
      columns={gridColumns}
      pinnedColumns={{ left: pinnedLeft }}
      hideFooterSelectedRowCount
      hideFooter
      columnHeaderHeight={32}
      rowHeight={48}
      disableColumnMenu
      disableColumnReorder
      disableRowSelectionOnClick
      sortingMode="server"
      sortModel={sortModel}
      onSortModelChange={handleSortModelChange}
      sx={baseGridSx(noRows)}
    />
  );
};

export default CriteriaPerformanceTable;
