import React from 'react';
import {
  GridColDef,
  GridRenderCellParams,
  GridValueGetterParams,
  GridRowId,
  GridAlignment,
} from '@mui/x-data-grid-pro';
import { VidMobTypography } from '../../../../vidMobComponentWrappers';

function visibleRowIndex(api: any, id: GridRowId): number {
  return typeof api.getRowIndexRelativeToVisibleRows === 'function'
    ? api.getRowIndexRelativeToVisibleRows(id)
    : typeof api.getRowIndex === 'function'
      ? api.getRowIndex(id)
      : api.getSortedRowIds().indexOf(id);
}

export const rowNumberCol: GridColDef = {
  field: '__rowNumber__',
  headerName: '#',
  type: 'number',
  minWidth: 35,
  flex: 0.33,
  sortable: false,
  filterable: false,
  disableColumnMenu: true,
  headerAlign: 'left' as GridAlignment,
  align: 'left' as GridAlignment,
  valueGetter: (p: GridValueGetterParams<any>) =>
    visibleRowIndex(p.api, p.id) + 1,
  renderCell: (p: GridRenderCellParams<any>) => (
    <VidMobTypography variant="caption" color="text.secondary">
      {p.value}
    </VidMobTypography>
  ),
};
