import {
  GridRowsProp,
  GridColDef,
  GridAlignment,
  GridRenderCellParams,
  GridSortModel,
  DataGridPro,
} from '@mui/x-data-grid-pro';
import React, { useMemo, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { TableColumnDto, ChangeDirection } from '../../../dashboard.types';
import { SortBy } from '../../widgetEdit/context/WidgetEditContext';
import {
  useSortModel,
  defaultRenderCell,
  baseGridSx,
} from '../utils/TableUtils';

interface GenericDataTableProps {
  columns: TableColumnDto[];
  data: GridRowsProp;
  isCompareToPreviousPeriodEnabled: boolean;
  isSortEnabled: boolean;
  sortBy?: SortBy | null;
  onSortChange?: (field: string, order: 'asc' | 'desc') => void;
}

const GenericDataTable: React.FC<GenericDataTableProps> = ({
  columns,
  data,
  isCompareToPreviousPeriodEnabled,
  isSortEnabled,
  sortBy,
  onSortChange,
}) => {
  const intl = useIntl();
  const noRows = data.length === 0;
  const { sortModel, setSortModel } = useSortModel(sortBy);

  const pinnedLeft = useMemo(
    () => (columns.length ? [columns[0].key] : []),
    [columns],
  );

  const gridColumns: GridColDef[] = useMemo(
    () =>
      columns.map((col) => {
        const { key, label, type, sortable, changeDirection, units, currency } =
          col;
        return {
          field: key,
          headerName: label,
          sortable: isSortEnabled && sortable,
          type,
          headerAlign: 'left' as GridAlignment,
          renderCell: (params: GridRenderCellParams) =>
            defaultRenderCell({
              params,
              fieldName: key,
              type,
              intl,
              units,
              currency,
              changeDirection: changeDirection as ChangeDirection | undefined,
              isCompareEnabled: isCompareToPreviousPeriodEnabled,
            }),
          flex: col.flex ?? 1,
          minWidth: col.minWidth ?? 100,
        } as GridColDef;
      }),
    [columns, intl, isCompareToPreviousPeriodEnabled, isSortEnabled],
  );

  const handleSortModelChange = useCallback(
    (newModel: GridSortModel) => {
      if (!isSortEnabled) return;

      if (newModel.length === 0) {
        setSortModel([]);
        onSortChange?.('', 'asc');
        return;
      }

      const { field, sort } = newModel[0];
      const colDef = columns.find((c) => c.key === field);

      if (!colDef?.sortable) {
        setSortModel(sortModel);
        return;
      }

      setSortModel([newModel[0]]);
      onSortChange?.(field, sort as 'asc' | 'desc');
    },
    [columns, isSortEnabled, onSortChange, sortModel],
  );

  return (
    <DataGridPro
      rows={data}
      columns={gridColumns}
      pinnedColumns={{ left: pinnedLeft }}
      hideFooterSelectedRowCount
      hideFooter
      columnHeaderHeight={32}
      rowHeight={48}
      disableColumnMenu
      disableColumnReorder
      disableRowSelectionOnClick
      sortingMode="server"
      sortModel={sortModel}
      onSortModelChange={handleSortModelChange}
      sx={baseGridSx(noRows)}
    />
  );
};

export default GenericDataTable;
