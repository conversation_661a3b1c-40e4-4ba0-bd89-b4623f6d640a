import React, { FC, useRef, useState, useEffect } from 'react';
import {
  VidMobTypography,
  VidMobChip,
  VidMobBox,
  VidMobTooltip,
} from '../../../vidMobComponentWrappers';
import {
  ChangeDirection,
  MetricWidgetItem,
  WidgetFormatTypeEnum,
} from '../../dashboard.types';
import MuiWidgetTooltip from '../../utils/muiTooltipUtils';
import { getChangeColor } from '../../utils/getChangeColor';
import { tooltipStyle } from './utils/tooltipStyle';
import { formatWidgetValue } from '../../utils/formatValues';
import { Currency } from '../../../types/currency.types';
import { useIntl } from 'react-intl';

interface Props {
  values: MetricWidgetItem[];
  unitLabel: WidgetFormatTypeEnum;
  currency?: Currency;
  isCompareToPreviousPeriodEnabled: boolean;
  changeDirection: ChangeDirection;
}

const MetricVisualization: FC<Props> = ({
  values,
  unitLabel,
  currency,
  isCompareToPreviousPeriodEnabled,
  changeDirection,
}) => {
  const intl = useIntl();
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);

  useEffect(() => {
    if (!containerRef.current) return;
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });
    observer.observe(containerRef.current);
    return () => {
      observer.disconnect();
    };
  }, []);

  const isSmall = containerWidth < 400;
  const count = values.length;

  const getItemStyles = (index: number) => {
    let flexBasis = '100%';
    let variant: 'h3' | 'h4' | 'h6' = 'h3';
    let hasVerticalDivider = false;
    let hasHorizontalDivider = false;
    let borderColor: string | undefined;

    switch (count) {
      case 2:
        flexBasis = '50%';
        variant = 'h4';
        if (index === 1) {
          hasVerticalDivider = true;
          borderColor = 'background.secondary';
        }
        break;

      case 3:
        if (isSmall) {
          if (index === 0) {
            flexBasis = '100%';
            variant = 'h3';
          } else {
            flexBasis = '50%';
            variant = 'h6';
            if (index === 2) {
              hasVerticalDivider = true;
              borderColor = 'background.secondary';
            }
          }
        } else {
          flexBasis = '33.3333%';
          variant = 'h4';
          if (index === 1 || index === 2) {
            hasVerticalDivider = true;
            borderColor = 'background.secondary';
          }
        }
        break;

      case 4:
        if (isSmall) {
          flexBasis = '50%';
          if (index < 2) {
            variant = 'h6';
          } else {
            variant = 'h6';
            hasHorizontalDivider = true;
            borderColor = 'background.secondary';
          }
          if (index === 1 || index === 3) {
            hasVerticalDivider = true;
            borderColor = 'background.secondary';
          }
        } else {
          flexBasis = '25%';
          variant = 'h6';
          if (index === 1 || index === 2 || index === 3) {
            hasVerticalDivider = true;
            borderColor = 'background.secondary';
          }
        }
        break;

      case 1:
      default:
        flexBasis = '100%';
        variant = 'h3';
        break;
    }

    return {
      flexBasis,
      variant,
      hasVerticalDivider,
      hasHorizontalDivider,
      borderColor,
      isCompareToPreviousPeriodEnabled,
    };
  };

  const renderMetricBlock = (
    item: MetricWidgetItem,
    variant: 'h3' | 'h4' | 'h6',
    isCompareToPreviousPeriodEnabled: boolean,
    changeDirection: ChangeDirection,
  ) => {
    const {
      value,
      changePercentage,
      label,
      previousValue,
      category,
      previousCategory,
      iconUrl,
    } = item;
    const hasChange =
      typeof changePercentage === 'number' && isCompareToPreviousPeriodEnabled;
    const changeLabel =
      hasChange &&
      `${changePercentage! > 0 ? '+' : ''}${changePercentage!.toFixed(2)}%`;
    const higherIsBetter = changeDirection === ChangeDirection.higherIsBetter;
    const { backgroundColor, textColor } = getChangeColor(
      higherIsBetter,
      changePercentage,
    );

    return (
      <VidMobBox
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        width="100%"
        height="100%"
      >
        <VidMobTypography
          variant="caption"
          align="center"
          color="text.secondary"
        >
          {label}
        </VidMobTypography>

        <VidMobTooltip
          title={
            <MuiWidgetTooltip
              baseName={label}
              category={category}
              previousCategory={previousCategory}
              primaryVal={formatWidgetValue(value, unitLabel, {
                currency,
                intl: intl,
              })}
              secondaryVal={
                previousValue
                  ? formatWidgetValue(previousValue, unitLabel, {
                      currency,
                      intl: intl,
                    })
                  : null
              }
              changePercentage={changePercentage}
              isHigherBetter={higherIsBetter}
              iconUrl={iconUrl}
              isCompareToPreviousPeriodEnabled={
                isCompareToPreviousPeriodEnabled
              }
            />
          }
          componentsProps={{
            tooltip: {
              sx: tooltipStyle,
            },
          }}
        >
          <VidMobTypography variant={variant} align="center">
            {formatWidgetValue(value, unitLabel, {
              currency,
              kpiOnly: true,
              intl: intl,
            })}
          </VidMobTypography>
        </VidMobTooltip>

        {hasChange && (
          <VidMobChip
            label={changeLabel}
            size="small"
            sx={{
              mt: 4,
              backgroundColor: backgroundColor,
              color: textColor,
              height: 24,
              fontSize: '0.75rem',
            }}
          />
        )}
      </VidMobBox>
    );
  };

  if (count < 1 || count > 4) {
    return null;
  }

  return (
    <VidMobBox
      ref={containerRef}
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        width: '100%',
        height: '100%',
      }}
    >
      {values.map((item, idx) => {
        const {
          flexBasis,
          variant,
          hasVerticalDivider,
          hasHorizontalDivider,
          borderColor,
        } = getItemStyles(idx);

        return (
          <VidMobBox
            key={idx}
            sx={{
              position: 'relative',
              boxSizing: 'border-box',
              flexBasis,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              px: 2,
              py: 2,
              minHeight: 80,

              ...(hasHorizontalDivider && {
                borderTop: '1px solid',
                borderColor,
              }),

              ...(hasVerticalDivider && {
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 24,
                  bottom: 24,
                  left: 0,
                  width: '1px',
                  backgroundColor: borderColor,
                },
              }),
            }}
          >
            {renderMetricBlock(
              item,
              variant,
              isCompareToPreviousPeriodEnabled,
              changeDirection,
            )}
          </VidMobBox>
        );
      })}
    </VidMobBox>
  );
};

export default MetricVisualization;
