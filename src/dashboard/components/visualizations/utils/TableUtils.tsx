import React, { useEffect, useMemo, useState } from 'react';
import { GridRenderCellParams, GridSortModel } from '@mui/x-data-grid-pro';
import {
  CellMeta,
  ChangeDirection,
  WidgetFormatTypeEnum,
} from '../../../dashboard.types';
import { formatWidgetValue } from '../../../utils/formatValues';
import { getChangeColor } from '../../../utils/getChangeColor';
import {
  VidMobBox,
  VidMobTypography,
} from '../../../../vidMobComponentWrappers';
import { Currency } from '../../../../types/currency.types';
import { IntlShape } from 'react-intl';
import { GLOBALS } from '../../../../constants';
import { SortBy } from '../../widgetEdit/context/WidgetEditContext';
const { EM_DASH_UNICODE } = GLOBALS;
const formatPrimitiveValue = (
  raw: string | number | null,
  type: string,
): string => {
  if (raw == null) return EM_DASH_UNICODE;
  switch (type) {
    case 'number':
      return typeof raw === 'number'
        ? formatWidgetValue(raw, undefined, { kpiOnly: true })
        : String(raw);
    case 'string':
    default:
      return String(raw);
  }
};

const formatChangePct = (rawPct: number | null, compareEnabled: boolean) => {
  if (!compareEnabled) return null;
  if (rawPct == null || Number.isNaN(rawPct)) return EM_DASH_UNICODE;
  const sign = rawPct > 0 ? '+' : '';
  return `${sign}${rawPct.toFixed(1)}%`;
};

const renderNestedCell = (
  cell: CellMeta,
  intl: IntlShape,
  isCompareEnabled: boolean,
  units?: WidgetFormatTypeEnum,
  currency?: Currency,
  changeDirection?: ChangeDirection,
): React.ReactNode => {
  const mainText = formatWidgetValue(
    cell.value,
    units,
    { currency, kpiOnly: true, intl },
    cell.defaultDisplayName,
  );

  const pct =
    isCompareEnabled && cell.changePercentage != null
      ? cell.changePercentage
      : null;
  const { textColor } = getChangeColor(
    changeDirection === ChangeDirection.higherIsBetter,
    pct,
  );

  const changePct = formatChangePct(pct, isCompareEnabled);

  return (
    <VidMobBox sx={{ display: 'flex', gap: 4, alignItems: 'center' }}>
      <VidMobTypography variant="body2">{mainText}</VidMobTypography>
      {changePct && (
        <VidMobTypography variant="caption" color={textColor}>
          {changePct}
        </VidMobTypography>
      )}
    </VidMobBox>
  );
};

interface DefaultRenderArgs {
  params: GridRenderCellParams;
  fieldName: string;
  type: string;
  intl: IntlShape;
  units?: WidgetFormatTypeEnum;
  currency?: Currency;
  changeDirection?: ChangeDirection;
  isCompareEnabled: boolean;
  isKpiLiftEnabled?: boolean;
  kpiField?: string;
}

export const defaultRenderCell = ({
  params,
  fieldName,
  type,
  intl,
  units,
  currency,
  changeDirection,
  isCompareEnabled,
  isKpiLiftEnabled,
  kpiField,
}: DefaultRenderArgs): React.ReactNode => {
  const raw = params.row[fieldName];

  // If KPI‑lift mode is on, we only show change % on the KPI value column.
  const showPct = !isKpiLiftEnabled || fieldName === kpiField;

  if (
    typeof raw === 'object' &&
    raw !== null &&
    ('value' in (raw as any) || 'defaultDisplayName' in (raw as any))
  ) {
    return renderNestedCell(
      raw as CellMeta,
      intl,
      isCompareEnabled && showPct,
      units,
      currency,
      changeDirection,
    );
  }

  const formatted = formatPrimitiveValue(raw as string | number | null, type);
  return <VidMobTypography variant="body2">{formatted}</VidMobTypography>;
};

export const baseGridSx = (noRows: boolean) => ({
  '& .MuiDataGrid-cell': {
    outline: 'unset !important',
    justifyContent: 'flex-start',
  },
  '& .MuiDataGrid-columnHeaderTitle': {
    color: 'text.secondary',
    typography: 'caption',
    justifyContent: 'flex-start',
  },
  '& .MuiDataGrid-columnHeaders': {
    borderColor: (theme: any) => theme.palette.background.secondary,
  },
  '& .Mui-selected': { backgroundColor: 'transparent !important' },
  border: 'unset',
  '& .MuiDataGrid-columnHeader': {
    outline: 'unset !important',
    borderBottom: (theme: any) => theme.palette.background.secondary,
  },
  '& .MuiDataGrid-row:hover': {
    backgroundColor: (theme: any) => theme.palette.background.secondary,
  },
  '& .MuiDataGrid-row.Mui-selected:hover': {
    backgroundColor: (theme: any) => theme.palette.background.secondary,
  },
  '& .MuiDataGrid-row > .MuiDataGrid-cell': {
    borderBottom: '1px solid #EEEEEE',
    borderColor: (theme: any) => theme.palette.background.secondary,
  },
  '& .MuiDataGrid-pinnedColumnHeaders, & .MuiDataGrid-pinnedColumns': {
    boxShadow: 'none',
  },
  '& .MuiDataGrid-virtualScroller': {
    mb: 4,
  },
  ...(noRows && {
    '& .MuiDataGrid-columnHeaders': {
      display: 'none',
    },
    '& .MuiDataGrid-virtualScroller': {
      overflowX: 'hidden !important',
    },
    '& .MuiDataGrid-main': {
      overflowX: 'hidden',
    },
  }),
  flex: 1,
});

export function useSortModel(sortBy: SortBy | null | undefined) {
  const initialSortModel = useMemo<GridSortModel>(
    () =>
      sortBy
        ? [
            {
              field: sortBy.sortBy,
              sort: sortBy.sortOrder?.toLowerCase() as 'asc' | 'desc',
            },
          ]
        : [],
    [sortBy],
  );

  const [sortModel, setSortModel] = useState<GridSortModel>(initialSortModel);

  useEffect(() => setSortModel(initialSortModel), [initialSortModel]);

  return { sortModel, setSortModel } as const;
}
