import Highcharts, { LegendOptions, Point, Series } from 'highcharts';
import { SeriesData } from '../../../types/dashboardTypes';
import {
  MultiSeriesChartType,
  WidgetFormatTypeEnum,
} from '../../../dashboard.types';
import { formatWidgetValue } from '../../../utils/formatValues';
import { Currency } from '../../../../types/currency.types';
import { textStyle } from './tooltipStyle';
import { IntlShape } from 'react-intl';

export const BAR_RADIUS = 4;
export const INNER_GAP_FACTOR = 0.02; // gap between Current & Prev in the pair
export const GROUP_GAP_FACTOR = 0.12; // gap between pairs (categories)
const CURRENT = 'current';
const PREVIOUS = 'previous';

export const buildSeries = (
  chartType: MultiSeriesChartType,
  seriesData: SeriesData[],
  primaryColors: string[],
  secondaryColors: string[],
  isCompareToPreviousPeriodEnabled: boolean,
  categories: string[],
): Highcharts.SeriesOptionsType[] => {
  switch (chartType) {
    case MultiSeriesChartType.LINE: {
      return seriesData.flatMap((s, idx) => {
        const id = `series-${idx}`;
        const colorIndex = idx % primaryColors.length;
        const items: Highcharts.SeriesLineOptions[] = [];
        items.push({
          id,
          name: s.name,
          type: chartType,
          data: s.data,
          color: primaryColors[colorIndex],
          marker: {
            symbol: 'circle',
            enabled: false,
            states: { hover: { enabled: true } },
          },
          showInLegend: true,
          custom: { iconUrl: s.iconUrl },
          connectNulls: false,
        });

        if (s.previousPeriodData && isCompareToPreviousPeriodEnabled) {
          items.push({
            id: `${id}-prev`,
            linkedTo: id,
            name: `${s.name} (Prev)`,
            type: chartType,
            marker: { enabled: false, states: { hover: { enabled: true } } },
            data: s.previousPeriodData,
            color: secondaryColors[colorIndex],
            showInLegend: false,
            connectNulls: false,
          });
        }

        return items;
      });
    }
    case MultiSeriesChartType.BAR: {
      const { curr, prev } = toPointArrays(
        seriesData,
        categories,
        primaryColors,
        secondaryColors,
      );

      const current: Highcharts.SeriesOptionsType = {
        id: CURRENT,
        type: 'bar',
        name: 'Current',
        colorByPoint: true,
        data: curr,
        showInLegend: true,
        states: { inactive: { opacity: 0.2 } },
        zIndex: 2,
      };

      if (!isCompareToPreviousPeriodEnabled) return [current];

      const previous: Highcharts.SeriesOptionsType = {
        id: PREVIOUS,
        type: 'bar',
        name: 'Previous',
        linkedTo: CURRENT,
        colorByPoint: true,
        data: prev,
        showInLegend: false,
        states: { inactive: { opacity: 0.2 } },
        zIndex: 1,
      };

      return [current, previous];
    }

    case MultiSeriesChartType.COLUMN: {
      return seriesData.flatMap((s, idx) => {
        const id = `series-${idx}`;
        const colorIndex = idx % primaryColors.length;
        const base = idx * (isCompareToPreviousPeriodEnabled ? 2 : 1);
        const items: (
          | Highcharts.SeriesBarOptions
          | Highcharts.SeriesColumnOptions
        )[] = [];

        items.push({
          id,
          index: base,
          name: s.name,
          type: chartType,
          data: s.data,
          color: primaryColors[colorIndex],
          showInLegend: true,
          states: {
            inactive: { opacity: 0.2 },
          },
          custom: { iconUrl: s.iconUrl },
        } as any);

        if (s.previousPeriodData && isCompareToPreviousPeriodEnabled) {
          items.push({
            id: `${id}-prev`,
            linkedTo: id,
            index: base,
            name: `${s.name} (Prev)`,
            type: chartType,
            data: s.previousPeriodData,
            color: secondaryColors[colorIndex],
            showInLegend: false,
            states: {
              inactive: { opacity: 0.2 },
            },
          } as any);
        }

        return items;
      });
    }

    default:
      return [];
  }
};

export function buildPlotOptions(
  chartType: MultiSeriesChartType,
  showDataLabels: boolean,
  unitLabelValues: WidgetFormatTypeEnum,
  intl: IntlShape,
  currency?: Currency,
): Highcharts.Options['plotOptions'] {
  const shared = {
    grouping: true,
    pointPadding: INNER_GAP_FACTOR,
    groupPadding: GROUP_GAP_FACTOR,
    borderRadius: BAR_RADIUS,
    dataLabels: {
      enabled: showDataLabels,
      style: {
        fontFamily: "'MaisonNeue', Roboto, sans-serif",
        fontWeight: '500',
      },
      formatter(this: Highcharts.PointLabelObject) {
        return this.y
          ? formatWidgetValue(this.y, unitLabelValues, {
              currency,
              kpiOnly: true,
              intl,
            })
          : null;
      },
    },
  };

  switch (chartType) {
    case MultiSeriesChartType.LINE:
      return {
        line: {
          dataLabels: {
            enabled: showDataLabels,
            formatter(this: Highcharts.PointLabelObject) {
              return this.y
                ? `${formatWidgetValue(this.y, unitLabelValues, { currency, kpiOnly: true, intl: intl })}`
                : null;
            },
          } as Highcharts.PlotLineDataLabelsOptions,
          marker: {
            symbol: 'circle',
            enabled: false,
            states: {
              hover: {
                enabled: true,
              },
            },
          },
        } as Highcharts.PlotLineOptions,
      };

    case MultiSeriesChartType.COLUMN:
      return {
        column: shared,
        bar: shared,
      };

    case MultiSeriesChartType.BAR:
      return {
        bar: shared,
        column: shared,
      };

    default:
      return {};
  }
}

export const buildLegend = (
  chartType?: MultiSeriesChartType,
): LegendOptions => {
  if (chartType === MultiSeriesChartType.BAR) {
    return { enabled: false };
  }
  return {
    useHTML: true,
    squareSymbol: true,
    symbolWidth: 12,
    symbolHeight: 12,
    symbolRadius: 0,
    enabled: true,
    layout: 'horizontal',
    align: 'center',
    verticalAlign: 'bottom',
    itemStyle: { whiteSpace: 'nowrap' },
    y: 0,
    labelFormatter: function (this: Series | Point) {
      const icon = (this.options as any).custom?.iconUrl;
      return `
          <div style="
            display:flex;
            align-items:'flex-start';"
            >
            <span style="display:inline-flex;align-items:center;gap:4px;${textStyle};line-height:14px">
              ${icon ? `<img src="${icon}" alt="" width="12" height="12" />` : ''}
              ${this.name}
            </span>
            </div>
          `;
    },
  };
};

const toPointArrays = (
  rows: SeriesData[],
  cats: string[],
  primary: string[],
  secondary: string[],
) => {
  const curr: Highcharts.PointOptionsObject[] = [];
  const prev: Highcharts.PointOptionsObject[] = [];

  rows.forEach((r) => {
    const x = cats.findIndex((c) => c === r.name);
    if (x === -1) return; // ignore unknown category

    const sync = (
      mateId: 'current' | 'previous',
    ): Highcharts.PointEventsOptionsObject => ({
      mouseOver(this: Highcharts.Point) {
        const mate = this.series.chart.get(mateId) as
          | Highcharts.Series
          | undefined;
        mate?.data[this.x as number]?.setState('hover');
      },
      mouseOut(this: Highcharts.Point) {
        const mate = this.series.chart.get(mateId) as
          | Highcharts.Series
          | undefined;
        mate?.data[this.x as number]?.setState('');
      },
    });

    curr.push({
      x,
      y: r.data[0],
      name: r.name,
      color: primary[x % primary.length],
      events: sync(PREVIOUS),

      custom: {
        categoryIdx: x,
        iconUrl: r.iconUrl,
      },
    });

    prev.push({
      x,
      y: r.previousPeriodData?.[0] ?? null,
      color: secondary[x % secondary.length],
      events: sync(CURRENT),
      custom: { categoryIdx: x },
    });
  });

  return { curr, prev };
};
