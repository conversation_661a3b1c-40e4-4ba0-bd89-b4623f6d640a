import { Operator } from '../../components/ReportFilters/types';
import { VisualizationType } from '../../widgets/types/visualizationType';
import { WidgetType, WidgetFilterKey } from '../../widgets/types/widgetTypes';
import {
  DashboardResponse,
  SharingScope,
  TimeGranularity,
} from '../dashboard.types';

export const getDashboardMock = async (): Promise<DashboardResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(dashboard);
    }, 1000);
  });
};

export const dashboard: DashboardResponse = {
  id: '66666666-6666-6666-6666-666666666666',
  name: 'Enterprise Adherence Overview',
  description: 'Instance created from the Enterprise Adherence Overview preset',
  dashboardTemplate: {
    id: '11111111-1111-1111-1111-111111111111',
    name: 'Enterprise Adherence Overview',
  },
  sharingScope: SharingScope.PRIVATE,
  ownerUser: {
    id: 21075,
    displayName: '<PERSON>',
    photoUrl: 'https://example.com/photos/johndoe.jpg',
  },
  dashboardFilter: {
    filters: [
      {
        key: WidgetFilterKey.MEDIA_CREATE_DATE,
        operator: Operator.BETWEEN,
        value: ['2025-04-01T00:00:00Z', '2025-04-30T23:59:59Z'],
      },
    ],
    groupBy: { time: TimeGranularity.MONTH },
    entityType: 'AD_ASSET',
  },
  dateCreated: '2025-05-07T00:00:00Z',
  lastUpdated: '2025-05-07T11:30:00Z',
  createdBy: {
    id: 21075,
    displayName: 'John Doe',
    photoUrl: 'https://example.com/photos/johndoe.jpg',
  },
  lastModifiedBy: {
    id: 21075,
    displayName: 'John Doe',
    photoUrl: 'https://example.com/photos/johndoe.jpg',
  },
  widgets: [
    {
      id: '88888888-8888-8888-8888-888888888888',
      widgetType: WidgetType.KEY_FINDINGS,
      name: 'Learnings',
      gridX: 0,
      gridY: 0,
      gridWidth: 6,
      gridHeight: 4,
      visualizationType: VisualizationType.bar,
      parameters: {
        content: 'Hello World',
      },
      filter: {
        filters: [
          {
            key: 'brandName',
            operator: Operator.IN,
            value: ['BrandA', 'BrandB'],
          },
        ],
      },
      dateCreated: '2025-05-07T00:00:00Z',
      lastUpdated: '2025-05-07T00:00:00Z',
    },
  ],
  isFavorite: false,
};

export const mockWidgetFilter = {
  filters: [
    {
      key: WidgetFilterKey.MEDIA_CREATE_DATE,
      operator: 'in',
      value: ['LAST_30_DAYS'],
    },
  ],
};
