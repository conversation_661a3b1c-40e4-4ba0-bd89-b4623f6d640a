//@ts-nocheck
import { VisualizationType } from '../../widgets/types/visualizationType';
import {
  MultiSeriesChartDto,
  MetricDto,
  TableDto,
  TextWidgetDto,
  WidgetDataResponse,
  WidgetFormatTypeEnum,
  DonutChartDto,
  ChangeDirection,
} from '../dashboard.types';
import {
  WidgetPreviewRequest,
  WidgetPreviewResponse,
  WidgetTypeResponse,
  WidgetType,
  WidgetFilterKey,
  WidgetFilterValueResponse,
  WidgetFilterValuesParams,
  WidgetDetailsResponse,
  FilterInputType,
} from '../../widgets/types/widgetTypes';
import { Operator } from '../../components/ReportFilters/types';

const textData = {
  content: `
**Hello World**

_This is a text widget._ 🥳🥳🥳

[Click here](https://www.google.com)
  `,
} as TextWidgetDto;

export const PLATFORM_ICONS: Record<string, string> = {
  FACEBOOK:
    'https://d2fe6afh41xruv.cloudfront.net/platform_icons/20240426/Facebook.svg',
  SNAPCHAT:
    'https://d2fe6afh41xruv.cloudfront.net/platform_icons/20240426/Snapchat.svg',
  TWITTER:
    'https://d2fe6afh41xruv.cloudfront.net/platform_icons/20240426/Twitter.svg',
  META: 'https://d2fe6afh41xruv.cloudfront.net/platform_icons/20240426/Meta.svg',
  TIKTOK:
    'https://d2fe6afh41xruv.cloudfront.net/platform_icons/20240426/TikTok.svg',
  YOUTUBE:
    'https://d2fe6afh41xruv.cloudfront.net/platform_icons/20240426/YouTube.svg', // guessed – adjust if needed
};

const barData: MultiSeriesChartDto = {
  categories: ['Jan 2025', 'Feb 2025', 'Mar 2025'],
  previousCategories: ['Jan 2024', 'Feb 2024', 'Mar 2024'],
  series: [
    {
      name: 'Facebook',
      iconUrl: PLATFORM_ICONS.FACEBOOK,
      data: [150, 220, 97],
      previousPeriodData: [34, 33, 42],
      changePercentageData: [21, 62, 71],
      changeDirection: ChangeDirection.higherIsBetter,
    },
    {
      name: 'Snap',
      iconUrl: PLATFORM_ICONS.SNAPCHAT,
      data: [89, 112, 32],
      previousPeriodData: [12, 34, 60],
      changePercentageData: [21, 62, 71],
      changeDirection: ChangeDirection.higherIsBetter,
    },
    {
      name: 'Twitter',
      iconUrl: PLATFORM_ICONS.TWITTER,
      data: [42, 93, 19],
      previousPeriodData: [200, 127, 60],
      changePercentageData: [21, 62, 71],
      changeDirection: ChangeDirection.higherIsBetter,
    },
  ],
  xAxisLabel: 'Month',
  yAxisLabel: 'Number of Assets',
  unitLabelValues: WidgetFormatTypeEnum.ASSET,
};

const metricData: MetricDto = {
  values: [
    {
      value: 75,
      previousValue: 70,
      changePercentage: 7.14,
      category: 'Jan 2025 - Jun 2025',
      previousCategory: 'Jan 2024 - Jun 2024',
      label: 'Meta',
      changeDirection: ChangeDirection.higherIsBetter,
      iconUrl: PLATFORM_ICONS.META,
    },
    {
      value: 75,
      previousValue: 70,
      changePercentage: -7.14,
      category: 'Jan 2025 - Jun 2025',
      previousCategory: 'Jan 2024 - Jun 2024',
      label: 'Meta',
      changeDirection: ChangeDirection.higherIsBetter,
      iconUrl: PLATFORM_ICONS.META,
    },
    {
      value: 75,
      previousValue: 70,
      changePercentage: -7.14,
      category: 'Jan 2025 - Jun 2025',
      previousCategory: 'Jan 2024 - Jun 2024',
      label: 'Meta',
      changeDirection: ChangeDirection.lowerIsBetter,
      iconUrl: PLATFORM_ICONS.META,
    },
    {
      value: 75,
      previousValue: 70,
      changePercentage: -7.14,
      category: 'Jan 2025 - Jun 2025',
      previousCategory: 'Jan 2024 - Jun 2024',
      label: 'Meta',
      iconUrl: PLATFORM_ICONS.META,
    },
  ],
  unitLabel: WidgetFormatTypeEnum.PERCENTAGE,
};

const tableData: TableDto = {
  columns: [
    {
      key: 'criteria',
      label: 'Criteria',
      type: 'string',
      sortable: false,
    },
    {
      key: 'averageAdherence',
      label: 'Avg. Adherence',
      type: 'number',
      units: WidgetFormatTypeEnum.PERCENTAGE,
      sortable: false,
      changeDirection: ChangeDirection.higherIsBetter,
    },
    {
      key: 'kpiValue',
      label: 'CPM',
      type: 'number',
      units: WidgetFormatTypeEnum.SPEND,
      sortable: false,
      changeDirection: ChangeDirection.lowerIsBetter,
    },
    {
      key: 'impressions',
      label: 'Impressions',
      type: 'number',
      units: WidgetFormatTypeEnum.NONE,
      sortable: false,
      changeDirection: ChangeDirection.higherIsBetter,
    },
  ],
  rows: [
    {
      criteria: {
        category: 'Formatting',
        workspaces: [],
        criteriaGroups: [],
        identifier: 'FRAMED_FOR_MOBILE',
        rule: 'Fits aspect ratio 1:1,4:5,9:16',
        parameters: {
          aspectRatios: ['1:1', '4:5', '9:16'],
        },
        description:
          "Confirms if the proportional relationship between the creative's width and height matches the specified rule.",
        defaultDisplayName: 'Fits Aspect Ratio',
      },
      averageAdherence: {
        value: 40.68,
        previousValue: 25.12,
        changePercentage: 53,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
      kpiValue: {
        value: 9.33,
        previousValue: 25.12,
        changePercentage: 19,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
      impressions: {
        value: 2_830_595,
        previousValue: 1_900_595,
        changePercentage: 36,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
    },
    {
      criteria: {
        category: 'Formatting',
        workspaces: [],
        criteriaGroups: [],
        identifier: 'FRAMED_FOR_MOBILE',
        rule: 'Fits aspect ratio 1:1,4:5,9:16',
        parameters: {
          aspectRatios: ['1:1', '4:5', '9:16'],
        },
        description:
          "Confirms if the proportional relationship between the creative's width and height matches the specified rule.",
        defaultDisplayName: 'Text present',
      },
      averageAdherence: {
        value: 32.93,
        previousValue: 25.12,
        changePercentage: 53,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
      kpiValue: {
        value: 6.18,
        previousValue: 18.11,
        changePercentage: 65,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
      impressions: {
        value: 13_598_485,
        previousValue: 1_921_334,
        changePercentage: -2.1,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
    },
    {
      criteria: {
        category: 'Formatting',
        workspaces: [],
        criteriaGroups: [],
        identifier: 'FRAMED_FOR_MOBILE',
        rule: 'Fits aspect ratio 1:1,4:5,9:16',
        parameters: {
          aspectRatios: ['1:1', '4:5', '9:16'],
        },
        description:
          "Confirms if the proportional relationship between the creative's width and height matches the specified rule.",
        defaultDisplayName: 'Motion Early',
      },
      averageAdherence: {
        value: 18.88,
        previousValue: 25.12,
        changePercentage: 53,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
      kpiValue: {
        value: 15.73,
        previousValue: 10.65,
        changePercentage: -27.1,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
      impressions: {
        value: 435_852,
        previousValue: 421_888,
        changePercentage: 13,
        category: 'Jan 2025',
        previousCategory: 'Feb 2025',
      },
    },
  ],
};

export const donutData: DonutChartDto = {
  title: 'Spend',
  unitLabelValues: WidgetFormatTypeEnum.SPEND,
  segments: [
    { name: 'Meta', value: 43_250, iconUrl: PLATFORM_ICONS.META },
    { name: 'TikTok', value: 21_500, iconUrl: PLATFORM_ICONS.TIKTOK },
    { name: 'YouTube', value: 18_750, iconUrl: PLATFORM_ICONS.YOUTUBE },
    { name: 'Snapchat', value: 8_750, iconUrl: PLATFORM_ICONS.SNAPCHAT },
  ],
  totalValue: 92_250,
};

function getMockData(type: string) {
  switch (type) {
    case VisualizationType.donut:
      return donutData;
    case VisualizationType.text:
      return textData;
    case VisualizationType.metric:
      return metricData;
    case VisualizationType.column:
    case VisualizationType.bar:
    case VisualizationType.line:
      return barData;
    case VisualizationType.table:
      return tableData;
    default:
      throw new Error(`Unsupported visualization type: ${type}`);
  }
}

export default async function mockWidgetData(
  type: string,
): Promise<WidgetDataResponse> {
  const randomDelay = Math.floor(Math.random() * 5000) + 1000;
  await new Promise((resolve) => setTimeout(resolve, randomDelay));
  return {
    widgetId: '77777777-7777-7777-7777-777777777777',
    widgetType: 'AVERAGE_ADHERENCE_SCORE',
    visualizationType: type,
    data: getMockData(type),
    lastRefreshed: '2025-05-21T03:12:45Z',
  };
}

export async function mockWidgetPreview(
  request: WidgetPreviewRequest,
): Promise<WidgetPreviewResponse> {
  // Simulate API delay
  const randomDelay = Math.floor(Math.random() * 3000) + 500;
  await new Promise((resolve) => setTimeout(resolve, randomDelay));

  return {
    widgetId: null, // As this is a preview for an unsaved widget
    widgetType: request.widgetType as WidgetType,
    visualizationType: request.visualizationType,
    data: getMockData(request.visualizationType),
    lastRefreshed: new Date().toISOString(),
  };
}

// Mock Widget Types Data
const mockWidgetTypes: WidgetTypeResponse[] = [
  {
    widgetType: WidgetType.ASSETS_SCORED,
    name: 'Assets Scored',
    description: 'Total number of assets scored',
    iconUrl: 'https://assets.vidmob.com/icons/assets-scored.svg',
    defaultVisualizationType: 'BAR',
    visualizationTypes: [
      VisualizationType.metric,
      VisualizationType.bar,
      VisualizationType.column,
      VisualizationType.donut,
      VisualizationType.table,
      VisualizationType.line,
    ],
    requiredFilters: [
      {
        key: WidgetFilterKey.MEDIA_CREATE_DATE,
        displayLabel: 'Date',
        type: FilterInputType.DATE_RANGE,
      },
      {
        key: WidgetFilterKey.ASSET_SOURCE,
        displayLabel: 'Asset Source',
        type: FilterInputType.MULTI_SELECT,
      },
      {
        key: WidgetFilterKey.WORKSPACE_ID,
        displayLabel: 'Workspace',
        type: FilterInputType.FILTERED_MULTI_SELECT,
      },
    ],
    optionalFilters: [
      {
        key: WidgetFilterKey.CHANNEL,
        displayLabel: 'Channel',
        type: FilterInputType.MULTI_SELECT,
      },
      {
        key: WidgetFilterKey.BRAND,
        displayLabel: 'Brand',
        type: FilterInputType.MULTI_SELECT,
      },
    ],
    filterValues: {},
  },
  {
    widgetType: WidgetType.BRAND_ADHERENCE,
    name: 'Brand Adherence',
    description: 'Brand adherence score and trends',
    iconUrl: 'https://assets.vidmob.com/icons/brand-adherence.svg',
    defaultVisualizationType: 'METRIC',
    visualizationTypes: [
      VisualizationType.metric,
      VisualizationType.donut,
      VisualizationType.bar,
      VisualizationType.line,
    ],
    requiredFilters: [
      {
        key: WidgetFilterKey.DATE_RANGE,
        displayLabel: 'Date Range',
        type: FilterInputType.DATE_RANGE,
      },
      {
        key: WidgetFilterKey.BRAND,
        displayLabel: 'Brand',
        type: FilterInputType.MULTI_SELECT,
      },
    ],
    optionalFilters: [
      {
        key: WidgetFilterKey.CHANNEL,
        displayLabel: 'Channel',
        type: FilterInputType.MULTI_SELECT,
      },
      {
        key: WidgetFilterKey.MARKET,
        displayLabel: 'Market',
        type: FilterInputType.FILTERED_MULTI_SELECT,
        dependsOn: ['brand'],
      },
    ],
    filterValues: {},
  },
  {
    widgetType: WidgetType.CHANNEL_ADHERENCE_TRENDS,
    name: 'Channel Adherence Trends',
    description: 'Adherence trends across different channels',
    iconUrl: 'https://assets.vidmob.com/icons/channel-trends.svg',
    defaultVisualizationType: 'LINE',
    visualizationTypes: ['LINE', 'BAR', 'TABLE'],
    requiredFilters: [
      {
        key: WidgetFilterKey.DATE_RANGE,
        displayLabel: 'Date Range',
        type: FilterInputType.DATE_RANGE,
      },
      {
        key: WidgetFilterKey.CHANNEL,
        displayLabel: 'Channel',
        type: FilterInputType.MULTI_SELECT,
      },
    ],
    optionalFilters: [
      {
        key: WidgetFilterKey.BRAND,
        displayLabel: 'Brand',
        type: FilterInputType.MULTI_SELECT,
      },
      {
        key: WidgetFilterKey.ASSET_TYPE,
        displayLabel: 'Asset Type',
        type: FilterInputType.MULTI_SELECT,
      },
    ],
    filterValues: {},
  },
  {
    widgetType: WidgetType.IMPRESSIONS,
    name: 'Impressions',
    description: 'Total impressions and performance metrics',
    iconUrl: 'https://assets.vidmob.com/icons/impressions.svg',
    defaultVisualizationType: 'COLUMN',
    visualizationTypes: [
      VisualizationType.metric,
      VisualizationType.column,
      VisualizationType.bar,
      VisualizationType.line,
      VisualizationType.table,
    ],
    requiredFilters: [
      {
        key: WidgetFilterKey.MEDIA_CREATE_DATE,
        displayLabel: 'Date Range',
        type: FilterInputType.DATE_RANGE,
      },
    ],
    optionalFilters: [
      {
        key: WidgetFilterKey.CHANNEL,
        displayLabel: 'Channel',
        type: FilterInputType.MULTI_SELECT,
      },
      {
        key: WidgetFilterKey.CAMPAIGN,
        displayLabel: 'Campaign',
        type: FilterInputType.FILTERED_MULTI_SELECT,
        dependsOn: ['channel'],
      },
      {
        key: WidgetFilterKey.BRAND,
        displayLabel: 'Brand',
        type: FilterInputType.MULTI_SELECT,
      },
    ],
    filterValues: {},
  },
];

export async function mockWidgetTypesList(): Promise<WidgetTypeResponse[]> {
  // Simulate API delay
  const randomDelay = Math.floor(Math.random() * 2000) + 300;
  await new Promise((resolve) => setTimeout(resolve, randomDelay));

  return mockWidgetTypes;
}

export async function mockWidgetType(
  widgetType: WidgetType,
): Promise<WidgetTypeResponse | null> {
  // Simulate API delay
  const randomDelay = Math.floor(Math.random() * 1500) + 200;
  await new Promise((resolve) => setTimeout(resolve, randomDelay));

  const foundWidget = mockWidgetTypes.find(
    (widget) => widget.widgetType === widgetType,
  );
  return foundWidget || null;
}

// Mock Widget Filter Values Data
const mockFilterValues: Record<
  string,
  Record<string, WidgetFilterValueResponse[]>
> = {
  [WidgetType.ASSETS_SCORED]: {
    [WidgetFilterKey.ASSET_SOURCE]: [
      { id: 'PRE_FLIGHT', name: 'Pre-Flight' },
      { id: 'IN_FLIGHT', name: 'In-Flight' },
    ],
    [WidgetFilterKey.CHANNEL]: [
      {
        id: 'AMAZONADVERTISING',
        name: 'Amazon Advertising',
        // mocked icon url since we use local icon component
        iconUrl: 'https://assets.vidmob.com/icons/amazon.svg',
      },
      {
        id: 'FACEBOOK',
        name: 'Facebook',
        iconUrl: 'https://assets.vidmob.com/icons/facebook.svg',
      },
      {
        id: 'GOOGLE',
        name: 'Google',
        iconUrl: 'https://assets.vidmob.com/icons/google.svg',
      },
      {
        id: 'TIKTOK',
        name: 'TikTok',
        iconUrl: 'https://assets.vidmob.com/icons/tiktok.svg',
      },
    ],
    [WidgetFilterKey.BRAND]: [
      { id: 'NIKE', name: 'Nike' },
      { id: 'ADIDAS', name: 'Adidas' },
      { id: 'PUMA', name: 'Puma' },
    ],
    [WidgetFilterKey.WORKSPACE_ID]: [
      { id: '12345', name: 'Workspace 1' },
      { id: '12346', name: 'Workspace 2' },
      { id: '12347', name: 'Workspace 3' },
    ],
  },
  [WidgetType.IMPRESSIONS]: {
    [WidgetFilterKey.CHANNEL]: [
      {
        id: 'AMAZONADVERTISING',
        name: 'Amazon Advertising',
        iconUrl: 'https://assets.vidmob.com/icons/amazon.svg',
      },
      {
        id: 'FACEBOOK',
        name: 'Facebook',
        iconUrl: 'https://assets.vidmob.com/icons/facebook.svg',
      },
      {
        id: 'GOOGLE',
        name: 'Google',
        iconUrl: 'https://assets.vidmob.com/icons/google.svg',
      },
      {
        id: 'SNAPCHAT',
        name: 'Snapchat',
        iconUrl: 'https://assets.vidmob.com/icons/snapchat.svg',
      },
    ],
    [WidgetFilterKey.CAMPAIGN]: [
      { id: 'SUMMER_2024', name: 'Summer 2024 Campaign' },
      { id: 'HOLIDAY_2024', name: 'Holiday 2024 Campaign' },
      { id: 'SPRING_2025', name: 'Spring 2025 Campaign' },
    ],
    [WidgetFilterKey.BRAND]: [
      { id: 'COCA_COLA', name: 'Coca Cola' },
      { id: 'PEPSI', name: 'Pepsi' },
      { id: 'SPRITE', name: 'Sprite' },
    ],
  },
  [WidgetType.BRAND_ADHERENCE]: {
    [WidgetFilterKey.BRAND]: [
      { id: 'NIKE', name: 'Nike' },
      { id: 'ADIDAS', name: 'Adidas' },
      { id: 'PUMA', name: 'Puma' },
    ],
    [WidgetFilterKey.CHANNEL]: [
      {
        id: 'FACEBOOK',
        name: 'Facebook',
        iconUrl: 'https://assets.vidmob.com/icons/facebook.svg',
      },
      {
        id: 'TIKTOK',
        name: 'TikTok',
        iconUrl: 'https://assets.vidmob.com/icons/tiktok.svg',
      },
    ],
    [WidgetFilterKey.MARKET]: [
      { id: 'US', name: 'United States' },
      { id: 'CA', name: 'Canada' },
      { id: 'UK', name: 'United Kingdom' },
    ],
  },
  [WidgetType.CHANNEL_ADHERENCE_TRENDS]: {
    [WidgetFilterKey.CHANNEL]: [
      {
        id: 'FACEBOOK',
        name: 'Facebook',
        iconUrl: 'https://assets.vidmob.com/icons/facebook.svg',
      },
      {
        id: 'GOOGLE',
        name: 'Google',
        iconUrl: 'https://assets.vidmob.com/icons/google.svg',
      },
      {
        id: 'TIKTOK',
        name: 'TikTok',
        iconUrl: 'https://assets.vidmob.com/icons/tiktok.svg',
      },
    ],
    [WidgetFilterKey.BRAND]: [
      { id: 'NIKE', name: 'Nike' },
      { id: 'ADIDAS', name: 'Adidas' },
    ],
    [WidgetFilterKey.ASSET_TYPE]: [
      { id: 'VIDEO', name: 'Video' },
      { id: 'IMAGE', name: 'Image' },
    ],
  },
};

export async function mockWidgetFilterValues(
  params: WidgetFilterValuesParams,
): Promise<WidgetFilterValueResponse[]> {
  // Simulate API delay
  const randomDelay = Math.floor(Math.random() * 2000) + 300;
  await new Promise((resolve) => setTimeout(resolve, randomDelay));

  const {
    widgetType,
    key,
    offset = 0,
    perPage = 100,
    sortBy,
    sortOrder = 'asc',
  } = params;

  // Get mock data for the specific widget type and field
  const widgetData = mockFilterValues[widgetType];
  if (!widgetData) {
    return [];
  }

  const fieldData = widgetData[key];
  if (!fieldData) {
    return [];
  }

  const result = [...fieldData];

  // Apply sorting if specified
  if (sortBy) {
    result.sort((a, b) => {
      const aValue = a[sortBy as keyof WidgetFilterValueResponse] || '';
      const bValue = b[sortBy as keyof WidgetFilterValueResponse] || '';

      if (sortOrder === 'desc') {
        return String(bValue).localeCompare(String(aValue));
      }
      return String(aValue).localeCompare(String(bValue));
    });
  }

  // Apply pagination
  const startIndex = offset;
  const endIndex = offset + perPage;

  return result.slice(startIndex, endIndex);
}

// TODO: edit to follow spec
const mockedWidgetDetails: Record<string, WidgetDetailsResponse> = {
  '1234': {
    widgetId: '1234',
    widgetType: WidgetType.ASSETS_SCORED,
    visualizationType: VisualizationType.column,
    dashboardFilter: {
      filters: [],
    },
    isCompareToPreviousPeriodEnabled: true,
    isViewDataLabelsEnabled: false,
    filter: {
      filters: [
        {
          key: WidgetFilterKey.ASSET_SOURCE,
          operator: Operator.EQUALS,
          value: ['IN_FLIGHT'],
        },
        {
          key: WidgetFilterKey.CHANNEL,
          operator: Operator.EQUALS,
          value: [
            {
              id: 'FACEBOOK',
              name: 'Facebook',
            },
          ],
        },
        {
          key: WidgetFilterKey.MEDIA_CREATE_DATE,
          operator: Operator.BETWEEN,
          value: ['2024-12-11', '2025-03-11'],
        },
        {
          key: WidgetFilterKey.WORKSPACE_ID,
          operator: Operator.IN,
          value: [
            {
              id: '12345',
              name: 'Workspace 1',
            },
          ],
        },
        {
          key: WidgetFilterKey.ASSET_TYPE,
          operator: Operator.IN,
          value: ['IN_FLIGHT'],
        },
      ],
    },
  },
};

export async function mockWidgetDetails(
  widgetId: string,
  _widgetDetails?: Omit<WidgetDetailsResponse, 'widgetId'>,
): Promise<WidgetDetailsResponse | null> {
  // Simulate API delay
  const randomDelay = Math.floor(Math.random() * 2000) + 300;
  await new Promise((resolve) => setTimeout(resolve, randomDelay));

  return mockedWidgetDetails[widgetId] || null;
}
