import { WidgetType } from '../../widgets/types/widgetTypes';

export const DASHBOARD_INTL_KEYS = {
  home: {
    id: 'ui.dashboard.home',
    defaultMessage: 'Home',
  },
  widgetDrawerOpen: {
    id: 'ui.dashboard.widgetDrawer.open',
    defaultMessage: 'Add widget',
  },
  widgetDrawerAdd: {
    id: 'ui.dashboard.widgetDrawer.add',
    defaultMessage: 'Add',
  },
  text: {
    title: {
      id: 'ui.dashboard.text.title',
      defaultMessage: 'Text',
    },
  },
  linkDialog: {
    edit: {
      id: 'ui.dashboard.linkDialog.edit',
      defaultMessage: 'Edit',
    },
    insert: {
      id: 'ui.dashboard.linkDialog.insert',
      defaultMessage: 'Insert',
    },
    cancel: {
      id: 'ui.dashboard.linkDialog.cancel',
      defaultMessage: 'Cancel',
    },
    enterUrl: {
      id: 'ui.dashboard.linkDialog.enterUrl',
      defaultMessage: 'Enter URL',
    },
  },
  apply: {
    id: 'button.global.apply.label',
    defaultMessage: 'Apply',
  },
  save: {
    id: 'button.global.save.label',
    defaultMessage: 'Save',
  },
  update: {
    id: 'button.global.update.label',
    defaultMessage: 'Update',
  },
  edit: {
    id: 'ui.dashboard.landingPage.edit',
    defaultMessage: 'Edit',
  },
  applying: {
    id: 'ui.dashboard.dashboard.applying',
    defaultMessage: 'Applying...',
  },
  dashboardSaved: {
    id: 'ui.dashboard.dashboard.saved',
    defaultMessage: 'Dashboard saved',
  },
  duplicate: {
    id: 'ui.dashboard.widget.moreActions.duplicate',
    defaultMessage: 'Duplicate',
  },
  delete: {
    id: 'ui.dashboard.landingPage.delete',
    defaultMessage: 'Delete',
  },
  cancel: {
    id: 'ui.dashboard.widget.moreActions.cancel',
    defaultMessage: 'Cancel',
  },
  editSidebar: {
    kpiSpendDisabled: {
      id: 'ui.dashboard.widget.editSidebar.kpiSpendDisabled',
      defaultMessage: 'Spend based KPIs are disabled for this organization.',
    },
    compareToAssetsThatDidNotMeetCriteria: {
      id: 'ui.dashboard.widget.editSidebar.compareToAssetsThatDidNotMeetCriteria',
      defaultMessage: 'Compare to assets that did not meet criteria',
    },
    compareToAssetsThatDidNotMeetCriteriaTooltip: {
      id: 'ui.dashboard.widget.editSidebar.compareToAssetsThatDidNotMeetCriteriaTooltip',
      defaultMessage:
        'Measures the percent lift of performance for assets that met criteria, compared to assets that did not.',
    },
    viewAverageMetric: {
      id: 'ui.dashboard.widget.editSidebar.viewAverageMetric',
      defaultMessage: 'View average metric',
    },
    compareToAverage: {
      id: 'ui.dashboard.widget.editSidebar.compareToAverage',
      defaultMessage: 'Compare to average',
    },
    viewBy: {
      id: 'ui.dashboard.widget.editSidebar.viewBy',
      defaultMessage: 'View by',
    },
    addOptionalFilter: {
      id: 'ui.dashboard.widget.editSidebar.addOptionalFilter',
      defaultMessage: 'Add filter',
    },
    requiredFiltersInfo: {
      id: 'ui.dashboard.widget.editSidebar.requiredFiltersInfo',
      defaultMessage: 'Choose what type of data you want this widget to show.',
    },
    chartSectionInfo: {
      id: 'ui.dashboard.widget.editSidebar.chartSectionInfo',
      defaultMessage: 'Select how your data will be visualized.',
    },
    optionalFiltersInfo: {
      id: 'ui.dashboard.widget.editSidebar.optionalFiltersInfo',
      defaultMessage: 'Narrow your results by applying filters.',
    },
    compareToPreviousPeriod: {
      id: 'ui.dashboard.widget.editSidebar.compareToPreviousPeriod',
      defaultMessage: 'Compare to previous period',
    },
    selectDateRange: {
      id: 'ui.dashboard.widget.editSidebar.selectDateRange',
      defaultMessage: 'Select date range',
    },
    resetFilters: {
      id: 'ui.dashboard.widget.editSidebar.resetFilters',
      defaultMessage: 'Reset',
    },
    chartSectionTitle: {
      id: 'ui.dashboard.widget.editSidebar.chartSection',
      defaultMessage: 'Chart',
    },
    chartSectionType: {
      id: 'ui.dashboard.widget.editSidebar.chartSectionType',
      defaultMessage: 'Type',
    },
    requiredFiltersTitle: {
      id: 'ui.dashboard.widget.editSidebar.requiredFiltersTitle',
      defaultMessage: 'Assets',
    },
    optionalFiltersTitle: {
      id: 'ui.dashboard.widget.editSidebar.optionalFilters',
      defaultMessage: 'Scope',
    },
    viewDataLabels: {
      id: 'ui.dashboard.widget.editSidebar.viewDataLabels',
      defaultMessage: 'View data labels',
    },
    disabledWorkspace: {
      id: 'ui.dashboard.widget.editSidebar.disabledWorkspace',
      defaultMessage:
        'This workspace can’t be selected because it doesn’t have {servicesNames} subscription.',
    },
  },
  actions: {
    duplicate: {
      id: 'ui.dashboard.actions.duplicate',
      defaultMessage: 'Duplicate',
    },
    delete: {
      id: 'ui.dashboard.actions.delete',
      defaultMessage: 'Delete',
    },
    favorite: {
      id: 'ui.dashboard.actions.favorite',
      defaultMessage: 'Starred',
    },
    unFavorite: {
      id: 'ui.dashboard.actions.unFavorite',
      defaultMessage: 'Not starred',
    },
    download: {
      id: 'ui.dashboard.actions.downloadPdf',
      defaultMessage: 'Download as PDF',
    },
    downloadFailed: {
      id: 'ui.dashboard.actions.downloadPdf.error',
      defaultMessage: 'Error downloading PDF. Please try again.',
    },
    share: {
      id: 'ui.dashboard.actions.share',
      defaultMessage: 'Share',
    },
  },
  disabled: {
    duplicateNew: {
      id: 'ui.dashboard.actions.duplicate.disabled',
      defaultMessage: 'Save dashboard to duplicate',
    },
    deleteNew: {
      id: 'ui.dashboard.actions.delete.disabled',
      defaultMessage: 'Save dashboard to delete',
    },
    favoriteNew: {
      id: 'ui.dashboard.actions.favorite.disabled',
      defaultMessage: 'Save dashboard to star',
    },
    downloadNew: {
      id: 'ui.dashboard.actions.downloadPdf.disabled',
      defaultMessage: 'Save dashboard to download as PDF',
    },
    deleteNotOwner: {
      id: 'ui.dashboard.landingPage.grid.action.delete.tooltip',
      defaultMessage: 'Only the owner of this dashboard can delete it',
    },
  },
  shareMenu: {
    share: {
      id: 'ui.dashboard.landingPage.grid.action.share.tooltip',
      defaultMessage: 'Only the owner of this dashboard can change sharing',
    },
    title: {
      id: 'ui.dashboard.shareMenu.title',
      defaultMessage: 'Who can view this dashboard',
    },
    onlyMe: {
      id: 'ui.dashboard.shareMenu.onlyMe',
      defaultMessage: 'Only me',
    },
    organization: {
      title: {
        id: 'ui.dashboard.shareMenu.organization',
        defaultMessage: 'Anyone in {organizationName}',
      },
      description: {
        id: 'ui.dashboard.shareMenu.organization.description',
        defaultMessage:
          'Based on workspace membership, some team members may not see all widgets',
      },
    },
  },
  duplicateModal: {
    title: {
      id: 'ui.dashboard.duplicateModal.title',
      defaultMessage: 'Duplicate without saving?',
    },
    description: {
      id: 'ui.dashboard.duplicateModal.description',
      defaultMessage:
        'You have unsaved changes. Duplicating now will create a copy of your last saved dashboard.',
    },
    button: {
      id: 'ui.dashboard.duplicateModal.button',
      defaultMessage: 'Duplicate',
    },
  },
  dashboardTitlePlaceholder: {
    id: 'ui.dashboard.dashboardTitlePlaceholder',
    defaultMessage: 'Add dashboard name',
  },
  widgetDrawerDisabledTooltip: {
    id: 'ui.dashboard.widgetDrawer.widgetDrawerDisabledTooltip',
    defaultMessage: 'Only the owner of this dashboard can add widgets',
  },
  noEditAccess: {
    id: 'ui.dashboard.widget.noEditAccess',
    defaultMessage: 'Only the owner of this dashboard can edit widgets',
  },
  addDisabled: {
    id: 'ui.dashboard.widgetDrawer.addDisabled',
    defaultMessage:
      'You’ve reached the 16-widget limit for this dashboard. To add a new one, remove an existing widget.',
  },
  saveDisabledTooltip: {
    id: 'ui.dashboard.saveDisabledTooltip',
    defaultMessage: 'Only the owner of this dashboard can save changes',
  },
  blankState: {
    title: {
      id: 'ui.dashboard.emptyState.title',
      defaultMessage: 'Add widgets',
    },
    description: {
      id: 'ui.dashboard.emptyState.description',
      defaultMessage:
        'Add widgets from the panel on the left to begin building out your dashboard',
    },
  },
  unsavedModal: {
    title: {
      id: 'ui.dashboard.unsavedChangesDialog.title',
      defaultMessage: 'Leave this page?',
    },
    description: {
      id: 'ui.dashboard.unsavedChangesDialog.description',
      defaultMessage:
        'If you leave this page now, you’ll lose any unsaved changes.',
    },
    leaveButton: {
      id: 'ui.dashboard.unsavedChangesDialog.leaveButton',
      defaultMessage: 'Close without saving',
    },
    stayButton: {
      id: 'ui.dashboard.unsavedChangesDialog.stayButton',
      defaultMessage: 'Keep editing',
    },
  },
};

export const WIDGET_TYPE_SPECIFIC_COPY: Partial<
  Record<
    WidgetType,
    Record<
      string,
      {
        id: string;
        defaultMessage: string;
      }
    >
  >
> = {
  [WidgetType.CRITERIA_PERFORMANCE]: {
    requiredFiltersTitle: {
      id: 'ui.dashboard.widget.editSidebar.requiredFiltersTitle.criteriaPerformance',
      defaultMessage: 'Criteria',
    },
    addOptionalFilter: {
      id: 'ui.dashboard.widget.editSidebar.addOptionalFilter.criteriaPerformance',
      defaultMessage: 'Refine scope',
    },
  },
};

export enum VidmobServices {
  'AGILE-CREATIVE-SUITE' = 'AGILE-CREATIVE-SUITE',
  'BRING-YOUR-OWN-CREATOR' = 'BRING-YOUR-OWN-CREATOR',
  'CREATIVE-INTELLIGENCE' = 'CREATIVE-INTELLIGENCE',
  'BRAND-GOVERNANCE' = 'BRAND-GOVERNANCE',
}

export const SERVICES_COPY: Record<
  VidmobServices,
  {
    id: string;
    defaultMessage: string;
  }
> = {
  [VidmobServices['AGILE-CREATIVE-SUITE']]: {
    id: 'ui.dashboard.widget.editSidebar.services.agileCreativeSuite',
    defaultMessage: 'Studio',
  },
  [VidmobServices['BRING-YOUR-OWN-CREATOR']]: {
    id: 'ui.dashboard.widget.editSidebar.services.bringYourOwnCreator',
    defaultMessage: 'Bring Your Own Creator',
  },
  [VidmobServices['CREATIVE-INTELLIGENCE']]: {
    id: 'ui.dashboard.widget.editSidebar.services.creativeIntelligence',
    defaultMessage: 'Analytics',
  },
  [VidmobServices['BRAND-GOVERNANCE']]: {
    id: 'ui.dashboard.widget.editSidebar.services.brandGovernance',
    defaultMessage: 'Scoring',
  },
};
