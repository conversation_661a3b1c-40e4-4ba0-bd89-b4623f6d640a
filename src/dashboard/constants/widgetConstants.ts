import React from 'react';
import {
  BookIcon,
  ChecklistIcon,
  ClipboardIcon,
  EyeIcon,
  GlobeIcon,
  GroupIcon,
  MetricIcon,
  PaperIcon,
  PercentageIcon2,
  PlayIcon,
  ScoreIcon,
  TextIcon,
} from '../../assets/vidmob-mui-icons/general';
import { WidgetFilterKey, WidgetType } from '../../widgets/types/widgetTypes';
import { ViewOnIcon } from '../../assets/vidmob-mui-icons/general/ViewOnIcon';

type IconVariant = 'default' | 'secondary';

interface WidgetIconSet {
  default: React.ElementType;
  secondary?: React.ElementType;
}

const same = (icon: React.ElementType): WidgetIconSet => ({
  default: icon,
  secondary: icon,
});

export const WIDGET_ICON_REGISTRY: Record<WidgetType, WidgetIconSet> = {
  [WidgetType.KEY_FINDINGS]: same(TextIcon),
  [WidgetType.ASSETS_SCORED]: same(MetricIcon),
  [WidgetType.CHANNEL_ADHERENCE_TRENDS]: same(ChecklistIcon),
  [WidgetType.ADHERENCE_SCORE]: same(PercentageIcon2),
  [WidgetType.CRITERIA_PERFORMANCE]: {
    default: ClipboardIcon,
    secondary: ScoreIcon,
  },
  [WidgetType.BRAND_ADHERENCE]: { default: PaperIcon, secondary: BookIcon },
  [WidgetType.IMPRESSIONS]: { default: EyeIcon, secondary: ViewOnIcon },
  [WidgetType.MARKET_ADHERENCE]: same(GlobeIcon),
  [WidgetType.ASSET_OVERVIEW]: { default: PlayIcon, secondary: GroupIcon },
};

export const WIDGET_ID_PREFIX = 'widget_';

export function getWidgetIcon(
  type: WidgetType,
  variant: IconVariant = 'default',
) {
  const set = WIDGET_ICON_REGISTRY[type];
  if (!set) {
    return null;
  }
  return variant === 'secondary' && set.secondary ? set.secondary : set.default;
}

export const shouldDisplayDateFilter = (
  widgetFilterKeyType: WidgetFilterKey,
): boolean => {
  return [
    WidgetFilterKey.MEDIA_CREATE_DATE,
    WidgetFilterKey.DATE_RANGE,
  ].includes(widgetFilterKeyType);
};
