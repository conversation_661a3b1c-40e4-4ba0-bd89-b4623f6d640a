export const DRAWER_WIDTH = 320;
export const ROW_HEIGHT = 90;
export const BASE_WIDTH = 2;
export const BASE_HEIGHT = 4;
export const COLUMNS = 4;
export const DEFAULT_SNAP = 1;
export const MAX_WIDGETS_PER_DASHBOARD = 16;

export const TEXT_COLORS = [
  '#212121',
  '#680331',
  '#B71C1C',
  '#FF9C00',
  '#1B5E20',
  '#005244',
  '#004966',
  '#0A2599',
  '#757575',
  '#E04685',
  '#D32F2F',
  '#FDC938',
  '#388E3C',
  '#03AA8F',
  '#00A1E0',
  '#1842EF',
];

export const EDITOR_TOOLBAR_OPTIONS = [
  [{ header: [false, 1, 2, 3] }],
  ['bold', 'italic', 'underline', 'link'],
  [{ list: 'ordered' }, { list: 'bullet' }],
  [
    {
      color: TEXT_COLORS,
    },
  ],
];

export enum WidgetCustomProperties {
  IS_COMPARE_TO_PREVIOUS_PERIOD_ENABLED = 'isCompareToPreviousPeriodEnabled',
  IS_VIEW_DATA_LABELS_ENABLED = 'isViewDataLabelsEnabled',
  VIEW_BY = 'viewBy',
  VIEW_DATA_LABELS_ENABLED = 'viewDataLabelsEnabled',
  IS_INCLUDE_TOTAL_ENABLED = 'isIncludeTotalEnabled',
  IS_COMPARE_TO_AVERAGE_ENABLED = 'isCompareToAverageEnabled',
  IS_KPI_LIFT_ENABLED = 'isKpiLiftEnabled',
}
