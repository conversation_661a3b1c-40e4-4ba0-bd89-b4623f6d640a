import { PAGE_HEADER_V2_BREADCRUMBS_ID } from '../../components/PageHeaderV2/PageHeaderV2';
import { WidgetType } from '../../widgets/types/widgetTypes';

export const PDF_DOWNLOAD_IDS = {
  DASHBOARD_CONTENT: 'dashboard-page-content',
  ADD_WIDGET_BUTTON: 'add-widget-button',
  ADD_WIDGET_DRAWER: 'add-widget-drawer',
  PAGE_HEADER_ACTION_BUTTONS: 'page-header-action-buttons',
  TEXT_WIDGET_DROPDOWN_BUTTON: 'text-widget-dropdown-button',
  WIDGET_DETAILS_LINK: 'widget-details-link',
  WIDGET_DROPDOWN_BUTTON: 'widget-dropdown-button',
  WIDGET_EDIT_BUTTON: 'widget-edit-button',
  PAGE_SUBHEADER: 'dashboard-page-subheader',
  HEADER_BORDER: 'dashboard-page-header_border',
  DASHBOARD_PAGE_PARENT: 'dashboard-page-top-parent',
  DASHBOARD_PAGE_CHILD: 'dashboard-page-child',
};

export const PDF_DOWNLOAD_IDS_TO_IGNORE = [
  PDF_DOWNLOAD_IDS.ADD_WIDGET_BUTTON,
  PDF_DOWNLOAD_IDS.ADD_WIDGET_DRAWER,
  PDF_DOWNLOAD_IDS.PAGE_HEADER_ACTION_BUTTONS,
  PDF_DOWNLOAD_IDS.TEXT_WIDGET_DROPDOWN_BUTTON,
  PDF_DOWNLOAD_IDS.WIDGET_DETAILS_LINK,
  PDF_DOWNLOAD_IDS.WIDGET_DROPDOWN_BUTTON,
  PDF_DOWNLOAD_IDS.WIDGET_EDIT_BUTTON,
  PAGE_HEADER_V2_BREADCRUMBS_ID,
];

export const PDF_WIDGETS_THAT_REQUIRE_DISCLAIMER = [
  WidgetType.ASSETS_SCORED,
  WidgetType.IMPRESSIONS,
];
