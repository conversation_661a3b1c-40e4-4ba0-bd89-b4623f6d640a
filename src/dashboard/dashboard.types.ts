import {
  GridRenderCellParams,
  GridValueGetterParams,
} from '@mui/x-data-grid-pro';
import { Operator } from '../components/ReportFilters/types';
import { Currency } from '../types/currency.types';
import { VisualizationType } from '../widgets/types/visualizationType';
import { FilterField, WidgetType } from '../widgets/types/widgetTypes';
import { ReportFilterInputDto } from '../widgets/types/widgetTypes';
import { WidgetCustomProperties } from './constants/dashboardConstants';
import { SortBy } from './components/widgetEdit/context/WidgetEditContext';

export interface WidgetDataResponse {
  widgetId: string | null;
  widgetType: string;
  visualizationType: string;
  data: WidgetVisualizationDto;
  lastRefreshed: string;
}

export interface MetricWidgetItem {
  value: number;
  previousValue?: number;
  changePercentage?: number;
  label: string;
  category: string;
  previousCategory?: string;
  iconUrl?: string;
}

export enum ChangeDirection {
  higherIsBetter = 'HIGHER_IS_BETTER',
  lowerIsBetter = 'LOWER_IS_BETTER',
}

export interface MetricDto {
  values: MetricWidgetItem[];
  unitLabel: WidgetFormatTypeEnum;
  currency?: Currency;
  changeDirection: ChangeDirection;
}

export interface MultiSeriesChartSeries {
  name: string;
  data: number[];
  iconUrl?: string;
  previousPeriodData?: number[];
  changePercentageData?: number[];
  changeDirection?: ChangeDirection;
}

export interface MultiSeriesChartDto {
  categories: string[];
  previousCategories?: string[];
  series: MultiSeriesChartSeries[];
  xAxisLabel?: string;
  yAxisLabel?: string;
  unitLabelValues: WidgetFormatTypeEnum;
  currency?: Currency;
}

export interface MetricDto {
  values: MetricWidgetItem[];
  unitLabel: WidgetFormatTypeEnum;
  currency?: Currency;
  changeDirection: ChangeDirection;
}

export interface DonutSegmentDto {
  name: string;
  value: number;
  percentage?: number;
  iconUrl?: string;
}

export interface DonutChartDto {
  values: MetricWidgetItem[];
  unitLabel: WidgetFormatTypeEnum;
  currency?: Currency;
  changeDirection: ChangeDirection;
  total?: MetricWidgetItem;
  reportPeriodLabel?: string;
  previousPeriodLabel?: string;
}
export interface TableColumnDto {
  key: string;
  label: string;
  type: string;
  sortable: boolean;
  units?: WidgetFormatTypeEnum;
  changeDirection?: ChangeDirection;
  currency?: Currency;
  renderCell?: (p: GridRenderCellParams) => React.ReactNode;
  valueGetter?: (p: GridValueGetterParams) => unknown;
  flex?: number;
  minWidth?: number;
}
export interface CellMeta {
  value: number;
  previousValue: number;
  changePercentage: number;
  category: string;
  previousCategory?: string;
  iconUrl?: string;
  media?: any;
  defaultDisplayName?: string;
}

export interface TableRowDto {
  [key: string]: string | number | null | CellMeta;
}

export interface TableDto {
  columns: TableColumnDto[];
  rows: TableRowDto[];
}

export interface TextWidgetDto {
  content: string;
  contentType?: 'markdown' | 'html' | 'plain';
  showWrapper?: boolean;
}

export type WidgetVisualizationDto =
  | MetricDto
  | MultiSeriesChartDto
  | DonutChartDto
  | TableDto
  | TextWidgetDto;

export interface WidgetDataRequest {
  widgetId?: string;
  widgetType: string;
  visualizationType: string;
  filter: WidgetFilter | null;
  parameters?: WidgetVisualizationDto;
  dashboardFilter: ReportFilterInputDto | null;
  widgetCustomProperties?: Partial<Record<WidgetCustomProperties, any>>;
  sortBy?: SortBy | null;
}

export interface DashboardResponse {
  id: string;
  name: string;
  description: string;
  dashboardTemplate: DashboardTemplate;
  ownerUser: User;
  dashboardFilter: ReportFilterInputDto;
  dateCreated: string;
  lastUpdated: string;
  createdBy: User;
  lastModifiedBy: User;
  widgets: Widget[];
  isFavorite: boolean;
  sharingScope: SharingScope;
}

export interface DashboardTemplate {
  id: string;
  name: string;
}

export interface User {
  id: number;
  displayName: string;
  photoUrl: string;
}

export interface DashboardFilters {
  filters: Filter[];
  groupBy: GroupBy;
  entityType: string;
}

export const EQUALS = 'equals';
export const IN = 'in';
export const BETWEEN = 'between';
export const GT = 'gt';
export const LT = 'lt';

export enum TimeGranularity {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  YEAR = 'YEAR',
}

export interface GroupBy {
  time: TimeGranularity;
}

export enum FilterOperator {
  EQUALS = 'equals',
  IN = 'in',
  BETWEEN = 'between',
  GT = 'gt',
  LT = 'lt',
  AFTER = 'after',
  BEFORE = 'before',
}

export interface Filter {
  key: string;
  operator: Operator;
  value: any;
}

export interface Widget {
  id: string;
  widgetType: WidgetType;
  name: string;
  gridX: number;
  gridY: number;
  gridWidth: number;
  gridHeight: number;
  visualizationType: VisualizationType;
  filter: WidgetFilter;
  data?: WidgetVisualizationDto;
  dateCreated: string;
  lastUpdated: string;
  parameters?: WidgetVisualizationDto;
  isCompareToPreviousPeriodEnabled?: boolean;
  isViewDataLabelsEnabled?: boolean;
  isKpiLiftEnabled?: boolean;
  isIncludeTotalEnabled?: boolean;
}

export interface SaveWidgetDto {
  widgetType: string;
  name: string;
  description?: string;
  visualizationType: string;
  parameters: WidgetVisualizationDto;
  filter?: WidgetFilter;
  isCompareToPreviousPeriodEnabled: boolean;
  isViewDataLabelsEnabled: boolean;
  isKpiLiftEnabled: boolean;
  isIncludeTotalEnabled: boolean;
  gridX: number;
  gridY: number;
  gridWidth: number;
  gridHeight: number;
  id?: string;
}

export interface WidgetFilter {
  filters: Filter[];
  sortBy?: SortBy;
}

export enum WidgetFormatTypeEnum {
  NONE = 'none',
  SPEND = 'spend',
  PERCENTAGE = 'percentage',
  FREQUENCY = 'frequency',
  SECOND = 'second',
  MILLISECOND = 'millisecond',
  ASSET = 'asset',
  IMPRESSION = 'impression',
}

export enum MultiSeriesChartType {
  BAR = 'bar',
  COLUMN = 'column',
  LINE = 'line',
}

export interface WidgetTypeInfo {
  widgetType: WidgetType;
  name: string;
  description: string;
  iconUrl: string;
  defaultVisualizationType: VisualizationType;
  visualizationTypes: VisualizationType[];
  requiredFilters: FilterField[];
  optionalFilters: FilterField[];
  defaultFilter: WidgetFilter;
}

export enum SharingScope {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
}

export interface SaveDashboardParams {
  organizationId: string;
  payload: SaveDashboardRequest;
  dashboardId?: string;
}

export interface SaveDashboardRequest {
  name?: string;
  description?: string;
  sourceDashboardId?: string;
  dashboardTemplateId?: string | null;
  sharingScope?: SharingScope;
  dashboardFilter?: ReportFilterInputDto | null;
  widgets?: SaveWidgetDto[];
  id?: string;
}
