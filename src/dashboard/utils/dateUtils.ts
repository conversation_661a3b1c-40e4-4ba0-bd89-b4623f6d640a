import { IntlShape } from 'react-intl';
import { WIDGET_INTL_KEYS } from '../../widgets/copy/widgetCopy';
import { formatDateRange } from '../components/widgetEdit/widgetEdit.utils';
import {
  DateFilterType,
  DateFilterValue,
  QuickRangeKey,
} from '../types/dashboardTypes';
import { Filter } from '../dashboard.types';

export const QUICK_RANGES = [
  {
    key: QuickRangeKey.P7D,
    intlId: 'ui.globalFilters.dateRange.last7Days',
    defaultMessage: 'Last 7 days',
  },
  {
    key: QuickRangeKey.P30D,
    intlId: 'ui.globalFilters.dateRange.last30Days',
    defaultMessage: 'Last 30 days',
  },
  {
    key: QuickRangeKey.P3M,
    intlId: 'ui.globalFilters.dateRange.last3Months',
    defaultMessage: 'Last 3 months',
  },
  {
    key: QuickRangeKey.P6M,
    intlId: 'ui.globalFilters.dateRange.last6Months',
    defaultMessage: 'Last 6 months',
  },
  {
    key: QuickRangeKey.P12M,
    intlId: 'ui.globalFilters.dateRange.last12Months',
    defaultMessage: 'Last 12 months',
  },
  {
    key: QuickRangeKey.MTD,
    intlId: 'ui.globalFilters.dateRange.monthToDate',
    defaultMessage: 'Month to date',
  },
  {
    key: QuickRangeKey.QTD,
    intlId: 'ui.globalFilters.dateRange.quarterToDate',
    defaultMessage: 'Quarter to date',
  },
  {
    key: QuickRangeKey.YTD,
    intlId: 'ui.globalFilters.dateRange.yearToDate',
    defaultMessage: 'Year to date',
  },
];

export function serializeDateFilter(val: DateFilterValue) {
  return val.type === DateFilterType.PRESET
    ? val.key
    : ([val.startDate, val.endDate] as [string, string]);
}

export const toDateFilterValue = (raw: any): DateFilterValue => {
  if (typeof raw === 'string') {
    return { type: DateFilterType.PRESET, key: raw as QuickRangeKey };
  }
  if (Array.isArray(raw) && raw.length === 2 && raw[0] && raw[1]) {
    return { type: DateFilterType.CUSTOM, startDate: raw[0], endDate: raw[1] };
  }
  return { type: DateFilterType.CUSTOM, startDate: '', endDate: '' };
};

export function getDateRangeLabel(
  val: DateFilterValue | undefined,
  formatMessage: IntlShape['formatMessage'],
): string {
  if (!val) return '';

  if (val.type === DateFilterType.PRESET) {
    const match = QUICK_RANGES.find((r) => r.key === val.key);
    return formatMessage({
      id: match?.intlId ?? WIDGET_INTL_KEYS.dateRange.custom.id,
      defaultMessage: match?.defaultMessage,
    });
  }

  return formatDateRange(
    { startDate: val.startDate, endDate: val.endDate },
    formatMessage,
  );
}

export const extractDateFilterValue = (raw?: Filter) => {
  return toDateFilterValue(raw?.value);
};

export const checkIfDateValueIsPreset = (val: string | string[]) => {
  if (typeof val === 'string') {
    return QUICK_RANGES.some((r) => r.key === val);
  }
  return val.some((v) => QUICK_RANGES.some((r) => r.key === v));
};
