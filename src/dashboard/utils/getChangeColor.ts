export const getChangeColor = (
  higherIsBetter: boolean,
  changePercentage?: number | null,
) => {
  let backgroundColor: string;
  let textColor: string;

  if (!changePercentage) {
    textColor = 'text.primary';
    backgroundColor = 'background.secondary';
    return { backgroundColor, textColor };
  }

  if (higherIsBetter) {
    if (changePercentage! > 0) {
      backgroundColor = 'success.light';
      textColor = 'success.main';
    } else {
      backgroundColor = 'error.light';
      textColor = 'error.main';
    }
  } else {
    if (changePercentage! > 0) {
      backgroundColor = 'error.light';
      textColor = 'error.main';
    } else {
      backgroundColor = 'success.light';
      textColor = 'success.main';
    }
  }
  return { backgroundColor, textColor };
};
