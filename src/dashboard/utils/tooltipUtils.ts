import { TooltipFormatterContextObject } from 'highcharts';
import { IntlShape } from 'react-intl';

import { SeriesData } from '../types/dashboardTypes';
import { MultiSeriesChartType, WidgetFormatTypeEnum } from '../dashboard.types';
import { Currency } from '../../types/currency.types';
import { WIDGET_INTL_KEYS } from '../../widgets/copy/widgetCopy';
import { formatWidgetValue } from './formatValues';
import { textStyle } from '../components/visualizations/utils/tooltipStyle';

const pick = <T>(arr: (T | null | undefined)[] | undefined, i: number) =>
  arr && arr.length ? (arr[Math.min(i, arr.length - 1)] ?? null) : null;

const buildTooltipHeader = (name: string, color?: string, iconUrl?: string) => {
  const iconMarkup = iconUrl
    ? `<img src="${iconUrl}" width="18" height="18" style="flex-shrink:0;" />`
    : color
      ? `<span style="width:10px;height:10px;background:${color};display:inline-block;flex-shrink:0;"></span>`
      : '';

  return `
    <div style="display:flex;align-items:center;gap:6px;margin-bottom:16px;">
      ${iconMarkup}
      <span style="${textStyle}font-weight:600;font-size:14px;">${name}</span>
    </div>
  `;
};

interface DefaultTooltipFormatterProps {
  rawSeries: SeriesData[];
  unitLabel: WidgetFormatTypeEnum;
  primaryColors: string[];
  secondaryColors: string[];
  categories: string[];

  intl: IntlShape;
  currency?: Currency;
  isCompareToPreviousPeriodEnabled: boolean;

  previousCategories?: string[];
  displayCategories?: string[];
  displayPreviousCategories?: string[];
}

export function makeDefaultTooltipFormatter({
  categories,
  rawSeries,
  unitLabel,
  primaryColors,
  secondaryColors,
  intl,
  currency,
  isCompareToPreviousPeriodEnabled,

  previousCategories = [],
  displayCategories = [],
  displayPreviousCategories = [],
}: DefaultTooltipFormatterProps) {
  return function (this: TooltipFormatterContextObject): string {
    const point = this.point as Highcharts.Point & {
      custom?: { categoryIdx?: number; iconUrl?: string };
    };

    const idx =
      typeof point.custom?.categoryIdx === 'number'
        ? point.custom.categoryIdx
        : point.index;
    const groupLabel = typeof this.x === 'string' ? this.x : categories[idx];

    const category = pick(displayCategories, idx) ?? groupLabel;
    const prevCategory =
      pick(displayPreviousCategories, idx) ??
      previousCategories[idx] ??
      intl.formatMessage(WIDGET_INTL_KEYS.previous);

    const baseSeriesName = this.series.name.replace(/ \(Prev\)$/, '');
    let seriesIndex = rawSeries.findIndex((s) => s.name === baseSeriesName);
    if (seriesIndex < 0) seriesIndex = idx;

    const headerLabel =
      this.series.type === MultiSeriesChartType.BAR
        ? groupLabel
        : baseSeriesName;
    const displayLabel = category;
    const displayPrevLabel = prevCategory;

    const currentVal = pick(rawSeries[seriesIndex].data, idx) ?? null;
    const prevVal = pick(rawSeries[seriesIndex].previousPeriodData, idx);
    const change = pick(rawSeries[seriesIndex].changePercentageData, idx);

    const iconUrl =
      point.custom?.iconUrl ?? rawSeries[seriesIndex].iconUrl ?? '';

    let html = `
      <div style="background:#FFF;border-radius:6px;padding:12px;box-shadow:0 2px 8px rgba(0,0,0,0.1);border:1px solid #BDBDBD;">
        ${buildTooltipHeader(headerLabel, undefined, iconUrl)}
        <div style="${textStyle}">
          <div style="display:flex;align-items:center;">
            <span style="width:10px;height:10px;background:${primaryColors[seriesIndex]};margin-right:6px;display:inline-block;"></span>
            <span style="color:#757575">${displayLabel}</span>
          </div>
          <div style="padding-left:16px;">
            ${formatWidgetValue(currentVal, unitLabel, { currency, intl })}
          </div>
        </div>
    `;

    if (isCompareToPreviousPeriodEnabled && typeof change === 'number') {
      const color = change > 0 ? '#388E3C' : change < 0 ? '#D32F2F' : '#212121';

      html += `
        <div style="margin-bottom:4px;${textStyle};padding-left:16px;">
          <span style="color:${color};">${change > 0 ? '+' : ''}${change}%</span>
          from previous period
        </div>
      `;
    }

    if (isCompareToPreviousPeriodEnabled && prevVal != null) {
      html += `
        <div style="margin-top:16px;margin-bottom:4px;${textStyle}">
          <div style="display:flex;align-items:center;">
            <span style="width:10px;height:10px;background:${secondaryColors[seriesIndex]};margin-right:6px;display:inline-block;flex-shrink:0;"></span>
            <span style="color:#757575">${displayPrevLabel}</span>
          </div>
          <div style="padding-left:16px;">
            ${formatWidgetValue(prevVal, unitLabel, { currency, intl })}
          </div>
        </div>
      `;
    }

    return html + '</div>';
  };
}

export const makeDonutTooltipFormatter = (
  unitLabel: WidgetFormatTypeEnum,
  intl: IntlShape,
  currency?: Currency,
): Highcharts.TooltipFormatterCallbackFunction =>
  function (this: Highcharts.TooltipFormatterContextObject): string {
    const p = this.point as Highcharts.Point & {
      name: string;
      y: number;
      percentage: number;
      color: string;
    };
    const icon = (p as any).custom?.iconUrl;

    return `
      <div style="background:#FFF;border-radius:6px;padding:12px;border:1px solid #E0E0E0;">
        ${buildTooltipHeader(p.name, p.color, icon)}
        <div style="${textStyle};margin-left:16px">
          ${formatWidgetValue(p.y, unitLabel, { currency, intl })}
        </div>
        <div style="${textStyle};margin-left:16px">
          ${p.percentage.toFixed(1)}% of total
        </div>
      </div>
    `;
  };
