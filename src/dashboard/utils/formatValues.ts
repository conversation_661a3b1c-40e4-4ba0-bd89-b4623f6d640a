import { IntlShape } from 'react-intl';
import { KpiFormatter } from '../../creativeAnalytics/services/KpiEngine/KpiFormatter';
import { Currency } from '../../types/currency.types';
import fuzzyNumber from '../../utils/fuzzyNumber';
import localizeNumberAndSetFractionDigits from '../../utils/localizeNumberAndSetFractionDigits';
import { WidgetFormatTypeEnum } from '../dashboard.types';
import { WIDGET_INTL_KEYS } from '../../widgets/copy/widgetCopy';
import { GLOBALS } from '../../constants';

const { EM_DASH_UNICODE } = GLOBALS;

type FormatFn = (
  value: number,
  opts: { currency?: Currency; fuzzy?: boolean; intl?: IntlShape },
) => string;

const rawFormatter: FormatFn = (v, { currency }) =>
  KpiFormatter.raw(v, true, currency);

const getNumberOrFuzzy = (v: number, fuzzy: boolean) =>
  fuzzy
    ? fuzzyNumber(v)
    : localizeNumberAndSetFractionDigits(v, 0, undefined, true);

const FORMATTERS: Record<WidgetFormatTypeEnum, FormatFn> = {
  [WidgetFormatTypeEnum.ASSET]: (v, { fuzzy, intl }) =>
    `${getNumberOrFuzzy(v, Boolean(fuzzy))} ${
      intl?.formatMessage(WIDGET_INTL_KEYS.units.assets) ?? 'assets'
    }`,

  [WidgetFormatTypeEnum.IMPRESSION]: (v, { fuzzy, intl }) =>
    `${getNumberOrFuzzy(v, Boolean(fuzzy))} ${
      intl?.formatMessage(WIDGET_INTL_KEYS.units.impressions) ?? 'impressions'
    }`,

  [WidgetFormatTypeEnum.PERCENTAGE]: (v) => KpiFormatter.percentage(v, true),

  [WidgetFormatTypeEnum.SPEND]: (v, { currency, fuzzy }) =>
    KpiFormatter.spend(v, true, currency, undefined, fuzzy),

  [WidgetFormatTypeEnum.MILLISECOND]: (v) => KpiFormatter.millisecond(v, true),

  [WidgetFormatTypeEnum.SECOND]: (v) => KpiFormatter.second(v, true),

  [WidgetFormatTypeEnum.FREQUENCY]: (v) => KpiFormatter.frequency(v, true),

  [WidgetFormatTypeEnum.NONE]: (v, { currency, fuzzy }) =>
    getNumberOrFuzzy(v, Boolean(fuzzy)) ?? rawFormatter(v, { currency }),
};

export function formatWidgetValue(
  value: number | string | null,
  formatType: WidgetFormatTypeEnum = WidgetFormatTypeEnum.NONE,
  options?: {
    currency?: Currency;
    fuzzy?: boolean;
    kpiOnly?: boolean;
    intl?: IntlShape;
  },
  defaultDisplayName?: string,
): string {
  if (defaultDisplayName) {
    return defaultDisplayName;
  }

  if (value === null || value === undefined) {
    return EM_DASH_UNICODE;
  }

  if (typeof value !== 'number') {
    return value;
  }

  const { currency, fuzzy = false, kpiOnly = false, intl } = options ?? {};

  if (
    kpiOnly &&
    ![
      WidgetFormatTypeEnum.PERCENTAGE,
      WidgetFormatTypeEnum.SPEND,
      WidgetFormatTypeEnum.MILLISECOND,
      WidgetFormatTypeEnum.SECOND,
      WidgetFormatTypeEnum.FREQUENCY,
      WidgetFormatTypeEnum.NONE,
    ].includes(formatType)
  ) {
    return rawFormatter(value, { currency });
  }

  const formatter = FORMATTERS[formatType] ?? rawFormatter;
  return formatter(value, { currency, fuzzy, intl });
}
