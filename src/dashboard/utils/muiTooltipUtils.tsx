import React, { FC } from 'react';
import {
  VidMobBox,
  VidMobStack,
  VidMobSvgIcon,
  VidMobTypography,
} from '../../vidMobComponentWrappers';
import { getChangeColor } from './getChangeColor';

interface TooltipProps {
  baseName: string;
  category?: string | null;
  previousCategory?: string;
  primaryVal: string;
  secondaryVal?: string | null;
  changePercentage?: number | null;
  primaryColor?: string; // optional color for “current” square
  secondaryColor?: string; // optional color for “previous” square
  isHigherBetter?: boolean;
  iconUrl?: string;
  showTooltip?: boolean;
  isCompareToPreviousPeriodEnabled: boolean;
}

const MuiWidgetTooltip: FC<TooltipProps> = ({
  baseName,
  category,
  previousCategory,
  primaryVal,
  secondaryVal,
  changePercentage,
  primaryColor,
  secondaryColor,
  isHigherBetter = true,
  iconUrl = '',
  showTooltip = true,
  isCompareToPreviousPeriodEnabled,
}) => {
  const { textColor } = getChangeColor(isHigherBetter, changePercentage);
  if (!showTooltip) {
    return null;
  }
  return (
    <VidMobBox
      sx={{
        backgroundColor: 'white',
        borderRadius: '6px',
        padding: 6,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        border: '1px solid',
        borderColor: 'text.disabled',
        maxWidth: 200,
      }}
    >
      <VidMobStack direction="row" gap={2}>
        {iconUrl ? (
          <VidMobSvgIcon fontSize="small">
            <image href={iconUrl} width="100%" height="100%" />
          </VidMobSvgIcon>
        ) : null}
        <VidMobTypography
          variant="subtitle2"
          color="text.primary"
          sx={{ fontWeight: 600, mb: 8 }}
        >
          {baseName}
        </VidMobTypography>
      </VidMobStack>

      <VidMobBox display="flex" alignItems="center" mb={0.5}>
        {primaryColor && (
          <VidMobBox
            sx={{
              width: 10,
              height: 10,
              backgroundColor: primaryColor,
              margin: 2,
              borderRadius: '2px',
            }}
          />
        )}

        <VidMobBox display="flex" flexDirection="column">
          {category && (
            <VidMobTypography variant="caption" color="text.secondary">
              {category}
            </VidMobTypography>
          )}
          <VidMobTypography variant="caption" color="text.primary" ml={8}>
            {primaryVal}
          </VidMobTypography>
        </VidMobBox>
      </VidMobBox>

      {isCompareToPreviousPeriodEnabled && changePercentage != null && (
        <VidMobTypography
          variant="caption"
          sx={{ mb: 0.5, fontWeight: 500, color: 'text.primary', ml: 8 }}
        >
          <VidMobBox component="span" sx={{ color: textColor }}>
            {changePercentage > 0 ? '+' : ''}
            {changePercentage.toFixed(2)}%
          </VidMobBox>{' '}
          from previous period
        </VidMobTypography>
      )}

      {isCompareToPreviousPeriodEnabled && secondaryVal != null && (
        <VidMobBox display="flex" alignItems="center" marginTop={8}>
          {secondaryColor && (
            <VidMobBox
              sx={{
                width: 10,
                height: 10,
                backgroundColor: secondaryColor,
                marginRight: 1,
                borderRadius: '2px',
                padding: 2,
              }}
            />
          )}
          <VidMobBox display="flex" flexDirection="column">
            <VidMobTypography variant="caption" color="text.secondary">
              {previousCategory || 'Previous'}
            </VidMobTypography>
            <VidMobTypography variant="caption" color="text.primary" ml={8}>
              {secondaryVal}
            </VidMobTypography>
          </VidMobBox>
        </VidMobBox>
      )}
    </VidMobBox>
  );
};

export default MuiWidgetTooltip;
