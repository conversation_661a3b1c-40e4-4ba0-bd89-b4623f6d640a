import { VisualizationType } from '../../widgets/types/visualizationType';
import {
  MultiSeriesChartDto,
  DonutChartDto,
  MetricDto,
  TableDto,
  WidgetVisualizationDto,
} from '../dashboard.types';

export function isEmptyVisualization(
  visualizationType: VisualizationType,
  widgetProperties: WidgetVisualizationDto,
): boolean {
  switch (visualizationType) {
    case VisualizationType.donut: {
      const donut = widgetProperties as DonutChartDto;
      return !Array.isArray(donut.values) || donut.values.length === 0;
    }

    case VisualizationType.metric: {
      const metric = widgetProperties as MetricDto;
      return !Array.isArray(metric.values) || metric.values.length === 0;
    }

    case VisualizationType.bar:
    case VisualizationType.column:
    case VisualizationType.line: {
      const multi = widgetProperties as MultiSeriesChartDto;
      return !Array.isArray(multi.series) || multi.series.length === 0;
    }

    case VisualizationType.table: {
      const table = widgetProperties as TableDto;
      return !Array.isArray(table.rows) || table.rows.length === 0;
    }

    default:
      return true;
  }
}
