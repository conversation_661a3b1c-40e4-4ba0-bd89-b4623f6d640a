import { ReportFilterInputDto } from '../../widgets/types/widgetTypes';
import { WIDGET_ID_PREFIX } from '../constants/widgetConstants';
import {
  SaveDashboardRequest,
  SaveWidgetDto,
  SharingScope,
  Widget,
  WidgetFilter,
  WidgetVisualizationDto,
} from '../dashboard.types';

export function toNewWidgetDto(w: Widget): SaveWidgetDto {
  return {
    widgetType: w.widgetType,
    name: w.name,
    visualizationType: w.visualizationType,
    parameters: w.parameters ?? ({} as WidgetVisualizationDto),
    filter: w.filter as WidgetFilter | undefined,
    isCompareToPreviousPeriodEnabled: Boolean(
      w.isCompareToPreviousPeriodEnabled,
    ),
    isKpiLiftEnabled: Boolean(w.isKpiLiftEnabled),
    isViewDataLabelsEnabled: <PERSON>olean(w.isViewDataLabelsEnabled),
    isIncludeTotalEnabled: Boolean(w.isViewDataLabelsEnabled),
    gridX: w.gridX,
    gridY: w.gridY,
    gridWidth: w.gridWidth,
    gridHeight: w.gridHeight,
    id: w.id.startsWith(WIDGET_ID_PREFIX) ? undefined : w.id,
  };
}

export function buildSaveDashboardPayload(opts: {
  name: string;
  description?: string;
  sharingScope: SharingScope;
  dashboardFilter?: ReportFilterInputDto;
  widgets: Widget[];
}): SaveDashboardRequest {
  const { name, description, sharingScope, dashboardFilter, widgets } = opts;

  return {
    name,
    description,
    sharingScope,
    dashboardFilter,
    widgets: widgets.map(toNewWidgetDto),
  };
}
