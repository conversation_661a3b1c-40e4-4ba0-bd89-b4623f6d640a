import { COLUMNS, BASE_HEIGHT } from '../constants/dashboardConstants';
import { VisualizationType } from '../../widgets/types/visualizationType';
import { getDataVisualizationRegistryItem } from '../../widgets/utils/dataVisualizationRegistry';
import { Layout } from 'react-grid-layout';
import { WidgetTypeInfo } from '../dashboard.types';
import { LandingPageFilterType } from '../components/landingPage/types/landingPageTypes';
import { LandingPageFilterId } from '../../muiCustomComponents/ControlBarFilters/controlBarFilters.types';
import { isNotEmpty } from '../../muiCustomComponents/ControlBarFilters/Filters/FilterUtils/controlBarFilters.utils';
import { convertDateToServerModel } from '../../creativeAnalytics/insights/components/InsightsV2/insightUtils';

export const makeLayoutId = (vis: VisualizationType, widgetId: string) =>
  `${vis}_${widgetId}`;

/** Split ← { vis: "bar", widgetId: "3b7d" } */
export const splitLayoutId = (layoutId: string) => {
  const [vis, ...rest] = layoutId.split('_');
  return { vis: vis as VisualizationType, widgetId: rest.join('_') };
};

/** given a visualisation type, build a fresh Layout item */
export const buildLayoutItem = (
  widget: WidgetTypeInfo,
  id: string,
  x: number,
  y: number,
  wOverride?: number,
  hOverride?: number,
): Layout => {
  const def = getDataVisualizationRegistryItem(widget.defaultVisualizationType);

  const w = wOverride ?? def.defaultW;
  const h = hOverride ?? BASE_HEIGHT;

  return {
    i: id,
    x,
    y,
    w,
    h,
    minW: def.minW,
    minH: BASE_HEIGHT,
    maxH: BASE_HEIGHT,
    isResizable: true,
  };
};

export const getMinHeight = (
  vis: VisualizationType | null,
  w: number,
): number => {
  if (!vis) return BASE_HEIGHT;
  const isFullText = vis === VisualizationType.text && w === COLUMNS;
  return isFullText ? 2 : BASE_HEIGHT;
};

export const replacePlaceholder = (
  grid: Layout[],
  dropped: Layout,
  updated: Partial<Layout>,
) => grid.map((l) => (l.i === dropped.i ? { ...l, ...updated } : l));

export const snapLayoutArray = (arr: Layout[]): Layout[] =>
  arr.map((item) => {
    if (item.i === '__DROP__') return item;

    const { vis } = splitLayoutId(item.i);
    const w = Math.max(1, Math.round(item.w));
    const x = Math.round(item.x);
    const clampedX = Math.min(COLUMNS - w, Math.max(0, x));

    const minH = getMinHeight(vis, w);
    const h = Math.max(minH, Math.round(item.h));

    const y = findLandingY(arr, { ...item, x: clampedX, w, h }, item.y);
    return { ...item, x: clampedX, w, h, minH, y };
  });

export const findNextSpot = (
  layout: Layout[],
  newW: number,
  newH: number,
  cols: number,
  startRow: number = 0,
): { x: number; y: number } => {
  const maxY = layout.reduce((m, i) => Math.max(m, i.y + i.h), 0);
  const rows: number[] = [];
  for (let y = startRow; y <= maxY; y++) rows.push(y);
  for (let y = 0; y < startRow; y++) rows.push(y);

  for (const y of rows) {
    for (let x = 0; x <= cols - newW; x++) {
      const collision = layout.some(
        (item) =>
          x < item.x + item.w &&
          x + newW > item.x &&
          y < item.y + item.h &&
          y + newH > item.y,
      );
      if (!collision) {
        return { x, y };
      }
    }
  }

  return { x: 0, y: maxY };
};

export const getWidgetLayout = (
  layout: Layout[],
  widgetId: string,
  defaultW: number,
  defaultH: number,
) => {
  const item = layout.find((l) => l.i === widgetId);
  if (!item) {
    return {
      gridX: 0,
      gridY: 0,
      gridWidth: defaultW,
      gridHeight: defaultH,
    } as const;
  }
  return {
    gridX: item.x,
    gridY: item.y,
    gridWidth: item.w,
    gridHeight: item.h,
  } as const;
};

export type DashboardListFilterServerModel = { [key: string]: any };

export function convertFiltersToServerModel(
  filters?: LandingPageFilterType,
  searchTerm?: string,
): DashboardListFilterServerModel {
  let serverFilters: DashboardListFilterServerModel = {};

  if (!filters && !searchTerm) {
    return serverFilters;
  }

  if (searchTerm) {
    serverFilters.searchTerm = searchTerm;
  }

  if (!filters) {
    return serverFilters;
  }

  Object.entries(filters).forEach(([key, value]) => {
    if (isNotEmpty(value)) {
      switch (key) {
        case LandingPageFilterId.DATE_CREATED: {
          serverFilters = {
            ...serverFilters,
            [key]: convertDateToServerModel(value as Date[]),
          };
          return;
        }
        case LandingPageFilterId.LAST_UPDATED: {
          serverFilters = {
            ...serverFilters,
            [LandingPageFilterId.LAST_UPDATED]: convertDateToServerModel(
              value as Date[],
            ),
          };
          return;
        }
        default:
          serverFilters[key] = value;
      }
    }
  });

  return serverFilters;
}

export function findLandingY(layout: Layout[], item: Layout, pointerY: number) {
  const { x, w, h } = item;
  const minH = getMinHeight(null, w);

  let y = Math.max(0, pointerY);

  const above = layout
    .filter(
      (l) =>
        l.i !== item.i && // ignore self / placeholder
        x < l.x + l.w &&
        x + w > l.x && // share at least 1 column
        l.y + l.h <= y, // block is fully above pointer
    )
    .map((l) => l.y + l.h);

  if (above.length) {
    y = Math.max(...above);
  } else {
    y = Math.floor(y / minH) * minH;
  }

  const collides = (other: Layout) =>
    x < other.x + other.w &&
    x + w > other.x &&
    y < other.y + other.h &&
    y + h > other.y;

  while (layout.some((l) => l.i !== item.i && collides(l))) {
    y += 1;
  }

  return y;
}
