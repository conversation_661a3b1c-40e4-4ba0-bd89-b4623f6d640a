import { trackCustomEventGainsight } from '../utils/gainsight';
import BffService from './BffService';

const { handleBffApiPost } = BffService;

interface Insight {
  title: string;
  reportType: string;
  savedAnalyticsReportId: string;
  type: string;
  platform: string;
  source: string;
  status: string;
  insightReportUrl: string;
  mediaTypes: string[];
  adAccountIds: string[];
  startDate: number;
  endDate: number;
  publishDate: number;
  brandPlatformMedia: { key: string }[];
  brandAnalysis: {
    platformMediaIds: string[];
    kpi: { id: string; name: string };
  };
}

export interface InsightData {
  insight: Insight;
  organizationId: string;
  workspaceIds: number[];
}

const trackEvent = (insightData: InsightData) => {
  try {
    const insight = insightData?.insight;
    if (!insight) return;

    const filters = {
      platform: insight.platform,
      savedAnalyticsReportId: insight.savedAnalyticsReportId,
      status: insight.status,
      type: insight.type,
      source: insight.source,
      mediaTypes: insight.mediaTypes,
      endDate: insight.endDate,
      startDate: insight.startDate,
      adAccountIds: insight.adAccountIds,
      kpi: insight.brandAnalysis?.kpi,
    };

    trackCustomEventGainsight('created_element_insight_user_generated', {
      insightName: insight.title,
      reportType: insight.reportType,
      elementTypes: Array.isArray(insight.brandPlatformMedia)
        ? insight.brandPlatformMedia.map((i) => i.key)
        : [],
      creativeExamples: insight.brandAnalysis?.platformMediaIds,
      filters: filters,
    });
  } catch (error) {
    console.error('Error tracking event:', error);
  }
};

class BffApiInsightServiceV2 {
  createInsightV2 = async (insightData: InsightData) => {
    const { insight, organizationId, workspaceIds } = insightData;
    const {
      platform,
      savedAnalyticsReportId,
      insightReportUrl,
      publishDate,
      ...others
    } = insight;

    trackEvent(insightData);

    const response = await handleBffApiPost(
      `/v1/organization/${organizationId}/insight`,
      {
        ...others,
        platform,
        reportId: savedAnalyticsReportId,
        workspaceIds,
        organizationId,
      },
    );
    return { insight: response };
  };
}

export default new BffApiInsightServiceV2();
