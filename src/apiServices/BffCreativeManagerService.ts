import BffService from './BffService';
import { PaginationParams } from '../types/pagination.types';
import { CreativeManagerRequestParams } from '../creativeAnalytics/__pages/CreativeManager/helpers/CreativeManagerTypes';

const { handleBffApiPostWithPagination } = BffService;

class BffCreativeManagerService {
  getCreatives = async (
    organizationId: string,
    params: CreativeManagerRequestParams,
    paginationOptions: PaginationParams,
  ) => {
    const { offset, perPage } = paginationOptions;
    return await handleBffApiPostWithPagination(
      `v1/creative-manager/organization/${organizationId}/creatives?perPage=${perPage}&offset=${offset}`,
      params,
    );
  };

  getAds = async (
    organizationId: string,
    params: CreativeManagerRequestParams,
    paginationOptions: PaginationParams,
  ) => {
    const { offset, perPage } = paginationOptions;
    return await handleBffApiPostWithPagination(
      `v1/creative-manager/organization/${organizationId}/ads?perPage=${perPage}&offset=${offset}`,
      params,
    );
  };
}

export default new BffCreativeManagerService();
