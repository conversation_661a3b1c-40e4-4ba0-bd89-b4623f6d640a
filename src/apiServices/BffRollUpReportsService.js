import { Api<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vmBffApiManager } from '../vidmobConnectionManager';
import BffService from './BffService';
import { scrubTimeZoneFromDateString } from '../utils/scrubTimeZoneFromDateString';
import { SCORING_REPORTS_TYPE_TO_URL } from '../creativeScoring/components/reports/rollUpReport/rollUpReport.constants';
import { SCORING_REPORT_FILTERS } from '../creativeScoring/components/ScoringFilters/types';
import { getFeatureFlag } from '../utils/featureFlagUtils';

const {
  handleBffApiGet,
  handleBffApiGetWithPagination,
  handleBffApiPost,
  handleBffApiPostWithStatus,
  handleBffApiPatch,
  handleBffApiDelete,
  handleBffApiPostWithPagination,
} = BffService;

const isCriteriaGroupsInReportsEnabled = getFeatureFlag(
  'isCriteriaGroupsInReportsEnabled',
);

const getReportsVersion = (filtersVersion) => {
  return `v${filtersVersion}`;
};

/**
getReportsList: get list of saved reports
createReportMetadata: create metadata for new report (name, description, filters, etc)
getReportMetadataByReportId: get metadata for saved report
getRollUpReportData: get a single report
getAvailableFilterOptions: get list of options for filter dropdowns (eg. available worksapces)
saveReport: save report metadata
updateReport: save updated report metadata
 */

class BffRollUpReportsService extends ApiRequestHandler {
  _scrubTimeZoneFromMetadata = (metadata) => {
    return {
      ...metadata,
      filters: metadata.filters.map((item) =>
        item.fieldName === SCORING_REPORT_FILTERS.DATE
          ? {
              ...item,
              value: item.value.map((date) =>
                scrubTimeZoneFromDateString(date),
              ),
            }
          : item,
      ),
    };
  };

  getReportTypesWorkspaceHasAccessTo = async ({ workspaceId }) => {
    const endpoint = `/v1/reports/workspace/${workspaceId}/report-types`;
    return handleBffApiGet(endpoint);
  };

  getReportsList = async ({
    reportTypes,
    workspaceId,
    searchTerm = null,
    offset = null,
    sortOrder = null,
    sortBy = null,
  }) => {
    const endpoint = `/v2/report/workspace/${workspaceId}`;
    const requestParams = {
      types: reportTypes.join(','),
      perPage: 10,
      offset,
      searchTerm,
      sortOrder,
      sortBy,
    };
    return handleBffApiGetWithPagination(endpoint, requestParams);
  };

  getReportsListV2 = async (
    workspaceId,
    filters,
    paginationOptions,
    sortOptions,
  ) => {
    const { offset = 0, perPage = 25 } = paginationOptions;
    const { sortBy, sortOrder } = sortOptions;

    let endpoint = `/v2/report/workspace/${workspaceId}?offset=${offset}&perPage=${perPage}`;
    if (sortBy) {
      endpoint += `&sortBy=${sortBy}`;
    }
    if (sortOrder) {
      endpoint += `&sortOrder=${sortOrder}`;
    }
    if (filters.searchTerm) {
      endpoint += `&searchTerm=${filters.searchTerm}`;
    }

    const requestParams = {
      ...filters,
      offset,
      perPage,
    };
    return handleBffApiPostWithPagination(endpoint, requestParams);
  };

  getReportsListV3 = async (
    workspaceId,
    filters,
    paginationOptions,
    sortOptions,
  ) => {
    const { offset = 0, perPage = 25 } = paginationOptions;
    const { sortBy, sortOrder } = sortOptions;

    let endpoint = `/v3/report/workspace/${workspaceId}?offset=${offset}&perPage=${perPage}`;
    if (sortBy) {
      endpoint += `&sortBy=${sortBy}`;
    }
    if (sortOrder) {
      endpoint += `&sortOrder=${sortOrder}`;
    }
    if (filters.searchTerm) {
      endpoint += `&searchTerm=${filters.searchTerm}`;
    }

    const requestParams = {
      ...filters,
      offset,
      perPage,
    };
    return handleBffApiPostWithPagination(endpoint, requestParams);
  };

  createReportMetadata = async ({ reportType, workspaceId }) => {
    let endpoint;

    if (isCriteriaGroupsInReportsEnabled) {
      endpoint = `/v3/report/create/workspace/${workspaceId}?type=${reportType}`;
    } else {
      endpoint = `/v2/report/create/workspace/${workspaceId}?type=${reportType}`;
    }

    return handleBffApiGet(endpoint);
  };

  getReportMetadataByReportId = async ({
    reportId,
    organizationId,
    includeInflightFiltersDisplayNames = false,
  }) => {
    let endpoint;

    if (isCriteriaGroupsInReportsEnabled) {
      endpoint = `/v4/report/${reportId}/organization/${organizationId}/metadata`;
    } else {
      endpoint = `/v3/report/${reportId}/organization/${organizationId}/metadata`;
    }

    if (includeInflightFiltersDisplayNames) {
      endpoint += '?includeInflightFiltersDisplayNames=true';
    }

    return handleBffApiGet(endpoint);
  };

  getRollUpReportData = async (reportType, partnerId, metadata) => {
    const reportTypeForEndpoint = SCORING_REPORTS_TYPE_TO_URL[reportType];
    const { filtersVersion } = metadata;
    const endpoint = `/${getReportsVersion(filtersVersion)}/reports/${reportTypeForEndpoint}/workspace/${partnerId}`;
    return handleBffApiPost(
      endpoint,
      this._scrubTimeZoneFromMetadata(metadata, reportType),
    );
  };

  getReportCsv = async (reportType, partnerId, metadata) => {
    const reportTypeForEndpoint = SCORING_REPORTS_TYPE_TO_URL[reportType];
    const endpoint = `/v1/reports/${reportTypeForEndpoint}/workspace/${partnerId}/csv`;
    return handleBffApiPost(endpoint, metadata);
  };

  getAvailableFilterOptions = async (partnerId) => {
    const endpoint = `/v1/reports/workspace/${partnerId}/options`;
    return handleBffApiGet(endpoint);
  };

  saveReport = async ({ metadata, organizationId, partnerId }) => {
    const endpoint = `v2/report/organization/${organizationId}/metadata`;
    const requestParams = { ...metadata, workspaceId: partnerId };
    const response = await handleBffApiPostWithStatus(endpoint, requestParams);
    const { status, data } = response;
    return { status, data };
  };

  updateReport = async ({ reportId, metadata, organizationId }) => {
    const endpoint = `v2/report/${reportId}/organization/${organizationId}/metadata`;
    return handleBffApiPatch(endpoint, metadata);
  };

  deleteReport = async (reportId, userId) => {
    const endpoint = `/v1/report/${reportId}`;
    const requestParams = { userId };
    const response = await handleBffApiDelete(endpoint, requestParams);
    const { status, data } = response;
    return { status, data };
  };
}

export default new BffRollUpReportsService(vmBffApiManager);
