import { ApiRequestHand<PERSON>, vmBffApiManager } from '../vidmobConnectionManager';
import BffService from './BffService';
import {
  DEFAULT_SORT_BY,
  DEFAULT_SORT_ORDER,
} from '../creativeScoring/constants/criteriaManagement.constants';
import { LandingPageFilterId } from '../creativeScoring/components/ScoringLandingFilters/scoringLandingFilters.types';
import { convertDateRangeToISODateStringObject } from '../utils/dateRangePickerMUIUtils';
import { getFeatureFlag } from '../utils/featureFlagUtils';

const {
  PLATFORMS,
  MEDIA_TYPES,
  IS_OPTIONAL,
  OWNER_IDS,
  CATEGORY,
  GLOBAL_STATUSES,
  DATE_ADDED,
  CRITERIA_GROUP,
} = LandingPageFilterId;

const {
  handleBffApiGetWithPagination,
  handleBffApiGet,
  handleBffApiPatch,
  handleBffApiPost,
  handleBffApiDelete,
} = BffService;

class BffCriteriaManagementService extends ApiRequestHandler {
  getCriteriaList = async ({
    workspaceId,
    offset,
    pageSize,
    sort,
    filters,
    searchText,
  }) => {
    const endpoint = `v2/criteria?workspaceIds=${workspaceId}`;

    const { startDate, endDate } = filters[DATE_ADDED]?.length
      ? convertDateRangeToISODateStringObject(filters[DATE_ADDED])
      : { startDate: null, endDate: null };

    const requestParams = {
      offset,
      perPage: pageSize,
      sortBy: sort?.sortBy || DEFAULT_SORT_BY,
      sortOrder: sort?.sortOrder || DEFAULT_SORT_ORDER,
      [PLATFORMS]: filters[PLATFORMS]?.join(','),
      [MEDIA_TYPES]: filters[MEDIA_TYPES]?.join(','),
      [IS_OPTIONAL]: filters[IS_OPTIONAL]?.join(','),
      [OWNER_IDS]: filters[OWNER_IDS]?.join(','),
      [CATEGORY]: filters[CATEGORY]?.join(','),
      [GLOBAL_STATUSES]: filters[GLOBAL_STATUSES]?.join(','),
      [CRITERIA_GROUP]: filters[CRITERIA_GROUP]?.join(','),
      searchText,
      startDate,
      endDate,
    };

    return handleBffApiGetWithPagination(endpoint, requestParams);
  };

  createCriteriaExportAsCSV = async ({
    workspaceIds,
    sort,
    filters,
    searchText,
  }) => {
    const endpoint = `v1/criteria/download-csv`;

    const { startDate, endDate } = filters[DATE_ADDED].length
      ? convertDateRangeToISODateStringObject(filters[DATE_ADDED])
      : { startDate: null, endDate: null };

    const initialParams = {
      sortBy: sort?.sortBy || DEFAULT_SORT_BY,
      sortOrder: sort?.sortOrder || DEFAULT_SORT_ORDER,
      [PLATFORMS]: filters[PLATFORMS]?.join(','),
      [MEDIA_TYPES]: filters[MEDIA_TYPES]?.join(','),
      [IS_OPTIONAL]: filters[IS_OPTIONAL]?.join(','),
      [OWNER_IDS]: filters[OWNER_IDS]?.join(','),
      [CATEGORY]: filters[CATEGORY]?.join(','),
      [GLOBAL_STATUSES]: filters[GLOBAL_STATUSES]?.join(','),
      [CRITERIA_GROUP]: filters[CRITERIA_GROUP]?.join(','),
      searchText,
      startDate,
      endDate,
    };

    const filteredParams = Object.fromEntries(
      Object.entries(initialParams).filter(
        ([_, value]) => value != null && value !== '',
      ),
    );

    const body = {
      workspaceIds,
      criteriaParams: filteredParams,
    };

    return handleBffApiPost(endpoint, body);
  };

  getAvailableFilterOptions = async ({ workspaceId }) => {
    const endpoint = 'v1/criteria/options';
    const params = { workspaceId, includeGlobalOptions: true };

    return handleBffApiGet(endpoint, params);
  };

  createCriteria = async (payload) => {
    const { partnerId } = payload;
    const endpoint = `v1/criteria/workspace/${partnerId}`;
    return handleBffApiPost(endpoint, payload);
  };

  editCriteria = async ({ workspaceId, criteriaId, params }) => {
    const endpoint = `v1/criteria/${workspaceId}/${criteriaId}`;
    return handleBffApiPatch(endpoint, { ...params });
  };

  deleteCriteria = async ({ workspaceId, criteriaId }) => {
    const endpoint = `v1/criteria/${criteriaId}/partner/${workspaceId}`;
    return handleBffApiDelete(endpoint);
  };

  createBrandIdentifier = async ({ workspaceId, brandIdentifiers }) => {
    const endpoint = `v1/brand/partner/${workspaceId}`;
    return handleBffApiPost(endpoint, { brandIdentifiers });
  };

  getCriteriaTemplates = async ({ workspaceId }) => {
    let endpoint = `v1/criteria/template/partner/${workspaceId}`;
    if (getFeatureFlag('isMessagingApertureEnabled')) {
      endpoint += `?excludeDeprecated=true`;
    }
    return handleBffApiGet(endpoint);
  };

  getCriteriaDetails = async ({ workspaceId, criteriaAttributes }) => {
    const endpoint = `v1/criteria/workspace/${workspaceId}/details`;
    return handleBffApiPost(endpoint, criteriaAttributes);
  };
}

export default new BffCriteriaManagementService(vmBffApiManager);
