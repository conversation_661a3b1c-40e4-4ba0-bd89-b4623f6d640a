import { VisualizationType } from './visualizationType';
import { WidgetVisualizationDto } from '../../dashboard/dashboard.types';
import { Operator, Value } from '../../components/ReportFilters/types';
import { SortBy } from '../../dashboard/components/widgetEdit/context/WidgetEditContext';

export enum WidgetType {
  ASSETS_SCORED = 'ASSETS_SCORED',
  BRAND_ADHERENCE = 'BRAND_ADHERENCE',
  CHANNEL_ADHERENCE_TRENDS = 'CHANNEL_ADHERENCE_TRENDS',
  IMPRESSIONS = 'IMPRESSIONS',
  ADHERENCE_SCORE = 'ADHERENCE_SCORE',
  MARKET_ADHERENCE = 'MARKET_ADHERENCE',
  ASSET_OVERVIEW = 'ASSET_OVERVIEW',
  CRITERIA_PERFORMANCE = 'CRITERIA_PERFORMANCE',
  KEY_FINDINGS = 'KEY_FINDINGS',
}

export enum WidgetFilterKey {
  MEDIA_CREATE_DATE = 'mediaCreateDate',
  ASSET_SOURCE = 'assetSource',
  WORKSPACE_ID = 'workspaceId',
  WORKSPACE = 'workspace',
  CHANNEL = 'channel',
  BRAND = 'brand',
  DATE_RANGE = 'date',
  MARKET = 'market',
  ASSET_TYPE = 'assetType',
  CAMPAIGN = 'campaign',
  COMPARE_TO_PREVIOUS_PERIOD = 'compareToPreviousPeriod',
  KPI = 'kpi',
}

export enum WidgetKeyEnum {
  PARAMETERS = 'parameters',
  DATA = 'data',
}

export type FilterEntry = {
  key: WidgetFilterKey;
  operator: Operator;
  value: any;
};

// Widget Preview Types
export interface ReportFilterInputDto {
  filters: Array<FilterEntry>;
  groupBy?: {
    time: string;
  };
  entityType?: string;
  sortBy?: SortBy;
}

export interface WidgetPreviewRequest {
  widgetType: WidgetType;
  visualizationType: string;
  parameters?: WidgetVisualizationDto;
  filter?: ReportFilterInputDto;
  dashboardFilter?: ReportFilterInputDto;
  isCompareToPreviousPeriodEnabled?: boolean;
  isViewDataLabelsEnabled?: boolean;
}

export type DataResponseDto = WidgetVisualizationDto;

export interface WidgetPreviewResponse {
  widgetId: string | null;
  widgetType: WidgetType;
  visualizationType: string;
  data: DataResponseDto;
  lastRefreshed: string;
}

export enum FilterInputType {
  DATE_RANGE = 'date-range',
  MULTI_SELECT = 'multi-select',
  FILTERED_MULTI_SELECT = 'filtered-multi-select',
  SINGLE_SELECT = 'single-select',
}

// Widget Types List Types
export interface FilterField {
  key: WidgetFilterKey;
  displayLabel?: string;
  type: FilterInputType;
  dependsOn?: string[];
  defaultValue?: any;
}

export interface WidgetTypeResponse {
  widgetType: WidgetType;
  name: string;
  description: string;
  iconUrl: string;
  defaultVisualizationType: string;
  visualizationTypes: string[];
  requiredFilters: FilterField[];
  optionalFilters: FilterField[];
  filterValues?: Record<string, FilterOptions>;
}

export interface Pagination {
  offset: number;
  perPage: number;
  nextOffset: number;
  totalSize: number;
}

export interface WidgetFilterValue {
  values: WidgetFilterValueResponse[];
  pagiantion: Pagination;
}

// Widget Filter Values Types
export interface WidgetFilterValuesRequest {
  filter?: ReportFilterInputDto;
}

export interface WidgetFilterValueResponse {
  id: string;
  name: string;
  iconUrl?: string;
}

export interface WidgetFilterValuesParams {
  organizationId: string;
  widgetType: WidgetType;
  key: WidgetFilterKey | string;
  offset?: number;
  perPage?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface WidgetDetailsResponse {
  widgetId: string;
  widgetType: WidgetType;
  visualizationType: VisualizationType;
  filter: ReportFilterInputDto;
  dashboardFilter: ReportFilterInputDto;
  isCompareToPreviousPeriodEnabled: boolean;
  isViewDataLabelsEnabled: boolean;
  isKpiLiftEnabled: boolean;
  isIncludeTotalEnabled: boolean;
}

export type FilterChangeArgs = {
  key: WidgetFilterKey;
  value: Value;
  operator: Operator;
};

export interface PaginationObject {
  offset: number;
  perPage: number;
  nextOffset: number | null;
  totalSize: number;
}

export interface FilterValue {
  id: string;
  name: string;
  iconUrl?: string;
  workspaceSubscriptionsMissing?: string[];
  spendEnabled?: boolean;
}

export interface FilterOptions {
  values: FilterValue[];
  pagination?: PaginationObject;
}
