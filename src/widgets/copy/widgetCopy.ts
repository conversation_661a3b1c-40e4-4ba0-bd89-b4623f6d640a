export const WIDGET_INTL_KEYS = {
  ASSETS_SCORED: {
    title: {
      id: 'ui.dashboard.widget.assetsScore.title',
      defaultMessage: 'Assets Scored',
    },
    description: {
      id: 'ui.dashboard.widget.assetsScore.description',
      defaultMessage: 'Total number of assets scored',
    },
  },
  IMPRESSIONS: {
    title: {
      id: 'ui.dashboard.widget.impressions.title',
      defaultMessage: 'Impressions Analyzed',
    },
    description: {
      id: 'ui.dashboard.widget.impressions.description',
      defaultMessage: 'Total impressions analyzed',
    },
  },
  ADHERENCE_SCORE: {
    title: {
      id: 'ui.dashboard.widget.adherenceScore.title',
      defaultMessage: 'Adherence Score',
    },
    description: {
      id: 'ui.dashboard.widget.adherenceScore.description',
      defaultMessage: 'Average creative adherence to your criteria',
    },
  },
  CHANNEL_ADHERENCE_TRENDS: {
    title: {
      id: 'ui.dashboard.widget.channelAdherence.title',
      defaultMessage: 'Channel Adherence Trends',
    },
    description: {
      id: 'ui.dashboard.widget.channelAdherence.description',
      defaultMessage: 'Adherence changes across channels over time',
    },
  },
  MARKET_ADHERENCE: {
    title: {
      id: 'ui.dashboard.widget.marketAdherence.title',
      defaultMessage: 'Market Adherence Trends',
    },
    description: {
      id: 'ui.dashboard.widget.marketAdherence.description',
      defaultMessage: 'Adherence changes across market over time',
    },
  },
  BRAND_ADHERENCE: {
    title: {
      id: 'ui.dashboard.widget.brandAdherence.title',
      defaultMessage: 'Brand Adherence Trends',
    },
    description: {
      id: 'ui.dashboard.widget.brandAdherence.description',
      defaultMessage: 'Adherence changes across brand over time',
    },
  },
  ASSET_OVERVIEW: {
    title: {
      id: 'ui.dashboard.widget.assets.title',
      defaultMessage: 'Asset Overview',
    },
    description: {
      id: 'ui.dashboard.widget.assets.description',
      defaultMessage: 'Rank assets by adherence and KPI results',
    },
  },
  CRITERIA_PERFORMANCE: {
    title: {
      id: 'ui.dashboard.widget.criteriaPerformance.title',
      defaultMessage: 'Criteria Performance Overview',
    },
    description: {
      id: 'ui.dashboard.widget.criteriaPerformance.description',
      defaultMessage: 'Top-performing creative criteria by KPI',
    },
  },
  KEY_FINDINGS: {
    title: {
      id: 'ui.dashboard.widget.learnings.title',
      defaultMessage: 'Key Learnings',
    },
    description: {
      id: 'ui.dashboard.widget.learnings.description',
      defaultMessage: 'Summarize and capture key insights',
    },
    placeholder: {
      id: 'ui.dashboard.widget.learnings.placeholder',
      defaultMessage: 'Note your key learnings...',
    },
  },
  units: {
    assets: {
      id: 'ui.dashboard.widget.asset.unit',
      defaultMessage: 'assets',
    },
    impressions: {
      id: 'ui.dashboard.widget.impression.unit',
      defaultMessage: 'impressions',
    },
  },
  previous: {
    id: 'ui.dashboard.widget.previous',
    defaultMessage: 'Previous',
  },
  drag: {
    tooltip: {
      id: 'ui.dashboard.widget.drag.tooltip',
      defaultMessage: 'Drag to move',
    },
  },
  details: {
    id: 'ui.dashboard.widget.details',
    defaultMessage: 'Details',
  },
  noData: {
    title: {
      id: 'ui.dashboard.widget.noData.title',
      defaultMessage: 'No data',
    },
    description: {
      id: 'ui.dashboard.widget.noData.description',
      defaultMessage:
        'We couldn’t load your data. Try refreshing the page or check back later.',
    },
  },
  noPermissions: {
    title: {
      id: 'ui.dashboard.widget.noPermissions.title',
      defaultMessage: 'No access to data',
    },
    description: {
      id: 'ui.dashboard.widget.noPermissions.description',
      defaultMessage:
        'You don’t have permissions to view the data in this widget, please reach out to your organization’s admin.',
    },
  },
  noWidgetData: {
    title: {
      id: 'ui.dashboard.widget.noWidgetData.title',
      defaultMessage: 'No data',
    },
    description: {
      id: 'ui.dashboard.widget.noWidgetData.description',
      defaultMessage:
        'We couldn’t load data for this widget. Try editing the widget to adjust its settings.',
    },
  },
  error: {
    title: {
      id: 'ui.dashboard.widget.error.title',
      defaultMessage: 'Error',
    },
    description: {
      id: 'ui.dashboard.widget.error.description',
      defaultMessage:
        'We couldn’t load your data. Try refreshing the page or check back later.',
    },
  },
  dateRange: {
    custom: {
      id: 'ui.globalFilters.dateRange.custom',
      defaultMessage: 'Custom',
    },
  },
  exportPdf: {
    disclaimer: {
      id: 'ui.dashboard.widget.exportPdf.disclaimer',
      defaultMessage: 'This does not measure usage against your subscription.',
    },
  },
  chartTooltips: {
    bar: {
      title: {
        id: 'ui.dashboard.chart.bar.title',
        defaultMessage: 'Bar',
      },
      description: {
        id: 'ui.dashboard.chart.bar.description',
        defaultMessage:
          'Best for comparing categories or segments side by side.',
      },
    },
    donut: {
      title: {
        id: 'ui.dashboard.chart.donut.title',
        defaultMessage: 'Donut',
      },
      description: {
        id: 'ui.dashboard.chart.donut.description',
        defaultMessage: 'Great for showing parts of a whole.',
      },
    },
    line: {
      title: {
        id: 'ui.dashboard.chart.line.title',
        defaultMessage: 'Line',
      },
      description: {
        id: 'ui.dashboard.chart.line.description',
        defaultMessage: 'Ideal for spotting trends over time.',
      },
    },
    column: {
      title: {
        id: 'ui.dashboard.chart.column.title',
        defaultMessage: 'Column',
      },
      description: {
        id: 'ui.dashboard.chart.column.description',
        defaultMessage: 'Great for visualizing volume over time.',
      },
    },
    metric: {
      title: {
        id: 'ui.dashboard.chart.metric.title',
        defaultMessage: 'Metric',
      },
      description: {
        id: 'ui.dashboard.chart.metric.description',
        defaultMessage: 'Use for a quick snapshot of the total value.',
      },
    },
    table: {
      title: {
        id: 'ui.dashboard.chart.table.title',
        defaultMessage: 'Table',
      },
      description: {
        id: 'ui.dashboard.chart.table.description',
        defaultMessage: 'Use to see details in rows for easy scanning.',
      },
    },
  },
  dislaimer: {
    id: 'ui.dashboard.widget.disclaimer',
    defaultMessage: 'This does not measure usage against your subscription.',
  },
};
