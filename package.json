{"name": "vidmob-agile-creative-studio", "version": "0.0.0-SNAPSHOT", "description": "React App Environment Setup", "private": true, "engines": {"node": "20.14.0", "npm": "9.6.5"}, "scripts": {"local": "VIDMOB_ENV=local node ./configs/srcFileStructure.js && VIDMOB_ENV=local node --inspect=0.0.0.0:9229 server/server.js --open --hot", "local:more-memory": "VIDMOB_ENV=local node ./configs/srcFileStructure.js && VIDMOB_ENV=local node --max-old-space-size=4096 server/server.js --open --hot", "zip-bundle": "node ./configs/zipBundle.js", "docker-zip-bundle": "NO_CDN=true node ./configs/zipBundle.js", "build:docker": "VIDMOB_ENV=development NODE_OPTIONS=--max-old-space-size=4096 webpack --progress --config webpack.docker.config.js && npm run docker-zip-bundle", "build:dev": "VIDMOB_ENV=development NODE_OPTIONS=--max-old-space-size=8192 webpack --progress --config webpack.dev.config.js && VIDMOB_ENV=development npm run zip-bundle", "build:stage": "VIDMOB_ENV=stage NODE_OPTIONS=--max-old-space-size=8192 webpack --progress --config webpack.stage.config.js && VIDMOB_ENV=stage npm run zip-bundle", "build:prod": "VIDMOB_ENV=production NODE_OPTIONS=--max-old-space-size=8192 webpack --progress --config webpack.prod.config.js && VIDMOB_ENV=production npm run zip-bundle", "build": "NODE_OPTIONS=--max-old-space-size=8192 webpack --progress --config webpack.prod.config.js && npm run zip-bundle", "clean-install": "rm -rf node_modules && rm -f package-lock.json && npm i", "lint": "npx eslint@7 --quiet --fix ./src/", "lint-rules": "npx eslint@7 --print-config ./src/index.jsx", "test": "VIDMOB_ENV=test jest --coverage", "test:silent": "VIDMOB_ENV=test jest --coverage --silent", "test:coverage": "VIDMOB_ENV=test jest --coverage", "test:watch": "VIDMOB_ENV=test jest --watchAll", "test:debug": "VIDMOB_ENV=test node --inspect node_modules/.bin/jest --runInBand", "styleguide:local": "styleguidist server", "styleguide:build": "styleguidist build", "preinstall": "node ./preInstall.js", "playwright": "npx playwright test --project=chromium", "storybook": "storybook dev -p 6006", "build:storybook": "storybook build"}, "keywords": ["react", "web application", "environment", "webpack", "babel", "express", "node", "vidMob"], "author": "VidMob", "browserslist": ["chrome >= 89", "edge >= 89", "firefox >= 87", "safari >= 14.1", "last 2 versions", ">2%", "not dead"], "dependencies": {"@datadog/browser-logs": "5.6.0", "@datadog/browser-rum": "5.6.0", "@emotion/react": "11.11.4", "@emotion/styled": "11.11.5", "@juggle/resize-observer": "3.3.1", "@mdxeditor/editor": "3.36.0", "@mui/base": "5.0.0-beta.4", "@mui/icons-material": "5.11.16", "@mui/lab": "5.0.0-alpha.137", "@mui/material": "5.12.0", "@mui/x-data-grid-pro": "6.18.6", "@mui/x-date-pickers": "6.4.0", "@mui/x-date-pickers-pro": "6.4.0", "@mui/x-license-pro": "6.0.3", "@reduxjs/toolkit": "2.2.3", "@splitsoftware/splitio": "10.26.1", "@tanstack/react-query": "4.32.0", "@tanstack/react-query-devtools": "4.32.0", "@u-wave/react-vimeo": "0.9.8", "axios": "1.7.7", "braintree-web-drop-in-react": "1.0.7", "branch-sdk": "2.50.2", "classnames": "2.2.5", "compression": "1.7.4", "core-js": "3.6.4", "crypto-js": "4.2.0", "d3": "6.2.0", "dayjs": "1.11.7", "dayjs-plugin-utc": "0.1.2", "dd-trace": "3.17.1", "dompurify": "3.1.7", "emoji-picker-react": "4.9.2", "escape-string-regexp": "5.0.0", "exceljs": "4.4.0", "exif-js": "2.3.0", "express": "4.21.0", "fast-xml-parser": "4.3.2", "file-saver": "2.0.5", "follow-redirects": "1.15.6", "highcharts": "10.1.0", "highcharts-react-official": "3.1.0", "history": "4.7.2", "html2canvas": "1.4.1", "is-iso-date": "0.0.1", "jspdf": "2.5.2", "jStat": "1.8.5", "jszip": "3.10.1", "jszip-utils": "0.1.0", "marked": "15.0.11", "md5": "2.2.1", "moment": "2.29.4", "notistack": "3.0.2", "opn": "5.4.0", "path-to-regexp": "3.0.0", "prop-types": "15.8.1", "qrcode-svg": "1.1.0", "qs": "6.9.7", "query-string": "6.2.0", "rc-slider": "10.0.1", "react": "18.2.0", "react-csv": "2.2.2", "react-dates-gte-react-17": "22.0.1", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.2.0", "react-ga": "2.5.2", "react-grid-layout": "1.5.1", "react-gtm-module": "2.0.11", "react-helmet": "5.2.1", "react-intercom": "1.0.14", "react-intl": "6.0.5", "react-loadable": "5.5.0", "react-markdown": "9.0.1", "react-moment-proptypes": "1.5.0", "react-phone-input-2": "2.15.1", "react-quill": "2.0.0", "react-redux": "9.1.0", "react-router": "5.2.0", "react-router-dom": "5.1.2", "react-router-dom-v5-compat": "6.28.1", "react-textarea-autosize": "8.3.4", "redux": "5.0.1", "redux-saga": "1.1.3", "rehype-raw": "7.0.0", "remark-gfm": "4.0.0", "reselect": "4.0.0", "stream": "0.0.2", "timers": "0.1.1", "trim": "1.0.1", "turndown": "7.2.0", "tus-js-client": "1.8.0-1", "vidmob-client-session-manager": "gitlab:vidmob/repos/libraries/vidmob-client-session-manager#0.45.0", "vidmob-node-auth-api": "gitlab:vidmob/repos/libraries/vidmob-node-auth-api#1.0.5", "vtt.js": "git+ssh://**************/vidmob/repos/libraries/vtt.js.git#master"}, "devDependencies": {"@babel/core": "7.22.0", "@babel/plugin-proposal-class-properties": "7.5.5", "@babel/plugin-proposal-nullish-coalescing-operator": "7.12.1", "@babel/plugin-proposal-optional-chaining": "7.12.7", "@babel/plugin-proposal-private-methods": "7.4.3", "@babel/plugin-syntax-dynamic-import": "7.2.0", "@babel/plugin-transform-runtime": "7.5.5", "@babel/preset-env": "7.5.5", "@babel/preset-react": "7.9.4", "@babel/preset-typescript": "7.12.7", "@playwright/test": "1.25.0", "@pmmmwh/react-refresh-webpack-plugin": "0.5.11", "@storybook/addon-essentials": "7.0.9", "@storybook/addon-interactions": "7.0.9", "@storybook/addon-links": "7.0.9", "@storybook/blocks": "7.0.9", "@storybook/react": "7.0.9", "@storybook/react-webpack5": "7.0.9", "@storybook/testing-library": "0.0.14-next.2", "@testing-library/dom": "8.13.0", "@testing-library/jest-dom": "5.11.0", "@testing-library/react": "14.3.0", "@testing-library/user-event": "14.3.0", "@types/file-saver": "2.0.7", "@types/node": "18.7.9", "@types/react": "18.2.75", "@types/react-dom": "17.0.0", "@types/react-grid-layout": "1.3.5", "@types/react-redux": "7.1.12", "@types/react-router": "5.1.9", "@types/react-router-dom": "5.1.7", "@types/turndown": "5.0.5", "archiver": "5.3.1", "autoprefixer": "9.6.1", "babel-jest": "24.0.0", "babel-loader": "9.1.3", "case-sensitive-paths-webpack-plugin": "2.4.0", "clean-webpack-plugin": "1.0.0", "css-loader": "3.6.0", "css-minimizer-webpack-plugin": "4.0.0", "cssnano": "4.1.10", "dotenv": "10.0.0", "eslint": "8.57.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "5.1.0", "html-webpack-plugin": "5.3.1", "html-webpack-preconnect-plugin": "1.2.0", "jest": "26.6.3", "jest-date-mock": "1.0.8", "jest-junit": "14.0.1", "json-loader": "0.5.7", "mini-css-extract-plugin": "1.3.9", "mkdirp": "0.5.6", "node": "16.16.0", "node-sass": "6.0.1", "npm-force-resolutions": "0.0.10", "playwright": "1.25.0", "postcss-loader": "5.3.0", "postcss-preset-env": "6.7.0", "postcss-scss": "3.0.5", "prettier": "^3.0.3", "react-refresh": "0.14.0", "react-styleguidist": "11.2.0", "react-test-renderer": "18.2.0", "reactstrap": "9.1.3", "redux-saga-test-plan": "4.0.0-rc.3", "sass-loader": "10.3.1", "shared-git-hooks": "1.2.1", "speed-measure-webpack-plugin": "1.5.0", "storybook": "7.0.9", "style-loader": "0.23.1", "stylelint": "13.13.0", "stylelint-config-standard": "22.0.0", "stylelint-webpack-plugin": "2.1.1", "terser-webpack-plugin": "5.3.3", "thread-loader": "4.0.2", "typescript": "4.7.4", "typescript-eslint": "7.3.1", "url-loader": "0.6.2", "webpack": "5.91.0", "webpack-cli": "4.9.2", "webpack-dev-middleware": "3.7.3", "webpack-dev-server": "3.11.2", "webpack-hot-middleware": "2.24.3", "webpack-merge": "4.2.2", "xml-loader": "1.2.1", "zxcvbn": "4.4.2"}, "resolutions": {"ua-parser-js": "0.7.30"}}